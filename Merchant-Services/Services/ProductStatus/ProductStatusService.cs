using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.EntityFrameworkCore;

namespace Merchant_Services.Services.ProductStatus;

public interface IProductStatusService
{
    Task<Dictionary<string, bool>> GetProductStatusBatchAsync(IEnumerable<string> productIds);
    Task<bool> IsProductActiveAsync(string productId);
    Task PreloadProductStatusesAsync(int merchantId);
    Task InvalidateProductStatusAsync(string productId);
    Task<List<string>> GetAllInactiveProductIdsAsync();
    Task MarkProductAsInactiveAsync(string productId);
}

public class ProductStatusService(
    ILogger<ProductStatusService> logger,
    IMemoryCache memoryCache,
    MerchantDbContext dbContext)
    : IProductStatusService
{
    private readonly ConcurrentDictionary<string, bool> _localCache = new();
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromHours(1);
    
    // Static cache for inactive product IDs that persists across sessions
    private static readonly ConcurrentDictionary<string, DateTime> InactiveProductsCache = new();
    
    // How long to keep products in the inactive cache before reconsidering them
    private static readonly TimeSpan InactiveCacheExpiry = TimeSpan.FromHours(5);
    
    // Memory cache key for the inactive products list
    private const string InactiveProductsCacheKey = "InactiveProducts";
    
    // Maximum number of inactive products to include in filter string
    private const int MaxInactiveProductsInFilter = 5000;

    public async Task<Dictionary<string, bool>> GetProductStatusBatchAsync(IEnumerable<string> productIds)
    {
        var result = new Dictionary<string, bool>();
        var uniqueIds = productIds.Distinct().ToList();
        
        foreach (var id in uniqueIds)
        {
            var isActive = await IsProductActiveAsync(id);
            result[id] = isActive;
        }
        
        return result;
    }
    
    public async Task<bool> IsProductActiveAsync(string productId)
    {
        // First check if it's in the inactive products static cache
        if (InactiveProductsCache.TryGetValue(productId, out DateTime timestamp))
        {
            // Check if the entry is still valid
            if (DateTime.UtcNow - timestamp <= InactiveCacheExpiry)
            {
                logger.LogDebug("Product {ProductId} found in inactive products cache", productId);
                return false;
            }
            else
            {
                // Entry expired, remove it from inactive cache
                InactiveProductsCache.TryRemove(productId, out _);
            }
        }
        
        // Then check local cache
        if (_localCache.TryGetValue(productId, out bool cachedStatus))
        {
            return cachedStatus;
        }
        
        // Then check memory cache
        string cacheKey = $"ProductStatus:{productId}";
        if (memoryCache.TryGetValue(cacheKey, out bool status))
        {
            // Update local cache
            _localCache[productId] = status;
            
            // If product is inactive, add to the static inactive cache
            if (!status)
            {
                InactiveProductsCache[productId] = DateTime.UtcNow;
            }
            
            return status;
        }
        
        // Fetch from database
        var product = await dbContext.Products.FirstOrDefaultAsync(p => p.InternalProductId == productId);
        if (product == null)
        {
            return false;
        }
        
        // Cache the result
        memoryCache.Set(cacheKey, product.Active, _cacheExpiry);
        _localCache[productId] = product.Active;
        
        // If product is inactive, add to the static inactive cache
        if (!product.Active)
        {
            InactiveProductsCache[productId] = DateTime.UtcNow;
        }
        
        return product.Active;
    }
    
    public async Task PreloadProductStatusesAsync(int merchantId)
    {
        logger.LogInformation("Preloading product statuses for merchant {MerchantId}", merchantId);
        
        var products = await dbContext.Products.Where(p => p.FkMerchantId == merchantId).ToListAsync();
        foreach (var product in products)
        {
            await IsProductActiveAsync(product.InternalProductId);
        }
    }
    
    public async Task InvalidateProductStatusAsync(string productId)
    {
        string cacheKey = $"ProductStatus:{productId}";
        memoryCache.Remove(cacheKey);
        _localCache.TryRemove(productId, out _);
        
        // Also remove from inactive products cache
        InactiveProductsCache.TryRemove(productId, out _);
        
        logger.LogInformation("Invalidated cache for product {ProductId}", productId);
    }
    
    public async Task UpdateProductStatusAsync(string productId, bool isActive)
    {
        string cacheKey = $"ProductStatus:{productId}";
        memoryCache.Set(cacheKey, isActive, _cacheExpiry);
        _localCache[productId] = isActive;
        
        // Update the inactive products cache
        if (isActive)
        {
            // If product is now active, remove from inactive cache
            InactiveProductsCache.TryRemove(productId, out _);
            logger.LogInformation("Product {ProductId} marked as active and removed from inactive cache", productId);
        }
        else
        {
            // If product is inactive, add to inactive cache
            InactiveProductsCache[productId] = DateTime.UtcNow;
            logger.LogInformation("Product {ProductId} marked as inactive and added to inactive cache", productId);
        }
        
    }
    
    public async Task<List<string>> GetAllInactiveProductIdsAsync()
    {
        // Clean up expired entries
        var expiredKeys = InactiveProductsCache
            .Where(kvp => DateTime.UtcNow - kvp.Value > InactiveCacheExpiry)
            .Select(kvp => kvp.Key)
            .ToList();
            
        foreach (var key in expiredKeys)
        {
            InactiveProductsCache.TryRemove(key, out _);
        }
        
        var inactiveProductIds = InactiveProductsCache.Keys.ToList();
        logger.LogInformation("Retrieved {Count} inactive product IDs from cache", inactiveProductIds.Count);
        
        return inactiveProductIds;
    }
    
    public async Task MarkProductAsInactiveAsync(string productId)
    {
        // Add to inactive products cache
        InactiveProductsCache[productId] = DateTime.UtcNow;
        
        // Also update the regular caches
        string cacheKey = $"ProductStatus:{productId}";
        memoryCache.Set(cacheKey, false, _cacheExpiry);
        _localCache[productId] = false;
        
        logger.LogInformation("Product {ProductId} manually marked as inactive", productId);
    }
    
    /// <summary>
    /// Filters a list of product IDs to return only the active ones
    /// </summary>
    /// <param name="productIds">List of product IDs to check</param>
    /// <returns>List of active product IDs</returns>
    public async Task<List<string>> FilterActiveProductsAsync(IEnumerable<string> productIds)
    {
        var result = new List<string>();
        var statuses = await GetProductStatusBatchAsync(productIds);
        
        foreach (var kvp in statuses)
        {
            if (kvp.Value) // If product is active
            {
                result.Add(kvp.Key);
            }
            else
            {
                // Ensure inactive products are in the static cache
                InactiveProductsCache[kvp.Key] = DateTime.UtcNow;
            }
        }
        
        logger.LogInformation("Filtered {TotalCount} products, {ActiveCount} are active, {InactiveCount} are inactive", 
            statuses.Count, result.Count, statuses.Count - result.Count);
        
        return result;
    }
    
    /// <summary>
    /// Gets a comma-separated string of inactive product IDs for use in search filters
    /// </summary>
    /// <returns>Comma-separated string of inactive product IDs</returns>
    public async Task<string> GetInactiveProductsFilterString()
    {
        var inactiveProducts = await GetAllInactiveProductIdsAsync();
        
        // Limit the number of products in the filter to avoid exceeding URL length limits
        var limitedInactiveProducts = inactiveProducts.Take(MaxInactiveProductsInFilter).ToList();
        
        if (limitedInactiveProducts.Count < inactiveProducts.Count)
        {
            logger.LogWarning("Limiting inactive products filter to {LimitCount} out of {TotalCount} products", 
                limitedInactiveProducts.Count, inactiveProducts.Count);
        }
        
        return string.Join(",", limitedInactiveProducts);
    }
} 