using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using Microsoft.Data.SqlClient;

namespace Merchant_Services.Services.ProductStatus;

public class ProductStatusPreloadService(
    IServiceScopeFactory serviceScopeFactory,
    ILogger<ProductStatusPreloadService> logger)
    : BackgroundService
{
    private readonly ConcurrentDictionary<int, DateTime> _lastPreloadTimes = new();
    private readonly TimeSpan _preloadInterval = TimeSpan.FromHours(12); // Adjust as needed

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        logger.LogInformation("Product Status Preload Service is starting");
        
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PreloadActiveMerchantsAsync(stoppingToken);
                
                // Wait before the next preload cycle
                await Task.Delay(TimeSpan.FromMinutes(30), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Normal shutdown
                break;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in Product Status Preload Service");
                
                // Wait a bit before retrying after an error
                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
        }
        
        logger.LogInformation("Product Status Preload Service is stopping");
    }
    
    private async Task PreloadActiveMerchantsAsync(CancellationToken stoppingToken)
    {
        try
        {
            using var scope = serviceScopeFactory.CreateScope();
            var productStatusService = scope.ServiceProvider.GetRequiredService<IProductStatusService>();
            
            // Use using statement for proper connection disposal and automatic closing of connection when reader is disposed, even if an exception is thrown
            using var connection = scope.ServiceProvider.GetRequiredService<SqlConnection>();
            await connection.OpenAsync(stoppingToken);

            // Get active merchants with significant product counts using raw SQL
            var sql = @"
                SELECT TOP 50 Id, ProductCount 
                FROM Merchants 
                WHERE IsActive = 1 AND IsDeleted = 0 
                ORDER BY ProductCount DESC";
            
            using var command = new SqlCommand(sql, connection);
            using var reader = await command.ExecuteReaderAsync(stoppingToken);
            
            var activeMerchants = new List<MerchantInfo>();
            while (await reader.ReadAsync(stoppingToken))
            {
                activeMerchants.Add(new MerchantInfo
                {
                    Id = reader.GetInt32(0),
                    ProductCount = reader.GetInt32(1)
                });
            }
            
            logger.LogInformation("Found {Count} active merchants for product status preloading", activeMerchants.Count);
            
            foreach (var merchant in activeMerchants)
            {
                if (stoppingToken.IsCancellationRequested)
                {
                    break;
                }
                
                // Skip if we've preloaded this merchant recently
                if (_lastPreloadTimes.TryGetValue(merchant.Id, out DateTime lastPreload) && 
                    DateTime.UtcNow - lastPreload < _preloadInterval)
                {
                    continue;
                }
                
                // Skip merchants with very few products
                if (merchant.ProductCount < 100)
                {
                    continue;
                }
                
                logger.LogInformation("Scheduling preload for merchant {MerchantId} with {ProductCount} products", 
                    merchant.Id, merchant.ProductCount);
                
                await productStatusService.PreloadProductStatusesAsync(merchant.Id);
                
                // Update last preload time
                _lastPreloadTimes[merchant.Id] = DateTime.UtcNow;
                
                // Add a small delay between merchants to avoid overwhelming the system
                await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error preloading active merchants");
        }
    }
    
    // Simple class to hold merchant information
    private class MerchantInfo
    {
        public int Id { get; set; }
        public int ProductCount { get; set; }
    }
} 