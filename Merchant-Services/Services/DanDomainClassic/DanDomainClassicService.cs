using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.EntityFrameworkCore;
using Shared.Models.Merchant;
using Shared.Services.Cache;
using ILogger = Serilog.ILogger;

namespace Webshop_Service.Services.DanDomain;

public class DanDomainClassicService : IDanDomainClassicService
{
    private readonly MerchantDbContext _merchantDbContext;
    private readonly ILogger _logger;
    private readonly ICacheService _cacheService;

    public DanDomainClassicService(MerchantDbContext merchantDbContext, ILogger logger, ICacheService cacheService)
    {
        _merchantDbContext = merchantDbContext;
        _logger = logger;
        _cacheService = cacheService;
    }

    public async Task<PluginInstallInfo?> GetDanDomainClassicShop(long shopIdentifier)
    {
        var cacheKey = $"DanDomainClassicService_GetDanDomainClassicShop_{shopIdentifier}";
        var danDomainClassic = await _cacheService.GetData<PluginInstallInfo>(cacheKey);
        if (danDomainClassic == null)
        {
            danDomainClassic = await _merchantDbContext.PluginInstallInfos
                .AsNoTracking()
                //.SingleOrDefaultAsync(a => a.ShopIdentifier == shopIdentifier.ToString() && a.Type == "DanDomainClassic")
                .FirstOrDefaultAsync(a => a.ShopIdentifier == shopIdentifier.ToString() && a.Type == "DanDomainClassic")
                .ConfigureAwait(false);
            _cacheService.SetData(cacheKey, danDomainClassic, TimeSpan.FromHours(12));
        }

        return danDomainClassic;
    }

    public async Task<PluginInstallInfo> CreateDanDomainClassicShopAsync(PluginInstallInfo danDomainClassic)
    {
        _merchantDbContext.Add(danDomainClassic);
        await _merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
        return danDomainClassic;
    }

    public async Task<PluginInstallInfo> UpdateDanDomainClassicShopAsync(PluginInstallInfo danDomainClassic)
    {
        var existingUrl = string.Empty;
        var existing =
            await _merchantDbContext.PluginInstallInfos.SingleOrDefaultAsync(a =>
                    a.ShopIdentifier == danDomainClassic.ShopIdentifier && a.Type == "DanDomainClassic")
                .ConfigureAwait(false);
        if (existing != null)
        {
            existingUrl = existing.Url;
            //existing.IsAppInstalled = danDomainClassic.IsAppInstalled;
            existing.Name = danDomainClassic.Name;
            existing.ApiKey = danDomainClassic.ApiKey;
            existing.Url = danDomainClassic.Url;
            _merchantDbContext.Update(existing);
        }

        // TODO - Do we need to create a new field to identify a DanDomain Shop?
        // TODO - Is This Updated Needed At All?
        //var webshop = await _merchantDbContext.Merchants.SingleOrDefaultAsync(a => a.AccessUrl == existingUrl).ConfigureAwait(false);
        var webshop =
            await _merchantDbContext.MerchantMeta
                .Include(a => a.FkMerchant)
                .SingleOrDefaultAsync(a =>
                    a.Value == existingUrl && a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUrl);
        if (webshop != null)
        {
            var MerchantMetum = await _merchantDbContext.MerchantMeta.SingleOrDefaultAsync(a =>
                a.FkMerchantId == webshop.FkMerchantId && a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey);
            if (MerchantMetum != null)
            {
                MerchantMetum.Value = danDomainClassic.ApiKey;
                _merchantDbContext.Update(MerchantMetum);
            }
        }

        await _merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
        return existing;
    }


    public async Task<PluginInstallInfo> UninstallDanDomainClassicShopAsync(int shopIdentifier)
    {
        var existing =
            await _merchantDbContext.PluginInstallInfos
                .SingleOrDefaultAsync(
                    a => a.ShopIdentifier == shopIdentifier.ToString() && a.Type == "DanDomainClassic")
                .ConfigureAwait(false);
        if (existing != null)
        {
            //existing.IsAppInstalled = false;
            _merchantDbContext.Update(existing);
        }

        await _merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
        return existing;
    }
}