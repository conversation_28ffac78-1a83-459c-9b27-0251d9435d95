using Merchant_Services.Models.ModelsDal.Merchant;

namespace Webshop_Service.Services.DanDomain;

public interface IDanDomainClassicService
{
    Task<PluginInstallInfo?> GetDanDomainClassicShop(long shopIdentifier);
    Task<PluginInstallInfo> CreateDanDomainClassicShopAsync(PluginInstallInfo danDomainClassic);
    Task<PluginInstallInfo> UpdateDanDomainClassicShopAsync(PluginInstallInfo danDomainClassic);
    Task<PluginInstallInfo> UninstallDanDomainClassicShopAsync(int shopIdentifier);
}