using Nest;

namespace Webshop_Services.Services.Elastic
{
    public interface IMerchantElasticSync
    {
        Task<Dictionary<string, decimal>> GetMerchantProductScore(ElasticClient client, string webshopId,
            DateTime fromDate, DateTime toDate, Dictionary<string, string> scoreSettings,
            CancellationToken cancellationToken);

        Task SyncInactiveProductsFromSqlToElastic();
    }
}