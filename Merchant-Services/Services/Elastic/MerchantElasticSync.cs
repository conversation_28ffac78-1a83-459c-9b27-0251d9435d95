using Nest;
using System.Globalization;
using Elastic.Clients.Elasticsearch;
using Elasticsearch.Net;
using Shared.Elastic.Elastic;
using Webshop_Services.Models.Elastic;
using Webshop.Webshop;
using DeleteByQueryRequest = Nest.DeleteByQueryRequest;
using ILogger = Serilog.ILogger;
using TermQuery = Nest.TermQuery;

namespace Webshop_Services.Services.Elastic
{
    public class MerchantElasticSync(IElasticService elasticService, IMerchantService merchantService, ILogger logger, Microsoft.Extensions.Configuration.IConfiguration configuration) : IMerchantElasticSync
    {
        private readonly IElasticService _elasticService = elasticService;

        public async Task<Dictionary<string, decimal>> GetMerchantProductScore(ElasticClient client, string merchantId,
            DateTime fromDate, DateTime toDate, Dictionary<string, string> scoreSettings,
            CancellationToken cancellationToken)
        {
            Dictionary<string, decimal> result = new();

            var now = DateTime.UtcNow;
            var scale = scoreSettings["PageviewDecayScaleDays"];
            var decay = double.Parse(scoreSettings["PageviewDecayFraction"], CultureInfo.InvariantCulture);
            var pageviewScore = int.Parse(scoreSettings["PageviewScore"]);

            var filters = new List<Func<QueryContainerDescriptor<MerchantProductScoreEvent>, QueryContainer>>
            {
                f => f.DateRange(dt => dt
                         .Field(f => f.CreatedDate)
                         .GreaterThanOrEquals(fromDate)
                         .LessThanOrEquals(toDate))
                     && f.Term(t => t.Field(f => f.ShopEvent.WebshopId).Value(merchantId))
                     && f.Term(t => t.Field(f => f.ShopEvent.EventType).Value("ProductLook"))
            };

            var response = await client.SearchAsync<MerchantProductScoreEvent>(s => s
                .Size(0)
                .Source(s => s
                    .Includes(i => i
                        .Field(f => f.ShopEvent.WebshopId)
                        .Field(f => f.ShopEvent.ProductInternalId)
                    )
                )
                .Index("customers-pages-events")
                .Query(q => q
                    .FunctionScore(fs => fs
                        .Name("MerchantProductScores")
                        .BoostMode(FunctionBoostMode.Replace)
                        .ScoreMode(FunctionScoreMode.Sum)
                        .Query(qu => qu
                            .Bool(b => b
                                .Filter(f => f.Bool(b => b.Should(filters)))
                            )
                        )
                        .Functions(fcs => fcs
                            .ExponentialDate(exp => exp
                                .Field(f => f.CreatedDate)
                                .Origin(toDate)
                                .Scale(scale + "d")
                                .Offset("0d")
                                .Decay(decay)
                                .Weight(pageviewScore)
                            )
                        )
                    )
                )
                .Aggregations(a => a.Terms("shops", t => t.Field("Shop_event.Webshop_id")
                    .Field("Shop_event.Product_internal_id")
                    .Aggregations(a => a
                        .Sum("productscore", s => s.Script("_score"))))), cancellationToken);

            if (!cancellationToken.IsCancellationRequested)
            {
                if (response.IsValid)
                {
                    var aggs = response.Aggregations.Terms("shops");
                    foreach (var agg in aggs.Buckets)
                    {
                        result.Add(agg.Key, (decimal) (agg.Sum("productscore").Value ?? 0.0));
                    }
                }
            }

            return result;
        }

        public async Task SyncInactiveProductsFromSqlToElastic()
        {
            var inactiveProducts = await merchantService.GetInactiveProducts();
            var deletedCount = 0;

            var elasticClient = InitializeElasticClient();
            var count = 0;
            var total = inactiveProducts.Count;
            // Try to delete products from elastic
            foreach (var product in inactiveProducts)
            {
                count++;
                var response = await elasticClient.DeleteByQueryAsync(new DeleteByQueryRequest("merchants-products-search-alias")
                {
                    Query = new QueryContainer(new TermQuery { Field = "Product.Internal_product_id", Value = product })
                });

                if (response.IsValid && response.Deleted > 0)
                {
                    deletedCount++;
                }
                if (count % 100 == 0)
                {
                    Console.Write($"\r{count}/{total} products tried to be deleted");
                }
            }
        }

        /// <summary>
        /// Initializes and configures the Elasticsearch client.
        /// </summary>
        /// <returns>An instance of <see cref="ElasticsearchClient"/> configured with the specified settings.</returns>
        private ElasticClient InitializeElasticClient()
        {
            // TODO Remove hardcoded values and use configuration after Elastic Index Refinement
            var prodElasticApiKey = "T0l3N09ZOEI3d0NHQkIycEp1T3U6WFl5ZEhOaDhSLUtucVhZUGdpdXQ1dw==";
            var settings = new ConnectionSettings(new Uri(configuration["ElasticHost"]))
                .ApiKeyAuthentication(new ApiKeyAuthenticationCredentials(prodElasticApiKey))
                .RequestTimeout(TimeSpan.FromMinutes(2));
            return new ElasticClient(settings);
        }
    }
}