using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.EntityFrameworkCore;
using Shared.Models.Merchant;

namespace Webshop_Service.Services.Shopify;

public class ShopifyService : IShopifyService
{
    private readonly MerchantDbContext _merchantDbContext;

    public ShopifyService(MerchantDbContext merchantDbContext)
    {
        _merchantDbContext = merchantDbContext;
    }

    public async Task<PluginInstallInfo?> GetShopifyShop(string url)
    {
        return await _merchantDbContext.PluginInstallInfos.SingleOrDefaultAsync(a => a.Url == url)
            .ConfigureAwait(false);
    }

    public async Task<PluginInstallInfo?> RemoveShopifyShopAsync(string url)
    {
        var shopify = await _merchantDbContext.PluginInstallInfos.SingleOrDefaultAsync(a => a.Url == url)
            .ConfigureAwait(false);
        if (shopify != null)
        {
            _merchantDbContext.PluginInstallInfos.Remove(shopify);
            var webshop = await _merchantDbContext.Merchants.SingleOrDefaultAsync(a => a.Url == url)
                .ConfigureAwait(false);
            if (webshop != null)
            {
                webshop.Type = "";
            }

            await _merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
        }

        return shopify;
    }

    public async Task<PluginInstallInfo> CreateShopifyShopAsync(
        PluginInstallInfo pluginInstall)
    {
        _merchantDbContext.Add(pluginInstall);
        await _merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
        return pluginInstall;
    }

    public async Task<PluginInstallInfo> UpdateShopifyShopAsync(PluginInstallInfo shopifyOrg)
    {
        var shopify = await _merchantDbContext.PluginInstallInfos.SingleOrDefaultAsync(a => a.Url == shopifyOrg.Url)
            .ConfigureAwait(false);
        if (shopify != null)
        {
            shopify.ApiKey = shopifyOrg.ApiKey;
            shopify.ShopEmail = shopifyOrg.ShopEmail;
            shopify.LastModifiedDate = shopifyOrg.LastModifiedDate;
            shopify.Url = shopifyOrg.Url;
            shopify.Name = shopifyOrg.Name;
            _merchantDbContext.Update(shopify);
            await _merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
        }

        var merchantMetaApiUrl = await _merchantDbContext.MerchantMeta
            .SingleOrDefaultAsync(a =>
                a.Value == shopifyOrg.Url && a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUrl)
            .ConfigureAwait(false);
        
        if(merchantMetaApiUrl == null)
        {
            return shopifyOrg;
        }
        
        var merchantMetaApiKey = await _merchantDbContext.MerchantMeta
            .SingleOrDefaultAsync(a =>
                a.FkMerchantId == merchantMetaApiUrl.FkMerchantId && a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey)
            .ConfigureAwait(false);
        
        if (merchantMetaApiKey != null)
        {
            merchantMetaApiKey.Value = shopifyOrg.ApiKey;
            _merchantDbContext.Update(merchantMetaApiKey);
        }

        await _merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
        return shopifyOrg;
    }
}