using Merchant_Services.Models.ModelsDal.Merchant;

namespace Webshop_Service.Services.Shopify;

public interface IShopifyService
{
    Task<PluginInstallInfo?> GetShopifyShop(string url);
    Task<PluginInstallInfo?> RemoveShopifyShopAsync(string url);
    Task<PluginInstallInfo> CreateShopifyShopAsync(PluginInstallInfo pluginInstallInfo);
    Task<PluginInstallInfo> UpdateShopifyShopAsync(PluginInstallInfo pluginInstallInfo);
}