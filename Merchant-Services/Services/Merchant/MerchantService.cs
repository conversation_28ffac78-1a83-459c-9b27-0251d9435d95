using System.Collections.Concurrent;
using System.Data;
using System.IdentityModel.Tokens.Jwt;
using System.Linq.Expressions;
using System.Net;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Web;
using Admin_Service.Models.MonitorEventWebshop;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Shared.Dto.MerchantDashboard.Settings;
using Shared.Dto.MerchantScore;
using Shared.Dto.Shop;
using Shared.Dto.Shop.Product;
using Shared.Dto.Webshop;
using Shared.Elastic.Elastic;
using Shared.Elastic.MerchantCustomerEvent;
using Shared.Helpers.Extractors;
using Shared.Helpers.Replacers;
using Shared.Models;
using Shared.Models.Merchant;
using Shared.Models.Merchant.Assets;
using Shared.Services;
using Shared.Services.Cache;
using Shared.Services.Image;
using Shared.Services.Partner;
using Shared.WebServiceService;
using ShopifySharp;
using SixLabors.Fonts;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Drawing.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Formats.Webp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using Webshop.Webshop;
using Webshops_Services.Models;
using ILogger = Serilog.ILogger;
using Product = Merchant_Services.Models.ModelsDal.Merchant.Product;
using User = Merchant_Services.Models.ModelsDal.Merchant.User;
using UserGroup = Merchant_Services.Models.ModelsDal.Merchant.UserGroup;
using SixLabors.ImageSharp.Drawing;

namespace Webshop_Service.Services.Webshop;

public class MerchantService(
    MerchantDbContext merchantDbContext,
    ILogger logger,
    IElasticService elasticService,
    ICacheService cacheService,
    VaultSettings vaultSettings,
    IImageService imageService,
    IConfiguration configuration,
    IMemoryCache memoryCache,
    IElasticMerchantCustomerEventService elasticMerchantCustomerEventService,
    IPartnerContext partnerContext)
    : IMerchantService
{
    private const string Unknown =
        "An unexpected error occurred. If the issue persists, contact tech for further assistance";

    public async Task<Merchant?> ValidateAsync(string apiKey, bool? forceUpdate = null)
    {
        var cacheKey = $"MerchantService_ValidateAsync_{apiKey}";
        var merchant = await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(6);
            var merchant = (await merchantDbContext.MerchantMeta
                .AsNoTracking()
                .Include(a => a.FkMerchant)
                .FirstOrDefaultAsync(a =>
                    a.Value == apiKey && a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey &&
                    a.FkMerchant.Active)
                .ConfigureAwait(false))?.FkMerchant;
            return merchant;
        });

        return merchant;
    }

    public async Task<Merchant?> ValidateShopIdentifierAsync(string shopIdentifier, bool? forceUpdate = null)
    {
        var cacheKey = $"MerchantService_ValidateShopIdentifierAsync_{shopIdentifier}";
        var merchant = await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(6);
            var merchant = (await merchantDbContext.MerchantMeta
                .AsNoTracking()
                .Include(a => a.FkMerchant)
                .FirstOrDefaultAsync(a =>
                    a.Value == shopIdentifier && a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ShopIdentifier &&
                    a.FkMerchant.Active)
                .ConfigureAwait(false))?.FkMerchant;
            return merchant;
        });

        return merchant;
    }

    public async Task<Merchant?> ValidateUrlAsync(string url, bool? forceUpdate = null)
    {
        url = new Uri(url).Host;
        var merchant = await memoryCache.GetOrCreateAsync(
            $"MerchantService_ValidateUrlAsync_{url}",
            async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(4);

                //var uri = new Uri(url);
                var potentialMerchants = await merchantDbContext.Merchants
                    .Include(a => a.MerchantMeta)
                    .Where(a => !string.IsNullOrEmpty(a.Url) && a.Active)
                    .ToListAsync();

                var validMerchants = potentialMerchants.Where(a =>
                {
                    try
                    {
                        var merchantUri = new Uri(a.Url);
                        return true;
                    }
                    catch (UriFormatException)
                    {
                        logger?.ForContext("service_name", GetType().Name)
                            .Warning("Invalid merchant URL: {Url}", a.Url);
                        return false;
                    }
                }).ToList();

                var merchant = validMerchants
                    .SingleOrDefault(a => new Uri(a.Url).Host == url);
                if (merchant == null)
                {
                    try
                    {
                        //uri = new Uri(url.Replace("www.", ""));
                        url = url.Replace("www.", "");
                        merchant = validMerchants
                            .SingleOrDefault(a => new Uri(a.Url).Host.Contains(url));
                    }
                    catch (UriFormatException)
                    {
                        // Handle invalid URL format after replacing "www."
                        return null;
                    }
                }

                return merchant;
            });

        return merchant;
    }

    public async Task<Merchant?> ValidateOrderApiKey(string apiKey, bool? forceUpdate = null)
    {
        apiKey = HttpUtility.UrlDecode(apiKey);
        var cacheKey = $"MerchantService_ValidateOrderApiKey_{apiKey}";
        var merchant = await cacheService.GetData<Merchant>(cacheKey);
        if (merchant == null || forceUpdate == true)
        {
            merchant = (await merchantDbContext.MerchantMeta
                .AsNoTracking()
                .Include(a => a.FkMerchant)
                .ThenInclude(a => a.MerchantOrderStatuses.Where(b => b.Active))
                .FirstOrDefaultAsync(a =>
                    a.Value == apiKey && a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey &&
                    a.FkMerchant.Active)
                .ConfigureAwait(false))?.FkMerchant;
            cacheService.SetData(cacheKey, merchant, TimeSpan.FromHours(6));
        }

        return merchant;
    }

    public async Task<Merchant?> GetByApikeyAsync(string apiKey)
    {
        var merchantId = (await merchantDbContext.MerchantMeta
            .AsNoTracking()
            .FirstOrDefaultAsync(a =>
                a.Value == apiKey && a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey && a.FkMerchant.Active)
            .ConfigureAwait(false))?.FkMerchantId;
        return await GetByIdFullAsync(merchantId ?? 0);
    }

    public async Task<Merchant?> GetByIdAsync(int merchantId, bool? forceUpdate = false)
    {
        //Memory cache
        var key = $"MerchantService_GetByIdAsync_{merchantId}";
        if (forceUpdate == true)
        {
            memoryCache.Remove(key);
        }

        var merchant = await memoryCache.GetOrCreateAsync(key, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(6);
            return await merchantDbContext.Merchants
                .Include(a => a.MerchantMeta)
                .Where(a => a.Id == merchantId && a.Active)
                .FirstOrDefaultAsync().ConfigureAwait(false);
        });
        return merchant;
    }

    //This ignore if the customer is set to activated in the system
    public async Task<Merchant?> GetByIdIgnoreActiveAsync(int merchantId)
    {
        var webshop = await merchantDbContext.Merchants
            .Where(a => a.Id == merchantId)
            .FirstOrDefaultAsync().ConfigureAwait(false);
        return webshop;
    }

    public async Task<Merchant?> GetByIdFullAsync(int merchantId, bool? forceUpdate = false)
    {
        var cacheKey = $"MerchantService_GetByIdFullAsync_{merchantId}";
        var merchant = await cacheService.GetData<Merchant>(cacheKey);
        if (merchant == null || forceUpdate == true)
        {
            merchant = await merchantDbContext.Merchants
                .Where(a => a.Id == merchantId && a.Active)
                .Include(a => a.MerchantPayments)
                .Include(a => a.MerchantMeta)
                .Include(a => a.MerchantPayers)
                .ThenInclude(a => a.FkPayer)
                .Include(a => a.MerchantAssets.Where(b => b.Active))
                .ThenInclude(a => a.FkMerchantAssetType)
                .Include(a => a.MerchantOrderStatuses.Where(b => b.Active))
                .FirstOrDefaultAsync().ConfigureAwait(false);
            cacheService.SetData(cacheKey, merchant, TimeSpan.FromHours(3));
        }

        return merchant;
    }

    public async Task<Merchant?> GetByIdReadyForUpdate(int merchantId)
    {
        var webshop = await merchantDbContext.Merchants
            .Where(a => a.Id == merchantId)
            .Include(a => a.MerchantMeta)
            .Include(a => a.MerchantOrderStatuses.Where(b => b.Active))
            .AsNoTracking()
            .FirstOrDefaultAsync().ConfigureAwait(false);
        return webshop;
    }

    public async Task<List<Merchant>> GetMerchantsReadyForUpdate(
        int lookBackDaysOrder, int lookBackDaysProduct, bool onlyNew)
    {
        //Get webshops there is ready for update
        var newerOrder = DateTime.UtcNow.AddHours(-10);
        var newerWebshopData = DateTime.UtcNow.AddHours(-10);
        var lastSync = DateTime.UtcNow.AddHours(-5);
        var merchantsQuery = merchantDbContext.Merchants
            .Include(a => a.MerchantMeta)
            .Include(a => a.MerchantOrderStatuses)
            .Where(a => !string.IsNullOrEmpty(a.Url) &&
                        a.Active &&
                        a.Sync == true &&
                        !a.IsDev &&
                        (a.LastAttemptedSync < lastSync || a.LastAttemptedSync == null) &&
                        a.Type.ToLower() != "custom" &&
                        a.Type.ToLower() != "campaign" &&
                        a.Type.ToLower() != "" &&
                        a.MerchantMeta.Any(b =>
                            b.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer && b.EndDate == null ||
                            b.EndDate > DateTime.UtcNow)
            );
        if (onlyNew)
        {
            merchantsQuery = merchantsQuery.Where(a => a.LastProductSync == null || a.LastOrderSync == null)
                .AsNoTracking();
        }
        else
        {
            if (lookBackDaysOrder != 0 && lookBackDaysProduct != 0)
            {
                merchantsQuery = merchantsQuery.Where(a => (a.LastOrderSync == null ||
                                                            a.LastOrderSync <= newerOrder ||
                                                            a.LastProductSync == null ||
                                                            a.LastProductSync <= newerWebshopData)).AsNoTracking();
            }
            else if (lookBackDaysOrder != 0)
            {
                merchantsQuery = merchantsQuery.Where(a => (a.LastOrderSync == null || a.LastOrderSync <= newerOrder))
                    .AsNoTracking();
            }
            else if (lookBackDaysProduct != 0)
            {
                merchantsQuery = merchantsQuery
                    .Where(a => (a.LastProductSync == null || a.LastProductSync <= newerWebshopData)).AsNoTracking();
            }
        }

        var merchants = await merchantsQuery.OrderBy(a => a.LastProductSync).ToListAsync().ConfigureAwait(false);
        merchants = merchants.Take(2).ToList();
        //Update last sync to give it time for update
        foreach (var merchant in merchants)
        {
            merchant.LastAttemptedSync = DateTime.UtcNow;
            merchantDbContext.Update(merchant);
            await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
        }

        merchantDbContext.ChangeTracker.Clear();
        return merchants;
    }

    public async Task<List<Merchant>> GetFullAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var cacheKey = $"MerchantService_GetFullAsync_{partnerId}";
        return await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(8);
            return await merchantDbContext.Merchants
                .Include(a => a.MerchantPayments)
                .Include(a => a.MerchantMeta)
                .Include(a => a.MerchantOrderStatuses.Where(b => b.Active))
                .Include(a => a.MerchantAssets.Where(b => b.Active))
                .ThenInclude(a => a.FkMerchantAssetType)
                .Where(a => !a.IsDev && a.Active && a.FkPartnerId == partnerId)
                .OrderByDescending(a => a.IsMarketingAllowed)
                .ThenBy(a => a.Name)
                .ToListAsync()
                .ConfigureAwait(false);
        }) ?? [];
    }

    public async Task<List<Merchant>> GetSimpleAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var merchants = await merchantDbContext.Merchants
            .Include(a => a.MerchantMeta)
            .Where(a => !a.IsDev && a.Active && a.IsMarketingAllowed && a.FkPartnerId == partnerId)
            .OrderByDescending(a => a.Name)
            .ToListAsync()
            .ConfigureAwait(false);

        return merchants ?? [];
    }


    public async Task<List<Merchant>> GetAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var merchants = await memoryCache.GetOrCreateAsync($"MerchantService_GetAsync_{partnerId}", async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(6);
            return await merchantDbContext.Merchants.Where(a =>
                    a.Type != "EMPTY" && a.Url != null && a.Active && a.FkPartnerId == partnerId)
                .ToListAsync().ConfigureAwait(false);
        });
        return merchants ?? new List<Merchant>();
    }

    public async Task<List<Merchant>> GetAllAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var merchants = await merchantDbContext.Merchants
            .AsNoTracking()
            .Include(a => a.MerchantCategoryRels)
            .ThenInclude(a => a.FkMerchantCategory)
            .Include(a => a.MerchantMeta)
            .OrderBy(a => a.Name)
            .Where(a => !a.IsDev && a.Active && a.FkPartnerId == partnerId)
            .ToListAsync().ConfigureAwait(false);

        foreach (var webshopCategoryRef in merchants.SelectMany(merchant => merchant.MerchantCategoryRels))
        {
            webshopCategoryRef.FkMerchantCategory.MerchantCategoryRels = new List<MerchantCategoryRel>();
            webshopCategoryRef.FkMerchantCategory.FkParent = null;
        }

        return merchants;
    }

    public async Task<List<Merchant>> GetAllCustomersAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var cacheKey = $"AllCustomers_{partnerId}";
        if (memoryCache.TryGetValue(cacheKey, out List<Merchant> cachedMerchants))
        {
            return cachedMerchants;
        }

        var now = DateTime.UtcNow;
        var merchants = await merchantDbContext.Merchants
            .AsNoTracking()
            .Include(a => a.MerchantMeta)
            .Where(a => !a.IsDev && a.Active && a.FkPartnerId == partnerId && a.MerchantMeta.Any(m =>
                m.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer && (m.EndDate == null || m.EndDate > now)))
            .OrderBy(a => a.Name)
            .ToListAsync().ConfigureAwait(false);

        memoryCache.Set(cacheKey, merchants, TimeSpan.FromHours(2));
        return merchants;
    }

    public async Task<List<int>> GetAllIdsAsync()
    {
        var partnerId = partnerContext.PartnerId;
        return await merchantDbContext.Merchants.Where(a =>
                !a.IsDev &&
                a.IsMarketingAllowed &&
                a.Active &&
                a.Sync == true &&
                a.FkPartnerId == partnerId)
            .Select(a => a.Id)
            .ToListAsync().ConfigureAwait(false);
    }

    public async Task<List<int>> GetAllMerchantIdsWithAllowedMarketingByPartnerIdAsync(int partnerId)
    {
        return await merchantDbContext.Merchants.Where(a =>
                !a.IsDev &&
                a.IsMarketingAllowed &&
                a.Active &&
                a.FkPartnerId == partnerId)
            .Select(a => a.Id)
            .ToListAsync().ConfigureAwait(false);
    }


    public async Task<List<int>> GetAllIdsByPartnerIdAsync()
    {
        var partnerId = partnerContext.PartnerId;
        return await merchantDbContext.Merchants.Where(a =>
                !a.IsDev &&
                a.FkPartnerId == partnerId)
            .Select(a => a.Id)
            .ToListAsync().ConfigureAwait(false);
    }

    public async Task<Dictionary<int, string>> GetAllNamesAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var cacheKey = $"MerchantService_GetAllNamesAsync_{partnerId}";
        return await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(2);
            return await merchantDbContext.Merchants
                .Where(a => !a.IsDev && a.Active && a.FkPartnerId == partnerId)
                .ToDictionaryAsync(merchant => merchant.Id, merchant => merchant.DisplayName)
                .ConfigureAwait(false);
        }) ?? [];
    }

    public async Task<List<string>> GetNamesByIdsAsync(List<int> merchantIds)
    {
        var cacheKey = $"MerchantService_MerchantNamesById_{string.Join("_", merchantIds)}";
        return await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(4);
            return await merchantDbContext.Merchants
                .Where(a => merchantIds.Contains(a.Id))
                .Select(a => a.DisplayName)
                .ToListAsync()
                .ConfigureAwait(false);
        }) ?? [];
    }


    public async Task<Merchant?> GetPaymentByIdAsync(int merchantId)
    {
        var merchant = await memoryCache.GetOrCreateAsync("GetPaymentByIdAsync" + merchantId, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(2);
            return await merchantDbContext.Merchants
                .Include(a => a.MerchantPayments)
                .Include(a => a.MerchantMeta)
                .Where(a => a.Id == merchantId && a.Active)
                .FirstOrDefaultAsync().ConfigureAwait(false);
        });
        return merchant;
    }

    public async Task<ProductReturnDto?> SearchProductAsync(SearchProductDto searchProduct)
    {
        Product? product = null;
        if (!string.IsNullOrEmpty(searchProduct.ProductId))
        {
            product = await merchantDbContext.Products
                .AsNoTracking()
                .FirstOrDefaultAsync(a => a.FkMerchantId == searchProduct.WebshopId &&
                                          a.MerchantProductId == searchProduct.ProductId).ConfigureAwait(false);
        }
        else if (!string.IsNullOrEmpty(searchProduct.Sku))
        {
            product = await merchantDbContext.Products
                .AsNoTracking()
                .FirstOrDefaultAsync(a => a.FkMerchantId == searchProduct.WebshopId &&
                                          a.Sku == searchProduct.Sku).ConfigureAwait(false);
        }
        else if (!string.IsNullOrEmpty(searchProduct.FullUrl))
        {
            product = await merchantDbContext.Products
                .AsNoTracking()
                .FirstOrDefaultAsync(a => a.FkMerchantId == searchProduct.WebshopId &&
                                          a.Permalink == searchProduct.FullUrl).ConfigureAwait(false);
        }
        //Removed for optimizing
        /*else if (!string.IsNullOrEmpty(searchProduct.Url))
        {
            product = await _merchantDbContext.Products
                .AsNoTracking()
                .FirstOrDefaultAsync(a => a.FkMerchantId == searchProduct.WebshopId &&
                                          a.Permalink.Contains(searchProduct.Url)).ConfigureAwait(false);
        }*/

        if (product != null)
        {
            return MapProductToReturn(product);
        }

        Variant? variant = null;
        if (!string.IsNullOrEmpty(searchProduct.ProductId))
        {
            variant = await merchantDbContext.Variants
                .AsNoTracking()
                .Include(a => a.FkProduct)
                .FirstOrDefaultAsync(a => a.FkProduct.FkMerchantId == searchProduct.WebshopId &&
                                          a.MerchantProductId == searchProduct.ProductId).ConfigureAwait(false);
        }
        else if (!string.IsNullOrEmpty(searchProduct.Sku))
        {
            variant = await merchantDbContext.Variants
                .AsNoTracking()
                .Include(a => a.FkProduct)
                .FirstOrDefaultAsync(a => a.FkProduct.FkMerchantId == searchProduct.WebshopId &&
                                          a.Sku == searchProduct.Sku).ConfigureAwait(false);
        }
        else if (!string.IsNullOrEmpty(searchProduct.FullUrl))
        {
            variant = await merchantDbContext.Variants
                .AsNoTracking()
                .Include(a => a.FkProduct)
                .FirstOrDefaultAsync(a => a.FkProduct.FkMerchantId == searchProduct.WebshopId &&
                                          a.Permalink == searchProduct.FullUrl).ConfigureAwait(false);
        }
        //Removed for optimizing
        /*else if (!string.IsNullOrEmpty(searchProduct.Url))
        {
            variant = await _merchantDbContext.Variants
                .AsNoTracking()
                .Include(a => a.FkProduct)
                .FirstOrDefaultAsync(a => a.FkProduct.FkMerchantId == searchProduct.WebshopId &&
                                          a.Permalink != null && a.Permalink.Contains(searchProduct.Url))
                .ConfigureAwait(false);
        }*/

        if (variant != null)
        {
            return MapProductToReturn(variant);
        }

        return null;
    }

    public async Task<List<MerchantScore>> GetMerchantScoreByAgeAndGender(byte? age, string gender)
    {
        var partnerId = partnerContext.PartnerId;
        if (age == 0)
            age = null;

        var merchantScores = await memoryCache.GetOrCreateAsync(
            $"MerchantService_GetMerchantScoreByAgeAndGender_{age}_{gender}_{partnerId}",
            async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(20);

                var groupedScores = await merchantDbContext.RelevanceScores
                    .AsNoTracking()
                    .Where(a => a.Age == age && a.Gender == gender && a.Active && a.FkMerchant.FkPartnerId == partnerId)
                    .Include(a => a.FkMerchant)
                    .GroupBy(a => a.FkMerchantId)
                    .ToListAsync();

                var merchantScoresList = groupedScores
                    .Select(g => g.OrderByDescending(a => a.LastModifiedDate).First())
                    .Select(a => new MerchantScore
                    {
                        Sum = Convert.ToDouble(a.ScoreSum),
                        MerchantId = a.FkMerchantId.ToString()
                    })
                    .OrderByDescending(a => a.Sum)
                    .ToList();

                return merchantScoresList;
            });

        return merchantScores ?? [];
    }

    /// <summary>
    /// Updates the relevance scores for merchants in bulk.
    /// </summary>
    /// <param name="relevanceScores">A list of <see cref="RelevanceScoreDto"/> objects containing the relevance scores to be updated.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    public async Task BulkUpdateMerchantRelevanceScoresAsync(List<RelevanceScoreDto> relevanceScores)
    {
        // Get distinct merchant IDs from the provided relevance scores
        var merchantIds = relevanceScores.Select(dto => dto.MerchantId).Distinct().ToList();

        // Retrieve existing relevance scores from the database for the given merchant IDs
        var existingRelevanceScores = await merchantDbContext.RelevanceScores
            .Where(rs => merchantIds.Contains(rs.FkMerchantId))
            .ToListAsync()
            .ConfigureAwait(false);

        var newRelevanceScores = new List<RelevanceScore>();
        var updatedRelevanceScores = new List<RelevanceScore>();

        // Iterate through each relevance score DTO
        foreach (var dto in relevanceScores)
        {
            // Find the existing relevance score that matches the criteria
            var existingRelevanceScore = existingRelevanceScores
                .FirstOrDefault(rs => rs.FkMerchantId == dto.MerchantId
                                      && rs.Gender == dto.Gender
                                      && rs.Age == dto.Age
                                      && rs.AgeInterval == dto.AgeInterval
                                      && rs.ScoreType == dto.ScoreType);

            if (existingRelevanceScore == null)
            {
                // If no existing relevance score is found, create a new one
                newRelevanceScores.Add(new RelevanceScore
                {
                    Id = 0,
                    Active = true,
                    FkMerchantId = dto.MerchantId,
                    Gender = dto.Gender,
                    Age = dto.Age,
                    AgeInterval = dto.AgeInterval,
                    ScoreType = dto.ScoreType,
                    ScoreSum = dto.ScoreSum
                });
            }
            else if (existingRelevanceScore.ScoreSum != dto.ScoreSum)
            {
                // If an existing relevance score is found and the score sum is different, update it
                existingRelevanceScore.ScoreSum = dto.ScoreSum;
                existingRelevanceScore.LastModifiedDate = DateTime.UtcNow;
                existingRelevanceScore.Active = true;
                updatedRelevanceScores.Add(existingRelevanceScore);
            }
        }

        // Add new relevance scores to the database
        if (newRelevanceScores.Count != 0)
        {
            await merchantDbContext.RelevanceScores.AddRangeAsync(newRelevanceScores).ConfigureAwait(false);
        }

        // Update existing relevance scores in the database
        if (updatedRelevanceScores.Count != 0)
        {
            merchantDbContext.RelevanceScores.UpdateRange(updatedRelevanceScores);
        }

        // Save changes to the database
        await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
    }

    private async Task DisableMerchantRelevanceScoreAsync(int merchantId)
    {
        var relevanceScores = await merchantDbContext.RelevanceScores
            .Where(a => a.FkMerchantId == merchantId)
            .ToListAsync().ConfigureAwait(false);
        foreach (var relevanceScore in relevanceScores)
        {
            relevanceScore.Active = false;
            merchantDbContext.Update(relevanceScore);
        }

        await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
    }

    // Pietras Function - i will try to improve it
    public async Task<ProductReturnEventDto?> SearchProductEventAsync(SearchProductDto searchProduct)
    {
        //return null;
        ProductReturnEventDto? result;

        var productReturnEventDto = await memoryCache.GetOrCreateAsync(
            $"MerchantService_SearchProductEventAsync_{searchProduct.WebshopId}_{searchProduct.ProductId}_{searchProduct.Sku}_{searchProduct.FullUrl}_{searchProduct.Url}",
            async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(2);

                try
                {
                    result = await elasticMerchantCustomerEventService.ProductLookUp(searchProduct);
                    if (result != null) return result;
                }
                catch (Exception ex)
                {
                    logger.ForContext("service_name", GetType().Name).Error(ex,
                        "Error fetching product from {Storage_type}", "Elasticsearch");
                }

                //Product
                if (!string.IsNullOrEmpty(searchProduct.ProductId))
                {
                    result = await FetchProductAsync(a =>
                        a.FkMerchantId == searchProduct.WebshopId && a.MerchantProductId == searchProduct.ProductId);
                    if (result != null) return result;
                }
                else if (!string.IsNullOrEmpty(searchProduct.Sku))
                {
                    result = await FetchProductAsync(
                        a => a.FkMerchantId == searchProduct.WebshopId && a.Sku == searchProduct.Sku);
                    if (result != null) return result;
                }
                else if (!string.IsNullOrEmpty(searchProduct.FullUrl))
                {
                    result = await FetchProductAsync(a =>
                        a.FkMerchantId == searchProduct.WebshopId && a.Permalink == searchProduct.FullUrl);
                    if (result != null) return result;
                }
                //Removed for optimizing
                /*else if (!string.IsNullOrEmpty(searchProduct.Url))
                {
                    result = await FetchProductAsync(a =>
                        a.FkMerchantId == searchProduct.WebshopId && a.Permalink.Contains(searchProduct.Url));
                    if (result != null) return result;
                }*/

                ////////////////////////////////
                //////////Variant///////////////
                ////////////////////////////////
                if (!string.IsNullOrEmpty(searchProduct.ProductId))
                {
                    result = await FetchVariantAsync(a =>
                        a.FkProduct.FkMerchantId == searchProduct.WebshopId &&
                        a.MerchantProductId == searchProduct.ProductId);
                    if (result != null) return result;
                }
                else if (!string.IsNullOrEmpty(searchProduct.Sku))
                {
                    result = await FetchVariantAsync(a =>
                        a.FkProduct.FkMerchantId == searchProduct.WebshopId && a.Sku == searchProduct.Sku);
                    if (result != null) return result;
                }
                else if (!string.IsNullOrEmpty(searchProduct.FullUrl))
                {
                    result = await FetchVariantAsync(a =>
                        a.FkProduct.FkMerchantId == searchProduct.WebshopId && a.Permalink == searchProduct.FullUrl);
                    if (result != null) return result;
                }

                //Removed for optimizing
                /*else if (!string.IsNullOrEmpty(searchProduct.Url))
                {
                    result = await FetchVariantAsync(a =>
                        a.FkProduct.FkMerchantId == searchProduct.WebshopId && a.Permalink.Contains(searchProduct.Url));
                    if (result != null) return result;
                }*/
                return null;
            });

        return productReturnEventDto;
    }


    private async Task<ProductReturnEventDto?> FetchProductAsync(Expression<Func<Product, bool>> predicate)
    {
        return await merchantDbContext.Products
            .AsNoTracking()
            .Where(predicate)
            .Select(a => new ProductReturnEventDto
            {
                Sku = a.Sku ?? "",
                ParentInternalProductId = "",
                Price = a.Price,
                InternalProductId = a.InternalProductId,
            })
            .FirstOrDefaultAsync()
            .ConfigureAwait(false);
    }

    private async Task<ProductReturnEventDto?> FetchVariantAsync(Expression<Func<Variant, bool>> predicate)
    {
        return await merchantDbContext.Variants
            .AsNoTracking()
            .Where(predicate)
            .Select(a => new ProductReturnEventDto
            {
                Sku = a.Sku ?? "",
                ParentInternalProductId = a.FkProduct.InternalProductId,
                Price = a.Price,
                InternalProductId = a.InternalVariantId,
            })
            .FirstOrDefaultAsync()
            .ConfigureAwait(false);
    }

    public async Task<ProductReturnDto?> SearchProductByInternalProductIdAsync(string internalProductId,
        bool productValidate = false)
    {
        var cacheKey = $"MerchantService_SearchProductByInternalProductIdAsync_{internalProductId}";
        var productReturn = await cacheService.GetData<ProductReturnDto>(cacheKey);
        if (productReturn == null)
        {
            var product = await merchantDbContext.Products
                .FirstOrDefaultAsync(a => a.InternalProductId == internalProductId)
                .ConfigureAwait(false);
            if (product != null)
            {
                productReturn = MapProductToReturn(product, productValidate);
                cacheService.SetData(cacheKey, productReturn, TimeSpan.FromHours(12));
                return productReturn;
            }

            var variant = await merchantDbContext.Variants
                .Include(a => a.FkProduct)
                .ThenInclude(a => a.ProductImages)
                .FirstOrDefaultAsync(a => a.InternalVariantId == internalProductId).ConfigureAwait(false);

            if (variant != null)
            {
                productReturn = MapProductToReturn(variant, productValidate);
                cacheService.SetData(cacheKey, productReturn, TimeSpan.FromHours(12));
                return productReturn;
            }

            return null;
        }

        return productReturn;
    }

    public async Task UpdateLastOrderSyncAsync(Merchant merchant)
    {
        merchantDbContext.ChangeTracker.Clear();
        merchant.Products = new List<Product>();
        merchant.MerchantMeta = new List<MerchantMetum>();
        merchant.MerchantPayments = new List<MerchantPayment>();
        merchantDbContext.Attach(merchant);
        merchant.LastOrderSync = DateTime.UtcNow;
        merchantDbContext.Entry(merchant).Property(x => x.LastOrderSync).IsModified = true;
        await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
    }

    public async Task UpdateMerchantProductsAsync(Merchant merchant, bool fullCatalog)
    {
        if (fullCatalog)
        {
            await DisableInactiveProductsAsync(merchant);
        }

        //Remove duplicates of products
        merchant.Products = merchant.Products.DistinctBy(a => a.MerchantProductId).ToList();
        var allMerchantProducts = merchant.Products;
        merchant.Products = new System.Collections.Generic.List<Product>();
        const int batchSizeProduct = 25000;
        const int milliSecondsUpdateDelayBatch = 100;
        var maxElasticSyncDate = await GetMaxElasticSyncDate();

        int totalBatches = (int) Math.Ceiling((double) allMerchantProducts.Count / batchSizeProduct);
        Console.WriteLine($"\nSynchronizing Products for merchant: {merchant.DisplayName}\n");

        merchantDbContext.ChangeTracker.Clear();
        merchant.LastProductSync = DateTime.UtcNow;
        await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
        merchantDbContext.ChangeTracker.Clear();

        DataTable productTable = new DataTable();
        productTable.Columns.Add("MerchantId", typeof(int));
        productTable.Columns.Add("MerchantProductId", typeof(string));
        productTable.Columns.Add("Name", typeof(string));
        productTable.Columns.Add("Sku", typeof(string));
        productTable.Columns.Add("Permalink", typeof(string));
        productTable.Columns.Add("Status", typeof(string));
        productTable.Columns.Add("Description", typeof(string));
        productTable.Columns.Add("FormattedDescription", typeof(string));
        productTable.Columns.Add("ShortDescription", typeof(string));
        productTable.Columns.Add("Price", typeof(decimal));
        productTable.Columns.Add("RegularPrice", typeof(decimal));
        productTable.Columns.Add("StockQuantity", typeof(int));
        productTable.Columns.Add("IsInStock", typeof(bool));
        productTable.Columns.Add("Active", typeof(int));
        productTable.Columns.Add("ProductImages", typeof(string));
        productTable.Columns.Add("Categories", typeof(string));
        productTable.Columns.Add("LastModifiedDate", typeof(DateTime));
        productTable.Columns.Add("MerchantCreatedDate", typeof(DateTime));
        productTable.Columns.Add("MerchantModifiedDate", typeof(DateTime));
        productTable.Columns.Add("ElasticSyncDate", typeof(DateTime));

        DataTable variantTable = new DataTable();
        variantTable.Columns.Add("MerchantId", typeof(int));
        variantTable.Columns.Add("MerchantProductId", typeof(string));
        variantTable.Columns.Add("MerchantVariantId", typeof(string));
        variantTable.Columns.Add("Name", typeof(string));
        variantTable.Columns.Add("Sku", typeof(string));
        variantTable.Columns.Add("Permalink", typeof(string));
        variantTable.Columns.Add("Status", typeof(string));
        variantTable.Columns.Add("Description", typeof(string));
        variantTable.Columns.Add("FormattedDescription", typeof(string));
        variantTable.Columns.Add("ShortDescription", typeof(string));
        variantTable.Columns.Add("Price", typeof(decimal));
        variantTable.Columns.Add("RegularPrice", typeof(decimal));
        variantTable.Columns.Add("StockQuantity", typeof(int));
        variantTable.Columns.Add("Active", typeof(int));
        variantTable.Columns.Add("ProductImages", typeof(string));
        variantTable.Columns.Add("LastModifiedDate", typeof(DateTime));
        variantTable.Columns.Add("MerchantCreatedDate", typeof(DateTime));
        variantTable.Columns.Add("MerchantModifiedDate", typeof(DateTime));

        var millisecondsUpdateDelay = 1;

        for (int i = 0, batchNumber = 1; i <= allMerchantProducts.Count; i += batchSizeProduct, batchNumber++)
        {
            Console.WriteLine($"\rProcessing batch {batchNumber} of {totalBatches}...");
            var start = DateTime.UtcNow;
            //Get batch to update
            var batch = allMerchantProducts.Skip(i).Take(batchSizeProduct).ToList();
            merchant.Products = batch;

            try
            {
                productTable.Rows.Clear();
                variantTable.Rows.Clear();
                int updateDelayIndex = 0;
                foreach (var product in merchant.Products)
                {
                    if (updateDelayIndex == milliSecondsUpdateDelayBatch)
                    {
                        maxElasticSyncDate = maxElasticSyncDate.AddMilliseconds(milliSecondsUpdateDelayBatch);
                        updateDelayIndex = 0;
                    }

                    updateDelayIndex++;

                    //Products START
                    var originalProductDescription = product.Description ?? "";
                    product.ElasticSyncDate = maxElasticSyncDate;
                    product.Description = HtmlReplacer.RemoveHtmlTags(originalProductDescription);
                    product.FormattedDescription = HtmlReplacer.RemoveHtmlTagsAndContent(originalProductDescription);
                    product.ShortDescription = HtmlReplacer.RemoveHtmlTags(product.ShortDescription ?? "");
                    if (product.Description.Trim() == "")
                    {
                        product.Description = null;
                    }

                    if (product.ShortDescription.Trim() == "")
                    {
                        product.ShortDescription = null;
                    }

                    //If no images set as disabled
                    if (product.ProductImages == null || product.ProductImages.Trim() == "" ||
                        product.ProductImages.Trim() == "[]")
                    {
                        product.Active = false;
                        product.ProductImages = null;
                    }

                    //If sale price is empty set to null instead of 0
                    if (product.Price == 0)
                    {
                        product.Price = null;
                    }

                    if (product.RegularPrice == 0)
                    {
                        if (product.Price != null)
                        {
                            product.RegularPrice = product.Price;
                            product.Price = null;
                        }
                    }

                    if (string.IsNullOrEmpty(product.Name.Trim()) || product.Name.Trim() == "NULL")
                    {
                        product.Name = string.Empty;
                    }

                    //Categories
                    if (product.Categories?.Length > 2499)
                    {
                        product.Categories = product.Categories.Substring(0, 2499);
                    }

                    productTable.Rows.Add(merchant.Id, product.MerchantProductId, product.Name, product.Sku,
                        product.Permalink,
                        product.Status, product.Description, product.FormattedDescription, product.ShortDescription,
                        product.Price, product.RegularPrice, product.StockQuantity, product.IsInStock,
                        product.Active, product.ProductImages, product.Categories, product.LastModifiedDate,
                        product.MerchantCreatedDate, product.MerchantModifiedDate, product.ElasticSyncDate);
                    //Products END

                    //Variants START
                    //Remove duplicates of variants
                    product.Variants = product.Variants.DistinctBy(a => a.MerchantProductId).ToList();
                    foreach (var variant in product.Variants)
                    {
                        var originalVariantDescription = variant.Description ?? "";
                        variant.LastModifiedDate = variant.LastModifiedDate.AddMilliseconds(millisecondsUpdateDelay);
                        variant.Description = HtmlReplacer.RemoveHtmlTags(originalVariantDescription);
                        variant.FormattedDescription =
                            HtmlReplacer.RemoveHtmlTagsAndContent(originalVariantDescription);
                        variant.ShortDescription = HtmlReplacer.RemoveHtmlTags(variant.ShortDescription ?? "");
                        if (variant.Description.Trim() == "")
                        {
                            variant.Description = null;
                        }

                        if (variant.ShortDescription.Trim() == "")
                        {
                            variant.ShortDescription = null;
                        }

                        //If no images set as disabled
                        if (variant.ProductImages == null ||
                            variant.ProductImages.Trim() == "" | variant.ProductImages.Trim() == "[]")
                        {
                            variant.Active = false;
                            variant.ProductImages = null;
                        }

                        //If sale price is empty set to null instead of 0
                        if (variant.Price == 0)
                        {
                            variant.Price = null;
                        }

                        if (string.IsNullOrEmpty(variant.Name.Trim()) || variant.Name.Trim() == "NULL")
                        {
                            variant.Name = string.Empty;
                        }

                        variantTable.Rows.Add(merchant.Id, variant.MerchantProductId, variant.MerchantVariantId,
                            variant.Name, variant.Sku, variant.Permalink, variant.Status, variant.Description,
                            variant.FormattedDescription,
                            variant.ShortDescription, variant.Price, variant.RegularPrice, variant.StockQuantity,
                            variant.Active, variant.ProductImages, variant.LastModifiedDate,
                            variant.MerchantCreatedDate, variant.MerchantModifiedDate);
                    }
                    //Variants END
                }

                if (productTable.Rows.Count > 0)
                {
                    using (var command = merchantDbContext.Database.GetDbConnection().CreateCommand())
                    {
                        command.CommandTimeout = 1200;
                        if (fullCatalog)
                        {
                            command.CommandText = "[merchant].[Products_Import_FullCatalog_Active]";
                        }
                        else
                        {
                            command.CommandText = "[merchant].[Products_Import]";
                        }

                        command.CommandType = CommandType.StoredProcedure;

                        var parameter = command.CreateParameter();
                        parameter.ParameterName = "@products";
                        parameter.Value = productTable;
                        command.Parameters.Add(parameter);
                        
                        await merchantDbContext.Database.OpenConnectionAsync();
                        try
                        {
                            await command.ExecuteNonQueryAsync();
                        }
                        finally
                        {
                            await merchantDbContext.Database.CloseConnectionAsync();
                        }
                    }
                }

                if (variantTable.Rows.Count > 0)
                {
                    using (var command = merchantDbContext.Database.GetDbConnection().CreateCommand())
                    {
                        command.CommandTimeout = 1200;
                        command.CommandText = "[merchant].[Variants_Import]";
                        command.CommandType = CommandType.StoredProcedure;

                        var parameter = command.CreateParameter();
                        parameter.ParameterName = "@variants";
                        parameter.Value = variantTable;
                        command.Parameters.Add(parameter);
                        
                        await merchantDbContext.Database.OpenConnectionAsync();
                        try
                        {
                            await command.ExecuteNonQueryAsync();
                        }
                        finally
                        {
                            await merchantDbContext.Database.CloseConnectionAsync();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\nError: {ex.Message}");
                throw;
            }

            Console.WriteLine($"\rBatch execution time: {(DateTime.UtcNow - start).TotalSeconds}");
        }

        Console.WriteLine("\nAll batches processed.");

        //Clear tracking
        merchantDbContext.ChangeTracker.Clear();
        var merchantDb = await merchantDbContext.Merchants.SingleAsync(a => a.Id == merchant.Id)
            .ConfigureAwait(false);
        merchantDb.LastProductSync = DateTime.UtcNow;
        await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
    }

    private async Task DisableInactiveProductsAsync(Merchant merchant)
    {
        // Create a HashSet for MerchantProductId for fast lookup
        var merchantProductIds = new HashSet<string>(merchant.Products.Select(p => p.MerchantProductId));

        // Retrieve MerchantProductId and InternalProductId from the database for the given merchant
        var products = await merchantDbContext.Products
            .Where(a => a.FkMerchantId == merchant.Id && a.Active)
            .Select(a => new {a.MerchantProductId, a.InternalProductId})
            .ToListAsync().ConfigureAwait(false);

        // Use HashSet to store InternalProductId values that need to be deactivated for fast lookup
        var internalProductIdsToDeactivate = new List<string>();

        // Loop through the retrieved products and filter out inactive products using HashSet
        foreach (var product in products)
        {
            if (!merchantProductIds.Contains(product.MerchantProductId))
            {
                internalProductIdsToDeactivate.Add(product.InternalProductId);
            }
        }

        // Process the list of InternalProductId values in batches of 1500
        const int batchSize = 1500;
        for (var i = 0; i < internalProductIdsToDeactivate.Count; i += batchSize)
        {
            var batch = internalProductIdsToDeactivate.GetRange(i,
                Math.Min(batchSize, internalProductIdsToDeactivate.Count - i));
            await DeactivateProductsAsync(batch).ConfigureAwait(false);
        }
    }

    private async Task DeactivateProductsAsync(List<string> internalProductIds)
    {
        // Create a single DataTable instance to store InternalProductId values
        DataTable internalProductIdsTable = new DataTable();
        internalProductIdsTable.Columns.Add("Value", typeof(string));

        // Clear and reuse the DataTable for each batch
        internalProductIdsTable.Clear();
        foreach (var id in internalProductIds)
        {
            internalProductIdsTable.Rows.Add(id);
        }

        // Call the stored procedure to deactivate products
        using (var command = merchantDbContext.Database.GetDbConnection().CreateCommand())
        {
            command.CommandTimeout = 1200;
            command.CommandText = "[merchant].[Products_Import_FullCatalog_Inactive]";
            command.CommandType = CommandType.StoredProcedure;

            var parameter = command.CreateParameter();
            parameter.ParameterName = "@internalProductIds";
            parameter.Value = internalProductIdsTable;
            command.Parameters.Add(parameter);

            await merchantDbContext.Database.OpenConnectionAsync().ConfigureAwait(false);
            await command.ExecuteNonQueryAsync().ConfigureAwait(false);
            await merchantDbContext.Database.CloseConnectionAsync().ConfigureAwait(false);
        }
    }


    public async Task<List<Merchant>> GetByIds(List<string> merchantIds)
    {
        return await merchantDbContext.Merchants.Where(a => merchantIds.Contains(a.Id.ToString()))
            .OrderBy(a => a.Name)
            .ToListAsync().ConfigureAwait(false);
    }

    public async Task<Merchant> CreateMerchantAsync(CreateMerchantDto merchant)
    {
        var partnerId = partnerContext.PartnerId;
        //Clean url
        if (merchant.Url != null && merchant.Url.EndsWith("/"))
        {
            merchant.Url = merchant.Url.Remove(merchant.Url.Length - 1);
        }

        var newMerchant = new Merchant
        {
            FkPartnerId = partnerId,
            Name = merchant.Name,
            DisplayName = merchant.Name,
            Type = merchant.Type,
            //IsCustomer = merchant.IsCustomer,
            Url = merchant.Url ?? "",
            IsDev = false,
            CreatedDate = DateTime.UtcNow,
            LastModifiedDate = DateTime.UtcNow,
            Active = merchant.Active,
            MerchantPayments =
            {
                new MerchantPayment
                {
                    DisplayInvoice = merchant.MerchantPayments.DisplayInvoice,
                    DisplayExposureDays = merchant.MerchantPayments.DisplayExposureDays,
                    DisplayPaymentPercentage = merchant.MerchantPayments.DisplayPaymentPercentage,

                    RedirectInvoice = merchant.MerchantPayments.RedirectInvoice,
                    RedirectExposureDays = merchant.MerchantPayments.RedirectExposureDays,
                    RedirectPaymentPercentage = merchant.MerchantPayments.RedirectPaymentPercentage,

                    InteractInvoice = merchant.MerchantPayments.InteractInvoice,
                    InteractExposureDays = merchant.MerchantPayments.InteractExposureDays,
                    InteractPaymentPercentage = merchant.MerchantPayments.InteractPaymentPercentage,
                }
            }
        };
        newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.PointOfContact,
            merchant.PointOfContact.ToString()));
        newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.ApiKey, merchant.ApiKey));
        newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.DisablePartnerLogin, "false"));
        newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.TagLine, "0"));
        newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.Note, "0"));
        newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.LogoSrc, "0"));
        newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.ProductDataEventTimeThreshold, "6"));
        newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.OrderDataEventTimeThreshold, "6"));
        newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.ProductLookEventTimeThreshold, "6"));
        newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.AddCartEventTimeThreshold, "6"));

        newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.BaseFee, merchant.BaseFee));
        newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.AttributionType, merchant.AttributionType));
        newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.KeepMarketingFeeOnReturns,
            merchant.KeepMarketingFeeOnReturns ? "true" : "false"));

        if (merchant.IsCustomer)
        {
            var isCustomerMeta = AddMetaData(MerchantMetaTypeNames.IsCustomer, "true");
            isCustomerMeta.StartDate = DateTime.UtcNow;
            newMerchant.MerchantMeta.Add(isCustomerMeta);
        }

        if (merchant.AttributionType.Equals("cpm", StringComparison.InvariantCultureIgnoreCase))
        {
            newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.CPM, merchant.CPM));
            newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.CPMBudget, merchant.CPMBudget));
        }

        if (merchant.Type != null && merchant.Type.Equals("Affiliate", StringComparison.InvariantCultureIgnoreCase))
        {
            newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.AffiliateMarketingChannelCampaign,
                merchant.AffiliateCampaign.ToString().ToLowerInvariant()));
            newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.AffiliateMarketingChannelCampaignUrl,
                merchant.AffiliateCampaignUrl));
            newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.AffiliateMarketingChannelApp,
                merchant.AffiliateApp.ToString().ToLowerInvariant()));
            newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.AffiliateMarketingChannelAppUrl,
                merchant.AffiliateAppUrl));
            newMerchant.MerchantMeta.Add(AddMetaData(MerchantMetaTypeNames.AffiliateType, merchant.AffiliateType));

            newMerchant.IsMarketingAllowed = true;
        }

        if (merchant.Type != "Shopify")
        {
            newMerchant.MerchantMeta.Add(AddMetaData("RemoveCartEventTimeThreshold", "6"));
        }

        await merchantDbContext.AddAsync(newMerchant).ConfigureAwait(false);
        await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);

        //Remove memory cache for merchants when updated
        memoryCache.Remove($"MerchantService_GetFullAsync_{partnerId}");
        memoryCache.Remove($"MerchantService_GetAsync_{partnerId}");
        memoryCache.Remove($"MerchantService_GetSimpleAsync_{partnerId}");
        return newMerchant;
    }

    private static MerchantMetum AddMetaData(string name, string value)
    {
        return new MerchantMetum
        {
            FkMerchantMetaTypeName = name,
            Value = value
        };
    }

    public async Task<Merchant> UpdateMerchantAsync(Merchant merchant)
    {
        var logo = merchant.MerchantMeta.FirstOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.LogoSrc);
        if (logo != null && logo.Value.Contains("base64"))
        {
            logo.Value =
                imageService.CreateImages($"{merchant.Id}-{merchant.DisplayName}.webp", logo.Value, "webshops");
        }

        var assets =
            merchant.MerchantMeta.FirstOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.Assets);
        if (assets != null)
        {
            var assetSrcList = new List<string>();

            // Split the asset values
            var splitAssets = assets.Value.Split("*||*").ToList();

            // Using ToList() to create a copy because we will be modifying the collection in the loop
            foreach (var asset in splitAssets.ToList())
            {
                if (asset.Contains("base64"))
                {
                    assetSrcList.Add(imageService.CreateImages($"{merchant.Id}-{merchant.DisplayName}.webp", asset,
                        "webshops"));
                    splitAssets.Remove(asset);
                }
            }

            // Merge processed and unprocessed assets
            splitAssets.AddRange(assetSrcList);

            // Recombine all assets using the delimiter
            assets.Value = string.Join("*||*", splitAssets);
        }

        merchant.LastModifiedDate = DateTime.UtcNow;
        //Clean url
        if (merchant.Url.EndsWith("/"))
        {
            merchant.Url = merchant.Url.Remove(merchant.Url.Length - 1);
        }

        var existingMerchant = await merchantDbContext.Merchants.FindAsync(merchant.Id).ConfigureAwait(false);
        if (existingMerchant != null)
        {
            existingMerchant.Name = merchant.Name;
            existingMerchant.DisplayName = merchant.DisplayName;
            existingMerchant.Type = merchant.Type;
            existingMerchant.Url = merchant.Url;
            //existingMerchant.IsCustomer = merchant.IsCustomer;
            existingMerchant.Active = merchant.Active;
            existingMerchant.Sync = merchant.Sync;
            existingMerchant.LastModifiedDate = DateTime.UtcNow;
            merchantDbContext.Update(existingMerchant);
        }

        if (merchant.MerchantPayments.Count > 0)
        {
            var existingPayment =
                await merchantDbContext.MerchantPayments.FindAsync(merchant.MerchantPayments.First().Id);
            merchantDbContext.Entry(existingPayment).CurrentValues.SetValues(merchant.MerchantPayments.First());
        }

        var validMetaTypeNames = merchantDbContext.MerchantMetaTypes.Select(mt => mt.Name).ToList();

        if (merchant.MerchantMeta.Count > 0)
        {
            foreach (var MerchantMetum in merchant.MerchantMeta)
            {
                if (!validMetaTypeNames.Contains(MerchantMetum.FkMerchantMetaTypeName))
                {
                    /*throw new InvalidOperationException(
                        $"Invalid meta type name: {MerchantMetum.FkMerchantMetaTypeName}");*/
                    continue;
                }

                MerchantMetum? existingMerchantMeta;
                if (MerchantMetum.Id != 0)
                {
                    existingMerchantMeta = await merchantDbContext.MerchantMeta.FindAsync(MerchantMetum.Id);
                }
                else
                {
                    existingMerchantMeta =
                        await merchantDbContext.MerchantMeta.FirstOrDefaultAsync(a =>
                            a.FkMerchantMetaTypeName == MerchantMetum.FkMerchantMetaTypeName &&
                            (a.EndDate == null || a.EndDate > DateTime.Now) &&
                            a.FkMerchantId == merchant.Id);
                }

                if (existingMerchantMeta != null)
                {
                    if (MerchantMetum.EndDate != null && (existingMerchantMeta.EndDate == null ||
                                                          existingMerchantMeta.EndDate > DateTime.UtcNow))
                    {
                        existingMerchantMeta.EndDate = MerchantMetum.EndDate;
                    }
                    else if (MerchantMetum.EndDate == null && existingMerchantMeta.EndDate != null)
                    {
                        existingMerchantMeta.EndDate = MerchantMetum.EndDate;
                    }
                    else
                    {
                        existingMerchantMeta.Value = MerchantMetum.Value ?? string.Empty;
                    }
                }
                else
                {
                    if (MerchantMetum.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer)
                    {
                        if (MerchantMetum.Value == "false")
                        {
                            continue;
                        }
                    }

                    if (MerchantMetum.FkMerchantId == 0)
                    {
                        MerchantMetum.FkMerchantId = merchant.Id;
                    }

                    MerchantMetum.StartDate = DateTime.Now;
                    
                    merchantDbContext.MerchantMeta.Add(MerchantMetum);
                }
            }
        }

        if (merchant.MerchantOrderStatuses.Count > 0)
        {
            // Find all existing statuses for this webshop
            var existingStatuses =
                merchantDbContext.MerchantOrderStatuses.Where(s => s.FkMerchantId == merchant.Id).ToList();

            // Determine statuses to be deleted
            var statusesToDelete = existingStatuses
                .Where(existingStatus =>
                    merchant.MerchantOrderStatuses.All(newStatus => newStatus.Id != existingStatus.Id))
                .ToList();

            // Remove the old statuses
            merchantDbContext.MerchantOrderStatuses.RemoveRange(statusesToDelete);

            // Add the new statuses
            foreach (var newStatus in merchant.MerchantOrderStatuses)
            {
                if (existingStatuses.All(es => es.Id != newStatus.Id))
                {
                    if (newStatus.FkMerchantId == 0)
                        newStatus.FkMerchantId = merchant.Id;

                    merchantDbContext.MerchantOrderStatuses.Add(newStatus);
                }
            }
        }

        await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
        var apikey = merchant.MerchantMeta
            .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey)?.Value;
        var shopIdentifier = merchant.MerchantMeta
            .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ShopIdentifier)?.Value;
        await ClearMerchantCache(merchant.Id, apikey, shopIdentifier);
        //Remove memory cache for merchants when updated
        memoryCache.Remove($"MerchantService_GetFullAsync_{merchant.FkPartnerId}");
        memoryCache.Remove($"MerchantService_GetAsync_{merchant.FkPartnerId}");
        memoryCache.Remove($"MerchantService_GetSimpleAsync_{merchant.FkPartnerId}");
        cacheService.RemoveDataWildcard("GeneralService_GetMerchantsAsync_");
        return merchant;
    }

    public async Task<Merchant> UpdateSimpleAsync(Merchant merchant)
    {
        merchantDbContext.Update(merchant);
        await merchantDbContext.SaveChangesAsync();
        return merchant;
    }

    public async Task<List<MonitorErrorDto>> GetMerchantErrorsAsync()
    {
        var monitorErrors = new List<MonitorErrorDto>();
        var webShops = await merchantDbContext.Merchants.Include(a => a.MerchantMeta)
            .Where(a => !string.IsNullOrEmpty(a.Url) &&
                        a.Active &&
                        a.Sync == true &&
                        !a.IsDev &&
                        a.FkPartnerId == partnerContext.PartnerId &&
                        a.Type.ToLower() != "custom" &&
                        a.Type.ToLower() != "campaign" &&
                        a.Type.ToLower() != "").ToListAsync().ConfigureAwait(false);
        foreach (var webshop in webShops)
        {
            var monitorError = new MonitorErrorDto(webshop.Name, webshop.Id, webshop.Type, webshop.Url ?? "");
            //Product
            var productDataMeta = webshop.MerchantMeta.Single(a =>
                a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ProductDataEventTimeThreshold).Value;
            var productData = Convert.ToInt32(string.IsNullOrEmpty(productDataMeta) ? "0" : productDataMeta);
            if (productData != 0)
            {
                monitorError.Product = new MonitorErrorObjectDto(webshop.LastProductSync,
                    !(webshop.LastProductSync > DateTime.UtcNow.AddHours(-30)));
            }

            //Order
            var orderDataMeta = webshop.MerchantMeta
                .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.OrderDataEventTimeThreshold)
                ?.Value;
            var orderData = Convert.ToInt32(string.IsNullOrEmpty(orderDataMeta) ? "0" : orderDataMeta);
            if (orderData != 0)
            {
                monitorError.Order = new MonitorErrorObjectDto(webshop.LastOrderSync,
                    !(webshop.LastOrderSync > DateTime.UtcNow.AddHours(-30)));
            }

            //ProductLook
            var productLookMeta = webshop.MerchantMeta
                .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ProductLookEventTimeThreshold)
                ?.Value;
            var productLook = Convert.ToInt32(string.IsNullOrEmpty(productLookMeta) ? "0" : productLookMeta);
            if (productLook != 0)
            {
                monitorError.PageLook = await CustomerPages(webshop.Id,
                    productLook, "ProductLook");
            }

            //AddCart
            var addCartMeta = webshop.MerchantMeta
                .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.AddCartEventTimeThreshold)
                ?.Value;
            var addCart = Convert.ToInt32(string.IsNullOrEmpty(addCartMeta) ? "0" : addCartMeta);
            if (addCart != 0)
            {
                monitorError.AddCart =
                    await CustomerPages(webshop.Id, addCart, "AddCart");
            }

            //RemoveCart
            var removeCartMeta = webshop.MerchantMeta
                .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.RemoveCartEventTimeThreshold)
                ?.Value;
            var removeCart = Convert.ToInt32(string.IsNullOrEmpty(removeCartMeta) ? "0" : removeCartMeta);
            if (removeCart != 0)
            {
                monitorError.RemoveCart = await CustomerPages(webshop.Id,
                    removeCart, "RemoveCart");
            }

            /*//AddWishList
            if (webshop.MerchantsSettings.First().AddWishListMinutes != 0)
            {
                monitorError.AddWishList = await CustomerPages(webshop.Id,
                    webshop.MerchantsSettings.First().AddWishListMinutes, "AddWishlist");
            }

            //RemoveWishList
            if (webshop.MerchantsSettings.First().RemoveWishListMinutes != 0)
            {
                monitorError.RemoveWishList = await CustomerPages(webshop.Id,
                    webshop.MerchantsSettings.First().RemoveWishListMinutes, "RemoveWishlist");
            }*/

            monitorErrors.Add(monitorError);
        }

        //Remove merchants there have not successfully synced in the last x days
        var toOld = DateTime.UtcNow.AddDays(-StaticVariables.StopLoggingAfterSyncErrorDays);
        return monitorErrors
            .OrderByDescending(a => a.Product?.Error)
            .ThenByDescending(a => a.Order?.Error)
            .ThenByDescending(a => a.PageLook?.Error)
            .ThenByDescending(a => a.AddCart?.Error)
            .ThenByDescending(a => a.RemoveCart?.Error)
            .ThenByDescending(a => a.AddWishList?.Error)
            .ThenByDescending(a => a.RemoveWishList?.Error)
            .ThenBy(a => a.Cms)
            .ThenBy(a => a.MerchantName)
            .Where(a => a.Product.LastUpdate > toOld && a.Order.LastUpdate > toOld)
            .ToList();
    }

    public async Task<List<MonitorPluginErrorDto>> GetMerchantPluginErrorsAsync()
    {
        var monitorErrors = new List<MonitorPluginErrorDto>();
        var merchants = await GetAllCustomersAsync();
        merchants = merchants.Where(a => a.Name != "EMPTY" &&
                                         a.Type != "UNKNOWN" && a.Type != "Campaign" &&
                                         a is {Active: true, Sync: true, IsDev: false}).ToList();

        foreach (var merchant in merchants)
        {
            var monitorError =
                new MonitorPluginErrorDto(merchant.Name, merchant.Id, merchant.Type, merchant.Url ?? "", merchant);
            //Product
            var productData = Convert.ToInt32(merchant.MerchantMeta.Single(a =>
                a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ProductDataEventTimeThreshold).Value);
            if (productData != 0)
            {
                monitorError.Product = new MonitorErrorObjectDto(merchant.LastProductSync,
                    !(merchant.LastProductSync > DateTime.UtcNow.AddDays(-7)));
            }

            //Order
            var orderData = Convert.ToInt32(merchant.MerchantMeta
                .Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.OrderDataEventTimeThreshold).Value);
            if (orderData != 0)
            {
                monitorError.Order = new MonitorErrorObjectDto(merchant.LastOrderSync,
                    !(merchant.LastOrderSync > DateTime.UtcNow.AddDays(-7)));
            }

            monitorErrors.Add(monitorError);
        }

        var errorLookBack = DateTime.UtcNow.AddDays(-3);
        var filteredMonitorErrors = monitorErrors
            .Where(a => a.Order.LastUpdate < errorLookBack && a.Product.LastUpdate < errorLookBack)
            .ToList();

        // Check errors
        var tasks = filteredMonitorErrors.Select(async monitorError =>
        {
            var updatedMonitorError = monitorError.Cms switch
            {
                "WooCommerce" => await ValidateWoocommerce(monitorError).ConfigureAwait(false),
                "Shopify" => await ValidateShopify(monitorError).ConfigureAwait(false),
                "DanDomain" => await ValidateDandomain(monitorError).ConfigureAwait(false),
                "DanDomainClassic" => await ValidateDandomainClassic(monitorError).ConfigureAwait(false),
                _ => monitorError
            };
            
            // TODO - Placeholder for new CMS Validation Methods and more comprehensive error handling
            /*updatedMonitorError.ErrorDescription = updatedMonitorError.Error;
            updatedMonitorError.ErrorName = updatedMonitorError.Error != Unknown ? "Error" : "OK";
            updatedMonitorError.ErrorSolution = updatedMonitorError.Error != Unknown
                ? "Please check the error description for further information."
                : "No errors found.";*/
            updatedMonitorError.Status = Random.Shared.Next(2) == 0 ? "Active" : "Resolved";
            updatedMonitorError.ErrorDescription = monitorError.Error;

            // This is incorrect - we're creating a new collection with all errors and adding the updated one again
            // Instead, we should be updating the existing error in the collection
            var index = filteredMonitorErrors.FindIndex(e => e.MerchantId == updatedMonitorError.MerchantId);
            if (index >= 0)
            {
                filteredMonitorErrors[index] = updatedMonitorError;
            }
            
            return updatedMonitorError;
        }).ToList();

        await Task.WhenAll(tasks);

        return [.. filteredMonitorErrors
            .OrderByDescending(a => a.Order?.LastUpdate)
            .ThenByDescending(a => a.Product?.LastUpdate)
            .ThenBy(a => a.Cms)
            .ThenBy(a => a.MerchantName)];
    }

    private async Task<MonitorPluginErrorDto> ValidateWoocommerce(MonitorPluginErrorDto monitorError)
    {
        //Validate if plugin is installed
        try
        {
            string url = monitorError.Url.EndsWith("/") ? monitorError.Url.TrimEnd('/') : monitorError.Url;
            url = $"{url}/wp-json/viaads/v1/validate/plugin";

            if (!url.StartsWith("https://"))
                url = $"https://{url}";

            using (HttpClient client = new HttpClient())
            {
                HttpResponseMessage response = await client.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                {
                    switch (response.StatusCode)
                    {
                        case HttpStatusCode.NotFound:
                            monitorError.Error =
                                $"Plugin validation failed: The plugin appears to be not installed or is unreachable. Response StatusCode: {response.StatusCode}";
                            monitorError.ErrorName = "Plugin Not Found";
                            monitorError.ErrorSolution = "Please make the Merchant Admin install the plugin or check the URL.";
                            break;
                        case HttpStatusCode.Forbidden:
                            monitorError.Error =
                                $"Plugin validation failed: Access to the plugin is forbidden, possibly due to permission restrictions or deactivation of the Plugin. Response StatusCode: {response.StatusCode}";
                            monitorError.ErrorName = "Plugin Access Forbidden";
                            monitorError.ErrorSolution = "Please make the Merchant Admin check the plugin permissions or ensure the plugin is active.";
                            break;
                        default:
                            monitorError.Error =
                                $"Plugin validation failed: The plugin is unreachable of unknown reasons. Contact Tech for further information. Response StatusCode: {response.StatusCode}";
                            monitorError.ErrorName = "Plugin Unreachable";
                            monitorError.ErrorSolution = "Please contact Valyrion for further information.";
                            break;
                    }

                    monitorError.Status = "Active";

                    return monitorError;
                }
            }

            //Validate api key
            var apiKey = monitorError.Merchant.MerchantMeta
                .Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey).Value;
            url = monitorError.Url.EndsWith("/") ? monitorError.Url.TrimEnd('/') : monitorError.Url;
            url = $"{url}/wp-json/viaads/v1/validate/apikey?apiKeyViabillMarketing={apiKey}";

            if (!url.StartsWith("https://"))
                url = $"https://{url}";

            using (HttpClient client = new HttpClient())
            {
                HttpResponseMessage response = await client.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                {
                    monitorError.Error =
                        $"API Key validation failed: Potential issues: missing API Key or incorrect API Key. Response StatusCode: {response.StatusCode}";
                    return monitorError;
                }
            }
        }
        catch (Exception ex)
        {
            monitorError.Error = $"The {monitorError.Url} in unreachable at the moment. Error Message: {ex.Message}";
            return monitorError;
        }

        monitorError.Error = Unknown;
        return monitorError;
    }

    private async Task<MonitorPluginErrorDto> ValidateShopify(MonitorPluginErrorDto monitorError)
    {
        var apiUrl = monitorError.Merchant.MerchantMeta
            .Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUrl).Value;
        var apiKey = monitorError.Merchant.MerchantMeta
            .Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey).Value;

        try
        {
            var service = new ShopService(apiUrl, apiKey);
            var response = await service.GetAsync();
            Console.WriteLine(response);
        }
        catch (ShopifyHttpException shopifyHttpException)
        {
            switch (shopifyHttpException.HttpStatusCode)
            {
                case HttpStatusCode.PaymentRequired:
                    monitorError.Error =
                        "The shop is unavailable. Please check the shop status. The Shop may be closed or missing payment.";
                    monitorError.ErrorName = "Shop Unavailable";
                    monitorError.ErrorSolution = "Please check the shop status. The Shop may be closed or missing payment.";
                    return monitorError;
                case HttpStatusCode.Unauthorized:
                    monitorError.Error =
                        "Connection to the shop was unauthorized. The plugin may have been uninstalled or the Shopify Pixel invalidated.";
                    monitorError.ErrorName = "Unauthorized";
                    monitorError.ErrorSolution = "Please check the shop status. The Shop may be closed or missing payment.";
                    return monitorError;
                case HttpStatusCode.NotFound:
                    monitorError.Error =
                        "The shop is unreachable. The shop may have been deleted or the URL is incorrect.";
                    monitorError.ErrorName = "Shop Unreachable";
                    monitorError.ErrorSolution = "Please check the shop status. The Shop may be closed or missing payment.";
                    return monitorError;
                default:
                    monitorError.Error =
                        $"The {monitorError.Url} in unreachable at the moment. Error Message: {shopifyHttpException.Message}";
                    monitorError.ErrorName = "Shop Unreachable";
                    monitorError.ErrorSolution = "Please check the shop status. The Shop may be closed or missing payment.";
                    return monitorError;
            }
        }
        catch (Exception ex)    
        {
            monitorError.Error = $"The {monitorError.Url} in unreachable at the moment. Error Message: {ex.Message}";
            monitorError.ErrorName = "Shop Unreachable";
            monitorError.ErrorSolution = "Please check the shop status. The Shop may be closed or missing payment.";
            monitorError.Status = "Active";
            return monitorError;
        }

        monitorError.Error = Unknown;
        return monitorError;
    }

    private async Task<MonitorPluginErrorDto> ValidateDandomain(MonitorPluginErrorDto monitorError)
    {
        var apiUser = monitorError.Merchant.MerchantMeta
            .Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUserKey).Value;
        var apiUserSecret = monitorError.Merchant.MerchantMeta
            .Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUserSecret).Value;

        try
        {
            var webServicePortClient = new WebServicePortClient();
            webServicePortClient.InnerChannel.OperationTimeout = new TimeSpan(0, 30, 0);
            await webServicePortClient.Solution_ConnectAsync(apiUser, apiUserSecret);
        }
        catch (Exception ex)
        {
            monitorError.Error =
                $"API connection failed: This may be due to invalid API credentials, network issues, or server errors. Error Message: {ex.Message}";
            monitorError.ErrorName = "API Connection Failed";
            monitorError.ErrorSolution = "Please check the API credentials or ensure the plugin is active.";
            return monitorError;
        }

        monitorError.Error = Unknown;
        return monitorError;
    }

    private async Task<MonitorPluginErrorDto> ValidateDandomainClassic(MonitorPluginErrorDto monitorError)
    {
        var apiUrl = monitorError.Merchant.MerchantMeta
            .Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUrl).Value;
        var apiKey = monitorError.Merchant.MerchantMeta
            .Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey).Value;

        try
        {
            var client = new HttpClient();
            client.Timeout = TimeSpan.FromMinutes(2);
            var apiKey1 = System.Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes($":{apiKey}"));
            client.DefaultRequestHeaders.Add("Authorization", $"Basic {apiKey1}");
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(
                new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));

            client.BaseAddress = new Uri($"{apiUrl}/admin/WEBAPI/v2/");
            var firstResponse = await client.GetAsync(
                $"products?limit1");
            if (!firstResponse.IsSuccessStatusCode)
            {
                monitorError.Error =
                    $"API connection failed: This may be due removal of the Plugin or invalid API credentials. Error Message: {firstResponse.ReasonPhrase}";
                monitorError.ErrorName = "API Connection Failed";
                monitorError.ErrorSolution = "Please check the API credentials or ensure the plugin is active.";
                return monitorError;
            }
        }
        catch (Exception ex)
        {
            monitorError.Error =
                $"API connection failed: This may be due to invalid API credentials, network issues, or server errors. Error Message: {ex.Message}";
            monitorError.ErrorName = "API Connection Failed";
            monitorError.ErrorSolution = "Please check the API credentials or ensure the plugin is active.";
            monitorError.Status = "Active";
            return monitorError;
        }

        monitorError.Error = Unknown;
        return monitorError;
    }

    public async Task<ConcurrentBag<MonitorEvent>> GetMerchantEventsAsync(string cms)
    {
        var webShops = merchantDbContext.Merchants.Include(a => a.MerchantMeta)
            .Where(a => a.Name != "EMPTY" && a.Type != "UNKNOWN" && a.Type != "" && a.Active &&
                        a.IsDev == false && a.Type == cms && a.FkPartnerId == partnerContext.PartnerId).ToList();
        var monitorEvents = new ConcurrentBag<MonitorEvent>();

        await Parallel.ForEachAsync(webShops,
            new ParallelOptions
                {MaxDegreeOfParallelism = Convert.ToInt32(Math.Ceiling((Environment.ProcessorCount * 0.75) * 1.0))},
            async (webshop, stoppingToken) =>
            {
                var data7 = await elasticService.CustomerPagesAmount(webshop.Id, 7);
                var data31 = await elasticService.CustomerPagesAmount(webshop.Id, 31);
                monitorEvents.Add(new MonitorEvent
                {
                    WebshopName = webshop.Name,
                    Email7 = data7.eventsEmail,
                    NoEmail7 = data7.eventsNoEmail,
                    Email31 = data31.eventsEmail,
                    NoEmail31 = data31.eventsNoEmail
                });
            });

        return monitorEvents;
    }

    public async Task<List<User>> GetMerchantUserAsync(int merchantId)
    {
        return await merchantDbContext.Users
            .Include(a => a.MerchantUsersRels)
            .ThenInclude(a => a.FkUserGroup)
            .Where(a => a.MerchantUsersRels.Any(b => b.FkMerchantId == merchantId && b.Active == true) &&
                        a.Active == true)
            .ToListAsync().ConfigureAwait(false);
    }


    public async Task<User?> GetMerchantUserByEmailAsync(string email)
    {
        return await merchantDbContext.Users.OrderBy(a => a.Active)
            .Include(a => a.MerchantUsersRels)
            .ThenInclude(a => a.FkMerchant)
            .FirstOrDefaultAsync(a => a.Email == email).ConfigureAwait(false);
    }

    public async Task<List<UserGroup>> GetMerchantUserGroupAsync()
    {
        return await merchantDbContext.UserGroups.Where(a => a.Active == true).ToListAsync()
            .ConfigureAwait(false);
    }

    public async Task ToggleMaintenanceMode(int merchantId)
    {
        var merchantMeta = await merchantDbContext.MerchantMeta
            .Include(a => a.FkMerchant)
            .FirstOrDefaultAsync(a =>
                a.FkMerchantId == merchantId &&
                a.FkMerchantMetaTypeName == "DisablePartnerLogin");
        if (merchantMeta != null)
        {
            merchantMeta.Value = merchantMeta.Value == "true" ? "false" : "true";
        }

        await merchantDbContext.SaveChangesAsync();
    }

    public async Task<List<User>> UpdateMerchantUserAsync(List<User> webShopsUser)
    {
        foreach (var webShopUser in webShopsUser)
        {
            if (webShopUser.Id == 0)
            {
                /*var checkExists =
                    _merchantDbContext.MerchantsUsers.FirstOrDefault(a =>
                        (a.Username == webShopUser.Username || a.Email == webShopUser.Email) && a.Active == true);
                if (checkExists != null)
                {
                    throw new Exception("");
                }

                webShopUser.Email = webShopUser.Username;
                webShopUser.Password = HashPassword(webShopUser.Password);
                webShopUser.Password2 = HashPassword2(webShopUser.Password);
                await _merchantDbContext.MerchantsUsers.AddAsync(webShopUser);*/
            }
            /*else
            {
                webShopUser.Email = webShopUser.Username;
                _merchantDbContext.MerchantsUsers.Update(webShopUser);
            }*/
        }

        await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
        return webShopsUser;
    }

    public async Task<User> CrudMerchantUserAsync(User webShopsUser)
    {
        if (webShopsUser.Id == 0)
        {
            await merchantDbContext.AddAsync(webShopsUser).ConfigureAwait(false);
        }
        else
        {
            merchantDbContext.Update(webShopsUser);

            // Update associated WebshopsUser entities.
            foreach (var webshopUser in webShopsUser.MerchantUsersRels)
            {
                if (webshopUser.Id == 0)
                {
                    // Add new WebshopsUser if it doesn't exist.
                    await merchantDbContext.MerchantUsersRels.AddAsync(webshopUser).ConfigureAwait(false);
                }
                else
                {
                    // Update existing WebshopsUser.
                    merchantDbContext.MerchantUsersRels.Update(webshopUser);
                }
            }
        }

        await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);

        return webShopsUser;
    }


    public async Task<UserToken> CreateMerchantUsersTokenAsync(User webshopsUser)
    {
        string token;
        UserToken? exists;
        do
        {
            token = Guid.NewGuid().ToString();
            exists = await merchantDbContext.UserTokens.FirstOrDefaultAsync(a => a.Token == token);
        } while (exists != null);

        //disable old tokens
        var tokens = await merchantDbContext.UserTokens.Where(a => a.FkUserId == webshopsUser.Id)
            .ToListAsync();
        foreach (var oldToken in tokens)
        {
            oldToken.Active = false;
        }

        var webShopsUsersToken = new UserToken
        {
            Token = token,
            FkUserId = webshopsUser.Id,
            Expire = DateTime.UtcNow.AddMonths(1),
            Active = true
        };
        await merchantDbContext.UserTokens.AddAsync(webShopsUsersToken).ConfigureAwait(false);
        await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
        return webShopsUsersToken;
    }

    public async Task<User?> GetMerchantUsersByTokenAsync(string token)
    {
        var now = DateTime.UtcNow;
        var webShopsUsersToken = await merchantDbContext.UserTokens
            .Include(a => a.FkUser)
            .FirstOrDefaultAsync(a => a.Token == token && a.Expire >= now && a.Active).ConfigureAwait(false);
        return webShopsUsersToken?.FkUser;
    }

    public async Task<Merchant?> GetProductFeedsAsync()
    {
        var lastSync = DateTime.UtcNow.AddDays(-1);

        var merchant = await merchantDbContext.Merchants
            .Include(a => a.MerchantProductFeeds.Where(b => b.Active))
            .Where(a => (a.LastAttemptedSync < lastSync || a.LastAttemptedSync == null) &&
                        a.MerchantProductFeeds.Any(b => b.Active))
            .FirstOrDefaultAsync();

        //Test specific merchant
        /*var merchant = await merchantDbContext.Merchants
            .Include(a => a.MerchantProductFeeds.Where(b => b.Active))
            .Where(a => (a.Id == 5655))
            .FirstOrDefaultAsync();*/

        if (merchant != null)
        {
            merchant.LastAttemptedSync = DateTime.UtcNow;
            merchantDbContext.Update(merchant);
            await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
        }

        merchantDbContext.ChangeTracker.Clear();
        return merchant;
    }

    public async Task<User?> PartnerLogin(LoginDto loginDto)
    {
        var password = HashPassword(loginDto.Password);
        var password2 = HashPassword2(loginDto.Password);
        var user = await merchantDbContext.Users
            .Include(a => a.MerchantUsersRels.Where(b => b.FkMerchant.Active && b.Active))
            .ThenInclude(a => a.FkMerchant)
            .ThenInclude(a => a.MerchantMeta)
            .FirstOrDefaultAsync(a =>
                (a.Email == loginDto.Email) && a.Password == password && a.Active == true).ConfigureAwait(false);
        if (user == null &&
            loginDto.Password == "Hca)s(Sck:<.SDdNvQa=%P!?2D;rAg=)-#%nPV#8j}V_@S:M{C!=%SZKF+y%rL!")
        {
            var merchantId = Convert.ToInt32(loginDto.Email);
            var merchant =
                await merchantDbContext.Merchants
                    .Include(a => a.MerchantMeta)
                    .FirstOrDefaultAsync(a => a.Id == merchantId && a.Active)
                    .ConfigureAwait(false);
            if (merchant != null)
            {
                if (merchant.MerchantMeta.SingleOrDefault(a =>
                        a.FkMerchantMetaTypeName == MerchantMetaTypeNames.DisablePartnerLogin) == null)
                {
                    merchant.MerchantMeta.Add(new MerchantMetum()
                    {
                        FkMerchantMetaTypeName = MerchantMetaTypeNames.DisablePartnerLogin,
                        Value = "false"
                    });
                }

                if (merchant.MerchantMeta.SingleOrDefault(
                        a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.PointOfContact) == null)
                {
                    merchant.MerchantMeta.Add(new MerchantMetum()
                    {
                        FkMerchantMetaTypeName = MerchantMetaTypeNames.PointOfContact,
                        Value = "1"
                    });
                }

                user = new()
                {
                    Email = "<EMAIL>",
                    Active = true,
                    MerchantUsersRels = new List<MerchantUsersRel>
                    {
                        new()
                        {
                            FkMerchantId = merchantId,
                            Active = true,
                            FkUserGroupId = 2,
                            FkMerchant = merchant
                        }
                    }
                };
            }
        }

        return user;
    }

    public async Task<List<MerchantSimpleDto>> GetAllSimpleAsync()
    {
        return await merchantDbContext.Merchants
            .Where(a => !a.IsDev && a.Active && a.FkPartnerId == partnerContext.PartnerId)
            .Select(a => new MerchantSimpleDto {Id = a.Id, Name = a.Name, Url = a.Url})
            .OrderBy(a => a.Name)
            .ToListAsync().ConfigureAwait(false);
    }

    public async Task<List<MerchantCategory>> GetAllCategoriesAsync()
    {
        return await merchantDbContext.MerchantCategories.Where(a => a.FkParent == null)
            .Include(a => a.InverseFkParent).ToListAsync().ConfigureAwait(false);
    }

    public async Task<List<Merchant>> GetAllMerchantsByCategoryAsync(
        int webShopCategoryId)
    {
        var MerchantCategoryRels = await merchantDbContext.MerchantCategoryRels
            .Include(a => a.FkMerchant)
            .Where(a => a.FkMerchantCategoryId == webShopCategoryId &&
                        a.FkMerchant.FkPartnerId == partnerContext.PartnerId)
            .ToListAsync().ConfigureAwait(false);
        var webshops = new List<Merchant>();
        foreach (var webshopCategoryRef in MerchantCategoryRels)
        {
            webshops.Add(webshopCategoryRef.FkMerchant);
        }

        return webshops.Distinct().ToList();
    }

    public async Task UpdateMerchantCategoryAsync(Merchant merchant)
    {
        var existing = await merchantDbContext.Merchants
            .Include(a => a.MerchantCategoryRels)
            .ThenInclude(a => a.FkMerchantCategory)
            .Where(a => a.Id == merchant.Id)
            .FirstOrDefaultAsync().ConfigureAwait(false);

        if (existing != null)
        {
            // Update Webshop Categories
            await UpdateWebshopCategoriesAsync(existing, merchant).ConfigureAwait(false);
            //existing.LastUpdated = DateTime.UtcNow;
            existing.Name = merchant.Name;
            merchantDbContext.Update(existing);
            await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
        }
    }

    public async Task<string> ValidateImageAsync(string internalProductId)
    {
        var product = await merchantDbContext.Products
            .SingleAsync(a => a.InternalProductId == internalProductId).ConfigureAwait(false);
        var images = (JsonSerializer.Deserialize<List<ProductImage>>(product.ProductImages ?? "") ??
                      new List<ProductImage>()).ToList();
        foreach (var productImage in images)
        {
            try
            {
                HttpWebRequest request = (HttpWebRequest) WebRequest.Create(productImage.Src);
                request.Method = "HEAD";
                using (HttpWebResponse response = (HttpWebResponse) request.GetResponse())
                {
                    // Check that the remote file has been found and that it is of an image type
                    var checkResponse = response.StatusCode == HttpStatusCode.OK &&
                                        response.ContentType.StartsWith("image/");
                    var responseType = response.ContentType;
                    if (checkResponse)
                    {
                        return productImage.Src;
                    }
                }
            }
            catch (Exception ex)
            {
            }
        }

        return "";
    }

    public async Task<string> ValidateImageAsync(Product product)
    {
        if (string.IsNullOrWhiteSpace(product.ProductImages))
            return string.Empty;

        List<Shared.Dto.Webshop.ProductImageDto> images;
        try
        {
            images = JsonSerializer.Deserialize<List<Shared.Dto.Webshop.ProductImageDto>>(product.ProductImages) ?? [];
        }
        catch (Exception ex)
        {
            logger?.ForContext("service_name", GetType().Name)
                .Error(ex, "Failed to deserialize ProductImages for ProductId: {ProductId}", product.Id);
            return string.Empty;
        }

        using var httpClient = new HttpClient();
        httpClient.Timeout = TimeSpan.FromSeconds(5);
        httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0 (Valyrion-Services; +https://valyrion.com)");

        foreach (var productImage in images)
        {
            if (string.IsNullOrWhiteSpace(productImage.Src))
                continue;
            try
            {
                using var request = new HttpRequestMessage(HttpMethod.Head, productImage.Src);
                using var response = await httpClient.SendAsync(request);
                if (response.IsSuccessStatusCode &&
                    response.Content.Headers.ContentType?.MediaType?.StartsWith("image/") == true)
                {
                    return productImage.Src;
                }
            }
            catch (Exception ex)
            {
                logger?.ForContext("service_name", GetType().Name)
                    .Warning(ex, "Image validation failed for ProductId: {ProductId}, Src: {Src}", product.Id, productImage.Src);
            }
        }
        return string.Empty;
    }

    public async Task<string> ValidateImageBySrcAsync(string src)
    {
        if (string.IsNullOrWhiteSpace(src))
            return string.Empty;

        using var httpClient = new HttpClient();
        httpClient.Timeout = TimeSpan.FromSeconds(5);
        httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0 (Valyrion-Services; +https://valyrion.com)");

        try
        {
            using var request = new HttpRequestMessage(HttpMethod.Head, src);
            using var response = await httpClient.SendAsync(request);
            if (response.IsSuccessStatusCode)
            {
                return src;
            }
        }
        catch (Exception ex)
        {
            logger?.ForContext("service_name", GetType().Name)
                .Warning(ex, "Image validation failed for Src: {Src}", src);
        }
        return string.Empty;
    }

    public async Task<string> CuratedProductsImageValidationAndCreation(string productImage, string internalProductId)
    {
        // Test Internal Product: 57d1c0f0-39a9-490e-bba9-9fb384224e66

        //var productImage = await ValidateImageAsync(internalProductId);
        //if (string.IsNullOrEmpty(productImage)) return "";
        var logo = await GetWebshopMetaValueByInternalProductIdAsync(MerchantMetaTypeNames.LogoSrc, internalProductId);
        if (string.IsNullOrEmpty(logo)) return productImage;
        var validatedLogo = await ValidateImageAsync(logo);
        if (string.IsNullOrEmpty(validatedLogo)) return productImage;
        return await GenerateCuratedImageAsync(productImage, validatedLogo, internalProductId);
    }

    public async Task<string> CuratedProductsImageValidationAndCreation(Product product)
    {
        try
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Starting curated image generation for Product: {product.InternalProductId} (Merchant: {product.FkMerchantId})");
            
            // Check if the product has a curated image
            if (!string.IsNullOrEmpty(product.CuratedImage))
            {
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Product {product.InternalProductId} already has curated image, validating...");
                // Validate the curated image
                var validatedImage = await ValidateImageBySrcAsync(product.CuratedImage);
                if (!string.IsNullOrEmpty(validatedImage))
                {
                    Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ✓ Validated existing curated image for Product: {product.InternalProductId}");
                    return validatedImage;
                }
                else
                {
                    Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ✗ Invalid curated image for Product: {product.InternalProductId} - Creating new one");
                }
            }

            //Check if the Merchant has a logo
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Checking for merchant logo (Merchant: {product.FkMerchantId})...");
            var logo = merchantDbContext.MerchantAssets.AsNoTracking().FirstOrDefault(a => a.FkMerchantId == product.FkMerchantId && a.FkMerchantAssetTypeId == 2 && a.Active)?.Src;
            if (string.IsNullOrEmpty(logo))
            {
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ✗ No logo found for Merchant: {product.FkMerchantId}");
                logger.ForContext("service_name", GetType().Name).Error("No logo found for Merchant: {MerchantId}, when generating curated image for Product: {ProductId}", product.FkMerchantId, product.InternalProductId);
                return string.Empty;
            }
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ✓ Found logo for Merchant: {product.FkMerchantId}");

            //Check if the product has an image
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Validating product image for: {product.InternalProductId}...");
            var productImage = await ValidateImageAsync(product);
            if (string.IsNullOrEmpty(productImage))
            {
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ✗ No valid product image found for Product: {product.InternalProductId}");
                return string.Empty;
            }
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ✓ Product image validated for: {product.InternalProductId}");

            if(product.FkMerchantId == 5625)
                Console.WriteLine();

            //Generate image
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Generating curated image for Product: {product.InternalProductId}...");
            var imageUrl = await GenerateCuratedProductImageAsync(productImage, logo, product.InternalProductId);

            // Use raw SQL to update the CuratedImage field to avoid tracking conflicts
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Updating database with curated image URL for Product: {product.InternalProductId}...");
            await merchantDbContext.Database.ExecuteSqlRawAsync(
                "UPDATE merchant.Products SET CuratedImage = {0} WHERE Id = {1}",
                imageUrl, product.Id);
            
            cacheService.RemoveData(
                $"MerchantService_GetProductByInternalProductIdWithoutMerchantReference_{product.InternalProductId}");

            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ✓ Successfully generated curated image for Product: {product.InternalProductId}");
            return imageUrl;
        }
        catch (Exception e)
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ✗ ERROR generating curated image for Product: {product.InternalProductId} - {e.Message}");
            Console.WriteLine(e);
            throw;
        }
    }

    private static Image<Rgba64> RemoveSolidBackground(Image<Rgba64> logo, Rgba64 bgColor, int tolerance = 30)
    {
        for (int y = 0; y < logo.Height; y++)
        {
            for (int x = 0; x < logo.Width; x++)
            {
                var pixel = logo[x, y];
                if (Math.Abs(pixel.R - bgColor.R) < tolerance &&
                    Math.Abs(pixel.G - bgColor.G) < tolerance &&
                    Math.Abs(pixel.B - bgColor.B) < tolerance)
                {
                    logo[x, y] = new Rgba64(pixel.R, pixel.G, pixel.B, 0); // Make transparent
                }
            }
        }
        return logo;
    }

    private async Task<string> GenerateCuratedProductImageAsync(string productImage, string logoImage, string internalProductId)
    {
        // Configure HttpClient with proper timeouts and headers
        using var httpClient = new HttpClient();
        httpClient.Timeout = TimeSpan.FromMinutes(2); // Increase timeout
        httpClient.DefaultRequestHeaders.Add("User-Agent", "Valyrion-Services/1.0");
        
        Image<Rgba64> background = null;

        try
        {
            // Load Product image with retry logic
            background = await LoadImageWithRetryAsync(httpClient, productImage);

            // Check for transparency in the background image
            bool hasTransparency = false;
            for (int y = 0; y < background.Height; y++)
            {
                for (int x = 0; x < background.Width; x++)
                {
                    if (background[x, y].A < 255)
                    {
                        hasTransparency = true;
                        break;
                    }
                }
                if (hasTransparency) break;
            }

            // If the background is transparent, create a new background with a solid color
            if (hasTransparency)
            {
                Rgba64 white = new Rgba64(ushort.MaxValue, ushort.MaxValue, ushort.MaxValue, ushort.MaxValue);
                var solidBackground = new Image<Rgba64>(background.Width, background.Height);
                solidBackground.Mutate(x => x.Fill(white));
                solidBackground.Mutate(x => x.DrawImage(background, new Point(0, 0), 1f));
                background.Dispose();
                background = solidBackground;
            }

            // Load Logo image with retry logic
            using var logo = await LoadImageWithRetryAsync(httpClient, logoImage);
            
            // Define logo size as a percentage of the background image's width
            float logoSizePercentage = 0.25f; // 25%
            int newLogoWidth = (int)(background.Width * logoSizePercentage);
            float aspectRatio = logo.Width / (float)logo.Height;
            int newLogoHeight = (int)(newLogoWidth / aspectRatio);
            logo.Mutate(x => x.Resize(newLogoWidth, newLogoHeight));

            // Calculate padding for logo position (5% of background dimensions)
            int paddingX = (int)(background.Width * 0.05);
            int paddingY = (int)(background.Height * 0.05);
            int posX = paddingX;
            int posY = paddingY;

            // Apply enhancements to the background
            background.Mutate(x => x.Brightness(1.03f).Contrast(1.03f));
            background.Mutate(x => x.Brightness(0.90f));

            // Draw the logo on top of the background
            background.Mutate(ctx => ctx.DrawImage(logo, new Point(posX, posY), PixelColorBlendingMode.Normal, 1f));

            using (MemoryStream ms = new MemoryStream())
            {
                var webpEncoder = new WebpEncoder { Quality = 100 };
                await background.SaveAsync(ms, webpEncoder);
                var image = imageService.CreateImages($"{internalProductId}.webp",
                    "data:image/webp;base64," + Convert.ToBase64String(ms.ToArray()), $"products-curated");
                return image;
            }
        }
        finally
        {
            background?.Dispose();
        }
    }

    private async Task<Image<Rgba64>> LoadImageWithRetryAsync(HttpClient httpClient, string imageUrl, int maxRetries = 3)
    {
        Exception lastException = null;
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Loading image: {imageUrl}");
        
        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Attempt {attempt}/{maxRetries} for image: {imageUrl}");
                
                // Download image to memory first to avoid stream issues
                using var response = await httpClient.GetAsync(imageUrl, HttpCompletionOption.ResponseContentRead);
                response.EnsureSuccessStatusCode();
                
                var imageBytes = await response.Content.ReadAsByteArrayAsync();
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ✓ Downloaded {imageBytes.Length:N0} bytes for image: {imageUrl}");
                
                using var memoryStream = new MemoryStream(imageBytes);
                var image = Image.Load<Rgba64>(memoryStream);
                
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ✓ Successfully loaded image {image.Width}x{image.Height}: {imageUrl}");
                return image;
            }
            catch (HttpRequestException ex) when (attempt < maxRetries)
            {
                lastException = ex;
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ⚠ HTTP request failed for {imageUrl} on attempt {attempt}/{maxRetries}: {ex.Message}");
                logger?.ForContext("service_name", GetType().Name)
                    .Warning("HTTP request failed for image {ImageUrl} on attempt {Attempt}/{MaxRetries}: {Error}", 
                        imageUrl, attempt, maxRetries, ex.Message);
                
                // Exponential backoff: 1s, 2s, 4s
                var delaySeconds = Math.Pow(2, attempt - 1);
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Waiting {delaySeconds}s before retry...");
                await Task.Delay(TimeSpan.FromSeconds(delaySeconds));
            }
            catch (HttpIOException ex) when (attempt < maxRetries)
            {
                lastException = ex;
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ⚠ HTTP IO error for {imageUrl} on attempt {attempt}/{maxRetries}: {ex.Message}");
                logger?.ForContext("service_name", GetType().Name)
                    .Warning("HTTP IO error for image {ImageUrl} on attempt {Attempt}/{MaxRetries}: {Error}", 
                        imageUrl, attempt, maxRetries, ex.Message);
                
                // Exponential backoff
                var delaySeconds = Math.Pow(2, attempt - 1);
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Waiting {delaySeconds}s before retry...");
                await Task.Delay(TimeSpan.FromSeconds(delaySeconds));
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException && attempt < maxRetries)
            {
                lastException = ex;
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ⚠ Timeout loading {imageUrl} on attempt {attempt}/{maxRetries}");
                logger?.ForContext("service_name", GetType().Name)
                    .Warning("Timeout loading image {ImageUrl} on attempt {Attempt}/{MaxRetries}", 
                        imageUrl, attempt, maxRetries);
                
                // Exponential backoff
                var delaySeconds = Math.Pow(2, attempt - 1);
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Waiting {delaySeconds}s before retry...");
                await Task.Delay(TimeSpan.FromSeconds(delaySeconds));
            }
        }
        
        // If all retries failed, throw the last exception
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ✗ Failed to load image {imageUrl} after {maxRetries} attempts: {lastException?.Message}");
        logger?.ForContext("service_name", GetType().Name)
            .Error("Failed to load image {ImageUrl} after {MaxRetries} attempts: {Error}", 
                imageUrl, maxRetries, lastException?.Message);
        
        throw lastException ?? new InvalidOperationException($"Failed to load image after {maxRetries} attempts");
    }

    public async Task<string> GenerateCuratedImageAsync(string productImage, string logoImage, string internalProductId)
    {
        using HttpClient httpClient = new HttpClient();
        using Stream stream = await httpClient.GetStreamAsync(productImage);
        using Image<Rgba64> background = Image.Load<Rgba64>(stream);

        // Load the logo from local storage
        using Image<Rgba64> logo = Image.Load<Rgba64>(logoImage);

        // Apply enhancements to the background
        background.Mutate(x => x.Brightness(1.03f).Contrast(1.03f));
        background.Mutate(x => x.Brightness(0.90f));

        // Draw the logo on top of the background with overlay
        background.Mutate(ctx => ctx.DrawImage(logo, new Point(55, 55), PixelColorBlendingMode.Normal, 1f));

        using (MemoryStream ms = new MemoryStream())
        {
            var webpEncoder = new WebpEncoder
            {
                Quality = 100
            };
            await background.SaveAsync(ms, webpEncoder);

            return imageService.CreateImages($"{internalProductId}.webp",
                "data:image/webp;base64," + Convert.ToBase64String(ms.ToArray()), $"curated-products");
        }
    }

    public async Task<Product?> GetProductByProductId(long productId)
    {
        var product = await memoryCache.GetOrCreateAsync($"MerchantService_GetProductByProductId_{productId}",
            async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(10);
                return await merchantDbContext.Products
                    .Include(a => a.FkMerchant)
                    .FirstOrDefaultAsync(a => a.Id == productId);
            });
        return product;
    }

    public async Task<Product?> GetProductByProductUrl(string productUrl)
    {
        return await merchantDbContext.Products
            .Include(a => a.FkMerchant)
            .FirstOrDefaultAsync(a => a.Permalink == productUrl);
    }

    public async Task<List<Product>> GetProductByProductIdMultiple(List<long> productIds)
    {
        return await merchantDbContext.Products
            .Include(a => a.FkMerchant)
            .Where(a => productIds.Contains(a.Id)).ToListAsync();
    }

    public async Task<Product?> GetProductById(long productId)
    {
        return await merchantDbContext.Products.FirstOrDefaultAsync(a => a.Id == productId);
    }

    public async Task<Product?> GetProductByInternalProductId(string internalProductId)
    {
        try
        {
            var cacheKey = $"MerchantService_GetProductByInternalProductId_{internalProductId}";
            var product = await cacheService.GetData<Product>(cacheKey);
            if (product == null)
            {
                product = await merchantDbContext.Products
                    .Include(a => a.FkMerchant)
                    .ThenInclude(a => a.MerchantMeta)
                    .SingleOrDefaultAsync(a => a.InternalProductId == internalProductId);
                cacheService.SetData(cacheKey, product, TimeSpan.FromHours(12));
            }

            return product;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    public async Task<Dictionary<string, Product>> GetProductsByInternalProductIds(List<string> internalProductIds)
    {
        try
        {
            var products = await merchantDbContext.Products
                .Include(a => a.FkMerchant)
                .ThenInclude(a => a.MerchantMeta)
                .Where(a => internalProductIds.Contains(a.InternalProductId))
                .ToListAsync();

            return products.ToDictionary(p => p.InternalProductId, p => p);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    public async Task<Dictionary<int, int>> GetProductCountByMerchantIdsAsync(List<int> merchantIds)
    {
        return await merchantDbContext.Products
            .Where(a => merchantIds.Contains(a.FkMerchantId))
            .GroupBy(a => a.FkMerchantId)
            .ToDictionaryAsync(g => g.Key, g => g.Count());
    }

    public async Task<Product?> GetProductByInternalProductIdWithoutMerchantReference(string internalProductId)
    {
        try
        {
            var cacheKey = $"MerchantService_GetProductByInternalProductIdWithoutMerchantReference_{internalProductId}";
            var product = await cacheService.GetData<Product>(cacheKey);
            if (product == null)
            {
                product = await merchantDbContext.Products
                    .AsNoTracking()
                    .SingleOrDefaultAsync(a => a.InternalProductId == internalProductId);
                cacheService.SetData(cacheKey, product, TimeSpan.FromHours(6));
            }

            return product;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    public async Task<Product> GetProductByProductIdWithoutMerchantReference(long productId)
    {
        try
        {
            var cacheKey = $"MerchantService_GetProductByProductIdWithoutMerchantReference_{productId}";
            var product = await cacheService.GetData<Product>(cacheKey);
            if (product == null)
            {
            product = await merchantDbContext.Products
                .AsNoTracking()
                .FirstOrDefaultAsync(a => a.Id == productId);
                cacheService.SetData(cacheKey, product, TimeSpan.FromHours(2));
            }
            return product;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    public async Task UpdateMerchantUserPasswordAsync(
        MerchantUserPartnerPortalPasswordDto merchantUserPartnerPortalPasswordDto)
    {
        var now = DateTime.UtcNow;
        var webShopsUsersToken = await merchantDbContext.UserTokens
            .AsNoTracking()
            .Include(a => a.FkUser)
            .FirstOrDefaultAsync(a =>
                a.Token == merchantUserPartnerPortalPasswordDto.Token && a.Expire >= now && a.Active)
            .ConfigureAwait(false);

        if (webShopsUsersToken != null)
        {
            webShopsUsersToken.FkUser.UserTokens = null;
            webShopsUsersToken.Active = false;
            webShopsUsersToken.FkUser.Password = HashPassword(merchantUserPartnerPortalPasswordDto.Password);
            webShopsUsersToken.FkUser.Password2 = HashPassword2(merchantUserPartnerPortalPasswordDto.Password);
            merchantDbContext.UserTokens.Update(webShopsUsersToken);
            await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
        }
        /* Used only when a user needs to have their password manually updated !! */
        /*var user = await merchantDbContext.Users
            .FirstOrDefaultAsync(a => a.Id == 1132).ConfigureAwait(false);
        user.Password = HashPassword(merchantUserPartnerPortalPasswordDto.Password);
        user.Password2 = HashPassword2(merchantUserPartnerPortalPasswordDto.Password);
        merchantDbContext.Users.Update(user);
        await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);*/
    }

    public async Task<ProductRefDto?> GetInAppProduct(string internalProductId)
    {
        var product = await merchantDbContext.Products
            .Include(a => a.FkMerchant)
            .Include(product => product.Variants)
            .Where(a => a.RegularPrice != null && a.RegularPrice > StaticVariables.MinimumPriceForShowingProduct &&
                        a.ProductImages != null && a.ProductImages != "")
            .SingleOrDefaultAsync(a => a.InternalProductId == internalProductId && a.Active);
        if (product == null)
        {
            return null;
        }

        var image = "";
        if (!string.IsNullOrEmpty(product.ProductImages))
        {
            var images = JsonSerializer.Deserialize<List<ProductImageDto>>(product.ProductImages);
            image = images?.FirstOrDefault()?.Src ?? "";
        }

        return new ProductRefDto
        {
            Id = product.InternalProductId,
            Name = product.Name,
            NormalPrice = product.RegularPrice ?? 0,
            SalePrice = product.Price ?? 0,
            OnSale = product.Price != null && product.Price != 0 && (product.RegularPrice != product.Price),
            Favored = false,
            IncludesVariants = product.Variants.Count > 0,
            ImageSrc = image,
            MerchantId = product.FkMerchantId,
            MerchantName = product.FkMerchant.Name
        };
    }

    public async Task<List<ProductRefDto>> GetInAppProductsFromIdList(List<string> internalProductIds)
    {
        List<ProductRefDto> productRefs = [];

        var products = await merchantDbContext.Products
            .Include(a => a.FkMerchant)
            .Include(product => product.Variants)
            .Where(a => a.RegularPrice != null && a.RegularPrice > StaticVariables.MinimumPriceForShowingProduct &&
                        a.ProductImages != null && a.ProductImages != "" &&
                        internalProductIds.Contains(a.InternalProductId) && a.Active).ToListAsync();

        foreach (var product in products)
        {
            var image = "";
            if (!string.IsNullOrEmpty(product.ProductImages))
            {
                var images = JsonSerializer.Deserialize<List<ProductImageDto>>(product.ProductImages);
                image = images?.FirstOrDefault()?.Src ?? "";
            }

            productRefs.Add(new ProductRefDto
                {
                    Id = product.InternalProductId,
                    Name = product.Name,
                    NormalPrice = product.RegularPrice ?? 0,
                    SalePrice = product.Price ?? 0,
                    OnSale = product.Price != null && product.Price != 0 && (product.RegularPrice != product.Price),
                    Favored = false,
                    IncludesVariants = product.Variants.Count > 0,
                    ImageSrc = image,
                    MerchantId = product.FkMerchantId,
                    MerchantName = product.FkMerchant.Name
                }
            );
        }

        ;

        return productRefs;
    }

    public async Task<bool> CheckFavorite(string internalProductId, string email)
    {
        var cacheKey = $"FavoriteProductIds_{email}_{partnerContext.PartnerId}";
        var cachedFavorites = await cacheService.GetData<List<string>>(cacheKey, true);

        if (cachedFavorites == null)
        {
            cachedFavorites = await merchantDbContext.ProductFavorites
                .Where(pf => pf.Email == email && pf.Active)
                .Select(pf => pf.InternalProductId)
                .ToListAsync();

            cacheService.SetData(cacheKey, cachedFavorites, TimeSpan.FromMinutes(30), true);
        }

        return cachedFavorites.Contains(internalProductId);
    }

    public async Task<List<string>> CheckFavoriteMultiple(List<string> internalProductIds, string email)
    {
        var cacheKey = $"FavoriteProductIds_{email}_{partnerContext.PartnerId}";
        var cachedFavorites = await cacheService.GetData<List<string>>(cacheKey, true);

        if (cachedFavorites == null)
        {
            cachedFavorites = await merchantDbContext.ProductFavorites
                .Where(pf => pf.Email == email && pf.Active)
                .Select(pf => pf.InternalProductId)
                .ToListAsync();

            cacheService.SetData(cacheKey, cachedFavorites, TimeSpan.FromMinutes(30), true);
        }

        return cachedFavorites.Where(internalProductIds.Contains).ToList();
    }

    public async Task<List<Product>> GetFavorites(string email)
    {
        return await merchantDbContext.ProductFavorites
            .Include(p => p.FkProduct)
            .ThenInclude(a => a.FkMerchant)
            .AsNoTracking()
            .Where(a => a.Email == email
                        && a.Active
                        && a.FkProduct.Active)
            .Select(a => a.FkProduct)
            .ToListAsync();
    }

    public async Task<ProductSingleRefResponse> GetInAppProduct(string productId, string email, string partnerGuid = "")
    {
        var product = await merchantDbContext.Products
            .Include(a => a.ProductFavorites)
            .Include(a => a.Variants)
            .Include(a => a.FkMerchant)
            .ThenInclude(a => a.MerchantAssets.Where(b => b.FkMerchantAssetTypeId == 2))
            .SingleOrDefaultAsync(a => a.InternalProductId == productId);

        //Check if product exists
        if (product == null)
        {
            return new ProductSingleRefResponse
            {
                Error = new ShopErrorDto()
                {
                    Status = 404,
                    Error = "Not found",
                    Message = "Provided ProductId was not associated with an existing product"
                }
            };
        }

        var images = new List<string>();
        if (!string.IsNullOrEmpty(product.ProductImages))
        {
            var imagesRaw = JsonSerializer.Deserialize<List<ProductImageDto>>(product.ProductImages);
            if (imagesRaw != null)
            {
                images = imagesRaw.Select(a => a.Src).ToList();
            }
        }
        //TODO lav secret om fra https://viaads-redirect-service-dev.azurewebsites.net/redirectdiscount/ til https://viaads-redirect-service-dev.azurewebsites.net/

        var redirectUrl = configuration["RedirectService-Url"] + "redirectShop/" + JwtTokenEncode(email, partnerGuid,
            product.Permalink, product.InternalProductId, product.FkMerchantId.ToString(), product.FkMerchant.Name);

        return new ProductSingleRefResponse
        {
            Data = new ProductSingleRefDto
            {
                Id = product.InternalProductId,
                Name = product.Name,
                Description = product.Description ?? product.ShortDescription ?? string.Empty,
                Favored = product.ProductFavorites.Where(a => a.Email == email && a.Active).ToList().Count > 0,
                IncludesVariants = product.Variants.Count > 0,
                ProductImages = images,
                NormalPrice = product.RegularPrice ?? 0,
                SalePrice = product.Price ?? 0,
                OnSale = product.Price != null && product.Price != 0 && (product.RegularPrice != product.Price),
                RedirectLink = redirectUrl,
                MerchantLogoSrc = product.FkMerchant.MerchantAssets.FirstOrDefault()?.Src ?? "",
                MerchantName = product.FkMerchant?.DisplayName ?? ""
            }
        };
    }

    public async Task<ResponseDto> UpdateFavoriteProduct(ShopFavoriteRequestDto favoriteRequest)
    {
        // Validate the product exists
        var product = await merchantDbContext.Products
            .Include(a => a.ProductFavorites.Where(b => b.Email == favoriteRequest.Email))
            .SingleOrDefaultAsync(a => a.InternalProductId == favoriteRequest.ProductId)
            .ConfigureAwait(false);
    
        if (product == null)
        {
            return new ResponseDto
            {
                Success = false,
                Message = "Invalid productId provided"
            };
        }

        // Check if a favorite already exists for this email/product combination
        var existingFavorite = await merchantDbContext.ProductFavorites
            .FirstOrDefaultAsync(
                f => f.InternalProductId == favoriteRequest.ProductId && f.Email == favoriteRequest.Email);

        if (favoriteRequest.Favored)
        {
            // Adding to favorites
            if (existingFavorite == null)
            {
                // Create a new favorite
                product.ProductFavorites.Add(new ProductFavorite
                {
                    Email = favoriteRequest.Email,
                    InternalProductId = favoriteRequest.ProductId,
                    Active = true
                });
            }
            else
            {
                // Update existing favorite
                existingFavorite.Active = favoriteRequest.Favored;
            }
        }
        else
        {
            // Removing from favorites
            if (existingFavorite != null)
            {
                existingFavorite.Active = favoriteRequest.Favored;
            }
            else
            {
                // Create a new favorite
                product.ProductFavorites.Add(new ProductFavorite
                {
                    Email = favoriteRequest.Email,
                    InternalProductId = favoriteRequest.ProductId,
                    Active = false
                });
            }
        }

        await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);

        return new ResponseDto
        {
            Success = true,
            Message = "Updated"
        };
    }

    public async Task<bool> CheckIfMerchantIsCustomer(int merchantId)
    {
        var isCustomer = false;
        var merchant = await merchantDbContext.Merchants
            .Include(a => a.MerchantMeta)
            .SingleOrDefaultAsync(a => a.Id == merchantId);
        if (merchant != null)
        {
            isCustomer = merchant.MerchantMeta.SingleOrDefault(a =>
                a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer && (a.EndDate == null ||
                    a.EndDate < DateTime.UtcNow)) != null;
        }

        return isCustomer;
    }

    public async Task<bool> CheckIfMarketingIsAllowed(int merchantId)
    {
        var marketingAllowed = false;
        var merchant = await merchantDbContext.Merchants.SingleOrDefaultAsync(a => a.Id == merchantId);
        if (merchant != null)
        {
            marketingAllowed = merchant.IsMarketingAllowed;
        }

        return marketingAllowed;
    }

    private async Task UpdateWebshopCategoriesAsync(Merchant existingMerchant,
        Merchant merchant)
    {
        // Delete Webshop Categories
        foreach (var existing in existingMerchant.MerchantCategoryRels.ToList()
                     .Where(existing =>
                         merchant.MerchantCategoryRels.All(c =>
                             c.FkMerchantCategoryId != existing.FkMerchantCategoryId)))
        {
            merchantDbContext.MerchantCategoryRels.Remove(existing);
        }

        // Insert Webshop Categories
        foreach (var category in merchant.MerchantCategoryRels)
        {
            var existing = existingMerchant.MerchantCategoryRels
                .FirstOrDefault(c => c.FkMerchantCategoryId == category.FkMerchantCategoryId && c.Id != 0);

            if (existing == null)
            {
                // Insert Webshop Category
                existingMerchant.MerchantCategoryRels.Add(new MerchantCategoryRel()
                {
                    FkMerchantCategoryId = category.FkMerchantCategoryId, FkMerchantId = merchant.Id
                });
            }
        }

        await merchantDbContext.SaveChangesAsync().ConfigureAwait(false);
    }

    private ProductReturnDto MapProductToReturn(Product product, bool productValidate = false)
    {
        var productReturn = new ProductReturnDto
        {
            Name = product.Name,
            MerchantId = product.FkMerchantId,
            MerchantProductId = product.MerchantProductId,
            Sku = product.Sku ?? "",
            ParentInternalProductId = "",
            Description = product.Description ?? "",
            ShortDescrption = product.ShortDescription ?? "",
            Status = product.Status ?? "",
            Price = product.Price,
            RegularPrice = product.RegularPrice,
            DateCreated = product.CreatedDate,
            DateUpdated = product.LastModifiedDate,
            PermaLink = product.Permalink ?? "",
            StockQuantity = product.StockQuantity,
            InternalProductId = product.InternalProductId,
            ProductImages = []
        };
        if (productValidate)
        {
            try
            {
                HttpWebRequest request = (HttpWebRequest) WebRequest.Create(productReturn.PermaLink);
                request.Method = "HEAD";
                request.UserAgent =
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
                using (HttpWebResponse response = (HttpWebResponse) request.GetResponse())
                {
                    if (response.ResponseUri.ToString() != productReturn.PermaLink ||
                        response.StatusCode == HttpStatusCode.NotFound)
                    {
                        logger.ForContext("service_name", GetType().Name).Error(
                            "Can't find product on url: {PermaLink} with internalProductId: {ProductInternalId}",
                            productReturn.PermaLink, productReturn.InternalProductId);
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.ForContext("service_name", GetType().Name).Error(ex,
                    "Can't find product on url: {PermaLink} with internalProductId: {ProductInternalId}",
                    productReturn.PermaLink, productReturn.InternalProductId);
            }
        }

        //Check if there are any images
        if (string.IsNullOrEmpty(product.ProductImages))
        {
            return productReturn;
        }

        var images = (JsonSerializer.Deserialize<List<ProductImage>>(product.ProductImages) ??
                      new List<ProductImage>()).ToList();
        foreach (var productImage in images)
        {
            var image = new ProductReturnImageDto
            {
                Src = productImage.Src
            };
            if (productValidate)
            {
                try
                {
                    if (productImage.Src.ToLower().StartsWith("https"))
                    {
                        HttpWebRequest request = (HttpWebRequest) WebRequest.Create(productImage.Src);
                        request.Method = "HEAD";
                        request.UserAgent =
                            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36";
                        using (HttpWebResponse response = (HttpWebResponse) request.GetResponse())
                        {
                            // Check that the remote file has been found and that it is of an image type
                            var checkResponse = response.StatusCode == HttpStatusCode.OK &&
                                                response.ContentType.StartsWith("image/");
                            var responseType = response.ContentType;
                            if (checkResponse)
                            {
                                productReturn.ProductImages.Add(image);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                }
            }
            else
            {
                productReturn.ProductImages.Add(image);
            }
        }


        return productReturn;
    }

    private ProductReturnDto MapProductToReturn(Variant variant, bool variantValidate = false)
    {
        var productReturn = new ProductReturnDto
        {
            Name = variant.Name,
            MerchantId = variant.FkProduct.FkMerchantId,
            MerchantProductId = variant.MerchantProductId,
            Sku = variant.Sku ?? "",
            ParentInternalProductId = variant.FkProduct.InternalProductId,
            Description = variant.Description ?? "",
            ShortDescrption = variant.ShortDescription ?? "",
            Status = variant.Status ?? "",
            Price = variant.Price,
            RegularPrice = variant.RegularPrice,
            HighestPrice = null,
            LowestPrice = null,
            DateCreated = variant.CreatedDate,
            DateUpdated = variant.LastModifiedDate,
            PermaLink = variant.Permalink ?? "",
            StockQuantity = variant.StockQuantity,
            InternalProductId = variant.InternalVariantId,
            ProductImages = []
        };

        if (variantValidate)
        {
            try
            {
                HttpWebRequest request = (HttpWebRequest) WebRequest.Create(productReturn.PermaLink);
                request.Method = "HEAD";
                using (HttpWebResponse response = (HttpWebResponse) request.GetResponse())
                {
                    if (response.ResponseUri.ToString() != productReturn.PermaLink ||
                        response.StatusCode == HttpStatusCode.NotFound)
                    {
                        logger.ForContext("service_name", GetType().Name).Error(
                            "Can't find product on url: {PermaLink} with internalProductId: {ProductInternalId} for variant",
                            productReturn.PermaLink, productReturn.InternalProductId);
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.ForContext("service_name", GetType().Name).Error(ex,
                    "Can't find product on url: {PermaLink} with internalProductId: {ProductInternalId} for variant",
                    productReturn.PermaLink, productReturn.InternalProductId);
            }
        }

        //Check if there are any images
        if (string.IsNullOrEmpty(variant.ProductImages))
        {
            return productReturn;
        }

        var images = (JsonSerializer.Deserialize<List<ProductImage>>(variant.ProductImages) ?? []).ToList();
        foreach (var productImage in images)
        {
            var image = new ProductReturnImageDto
            {
                Src = productImage.Src
            };
            if (variantValidate)
            {
                try
                {
                    if (productImage.Src.ToLower().StartsWith("https"))
                    {
                        HttpWebRequest request = (HttpWebRequest) WebRequest.Create(productImage.Src);
                        request.Method = "HEAD";
                        using (HttpWebResponse response = (HttpWebResponse) request.GetResponse())
                        {
                            // Check that the remote file has been found and that it is of an image type
                            var checkResponse = response.StatusCode == HttpStatusCode.OK &&
                                                response.ContentType.StartsWith("image/");
                            var responseType = response.ContentType;
                            if (checkResponse)
                            {
                                productReturn.ProductImages.Add(image);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.ForContext("service_name", GetType().Name).Error(ex,
                        "Can't find product on url: {PermaLink} with internalProductId: {ProductInternalId} for variant",
                        productReturn.PermaLink, productReturn.InternalProductId);
                }
            }
            else
            {
                productReturn.ProductImages.Add(image);
            }
        }

        return productReturn;
    }

    private string HashPassword(string password)
    {
        using (SHA256 sha256 = SHA256.Create())
        {
            byte[] passwordBytes = Encoding.UTF8.GetBytes(password);
            byte[] hashedBytes = sha256.ComputeHash(passwordBytes);
            return Convert.ToBase64String(hashedBytes);
        }
    }

    private string HashPassword2(string password)
    {
        string salt = BCrypt.Net.BCrypt.GenerateSalt();
        string hashedPassword = BCrypt.Net.BCrypt.HashPassword(password, salt);
        return hashedPassword;
    }

    private async Task<MonitorErrorObjectDto> CustomerPages(int webshopId, int hours, string type)
    {
        var newest = await elasticService.NewestCustomerPageEvents(webshopId,
            type).ConfigureAwait(false);
        return new MonitorErrorObjectDto(newest?.Event_received,
            !(newest?.Event_received > DateTime.UtcNow.AddHours(-hours)) || newest?.Event_received == null);
    }

    private string JwtTokenEncode(string email, string partnerGuid, string url, string productId,
        string webshopId = "", string webshopName = "")
    {
        var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(vaultSettings.JwtTokenKey));

        var tokenHandler = new JwtSecurityTokenHandler();

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity([
                new Claim("email", email),
                new Claim("url", url),
                new Claim("pid", productId),
                //new Claim("did", discountId),
                new Claim("wid", webshopId),
                new Claim("wname", webshopName),
                //new Claim("type", type),
                //new Claim("dcode", discountCode ?? ""),
                //new Claim("dcodeid", discountCodeId ?? ""),
                //new Claim("vbdai", debtorAccountId)
                new Claim("pguid", partnerGuid),
                new Claim("parid", partnerContext.PartnerId.ToString())
            ]),
            Issuer = vaultSettings.JwtIssuer,
            SigningCredentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha512Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);

        return tokenHandler.WriteToken(token);
    }

    public async Task<string> GetWebshopMetaValueByNameAsync(string metaName, int merchantId)
    {
        var merchant = await merchantDbContext.Merchants
            .Include(w => w.MerchantMeta) // Include the related metadata entries
            .FirstOrDefaultAsync(w => w.Id == merchantId); // Find the webshop by ID

        if (merchant == null)
        {
            throw new ArgumentException($"No merchant found with ID: {merchantId}");
        }

        // Find the meta entry by name
        var metaEntry = merchant.MerchantMeta.FirstOrDefault(m => m.FkMerchantMetaTypeName == metaName);
        if (metaEntry == null)
        {
            throw new ArgumentException($"No meta entry found with name: {metaName} for merchant ID: {merchantId}");
        }

        return metaEntry.Value;
    }


    private async Task<string> GetWebshopMetaValueByInternalProductIdAsync(string metaName, string internalProductId)
    {
        var product = await merchantDbContext.Products
            .Include(w => w.FkMerchant)
            .ThenInclude(w => w.MerchantMeta) // Include the related metadata entries
            .FirstOrDefaultAsync(w => w.InternalProductId == internalProductId); // Find the webshop by ID

        if (product?.FkMerchant == null)
        {
            throw new ArgumentException($"No webshop found with Internal Product ID: {internalProductId}");
        }

        // Find the meta entry by name
        var metaEntry = product.FkMerchant.MerchantMeta.FirstOrDefault(m => m.FkMerchantMetaTypeName == metaName);
        if (metaEntry == null)
        {
            throw new ArgumentException(
                $"No meta entry found with name: {metaName} for webshop ID: {product.FkMerchantId}");
        }

        return metaEntry.Value;
    }

    // Shopify Validation Functions

    public async Task<PluginInstallInfo?> GetShopifyByDomainAsync(string myShopifyDomain)
    {
        return await merchantDbContext.PluginInstallInfos
            .FirstOrDefaultAsync(a => a.Type == "Shopify" && EF.Functions.Like(a.Url, $"{myShopifyDomain}.%"));
    }

    public async Task<PluginInstallInfo?> GetShopifyByEmailAsync(string email)
    {
        return await merchantDbContext.PluginInstallInfos
            .FirstOrDefaultAsync(a => a.Type == "Shopify" && a.ShopEmail == email);
    }

    public async Task<bool> CheckIfMyshopifyDomainIsClaimed(string myShopifyDomain,
        int webshopId)
    {
        var apiUrlMeta =
            merchantDbContext.MerchantMeta.SingleOrDefault(a =>
                a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUrl &&
                EF.Functions.Like(a.Value, $"{myShopifyDomain}.%") && a.FkMerchantId != webshopId);

        return apiUrlMeta != null;
    }

    // DanDomain Classic Validation Functions
    public async Task<List<PluginInstallInfo>?> GetLatestDanDomainClassicShops()
    {
        return await merchantDbContext.PluginInstallInfos.Where(a => a.Type == "DanDomainClassic")
            .OrderByDescending(a => a.Id).Take(5).ToListAsync();
    }

    public async Task<PluginInstallInfo?> GetDanDomainClassicByIdentifierAsync(long shopIdentifier)
    {
        return await merchantDbContext.PluginInstallInfos
            .FirstOrDefaultAsync(a => a.ShopIdentifier == shopIdentifier.ToString() && a.Type == "DanDomainClassic");
    }

    public async Task<bool> CheckIfDanDomainClassicIsClaimed(long shopIdentifier, int webshopId)
    {
        var apiUrlMeta =
            merchantDbContext.MerchantMeta.SingleOrDefault(a =>
                a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUrl &&
                EF.Functions.Like(a.Value, $"%//{shopIdentifier}.%") &&
                a.FkMerchantId != webshopId);
        return apiUrlMeta != null;
    }

    public async Task Test()
    {
        /*var webshops = await _merchantDbContext.Merchants
            .Include(a => a.MerchantMeta)
            .Where(a => a.Type == "DanDomain").ToListAsync();

        foreach (var webshop in webshops)
        {
            var apiKey = webshop.MerchantMeta.SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey);
            if (apiKey != null)
            {
                var decryptString = Encoding.UTF8.GetString(Convert.FromBase64String(apiKey.Value));
                decryptString = DecryptString("kYp2s5v8yFB?E(H+MbQeThWmZq4t6w9z", decryptString);
                var keyValues = decryptString.Split("\\\\");
                var username = keyValues.First();
                var password = keyValues.Last();

                var apiUserKey =
                    webshop.MerchantMeta.SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUserKey);
                if (apiUserKey == null)
                {
                    webshop.MerchantMeta.Add(new MerchantMetum
                    {
                        FkMerchantMetaTypeName = MerchantMetaTypeNames.ApiUserKey,
                        Value = username
                    });
                }
                else
                {
                    webshop.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUserKey).Value =
                        username;
                }

                var apiUserSecret =
                    webshop.MerchantMeta.SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUserSecret);
                if (apiUserSecret == null)
                {
                    webshop.MerchantMeta.Add(new MerchantMetum
                    {
                        FkMerchantMetaTypeName = MerchantMetaTypeNames.ApiUserSecret,
                        Value = password
                    });
                }
                else
                {
                    webshop.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUserSecret).Value =
                        password;
                }

                await UpdateWebshopAsync(webshop);
            }
        }*/
    }

    private static string DecryptString(string key, string cipherText)
    {
        byte[] iv = new byte[16];
        byte[] buffer = Convert.FromBase64String(cipherText);
        using (Aes aes = Aes.Create())
        {
            aes.Key = Encoding.UTF8.GetBytes(key);
            aes.IV = iv;
            ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);
            using (MemoryStream memoryStream = new MemoryStream(buffer))
            {
                using (CryptoStream cryptoStream =
                       new CryptoStream((Stream) memoryStream, decryptor, CryptoStreamMode.Read))
                {
                    using (StreamReader streamReader = new StreamReader((Stream) cryptoStream))
                    {
                        return streamReader.ReadToEnd();
                    }
                }
            }
        }
    }

    // Validation Helpers
    private static string GetDomainPrefix(string domain)
    {
        return domain.Contains('.')
            ? domain.Split('.')[0]
            : domain;
    }

    private static bool DoesUrlContainIdentifier(string? url, long identifier)
    {
        if (string.IsNullOrEmpty(url))
            return false;

        return Uri.TryCreate(url, UriKind.Absolute, out var uri) &&
               uri.Host.Contains(identifier.ToString(), StringComparison.OrdinalIgnoreCase);
    }

    public async Task AddOrUpdateMerchantMeta(Merchant merchant, string metaTypeName,
        string value)
    {
        var existingMeta = merchant.MerchantMeta.SingleOrDefault(a => a.FkMerchantMetaTypeName == metaTypeName);

        if (existingMeta == null)
        {
            merchant.MerchantMeta.Add(new MerchantMetum
            {
                FkMerchantMetaTypeName = metaTypeName,
                Value = value,
                FkMerchantId = merchant.Id
            });
        }
        else
        {
            existingMeta.Value = value;
        }
    }

    public async Task<string> FetchOrAddMerchantMeta(Merchant webshop, string metaTypeName,
        Func<string> valueGenerator)
    {
        var existingMeta = webshop.MerchantMeta.SingleOrDefault(a => a.FkMerchantMetaTypeName == metaTypeName);

        if (existingMeta == null)
        {
            var newValue = valueGenerator();
            webshop.MerchantMeta.Add(new MerchantMetum
            {
                FkMerchantMetaTypeName = metaTypeName,
                Value = newValue
            });
            return newValue;
        }
        else
        {
            return existingMeta.Value;
        }
    }

    /// <summary>
    /// Activates synchronization for the specified merchant.
    /// </summary>
    /// <param name="merchantId">The ID of the merchant to activate synchronization for.</param>
    public async Task<bool> ActivateSync(int merchantId)
    {
        try
        {
            var webshop = await merchantDbContext.Merchants.SingleOrDefaultAsync(a => a.Id == merchantId);
            if (webshop == null) return false;

            webshop.Sync = true;
            await merchantDbContext.SaveChangesAsync();
            await ClearMerchantCache(webshop.Id);
            return true;
        }
        catch (Exception ex)
        {
            logger.Error(ex, $"Failed to activate sync for webshop with ID {merchantId}");
            return false;
        }
    }

    public async Task ClearMerchantCache(int merchantId, string? apiKey = null, string? shopIdentifier = null)
    {
        await GetByIdAsync(merchantId, true);
        await GetByIdFullAsync(merchantId, true);

        if (apiKey != null)
        {
            await ValidateAsync(apiKey, true);
            await ValidateOrderApiKey(apiKey, true);
        }

        if (shopIdentifier != null)
        {
            await ValidateShopIdentifierAsync(shopIdentifier, true);
        }
    }

    public async Task<MerchantAsset> GenerateMerchantAsset(MerchantAssetDto merchantAssetDto)
    {
        DateOnly? dateOnlyNullable = merchantAssetDto.OverrideExpireDate.HasValue
            ? DateOnly.FromDateTime(merchantAssetDto.OverrideExpireDate.Value)
            : null;

        var webshopAsset = new MerchantAsset
        {
            Src = "",
            Active = true,
            FkMerchantId = merchantAssetDto.MerchantId,
            OverrideExpireDate = dateOnlyNullable,
            FkMerchantAssetTypeId = merchantAssetDto.TypeId,
        };

        var assetImageType = ImageExtractors.ExtractFileTypeFromBase64(merchantAssetDto.Src);

        if (merchantAssetDto.TypeId == 2)
        {
            var oldLogo = await merchantDbContext.MerchantAssets.Where(a => a.Active && a.FkMerchantAssetTypeId == 2)
                .FirstOrDefaultAsync();
            if (oldLogo != null)
            {
                oldLogo.Active = false;
            }
        }

        await merchantDbContext.AddAsync(webshopAsset);
        await merchantDbContext.SaveChangesAsync();

        if (merchantAssetDto.TypeId == 3 && merchantAssetDto.GenerateImage)
        {
            FontCollection collection = new();
            FontFamily smallFamily = collection.Add("Models/StaticData/Poppins-Regular.ttf");
            Font font = smallFamily.CreateFont(100);

            // Loading Base Asset
            using Image<Rgba64> background = await imageService.LoadImageAsync(merchantAssetDto.Src);

            // Loading Logo
            using Image<Rgba64> logo = await imageService.LoadImageAsync(merchantAssetDto.Logo);

            // Apply enhancements to the background
            background.Mutate(x => x.Brightness(0.80f).Contrast(1.03f));

            // Draw the logo on top of the background with overlay
            background.Mutate(ctx => ctx.DrawImage(logo, new Point(55, 55), PixelColorBlendingMode.Normal, 1f));
            background.Mutate(x => x.DrawText(merchantAssetDto.Text, font, Color.White, new PointF(80, 534)));

            using (MemoryStream ms = new MemoryStream())
            {
                /*var webpEncoder = new WebpEncoder
                {
                    Quality = 100
                };
                await background.SaveAsync(ms, webpEncoder);

                var image = _imageService.CreateImages($"{webshopAsset.Id}.{assetImageType}",
                    $"data:image/{assetImageType};base64," + Convert.ToBase64String(ms.ToArray()), $"curated-webshop");
                webshopAsset.Src = image;*/
                var jpegEncoder = new JpegEncoder()
                {
                    Quality = 100
                };
                await background.SaveAsync(ms, jpegEncoder);

                var image = imageService.CreateImages($"{webshopAsset.Id}.jpeg",
                    "data:image/jpeg;base64," + Convert.ToBase64String(ms.ToArray()), $"webshops-curated");
                webshopAsset.Src = image;
            }
        }
        else
        {
            webshopAsset.Src = imageService.CreateImages($"{webshopAsset.Id}.{assetImageType}",
                merchantAssetDto.Src, "webshops");
        }

        await merchantDbContext.SaveChangesAsync();
        merchantDbContext.ChangeTracker.Clear();
        return await merchantDbContext.MerchantAssets
            .Include(a => a.FkMerchantAssetType)
            .SingleAsync(a => a.Id == webshopAsset.Id);
    }

    public async Task<ResponseDto> RemoveMerchantAsset(int assetId)
    {
        var asset = await merchantDbContext.MerchantAssets.FindAsync(assetId);
        if (asset == null)
            return new ResponseDto {Success = false, Message = "Asset not found."};

        asset.Active = false;

        await merchantDbContext.SaveChangesAsync();

        return new ResponseDto {Success = true, Message = "Asset deactivated successfully."};
    }

    public async Task<List<Merchant>> GetMerchantPayers()
    {
        var partnerId = partnerContext.PartnerId;
        var merchants = await memoryCache.GetOrCreateAsync($"MerchantService_GetMerchantPayers_ {partnerId}",
            async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1);
                return await merchantDbContext.Merchants
                    .Include(a => a.MerchantPayers.Where(b => b.Active))
                    .ThenInclude(a => a.FkPayer)
                    .Include(a => a.MerchantPayments)
                    .Include(a => a.MerchantMeta)
                    .Where(a => a.Active && a.FkPartnerId == partnerId)
                    .ToListAsync();
            });
        return merchants;

        /*return await _merchantDbContext.Merchants
            .Include(a => a.MerchantPayers.Where(b => b.Active))
            .ThenInclude(a => a.FkPayer)
            .Include(a => a.MerchantPayments)
            .Include(a => a.MerchantMeta)
            .Where(a => a.Active)
            .ToListAsync();*/
    }

    public async Task<List<Payer>> GetPayers()
    {
        return await merchantDbContext.Payers.ToListAsync();
    }

    public async Task<string> Defragmentation()
    {
        var start = DateTime.UtcNow;
        using (var command = merchantDbContext.Database.GetDbConnection().CreateCommand())
        {
            command.CommandTimeout = 1200;
            command.CommandText = "dbo.RebuildFragmentedIndexes ";
            command.CommandType = CommandType.StoredProcedure;
            command.CommandTimeout = 60 * 60 * 2; // Timeout in seconds = 2 hours

            await merchantDbContext.Database.OpenConnectionAsync();
            try
            {
                await command.ExecuteNonQueryAsync();
            }
            finally
            {
                await merchantDbContext.Database.CloseConnectionAsync();
            }
        }

        return $"Execution time {(DateTime.UtcNow - start).TotalSeconds}";
    }

    public async Task DeactivateAllMerchantProducts(int merchantId)
    {
        string sql =
            "UPDATE Merchant.Products SET LastModifiedDate = @lastModifiedDate, Active = 0 WHERE FK_MerchantId = @merchantId";
        object[] parameters =
        {
            new SqlParameter("@lastModifiedDate", DateTime.UtcNow),
            new SqlParameter("@merchantId", merchantId)
        };

        await merchantDbContext.Database.ExecuteSqlRawAsync(sql, parameters);
    }

    public async Task DeactivateProduct(string merchantProductId, string merchantUrl, int? merchantId = null)
    {
        if (merchantId == null)
        {
            merchantId = (await merchantDbContext.MerchantMeta.FirstOrDefaultAsync(a =>
                a.Value == merchantUrl && a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUrl))?.FkMerchantId;
        }

        if (merchantId != null)
        {
            //_logger.ForContext("service_name", GetType().Name).Error("Product deletede shopify: product id: " + merchantProductId + " url: " + merchantUrl + " merchantId: " + merchantId);
            string sql =
                "UPDATE Merchant.Products SET LastModifiedDate = @lastModifiedDate, Active = 0 WHERE FK_MerchantId = @merchantId and MerchantProductId = @merchantProductId";
            object[] parameters =
            {
                new SqlParameter("@lastModifiedDate", DateTime.UtcNow),
                new SqlParameter("@merchantId", merchantId),
                new SqlParameter("@merchantProductId", merchantProductId)
            };

            await merchantDbContext.Database.ExecuteSqlRawAsync(sql, parameters);
        }
    }

    public async Task<List<Merchant>> HandleIsMarketingAllowed()
    {
        var merchants = await merchantDbContext
            .Merchants
            .Include(a => a.MerchantMeta)
            .ToListAsync();

        foreach (var merchant in merchants)
        {
            var now = DateTime.UtcNow;
            var isCustomer = merchant.MerchantMeta
                .Any(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer && a.StartDate != null &&
                          (a.EndDate == null || a.EndDate >= now));

            var isCustomerNewest = merchant.MerchantMeta
                .Where(a => a.EndDate != null)
                .OrderByDescending(a => a.EndDate)
                .FirstOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer);

            var cutOffDate = merchant.MerchantMeta
                .FirstOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.CutOffDate && a.EndDate <= now);

            if (!merchant.Active || merchant.IsDev)
            {
                if (merchant.IsMarketingAllowed)
                {
                    merchant.IsMarketingAllowed = false;
                    await UpdateAllProductsLastModifiedDate(merchant.Id);
                    await DisableMerchantRelevanceScoreAsync(merchant.Id);
                }
            }
            else if (cutOffDate != null)
            {
                if (merchant.IsMarketingAllowed)
                {
                    merchant.IsMarketingAllowed = false;
                    await UpdateAllProductsLastModifiedDate(merchant.Id);
                    await DisableMerchantRelevanceScoreAsync(merchant.Id);
                }

                if (isCustomerNewest?.EndDate <= now)
                {
                    merchantDbContext.Remove(cutOffDate);
                }
            }
            else if (isCustomer)
            {
                if (!merchant.IsMarketingAllowed)
                {
                    merchant.IsMarketingAllowed = true;
                    await UpdateAllProductsLastModifiedDate(merchant.Id);
                }
            }
            else
            {
                if (merchant.IsMarketingAllowed)
                {
                    merchant.IsMarketingAllowed = false;
                    await UpdateAllProductsLastModifiedDate(merchant.Id);
                    await DisableMerchantRelevanceScoreAsync(merchant.Id);
                }
            }
        }

        await merchantDbContext.SaveChangesAsync();
        return merchants;
    }

    private async Task UpdateAllProductsLastModifiedDate(int merchantId)
    {
        int batchSize = 2000;
        int batchNumber = 0;
        bool moreBatches = true;

        while (moreBatches)
        {
            DateTime batchTime = DateTime.UtcNow.AddSeconds(batchNumber);

            // Fetch a batch of products
            var productBatch = await merchantDbContext.Products
                .Where(p => p.FkMerchantId == merchantId)
                .OrderBy(p => p.Id)
                .Skip(batchNumber * batchSize)
                .Take(batchSize)
                .ToListAsync();

            if (productBatch.Count > 0)
            {
                // Update the batch
                string sql = "UPDATE Merchant.Products SET LastModifiedDate = @lastModifiedDate WHERE Id IN ({0})";
                var ids = string.Join(",", productBatch.Select(p => p.Id));
                sql = string.Format(sql, ids);

                object[] parameters =
                {
                    new SqlParameter("@lastModifiedDate", batchTime),
                };

                await merchantDbContext.Database.ExecuteSqlRawAsync(sql, parameters);

                // Increment the batch number for the next iteration
                batchNumber++;
            }
            else
            {
                // No more batches to process
                moreBatches = false;
            }
        }
    }

    public async Task CreateTermsAndConditionsAcceptEvent(int merchantId, string email, int? userId)
    {
        await merchantDbContext.TermsAndConditionsAcceptances.AddAsync(new TermsAndConditionsAcceptance()
        {
            Active = true,
            FkMerchantId = merchantId,
            Email = email,
            FkUserId = userId,
            Version = "1.0.0",
        });
        await merchantDbContext.SaveChangesAsync();
    }

    /// <summary>
    /// Calculates the actual and potential lines for merchants and updates the relevance scores in bulk.
    /// </summary>
    /// <remarks>
    /// This method retrieves merchant IDs and calculates relevance scores for different age groups and genders.
    /// It then performs a bulk update of the relevance scores in the database.
    /// </remarks>
    public async Task CalculateActualPotentialLinesAsync()
    {
        const byte ageInterval = 10; // The interval of ages to group by
        const byte startAge = 18; // The starting age for the calculation
        const byte endAge = 69; // The ending age for the calculation
        var genders = new[] {"Unknown", "Male", "Female"}; // The genders to consider

        // Retrieve all merchant IDs
        var merchantIds = await GetAllIdsAsync();
        var relevanceScores = new List<RelevanceScoreDto>();

        // Loop through each gender
        foreach (var gender in genders)
        {
            List<Task<List<MerchantScore>>> tasks = [];

            if (gender != "Unknown")
            {
                // Loop through each age group for known genders
                for (byte age = startAge; age <= endAge; age++)
                {
                    tasks.Add(elasticService.GetLinesExtended("invoices-lines", merchantIds, gender, age,
                        ageInterval));
                    tasks.Add(elasticService.GetLinesExtended("invoices-potentiallines", merchantIds, gender, age,
                        ageInterval));
                }
            }
            else
            {
                // Retrieve lines for the "Unknown" gender
                tasks.Add(elasticService.GetLinesExtended("invoices-lines", merchantIds, gender));
                tasks.Add(elasticService.GetLinesExtended("invoices-potentiallines", merchantIds, gender));
            }

            // Wait for all tasks to complete
            var results = await Task.WhenAll(tasks);

            // Process the results
            for (int i = 0; i < results.Length; i += 2)
            {
                var actual = results[i];
                var potentials = results[i + 1];
                var actualDict = actual.ToDictionary(x => x.MerchantId);

                // Merge actual and potential scores
                foreach (var potential in potentials)
                {
                    if (actualDict.TryGetValue(potential.MerchantId, out var score))
                    {
                        score.Sum += potential.Sum;
                    }
                    else
                    {
                        actual.Add(potential);
                    }
                }

                // Add the merged results to the relevance scores list
                relevanceScores.AddRange(actual.Select(line => new RelevanceScoreDto
                {
                    Age = gender == "Unknown" ? null : (byte?) (startAge + (i / 2) % (endAge - startAge + 1)),
                    AgeInterval = ageInterval,
                    Gender = gender,
                    ScoreSum = new decimal(line.Sum),
                    ScoreType = "ActualPotential",
                    MerchantId = Convert.ToInt32(line.MerchantId)
                }));
            }
        }

        // Perform a bulk update if there are any relevance scores
        if (relevanceScores.Count > 0)
        {
            await BulkUpdateMerchantRelevanceScoresAsync(relevanceScores);
        }
    }

    private async Task<DateTime> GetMaxElasticSyncDate()
    {
        return await merchantDbContext.Products
            .MaxAsync(p => p.ElasticSyncDate);
    }

    public async Task<bool> IsProductDeactivatedByMerchant(long productId)
    {
        var product = await merchantDbContext.Products
            .FirstOrDefaultAsync(p => p.Id == productId);

        if (product == null)
        {
            return false;
        }

        return !product.Active;
    }

    public async Task<Dictionary<long, string>> GetAllActiveProducts(int skip = 0, int take = 1000)
    {
        var disallowedMerchantIds = new List<int> { 4530, 4535, 4821, 4788, 5552, 4772, 2029 };
        return (await merchantDbContext.Products
            .AsNoTracking()
            .Where(p => p.Active && !string.IsNullOrEmpty(p.ProductImages) && !disallowedMerchantIds.Contains(p.FkMerchantId))
            .Skip(skip)
            .Take(take)
            .Select(p => new {p.Id, p.ProductImages})
            .ToDictionaryAsync(p => p.Id, p => p.ProductImages))!;
    }

    public async Task<string> GetMerchantName(int merchantId)
    {
        return await merchantDbContext.Merchants.FirstOrDefaultAsync(a => a.Id == merchantId)
            .ContinueWith(a => a.Result?.Name ?? "");
    }

    public async Task<List<string>> GetInactiveProducts()
    {
        var disallowedMerchantIds = new List<int> { 4530, 4535, 4821, 4788, 5552, 4772, 2029 };
        return await merchantDbContext.Products
            .Where(p => !p.Active && !disallowedMerchantIds.Contains(p.FkMerchantId) && p.LastModifiedDate > DateTime.UtcNow.AddDays(-3))
            .Select(p => p.InternalProductId)
            .ToListAsync();
    }

    public async Task<List<Merchant>> GetAllActiveCpmMerchants()
    {
        var partnerId = partnerContext.PartnerId;
        // The merchant has to be active customers and have the attribution type cpm
        return await merchantDbContext.Merchants
            .Include(m => m.MerchantMeta)
            .Where(m => m.Active && 
            m.FkPartnerId == partnerId &&
            m.MerchantMeta.Any(meta => meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType && meta.Value == "cpm") && 
            m.MerchantMeta.Any(meta => meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer && meta.StartDate != null &&
                          (meta.EndDate == null || meta.EndDate >= DateTime.UtcNow)))
            .ToListAsync();
    }

    public async Task<List<Merchant>> GetAllActiveBaseFeeMerchants()
    {
        var partnerId = partnerContext.PartnerId;
        return await merchantDbContext.Merchants
            .Include(m => m.MerchantMeta)
            .Where(m => m.Active && 
            m.FkPartnerId == partnerId &&
            m.MerchantMeta.Any(meta => meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.BaseFee && 
                               !string.IsNullOrWhiteSpace(meta.Value) && meta.Value != "0") && 
            m.MerchantMeta.Any(meta => meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer && meta.StartDate != null &&
                          (meta.EndDate == null || meta.EndDate >= DateTime.UtcNow)))
            .ToListAsync();
    }

    public async Task<MerchantDashboardSettingsDto> GetMerchantDashboardSettings(int merchantId)
    {
        var merchant = await merchantDbContext.Merchants
            .Include(m => m.MerchantMeta)
            .FirstOrDefaultAsync(m => m.Id == merchantId);
        
        return new MerchantDashboardSettingsDto()
        {
            ApiKey = merchant?.MerchantMeta.FirstOrDefault(m => m.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey)?.Value ?? string.Empty,
            ApiUserKey = merchant?.MerchantMeta.FirstOrDefault(m => m.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUserKey)?.Value ?? string.Empty,
            ApiUserSecret = merchant?.MerchantMeta.FirstOrDefault(m => m.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUserSecret)?.Value ?? string.Empty,
            MerchantId = merchant?.Id ?? 0,
        };
    }

    public async Task<MerchantDashboardSettingsDto> UpdateMerchantDashboardSettings(MerchantDashboardSettingsDto settings)
    {
        var merchant = await merchantDbContext.Merchants
            .Include(m => m.MerchantMeta)
            .FirstOrDefaultAsync(m => m.Id == settings.MerchantId);

        if (merchant == null)
        {
            throw new Exception("Merchant not found");
        }

        // Get existing Meta
        var existingMeta = merchant.MerchantMeta.FirstOrDefault(m => m.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey);

        existingMeta = merchant.MerchantMeta.FirstOrDefault(m => m.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUserKey);

        if (existingMeta == null)
        {   
            merchant.MerchantMeta.Add(new MerchantMetum()
            {
                FkMerchantMetaTypeName = MerchantMetaTypeNames.ApiUserKey,
                Value = settings.ApiUserKey
            });
        }       
        else
        {
            existingMeta.Value = settings.ApiUserKey;
        }

        existingMeta = merchant.MerchantMeta.FirstOrDefault(m => m.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUserSecret);

        if (existingMeta == null)
        {
            merchant.MerchantMeta.Add(new MerchantMetum()
            {
                FkMerchantMetaTypeName = MerchantMetaTypeNames.ApiUserSecret,
                Value = settings.ApiUserSecret
            });
        }   
        else
        {
            existingMeta.Value = settings.ApiUserSecret;
        }

        await merchantDbContext.SaveChangesAsync();

        return settings;
    }

    public async Task UpdateMerchantMeta(int merchantId, string metaTypeName, string value)
    {
        var merchant = await merchantDbContext.Merchants
            .Include(m => m.MerchantMeta)
            .FirstOrDefaultAsync(m => m.Id == merchantId);
            
        if (merchant == null)
        {
            throw new Exception("Merchant not found");
        }

        var existingMeta = merchant.MerchantMeta.FirstOrDefault(m => m.FkMerchantMetaTypeName == metaTypeName);

        if (existingMeta == null)
        {
            merchant.MerchantMeta.Add(new MerchantMetum()
            {
                FkMerchantMetaTypeName = metaTypeName,
                Value = value
            });
        }
        else
        {
            existingMeta.Value = value;
        }
        

        await merchantDbContext.SaveChangesAsync();
    }

    public async Task<Dictionary<int, List<MerchantMetum>>> GetMerchantsWithMetadataAsync(List<int> merchantIds)
    {
        var cacheKey = $"MerchantsWithMetadata_{string.Join(",", merchantIds.OrderBy(x => x))}";
        return await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(2);
            
            var merchants = await merchantDbContext.Merchants
                .Include(m => m.MerchantMeta.Where(meta => 
                    meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.CustomerSegmentMale ||
                    meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.CustomerSegmentFemale ||
                    meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.CustomerSegmentUnknown))
                .Where(m => merchantIds.Contains(m.Id) && m.Active)
                .ToListAsync();
                
            return merchants.ToDictionary(m => m.Id, m => m.MerchantMeta.ToList());
        }) ?? new Dictionary<int, List<MerchantMetum>>();
    }

    public async Task<List<MerchantAsset>> GetMerchantLogosAsync()
    {
        var cacheKey = $"MerchantService_GetMerchantLogosAsync_{partnerContext.PartnerId}";
        return await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(2);
            var merchantsIds = await merchantDbContext.Merchants.Where(a => a.Active && a.FkPartnerId == partnerContext.PartnerId).Select(a => a.Id).ToListAsync();

            return await merchantDbContext.MerchantAssets
                .Where(a => a.Active && a.FkMerchantAssetTypeId == 2 && merchantsIds.Contains(a.FkMerchantId))
                .ToListAsync();
        }) ?? new List<MerchantAsset>();
    }

    public async Task<Dictionary<string, Product>> GetProductsByProductIds(List<long> productIds)
    {
        return await merchantDbContext.Products
            .Include(p => p.FkMerchant)
            .Where(p => productIds.Contains(p.Id))
            .ToDictionaryAsync(p => p.InternalProductId, p => p);
    }
}