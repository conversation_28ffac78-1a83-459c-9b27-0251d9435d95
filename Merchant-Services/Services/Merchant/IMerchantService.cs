using System.Collections.Concurrent;
using Admin_Service.Models.MonitorEventWebshop;
using Merchant_Services.Models.ModelsDal.Merchant;
using Shared.Dto.MerchantDashboard.Settings;
using Shared.Dto.MerchantScore;
using Shared.Dto.Shop;
using Shared.Dto.Shop.Product;
using Shared.Dto.Webshop;
using Shared.Models;
using Shared.Models.Merchant;
using Shared.Models.Merchant.Assets;
using Webshops_Services.Models;

namespace Webshop.Webshop;

public interface IMerchantService
{
    Task<Merchant?> ValidateAsync(string apiKey, bool? forceUpdate = null);
    Task<Merchant?> ValidateShopIdentifierAsync(string shopIdentifier, bool? forceUpdate = null);
    Task<Merchant?> ValidateUrlAsync(string url, bool? forceUpdate = null);
    Task<Merchant?> ValidateOrderApiKey(string apiKey, bool? forceUpdate = null);
    Task<Merchant?> GetByApikeyAsync(string apiKey);
    Task<Merchant?> GetByIdAsync(int merchantId, bool? forceUpdate = null);
    Task<Merchant?> GetByIdIgnoreActiveAsync(int merchantId);
    Task<Merchant?> GetByIdFullAsync(int merchantId, bool? forceUpdate = false);
    Task<Merchant?> GetByIdReadyForUpdate(int merchantId);
    Task<List<Merchant>> GetMerchantsReadyForUpdate(int lookBackDaysOrder, int lookBackDaysProduct, bool onlyNew);
    Task<List<Merchant>> GetFullAsync();
    Task<List<Merchant>> GetSimpleAsync();
    Task<List<Merchant>> GetAsync();
    Task<List<Merchant>> GetAllAsync();
    Task<List<Merchant>> GetAllCustomersAsync();
    Task<List<int>> GetAllIdsAsync();
    Task<List<int>> GetAllIdsByPartnerIdAsync();
    Task<Dictionary<int, string>> GetAllNamesAsync();
    Task<List<string>?> GetNamesByIdsAsync(List<int> merchantIds);
    Task<Merchant?> GetPaymentByIdAsync(int merchantId);

    Task<ProductReturnDto?> SearchProductAsync(SearchProductDto searchProduct);

    Task BulkUpdateMerchantRelevanceScoresAsync(List<RelevanceScoreDto> relevanceScores);
    Task<List<MerchantScore>> GetMerchantScoreByAgeAndGender(byte? age, string gender);

    //Minimal endpoint for product search in events
    Task<ProductReturnEventDto?> SearchProductEventAsync(SearchProductDto searchProduct);

    Task<ProductReturnDto?> SearchProductByInternalProductIdAsync(string internalProductId,
        bool productValidate = false);

    Task UpdateLastOrderSyncAsync(Merchant merchant);
    Task UpdateMerchantProductsAsync(Merchant merchant, bool fullCatalog);
    Task<List<Merchant>> GetByIds(List<string> merchantIds);
    Task<Merchant> CreateMerchantAsync(CreateMerchantDto merchant);
    Task<Merchant> UpdateMerchantAsync(Merchant merchant);
    Task<Merchant> UpdateSimpleAsync(Merchant merchant);
    Task<List<MonitorErrorDto>> GetMerchantErrorsAsync();
    Task<List<MonitorPluginErrorDto>> GetMerchantPluginErrorsAsync();
    Task<ConcurrentBag<MonitorEvent>> GetMerchantEventsAsync(string cms);
    Task<List<User>> GetMerchantUserAsync(int merchantId);
    Task<User?> GetMerchantUserByEmailAsync(string email);
    Task<List<UserGroup>> GetMerchantUserGroupAsync();
    Task ToggleMaintenanceMode(int merchantId);
    Task<List<User>> UpdateMerchantUserAsync(List<User> users);
    Task<User> CrudMerchantUserAsync(User user);
    Task<UserToken> CreateMerchantUsersTokenAsync(User user);
    Task<User?> GetMerchantUsersByTokenAsync(string token);
    Task<Merchant?> GetProductFeedsAsync();
    Task<User?> PartnerLogin(LoginDto loginDto);
    Task<List<MerchantSimpleDto>> GetAllSimpleAsync();
    Task<List<MerchantCategory>> GetAllCategoriesAsync();
    Task<List<Merchant>> GetAllMerchantsByCategoryAsync(int merchantCategoryId);
    Task UpdateMerchantCategoryAsync(Merchant merchant);
    Task<string> ValidateImageAsync(string internalProductId);
    Task<string> ValidateImageAsync(Product product);
    Task<string> ValidateImageBySrcAsync(string src);
    Task<Product?> GetProductByProductId(long productId);
    Task<Product?> GetProductByProductUrl(string productUrl);
    Task<List<Product>> GetProductByProductIdMultiple(List<long> productIds);
    Task<Product?> GetProductByInternalProductId(string internalProductId);
    Task<Product?> GetProductByInternalProductIdWithoutMerchantReference(string internalProductId);
    Task<Dictionary<string, Product>> GetProductsByInternalProductIds(List<string> internalProductIds);
    Task UpdateMerchantUserPasswordAsync(MerchantUserPartnerPortalPasswordDto merchantUserPartnerPortalPasswordDto);
    Task<ProductRefDto?> GetInAppProduct(string internalProductId);
    Task<List<ProductRefDto>> GetInAppProductsFromIdList(List<string> internalProductIds);
    Task<bool> CheckFavorite(string internalProductId, string email);
    Task<List<string>> CheckFavoriteMultiple(List<string> internalProductId, string email);
    Task<List<Product>> GetFavorites(string email);
    Task<ProductSingleRefResponse> GetInAppProduct(string productId, string email, string debtorAccountId);
    Task<ResponseDto> UpdateFavoriteProduct(ShopFavoriteRequestDto favoriteRequest);

    Task AddOrUpdateMerchantMeta(Merchant merchant, string metaTypeName, string value);
    Task<string> FetchOrAddMerchantMeta(Merchant merchant, string metaTypeName, Func<string> valueGenerator);
    Task<bool> CheckIfMerchantIsCustomer(int merchantId);
    Task<bool> CheckIfMarketingIsAllowed(int merchantId);

    // Shopify Validation
    Task<PluginInstallInfo?> GetShopifyByDomainAsync(string myShopifyDomain);
    Task<PluginInstallInfo?> GetShopifyByEmailAsync(string email);
    Task<bool> CheckIfMyshopifyDomainIsClaimed(string myShopifyDomain, int merchantId);

    // DanDomain Classic Validation
    Task<List<PluginInstallInfo>?> GetLatestDanDomainClassicShops();
    Task<PluginInstallInfo?> GetDanDomainClassicByIdentifierAsync(long shopIdentifier);
    Task<bool> CheckIfDanDomainClassicIsClaimed(long shopIdentifier, int merchantId);
    Task Test();
    Task<bool> ActivateSync(int merchantId);
    Task<string> CuratedProductsImageValidationAndCreation(string productImage, string internalProductId);
    Task<string> CuratedProductsImageValidationAndCreation(Product product);
    Task ClearMerchantCache(int merchantId, string? apiKeyKey = null, string? shopIdentifier = null);
    Task<MerchantAsset> GenerateMerchantAsset(MerchantAssetDto merchantAssetDto);
    Task<ResponseDto> RemoveMerchantAsset(int assetId);
    Task<List<Merchant>> GetMerchantPayers();
    Task<List<Payer>> GetPayers();
    Task<string> Defragmentation();
    Task DeactivateProduct(string merchantProductId, string merchantUrl, int? merchantId = null);
    Task<List<Merchant>> HandleIsMarketingAllowed();
    
    Task<bool> IsProductDeactivatedByMerchant(long productId);


    // Merchant Dashboard
    Task CreateTermsAndConditionsAcceptEvent(int merchantId, string email, int? userId);
    
    // Calculations
    Task CalculateActualPotentialLinesAsync();

    // Development Only
    Task DeactivateAllMerchantProducts(int merchantId);
    
    Task<Dictionary<long, string>> GetAllActiveProducts(int skip = 0, int take = 1000);
    Task<string> GetMerchantName(int merchantId);
    Task<List<string>> GetInactiveProducts();
    Task<List<Merchant>> GetAllActiveCpmMerchants();
    Task<List<Merchant>> GetAllActiveBaseFeeMerchants();
    Task<MerchantDashboardSettingsDto> GetMerchantDashboardSettings(int merchantId);
    Task<MerchantDashboardSettingsDto> UpdateMerchantDashboardSettings(MerchantDashboardSettingsDto settings);
    Task UpdateMerchantMeta(int merchantId, string metaTypeName, string value);
    Task<List<int>> GetAllMerchantIdsWithAllowedMarketingByPartnerIdAsync(int partnerId);
    Task<Product?> GetProductById(long productDbId);
    Task<Product> GetProductByProductIdWithoutMerchantReference(long productId);
    Task<Dictionary<int, int>> GetProductCountByMerchantIdsAsync(List<int> allMerchantIds);
    Task<Dictionary<int, List<MerchantMetum>>> GetMerchantsWithMetadataAsync(List<int> merchantIds);

    Task<List<MerchantAsset>> GetMerchantLogosAsync();
    Task<Dictionary<string, Product>> GetProductsByProductIds(List<long> productIds);
}