using System.Net;
using System.Text.Json;
using AutoMapper;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Dto.Shop.Sections.Internal;
using Shared.Dto.Webshop;
using Shared.Models;
using Shared.Models.Merchant.Assets;
using Shared.Services.Partner;
using ShopifySharp;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;
using User = Merchant_Services.Models.ModelsDal.Merchant.User;

namespace Endpoints.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class MerchantController : ControllerBase
{
    private readonly ILogger _logger;
    private readonly IMapper _mapper;
    private readonly IMerchantService _merchantService;
    private readonly IPartnerContext _partnerContext;
    private const string Secret = "c56rovWYj0EcKIh0kR0lbHBeiUgfswT4b9PeLdGTTih7i3SEJIkdRa1pTvduQ7Fw";

    public MerchantController(ILogger logger, IMerchantService merchantService, IPartnerContext partnerContext)
    {
        var config = new MapperConfiguration(cfg =>
        {
            //Merchant
            cfg.CreateMap<Merchant, MerchantDto>().ReverseMap();
            cfg.CreateMap<MerchantPayment, MerchantPaymentDto>().ReverseMap();
            cfg.CreateMap<MerchantOrderStatus, MerchantOrderStatusDto>().ReverseMap();
            cfg.CreateMap<User, UserDto>().ReverseMap();
            cfg.CreateMap<UserGroup, UserGroupDto>().ReverseMap();
            cfg.CreateMap<MerchantMetum, MerchantMetumDto>().ReverseMap();
            cfg.CreateMap<MerchantMetaType, MerchantMetaTypeDto>().ReverseMap();
        });
        _mapper = config.CreateMapper();
        _logger = logger;
        _merchantService = merchantService;
        _partnerContext = partnerContext;
    }

    [HttpGet]
    [Route("all")]
    public async Task<IActionResult> GetAllAsync()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllAsync"))
            {
                var success = await _merchantService.GetFullAsync()
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting all merchants");
            return Ok();
        }
    }

    [HttpGet]
    [Route("all/simple")]
    public async Task<IActionResult> GetAllSimpleAsync()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllSimpleAsync"))
            {
                var success = await _merchantService.GetSimpleAsync()
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting all merchants Simple Info");
            return Ok();
        }
    }

    [HttpGet]
    [Route("merchantCategories")]
    public async Task<IActionResult> GetAllCategoriesAsync()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllCategoriesAsync"))
            {
                var success = await _merchantService.GetAllCategoriesAsync()
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting all merchants categories");
            return Ok();
        }
    }

    [HttpGet]
    [Route("full/{id}")]
    public async Task<IActionResult> GetFullMerchantByIdAsync(int id)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetFullMerchantByIdAsync"))
            {
                var success = await _merchantService.GetByIdFullAsync(id)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving full merchant");
            return Ok();
        }
    }

    [HttpGet]
    [Route("full")]
    public async Task<IActionResult> GetFullMerchantsAsync()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetFullMerchantsAsync"))
            {
                var success = await _merchantService.GetFullAsync()
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving full all merchants");
            return Ok();
        }
    }

    [HttpPost]
    public async Task<IActionResult> CreateAsync(CreateMerchantDto createMerchantDto)
    {
        try
        {
            var result = await _merchantService.CreateMerchantAsync(createMerchantDto)
                .ConfigureAwait(false);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error CreateAsync");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }


    [HttpPut]
    public async Task<IActionResult> UpdateMerchantAsync(MerchantDto merchantDto)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateMerchantAsync"))
            {
                var merchant = _mapper.Map<MerchantDto, Merchant>(merchantDto);
                var success = await _merchantService.UpdateMerchantAsync(merchant)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while UpdateMerchantAsync");
            return Ok();
        }
    }

    [HttpGet]
    [Route("toggleMaintenanceMode/{merchantId}")]
    public async Task<IActionResult> ToggleMaintenanceMode(int merchantId)
    {
        try
        {
            await _merchantService.ToggleMaintenanceMode(merchantId)
                .ConfigureAwait(false);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error GetMerchantUserGroupAsync");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [Route("merchant")]
    public async Task<IActionResult> UpdateMerchantCategoryAsync(MerchantDto merchant)
    {
        try
        {
            await _merchantService
                .UpdateMerchantCategoryAsync(_mapper.Map<MerchantDto, Merchant>(merchant))
                .ConfigureAwait(false);
            return Ok("success");
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error UpdateAsync");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("merchantError")]
    public async Task<IActionResult> GetMerchantErrors()
    {
        try
        {
            var result = await _merchantService.GetMerchantErrorsAsync()
                .ConfigureAwait(false);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error GetMerchantErrors");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("merchantPluginErrors")]
    public async Task<IActionResult> GetMerchantPluginErrors()
    {
        try
        {
            var result = await _merchantService.GetMerchantPluginErrorsAsync()
                .ConfigureAwait(false);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error GetMerchantPluginErrors");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("merchantEvents/{cms}")]
    public async Task<IActionResult> GetMerchantEvents(string cms)
    {
        try
        {
            var result = await _merchantService.GetMerchantEventsAsync(cms)
                .ConfigureAwait(false);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error GetMerchantEvents");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }


    [HttpGet]
    [Route("merchantUser/{merchantId}")]
    public async Task<IActionResult> GetMerchantUserAsync(int merchantId)
    {
        try
        {
            var result = await _merchantService.GetMerchantUserAsync(merchantId)
                .ConfigureAwait(false);
            return Ok(_mapper.Map<List<User>, List<UserDto>>(result));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error GetMerchantUserAsync");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpPut]
    [Route("merchantUser")]
    public async Task<IActionResult> UpdateAsync(List<UserDto> merchantUsersDtos)
    {
        try
        {
            var merchantUsers = _mapper.Map<List<UserDto>, List<User>>(merchantUsersDtos);
            var result = await _merchantService.UpdateMerchantUserAsync(merchantUsers)
                .ConfigureAwait(false);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error UpdateAsync");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("merchantUserGroup")]
    public async Task<IActionResult> GetMerchantUserGroupAsync()
    {
        try
        {
            var result = await _merchantService.GetMerchantUserGroupAsync()
                .ConfigureAwait(false);
            return Ok(_mapper.Map<List<UserGroup>, List<UserGroupDto>>(result));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error GetMerchantUserGroupAsync");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [Route("GenerateMerchantAsset")]
    public async Task<IActionResult> GenerateMerchantAsset(MerchantAssetDto merchantAssetDto)
    {
        try
        {
            var merchantAsset = await _merchantService.GenerateMerchantAsset(merchantAssetDto)
                .ConfigureAwait(false);
            return Ok(merchantAsset);
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error GenerateMerchantAsset");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpDelete]
    [Route("RemoveMerchantAsset/{assetId:int}")]
    public async Task<IActionResult> GenerateMerchantAsset(int assetId)
    {
        try
        {
            var response = await _merchantService.RemoveMerchantAsset(assetId)
                .ConfigureAwait(false);
            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error RemoveMerchantAsset");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    // Products 

    [HttpGet]
    [Route("product/{id}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetProductByIdAsync(string id)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetProductByIdAsync"))
            {
                var product = await _merchantService.GetProductByInternalProductIdWithoutMerchantReference(id)
                    .ConfigureAwait(false);

                if (product == null)
                {
                    return BadRequest("The Product was not found in the system");
                }

                if (product.Active == false)
                {
                    return BadRequest("The Product is currently deactivated by the Merchant");
                }

                var image = "";
                if (!string.IsNullOrEmpty(product.ProductImages))
                {
                    var images = JsonSerializer.Deserialize<List<ProductImageDto>>(product.ProductImages);
                    image = images?.FirstOrDefault()?.Src ?? "";
                }

                var productUrl = product.Permalink;

                // Check if the ProductUrl is valid
                if (!string.IsNullOrEmpty(productUrl))
                {
                    using (var httpClient = new HttpClient())
                    {
                        // Simulates a user by setting a common browser User-Agent
                        httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

                        try
                        {
                            var response = await httpClient.GetAsync(productUrl);
                            if (!response.IsSuccessStatusCode && response.StatusCode != HttpStatusCode.Found)
                            {
                                _logger.ForContext("service_name", GetType().Name)
                                    .Warning(
                                        "Product URL is invalid or product does not exist at URL: {ProductUrl}",
                                        productUrl);
                                return BadRequest("Product URL is invalid or product does not exist at URL");
                            }
                        }
                        catch (HttpRequestException httpEx)
                        {
                            _logger.ForContext("service_name", GetType().Name)
                                .Error(httpEx, $"Error while checking Product URL: {productUrl}");
                            return BadRequest($"Error while checking Product URL: {productUrl}");
                        }
                    }
                }

                var returnable = new SectionProductInternalDto()
                {
                    Id = product.Id,
                    Name = product.Name,
                    InternalProductId = product.InternalProductId,
                    ImgSrc = image,
                    ProductUrl = productUrl,
                    IsInvalidated = false,
                    InvalidationReason = null
                };

                return Ok(returnable);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error while retrieving Product with Id: {id}");
            return StatusCode(500, "Internal server error");
        }
    }


    [HttpGet]
    [AllowAnonymous]
    [PartnerAuthExempt]
    [Route("MerchantRelevanceScoresByAgeAndGender/{age}/{gender}/{partnerId:int}/{key}")]
    public async Task<IActionResult> GetMerchantRelevanceByAgeAndGender(byte? age, string gender, int partnerId, string key)
    {
        try
        {
            if (key != Secret)
                return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetMerchantRelevanceByAgeAndGender"))
            {
                if(_partnerContext.PartnerId != partnerId)
                    _partnerContext.SetPartnerId(partnerId);
                
                var merchantRelevanceScores = await _merchantService.GetMerchantScoreByAgeAndGender(age, gender)
                    .ConfigureAwait(false);
                return Ok(merchantRelevanceScores);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error while retrieving MerchantRelevanceScoresByAgeAndGender: {age} __ {gender}");
            return Ok();
        }
    }
}