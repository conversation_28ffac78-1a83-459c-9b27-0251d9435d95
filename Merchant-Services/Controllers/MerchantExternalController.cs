using AutoMapper;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Dto.Webshop;
using Shared.Models;
using Shared.Services;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace Endpoints.Controllers;

[AllowAnonymous]
[Route("[controller]")]
[ApiController]
public class MerchantExternalController : ControllerBase
{
    private readonly ILogger _logger;
    private readonly IMapper _mapper;
    private readonly IMerchantService _merchantService;

    public MerchantExternalController(ILogger logger, IMerchantService merchantService)
    {
        var config = new MapperConfiguration(cfg =>
        {
            //Merchant
            cfg.CreateMap<Merchant, MerchantDto>().ReverseMap();
            cfg.CreateMap<MerchantPayment, MerchantPaymentDto>().ReverseMap();
            cfg.CreateMap<MerchantOrderStatus, MerchantOrderStatusDto>().ReverseMap();
            cfg.CreateMap<User, UserDto>().ReverseMap();
            cfg.CreateMap<UserGroup, UserGroupDto>().ReverseMap();
            cfg.CreateMap<MerchantMetum, MerchantMetumDto>().ReverseMap();
            cfg.CreateMap<MerchantMetaType, MerchantMetaTypeDto>().ReverseMap();
        });
        _mapper = config.CreateMapper();

        _logger = logger;
        _merchantService = merchantService;
    }

    [HttpGet]
    [Route("{merchantId}")]
    public async Task<IActionResult> GetMerchantByIdAsync(int merchantId)
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetMerchantByIdAsync"))
                {
                    var success = await _merchantService.GetByIdAsync(merchantId)
                        .ConfigureAwait(false);
                    return Ok(success);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving merchant");
            return Ok();
        }
    }

    [HttpGet]
    [Route("ignoreActive/{merchantId}")]
    public async Task<IActionResult> GetMerchantIgnoreByIdAsync(int merchantId)
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetMerchantIgnoreByIdAsync"))
                {
                    var success = await _merchantService.GetByIdIgnoreActiveAsync(merchantId)
                        .ConfigureAwait(false);
                    return Ok(success);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while retrieving merchant with ignoreActive");
            return Ok();
        }
    }

    [HttpGet]
    [Route("payment/{merchantId}")]
    [PartnerAuthExempt]
    public async Task<IActionResult> GetMerchantExternalPaymentByIdAsync(int merchantId)
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetMerchantExternalPaymentByIdAsync"))
                {
                    var success = await _merchantService.GetPaymentByIdAsync(merchantId)
                        .ConfigureAwait(false);
                    return Ok(success);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving merchant");
            return Ok();
        }
    }

    [HttpGet]
    [Route("ProductByInternalProductId/{internalProductId}")]
    public async Task<IActionResult> ProductByInternalProductIdAsync(string internalProductId)
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "ProductByInternalProductIdAsync"))
                {
                    var success = await _merchantService.SearchProductByInternalProductIdAsync(internalProductId)
                        .ConfigureAwait(false);
                    return Ok(success);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while retrieving ProductByInternalProductIdAsync");
            return Ok();
        }
    }

    [HttpGet]
    [Route("ProductByInternalProductIdImageValidate/{internalProductId}")]
    public async Task<IActionResult> ProductByInternalProductIdImageValidateAsync(string internalProductId)
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "ProductByInternalProductIdImageValidate"))
                {
                    var success = await _merchantService.SearchProductByInternalProductIdAsync(internalProductId, true)
                        .ConfigureAwait(false);
                    if (success != null)
                    {
                        return Ok(success);
                    }

                    return Ok();
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while retrieving ProductByInternalProductIdImageValidate");
            return Ok();
        }
    }

    [HttpGet]
    [Route("validateImage/{internalProductId}")]
    public async Task<IActionResult> ValidateImageAsync(string internalProductId)
    {
        try
        {
            var result = await _merchantService.ValidateImageAsync(internalProductId).ConfigureAwait(false);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error ValidateImageAsync");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("all/names")]
    public async Task<IActionResult> GetAllNamesAsync()
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetAllSimpleAsync"))
                {
                    var success = await _merchantService.GetAllNamesAsync()
                        .ConfigureAwait(false);
                    return Ok(success);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting all merchant names");
            return Ok();
        }
    }
}