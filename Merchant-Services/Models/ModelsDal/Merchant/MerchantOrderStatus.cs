using System;
using System.Collections.Generic;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class MerchantOrderStatus
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public string Status { get; set; } = null!;

    public bool Denied { get; set; }

    public int FkMerchantId { get; set; }

    public virtual Merchant FkMerchant { get; set; } = null!;
}
