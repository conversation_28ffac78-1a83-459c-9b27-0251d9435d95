using System;
using System.Collections.Generic;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class MerchantUsersRel
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public int FkUserId { get; set; }

    public int FkMerchantId { get; set; }

    public int FkUserGroupId { get; set; }

    public virtual Merchant FkMerchant { get; set; } = null!;

    public virtual User FkUser { get; set; } = null!;

    public virtual UserGroup FkUserGroup { get; set; } = null!;
}
