using System;
using System.Collections.Generic;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class TermsAndConditionsAcceptance
{
    public int Id { get; set; }

    public bool Active { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public int FkMerchantId { get; set; }

    public int? FkUserId { get; set; }

    public string Email { get; set; } = null!;

    public DateTime AcceptedDate { get; set; }

    public string Version { get; set; } = null!;

    public virtual Merchant FkMerchant { get; set; } = null!;

    public virtual User? FkUser { get; set; }
}
