using System;
using System.Collections.Generic;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class MerchantPayment
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public bool RedirectInvoice { get; set; }

    public bool DisplayInvoice { get; set; }

    public bool InteractInvoice { get; set; }

    public decimal RedirectPaymentPercentage { get; set; }

    public decimal DisplayPaymentPercentage { get; set; }

    public decimal InteractPaymentPercentage { get; set; }

    public int RedirectExposureDays { get; set; }

    public int DisplayExposureDays { get; set; }

    public int InteractExposureDays { get; set; }

    public int FkMerchantId { get; set; }

    public virtual Merchant FkMerchant { get; set; } = null!;
}
