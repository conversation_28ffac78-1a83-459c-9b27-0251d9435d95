using System;
using System.Collections.Generic;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class UserGroup
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public string Name { get; set; } = null!;

    public virtual ICollection<MerchantUsersRel> MerchantUsersRels { get; set; } = new List<MerchantUsersRel>();
}
