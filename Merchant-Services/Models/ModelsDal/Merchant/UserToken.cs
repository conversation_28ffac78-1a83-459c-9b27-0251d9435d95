using System;
using System.Collections.Generic;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class UserToken
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public bool Active { get; set; }

    public string Token { get; set; } = null!;

    public DateTime Expire { get; set; }

    public int FkUserId { get; set; }

    public virtual User FkUser { get; set; } = null!;
}
