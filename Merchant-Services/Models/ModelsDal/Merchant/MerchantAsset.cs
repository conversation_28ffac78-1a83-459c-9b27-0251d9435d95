using System;
using System.Collections.Generic;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class MerchantAsset
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public string Src { get; set; } = null!;

    public DateOnly? OverrideExpireDate { get; set; }

    public int FkMerchantId { get; set; }

    public int FkMerchantAssetTypeId { get; set; }

    public virtual Merchant FkMerchant { get; set; } = null!;

    public virtual MerchantAssetType FkMerchantAssetType { get; set; } = null!;
}
