using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class MerchantDbContext : DbContext
{
    public MerchantDbContext(DbContextOptions<MerchantDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Merchant> Merchants { get; set; }

    public virtual DbSet<MerchantAsset> MerchantAssets { get; set; }

    public virtual DbSet<MerchantAssetType> MerchantAssetTypes { get; set; }

    public virtual DbSet<MerchantCategory> MerchantCategories { get; set; }

    public virtual DbSet<MerchantCategoryRel> MerchantCategoryRels { get; set; }

    public virtual DbSet<MerchantMetaType> MerchantMetaTypes { get; set; }

    public virtual DbSet<MerchantMetum> MerchantMeta { get; set; }

    public virtual DbSet<MerchantOrderStatus> MerchantOrderStatuses { get; set; }

    public virtual DbSet<MerchantPayer> MerchantPayers { get; set; }

    public virtual DbSet<MerchantPayment> MerchantPayments { get; set; }

    public virtual DbSet<MerchantProductFeed> MerchantProductFeeds { get; set; }

    public virtual DbSet<MerchantUsersRel> MerchantUsersRels { get; set; }

    public virtual DbSet<Payer> Payers { get; set; }

    public virtual DbSet<PluginInstallInfo> PluginInstallInfos { get; set; }

    public virtual DbSet<Product> Products { get; set; }

    public virtual DbSet<ProductFavorite> ProductFavorites { get; set; }

    public virtual DbSet<ProductsStaging> ProductsStagings { get; set; }

    public virtual DbSet<RelevanceScore> RelevanceScores { get; set; }

    public virtual DbSet<TermsAndConditionsAcceptance> TermsAndConditionsAcceptances { get; set; }

    public virtual DbSet<User> Users { get; set; }

    public virtual DbSet<UserGroup> UserGroups { get; set; }

    public virtual DbSet<UserToken> UserTokens { get; set; }

    public virtual DbSet<Variant> Variants { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Merchant>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Merchant__3214EC07407DB226");

            entity.ToTable("Merchants", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.DisplayName).HasMaxLength(255);
            entity.Property(e => e.FkPartnerId).HasColumnName("FK_PartnerId");
            entity.Property(e => e.LastAttemptedSync).HasPrecision(0);
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastOrderSync).HasPrecision(0);
            entity.Property(e => e.LastProductSync).HasPrecision(0);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Type).HasMaxLength(255);
            entity.Property(e => e.Url).HasMaxLength(500);
        });

        modelBuilder.Entity<MerchantAsset>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Merchant__3214EC07FF790C13");

            entity.ToTable("MerchantAssets", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FkMerchantAssetTypeId).HasColumnName("FK_MerchantAssetTypeId");
            entity.Property(e => e.FkMerchantId).HasColumnName("FK_MerchantId");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Src).HasMaxLength(500);

            entity.HasOne(d => d.FkMerchantAssetType).WithMany(p => p.MerchantAssets)
                .HasForeignKey(d => d.FkMerchantAssetTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantA__FK_Me__25518C17");

            entity.HasOne(d => d.FkMerchant).WithMany(p => p.MerchantAssets)
                .HasForeignKey(d => d.FkMerchantId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantA__FK_Me__245D67DE");
        });

        modelBuilder.Entity<MerchantAssetType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Merchant__3214EC07B49673BF");

            entity.ToTable("MerchantAssetTypes", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Name).HasMaxLength(255);
        });

        modelBuilder.Entity<MerchantCategory>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Merchant__3214EC07FA4A2608");

            entity.ToTable("MerchantCategories", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FkParentId).HasColumnName("FK_ParentId");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Name).HasMaxLength(2500);

            entity.HasOne(d => d.FkParent).WithMany(p => p.InverseFkParent)
                .HasForeignKey(d => d.FkParentId)
                .HasConstraintName("FK__MerchantC__FK_Pa__1332DBDC");
        });

        modelBuilder.Entity<MerchantCategoryRel>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Merchant__3214EC07A3C06DDE");

            entity.ToTable("MerchantCategoryRel", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FkMerchantCategoryId).HasColumnName("FK_MerchantCategoryId");
            entity.Property(e => e.FkMerchantId).HasColumnName("FK_MerchantId");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkMerchantCategory).WithMany(p => p.MerchantCategoryRels)
                .HasForeignKey(d => d.FkMerchantCategoryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantC__FK_Me__19DFD96B");

            entity.HasOne(d => d.FkMerchant).WithMany(p => p.MerchantCategoryRels)
                .HasForeignKey(d => d.FkMerchantId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantC__FK_Me__18EBB532");
        });

        modelBuilder.Entity<MerchantMetaType>(entity =>
        {
            entity.HasKey(e => e.Name).HasName("PK__Merchant__737584F79B58D22C");

            entity.ToTable("MerchantMetaTypes", "merchant");

            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<MerchantMetum>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Merchant__3214EC071415269C");

            entity.ToTable("MerchantMeta", "merchant");

            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.EndDate).HasPrecision(0);
            entity.Property(e => e.FkMerchantId).HasColumnName("FK_MerchantId");
            entity.Property(e => e.FkMerchantMetaTypeName)
                .HasMaxLength(255)
                .HasColumnName("FK_MerchantMetaTypeName");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.StartDate).HasPrecision(0);
            entity.Property(e => e.Value).HasMaxLength(2000);

            entity.HasOne(d => d.FkMerchant).WithMany(p => p.MerchantMeta)
                .HasForeignKey(d => d.FkMerchantId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantM__FK_Me__2DE6D218");

            entity.HasOne(d => d.FkMerchantMetaTypeNameNavigation).WithMany(p => p.MerchantMeta)
                .HasForeignKey(d => d.FkMerchantMetaTypeName)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantM__FK_Me__2EDAF651");
        });

        modelBuilder.Entity<MerchantOrderStatus>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Merchant__3214EC0723BA82B5");

            entity.ToTable("MerchantOrderStatuses", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FkMerchantId).HasColumnName("FK_MerchantId");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Status).HasMaxLength(200);

            entity.HasOne(d => d.FkMerchant).WithMany(p => p.MerchantOrderStatuses)
                .HasForeignKey(d => d.FkMerchantId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantO__FK_Me__3493CFA7");
        });

        modelBuilder.Entity<MerchantPayer>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Merchant__3214EC0756B6F30C");

            entity.ToTable("MerchantPayers", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.FkMerchantId).HasColumnName("FK_MerchantId");
            entity.Property(e => e.FkPayerId).HasColumnName("FK_PayerId");

            entity.HasOne(d => d.FkMerchant).WithMany(p => p.MerchantPayers)
                .HasForeignKey(d => d.FkMerchantId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantP__FK_Me__04459E07");

            entity.HasOne(d => d.FkPayer).WithMany(p => p.MerchantPayers)
                .HasForeignKey(d => d.FkPayerId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantP__FK_Pa__035179CE");
        });

        modelBuilder.Entity<MerchantPayment>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Merchant__3214EC074F6785B3");

            entity.ToTable("MerchantPayments", "merchant");

            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.DisplayPaymentPercentage).HasColumnType("decimal(5, 2)");
            entity.Property(e => e.FkMerchantId).HasColumnName("FK_MerchantId");
            entity.Property(e => e.InteractPaymentPercentage).HasColumnType("decimal(5, 2)");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.RedirectPaymentPercentage).HasColumnType("decimal(5, 2)");

            entity.HasOne(d => d.FkMerchant).WithMany(p => p.MerchantPayments)
                .HasForeignKey(d => d.FkMerchantId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantP__FK_Me__395884C4");
        });

        modelBuilder.Entity<MerchantProductFeed>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Merchant__3214EC0707C24FF1");

            entity.ToTable("MerchantProductFeeds", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.Categories).HasMaxLength(100);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Delimiter).HasMaxLength(50);
            entity.Property(e => e.Description).HasMaxLength(100);
            entity.Property(e => e.FkMerchantId).HasColumnName("FK_MerchantId");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.PermaLink).HasMaxLength(100);
            entity.Property(e => e.PictureLink).HasMaxLength(100);
            entity.Property(e => e.Price).HasMaxLength(100);
            entity.Property(e => e.RegularPrice).HasMaxLength(100);
            entity.Property(e => e.Sku).HasMaxLength(100);
            entity.Property(e => e.StockStatus).HasMaxLength(100);
            entity.Property(e => e.Type).HasMaxLength(50);
            entity.Property(e => e.Url).HasMaxLength(500);

            entity.HasOne(d => d.FkMerchant).WithMany(p => p.MerchantProductFeeds)
                .HasForeignKey(d => d.FkMerchantId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantP__FK_Me__3F115E1A");
        });

        modelBuilder.Entity<MerchantUsersRel>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Merchant__3214EC07348EC80A");

            entity.ToTable("MerchantUsersRel", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FkMerchantId).HasColumnName("FK_MerchantId");
            entity.Property(e => e.FkUserGroupId).HasColumnName("FK_UserGroupId");
            entity.Property(e => e.FkUserId).HasColumnName("FK_UserId");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkMerchant).WithMany(p => p.MerchantUsersRels)
                .HasForeignKey(d => d.FkMerchantId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantU__FK_Me__02FC7413");

            entity.HasOne(d => d.FkUserGroup).WithMany(p => p.MerchantUsersRels)
                .HasForeignKey(d => d.FkUserGroupId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantU__FK_Us__03F0984C");

            entity.HasOne(d => d.FkUser).WithMany(p => p.MerchantUsersRels)
                .HasForeignKey(d => d.FkUserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantU__FK_Us__02084FDA");
        });

        modelBuilder.Entity<Payer>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Payer__3214EC0700C3A2CC");

            entity.ToTable("Payers", "merchant");

            entity.Property(e => e.InvoiceType).HasMaxLength(50);
            entity.Property(e => e.ProviderCustomerId).HasMaxLength(100);
        });

        modelBuilder.Entity<PluginInstallInfo>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__PluginIn__3214EC07B91FD958");

            entity.ToTable("PluginInstallInfos", "merchant");

            entity.Property(e => e.ApiKey).HasMaxLength(255);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.ShopEmail).HasMaxLength(255);
            entity.Property(e => e.ShopIdentifier).HasMaxLength(255);
            entity.Property(e => e.Type).HasMaxLength(255);
            entity.Property(e => e.Url).HasMaxLength(255);
        });

        modelBuilder.Entity<Product>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Products__3214EC07CAA1EA27");

            entity.ToTable("Products", "merchant");

            entity.HasIndex(e => e.ElasticSyncDate, "IX_ElasticSyncDate");

            entity.HasIndex(e => new { e.FkMerchantId, e.InternalProductId }, "IX_FK_MerchantId,InternalProductId");

            entity.HasIndex(e => new { e.FkMerchantId, e.MerchantProductId }, "IX_FK_MerchantId,MerchantProductId");

            entity.HasIndex(e => new { e.FkMerchantId, e.Sku }, "IX_FK_MerchantId,Sku");

            entity.HasIndex(e => e.InternalProductId, "nci_msft_1_Products_B7D2D2B8B1C19B68332DD662D2E5FB5A");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.Categories).HasMaxLength(2500);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CuratedImage).HasMaxLength(500);
            entity.Property(e => e.ElasticSyncDate)
                .HasPrecision(2)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FkMerchantId).HasColumnName("FK_MerchantId");
            entity.Property(e => e.InternalProductId)
                .HasMaxLength(36)
                .HasDefaultValueSql("(lower(newid()))");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.MerchantCreatedDate).HasPrecision(0);
            entity.Property(e => e.MerchantModifiedDate).HasPrecision(0);
            entity.Property(e => e.MerchantProductId).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(1000);
            entity.Property(e => e.Permalink).HasMaxLength(2048);
            entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.ProductImages).HasMaxLength(2500);
            entity.Property(e => e.RegularPrice).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.Sku).HasMaxLength(800);
            entity.Property(e => e.Status).HasMaxLength(50);

            entity.HasOne(d => d.FkMerchant).WithMany(p => p.Products)
                .HasForeignKey(d => d.FkMerchantId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Products__FK_Mer__59E54FE7");
        });

        modelBuilder.Entity<ProductFavorite>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__ProductF__3214EC076E3007C4");

            entity.ToTable("ProductFavorites", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Email).HasMaxLength(255);
            entity.Property(e => e.FkProductId).HasColumnName("FK_ProductId");
            entity.Property(e => e.InternalProductId).HasMaxLength(36);
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkProduct).WithMany(p => p.ProductFavorites)
                .HasForeignKey(d => d.FkProductId)
                .HasConstraintName("FK__ProductFa__FK_Pr__361203C5");
        });

        modelBuilder.Entity<ProductsStaging>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Products_Staging__3214EC07CAA1EA27");

            entity.ToTable("Products_Staging", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.Categories).HasMaxLength(2500);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.CuratedImage).HasMaxLength(500);
            entity.Property(e => e.ElasticSyncDate)
                .HasPrecision(2)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FkMerchantId).HasColumnName("FK_MerchantId");
            entity.Property(e => e.InternalProductId)
                .HasMaxLength(36)
                .HasDefaultValueSql("(lower(newid()))");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.MerchantCreatedDate).HasPrecision(0);
            entity.Property(e => e.MerchantModifiedDate).HasPrecision(0);
            entity.Property(e => e.MerchantProductId).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(1000);
            entity.Property(e => e.Permalink).HasMaxLength(2048);
            entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.ProductImages).HasMaxLength(2500);
            entity.Property(e => e.RegularPrice).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.Sku).HasMaxLength(200);
            entity.Property(e => e.Status).HasMaxLength(50);

            entity.HasOne(d => d.FkMerchant).WithMany(p => p.ProductsStagings)
                .HasForeignKey(d => d.FkMerchantId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Products_Staging__FK_Mer__59E54FE7");
        });

        modelBuilder.Entity<RelevanceScore>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Relevanc__3214EC07F7B32984");

            entity.ToTable("RelevanceScores", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FkMerchantId).HasColumnName("FK_MerchantId");
            entity.Property(e => e.Gender).HasMaxLength(10);
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.ScoreSum).HasColumnType("decimal(19, 4)");
            entity.Property(e => e.ScoreType).HasMaxLength(20);

            entity.HasOne(d => d.FkMerchant).WithMany(p => p.RelevanceScores)
                .HasForeignKey(d => d.FkMerchantId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Relevance__FK_Me__0C3BC58A");
        });

        modelBuilder.Entity<TermsAndConditionsAcceptance>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__TermsAnd__3214EC07831DC569");

            entity.ToTable("TermsAndConditionsAcceptance", "merchant");

            entity.Property(e => e.AcceptedDate).HasDefaultValueSql("(sysdatetime())");
            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(sysdatetime())");
            entity.Property(e => e.Email).HasMaxLength(255);
            entity.Property(e => e.FkMerchantId).HasColumnName("FK_MerchantId");
            entity.Property(e => e.FkUserId).HasColumnName("FK_UserId");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(sysdatetime())");
            entity.Property(e => e.Version)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.HasOne(d => d.FkMerchant).WithMany(p => p.TermsAndConditionsAcceptances)
                .HasForeignKey(d => d.FkMerchantId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MerchantId");

            entity.HasOne(d => d.FkUser).WithMany(p => p.TermsAndConditionsAcceptances)
                .HasForeignKey(d => d.FkUserId)
                .HasConstraintName("FK_UserId");
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Users__3214EC076A93B651");

            entity.ToTable("Users", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Email).HasMaxLength(255);
            entity.Property(e => e.FirstName).HasMaxLength(100);
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastName).HasMaxLength(100);
            entity.Property(e => e.Password).HasMaxLength(200);
            entity.Property(e => e.Password2).HasMaxLength(200);
        });

        modelBuilder.Entity<UserGroup>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__UserGrou__3214EC07AB1268F4");

            entity.ToTable("UserGroups", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Name).HasMaxLength(255);
        });

        modelBuilder.Entity<UserToken>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__UserToke__3214EC0765BAFE2E");

            entity.ToTable("UserTokens", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Expire).HasPrecision(0);
            entity.Property(e => e.FkUserId).HasColumnName("FK_UserId");
            entity.Property(e => e.Token).HasMaxLength(1000);

            entity.HasOne(d => d.FkUser).WithMany(p => p.UserTokens)
                .HasForeignKey(d => d.FkUserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserToken__FK_Us__7C4F7684");
        });

        modelBuilder.Entity<Variant>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Variants__3214EC076A6FEAB8");

            entity.ToTable("Variants", "merchant");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FkProductId).HasColumnName("FK_ProductId");
            entity.Property(e => e.InternalVariantId)
                .HasMaxLength(36)
                .HasDefaultValueSql("(lower(newid()))");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.MerchantCreatedDate).HasPrecision(0);
            entity.Property(e => e.MerchantModifiedDate).HasPrecision(0);
            entity.Property(e => e.MerchantProductId).HasMaxLength(255);
            entity.Property(e => e.MerchantVariantId).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(1000);
            entity.Property(e => e.Permalink).HasMaxLength(400);
            entity.Property(e => e.Price).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.ProductImages).HasMaxLength(2500);
            entity.Property(e => e.RegularPrice).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.Sku).HasMaxLength(200);
            entity.Property(e => e.Status).HasMaxLength(50);

            entity.HasOne(d => d.FkProduct).WithMany(p => p.Variants)
                .HasForeignKey(d => d.FkProductId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Variants__FK_Pro__30592A6F");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
