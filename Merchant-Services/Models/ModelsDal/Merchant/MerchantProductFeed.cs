using System;
using System.Collections.Generic;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class MerchantProductFeed
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public string Url { get; set; } = null!;

    public string Name { get; set; } = null!;

    public string? Sku { get; set; }

    public string? StockStatus { get; set; }

    public string? PermaLink { get; set; }

    public string? PictureLink { get; set; }

    public string? Price { get; set; }

    public string? RegularPrice { get; set; }

    public string? Categories { get; set; }

    public string Type { get; set; } = null!;

    public string Delimiter { get; set; } = null!;

    public int FkMerchantId { get; set; }

    public string? Description { get; set; }

    public virtual Merchant FkMerchant { get; set; } = null!;
}
