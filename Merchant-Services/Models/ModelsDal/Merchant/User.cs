using System;
using System.Collections.Generic;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class User
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public string Email { get; set; } = null!;

    public string Password { get; set; } = null!;

    public string? Password2 { get; set; }

    public string? FirstName { get; set; }

    public string? LastName { get; set; }

    public virtual ICollection<MerchantUsersRel> MerchantUsersRels { get; set; } = new List<MerchantUsersRel>();

    public virtual ICollection<TermsAndConditionsAcceptance> TermsAndConditionsAcceptances { get; set; } = new List<TermsAndConditionsAcceptance>();

    public virtual ICollection<UserToken> UserTokens { get; set; } = new List<UserToken>();
}
