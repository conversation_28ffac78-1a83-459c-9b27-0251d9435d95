using System;
using System.Collections.Generic;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class Product
{
    public long Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public string MerchantProductId { get; set; } = null!;

    public DateTime? MerchantCreatedDate { get; set; }

    public DateTime? MerchantModifiedDate { get; set; }

    public string Name { get; set; } = null!;

    public string? Sku { get; set; }

    public string Permalink { get; set; } = null!;

    public string? Status { get; set; }

    public string? Description { get; set; }

    public string? ShortDescription { get; set; }

    public decimal? Price { get; set; }

    public decimal? RegularPrice { get; set; }

    public int? StockQuantity { get; set; }

    public string InternalProductId { get; set; } = null!;

    public string? ProductImages { get; set; }

    public string? Categories { get; set; }

    public string? ImageEmbeddings { get; set; }

    public string? TextEmbeddings { get; set; }

    public string? CuratedImage { get; set; }

    public int FkMerchantId { get; set; }

    public DateTime ElasticSyncDate { get; set; }

    public string? FormattedDescription { get; set; }

    public bool? IsInStock { get; set; }

    public virtual Merchant FkMerchant { get; set; } = null!;

    public virtual ICollection<ProductFavorite> ProductFavorites { get; set; } = new List<ProductFavorite>();

    public virtual ICollection<Variant> Variants { get; set; } = new List<Variant>();
}
