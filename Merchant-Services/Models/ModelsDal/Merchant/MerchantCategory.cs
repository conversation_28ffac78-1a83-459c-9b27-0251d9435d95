using System;
using System.Collections.Generic;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class MerchantCategory
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public string Name { get; set; } = null!;

    public int SortOrder { get; set; }

    public int? FkParentId { get; set; }

    public virtual MerchantCategory? FkParent { get; set; }

    public virtual ICollection<MerchantCategory> InverseFkParent { get; set; } = new List<MerchantCategory>();

    public virtual ICollection<MerchantCategoryRel> MerchantCategoryRels { get; set; } = new List<MerchantCategoryRel>();
}
