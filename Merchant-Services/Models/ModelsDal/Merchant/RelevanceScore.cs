using System;
using System.Collections.Generic;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class RelevanceScore
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public string Gender { get; set; } = null!;

    public byte? Age { get; set; }

    public byte? AgeInterval { get; set; }

    public int FkMerchantId { get; set; }

    public decimal ScoreSum { get; set; }

    public string ScoreType { get; set; } = null!;

    public virtual Merchant FkMerchant { get; set; } = null!;
}
