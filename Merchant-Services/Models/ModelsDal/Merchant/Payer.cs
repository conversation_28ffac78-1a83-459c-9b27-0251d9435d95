using System;
using System.Collections.Generic;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class Payer
{
    public int Id { get; set; }

    public string? ProviderCustomerId { get; set; }

    public string InvoiceType { get; set; } = null!;

    public bool PaymentActivated { get; set; }

    public virtual ICollection<MerchantPayer> MerchantPayers { get; set; } = new List<MerchantPayer>();
}
