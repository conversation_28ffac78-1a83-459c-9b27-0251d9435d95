using System;
using System.Collections.Generic;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class MerchantMetum
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public string Value { get; set; } = null!;

    public int FkMerchantId { get; set; }

    public string FkMerchantMetaTypeName { get; set; } = null!;

    public DateTime? StartDate { get; set; }

    public DateTime? EndDate { get; set; }

    public virtual Merchant FkMerchant { get; set; } = null!;

    public virtual MerchantMetaType FkMerchantMetaTypeNameNavigation { get; set; } = null!;
}
