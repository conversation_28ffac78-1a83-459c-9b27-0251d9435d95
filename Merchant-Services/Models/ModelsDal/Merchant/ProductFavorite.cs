using System;
using System.Collections.Generic;

namespace Merchant_Services.Models.ModelsDal.Merchant;

public partial class ProductFavorite
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public string Email { get; set; } = null!;

    public string InternalProductId { get; set; } = null!;

    public long? FkProductId { get; set; }

    public virtual Product? FkProduct { get; set; }
}
