namespace Webshops_Services.Models;

public class MonitorErrorDto
{
    public MonitorErrorDto(string merchantName, int merchantId, string cms, string url)
    {
        MerchantName = merchantName;
        MerchantId = merchantId;
        Cms = cms;
        Url = url;
    }

    public string MerchantName { get; set; }
    public int MerchantId { get; set; }
    public string Cms { get; set; }
    public string Url { get; set; }

    public MonitorErrorObjectDto Product { get; set; }
    public MonitorErrorObjectDto Order { get; set; }
    public MonitorErrorObjectDto PageLook { get; set; }
    public MonitorErrorObjectDto AddCart { get; set; }
    public MonitorErrorObjectDto RemoveCart { get; set; }
    public MonitorErrorObjectDto AddWishList { get; set; }
    public MonitorErrorObjectDto RemoveWishList { get; set; }
}