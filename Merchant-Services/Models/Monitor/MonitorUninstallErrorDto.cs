using Merchant_Services.Models.ModelsDal.Merchant;

namespace Webshops_Services.Models;

public class MonitorPluginErrorDto
{
    public MonitorPluginErrorDto(string merchantName, int merchantId, string cms, string url, Merchant merchant)
    {
        MerchantName = merchantName;
        MerchantId = merchantId;
        Cms = cms;
        Url = url;
        Merchant = merchant;
    }

    // TO Be Removed when the new Platform is live
    public string MerchantName { get; set; }
    // TO Be Removed when the new Platform is live
    public int MerchantId { get; set; }
    public string Cms { get; set; }
    public string Url { get; set; }
    // TO Be Removed when the new Platform is live
    public string Error { get; set; }
    public string ErrorName { get; set; }
    public string ErrorDescription { get; set; }
    public string ErrorSolution { get; set; }
    public string Status { get; set; }
    public Merchant Merchant { get; set; }
    public MonitorErrorObjectDto Product { get; set; }
    public MonitorErrorObjectDto Order { get; set; }
}