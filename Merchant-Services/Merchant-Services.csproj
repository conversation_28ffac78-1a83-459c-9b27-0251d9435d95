<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <PackageId>Merchant-Services</PackageId>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Message-Services\Message-Services.csproj" />
        <ProjectReference Include="..\Shared\Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Models\ModelsDal\" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
        <PackageReference Include="EFCore.BulkExtensions.SqlServer" Version="8.0.4" />
        <PackageReference Include="Elastic.Clients.Elasticsearch" Version="8.14.8" />
    </ItemGroup>

</Project>