using System;
using System.Collections.Generic;

namespace Automation_Services.Models.Message;

public class MessageDto
{
    public MessageDto()
    {
        MessageProducts = new List<MessageProductDto>();
    }

    public string Origin { get; set; }

    public int Priority { get; set; }

    public Guid EmailGuid { get; set; }

    public int? CampaignId { get; set; }

    public int? CampaignGroupId { get; set; }

    public string Email { get; set; }

    public string? AutomationId { get; set; }

    public string? AutomationTrigger { get; set; }

    public bool IsOverride { get; set; }

    public bool IsTest { get; set; }

    public DateTime ActionDate { get; set; }

    public List<MessageProductDto> MessageProducts { get; set; }

    public ExperimentDto Experiment { get; set; }
}