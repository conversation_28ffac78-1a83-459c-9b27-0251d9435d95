using Automation_Service.Models.ModelsDal.Automation;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Shared;
using IConnection = RabbitMQ.Client.IConnection;

namespace Automation_Services.Models.ModelsDal.Automation;

public class AutomationDbContextTracking(
    DbContextOptions<AutomationDbContext> options,
    IHttpContextAccessor httpContextAccessor,
    //IConnection rabbitConnection
    [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud)
    : AutomationDbContext(options)
{
    public virtual async Task<int> SaveChangesAsync()
    {
        OnBeforeSaveChanges();
        var result = await base.SaveChangesAsync();
        return result;
    }

    public virtual int SaveChanges()
    {
        OnBeforeSaveChanges();
        var result = base.SaveChanges();
        return result;
    }

    private void OnBeforeSaveChanges()
    {
        AuditSaveDb.OnBeforeSaveChanges(ChangeTracker, httpContextAccessor, rabbitConnectionCloud);
    }
}