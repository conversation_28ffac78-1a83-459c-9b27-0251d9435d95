using System;
using System.Collections.Generic;

namespace Automation_Service.Models.ModelsDal.Automation;

public partial class CuratedProductBatchEmail
{
    public int Id { get; set; }

    public int FkCuratedProductBatchId { get; set; }

    public string Email { get; set; } = null!;

    public DateTime CreatedDate { get; set; }

    public DateTime? DoneDate { get; set; }

    public virtual CuratedProductBatch FkCuratedProductBatch { get; set; } = null!;
}