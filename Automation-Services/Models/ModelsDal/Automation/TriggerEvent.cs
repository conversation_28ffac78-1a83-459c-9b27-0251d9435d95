using System;
using System.Collections.Generic;

namespace Automation_Service.Models.ModelsDal.Automation;

public partial class TriggerEvent
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string Description { get; set; } = null!;

    public virtual ICollection<AutomationTriggerEvent> AutomationTriggerEvents { get; set; } =
        new List<AutomationTriggerEvent>();
}