using System;
using System.Collections.Generic;

namespace Automation_Service.Models.ModelsDal.Automation;

public partial class AutomationTriggerEvent
{
    public int Id { get; set; }

    public int FkAutomationId { get; set; }

    public int FkTriggerEventId { get; set; }

    public DateTime CreatedDate { get; set; }

    public virtual Automation FkAutomation { get; set; } = null!;

    public virtual TriggerEvent FkTriggerEvent { get; set; } = null!;
}