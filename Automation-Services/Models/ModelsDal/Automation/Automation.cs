using System;
using System.Collections.Generic;

namespace Automation_Service.Models.ModelsDal.Automation;

public partial class Automation
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string Description { get; set; } = null!;

    public int? CampaignId { get; set; }

    public bool? Active { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public int? CampaignGroupId { get; set; }

    public byte? StartTime { get; set; }

    public byte? EndTime { get; set; }

    public int HoursAfterEvent { get; set; }

    public virtual ICollection<AutomationTriggerEvent> AutomationTriggerEvents { get; set; } =
        new List<AutomationTriggerEvent>();
}