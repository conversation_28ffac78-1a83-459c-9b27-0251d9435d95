using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace Automation_Service.Models.ModelsDal.Automation;

public partial class AutomationDbContext : DbContext
{
    public AutomationDbContext(DbContextOptions<AutomationDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Automation> Automations { get; set; }

    public virtual DbSet<AutomationEmail> AutomationEmails { get; set; }

    public virtual DbSet<AutomationTriggerEvent> AutomationTriggerEvents { get; set; }

    public virtual DbSet<CuratedProductBatch> CuratedProductBatches { get; set; }

    public virtual DbSet<CuratedProductBatchEmail> CuratedProductBatchEmails { get; set; }

    public virtual DbSet<LastRunTime> LastRunTimes { get; set; }

    public virtual DbSet<MlAlgorithm> MlAlgorithms { get; set; }

    public virtual DbSet<MlRelevantProductCount> MlRelevantProductCounts { get; set; }

    public virtual DbSet<Setting> Settings { get; set; }

    public virtual DbSet<SettingsProductScoreDecay> SettingsProductScoreDecays { get; set; }

    public virtual DbSet<TriggerEvent> TriggerEvents { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.UseCollation("Danish_Norwegian_CI_AS");

        modelBuilder.Entity<Automation>(entity =>
        {
            entity.Property(e => e.Active)
                .IsRequired()
                .HasDefaultValueSql("((1))");
            entity.Property(e => e.CreatedDate)
                .HasPrecision(2)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(2)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Name)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<AutomationEmail>(entity =>
        {
            entity.HasKey(e => e.EmailId);

            entity.HasIndex(e => e.EmailAddress, "IX_EmailAddress");

            entity.Property(e => e.EmailId).ValueGeneratedNever();
            entity.Property(e => e.CreatedDate)
                .HasPrecision(2)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.EmailAddress).HasMaxLength(254);
            entity.Property(e => e.Type)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<AutomationTriggerEvent>(entity =>
        {
            entity.Property(e => e.CreatedDate)
                .HasPrecision(2)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FkAutomationId).HasColumnName("FK_AutomationId");
            entity.Property(e => e.FkTriggerEventId).HasColumnName("FK_TriggerEventId");

            entity.HasOne(d => d.FkAutomation).WithMany(p => p.AutomationTriggerEvents)
                .HasForeignKey(d => d.FkAutomationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AutomationTriggerEvents_Automations");

            entity.HasOne(d => d.FkTriggerEvent).WithMany(p => p.AutomationTriggerEvents)
                .HasForeignKey(d => d.FkTriggerEventId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AutomationTriggerEvents_TriggerEvents");
        });

        modelBuilder.Entity<CuratedProductBatch>(entity =>
        {
            entity.Property(e => e.CreatedDate)
                .HasPrecision(2)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Description).HasMaxLength(250);
            entity.Property(e => e.DoneDate).HasPrecision(2);
            entity.Property(e => e.RunDate).HasPrecision(2);
        });

        modelBuilder.Entity<CuratedProductBatchEmail>(entity =>
        {
            entity.HasIndex(e => new {e.FkCuratedProductBatchId, e.Email}, "IX_FK_CuratedProductBatchId_Email");

            entity.Property(e => e.CreatedDate)
                .HasPrecision(2)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.DoneDate).HasPrecision(2);
            entity.Property(e => e.Email).HasMaxLength(255);
            entity.Property(e => e.FkCuratedProductBatchId).HasColumnName("FK_CuratedProductBatchId");

            entity.HasOne(d => d.FkCuratedProductBatch).WithMany(p => p.CuratedProductBatchEmails)
                .HasForeignKey(d => d.FkCuratedProductBatchId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CuratedProductBatchEmails_CuratedProductBatches");
        });

        modelBuilder.Entity<LastRunTime>(entity =>
        {
            entity.HasKey(e => e.Name);

            entity.ToTable("LastRunTime");

            entity.Property(e => e.Name)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Time).HasPrecision(2);
        });

        modelBuilder.Entity<MlAlgorithm>(entity =>
        {
            entity.Property(e => e.Name)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<MlRelevantProductCount>(entity =>
        {
            entity.HasIndex(e => e.FkMlAlgorithmId, "IX_FK_MlAlgorithmId");

            entity.Property(e => e.FkMlAlgorithmId).HasColumnName("FK_MlAlgorithmId");

            entity.HasOne(d => d.FkMlAlgorithm).WithMany(p => p.MlRelevantProductCounts)
                .HasForeignKey(d => d.FkMlAlgorithmId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MlRelevantProductCounts_MlAlgorithms");
        });

        modelBuilder.Entity<Setting>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Settings__3214EC07B520A881");

            entity.Property(e => e.Description).IsUnicode(false);
            entity.Property(e => e.GroupName)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Name)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.Value).IsUnicode(false);
        });

        modelBuilder.Entity<SettingsProductScoreDecay>(entity =>
        {
            entity.ToTable("SettingsProductScoreDecay");

            entity.Property(e => e.CreatedDate)
                .HasPrecision(2)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.FkSettingId).HasColumnName("FK_SettingId");
            entity.Property(e => e.Name)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.Value)
                .HasMaxLength(255)
                .IsUnicode(false);

            entity.HasOne(d => d.FkSetting).WithMany(p => p.SettingsProductScoreDecays)
                .HasForeignKey(d => d.FkSettingId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SettingsProductScoreDecay_SettingsProductScoreDecay");
        });

        modelBuilder.Entity<TriggerEvent>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}