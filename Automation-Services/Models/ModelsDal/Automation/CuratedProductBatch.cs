using System;
using System.Collections.Generic;

namespace Automation_Service.Models.ModelsDal.Automation;

public partial class CuratedProductBatch
{
    public int Id { get; set; }

    public int CampaignId { get; set; }

    public string? Description { get; set; }

    public DateTime RunDate { get; set; }

    public DateTime? DoneDate { get; set; }

    public bool Active { get; set; }

    public DateTime CreatedDate { get; set; }

    public virtual ICollection<CuratedProductBatchEmail> CuratedProductBatchEmails { get; set; } =
        new List<CuratedProductBatchEmail>();
}