using System;
using Automation_Service.Models.ModelsElastic;
using Nest;

namespace Automation_Services.ModelsElastic.ModelsElastic
{
    public class CustomerProductRecommendation
    {
        [Object(Name = "Customer")] public Customer CustomerInfo { get; set; }

        [Object(Name = "Recommendation")] public Recommendation RecommendationInfo { get; set; }

        [Date(Name = "@timestamp")] public DateTime CreatedDate { get; set; }
    }
}