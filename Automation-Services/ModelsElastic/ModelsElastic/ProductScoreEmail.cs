using System;
using Automation_Service.Models.ModelsElastic;
using Nest;

namespace Automation_Services.ModelsElastic.ModelsElastic
{
    public class ProductScoreEmail
    {
        [Date(Name = "@timestamp")] public DateTime CreatedDate { get; set; }

        [Object(Name = "Customer")] public Customer CustomerInfo { get; set; }

        [Object(Name = "Email")] public Email EmailInfo { get; set; }

        [Object(Name = "Automation")] public AutomationInfo Automation { get; set; }
    }
}