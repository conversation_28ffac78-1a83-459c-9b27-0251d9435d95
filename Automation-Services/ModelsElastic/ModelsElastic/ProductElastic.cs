using Nest;

namespace Automation_Services.ModelsElastic.ModelsElastic
{
    public class ProductElastic
    {
        [Keyword(Name = "Internal_product_id")]
        public string InternalProductId { get; set; }

        [Keyword(Name = "Product_sku")] public string ProductSku { get; set; }

        [Keyword(Name = "Webshop_id")] public string WebshopId { get; set; }

        [Number(Name = "Score")] public decimal Score { get; set; }

        [Keyword(Name = "Recommended_by")] public string RecommendedBy { get; set; }
    }
}