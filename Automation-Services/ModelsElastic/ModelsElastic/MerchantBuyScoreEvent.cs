using System;
using Automation_Service.Models.ModelsElastic;
using Nest;

namespace Automation_Services.ModelsElastic.ModelsElastic
{
    public class MerchantBuyScoreEvent
    {
        [Object(Name = "Customer")] public Customer InternalCustomer { get; set; }

        [Object(Name = "Shop_order")] public MerchantBuyScoreEventInfo ShopOrder { get; set; }

        [Date(Name = "@timestamp")] public DateTime CreatedDate { get; set; }

        [Ignore] public decimal Score { get; set; }
    }
}