using System.Collections.Generic;
using Nest;
using Shared.Elastic.Order;

namespace Automation_Service.Models.ModelsElastic
{
    public class MerchantBuyScoreEventInfo
    {
        [Keyword(Name = "Product_internal_id")]
        public string ProductInternalId { get; set; }

        [Keyword(Name = "Price_range")] public string PriceRange { get; set; }

        [Number(Name = "Total_price")] public decimal TotalPrice { get; set; }

        [Number(Name = "Total_price_tax_included")]
        public decimal TotalPriceTaxIncluded { get; set; }

        [Keyword(Name = "Webshop_id")] public string WebshopId { get; set; }

        [Keyword(Name = "Webshop_name")] public string WebshopName { get; set; }

        [Keyword(Name = "Status")] public string Status { get; set; }

        [Keyword(Name = "Order_number")] public string OrderId { get; set; }

        [Object(Name = "Order_items")] public List<ElasticOrderEventItem> OrderItems { get; set; }
    }
}