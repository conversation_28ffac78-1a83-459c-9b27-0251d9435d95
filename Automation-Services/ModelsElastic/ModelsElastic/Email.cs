using System;
using System.Collections.Generic;
using Nest;

namespace Automation_Services.ModelsElastic.ModelsElastic
{
    public class Email
    {
        [Keyword(Name = "Email_guid")] public string Id { get; set; }

        [Keyword(Name = "Campaign_id")] public string CampaignId { get; set; }

        [Date(Name = "Action_date")] public DateTime ActionDate { get; set; }

        [Date(Name = "Sent_date")] public DateTime? SentDate { get; set; }

        [Keyword(Name = "Origin")] public string Origin { get; set; }

        [Number(NumberType.Short, Name = "Staging_number")]
        public short StagingNumber { get; set; }

        [Boolean(Name = "Is_override")] public bool IsOverride { get; set; }

        [Boolean(Name = "Is_test")] public bool IsTest { get; set; }

        [Keyword(Name = "Status")] public string Status { get; set; }

        [Nested(Name = "Products")] public List<ProductElastic> Products { get; set; }
    }
}