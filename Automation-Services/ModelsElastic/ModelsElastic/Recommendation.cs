using System.Collections.Generic;
using Nest;
using Shared.Elastic.Order;

namespace Automation_Service.Models.ModelsElastic
{
    public class Recommendation
    {
        [Keyword(Name = "Webshop_id")] public int WebshopId { get; set; }

        [Keyword(Name = "Webshop_name")] public string WebshopName { get; set; }

        [Keyword(Name = "Origin")] public string Origin { get; set; }

        [Nested(Name = "Products")] public List<ProductRecommendation> Recommendations { get; set; }
    }
}