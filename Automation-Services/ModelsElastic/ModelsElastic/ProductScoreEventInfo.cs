using Nest;

namespace Automation_Service.Models.ModelsElastic
{
    public class ProductScoreEventInfo
    {
        [Keyword(Name = "Product_internal_id")]
        public string ProductInternalId { get; set; }

        [Keyword(Name = "Price_range")] public string PriceRange { get; set; }

        [Number(Name = "Price")] public decimal Price { get; set; }

        [Keyword(Name = "Webshop_id")] public string WebshopId { get; set; }

        [Keyword(Name = "Event_type")] public string EventType { get; set; }

        [Keyword(Name = "Order_number")] public string OrderId { get; set; }
    }
}