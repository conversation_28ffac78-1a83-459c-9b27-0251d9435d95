using AutoMapper;
using Automation_Services.Services.Automation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ILogger = Serilog.ILogger;

namespace Automation_Services.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class AutomationController : ControllerBase
{
    private readonly ILogger _logger;
    private readonly IMapper _mapper;
    private readonly IAutomationService _automationService;

    public AutomationController(ILogger logger, IAutomationService automationService, IMapper mapper)
    {
        _logger = logger;
        _automationService = automationService;
        _mapper = mapper;
    }

    /*[HttpGet]
    public async Task<IActionResult> GetAllAsync()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllAsync"))
            {
                var result = await _automationService.GetAllAsync().ConfigureAwait(false);
                return Ok(_mapper.Map<IEnumerable<Automation>, IEnumerable<AutomationDto>>(result));
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Failed reading from Automation database");
            return Ok();
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("Merchantscore/{customerEmail}")]
    public async Task<IActionResult> TestMerchantScore(string customerEmail, CancellationToken cancellationToken)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "RunMerchantScoreTest"))
            {
                return Ok(await _automationService.RunMerchantScore(customerEmail, cancellationToken).ConfigureAwait(false));
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Failed running MerchantScore Test");
            return Ok();
        }
    }


    [HttpGet]
    [AllowAnonymous]
    [Route("RunMerchantscore")]
    public async Task<IActionResult> RunMerchantScore(CancellationToken cancellationToken)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "RunMerchantScore"))
            {

                await _automationService.RunMerchantScore(cancellationToken).ConfigureAwait(false);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Failed running MerchantScore");
            return Ok();
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("TestMerchantscore")]
    public async Task<IActionResult> TestMerchantScore(string receiverEmail, string customerEmail, CancellationToken cancellationToken)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "RunMerchantScoreTest"))
            {

                await _automationService.RunTestMerchantScore(receiverEmail, customerEmail, cancellationToken).ConfigureAwait(false);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Failed running MerchantScore Test");
            return Ok();
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("CreateCuratedBatch")]
    public async Task<IActionResult> CreateCuratedBatch(int campaignId, DateTime runDate, string description, CancellationToken cancellationToken)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CreateCuratedBatch"))
            {
                await _automationService.CreateCuratedBatch(campaignId, runDate, description, cancellationToken).ConfigureAwait(false);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Failed running CreateCuratedBatch");
            return Ok();
        }
    }*/
}