using Automation_Service.Models.ModelsDal.Automation;
using Automation_Services.Models;
using Automation_Services.Models.Message;
using Automation_Services.ModelsElastic.ModelsElastic;
using Marlin_OS_Integration_API.Models.Order;
using Nest;
using Shared.Models.Automation;

namespace Automation_Services.Services.AutomationElastic
{
    public interface IAutomationElastic
    {
        List<string> GetCustomersWithPageEvents(ElasticClient client, List<string> eventTypes, DateTime startDate,
            DateTime endDate);

        Task<List<ShopEventDto>> GetPageLooks(ElasticClient client, string email, DateTime startDate, DateTime endDate);

        Task<List<ElasticOrderEvent>> GetInitialOrderEvents(ElasticClient client, DateTime fromDate, DateTime toDate,
            string email);

        Task<List<ShopEventDto>> GetProductsLeft(ElasticClient client, string email, DateTime fromDate, DateTime toDate,
            string addTerm, string removeTerm);

        List<string> GetEmailAddressesWithProductScoreEvents(ElasticClient client, DateTime fromDate, DateTime toDate);

        MerchantsUsersDto GetEmailAddressesWithProductBought(ElasticClient client, int lookbackDays,
            CancellationToken cancellationToken);

        MerchantsUsersDto GetEmailAddressesWithMailClicks(ElasticClient client, int lookbackDays,
            CancellationToken cancellationToken);

        Task<List<ProductScoreEvent>> GetProductScore(ElasticClient client, string email, DateTime fromDate,
            DateTime toDate, Dictionary<string, int> scoreSettings, List<SettingsProductScoreDecayDto> decaySettings);

        Task<List<MerchantBuyScoreEvent>> GetMerchantBuyScore(ElasticClient client, string email, int lookBackDays,
            Dictionary<string, string> scoreSettings, CancellationToken cancellationToken);

        Task<List<MerchantClickScoreEvent>> GetMerchantClickScore(ElasticClient client, string email, int lookBackDays,
            Dictionary<string, string> scoreSettings, CancellationToken cancellationToken);

        Task<Dictionary<string, decimal>> GetMerchantProductScore(ElasticClient client, string webshopId,
            DateTime fromDate, DateTime toDate, Dictionary<string, string> scoreSettings,
            CancellationToken cancellationToken);

        Task<bool> SaveProductScore(ElasticClient client, ProductScoreEvent productScore);

        Task<List<CustomerProductRecommendation>> GetAiClusterRecommendations(ElasticClient client, string email,
            CancellationToken cancellationToken);

        Task<List<string>> GetProductsBought(ElasticClient client, string email, int lookbackDays,
            CancellationToken cancellationToken);
    }
}