using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Automation_Service.Models.ModelsDal.Automation;
using Automation_Service.Models.ModelsElastic;
using Automation_Services.Models;
using Automation_Services.Models.Message;
using Automation_Services.ModelsElastic.ModelsElastic;
using Marlin_OS_Integration_API.Models.Order;
using Nest;
using Shared.Elastic.Behavior;
using Shared.Elastic.Models.Behavior;
using Shared.Models.Automation;

namespace Automation_Services.Services.AutomationElastic
{
    public class AutomationElastic : IAutomationElastic
    {
        public List<string> GetCustomersWithPageEvents(ElasticClient client, List<string> eventTypes, DateTime fromDate,
            DateTime toDate)
        {
            var result = new ConcurrentBag<string>();

            int numberOfSlices = 2;

            var scrollAllObservable = client.ScrollAll<ElasticBehaviorEvent>("1m", numberOfSlices, sc => sc
                .MaxDegreeOfParallelism(numberOfSlices)
                .Search(s => s
                    .Index("customers-pages-events")
                    .Size(5000)
                    .Source(s => s
                        .Includes(i => i
                            .Field(f => f.Customer.Email)
                        )
                    )
                    .Query(q => q
                        .Bool(b => b
                            .MustNot(mn => mn.Terms(t => t
                                .Field(p => p.Customer.Email)
                                .Terms("n/a")))
                            .Filter(f => f
                                    .DateRange(d => d
                                        .Field(f => f.Event_date)
                                        .GreaterThanOrEquals(fromDate)
                                        .LessThan(toDate)),
                                f => f.Terms(t => t
                                    .Field(p => p.Shop_event.Event_type)
                                    .Terms(eventTypes))
                            )
                        )
                    )
                )
            ).Wait(TimeSpan.FromMinutes(30), response =>
            {
                foreach (var doc in response.SearchResponse.Documents)
                    result.Add(doc.Customer.Email);
            });

            return result.ToList();
        }

        public async Task<List<ShopEventDto>> GetPageLooks(ElasticClient client, string email, DateTime startDate,
            DateTime endDate)
        {
            List<ShopEventDto> events = new();

            var response = (await client.SearchAsync<ElasticBehaviorEvent>(s => s
                .Source(s => s
                    .Includes(i => i
                        .Field(f => f.Customer.Email)
                        .Field(f => f.Shop_event.Product_sku)
                        .Field(f => f.Shop_event.Product_internal_id)
                        .Field(f => f.Shop_event.Webshop_id)
                        .Field(f => f.Event_date)
                    )
                )
                .Index("customers-pages-events")
                .Query(q => q
                    .Bool(b => b
                        .Filter(f =>
                            f.DateRange(dt => dt
                                .Field(field => field.Event_date)
                                .GreaterThanOrEquals(startDate)
                                .LessThanOrEquals(endDate))
                            && f.Term(t => t.Field(f => f.Customer.Email).Value(email))
                            && f.Term(t => t.Field(f => f.Shop_event.Event_type).Value("ProductLook")))))
                .Sort(s => s.Descending(d => d.Event_date)))).Documents.ToList();

            foreach (var shopEvent in response)
            {
                events.Add(new ShopEventDto
                {
                    WebshopId = shopEvent.Shop_event.Webshop_id, ProductSku = shopEvent.Shop_event.Product_sku,
                    ProductInternalId = shopEvent.Shop_event.Product_internal_id, EventTime = shopEvent.Event_date,
                    Priority = 2
                });
            }

            return events;
        }


        public async Task<List<ElasticOrderEvent>> GetInitialOrderEvents(ElasticClient client, DateTime fromDate,
            DateTime toDate, string email)
        {
            List<ElasticOrderEvent> responsedata = new();

            if (client.Indices.Exists("customers-orders").Exists)
            {
                responsedata = (await client.SearchAsync<ElasticOrderEvent>(s => s
                    .Index("customers-orders")
                    .Size(100)
                    .Query(q =>
                        q.Bool(b => b
                            .Filter(f => f
                                             .DateRange(d => d.Field(f => f.Order_date)
                                                 .GreaterThanOrEquals(fromDate)
                                                 .LessThan(toDate))
                                         && f.Term(t => t.Field(f => f.Customer.Email).Value(email))
                            ))
                    )
                    .Collapse(c => c
                        .Field(f => f.Shop_order.Webshop_id)
                        .Field(f => f.Shop_order.Order_number)
                        .InnerHits(ih => ih
                            .Name("FirstOrderEntry")
                            .Size(1)
                            .Sort(s => s
                                .Ascending(f => f.Order_date))))
                )).Documents.ToList();
            }

            return responsedata;
        }

        public async Task<List<ShopEventDto>> GetProductsLeft(ElasticClient client, string email, DateTime fromDate,
            DateTime toDate, string addTerm, string removeTerm)
        {
            List<ShopEventDto> events = new();

            if (client.Indices.Exists("customers-pages-events").Exists)
            {
                var response = await client.SearchAsync<ElasticBehaviorEvent>(s => s
                    .Index("customers-pages-events")
                    .Query(q => q
                        .Bool(b => b
                            .Filter(f =>
                                    f.Terms(t => t.Field(f => f.Shop_event.Event_type).Terms(addTerm, removeTerm)),
                                f => f.Term(t => t.Field(f => f.Customer.Email).Value(email)),
                                f => f.DateRange(d => d.Field(f => f.Event_date)
                                    .GreaterThanOrEquals(fromDate)
                                    .LessThan(toDate))
                            )
                        ))
                    .Collapse(c => c
                        .Field(f => f.Shop_event.Webshop_id)
                        .Field(f => f.Shop_event.Product_sku)
                        .Field(f => f.Shop_event.Product_internal_id)
                        .InnerHits(ih => ih
                            .Name("NewestEventType")
                            .Size(1)
                            .Sort(s => s
                                .Descending(f => f.Event_date))))
                );

                var hits = response.Hits;

                foreach (var item in hits)
                {
                    var productLeft = item.InnerHits["NewestEventType"].Documents<ElasticBehaviorEvent>().First();

                    if (productLeft != null)
                    {
                        events.Add(new ShopEventDto
                        {
                            WebshopId = productLeft.Shop_event.Webshop_id,
                            ProductSku = productLeft.Shop_event.Product_sku,
                            ProductInternalId = productLeft.Shop_event.Product_internal_id,
                            EventTime = productLeft.Event_date, Priority = 1
                        });
                    }
                }
            }

            return events;
        }


        public List<string> GetEmailAddressesWithProductScoreEvents(ElasticClient client, DateTime fromDate,
            DateTime toDate)
        {
            var result = new ConcurrentBag<string>();
            int numberOfSlices = 2;

            var scrollAllObservable = client.ScrollAll<ProductScoreEvent>("1m", numberOfSlices, sc => sc
                    .MaxDegreeOfParallelism(numberOfSlices)
                    .Search(s => s
                        .Index("customers-scores-products")
                        .Size(5000)
                        .Source(s => s
                            .Includes(i => i
                                .Field(f => f.InternalCustomer.Email)
                            )
                        )
                        .Query(q =>
                            q.Bool(b => b
                                .Filter(f => f
                                    .DateRange(d => d
                                        .Field(f => f.CreatedDate)
                                        .GreaterThanOrEquals(fromDate)
                                        .LessThan(toDate)))
                            ))))
                .Wait(TimeSpan.FromMinutes(15), response =>
                {
                    foreach (var doc in response.SearchResponse.Documents)
                        result.Add(doc.InternalCustomer.Email);
                });

            return result.Distinct().ToList();
        }

        public MerchantsUsersDto GetEmailAddressesWithProductBought(ElasticClient client, int lookbackDays,
            CancellationToken cancellationToken)
        {
            ConcurrentDictionary<string, List<string>> emails = new();
            ConcurrentBag<string> merchants = new();

            int numberOfSlices = 2;

            var scrollAllObservable = client.ScrollAll<MerchantBuyScoreEvent>("1m", numberOfSlices, sc => sc
                    .MaxDegreeOfParallelism(numberOfSlices)
                    .Search(s => s
                        .Index("customers-scores-merchants")
                        .Size(5000)
                        .Source(s => s
                            .Includes(i => i
                                .Field(f => f.InternalCustomer.Email)
                                .Field(f => f.ShopOrder.WebshopId)
                            )
                        )
                        .Query(q =>
                            q.Bool(b => b
                                .Filter(f => f
                                    .DateRange(d => d
                                        .Field(f => f.CreatedDate)
                                        .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-lookbackDays))
                                    ))
                            ))), cancellationToken)
                .Wait(TimeSpan.FromMinutes(15), response =>
                {
                    foreach (var doc in response.SearchResponse.Documents)
                    {
                        if (doc.ShopOrder is not null && doc.ShopOrder.WebshopId != "0" &&
                            doc.ShopOrder.WebshopId != "n/a")
                        {
                            if (emails.ContainsKey(doc.InternalCustomer.Email))
                            {
                                emails[doc.InternalCustomer.Email].Add(doc.ShopOrder.WebshopId);
                            }
                            else
                            {
                                var isAdded = emails.TryAdd(doc.InternalCustomer.Email,
                                    new List<string> {doc.ShopOrder.WebshopId});

                                if (!isAdded)
                                    emails[doc.InternalCustomer.Email].Add(doc.ShopOrder.WebshopId);
                            }

                            merchants.Add(doc.ShopOrder.WebshopId);
                        }
                    }
                });

            var result = new MerchantsUsersDto();

            result.Merchants = merchants.Distinct().ToList();
            result.Emails = emails.ToDictionary(x => x.Key, y => y.Value.Distinct().ToList());

            return result;
        }

        public MerchantsUsersDto GetEmailAddressesWithMailClicks(ElasticClient client, int lookbackDays,
            CancellationToken cancellationToken)
        {
            ConcurrentDictionary<string, List<string>> emails = new();
            ConcurrentBag<string> merchants = new();

            int numberOfSlices = 2;

            var scrollAllObservable = client.ScrollAll<MerchantClickScoreEvent>("1m", numberOfSlices, sc => sc
                    .MaxDegreeOfParallelism(numberOfSlices)
                    .Search(s => s
                        .Index("campaigns-mails-clicks")
                        .Size(5000)
                        .Source(s => s
                            .Includes(i => i
                                .Field(f => f.InternalCustomer.Email)
                                .Field(f => f.ShopEventInfo.WebshopId)
                            )
                        )
                        .Query(q =>
                            q.Bool(b => b
                                .Filter(f => f
                                    .DateRange(d => d
                                        .Field(f => f.CreatedDate)
                                        .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-lookbackDays))
                                    ))
                            ))), cancellationToken)
                .Wait(TimeSpan.FromMinutes(15), response =>
                {
                    foreach (var doc in response.SearchResponse.Documents)
                    {
                        if (doc.ShopEventInfo is not null && doc.ShopEventInfo.WebshopId != "0" &&
                            doc.ShopEventInfo.WebshopId != "n/a")
                        {
                            if (emails.ContainsKey(doc.InternalCustomer.Email))
                            {
                                emails[doc.InternalCustomer.Email].Add(doc.ShopEventInfo.WebshopId);
                            }
                            else
                            {
                                var isAdded = emails.TryAdd(doc.InternalCustomer.Email,
                                    new List<string> {doc.ShopEventInfo.WebshopId});

                                if (!isAdded)
                                    emails[doc.InternalCustomer.Email].Add(doc.ShopEventInfo.WebshopId);
                            }

                            merchants.Add(doc.ShopEventInfo.WebshopId);
                        }
                    }
                });

            var result = new MerchantsUsersDto();

            result.Merchants = merchants.Distinct().ToList();
            result.Emails = emails.ToDictionary(x => x.Key, y => y.Value.Distinct().ToList());

            return result;
        }

        public async Task<List<ProductScoreEvent>> GetProductScore(ElasticClient client, string email,
            DateTime fromDate, DateTime toDate, Dictionary<string, int> scoreSettings,
            List<SettingsProductScoreDecayDto> decaySettings)
        {
            var result = new List<ProductScoreEvent>();

            var now = TimeZoneInfo.ConvertTimeToUtc(DateTime.UtcNow);
            now = new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute, now.Second);

            var filters = new List<Func<QueryContainerDescriptor<ProductScoreEvent>, QueryContainer>>
            {
                f => f.DateRange(dt => dt
                         .Field(f => f.CreatedDate)
                         .GreaterThanOrEquals(fromDate)
                         .LessThanOrEquals(toDate))
                     && f.Term(t => t.Field(f => f.InternalCustomer.Email).Value(email)),

                f => f.DateRange(dt => dt
                         .Field(f => f.CreatedDate)
                         .GreaterThanOrEquals(toDate)
                         .LessThanOrEquals(now))
                     && f.Term(t => t.Field(f => f.InternalCustomer.Email).Value(email))
                     && f.Terms(t => t.Field(f => f.ShopEvent.EventType).Terms(new string[] {"order", "mailsent"}))
            };

            var response = await client.SearchAsync<ProductScoreEvent>(s => s
                .Source(s => s
                    .Includes(i => i
                        .Field(f => f.InternalCustomer.Email)
                        .Field(f => f.ShopEvent.WebshopId)
                        .Field(f => f.ShopEvent.ProductInternalId)
                        .Field(f => f.ShopEvent.OrderId)
                        .Field(f => f.ShopEvent.PriceRange)
                        .Field(f => f.ShopEvent.EventType)
                        .Field(f => f.CreatedDate)
                    )
                )
                .Index("customers-scores-products")
                .Query(q => q
                    .FunctionScore(fs => fs
                        .Name("ProductScores")
                        .BoostMode(FunctionBoostMode.Replace)
                        .ScoreMode(FunctionScoreMode.Sum)
                        .Query(qu => qu
                            .Bool(b => b
                                .Filter(f => f.Bool(b => b.Should(filters)))
                            )
                        )
                        .Functions(fcs => fcs
                            .ExponentialDate(exp => exp
                                .Field(f => f.CreatedDate)
                                .Origin(toDate)
                                .Scale(decaySettings
                                    .Where(x => x.Name == "ScaleDays" && x.SettingName == "ProductLookAt").First()
                                    .Value + "d")
                                .Offset(decaySettings
                                    .Where(x => x.Name == "OffsetDays" && x.SettingName == "ProductLookAt").First()
                                    .Value + "d")
                                .Decay(double.Parse(
                                    decaySettings.Where(x => x.Name == "Decay" && x.SettingName == "ProductLookAt")
                                        .First().Value, CultureInfo.InvariantCulture))
                                .Weight(scoreSettings["ProductLookAt"])
                                .Filter(f =>
                                    f.Terms(t => t.Field(f => f.ShopEvent.EventType).Terms(new[] {"ProductLook"})))
                            )
                            .ExponentialDate(exp => exp
                                .Field(f => f.CreatedDate)
                                .Origin(toDate)
                                .Scale(decaySettings
                                    .Where(x => x.Name == "ScaleDays" && x.SettingName == "ProductAddCart").First()
                                    .Value + "d")
                                .Offset(decaySettings
                                    .Where(x => x.Name == "OffsetDays" && x.SettingName == "ProductAddCart").First()
                                    .Value + "d")
                                .Decay(double.Parse(
                                    decaySettings.Where(x => x.Name == "Decay" && x.SettingName == "ProductAddCart")
                                        .First().Value, CultureInfo.InvariantCulture))
                                .Weight(scoreSettings["ProductAddCart"])
                                .Filter(f => f.Terms(t =>
                                    t.Field(f => f.ShopEvent.EventType).Terms(new[] {"AddCart", "RemoveCart"})))
                            )
                            .ExponentialDate(exp => exp
                                .Field(f => f.CreatedDate)
                                .Origin(toDate)
                                .Scale(decaySettings
                                    .Where(x => x.Name == "ScaleDays" && x.SettingName == "ProductAddWishList").First()
                                    .Value + "d")
                                .Offset(decaySettings
                                    .Where(x => x.Name == "OffsetDays" && x.SettingName == "ProductAddWishList").First()
                                    .Value + "d")
                                .Decay(double.Parse(
                                    decaySettings.Where(x => x.Name == "Decay" && x.SettingName == "ProductAddWishList")
                                        .First().Value, CultureInfo.InvariantCulture))
                                .Weight(scoreSettings["ProductAddWishList"])
                                .Filter(f => f.Terms(t =>
                                    t.Field(f => f.ShopEvent.EventType).Terms(new[] {"AddWishlist", "RemoveWishlist"})))
                            )
                            .ExponentialDate(exp => exp
                                .Field(f => f.CreatedDate)
                                .Origin(toDate)
                                .Scale(decaySettings
                                    .Where(x => x.Name == "ScaleDays" && x.SettingName == "ClickProduct").First()
                                    .Value + "d")
                                .Offset(decaySettings
                                    .Where(x => x.Name == "OffsetDays" && x.SettingName == "ClickProduct").First()
                                    .Value + "d")
                                .Decay(double.Parse(
                                    decaySettings.Where(x => x.Name == "Decay" && x.SettingName == "ClickProduct")
                                        .First().Value, CultureInfo.InvariantCulture))
                                .Weight(scoreSettings["ClickProduct"])
                                .Filter(f => f.Terms(t =>
                                    t.Field(f => f.ShopEvent.EventType).Terms(new[] {"MailCampaignProductClick"})))
                            )
                            .ExponentialDate(exp => exp
                                .Field(f => f.CreatedDate)
                                .Origin("now/d")
                                .Scale(decaySettings
                                    .Where(x => x.Name == "ScaleDays" && x.SettingName == "ProductBought").First()
                                    .Value + "d")
                                .Offset(decaySettings
                                    .Where(x => x.Name == "OffsetDays" && x.SettingName == "ProductBought").First()
                                    .Value + "d")
                                .Decay(double.Parse(
                                    decaySettings.Where(x => x.Name == "Decay" && x.SettingName == "ProductBought")
                                        .First().Value, CultureInfo.InvariantCulture))
                                .Weight(scoreSettings["ProductBought"])
                                .Filter(f => f.Terms(t => t.Field(f => f.ShopEvent.EventType).Terms(new[] {"order"})))
                            )
                            .LinearDate(exp => exp
                                .Field(f => f.CreatedDate)
                                .Origin("now/d")
                                .Scale("1m")
                                .Offset(scoreSettings["MailCooldownDays"] + "d")
                                .Decay(0.5)
                                .Weight(10000.0)
                                .Filter(f =>
                                    f.Terms(t => t.Field(f => f.ShopEvent.EventType).Terms(new[] {"mailsent"})))
                            )
                        )
                    )
                ));

            foreach (var hit in response.Hits)
            {
                result.Add(new ProductScoreEvent
                {
                    CreatedDate = hit.Source.CreatedDate, InternalCustomer = hit.Source.InternalCustomer,
                    ShopEvent = hit.Source.ShopEvent, Score = (decimal) (hit.Score ?? 0.0)
                });
            }

            return result;
        }

        public async Task<bool> SaveProductScore(ElasticClient client, ProductScoreEvent productScore)
        {
            var indexResponse = await client.IndexAsync(productScore,
                b => b.Index("customers-scores-products").Pipeline("add_ingest_time"));

            return indexResponse.IsValid;
        }

        public async Task<List<MerchantBuyScoreEvent>> GetMerchantBuyScore(ElasticClient client, string email,
            int lookBackDays, Dictionary<string, string> scoreSettings, CancellationToken cancellationToken)
        {
            var result = new List<MerchantBuyScoreEvent>();

            var now = TimeZoneInfo.ConvertTimeToUtc(DateTime.UtcNow);
            now = new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute, now.Second);
            var fromDate = now.AddDays(-lookBackDays);
            var scale = scoreSettings["DecayScaleDays"];
            var decay = double.Parse(scoreSettings["DecayFraction"], CultureInfo.InvariantCulture);
            var buyScore = int.Parse(scoreSettings["BuyScore"]);


            var filters = new List<Func<QueryContainerDescriptor<MerchantBuyScoreEvent>, QueryContainer>>
            {
                f => f.DateRange(dt => dt
                         .Field(f => f.CreatedDate)
                         .GreaterThanOrEquals(fromDate))
                     && f.Term(t => t.Field(f => f.InternalCustomer.Email).Value(email))
            };

            var response = await client.SearchAsync<MerchantBuyScoreEvent>(s => s
                .Source(s => s
                    .Includes(i => i
                        .Field(f => f.InternalCustomer.Email)
                        .Field(f => f.ShopOrder.WebshopId)
                        .Field(f => f.CreatedDate)
                        .Field(f => f.ShopOrder.OrderItems)
                    )
                )
                .Index("customers-scores-merchants")
                .Query(q => q
                    .FunctionScore(fs => fs
                        .Name("MerchantScores")
                        .BoostMode(FunctionBoostMode.Max)
                        .ScoreMode(FunctionScoreMode.Sum)
                        .Query(qu => qu
                            .Bool(b => b
                                .Filter(f => f.Bool(b => b.Should(filters)))
                            )
                        )
                        .Functions(fcs => fcs
                            .ExponentialDate(exp => exp
                                .Field(f => f.CreatedDate)
                                .Origin(now)
                                .Scale(scale + "d")
                                .Offset("0d")
                                .Decay(decay)
                                .Weight(buyScore)
                            )
                        )
                    )
                ), cancellationToken);

            if (!cancellationToken.IsCancellationRequested)
            {
                foreach (var hit in response.Hits)
                {
                    result.Add(new MerchantBuyScoreEvent
                    {
                        CreatedDate = hit.Source.CreatedDate, InternalCustomer = hit.Source.InternalCustomer,
                        ShopOrder = hit.Source.ShopOrder, Score = (decimal) (hit.Score ?? 0.0)
                    });
                }
            }

            return result;
        }

        public async Task<List<MerchantClickScoreEvent>> GetMerchantClickScore(ElasticClient client, string email,
            int lookBackDays, Dictionary<string, string> scoreSettings, CancellationToken cancellationToken)
        {
            var result = new List<MerchantClickScoreEvent>();

            var now = TimeZoneInfo.ConvertTimeToUtc(DateTime.UtcNow);
            now = new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute, now.Second);
            var fromDate = now.AddDays(-lookBackDays);
            var scale = scoreSettings["DecayScaleDays"];
            var decay = double.Parse(scoreSettings["DecayFraction"], CultureInfo.InvariantCulture);
            var clickScore = int.Parse(scoreSettings["ClickScore"]);

            var filters = new List<Func<QueryContainerDescriptor<MerchantClickScoreEvent>, QueryContainer>>
            {
                f => f.DateRange(dt => dt
                         .Field(f => f.CreatedDate)
                         .GreaterThanOrEquals(fromDate))
                     && f.Term(t => t.Field(f => f.InternalCustomer.Email).Value(email))
            };

            var response = await client.SearchAsync<MerchantClickScoreEvent>(s => s
                .Source(s => s
                    .Includes(i => i
                        .Field(f => f.InternalCustomer.Email)
                        .Field(f => f.ShopEventInfo.WebshopId)
                        .Field(f => f.CreatedDate)
                    )
                )
                .Index("campaigns-mails-clicks")
                .Query(q => q
                    .FunctionScore(fs => fs
                        .Name("MerchantScores")
                        .BoostMode(FunctionBoostMode.Max)
                        .ScoreMode(FunctionScoreMode.Sum)
                        .Query(qu => qu
                            .Bool(b => b
                                .Filter(f => f.Bool(b => b.Should(filters)))
                            )
                        )
                        .Functions(fcs => fcs
                            .LinearDate(exp => exp
                                .Field(f => f.CreatedDate)
                                .Origin(now)
                                .Scale(scale + "d")
                                .Offset("0d")
                                .Decay(decay)
                                .Weight(clickScore)
                            )
                        )
                    )
                ), cancellationToken);

            if (!cancellationToken.IsCancellationRequested)
            {
                foreach (var hit in response.Hits)
                {
                    result.Add(new MerchantClickScoreEvent
                    {
                        CreatedDate = hit.Source.CreatedDate, InternalCustomer = hit.Source.InternalCustomer,
                        ShopEventInfo = hit.Source.ShopEventInfo, Score = (decimal) (hit.Score ?? 0.0)
                    });
                }
            }

            return result;
        }

        public async Task<Dictionary<string, decimal>> GetMerchantProductScore(ElasticClient client, string webshopId,
            DateTime fromDate, DateTime toDate, Dictionary<string, string> scoreSettings,
            CancellationToken cancellationToken)
        {
            Dictionary<string, decimal> result = new();

            var now = TimeZoneInfo.ConvertTimeToUtc(DateTime.UtcNow);
            now = new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute, now.Second);
            var scale = scoreSettings["PageviewDecayScaleDays"];
            var decay = double.Parse(scoreSettings["PageviewDecayFraction"], CultureInfo.InvariantCulture);
            var pageviewScore = int.Parse(scoreSettings["PageviewScore"]);

            var filters = new List<Func<QueryContainerDescriptor<MerchantProductScoreEvent>, QueryContainer>>
            {
                f => f.DateRange(dt => dt
                         .Field(f => f.CreatedDate)
                         .GreaterThanOrEquals(fromDate)
                         .LessThanOrEquals(toDate))
                     && f.Term(t => t.Field(f => f.ShopEvent.WebshopId).Value(webshopId))
                     && f.Term(t => t.Field(f => f.ShopEvent.EventType).Value("ProductLook"))
            };

            var response = await client.SearchAsync<MerchantProductScoreEvent>(s => s
                .Size(0)
                .Source(s => s
                    .Includes(i => i
                        .Field(f => f.ShopEvent.WebshopId)
                        .Field(f => f.ShopEvent.ProductInternalId)
                    )
                )
                .Index("customers-pages-events")
                .Query(q => q
                    .FunctionScore(fs => fs
                        .Name("MerchantProductScores")
                        .BoostMode(FunctionBoostMode.Replace)
                        .ScoreMode(FunctionScoreMode.Sum)
                        .Query(qu => qu
                            .Bool(b => b
                                .Filter(f => f.Bool(b => b.Should(filters)))
                            )
                        )
                        .Functions(fcs => fcs
                            .ExponentialDate(exp => exp
                                .Field(f => f.CreatedDate)
                                .Origin(toDate)
                                .Scale(scale + "d")
                                .Offset("0d")
                                .Decay(decay)
                                .Weight(pageviewScore)
                            )
                        )
                    )
                )
                .Aggregations(a => a.Terms("shops", t => t.Field("Shop_event.Webshop_id")
                    .Field("Shop_event.Product_internal_id")
                    .Aggregations(a => a
                        .Sum("productscore", s => s.Script("_score"))))), cancellationToken);

            if (!cancellationToken.IsCancellationRequested)
            {
                if (response.IsValid)
                {
                    var aggs = response.Aggregations.Terms("shops");
                    foreach (var agg in aggs.Buckets)
                    {
                        result.Add(agg.Key, (decimal) (agg.Sum("productscore").Value ?? 0.0));
                    }
                }
            }

            return result;
        }


        public async Task<List<CustomerProductRecommendation>> GetAiClusterRecommendations(ElasticClient client,
            string email, CancellationToken cancellationToken)
        {
            List<CustomerProductRecommendation> recommendations = new();

            if (client.Indices.Exists("customers-recommendations").Exists)
            {
                var response = await client.SearchAsync<CustomerProductRecommendation>(s => s
                    .Index("customers-recommendations")
                    .Query(q => q
                        .Bool(b => b
                            .Filter(f =>
                                f.Term(t => t.Field(f => f.CustomerInfo.Email).Value(email))
                            )
                        )));

                if (!cancellationToken.IsCancellationRequested)
                {
                    foreach (var hit in response.Hits)
                    {
                        recommendations.Add(new CustomerProductRecommendation
                        {
                            CreatedDate = hit.Source.CreatedDate, CustomerInfo = hit.Source.CustomerInfo,
                            RecommendationInfo = hit.Source.RecommendationInfo
                        });
                    }
                }
            }

            return recommendations;
        }

        public async Task<List<string>> GetProductsBought(ElasticClient client, string email, int lookbackDays,
            CancellationToken cancellationToken)
        {
            List<string> products = new();

            var response = await client.SearchAsync<ElasticOrderEvent>(s => s
                    .Index("customers-orders")
                    .Query(q => q
                        .Bool(b => b
                            .Filter(f => f
                                    .DateRange(d => d
                                        .Field(f => f.Order_date)
                                        .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-lookbackDays))),
                                f => f.Term(t => t.Field(f => f.Customer.Email).Value(email)),
                                f => f.Exists(e => e.Field(f => f.Shop_order.Order_items))
                            )
                        ))
                , cancellationToken);

            if (!cancellationToken.IsCancellationRequested)
            {
                List<string> foundProducts = new();

                foreach (var hit in response.Hits)
                {
                    products.AddRange(hit.Source.Shop_order.Order_items.Select(x => x.Internal_product_id ?? ""));
                }
            }

            return products;
        }
    }
}