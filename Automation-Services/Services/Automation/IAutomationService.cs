namespace Automation_Services.Services.Automation
{
    public interface IAutomationService
    {
        //Task<List<Automation_Service.Models.ModelsDal.Automation.Automation>> GetAllAsync();
        Task Run();

        Task RunTest(string recipient, int campaignId, string internalProductId);
        //Task<List<ProductReturnDto>> RunMerchantScore(string receiverEmail, CancellationToken cancellationToken);
        //Task RunMerchantScore(CancellationToken cancellationToken);
        //Task RunTestMerchantScore(string receiverEmail, string customerEmail, CancellationToken cancellationToken);
        //Task CreateCuratedBatch(int campaignId, DateTime runDate, string description, CancellationToken cancellationToken);
    }
}