using System.Collections.Concurrent;
using System.Diagnostics;
using System.Globalization;
using System.Text;
using System.Text.Json;
using System.Threading;
using Audience.Services.Audience;
using Automation_Service.Models.ModelsElastic;
using Automation_Services.Models;
using Automation_Services.Models.Message;
using Automation_Services.Models.ModelsDal.Automation;
using Automation_Services.ModelsElastic.ModelsElastic;
using Automation_Services.Services.AutomationElastic;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Nest;
using RabbitMQ.Client;
using Shared.Models.Automation;
using Shared.Services.Setting;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace Automation_Services.Services.Automation;

public class AutomationService(
    ILogger logger,
    ICustomerService customerService,
    IMerchantService merchantService,
    IAutomationElastic automationElastic,
    ElasticClient elasticClient,
    [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud,
    ISettingService settingService)
    : IAutomationService
{
    private readonly IMerchantService _merchantService = merchantService;

    public async Task Run()
    {
        List<string>? emails = new();

        int createdEmails = 0;
        Stopwatch stopWatch = Stopwatch.StartNew();

        try
        {
            var scoreSettings = (await settingService.GetSettingByGroupAsync(2))
                .Select(x => new {x.Key, value = int.Parse(x.Value)}).ToDictionary(x => x.Key, x => x.value);

            var decaySettings = await settingService.GetSettingAutomation();


            emails = await customerService.GetAllConsentEmailListAsync();

            var timeNow = DateTime.UtcNow;
            var eventLeeTime = scoreSettings["EventLeeTimeInHours"];
            var lookbackDays = scoreSettings["LookbackDays"];
            var newEventsLookbackInHours = scoreSettings["NewEventsLookbackInHours"];

            var lastRunAt = DateTime.UtcNow.AddHours(-16);

            var scoreEmails = automationElastic.GetEmailAddressesWithProductScoreEvents(elasticClient,
                lastRunAt.AddHours(-(eventLeeTime + newEventsLookbackInHours)), lastRunAt.AddHours(-eventLeeTime));

            scoreEmails = scoreEmails.Intersect(emails).ToList();

            using (var publishChannel = rabbitConnectionCloud.CreateModel())
            {
                publishChannel.ConfirmSelect();

                foreach (var scoreEmail in scoreEmails)
                {
                    var scores = await automationElastic.GetProductScore(elasticClient, scoreEmail,
                        TimeZoneInfo.ConvertTimeToUtc(DateTime.UtcNow).AddDays(-lookbackDays),
                        lastRunAt.AddHours(-eventLeeTime), scoreSettings, decaySettings);

                    scores.RemoveAll(x =>
                        string.IsNullOrEmpty(x.ShopEvent.ProductInternalId) && x.ShopEvent.EventType != "mailsent");

                    if (scores.Count > 0)
                    {
                        foreach (var x in scores.Where(x =>
                                         x.ShopEvent.EventType.Contains("Remove") || x.ShopEvent.EventType == "order")
                                     .ToList())
                        {
                            x.Score = -x.Score;
                        }

                        var mailSentScore = scores.Where(x => x.ShopEvent.EventType == "mailsent").Sum(x => x.Score);

                        var theScores = scores.Where(x => x.ShopEvent.ProductInternalId != "n/a")
                            .GroupBy(x => x.ShopEvent.ProductInternalId).Select(x => new ProductElastic
                            {
                                InternalProductId = x.Key, Score = x.Sum(s => s.Score) - mailSentScore,
                                WebshopId = x.First().ShopEvent.WebshopId
                            }).ToList();

                        var products = new List<ProductElastic>();
                        foreach (var productScore in theScores)
                        {
                            if (productScore.Score >= scoreSettings["TriggerScore"])
                            {
                                products.AddRange(theScores.Where(x => x.Score >= 25).OrderByDescending(x => x.Score)
                                    .Take(scoreSettings["MaxProductCountForMails"]));
                                break;
                            }
                        }

                        if (products.Count > 0)
                        {
                            if (await automationElastic.SaveProductScore(elasticClient,
                                    new ProductScoreEvent
                                    {
                                        CreatedDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.UtcNow),
                                        InternalCustomer = new Customer {Email = scoreEmail},
                                        ShopEvent = new ProductScoreEventInfo
                                            {EventType = "mailsent", ProductInternalId = "n/a"}
                                    }))
                            {
                                var messageProducts = new List<MessageProductDto>();
                                int index = 1;
                                List<string> addedProducts = new();


                                foreach (var product in products)
                                {
                                    if (!addedProducts.Contains(product.InternalProductId))
                                    {
                                        messageProducts.Add(new MessageProductDto
                                        {
                                            InternalProductId = product.InternalProductId, Score = product.Score,
                                            RecommendedBy = new List<string> {"ProductScore"},
                                            MerchantId = product.WebshopId
                                        });

                                        addedProducts.Add(product.InternalProductId);

                                        index++;
                                    }
                                }

                                var messageList = new List<MessageDto>
                                {
                                    new MessageDto
                                    {
                                        Email = scoreEmail,
                                        EmailGuid = Guid.NewGuid(),
                                        CampaignId = scoreSettings["CampaignId"],
                                        AutomationId = "1",
                                        Origin = "automation",
                                        ActionDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.UtcNow),
                                        AutomationTrigger = "ProductScore",
                                        MessageProducts = messageProducts
                                    }
                                };

                                var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(messageList));

                                publishChannel.BasicPublish(exchange: "campaign",
                                    routingKey: "campaign_mail_new",
                                    basicProperties: null,
                                    body: actionBody);

                                publishChannel.WaitForConfirmsOrDie(new TimeSpan(0, 0, 3));
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error preparing and queueing email action");
        }

        stopWatch.Stop();
        var sec = stopWatch.ElapsedMilliseconds / 1000;

        logger.ForContext("service_name", GetType().Name)
            .Information("Finished {Event_type} of {Count} mails in {elapsed} seconds", "creating mails",
                createdEmails, sec);
    }

    public async Task RunTest(string recipient, int campaignId, string internalProductId)
    {
        List<string>? emails = new();

        int createdEmails = 0;
        Stopwatch stopWatch = Stopwatch.StartNew();

        try
        {
            var scoreSettings = (await settingService.GetSettingByGroupAsync(2))
                .Select(x => new {x.Key, value = int.Parse(x.Value)}).ToDictionary(x => x.Key, x => x.value);

            var decaySettings = await settingService.GetSettingAutomation();

            emails = await customerService.GetAllConsentEmailListAsync();

            var timeNow = TimeZoneInfo.ConvertTimeToUtc(DateTime.UtcNow);
            timeNow = new DateTime(timeNow.Year, timeNow.Month, timeNow.Day, timeNow.Hour, timeNow.Minute,
                timeNow.Second);
            var eventLeeTime = scoreSettings["EventLeeTimeInHours"];
            var lookbackDays = scoreSettings["LookbackDays"];
            var newEventsLookbackInHours = scoreSettings["NewEventsLookbackInHours"];

            var lastRunAt = DateTime.UtcNow.AddHours(-16);

            var scoreEmails = automationElastic.GetEmailAddressesWithProductScoreEvents(elasticClient,
                lastRunAt.AddHours(-(eventLeeTime + newEventsLookbackInHours)), lastRunAt.AddHours(-eventLeeTime));

            scoreEmails = scoreEmails.Intersect(emails).ToList();

            using (var publishChannel = rabbitConnectionCloud.CreateModel())
            {
                publishChannel.ConfirmSelect();

                var maxProductsFound = 0;
                var scoreEmailToSend = string.Empty;
                var productsToSend = new List<ProductElastic>();

                foreach (var scoreEmail in scoreEmails)
                {
                    var scores = await automationElastic.GetProductScore(elasticClient, scoreEmail,
                        TimeZoneInfo.ConvertTimeToUtc(DateTime.UtcNow).AddDays(-lookbackDays),
                        lastRunAt.AddHours(-eventLeeTime), scoreSettings, decaySettings);

                    scores.RemoveAll(x =>
                        string.IsNullOrEmpty(x.ShopEvent.ProductInternalId) && x.ShopEvent.EventType != "mailsent");

                    if (scores.Count > 0)
                    {
                        foreach (var score in scores.Where(x =>
                                         x.ShopEvent.EventType.Contains("Remove") || x.ShopEvent.EventType == "order")
                                     .ToList())
                        {
                            score.Score = -score.Score;
                        }

                        var mailSentScore = scores.Where(x => x.ShopEvent.EventType == "mailsent").Sum(x => x.Score);

                        var theScores = scores.Where(x => x.ShopEvent.ProductInternalId != "n/a")
                            .GroupBy(x => x.ShopEvent.ProductInternalId).Select(x => new ProductElastic
                            {
                                InternalProductId = x.Key, Score = x.Sum(s => s.Score) - mailSentScore,
                                WebshopId = x.First().ShopEvent.WebshopId
                            }).ToList();

                        var products = new List<ProductElastic>();
                        foreach (var productScore in theScores)
                        {
                            if (productScore.Score >= scoreSettings["TriggerScore"])
                            {
                                products.AddRange(theScores.Where(x => x.Score >= 25).OrderByDescending(x => x.Score)
                                    .Take(scoreSettings["MaxProductCountForMails"]));
                                break;
                            }
                        }

                        if (products.Count > 0)
                        {
                            if (products.Count > maxProductsFound)
                            {
                                maxProductsFound = products.Count;
                                scoreEmailToSend = scoreEmail;
                                productsToSend = products;
                            }
                        }
                    }
                }

                if (!string.IsNullOrEmpty(scoreEmailToSend))
                {
                    var messageProducts = new List<MessageProductDto>();
                    int index = 1;
                    List<string> addedProducts = new();

                    foreach (var product in productsToSend)
                    {
                        if (!addedProducts.Contains(product.InternalProductId))
                        {
                            messageProducts.Add(new MessageProductDto
                            {
                                InternalProductId = product.InternalProductId, Score = product.Score,
                                RecommendedBy = new List<string> {"ProductScore"}, MerchantId = product.WebshopId
                            });

                            addedProducts.Add(product.InternalProductId);

                            index++;
                        }
                    }

                    var messageList = new List<MessageDto>
                    {
                        new MessageDto
                        {
                            Email = recipient,
                            EmailGuid = Guid.NewGuid(),
                            CampaignId = campaignId,
                            AutomationId = "1",
                            Origin = "automation",
                            IsTest = true,
                            ActionDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.UtcNow),
                            AutomationTrigger = "ProductScore",
                            MessageProducts = messageProducts
                        }
                    };

                    var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(messageList));

                    publishChannel.BasicPublish(exchange: "campaign",
                        routingKey: "campaign_mail_new",
                        basicProperties: null,
                        body: actionBody);

                    publishChannel.WaitForConfirmsOrDie(new TimeSpan(0, 0, 3));
                }

                if (internalProductId != string.Empty)
                {
                    var messageList = new List<MessageDto>
                    {
                        new MessageDto
                        {
                            Email = recipient,
                            EmailGuid = Guid.NewGuid(),
                            CampaignId = campaignId,
                            AutomationId = "1",
                            Origin = "automation",
                            ActionDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.UtcNow),
                            AutomationTrigger = "ProductScore",
                            MessageProducts = new List<MessageProductDto>(),
                            IsTest = true
                        }
                    };

                    for (int i = 0; i < 20; i++)
                    {
                        messageList.First().MessageProducts.Add(new MessageProductDto()
                        {
                            InternalProductId = internalProductId, Score = 100,
                            RecommendedBy = new List<string> {"ProductScore"}
                        });
                    }

                    var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(messageList));

                    publishChannel.BasicPublish(exchange: "campaign",
                        routingKey: "campaign_mail_new",
                        basicProperties: null,
                        body: actionBody);

                    publishChannel.WaitForConfirmsOrDie(new TimeSpan(0, 0, 3));
                }
            }
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error testing and queueing email action");
        }
    }


    public async Task<bool> GetCustomerCuratedProduct(string email, CancellationToken cancellationToken)
    {
        return true;
    }

    public async Task<List<MessageProductDto>> GetMlClusterRecommendations(string email,
        Dictionary<string, byte> relevantRecommendationsPerMerchant, string recommendedBy,
        CancellationToken cancellationToken)
    {
        var selectedProducts = new List<MessageProductDto>();

        var mlRecommendations = new List<CustomerProductRecommendation>();

        if (mlRecommendations != null)
        {
            foreach (var recommendation in mlRecommendations)
            {
                var webshopId = recommendation.RecommendationInfo.WebshopId.ToString(CultureInfo.InvariantCulture);

                if (relevantRecommendationsPerMerchant.ContainsKey(webshopId))
                {
                    var count = relevantRecommendationsPerMerchant[webshopId];

                    var sortedRecommendations = recommendation.RecommendationInfo.Recommendations
                        .Where(x => x.Score > 0.0m)
                        .Select(x => new {x.InternalProductId, x.Score, recommendation.RecommendationInfo.WebshopId})
                        .OrderByDescending(x => x.Score).ToArray();

                    if (sortedRecommendations.Length > 0)
                    {
                        for (int i = 0; i < count; i++)
                        {
                            if (sortedRecommendations.Length > i)
                            {
                                var product = sortedRecommendations[i];

                                selectedProducts.Add(new MessageProductDto
                                {
                                    InternalProductId = product.InternalProductId, MerchantId = webshopId,
                                    Score = product.Score, RecommendedBy = new List<string> {recommendedBy}
                                });
                            }
                        }
                    }
                }
            }
        }

        return selectedProducts;
    }


    private async Task<Dictionary<string, WebshopMerchantScore>> MerchantBuyScore(
        Dictionary<string, WebshopMerchantScore> merchantScores, string email, int lookbackDays, int initialBuyScore,
        Dictionary<string, string> scoreSettings, CancellationToken cancellationToken)
    {
        var scores = await automationElastic.GetMerchantBuyScore(elasticClient, email, lookbackDays, scoreSettings,
            cancellationToken);

        foreach (var score in scores)
        {
            if (!merchantScores.ContainsKey(score.ShopOrder.WebshopId))
                merchantScores.Add(score.ShopOrder.WebshopId,
                    new WebshopMerchantScore
                    {
                        WebshopId = score.ShopOrder.WebshopId, Score = score.Score + initialBuyScore,
                        InternalProductIdList = (score.ShopOrder.OrderItems != null
                            ? score.ShopOrder.OrderItems.Select(x => x.Internal_product_id ?? "").ToList()
                            : new List<String>())
                    });
            else
            {
                merchantScores[score.ShopOrder.WebshopId].Score += score.Score;
                merchantScores[score.ShopOrder.WebshopId].InternalProductIdList.AddRange(
                    (score.ShopOrder.OrderItems != null
                        ? score.ShopOrder.OrderItems.Select(x => x.Internal_product_id ?? "").ToList()
                        : new List<String>()));
            }
        }

        return merchantScores;
    }

    private async Task<Dictionary<string, WebshopMerchantScore>> MerchantClickScore(
        Dictionary<string, WebshopMerchantScore> merchantScores, string email, int lookbackDays,
        Dictionary<string, string> scoreSettings, CancellationToken cancellationToken)
    {
        var scores = await automationElastic.GetMerchantClickScore(elasticClient, email, lookbackDays, scoreSettings,
            cancellationToken);

        foreach (var score in scores)
        {
            if (!merchantScores.ContainsKey(score.ShopEventInfo.WebshopId))
                merchantScores.Add(score.ShopEventInfo.WebshopId,
                    new WebshopMerchantScore
                    {
                        WebshopId = score.ShopEventInfo.WebshopId, Score = score.Score,
                        InternalProductIdList = new List<String>()
                    });
            else
            {
                merchantScores[score.ShopEventInfo.WebshopId].Score += score.Score;
            }
        }

        return merchantScores;
    }
}