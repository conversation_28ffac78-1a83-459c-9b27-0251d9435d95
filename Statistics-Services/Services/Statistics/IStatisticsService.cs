using Shared.Dto.Dashboard;
using Shared.Dto.Discount;
using Shared.Models;
using Shared.Models.PartnerPortal;
using Statistics_Services.Models;

namespace Statistics_Services.Services.Statistics;

public interface IStatisticsService
{
    Task<PerformanceDashboardDto> PerformanceDashboard(int campaignId);
    Task<GeneralQueueStatsDto> GeneralQueueStats(int lookBackDays);
    Task<MerchantProfile> GetMerchantProfileAsync(int merchantId);
    Task<DashboardProfile> GetDashboardProfileAsync(int year, int month);

    Task<MerchantMonthlyPerformance> GetMerchantProfileMonthAsync(int merchantId, int month, int year);

    //Discount
    Task<List<DiscountStatisticsDto>> GetDiscountStatistics(int discountId);
}