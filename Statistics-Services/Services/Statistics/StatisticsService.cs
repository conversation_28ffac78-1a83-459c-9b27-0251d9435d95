#region

using Admin_Services.Models.ModelsDal.Invoice;
using Admin_Services.Services.Admin;
using Admin_Services.Services.Invoice;
using Audience.Services.Audience;
using Discount_Services.Services.Discounts;
using Invoice_Services.Models.ModelsDal.Invoice;
using Message_Services.Services.Message;
using Partner_Services.Services.General;
using Shared.Dto.Dashboard;
using Shared.Dto.Discount;
using Shared.Elastic.CampaignMailClick;
using Shared.Elastic.CampaignMailOpen;
using Shared.Elastic.DiscountOpen;
using Shared.Elastic.Elastic;
using Shared.Elastic.ShopProductsDisplays;
using Shared.Helpers.Converters;
using Shared.Models.Merchant;
using Shared.Models.PartnerPortal;
using Shared.Services.Partner;
using Statistics_Services.Models;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

#endregion

namespace Statistics_Services.Services.Statistics;

public class StatisticsService(
    IMerchantService merchantService,
    ILogger logger,
    IElasticService elasticService,
    ICustomerService customerService,
    IAdminService adminService,
    IMessageService messageService,
    IDiscountService discountService,
    IPartnerService partnerService,
    IInvoiceService invoiceService,
    IElasticCampaignMailOpenService elasticCampaignMailOpenService,
    IElasticDiscountOpenService elasticDiscountOpenService,
    IElasticCampaignMailClickService elasticCampaignMailClickService,
    IElasticShopProductDisplaysService elasticShopProductDisplaysService,
    IPartnerContext partnerContext)
    : IStatisticsService
{
    public async Task<PerformanceDashboardDto> PerformanceDashboard(int campaignId)
    {
        var delivered = await messageService.Messages(campaignId, "sent");
        var unsubscribe = await customerService.UnsubscribedCustomers(campaignId);
        decimal unsubscribeRate = 0;
        if (delivered != 0)
        {
            unsubscribeRate = Math.Round((decimal) unsubscribe / (decimal) delivered * 100, 2);
        }

        var invoiceLines = await adminService.InvoiceLinesAsync(campaignId);
        var performance = new PerformanceDashboardDto
        {
            AudienceSize = await messageService.Messages(campaignId),
            Delivered = delivered,
            Opens = await elasticCampaignMailOpenService.Opens(null, null, null, campaignId),
            UniqueOpens = await elasticCampaignMailOpenService.UniqueOpens(null, null, null, campaignId),
            Clicks = await elasticCampaignMailClickService.Clicks(null, null, null, campaignId),
            UniqueClicks = await elasticCampaignMailClickService.UniqueClicks(null, null, null, campaignId),
            OrderGenerated = invoiceLines
                .DistinctBy(a => a.OrderId)
                .Count(),
            OrderValueGenerated = invoiceLines.Sum(a => a.TotalPrice),
            OrdersValueUs = invoiceLines.Sum(a => a.TotalCut),
            Unsubscribes = unsubscribe,
            UnsubscribeRate = unsubscribeRate
        };
        return performance;
    }

    public async Task<GeneralQueueStatsDto> GeneralQueueStats(int lookBackDays)
    {
        var partnerId = partnerContext.PartnerId;
        var lookBackDay = DateTime.UtcNow.AddDays(-lookBackDays);
        var now = DateTime.UtcNow;
        var delivered =
            await messageService.MessagesDelivered(partnerId, lookBackDay);
        var failed = await messageService.MessagesFailed(lookBackDay);
        var queueSize = await messageService.MessagesQueueSize(lookBackDay);
        var onCooldown = await messageService.MessagesAction(now);
        var expired = await messageService.MessagesExpired(lookBackDay);
        var unsubscribe = await customerService.UnsubscribedCustomers(lookBackDay);
        decimal unsubscribeRate = 0;
        if (delivered != 0)
        {
            unsubscribeRate = Math.Round((decimal) unsubscribe / (decimal) delivered * 100, 2);
        }

        return new GeneralQueueStatsDto
        {
            Delivered = delivered,
            QueueSize = queueSize,
            Failed = failed,
            Expired = expired,
            OnCooldown = onCooldown,
            Unsubscribe = unsubscribe,
            UnsubscribeRate = unsubscribeRate
        };
    }

    public async Task<MerchantProfile> GetMerchantProfileAsync(int merchantId)
    {
        var year = DateTime.UtcNow.Year;
        var firstDayOfYear = new DateTime(year, 01, 01);
        var lastDayOfYear = new DateTime(year, 12, 31);

        var merchant = await merchantService.GetPaymentByIdAsync(merchantId);
        var customerPeriods = merchant.MerchantMeta
            .Where(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer)
            .Where(a => (a.EndDate == null || a.EndDate > firstDayOfYear))
            .OrderBy(a => a.StartDate)
            .ToList();

        List<OrderLine> invoiceLines1 = [];
        long totalExposures = 0;

        foreach (var customerPeriod in customerPeriods)
        {
            var from = customerPeriod.StartDate;
            var to = customerPeriod.EndDate;

            if (from < firstDayOfYear)
            {
                from = firstDayOfYear;
            }

            if (to != null)
            {
                to = to.Value.AddDays(-1);
            }

            if (to == null)
            {
                to = lastDayOfYear;
            }

            to = DateTimeExtensions.SetTimeToMax(to.Value);

            //Orders
            invoiceLines1.AddRange(await adminService.InvoiceLinesAsync(from.Value, to.Value, merchant.Id));

            //Revenue
            to = DateTimeExtensions.SetTimeToMax(to.Value);
            if (merchant.MerchantMeta
                    .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType)?.Value
                    .ToLowerInvariant() == MerchantAttributionTypes.CPM)
            {
                totalExposures += await elasticCampaignMailOpenService.Opens(from, to, merchantId);
            }
            else if (merchant.MerchantPayments.First().DisplayInvoice)
            {
                totalExposures += await elasticCampaignMailOpenService.Opens(from, to, merchantId);
            }
            else
            {
                totalExposures += await elasticCampaignMailClickService.Clicks(from, to, merchantId);
            }

            totalExposures += await elasticDiscountOpenService.Opens(from, to, merchantId);
        }

        //Wait 3 days to show orders
        /*var newer3Days = DateTime.UtcNow.AddDays(-3);
        invoiceLines1 = invoiceLines1.Where(a => a.OrderDate <= newer3Days).ToList();*/

        var latestOrdersList = invoiceLines1
            .GroupBy(o => o.OrderId).ToList();
        var invoiceLines = new List<OrderLine>();
        foreach (var latestOrders in latestOrdersList)
        {
            var skipLine = false;

            if (latestOrders.Last().ValyrionStatus == "fullRefund")
            {
                if (latestOrders.Last().OrderDate > latestOrders.Last().OrderLastModified.AddHours(-60))
                {
                    skipLine = true;
                }
            }

            if (!skipLine)
            {
                invoiceLines.Add(latestOrders.Last());
            }
        }

        var revenue = invoiceLines1.Sum(a => a.TotalPrice);
        var ordersGenerated = invoiceLines1.Select(o => o.OrderId).Distinct().Count();

        var latestOrders1 = invoiceLines
            .GroupBy(o => o.OrderId)
            .Select(g => g.OrderByDescending(o => o.OrderLastModified).First()).ToList();

        //Refunds
        var fullRefund = latestOrders1.Count(a => a.ValyrionStatus == "fullRefund");
        var partialRefunds = latestOrders1.Count(a => a.ValyrionStatus == "partialRefund");
        var totalReturns = fullRefund + partialRefunds;

        var merchantProfile = new MerchantProfile
        {
            WebshopName = merchant.DisplayName,
            Revenue = revenue,
            OrdersGenerated = ordersGenerated,
            TotalExposures = totalExposures,
            AverageOrderValue = ordersGenerated != 0 && revenue != 0 ? revenue / ordersGenerated : 0,
            FullReturns = fullRefund,
            PartialReturns = partialRefunds,
            TotalReturns = totalReturns,
            ReturnRate = totalReturns != 0 && ordersGenerated != 0 ? (decimal) totalReturns / ordersGenerated * 100 : 0,
        };

        return merchantProfile;
    }

    public async Task<DashboardProfile> GetDashboardProfileAsync(int year, int month)
    {
        var partnerId = partnerContext.PartnerId;
        var now = DateTime.UtcNow;
        var firstDayOfYear = new DateTime(year, 01, 01);
        var lastDayOfYear = new DateTime(year, 01, 01).AddYears(1).AddMicroseconds(-1);
        var firstDayOfMonth = new DateTime(year, month, 01);
        var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddMicroseconds(-1);
        var firstDayOfLastMonth = new DateTime(year, month, 01).AddMonths(-1);
        var lastDayOfLastMonth = firstDayOfLastMonth.AddMonths(1).AddMicroseconds(-1);
        var firstDayOfYearLastMonth = new DateTime(firstDayOfLastMonth.Year, 01, 01);
        
        var merchantIds = await merchantService.GetAllIdsByPartnerIdAsync();

        //Year
        var (revenueYear, cpaRevenueYear, cpmRevenueYear, baseFeeRevenueYear) = await invoiceService.CalculateRevenueByPeriodAsync(firstDayOfYear, lastDayOfYear);
        var invoiceLinesYear = await adminService.InvoiceLinesByPartnerIdAsync(firstDayOfYear, partnerId);
        var invoiceLinesYearLast = await adminService.InvoiceLinesByPartnerIdAsync(firstDayOfYearLastMonth, partnerId);
        var ordersGeneratedYear = invoiceLinesYear.Select(o => o.OrderId).Distinct().Count();
        var totalExposuresYear = await elasticCampaignMailOpenService.ExposuresByMerchantIds(firstDayOfYear, lastDayOfYear, merchantIds);
        totalExposuresYear += await elasticDiscountOpenService.ExposuresByMerchantIds(firstDayOfYear, lastDayOfYear, merchantIds);
        totalExposuresYear += await elasticShopProductDisplaysService.ExposuresByMerchantIds(firstDayOfYear, lastDayOfYear, merchantIds);
        var totalDisplaysYear = await elasticCampaignMailOpenService.OpensByMerchantIds(firstDayOfYear, lastDayOfYear, merchantIds);
        totalDisplaysYear += await elasticDiscountOpenService.OpensByMerchantIds(firstDayOfYear, lastDayOfYear, merchantIds);
        totalDisplaysYear += await elasticShopProductDisplaysService.DisplaysByMerchantIds(firstDayOfYear, lastDayOfYear, merchantIds);
        var messageSentYear = await messageService.MessagesDelivered(partnerId, firstDayOfYear);
        var merchants = await merchantService.GetAllCustomersAsync();
        
        var bestMerchantYear = await adminService.FetchBestMerchantAsync(merchantIds, firstDayOfYear);
        if (bestMerchantYear.MerchantId != 0)
        {
            bestMerchantYear.MerchantName = merchants.SingleOrDefault(a => a.Id == bestMerchantYear.MerchantId)?.DisplayName ?? string.Empty;
            bestMerchantYear.TotalExposures = await elasticCampaignMailOpenService.Opens(firstDayOfYear, null, bestMerchantYear.MerchantId);
            bestMerchantYear.TotalExposures += await elasticDiscountOpenService.Opens(firstDayOfYear, null, bestMerchantYear.MerchantId);
            bestMerchantYear.TotalExposures += await elasticShopProductDisplaysService.Displays(firstDayOfYear, null, bestMerchantYear.MerchantId);   
        }
        //Month
        var (revenueMonth, cpaRevenueMonth, cpmRevenueMonth, baseFeeRevenueMonth) = await invoiceService.CalculateRevenueByPeriodAsync(firstDayOfMonth, lastDayOfMonth);
        var invoiceLinesMonth = invoiceLinesYear
            .Where(a => a.OrderDate >= firstDayOfMonth && a.OrderDate <= lastDayOfMonth).ToList();
        var ordersGeneratedMonth = invoiceLinesMonth.Select(o => o.OrderId).Distinct().Count();
        var messageSentMonth = await messageService.MessagesDelivered(partnerId, firstDayOfMonth);
        var totalExposuresMonth = await elasticCampaignMailOpenService.ExposuresByMerchantIds(firstDayOfMonth, lastDayOfMonth, merchantIds);
        totalExposuresMonth += await elasticDiscountOpenService.ExposuresByMerchantIds(firstDayOfMonth, lastDayOfMonth, merchantIds);
        totalExposuresMonth += await elasticShopProductDisplaysService.ExposuresByMerchantIds(firstDayOfMonth, lastDayOfMonth, merchantIds);
        var totalDisplaysMonth = await elasticCampaignMailOpenService.OpensByMerchantIds(firstDayOfMonth, lastDayOfMonth, merchantIds);
        totalDisplaysMonth += await elasticDiscountOpenService.OpensByMerchantIds(firstDayOfMonth, lastDayOfMonth, merchantIds);
        totalDisplaysMonth += await elasticShopProductDisplaysService.DisplaysByMerchantIds(firstDayOfMonth, lastDayOfMonth, merchantIds);
        var bestMerchantMonth = await adminService.FetchBestMerchantAsync(merchantIds, firstDayOfMonth, lastDayOfMonth);
        if (bestMerchantMonth.MerchantId != 0)
        {
            bestMerchantMonth.MerchantName = merchants.SingleOrDefault(a => a.Id == bestMerchantMonth.MerchantId)?.DisplayName ?? string.Empty;
            bestMerchantMonth.TotalExposures = await elasticCampaignMailOpenService.Opens(firstDayOfMonth, lastDayOfMonth, bestMerchantMonth.MerchantId);
            bestMerchantMonth.TotalExposures += await elasticDiscountOpenService.Opens(firstDayOfMonth, lastDayOfMonth, bestMerchantMonth.MerchantId);
            bestMerchantMonth.TotalExposures += await elasticShopProductDisplaysService.Displays(firstDayOfMonth, lastDayOfMonth, bestMerchantMonth.MerchantId);   
        }
        
        //Last month
        var invoiceLinesLastMonth = invoiceLinesYearLast
            .Where(a => a.OrderDate.Month == firstDayOfLastMonth.Month).ToList();

        decimal accumulatedCurrentMonth = 0;
        decimal accumulatedLastMonth = 0;
        var revenueGraph = new DashboardProfileRevenueGraph();
        revenueGraph.Month.Add(0);
        revenueGraph.LastMonth.Add(0);
        
        // Calculate revenue per day including CPM and Base Fee
        var currentMonthRevenue = await invoiceService.CalculateRevenuePerDayByPeriodAsync(firstDayOfMonth, lastDayOfMonth);
        var lastMonthRevenue = await invoiceService.CalculateRevenuePerDayByPeriodAsync(firstDayOfLastMonth, lastDayOfLastMonth);
        
        // Build revenue graph with accumulated values
        for (int i = 0; i < currentMonthRevenue.Count; i++)
        {
            accumulatedCurrentMonth += currentMonthRevenue[i];
            revenueGraph.Month.Add(accumulatedCurrentMonth);
            
            // Make sure we don't go out of bounds for last month (which might have different number of days)
            if (i < lastMonthRevenue.Count)
            {
                accumulatedLastMonth += lastMonthRevenue[i];
                revenueGraph.LastMonth.Add(accumulatedLastMonth);
            }
        }
        
        // If current month has fewer days than last month, complete the last month data
        if (currentMonthRevenue.Count < lastMonthRevenue.Count)
        {
            for (int i = currentMonthRevenue.Count; i < lastMonthRevenue.Count; i++)
            {
                accumulatedLastMonth += lastMonthRevenue[i];
                revenueGraph.LastMonth.Add(accumulatedLastMonth);
            }
        }

        //Akk exposures
        accumulatedCurrentMonth = 0;
        accumulatedLastMonth = 0;
        var exposuresGraph = new DashboardProfileExposuresGraph();
        exposuresGraph.Month.Add(0);
        exposuresGraph.LastMonth.Add(0);

        //Current month
        var exposuresGraphDatas =
            await elasticCampaignMailOpenService.OpensDayCountByMerchantIds(firstDayOfMonth, lastDayOfMonth, merchantIds);
        var exposuresDiscountGraphDatas =
            await elasticDiscountOpenService.OpensDayCountByMerchantIds(firstDayOfMonth, lastDayOfMonth, merchantIds);
        var exposuresShopProductDisplaysGraphDatas =
            await elasticShopProductDisplaysService.DisplaysDayCountByMerchantIds(firstDayOfMonth, lastDayOfMonth, merchantIds);

        //Last month
        var exposuresGraphDatasLastMonth =
            await elasticCampaignMailOpenService.OpensDayCountByMerchantIds(firstDayOfLastMonth, lastDayOfLastMonth, merchantIds);
        var exposuresDiscountGraphDatasLastMonth =
            await elasticDiscountOpenService.OpensDayCountByMerchantIds(firstDayOfLastMonth, lastDayOfLastMonth, merchantIds);
        var exposuresShopProductDisplaysGraphDatasLastMonth =
            await elasticShopProductDisplaysService.DisplaysDayCountByMerchantIds(firstDayOfLastMonth, lastDayOfLastMonth, merchantIds);
        foreach (var exposuresGraphData in exposuresGraphDatas)
        {
            accumulatedCurrentMonth += exposuresGraphData.Value;
            accumulatedCurrentMonth += exposuresDiscountGraphDatas
                .SingleOrDefault(a => a.Key.Day == exposuresGraphData.Key.Day).Value;
            exposuresGraph.Month.Add(accumulatedCurrentMonth);

            var data = exposuresGraphDatasLastMonth.SingleOrDefault(a => a.Key.Day == exposuresGraphData.Key.Day);
            if (data.Key != default)
            {
                accumulatedLastMonth += data.Value;
            }

            //Discount
            data = exposuresDiscountGraphDatasLastMonth.SingleOrDefault(a => a.Key.Day == exposuresGraphData.Key.Day);
            if (data.Key != default)
            {
                accumulatedLastMonth += data.Value;
            }

            exposuresGraph.LastMonth.Add(accumulatedLastMonth);
        }

        Console.WriteLine((DateTime.UtcNow - now).Seconds + " exposuresGraph");
        //MQL Graph
        var mqlGraphGraphDatas = await elasticService.CustomersAudienceCount(partnerId, firstDayOfMonth, lastDayOfMonth);
        // Not in use and Elastic Index Removed
        //var totalRevenue = await _elasticService.CustomerOrdersRevenueElastic(partnerId, firstDayOfYear);
        // Temporarily Disabled
        //var liveDiscounts = await _discountService.GetDiscountActive(partnerId);
        // Temporarily Disabled
        //var discountEvents = await _elasticService.DiscountEventsElastic("discount_overview_viewed", firstDayOfMonth);
        var audienceExposures = await elasticCampaignMailOpenService.UniqueExposuresByMerchantIds(firstDayOfMonth, lastDayOfMonth, merchantIds);
        var partnerName = await partnerService.GetPartnerName();
        DashboardProfile viabillProfile;
        try
        {
            viabillProfile = new DashboardProfile
            {
                RevenueYear = revenueYear,
                RevenueDrivenYear = invoiceLinesYear.Sum(a => a.TotalPrice),
                OrdersYear = ordersGeneratedYear,
                TotalExposuresYear = totalExposuresYear,
                TotalDisplaysYear = totalDisplaysYear,
                MailSentYear = messageSentYear,
                MerchantsYear = merchants.Count,
                TotalRevenueYear = 0,

                CouponLive = 0,
                CouponEvents = 0,

                Revenue = revenueMonth,
                RevenueDriven = invoiceLinesMonth.Sum(a => a.TotalPrice),
                AverageOrder = invoiceLinesMonth.Count != 0
                    ? invoiceLinesMonth.Sum(a => a.TotalPrice) / invoiceLinesMonth.Count
                    : 0,
                Orders = ordersGeneratedMonth,
                TotalExposures = totalExposuresMonth,
                AudienceExposures = audienceExposures,
                TotalDisplays = totalDisplaysMonth,
                MailSent = messageSentMonth,
                RevenueGraph = revenueGraph,
                ExposuresGraph = exposuresGraph,
                MqlGraph = mqlGraphGraphDatas,
                BestMerchantYearToDate = bestMerchantYear,
                BestMerchantMonth = bestMerchantMonth,
                PartnerName = partnerName
            };
        }
        catch (Exception ex)
        {
            logger.Error(ex, "An error occurred while initializing DashboardProfile: {Message}", ex.Message);
            throw;
        }

        return viabillProfile;
    }

    public async Task<MerchantMonthlyPerformance> GetMerchantProfileMonthAsync(int merchantId, int month, int year)
    {
        var merchant = await merchantService.GetPaymentByIdAsync(merchantId);

        var firstDayOfMonth = new DateTime(year, month, 01);
        var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddMicroseconds(-1);
        var oldestDayAllowed = new DateTime(2023, 05, 01);
        var firstDayOfLastMonth = new DateTime(year, month, 1).AddMonths(-1);
        var lastDayOfLastMonth = firstDayOfLastMonth.AddMonths(1).AddMicroseconds(-1);
        var now = DateTime.UtcNow;

        var customerPeriods = merchant.MerchantMeta
            .Where(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer)
            .Where(a => (a.EndDate == null || a.EndDate > firstDayOfMonth) && a.StartDate <= lastDayOfMonth)
            .OrderBy(a => a.StartDate)
            .ToList();

        var customerPeriodsLastMonth = merchant.MerchantMeta
            .Where(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer)
            .Where(a => (a.EndDate == null || a.EndDate > firstDayOfLastMonth) && a.StartDate <= lastDayOfLastMonth)
            .OrderBy(a => a.StartDate)
            .ToList();

        List<OrderLine> invoiceLines1 = new List<OrderLine>();
        List<OrderLine> invoiceLinesLast1 = new List<OrderLine>();

        //This month
        foreach (var customerPeriod in customerPeriods)
        {
            var from = customerPeriod.StartDate;
            var to = customerPeriod.EndDate;

            if (from < firstDayOfMonth)
            {
                from = firstDayOfMonth;
            }

            if (to != null)
            {
                to = to.Value.AddDays(-1);
            }

            if (to == null || to > lastDayOfMonth)
            {
                to = lastDayOfMonth;
            }

            to = DateTimeExtensions.SetTimeToMax(to.Value);

            invoiceLines1.AddRange(await adminService.InvoiceLinesAsync(merchant.Id, from.Value, to.Value));
        }

        //Last month
        foreach (var customerPeriodLastMonth in customerPeriodsLastMonth)
        {
            var from = customerPeriodLastMonth.StartDate;
            var to = customerPeriodLastMonth.EndDate;

            if (from < firstDayOfLastMonth)
            {
                from = firstDayOfLastMonth;
            }

            if (to != null)
            {
                to = to.Value.AddDays(-1);
            }

            if (to == null || to > lastDayOfLastMonth)
            {
                to = lastDayOfLastMonth;
            }

            if (now.Year == year && now.Month == month)
            {
                invoiceLinesLast1.AddRange(await adminService.InvoiceLinesAsync(oldestDayAllowed, merchant.Id,
                    from.Value,
                    to.Value, now.Day));
            }
            else
            {
                invoiceLinesLast1.AddRange(await adminService.InvoiceLinesAsync(merchant.Id, from.Value,
                    to.Value));
            }
        }


        //var invoiceLines1 =
        //    await _adminService.InvoiceLinesAsync(merchant.Id, firstDayOfMonth, lastDayOfMonth);

        //List<OrderLine> invoiceLinesLast1;
        //var now = DateTime.UtcNow;
        /*if (now.Year == year && now.Month == month)
        {
            invoiceLinesLast1 = await _adminService.InvoiceLinesAsync(oldestDayAllowed, merchant.Id,
                firstDayOfLastMonth.Month,
                firstDayOfLastMonth.Year, now.Day);
        }
        else
        {
            invoiceLinesLast1 =
                await _adminService.InvoiceLinesAsync(merchant.Id, firstDayOfLastMonth,
                    lastDayOfLastMonth);
        }*/

        //Wait 3 days to show orders
        var newer3Days = DateTime.UtcNow.AddDays(-3);
        invoiceLines1 = invoiceLines1.Where(a => a.OrderDate <= newer3Days).ToList();
        invoiceLinesLast1 = invoiceLinesLast1.Where(a => a.OrderDate <= newer3Days).ToList();

        //Invoice
        var latestOrdersList = invoiceLines1
            .GroupBy(o => o.OrderId).ToList();
        var invoiceLines = new List<OrderLine>();
        foreach (var latestOrders in latestOrdersList)
        {
            var skipLine = false;

            if (latestOrders.Last().ValyrionStatus == "fullRefund")
            {
                if (latestOrders.Last().OrderDate > latestOrders.Last().OrderLastModified.AddHours(-60))
                {
                    skipLine = true;
                }
            }

            if (!skipLine)
            {
                invoiceLines.Add(latestOrders.Last());
            }
        }

        //Invoice Last
        var latestOrdersListLast = invoiceLinesLast1
            .GroupBy(o => o.OrderId).ToList();
        var invoiceLinesLast = new List<OrderLine>();
        foreach (var latestOrders in latestOrdersListLast)
        {
            var skipLine = false;

            if (latestOrders.Last().ValyrionStatus == "fullRefund")
            {
                if (latestOrders.Last().OrderDate > latestOrders.Last().OrderLastModified.AddHours(-60))
                {
                    skipLine = true;
                }
            }

            if (!skipLine)
            {
                invoiceLinesLast.Add(latestOrders.Last());
            }
        }

        var revenue = invoiceLines1.Sum(a => a.TotalPrice);
        var revenueLastMonth = invoiceLinesLast1.Sum(a => a.TotalPrice);
        var ordersGenerated = invoiceLines.Select(o => o.OrderId).Distinct().Count();
        var ordersGeneratedLastMonth = invoiceLinesLast.Select(o => o.OrderId).Distinct().Count();

        //Refunds
        var latestOrders1 = invoiceLines
            .GroupBy(o => o.OrderId)
            .Select(g => g.OrderByDescending(o => o.OrderLastModified).First()).ToList();
        var fullRefund = latestOrders1.Count(a => a.ValyrionStatus == "fullRefund");
        var partialRefunds = latestOrders1.Count(a => a.ValyrionStatus == "partialRefund");


        //Orders
        var recentSaleOrderDtos = new List<SaleOrderDto>();
        foreach (var latestOrders in latestOrdersList)
        {
            decimal value = 0;
            var viaAdsStauts = "";
            var skipLine = false;
            foreach (var latestOrder in latestOrders)
            {
                value += latestOrder.TotalPrice;
                viaAdsStauts = latestOrder.ValyrionStatus;
            }

            var customerName = latestOrders.First().CustomerName ?? "";
            if (latestOrders.First().CustomerName is "n/a" or null or "")
            {
                customerName = latestOrders.First().Email;
            }

            switch (viaAdsStauts)
            {
                case "fullRefund":
                    viaAdsStauts = "Full return";
                    if (latestOrders.Last().OrderDate > latestOrders.Last().OrderLastModified.AddHours(-60))
                    {
                        skipLine = true;
                    }

                    break;
                case "partialRefund":
                    viaAdsStauts = "Partial return";
                    break;
                default:
                    viaAdsStauts = "Paid";
                    break;
            }

            if (!skipLine)
            {
                recentSaleOrderDtos.Add(new SaleOrderDto
                {
                    OrderId = latestOrders.First().OrderId,
                    OrderDate = latestOrders.First().OrderDate,
                    CustomerName = customerName,
                    ExposureDate = latestOrders.First().ExposureDate,
                    OriginalAmount = latestOrders.First().TotalPrice,
                    RefundedAmount = latestOrders.First().TotalPrice - value,
                    TotalExVat = value,
                    ViaAdsStatus = viaAdsStauts
                });
            }
        }

        //Exposures
        long totalExposures = 0;
        long totalExposuresMail = 0;
        long totalExposuresDiscount = 0;
        long audienceExposed = 0;

        var exposuresGraph = new DashboardProfileRevenueGraph();
        decimal accumulatedCurrentMonth = 0;
        decimal accumulatedLastMonth = 0;

        exposuresGraph.Month.Add(0);
        exposuresGraph.LastMonth.Add(0);

        Dictionary<DateTime, long> exposuresGraphDatas = new Dictionary<DateTime, long>();
        Dictionary<DateTime, long> exposuresGraphDatasLastMonth = new Dictionary<DateTime, long>();
        Dictionary<DateTime, long> exposuresDiscountGraphDatas = new Dictionary<DateTime, long>();
        Dictionary<DateTime, long> exposuresDiscountGraphDatasLastMonth = new Dictionary<DateTime, long>();

        //This month customerPeriods
        foreach (var customerPeriod in customerPeriods)
        {
            var from = customerPeriod.StartDate;
            var to = customerPeriod.EndDate;

            if (from < firstDayOfMonth)
            {
                from = firstDayOfMonth;
            }

            if (to != null)
            {
                to = to.Value.AddDays(-1);
            }

            if (to == null || to > lastDayOfMonth)
            {
                to = lastDayOfMonth;
            }

            to = DateTimeExtensions.SetTimeToMax(to.Value);

            if (merchant.MerchantMeta
                    .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType)?.Value
                    .ToLowerInvariant() == MerchantAttributionTypes.CPM)
            {
                totalExposures += await elasticCampaignMailOpenService.Opens(from, to, merchant.Id);
                audienceExposed +=
                    await elasticCampaignMailOpenService.UniqueOpens(from, to, merchant.Id);
            }
            else if (merchant.MerchantPayments.First().DisplayInvoice)
            {
                totalExposures += await elasticCampaignMailOpenService.Opens(from, to, merchant.Id);
                audienceExposed +=
                    await elasticCampaignMailOpenService.UniqueOpens(from, to, merchant.Id);
            }
            else
            {
                totalExposures +=
                    await elasticCampaignMailClickService.Clicks(from, to, merchant.Id);
                audienceExposed +=
                    await elasticCampaignMailClickService.UniqueClicks(from, to, merchant.Id);
            }

            totalExposuresMail = totalExposures;

            //Discount
            totalExposuresDiscount += await elasticDiscountOpenService.Opens(from, to, merchant.Id);
            totalExposures += totalExposuresDiscount;
            audienceExposed += await elasticDiscountOpenService.UniqueOpens(from, to, merchant.Id);

            if (merchant.MerchantMeta
                    .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType)?.Value
                    .ToLowerInvariant() == MerchantAttributionTypes.CPM)
            {
                foreach (var value in await elasticCampaignMailOpenService.OpensDayCount(from.Value, to.Value,
                             merchant.Id))
                {
                    if (!exposuresGraphDatas.ContainsKey(value.Key))
                    {
                        exposuresGraphDatas[value.Key] = value.Value;
                    }
                }
            }
            else if (merchant.MerchantPayments.First().DisplayInvoice)
            {
                foreach (var value in await elasticCampaignMailOpenService.OpensDayCount(from.Value, to.Value,
                             merchant.Id))
                {
                    if (!exposuresGraphDatas.ContainsKey(value.Key))
                    {
                        exposuresGraphDatas[value.Key] = value.Value;
                    }
                }
            }
            else
            {
                foreach (var value in await elasticCampaignMailClickService.ClicksDayCount(from.Value, to.Value,
                             merchant.Id))
                {
                    if (!exposuresGraphDatas.ContainsKey(value.Key))
                    {
                        exposuresGraphDatas[value.Key] = value.Value;
                    }
                }
            }

            foreach (var value in await elasticDiscountOpenService.OpensDayCount(from.Value, to.Value, merchant.Id))
            {
                if (!exposuresDiscountGraphDatas.ContainsKey(value.Key))
                {
                    exposuresDiscountGraphDatas[value.Key] = value.Value;
                }
            }
        }

        //Last month customerPeriods
        foreach (var customerPeriod in customerPeriodsLastMonth)
        {
            var from = customerPeriod.StartDate;
            var to = customerPeriod.EndDate;

            if (from < firstDayOfLastMonth)
            {
                from = firstDayOfLastMonth;
            }

            if (to != null)
            {
                to = to.Value.AddDays(-1);
            }

            if (to == null || to > lastDayOfLastMonth)
            {
                to = lastDayOfLastMonth;
            }

            if (merchant.MerchantMeta
                    .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType)?.Value
                    .ToLowerInvariant() == MerchantAttributionTypes.CPM)
            {
                foreach (var value in await elasticCampaignMailOpenService.OpensDayCount(from.Value, to.Value,
                             merchant.Id))
                {
                    if (!exposuresGraphDatasLastMonth.ContainsKey(value.Key))
                    {
                        exposuresGraphDatasLastMonth[value.Key] = value.Value;
                    }
                }
            }
            else if (merchant.MerchantPayments.First().DisplayInvoice)
            {
                foreach (var value in await elasticCampaignMailOpenService.OpensDayCount(from.Value, to.Value,
                             merchant.Id))
                {
                    if (!exposuresGraphDatasLastMonth.ContainsKey(value.Key))
                    {
                        exposuresGraphDatasLastMonth[value.Key] = value.Value;
                    }
                }
            }
            else
            {
                foreach (var value in await elasticCampaignMailClickService.ClicksDayCount(from.Value, to.Value,
                             merchant.Id))
                {
                    if (!exposuresGraphDatasLastMonth.ContainsKey(value.Key))
                    {
                        exposuresGraphDatasLastMonth[value.Key] = value.Value;
                    }
                }
            }

            foreach (var value in await elasticDiscountOpenService.OpensDayCount(from.Value, to.Value, merchant.Id))
            {
                if (!exposuresDiscountGraphDatasLastMonth.ContainsKey(value.Key))
                {
                    exposuresDiscountGraphDatasLastMonth[value.Key] = value.Value;
                }
            }
        }


        /*if (merchant.MerchantMeta
                .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType)?.Value
                .ToLowerInvariant() == MerchantAttributionTypes.CPM)
        {
            //exposuresGraphDatas =
             //   await _elasticCampaignMailOpenService.OpensDayCount(firstDayOfMonth, lastDayOfMonth, merchant.Id);
            //Last month
            exposuresGraphDatasLastMonth =
                await _elasticCampaignMailOpenService.OpensDayCount(firstDayOfLastMonth, lastDayOfLastMonth, merchant.Id);
        }
        else if (merchant.MerchantPayments.First().DisplayInvoice)
        {
            //exposuresGraphDatas =
            //    await _elasticCampaignMailOpenService.OpensDayCount(firstDayOfMonth, lastDayOfMonth, merchant.Id);
            //Last month
            exposuresGraphDatasLastMonth =
                await _elasticCampaignMailOpenService.OpensDayCount(firstDayOfLastMonth, lastDayOfLastMonth, merchant.Id);
        }
        else
        {
            //exposuresGraphDatas =
            //    await _elasticCampaignMailClickService.ClicksDayCount(firstDayOfMonth, lastDayOfMonth, merchant.Id);
            //Last month
            exposuresGraphDatasLastMonth =
                await _elasticCampaignMailClickService.ClicksDayCount(firstDayOfLastMonth, lastDayOfLastMonth, merchant.Id);
        }*/

        //exposuresDiscountGraphDatas = await _elasticDiscountOpenService.OpensDayCount(firstDayOfMonth, lastDayOfMonth, merchant.Id);


        //Last month
        //var exposuresDiscountGraphDatasLastMonth =
        //    await _elasticDiscountOpenService.OpensDayCount(firstDayOfLastMonth, lastDayOfLastMonth, merchant.Id);

        int daysInMonth = DateTime.DaysInMonth(year, month);
        for (int day = 1; day <= daysInMonth; day++)
        {
            var currentDayExposure = exposuresGraphDatas.SingleOrDefault(a => a.Key.Day == day);
            var currentDayExposureDiscount = exposuresDiscountGraphDatas.SingleOrDefault(a => a.Key.Day == day);

            // Accumulate for current month
            if (currentDayExposure.Key != default)
                accumulatedCurrentMonth += currentDayExposure.Value;

            if (currentDayExposureDiscount.Key != default)
                accumulatedCurrentMonth += currentDayExposureDiscount.Value;

            exposuresGraph.Month.Add(accumulatedCurrentMonth);

            // Accumulate for last month
            var lastMonthExposure = exposuresGraphDatasLastMonth.SingleOrDefault(a => a.Key.Day == day);
            var lastMonthExposureDiscount = exposuresDiscountGraphDatasLastMonth.SingleOrDefault(a => a.Key.Day == day);

            if (lastMonthExposure.Key != default)
                accumulatedLastMonth += lastMonthExposure.Value;

            if (lastMonthExposureDiscount.Key != default)
                accumulatedLastMonth += lastMonthExposureDiscount.Value;

            exposuresGraph.LastMonth.Add(accumulatedLastMonth);
        }


        var merchantMonthlyPerformance = new MerchantMonthlyPerformance
        {
            Revenue = revenue,
            RevenueLastMonth = revenueLastMonth,
            OrdersGenerated = ordersGenerated,
            OrdersGeneratedLastMonth = ordersGeneratedLastMonth,
            AverageOrderValue = ordersGenerated != 0 && revenue != 0 ? revenue / ordersGenerated : 0,
            AverageOrderValueLastMonth = ordersGeneratedLastMonth != 0 && revenueLastMonth != 0
                ? revenueLastMonth / ordersGeneratedLastMonth
                : 0,
            PartialReturns = partialRefunds,
            FullReturns = fullRefund,
            TotalExposures = totalExposures,
            AudienceExposed = audienceExposed,
            SaleOrdersCurrentMonth = recentSaleOrderDtos,
            ExposuresGraph = exposuresGraph,
            TotalExposuresEmail = totalExposuresMail,
            TotalExposuresDiscount = totalExposuresDiscount
        };


        return merchantMonthlyPerformance;
    }

    public async Task<List<DiscountStatisticsDto>> GetDiscountStatistics(int discountId)
    {
        var discountStatisticsDtos = new List<DiscountStatisticsDto>();
        var discount = await discountService.GetDiscountWithChildren(discountId);
        /*var open = await _elasticService.DiscountEventsElastic(discount.Id, "discount_overview_viewed");
        var click = await _elasticService.DiscountEventsElastic(discount.Id, "discount_details_viewed");
        var redirect = await _elasticService.DiscountEventsRedirectElastic(discount.Id);
        discountStatisticsDtos.Add(new DiscountStatisticsDto
        {
            DiscountId = discountId,
            Clicks = click,
            Opens = open,
            Redirects = redirect,
        });*/

        foreach (var discountChild in discount.InverseFkParent)
        {
            var opens = await elasticService.DiscountEventsElastic(discountChild.Id, "discount_overview_viewed");
            var clicks = await elasticService.DiscountEventsElastic(discountChild.Id, "discount_details_viewed");
            var redirects = await elasticService.DiscountEventsRedirectElastic(discountChild.Id);
            discountStatisticsDtos.Add(new DiscountStatisticsDto
            {
                DiscountId = discountChild.Id,
                Clicks = clicks,
                Opens = opens,
                Redirects = redirects,
            });
        }

        return discountStatisticsDtos;
    }
}