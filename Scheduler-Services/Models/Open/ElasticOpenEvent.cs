// ReSharper disable All

using Marlin_OS_Integration_API.Models.Open;
using Nest;

namespace Marlin_OS_Viabill_API.ModelsDto.WebAds
{
    public class ElasticOpenEvent
    {
        [Date(Name = "@timestamp")] public DateTime Event_received { get; set; }
        [Object(Name = "Shop_event")] public ShopEventOpen? Shop_event { get; set; }
        [Object(Name = "Customer")] public CustomerOpen? Customer { get; set; }
        [Object(Name = "UserAgent")] public UserAgentOpenDto? UserAgent { get; set; }
        public ClientOpenDto? client { get; set; }
    }
}