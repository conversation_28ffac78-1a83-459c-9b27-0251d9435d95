<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <RootNamespace>Scheduler_Services</RootNamespace>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Customer-Services\Customer-Services.csproj"/>
        <ProjectReference Include="..\Message-Services\Message-Services.csproj"/>
        <ProjectReference Include="..\Shared\Shared.csproj"/>
        <ProjectReference Include="..\Partner-Services\Partner-Services.csproj"/>
        <ProjectReference Include="..\Merchant-Services\Merchant-Services.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Azure.Storage.Blobs" Version="12.20.0"/>
        <PackageReference Include="Google.Apis.Gmail.v1" Version="1.67.0.3287"/>
        <PackageReference Include="NUglify" Version="1.21.7"/>
    </ItemGroup>

</Project>