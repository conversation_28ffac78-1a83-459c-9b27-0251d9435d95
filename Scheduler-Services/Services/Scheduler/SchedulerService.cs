#region

using System.Collections.Concurrent;
using System.IdentityModel.Tokens.Jwt;
using System.Runtime.ExceptionServices;
using System.Text;
using System.Text.RegularExpressions;
using Audience.Services.Audience;
using Azure.Storage.Blobs;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Gmail.v1;
using Google.Apis.Gmail.v1.Data;
using Google.Apis.Services;
using Message_Services.Services.Message;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using NUglify;
using Partner_Services.Services.PartnerData;
using Renci.SshNet;
using Scheduler_Services.Models.GreyNumber;
using Scheduler_Services.Models.Unsubscribe;
using Shared.Elastic.CampaignMailOpen;
using Shared.Models;
using ILogger = Serilog.ILogger;

#endregion

namespace Scheduler_Services.Services.Scheduler;

public class SchedulerService(
    ILogger logger,
    IConfiguration configuration,
    ICustomerService customerService,
    IMessageService messageService,
    IElasticCampaignMailOpenService elasticCampaignMailOpenService,
    IPartnerDataService partnerDataService)
    : ISchedulerService
{
    private static readonly string[] Scopes = {GmailService.Scope.GmailModify};
    private const string ApplicationName = "Unsubscribe Service";
    private const string UserEmail = "<EMAIL>";

    // This regex is used for a simple email validation
    private static readonly Regex EmailRegex =
        new(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.Compiled | RegexOptions.IgnoreCase);

    // TODO ASK LUKAS IF GREYNUMBER STILL IS NEEDE BEFORE DELETING
    /*public async Task<ResponseDto> GreyNumber()
    {
        var greyNumbers = new ConcurrentBag<GreyNumber>();
        var index = "customers-exposures";
        var count = 0;
        var now = DateTime.UtcNow;
        var old = DateTime.UtcNow.AddYears(-1);

        var contacts = await _customerService.GetAllConsentAsync();
        var webshops = await _merchantService.GetAsync().ConfigureAwait(false);

        var to = DateTime.UtcNow;
        var from = to.AddDays(-(180 - 1)).AddTicks(-1);

        await Parallel.ForEachAsync(contacts,
            new ParallelOptions
                {MaxDegreeOfParallelism = Convert.ToInt32(Math.Ceiling((Environment.ProcessorCount * 0.75) * 1.0))},
            async (contact, stoppingToken) =>
            {
                count++;
                Console.WriteLine(count);
                if (contact.PhoneNumber == "")
                {
                    contact.PhoneNumber = "999999999999999999999";
                }

                //Get all orders from elasticSearch
                var orderSearchResult =
                    await _elasticService.CustomerOrdersElastic(contact.Email, contact.PhoneNumber, 3);
                var orderSearchResultViabill = await _elasticService.ViaBillOrdersElastic(contact.Email, 3);

                if (orderSearchResult.Count > 0 || orderSearchResultViabill.Count > 0)
                {
                    //Opens
                    var campaignOpens =
                        //(await _elasticService.CampaignOpenElastic(contact.Email, 180).ConfigureAwait(false));
                        (await _elasticCampaignMailOpenService.OpensData(from, to, contact.Email, null)
                            .ConfigureAwait(false));
                    //Click
                    //var campaignClicks = await _elasticService.CampaignClickElastic(contact.Email, 180).ConfigureAwait(false);
                    var campaignClicks = await _elasticCampaignMailClickService
                        .ClicksData(from, to, contact.Email, null)
                        .ConfigureAwait(false);

                    //Customer order
                    foreach (var orderResponse in orderSearchResult.OrderByDescending(a => a.Order_date))
                    {
                        if (orderResponse.Shop_order != null && orderResponse.Shop_order.Webshop_id != null)
                        {
                            if (!greyNumbers.Any(a => a.Customer.Email == contact.Email &&
                                                      a.Shop.Webshop_id == orderResponse.Shop_order.Webshop_id))
                            {
                                var campaignClicks1 = campaignClicks.Where(a =>
                                    a.Shop_event.Webshop_id == orderResponse.Shop_order.Webshop_id).ToList();

                                var campaignOpens1 = campaignOpens.Where(a =>
                                    //a.Shop_event.Webshop_id == orderResponse.Shop_order.Webshop_id).ToList();
                                    a.Shop_event.Webshop_id.Contains(orderResponse.Shop_order.Webshop_id)).ToList();

                                var webshopName = webshops.SingleOrDefault(a =>
                                    a.Id.ToString() == orderResponse.Shop_order.Webshop_id)?.Name;
                                if (webshopName != null)
                                {
                                    DateTime? date = null;
                                    var exposureActive = false;
                                    if (campaignClicks1.Count > 0)
                                    {
                                        date = campaignClicks1.DefaultIfEmpty().Max(a => a.EventObject.Created);
                                    }

                                    if (campaignOpens1.Count > 0 &&
                                        (campaignOpens1.Max(a => a.EventObject.Created > date) || date == null))
                                    {
                                        date = campaignOpens1.Max(a => a.EventObject.Created);
                                    }

                                    if (date != null)
                                    {
                                        date = date.Value.AddDays(30);
                                        if (date >= DateTime.UtcNow)
                                        {
                                            exposureActive = true;
                                        }
                                    }
                                    else
                                    {
                                        date = old;
                                    }

                                    var greyNumber = new GreyNumber
                                    {
                                        timestamp = now,
                                        Customer = new GreyNumberCustomer
                                        {
                                            Email = contact.Email,
                                            Exposure_active = exposureActive,
                                            Exposure_end = date,
                                            Engaged = contact.MissedOpenMails < 11
                                        },
                                        Shop = new GreyNumberShop
                                        {
                                            Webshop_id = orderResponse.Shop_order.Webshop_id,
                                            Webshop_name = webshopName
                                        }
                                    };

                                    greyNumbers.Add(greyNumber);
                                }
                            }
                        }
                    }

                    //ViaBillOrder
                    //Customer order
                    foreach (var orderResponse in orderSearchResultViabill.OrderByDescending(a => a.Order_date))
                    {
                        if (orderResponse.Shop_order != null && orderResponse.Shop_order.Webshop_id != null)
                        {
                            if (!greyNumbers.Any(a => a.Customer.Email == contact.Email &&
                                                      a.Shop.Webshop_id == orderResponse.Shop_order.Webshop_id))
                            {
                                var campaignClicks1 = campaignClicks.Where(a =>
                                    a.Shop_event.Webshop_id == orderResponse.Shop_order.Webshop_id).ToList();

                                var campaignOpens1 = campaignOpens.Where(a =>
                                    //a.Shop_event.Webshop_id == orderResponse.Shop_order.Webshop_id).ToList();
                                    a.Shop_event.Webshop_id.Contains(orderResponse.Shop_order.Webshop_id)).ToList();
                                DateTime? date = null;
                                var exposureActive = false;
                                if (campaignClicks1.Count > 0)
                                {
                                    date = campaignClicks1.DefaultIfEmpty().Max(a => a.EventObject.Created);
                                }

                                if (campaignOpens1.Count > 0 &&
                                    (campaignOpens1.Max(a => a.EventObject.Created > date) || date == null))
                                {
                                    date = campaignOpens1.Max(a => a.EventObject.Created);
                                }

                                if (date != null)
                                {
                                    date = date.Value.AddDays(30);
                                    if (date >= DateTime.UtcNow)
                                    {
                                        exposureActive = true;
                                    }
                                }
                                else
                                {
                                    date = old;
                                }

                                var webshopName = webshops.SingleOrDefault(a =>
                                    a.Id.ToString() == orderResponse.Shop_order.Webshop_id)?.Name;
                                if (webshopName != null)
                                {
                                    var greyNumber = new GreyNumber
                                    {
                                        timestamp = now,
                                        Customer = new GreyNumberCustomer
                                        {
                                            Email = contact.Email,
                                            Exposure_active = exposureActive,
                                            Exposure_end = date,
                                            Engaged = contact.MissedOpenMails < 11
                                        },
                                        Shop = new GreyNumberShop
                                        {
                                            Webshop_id = orderResponse.Shop_order.Webshop_id,
                                            Webshop_name = webshopName
                                        }
                                    };

                                    greyNumbers.Add(greyNumber);
                                }
                            }
                        }
                    }
                }
            });

        await _elasticClient.Indices.DeleteAsync(index);
        var bulkAllObservable = _elasticClient.BulkAll(greyNumbers, d => d
            .Index(index)
            .BackOffRetries(10)
            .BackOffTime("30s")
            .RefreshOnCompleted()
            .MaxDegreeOfParallelism(Environment.ProcessorCount)
            .Size(1000)
            .BufferToBulk((desc, docs) => desc.CreateMany(docs))
        );

        var waitHandle = new ManualResetEvent(false);
        ExceptionDispatchInfo exceptionDispatchInfo = null;

        var observer = new BulkAllObserver(
            onNext: response => { },
            onError: exception =>
            {
                exceptionDispatchInfo = ExceptionDispatchInfo.Capture(exception);
                waitHandle.Set();
            },
            onCompleted: () => { waitHandle.Set(); });

        bulkAllObservable.Subscribe(observer);
        waitHandle.WaitOne();
        exceptionDispatchInfo?.Throw();

        return new ResponseDto
        {
            Success = true,
            Message = "True"
        };
    }*/

    public async Task<ResponseDto> MinifyJavascript()
    {
        try
        {
            // Old Valyrion FTP
            var keyString = FormatKeyString(configuration["ValyrionFtpKey"]);
            var key = new PrivateKeyFile(new MemoryStream(Encoding.UTF8.GetBytes(keyString)));

            await ProcessSftpFiles(configuration["ValyrionFtpHost"], "viabilldata.plugins.valyrion", key, "");


            // New Valyrion FTP
            var keyString2 = FormatKeyString(configuration["ValyrionStorageFtpKey"]);
            var key2 = new PrivateKeyFile(new MemoryStream(Encoding.UTF8.GetBytes(keyString2)));

            await ProcessSftpFiles(configuration["ValyrionStorageFtpHost"],
                configuration["ValyrionPluginStorageFtpUsername"], key2, "");
            await ProcessSftpFiles(configuration["ValyrionStorageFtpHost"],
                configuration["ValyrionPluginStorageFtpUsername"], key2, "custom");
            await ProcessSftpFiles(configuration["ValyrionStorageFtpHost"],
                configuration["ValyrionPluginStorageFtpUsername"], key2, "custom/merchants");
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Warning(ex, "Error connecting to public marlin files ftp");
        }

        return new ResponseDto {Message = "Success", Success = true};
    }

    private static string FormatKeyString(string key)
    {
        return key.Replace(" ", "\r\n")
            .Replace("-----BEGIN\r\nRSA\r\nPRIVATE\r\nKEY-----", "-----BEGIN RSA PRIVATE KEY-----")
            .Replace("-----END\r\nRSA\r\nPRIVATE\r\nKEY-----", "-----END RSA PRIVATE KEY-----");
    }

    private async Task ProcessSftpFiles(string host, string username, PrivateKeyFile key, string directory)
    {
        using (var client = new SftpClient(host, username, key))
        {
            client.Connect();
            var files = client.ListDirectory(directory).ToList();
            foreach (var file in files.Where(x => x.IsRegularFile))
            {
                using (var fileStream = new MemoryStream())
                {
                    client.DownloadFile(file.FullName, fileStream);
                    var content = Encoding.ASCII.GetString(fileStream.ToArray());
                    if (file.Name.Contains(".js") && file.Name.Split(".").Length == 2)
                    {
                        var result = Uglify.Js(content);
                        if (result.Errors.Count == 0)
                        {
                            byte[] byteArray = Encoding.UTF8.GetBytes(result.Code);
                            var name = file.Name.Split(".");
                            client.UploadFile(new MemoryStream(byteArray), $"{directory}/min/{name[0]}.min.js");
                        }
                        else
                        {
                            logger.ForContext("service_name", GetType().Name)
                                .Warning("Error minifying file {File}", file.Name);
                        }
                    }
                }
            }

            client.Disconnect();
        }
    }

    public async Task<ResponseDto> CalculateEngagementLevels()
    {
        /*var mails = (await _messageService.MessagesFromCampaign(539).ConfigureAwait(false))
            //.Where(a => a.ProcessedDate != null && a.Status == "sent").Skip(30000).ToList();
            .Where(a => a.ProcessedDate != null && a.Status == "sent").Skip(5000).Take(25000).ToList();
        var audiences = (await _audienceService.GetAllAsync().ConfigureAwait(false))
            .Where(a => a.MarketingStatus && a.MissedOpenMails == 0 && a.GenderMale == false).ToList();

        foreach (var mail in mails)
        {
            var contact = audiences.FirstOrDefault(a => a.Email == mail.Email);
            if (contact != null)
            {
                var wids = new List<string?>
                {
                    "4588",
                    "4799",
                    "1623",
                    "4650",
                    "4792",
                    "1076",
                    "4598",
                    "4773",
                    "4774",
                    "1932",
                    "4532",
                    "4778",
                };
                foreach (var wid in wids)
                {
                    var elasticEvent = new ElasticOpenEvent
                    {
                        Event_received = mail.ProcessedDate.Value,
                        Customer = new CustomerOpen
                        {
                            Email = contact.Email,
                            Campaign_Id = mail.CampaignId.ToString() ?? "",
                            Automation_id = mail.AutomationId ?? "",
                            Email_guid = mail.EmailGuid.ToString()
                        },
                        Shop_event = new ShopEventOpen
                        {
                            Webshop_id = wid
                        },
                        client = new ClientOpenDto
                        {
                            ip = "0.0.0.0",
                        },
                        UserAgent = new UserAgentOpenDto
                        {
                            name = "missed events",
                            original = "n/a",
                            version = "n/a",
                            device = new DeviceOpenDto
                            {
                                name = "n/a"
                            }
                        }
                    };

                    var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticEvent));
                    using (var publishChannel = _rabbitConnection.CreateModel())
                    {
                        publishChannel.BasicPublish(exchange: "campaign",
                            routingKey: "campaign_mail_open",
                            basicProperties: null,
                            body: actionBody);
                    }
                }
            }
        }*/

        var lookBackDays = DateTime.UtcNow.AddDays(-7);
        var sendMails = await messageService.MessagesSent(lookBackDays);
        int batchSize = 5000;

        var sendMailBatches = sendMails
            .Select((mail, index) => new {Mail = mail, Index = index})
            .GroupBy(x => x.Index / batchSize)
            .Select(g => g.Select(x => x.Mail).ToList());

        foreach (var batch in sendMailBatches)
        {
            var emailGuids = batch.Select(a => a.EmailGuid.ToString()).ToList();
            var allOpens = elasticCampaignMailOpenService.CheckOpens(emailGuids).Result;

            var openGuidsSet = new HashSet<string>(allOpens.Select(a => a.Customer.Email_guid));

            var engagedEmails = batch
                .Where(mail => openGuidsSet.Contains(mail.EmailGuid.ToString()))
                .Select(mail => mail.Email)
                .ToList();

            var unengagedEmails = batch
                .Where(mail => !openGuidsSet.Contains(mail.EmailGuid.ToString()))
                .Select(mail => mail.Email)
                .ToList();

            if (engagedEmails.Count != 0)
            {
                customerService.BulkUpdateUnengagedCustomers(engagedEmails, clear: true).Wait();
            }

            if (unengagedEmails.Count != 0)
            {
                customerService.BulkUpdateUnengagedCustomers(unengagedEmails, clear: false).Wait();
            }
        }


        return new ResponseDto {Message = "Success", Success = true};
    }

    public async Task<ResponseDto> EmailContactUnsubscribe()
    {
        var unsubscribes = await GetRecentUnsubscribes();

        foreach (var unsubscribe in unsubscribes)
        {
            var contacts = await customerService.UnsubscribeByEmailAsync(unsubscribe.Email, unsubscribe.CampaignId,
                unsubscribe.UnsubscribedAt);
            
            await partnerDataService.Unsubscribe(contacts);

            if (contacts.Count == 0)
            {
                logger.Warning("The Contact that tried to Unsubscribe does not exist, The email: {email}",
                    unsubscribe.Email);
            }
        }

        return new ResponseDto {Message = "Success", Success = true};
    }

    private async Task<List<UnsubscribeDto>> GetRecentUnsubscribes()
    {
        var unsubscribes = new List<UnsubscribeDto>();

        // Azure storage access information
        // TODO - Move this file
        const string blobContainerName = "assets";
        const string blobFileName = "Gmail/one-click-unsubscribe-viaads-085f789826dc.json";

        var blobServiceClient = new BlobServiceClient(configuration["MarlinAssets-BlobStorageConnectionString"]);
        var containerClient = blobServiceClient.GetBlobContainerClient(blobContainerName);
        var blobClient = containerClient.GetBlobClient(blobFileName);

        var memoryStream = new MemoryStream();
        await blobClient.DownloadToAsync(memoryStream);
        memoryStream.Position = 0;

        var credential = GoogleCredential.FromStream(memoryStream)
            .CreateScoped(Scopes)
            .CreateWithUser(UserEmail);

        var service = new GmailService(new BaseClientService.Initializer()
        {
            HttpClientInitializer = credential,
            ApplicationName = ApplicationName,
        });

        // Move messages from SPAM to INBOX and delete them from SPAM
        var spamQuery = "in:spam";
        var spamRequest = service.Users.Messages.List(UserEmail);
        spamRequest.Q = spamQuery;
        var spamResponse = await spamRequest.ExecuteAsync();
        if (spamResponse != null && spamResponse.Messages != null)
        {
            foreach (var messageItem in spamResponse.Messages)
            {
                var modifyRequest = new ModifyMessageRequest
                {
                    RemoveLabelIds = new List<string> {"SPAM"},
                    AddLabelIds = new List<string> {"INBOX"}
                };
                await service.Users.Messages.Modify(modifyRequest, UserEmail, messageItem.Id).ExecuteAsync();

                // Delete message from SPAM folder permanently
                //await service.Users.Messages.Delete(userEmail, messageItem.Id).ExecuteAsync();
            }
        }

        // Process messages from INBOX
        var threeDaysAgo = DateTime.UtcNow.AddDays(-3);
        string pageToken = null;
        var query = $"is:unread after:{threeDaysAgo:yyyy/MM/dd}";
        do
        {
            var request = service.Users.Messages.List(UserEmail);
            request.Q = query;
            request.MaxResults = 100;
            try
            {
                var response = await request.ExecuteAsync();
                pageToken = response?.NextPageToken ?? string.Empty;
                if (response is {Messages: not null})
                {
                    foreach (var messageItem in response.Messages)
                    {
                        var emailInfoRequest = service.Users.Messages.Get(UserEmail, messageItem.Id);
                        var emailInfoResponse = await emailInfoRequest.ExecuteAsync();

                        if (emailInfoResponse != null)
                        {
                            string subject = emailInfoResponse.Payload.Headers.FirstOrDefault(a =>
                                    a.Name.Equals("subject", StringComparison.CurrentCultureIgnoreCase))
                                ?.Value ?? string.Empty;
                            string date = emailInfoResponse.Payload.Headers.FirstOrDefault(a =>
                                    a.Name.Equals("date", StringComparison.CurrentCultureIgnoreCase))
                                ?.Value ?? string.Empty;
                            string from = emailInfoResponse.Payload.Headers.FirstOrDefault(a =>
                                    a.Name.Equals("from", StringComparison.CurrentCultureIgnoreCase))
                                ?.Value ?? string.Empty;
                            if (!from.IsNullOrEmpty())
                            {
                                from = ExtractEmail(from);
                            }

                            unsubscribes.Add(new UnsubscribeDto()
                            {
                                Email = from,
                                CampaignId = ExtractCampaignIdFromJwt(subject),
                                UnsubscribedAt = Convert.ToDateTime(date)
                            });

                            var modifyRequest = new ModifyMessageRequest
                            {
                                RemoveLabelIds = new List<string> {"UNREAD"}
                            };
                            await service.Users.Messages.Modify(modifyRequest, UserEmail, messageItem.Id)
                                .ExecuteAsync();
                        }
                    }
                }
            }
            catch (Exception e)
            {
                logger.ForContext("service_name", GetType().Name)
                    .Error(e, "Error processing email contact unsubscribe");
                throw;
            }
        } while (!string.IsNullOrEmpty(pageToken));

        return unsubscribes;
    }


    private int ExtractCampaignIdFromJwt(string jwt)
    {
        if (JwtTokenValidate(jwt))
        {
            var handler = new JwtSecurityTokenHandler();
            var jsonToken = handler.ReadToken(jwt);

            if (jsonToken is JwtSecurityToken tokenS)
            {
                var cid = tokenS.Claims.FirstOrDefault(claim => claim.Type == "cid");
                return cid != null ? Convert.ToInt32(cid.Value) : 0;
            }
        }

        return 0;
    }


    private bool JwtTokenValidate(string tokenString)
    {
        var tokenHandler = new JwtSecurityTokenHandler();

        var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["JwtTokenKey"]));

        bool result = true;

        try
        {
            var tokenRead = tokenHandler.ValidateToken(tokenString, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                ValidateIssuer = false,
                ValidateAudience = false,
                ValidateLifetime = false,
                IssuerSigningKey = securityKey
            }, out SecurityToken token);
        }
        catch
        {
            result = false;
        }

        return result;
    }

    private static string ExtractEmail(string input)
    {
        // Check if input contains angle brackets and extract the email
        var match = Regex.Match(input, @"<([^>]+)>");
        if (match.Success && IsValidEmail(match.Groups[1].Value))
        {
            // Return the email address within angle brackets if it's valid
            return match.Groups[1].Value;
        }

        // If no angle brackets or invalid email within brackets, check if the input itself is a valid email address
        if (IsValidEmail(input))
        {
            return input;
        }

        // No valid email address found
        return string.Empty;
    }

    private static bool IsValidEmail(string email)
    {
        return EmailRegex.IsMatch(email);
    }
}