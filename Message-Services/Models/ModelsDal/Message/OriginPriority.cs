using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Message_Services.Models.ModelsDal.Message;

[Table("OriginPriorities", Schema = "message")]
public partial class OriginPriority
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(50)]
    public string Name { get; set; } = null!;

    [InverseProperty("FkMessageOriginPriority")]
    public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
}
