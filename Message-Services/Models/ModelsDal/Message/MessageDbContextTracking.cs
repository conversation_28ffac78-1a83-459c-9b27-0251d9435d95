using Message_Service.Models.ModelsDal.Message;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Shared;
using IConnection = RabbitMQ.Client.IConnection;

namespace Message_Services.Models.ModelsDal.Message;

public class MessageDbContextTracking(
    DbContextOptions<MessageDbContext> options,
    IHttpContextAccessor httpContextAccessor,
    [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud)
    //IConnection rabbitConnection)
    : MessageDbContext(options)
{
    public virtual async Task<int> SaveChangesAsync()
    {
        OnBeforeSaveChanges();
        var result = await base.SaveChangesAsync();
        return result;
    }

    public virtual int SaveChanges()
    {
        OnBeforeSaveChanges();
        var result = base.SaveChanges();
        return result;
    }

    private void OnBeforeSaveChanges()
    {
        AuditSaveDb.OnBeforeSaveChanges(ChangeTracker, httpContextAccessor, rabbitConnectionCloud);
    }
}