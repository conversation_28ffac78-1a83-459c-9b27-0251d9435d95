using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Message_Services.Models.ModelsDal.Message;

[Table("Products", Schema = "message")]
public partial class Product
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    [StringLength(36)]
    public string InternalProductId { get; set; } = null!;

    [Column(TypeName = "decimal(10, 2)")]
    public decimal? ProductScore { get; set; }

    [Column(TypeName = "decimal(10, 2)")]
    public decimal? MerchantScore { get; set; }

    [Column(TypeName = "decimal(18, 4)")]
    public decimal? Score { get; set; }

    [StringLength(50)]
    public string? RecommendedBy { get; set; }

    [Column("FK_MessageId")]
    public int FkMessageId { get; set; }

    [ForeignKey("FkMessageId")]
    [InverseProperty("Products")]
    public virtual Message FkMessage { get; set; } = null!;
}
