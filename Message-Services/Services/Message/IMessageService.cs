using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Shared.Dto.Campaign;
using Shared.Dto.Message;
using Shared.Models;
using Shared.Models.Message;

namespace Message_Services.Services.Message;

public interface IMessageService
{
    Task<int> Messages(int campaignId);
    Task<List<Models.ModelsDal.Message.Message>> MessagesSent(int campaignId);
    Task<int> GetMessagesSentCountByCampaignIdThisYear(int campaignId);
    Task<Models.ModelsDal.Message.Message?> MessagesSent(int campaignId, bool first);
    Task<long> MessagesSent(DateTime from, DateTime to, int? campaignId);
    Task<List<int?>> SendStatus(List<int> campaignIds);
    Task<int> Messages(int campaignId, string status);
    Task<int> MessagesWithLowerActionDate(int campaignId, string status);
    Task<List<MessageEmailGuid>> MessagesSent(DateTime lookBackDay);
    Task<int> MessagesSent(DateTime from, DateTime to);
    Task<List<int>> MessagesSentUniqueCampaignCount(DateTime from, DateTime to);
    Task<int> MessagesDelivered(int partnerId, DateTime lookBackDay);
    Task<int> MessagesFailed(DateTime lookBackDay);
    Task<int> MessagesExpired(DateTime lookBackDay);
    Task<int> MessagesQueueSize(DateTime lookBackDay);
    Task<int> MessagesAction(DateTime actionDate);
    Task<List<QueuedCampaigns>> GetCampaignsInQueue(int partnerId);
    Task<ResponseDto> DisregardQueuedCampaign(int campaignId);
    Task FirstOpenHandling(string emailGuid);
    Task<ResponseDto> ChangePriorityOfQueuedCampaigns(List<QueuedCampaigns> queuedCampaigns);
    Task<int> QueueSize(int partnerId);
    Task<int> QueueSizeByCampaignId(int campaignId);
    Task<HttpResponseMessage> SendServiceEmailAsync(ServiceEmailDto serviceEmail);
    Task<List<MessageDto>> OpenRate();
}