#region

using System.Text;
using System.Text.Json;
using EFCore.BulkExtensions;
using Message_Services.Models.ModelsDal.Message;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Shared.Dto.Campaign;
using Shared.Dto.Message;
using Shared.Models;
using Shared.Models.Message;
using Shared.Services.Partner;
using ILogger = Serilog.ILogger;

#endregion

namespace Message_Services.Services.Message;

public class MessageService : IMessageService
{
    private readonly MessageDbContextTracking _messageDbContext;
    private readonly ILogger _logger;
    private readonly HttpClient _client;
    private readonly IConfiguration _configuration;
    private readonly IPartnerContext partnerContext;

    public MessageService(MessageDbContextTracking messageDbContext, ILogger logger, IConfiguration configuration, IPartnerContext partnerContext)
    {
        _messageDbContext = messageDbContext;
        _logger = logger;
        _configuration = configuration;
        this.partnerContext = partnerContext;

        _client = new HttpClient();
        _client.BaseAddress = new Uri($"{_configuration["MessageWorker-Url"]}message/");
        //_client.BaseAddress = new Uri("http://localhost:5000/message/");
    }

    public async Task<int> Messages(int campaignId)
    {
        return await _messageDbContext.Messages.CountAsync(a =>
                a.FkCampaignId == campaignId & a.Status != "disregarded" && a.AutomationId == null && a.IsTest == false)
            .ConfigureAwait(false);
    }

    public async Task<List<Models.ModelsDal.Message.Message>> MessagesSent(int campaignId)
    {
        return await _messageDbContext.Messages.Where(a => a.FkCampaignId == campaignId).ToListAsync();
    }

    public async Task<Models.ModelsDal.Message.Message?> MessagesSent(int campaignId, bool first)
    {
        if (first)
        {
            return await _messageDbContext.Messages.Where(a => a.FkCampaignId == campaignId && a.ProcessedDate != null)
                .OrderBy(a => a.ProcessedDate).FirstOrDefaultAsync();
        }

        return await _messageDbContext.Messages.Where(a => a.FkCampaignId == campaignId)
            .OrderByDescending(a => a.ProcessedDate).FirstOrDefaultAsync();
    }

    public async Task<long> MessagesSent(DateTime from, DateTime to, int? campaignId)
    {
        return await _messageDbContext.Messages.CountAsync(
            a =>
                a.ProcessedDate.HasValue && a.ProcessedDate.Value.Date >= from &&
                a.ProcessedDate.Value.Date < to &&
                (campaignId == null || a.FkCampaignId == campaignId)
        );
    }

    public async Task<List<int?>> SendStatus(List<int> campaignIds)
    {
        var sentStatus = await _messageDbContext.Messages
            .Where(a => a.FkCampaignId != null && campaignIds.Contains((int) a.FkCampaignId) &&
                        a.Status != "disregarded" &&
                        a.AutomationId == null &&
                        a.IsTest == false)
            .Select(a => a.FkCampaignId)
            .Distinct()
            .ToListAsync()
            .ConfigureAwait(false);
        return sentStatus;
    }

    public async Task<int> Messages(int campaignId, string status)
    {
        return await _messageDbContext.Messages.CountAsync(a =>
                a.FkCampaignId == campaignId && a.Status == status && !a.IsTest && a.AutomationId == null)
            .ConfigureAwait(false);
    }

    public async Task<int> MessagesWithLowerActionDate(int campaignId, string status)
    {
        return await _messageDbContext.Messages.CountAsync(a =>
                a.FkCampaignId == campaignId && a.Status == status && a.ActionDate < DateTime.UtcNow)
            .ConfigureAwait(false);
    }

    public async Task<List<MessageEmailGuid>> MessagesSent(DateTime lookBackDay)
    {
        DateTime startOfDay = lookBackDay.Date;
        DateTime endOfDay = lookBackDay.Date.AddDays(1);

        return await _messageDbContext.Messages
            .AsNoTracking()
            .Where(a => a.Status == "sent" &&
                        a.ProcessedDate.HasValue &&
                        a.ProcessedDate >= startOfDay &&
                        a.ProcessedDate < endOfDay &&
                        !a.IsTest)
            .Select(a => new MessageEmailGuid
            {
                EmailGuid = a.EmailGuid,
                Email = a.Email
            })
            .ToListAsync();
    }

    public async Task<int> GetMessagesSentCountByCampaignIdThisYear(int campaignId)
    {
        return await _messageDbContext.Messages
            .Where(a => a.FkCampaignId == campaignId && a.Status == "sent" && a.IsTest == false && a.ActionDate.HasValue && a.ActionDate.Value.Year == DateTime.UtcNow.Year)
            .CountAsync();
    }


    public async Task<int> MessagesSent(DateTime from, DateTime to)
    {
        return await _messageDbContext.Messages.Where(a =>
            a.Status == "sent" && a.ProcessedDate.HasValue && a.ProcessedDate.Value.Date >= from &&
            a.ProcessedDate.Value.Date < to && a.IsTest == false).CountAsync();
    }

    public async Task<List<int>> MessagesSentUniqueCampaignCount(DateTime from, DateTime to)
    {
        var campaignIds = await _messageDbContext.Messages
            .Where(a => a.Status == "sent" &&
                        a.ProcessedDate.HasValue &&
                        a.ProcessedDate.Value.Date >= from &&
                        a.ProcessedDate.Value.Date < to &&
                        a.IsTest == false && a.AutomationId == null)
            .Select(a => a.FkCampaignId ?? 0)
            .Distinct()
            .ToListAsync();
        return campaignIds;
    }

    public async Task<int> MessagesDelivered(int partnerId, DateTime lookBackDay)
    {
        return await _messageDbContext.Messages.CountAsync(a =>
            a.Status == "sent" && a.ProcessedDate > lookBackDay && a.IsTest == false && a.FkPartnerId == partnerId);
    }

    public async Task<int> MessagesFailed(DateTime lookBackDay)
    {
        return await _messageDbContext.Messages.CountAsync(a =>
            (a.Status == "disregarded" || a.Status == "failed") && a.ProcessedDate > lookBackDay);
    }

    public async Task<int> MessagesExpired(DateTime lookBackDay)
    {
        return await _messageDbContext.Messages.CountAsync(a =>
            (a.Status == "expired") && a.ProcessedDate > lookBackDay);
    }

    public async Task<int> MessagesAction(DateTime actionDate)
    {
        return await _messageDbContext.Messages.CountAsync(a => a.ActionDate > actionDate);
    }

    public async Task<List<QueuedCampaigns>> GetCampaignsInQueue(int partnerId)
    {
        var messageCounts = await _messageDbContext.Messages
            .Where(a => (a.Status == "new" || a.Status == "ready") && !a.IsTest && a.FkMessageOriginPriorityId == 2 && a.FkPartnerId == partnerId)
            .GroupBy(a => new {a.FkCampaignId, a.Priority})
            .Select(g => new
            {
                CampaignId = g.Key.FkCampaignId,
                Priority = g.Key.Priority,
                Count = g.Count()
            })
            .ToListAsync();

        var groupedMessages = new List<QueuedCampaigns>();
        foreach (var messageStats in messageCounts)
        {
            groupedMessages.Add(new QueuedCampaigns
            {
                Id = messageStats.CampaignId ?? 0,
                Priority = messageStats.Priority,
                Delivered = _messageDbContext.Messages.Count(a =>
                    a.FkCampaignId == messageStats.CampaignId.GetValueOrDefault() && a.Status == "sent"),
                Audience = messageStats.Count
            });
        }

        /*var messages = await _messageDbContext.Messages
            .Where(a => (a.Status == "new" || a.Status == "ready") && !a.IsTest && a.FkMessageOriginPriorityId == 2)
            .ToListAsync();

        var groupedMessages = messages
            .GroupBy(a => a.FkCampaignId)
            .Select(group => new QueuedCampaigns
            {
                Id = group.Key.GetValueOrDefault(),
                Audience = group.Count(a => a.Status is "new" or "ready"),
                Delivered = _messageDbContext.Messages.Count(a =>
                    a.FkCampaignId == group.Key.GetValueOrDefault() && a.Status == "sent"),
                Priority = group.Min(a => a.Priority)
            })
            .OrderBy(a => a.Priority)
            .ThenByDescending(a => a.Id)
            .ToList();*/

        return groupedMessages;
    }

    public async Task<ResponseDto> DisregardQueuedCampaign(int campaignId)
    {
        await _messageDbContext.Messages
            .Where(a => a.FkCampaignId == campaignId && (a.Status == "new" || a.Status == "ready"))
            .BatchUpdateAsync(a => new Models.ModelsDal.Message.Message {Status = "disregarded"});

        return new ResponseDto()
        {
            Message = "Disregarded",
            Success = true
        };
    }

    public async Task FirstOpenHandling(string emailGuid)
    {
        if (Guid.TryParse(emailGuid, out Guid guid))
        {
            var rowsAffected = await _messageDbContext.Messages
                .Where(a => a.EmailGuid == guid && a.FirstOpen == null)
                .ExecuteUpdateAsync(m => m.SetProperty(x => x.FirstOpen, DateTime.UtcNow));

            if (rowsAffected == 0)
            {
                _logger.ForContext("service_name", GetType().Name)
                    .Information("EmailGuid does not exist or already has a FirstOpen set {EmailGuid}", emailGuid);
            }
        }
        else
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error("Invalid emailGuid format {EmailGuid}", emailGuid);
        }
    }


    public async Task<ResponseDto> ChangePriorityOfQueuedCampaigns(List<QueuedCampaigns> queuedCampaigns)
    {
        foreach (var queuedCampaign in queuedCampaigns)
        {
            var messages =
                await _messageDbContext.Messages
                    .Where(a => a.FkCampaignId == queuedCampaign.Id && (a.Status == "new" || a.Status == "ready"))
                    .ToListAsync();
            if (messages.All(a => a.Priority == queuedCampaign.Priority)) continue;

            foreach (var message in messages)
            {
                message.Priority = queuedCampaign.Priority;
            }

            await _messageDbContext.BulkSaveChangesAsync();
            //await _messageDbContext.SaveChangesAsync();
        }

        return new ResponseDto()
        {
            Message = "Changed Priority",
            Success = true
        };
    }

    public async Task<int> QueueSize(int partnerId)
    {
        return await _messageDbContext.Messages.CountAsync(a =>
            (a.Status == "new" || a.Status == "ready") && a.FkPartnerId == partnerId);
    }

    public async Task<int> QueueSizeByCampaignId(int campaignId)
    {
        return await _messageDbContext.Messages.CountAsync(a =>
            (a.Status == "new" || a.Status == "ready") && a.FkCampaignId == campaignId && a.AutomationId == null &&
            a.IsTest == false);
    }

    public async Task<int> MessagesQueueSize(DateTime lookBackDay)
    {
        return await _messageDbContext.Messages.CountAsync(a =>
            (a.Status == "new" || a.Status == "ready") && a.CreatedDate > lookBackDay);
    }

    public async Task<HttpResponseMessage> SendServiceEmailAsync(ServiceEmailDto serviceEmail)
    {
        int retryCount = 0;
        const int maxRetries = 2;

        try
        {
            while (retryCount <= maxRetries)
            {
                var jsonString = JsonSerializer.Serialize(serviceEmail);
                var content = new StringContent(jsonString, Encoding.UTF8, "application/json");
                var response = await _client.PostAsync("service", content);

                if (response.IsSuccessStatusCode)
                {
                    _logger.ForContext("service_name", GetType().Name).Information(
                        "Email was sent with the following information: {ServiceEmail}",
                        JsonSerializer.Serialize(serviceEmail));

                    return response;
                }

                //Failed so retry
                _logger.ForContext("service_name", GetType().Name).Error(
                    "Email was TRIED Sent with the following information: {ServiceEmail}. Code: {StatusCode}, Response: {Response}",
                    JsonSerializer.Serialize(serviceEmail), response.StatusCode,
                    await response.Content.ReadAsStringAsync());

                retryCount++;

                if (retryCount <= maxRetries)
                {
                    // Optionally add a delay before retrying
                    await Task.Delay(TimeSpan.FromSeconds(2));
                }
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }

        return new HttpResponseMessage();
    }

    public async Task<List<MessageDto>> OpenRate()
    {
        var partnerId = partnerContext.PartnerId;
        var toOld = DateTime.UtcNow.AddMonths(-3);

        //FirstOpen was first made on the 2024-05-27 so older data does not have it
        var dataFrom = new DateTime(2024, 05, 27);
        if (dataFrom > toOld)
        {
            toOld = dataFrom;
        }

        var allMessages = await _messageDbContext.Messages
            .Where(a => a.Status == "sent" && a.ActionDate > toOld && a.FkPartnerId == partnerId)
            .Select(a => new MessageDto
            {
                Email = a.Email,
                FirstOpen = a.FirstOpen,
                ActionDate = a.ActionDate
            })
            .ToListAsync();

        var messages = allMessages
            .GroupBy(e => e.Email)
            .SelectMany(g => g
                .OrderByDescending(e => e.ActionDate)
                .Take(10)).ToList();
        return messages;
    }

    /*public async Task<List<Message_Service.Models.ModelsDal.Message.Setting>> GetMessageServiceSettings()
    {
        return await _messageDbContext.Settings.Where(a => a.Active).ToListAsync();
    }

    public async Task<string?> GetSettingValueByName(string name, string groupName)
    {
        var setting =
            await _messageDbContext.Settings.SingleOrDefaultAsync(a =>
                a.Active && a.Name == name && a.GroupName == groupName).ConfigureAwait(false);
        return setting?.Value;
    }

    public async Task MailQueueExpire()
    {
        var days = Convert.ToInt32((await _messageDbContext.Settings.SingleAsync(a => a.Name == "MailQueueExpire"))
            .Value);
        var date = DateTime.UtcNow.AddDays(-days);
        var messages = _messageDbContext.Messages
            .Where(a => a.ProcessedDate == null && a.Status == "new" && a.CreatedDate < date).ToList();
        foreach (var message in messages)
        {
            message.Status = "expired";
            _messageDbContext.Update(message);
        }

        await _messageDbContext.SaveChangesAsync();
    }*/
}