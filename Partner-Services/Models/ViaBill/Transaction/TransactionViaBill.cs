namespace Viabill_Services.Models.Transaction;

public class TransactionViaBill
{
    public string uuid { get; set; }
    public string entryType { get; set; }
    public string created { get; set; }
    public string updated { get; set; }
    public string? lastAuthorizedDate { get; set; }
    public long? webshopStoreId { get; set; }
    public long? merchantAccountId { get; set; }
    public long? debtorAccountId { get; set; }
    public string? debtorEmail { get; set; }
    public string transaction { get; set; }
    public string currency { get; set; }
    public decimal? amount { get; set; }
    public string orderNumber { get; set; }
    public string status { get; set; }
    public string? refundTimestamp { get; set; }
    public List<VirtualCardTransactionDetail> virtualCardTransactionDetails { get; set; }

    public class VirtualCardTransactionDetail
    {
        public string? authorizationMethod { get; set; }
        public string? merchantName { get; set; }
        public string? merchantCategory { get; set; }
        public string? productName { get; set; }
        public string? productCategory { get; set; }
        public string? productPrice { get; set; }
    }
}