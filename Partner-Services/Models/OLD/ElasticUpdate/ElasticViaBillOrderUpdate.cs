// ReSharper disable All

using Nest;

namespace Marlin_OS_Integration_API.Models.Order;

public class ElasticViaBillOrderUpdate
{
    [Keyword(Name = "id")] public string? id { get; set; }
    [Object(Name = "Shop_order")] public ElasticViaBillShopOrderUpdate? Shop_order { get; set; }
}

public class ElasticViaBillShopOrderUpdate
{
    [Keyword(Name = "Webshop_id")] public string? Webshop_id { get; set; }
}