using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("PartnerAssets", Schema = "partner")]
public partial class PartnerAsset
{
    [Key]
    public int Id { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime CreatedDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime LastModifiedDate { get; set; }

    public bool IsActive { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [StringLength(100)]
    public string AssetType { get; set; } = null!;

    [StringLength(500)]
    public string AssetPath { get; set; } = null!;

    [ForeignKey("FkPartnerId")]
    [InverseProperty("PartnerAssets")]
    public virtual Partner FkPartner { get; set; } = null!;
}
