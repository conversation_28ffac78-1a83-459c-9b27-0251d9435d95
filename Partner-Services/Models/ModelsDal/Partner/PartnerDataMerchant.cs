using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("PartnerDataMerchants", Schema = "partner")]
public partial class PartnerDataMerchant
{
    [Key]
    public long Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    [Precision(0)]
    public DateTime PartnerCreatedDate { get; set; }

    [Precision(0)]
    public DateTime PartnerLastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(200)]
    public string Uuid { get; set; } = null!;

    [StringLength(200)]
    public string Name { get; set; } = null!;

    [StringLength(500)]
    public string Url { get; set; } = null!;

    [Column("FK_PartnerDataId")]
    public long FkPartnerDataId { get; set; }

    [StringLength(1000)]
    public string? Categories { get; set; }

    [ForeignKey("FkPartnerDataId")]
    [InverseProperty("PartnerDataMerchants")]
    public virtual PartnerData FkPartnerData { get; set; } = null!;
}
