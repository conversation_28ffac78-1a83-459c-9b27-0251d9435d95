using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("FileDataStructure", Schema = "partner")]
public partial class FileDataStructure
{
    [Key]
    public int Id { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime CreatedDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime LastModifiedDate { get; set; }

    public bool IsActive { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [StringLength(100)]
    public string FileType { get; set; } = null!;

    [StringLength(500)]
    public string FileName { get; set; } = null!;

    [StringLength(50)]
    public string ContentType { get; set; } = null!;

    public bool IsArchived { get; set; }

    [ForeignKey("FkPartnerId")]
    [InverseProperty("FileDataStructures")]
    public virtual Partner FkPartner { get; set; } = null!;
}
