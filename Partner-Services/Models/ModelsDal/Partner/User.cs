using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("Users", Schema = "partner")]
public partial class User
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(255)]
    public string Email { get; set; } = null!;

    [StringLength(255)]
    public string Password { get; set; } = null!;

    [StringLength(255)]
    public string FirstName { get; set; } = null!;

    [StringLength(255)]
    public string LastName { get; set; } = null!;

    [Column("TFAAuthKey")]
    [StringLength(255)]
    public string? TfaauthKey { get; set; }

    [Column("TFAEnabled")]
    public bool? Tfaenabled { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [StringLength(50)]
    public string? Role { get; set; }

    [ForeignKey("FkPartnerId")]
    [InverseProperty("Users")]
    public virtual Partner FkPartner { get; set; } = null!;

    [InverseProperty("FkUser")]
    public virtual ICollection<UserPermissionsRel> UserPermissionsRels { get; set; } = new List<UserPermissionsRel>();

    [InverseProperty("FkUser")]
    public virtual ICollection<UserToken> UserTokens { get; set; } = new List<UserToken>();
}
