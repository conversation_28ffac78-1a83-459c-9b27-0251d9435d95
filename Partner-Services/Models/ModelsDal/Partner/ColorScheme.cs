using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("ColorScheme", Schema = "partner")]
public partial class ColorScheme
{
    [Key]
    public int Id { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime CreatedDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime LastModifiedDate { get; set; }

    public bool IsActive { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [StringLength(7)]
    public string PrimaryColor { get; set; } = null!;

    [StringLength(7)]
    public string SecondaryColor { get; set; } = null!;

    [StringLength(7)]
    public string TertiaryColor { get; set; } = null!;

    [ForeignKey("FkPartnerId")]
    [InverseProperty("ColorSchemes")]
    public virtual Partner FkPartner { get; set; } = null!;
}
