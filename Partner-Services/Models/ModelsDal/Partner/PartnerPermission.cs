using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("PartnerPermissions", Schema = "partner")]
public partial class PartnerPermission
{
    [Key]
    public int Id { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime CreatedDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [Column("FK_PermissionId")]
    public int FkPermissionId { get; set; }

    [ForeignKey("FkPartnerId")]
    [InverseProperty("PartnerPermissions")]
    public virtual Partner FkPartner { get; set; } = null!;

    [ForeignKey("FkPermissionId")]
    [InverseProperty("PartnerPermissions")]
    public virtual Permission FkPermission { get; set; } = null!;
}
