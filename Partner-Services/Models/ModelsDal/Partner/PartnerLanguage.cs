using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("PartnerLanguages", Schema = "partner")]
public partial class PartnerLanguage
{
    [Key]
    public int Id { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime CreatedDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime LastModifiedDate { get; set; }

    public bool IsActive { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [Column("FK_LanguageId")]
    public int FkLanguageId { get; set; }

    public bool IsDefault { get; set; }

    [ForeignKey("FkPartnerId")]
    [InverseProperty("PartnerLanguages")]
    public virtual Partner FkPartner { get; set; } = null!;
}
