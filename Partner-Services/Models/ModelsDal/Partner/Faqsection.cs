using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("FAQSection", Schema = "partner")]
public partial class Faqsection
{
    [Key]
    public int Id { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime CreatedDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime LastModifiedDate { get; set; }

    public bool IsActive { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [Column("FK_LanguageId")]
    public int FkLanguageId { get; set; }

    [StringLength(255)]
    public string Name { get; set; } = null!;

    public int DisplayOrder { get; set; }

    [InverseProperty("FkSection")]
    public virtual ICollection<Faq> Faqs { get; set; } = new List<Faq>();

    [ForeignKey("FkPartnerId")]
    [InverseProperty("Faqsections")]
    public virtual Partner FkPartner { get; set; } = null!;
}
