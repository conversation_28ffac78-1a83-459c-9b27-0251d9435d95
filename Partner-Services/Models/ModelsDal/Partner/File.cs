using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("Files", Schema = "partner")]
public partial class File
{
    [Key]
    public long Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    [StringLength(1000)]
    public string FileName { get; set; } = null!;

    [Precision(0)]
    public DateTime FileCreationTime { get; set; }

    [Precision(0)]
    public DateTime FileImportTime { get; set; }

    public long Size { get; set; }

    public int ObjectCount { get; set; }

    public int? RunTimeSeconds { get; set; }

    public bool Done { get; set; }

    [StringLength(50)]
    public string Type { get; set; } = null!;

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [ForeignKey("FkPartnerId")]
    [InverseProperty("Files")]
    public virtual Partner FkPartner { get; set; } = null!;
}
