using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("UserPermissionsRel", Schema = "partner")]
public partial class UserPermissionsRel
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [Column("FK_UserId")]
    public int FkUserId { get; set; }

    [Column("FK_PermissionId")]
    public int FkPermissionId { get; set; }

    [ForeignKey("FkPermissionId")]
    [InverseProperty("UserPermissionsRels")]
    public virtual Permission FkPermission { get; set; } = null!;

    [ForeignKey("FkUserId")]
    [InverseProperty("UserPermissionsRels")]
    public virtual User FkUser { get; set; } = null!;
}
