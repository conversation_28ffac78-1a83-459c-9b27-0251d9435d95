using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("FAQ", Schema = "partner")]
public partial class Faq
{
    [Key]
    public int Id { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime CreatedDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime LastModifiedDate { get; set; }

    public bool IsActive { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [Column("FK_LanguageId")]
    public int FkLanguageId { get; set; }

    [Column("FK_SectionId")]
    public int FkSectionId { get; set; }

    [StringLength(1000)]
    public string Question { get; set; } = null!;

    public string Answer { get; set; } = null!;

    public int DisplayOrder { get; set; }

    [ForeignKey("FkPartnerId")]
    [InverseProperty("Faqs")]
    public virtual Partner FkPartner { get; set; } = null!;

    [ForeignKey("FkSectionId")]
    [InverseProperty("Faqs")]
    public virtual Faqsection FkSection { get; set; } = null!;
}
