using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("Partners", Schema = "partner")]
public partial class Partner
{
    [Column(TypeName = "datetime")]
    public DateTime CreatedDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime LastModifiedDate { get; set; }

    public bool IsActive { get; set; }

    [StringLength(255)]
    public string Name { get; set; } = null!;

    public Guid PartnerToken { get; set; }

    [Key]
    public int Id { get; set; }

    public Guid UniqueIdentifier { get; set; }

    [InverseProperty("FkPartner")]
    public virtual ICollection<ColorScheme> ColorSchemes { get; set; } = new List<ColorScheme>();

    [InverseProperty("FkPartner")]
    public virtual ICollection<Faq> Faqs { get; set; } = new List<Faq>();

    [InverseProperty("FkPartner")]
    public virtual ICollection<Faqsection> Faqsections { get; set; } = new List<Faqsection>();

    [InverseProperty("FkPartner")]
    public virtual ICollection<Feedback> Feedbacks { get; set; } = new List<Feedback>();

    [InverseProperty("FkPartner")]
    public virtual ICollection<FileDataStructure> FileDataStructures { get; set; } = new List<FileDataStructure>();

    [InverseProperty("FkPartner")]
    public virtual ICollection<File> Files { get; set; } = new List<File>();

    [InverseProperty("FkPartner")]
    public virtual ICollection<PartnerAsset> PartnerAssets { get; set; } = new List<PartnerAsset>();

    [InverseProperty("FkPartner")]
    public virtual ICollection<PartnerData> PartnerData { get; set; } = new List<PartnerData>();

    [InverseProperty("FkPartner")]
    public virtual ICollection<PartnerLanguage> PartnerLanguages { get; set; } = new List<PartnerLanguage>();

    [InverseProperty("FkPartner")]
    public virtual ICollection<PartnerPermission> PartnerPermissions { get; set; } = new List<PartnerPermission>();

    [InverseProperty("FkPartner")]
    public virtual ICollection<SupportContact> SupportContacts { get; set; } = new List<SupportContact>();

    [InverseProperty("FkPartner")]
    public virtual ICollection<User> Users { get; set; } = new List<User>();
}
