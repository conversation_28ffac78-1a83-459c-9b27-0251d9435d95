using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("SupportContacts", Schema = "partner")]
public partial class SupportContact
{
    [Key]
    public int Id { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime CreatedDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime LastModifiedDate { get; set; }

    public bool IsActive { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [StringLength(255)]
    public string ContactName { get; set; } = null!;

    [StringLength(100)]
    public string Role { get; set; } = null!;

    [StringLength(50)]
    public string PhoneNumber { get; set; } = null!;

    [StringLength(255)]
    public string Email { get; set; } = null!;

    public bool IsPrimaryContact { get; set; }

    [StringLength(1000)]
    public string ImageUrl { get; set; } = null!;

    [ForeignKey("FkPartnerId")]
    [InverseProperty("SupportContacts")]
    public virtual Partner FkPartner { get; set; } = null!;
}
