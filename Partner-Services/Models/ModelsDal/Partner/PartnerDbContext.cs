using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

public partial class PartnerDbContext : DbContext
{
    public PartnerDbContext(DbContextOptions<PartnerDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<ColorScheme> ColorSchemes { get; set; }

    public virtual DbSet<Faq> Faqs { get; set; }

    public virtual DbSet<Faqsection> Faqsections { get; set; }

    public virtual DbSet<Feedback> Feedbacks { get; set; }

    public virtual DbSet<File> Files { get; set; }

    public virtual DbSet<FileDataStructure> FileDataStructures { get; set; }

    public virtual DbSet<Partner> Partners { get; set; }

    public virtual DbSet<PartnerAsset> PartnerAssets { get; set; }

    public virtual DbSet<PartnerData> PartnerDatas { get; set; }

    public virtual DbSet<PartnerDataMerchant> PartnerDataMerchants { get; set; }

    public virtual DbSet<PartnerLanguage> PartnerLanguages { get; set; }

    public virtual DbSet<PartnerPermission> PartnerPermissions { get; set; }

    public virtual DbSet<Permission> Permissions { get; set; }

    public virtual DbSet<SupportContact> SupportContacts { get; set; }

    public virtual DbSet<User> Users { get; set; }

    public virtual DbSet<UserPermissionsRel> UserPermissionsRels { get; set; }

    public virtual DbSet<UserToken> UserTokens { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ColorScheme>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__ColorSch__3214EC07C1B3DCB8");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkPartner).WithMany(p => p.ColorSchemes)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__ColorSche__FK_Pa__1FD8A9E3");
        });

        modelBuilder.Entity<Faq>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__FAQ__3214EC07CD693427");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkPartner).WithMany(p => p.Faqs)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FAQ__FK_PartnerI__25918339");

            entity.HasOne(d => d.FkSection).WithMany(p => p.Faqs)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FAQ__FK_SectionI__5A3A55A2");
        });

        modelBuilder.Entity<Faqsection>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__FAQSecti__3214EC075D6CB626");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkPartner).WithMany(p => p.Faqsections)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FAQSectio__FK_Pa__249D5F00");
        });

        modelBuilder.Entity<Feedback>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Feedback__3214EC070363A9D1");

            entity.HasOne(d => d.FkPartner).WithMany(p => p.Feedbacks)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PartnerId");
        });

        modelBuilder.Entity<File>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Files__3214EC07D1456A75");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkPartner).WithMany(p => p.Files)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Files_PartnerId");
        });

        modelBuilder.Entity<FileDataStructure>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__FileData__3214EC07477560C7");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkPartner).WithMany(p => p.FileDataStructures)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FileDataS__FK_Pa__23A93AC7");
        });

        modelBuilder.Entity<Partner>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.PartnerToken).HasDefaultValueSql("(newid())");
        });

        modelBuilder.Entity<PartnerAsset>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__PartnerA__3214EC077194BDFB");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkPartner).WithMany(p => p.PartnerAssets)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__PartnerAs__FK_Pa__22B5168E");
        });

        modelBuilder.Entity<PartnerData>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__PartnerD__3214EC073376C04B");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkPartner).WithMany(p => p.PartnerData)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PartnerDatas_PartnerId");
        });

        modelBuilder.Entity<PartnerDataMerchant>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__PartnerD__3214EC07F0E3B017");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkPartnerData).WithMany(p => p.PartnerDataMerchants)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__PartnerDa__FK_Pa__12C8C788");
        });

        modelBuilder.Entity<PartnerLanguage>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__PartnerL__3214EC07A6F09A6B");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkPartner).WithMany(p => p.PartnerLanguages)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__PartnerLa__FK_Pa__21C0F255");
        });

        modelBuilder.Entity<PartnerPermission>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__PartnerP__3214EC073695997C");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkPartner).WithMany(p => p.PartnerPermissions)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PartnerPermissions_Partners");

            entity.HasOne(d => d.FkPermission).WithMany(p => p.PartnerPermissions)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PartnerPermissions_Permissions");
        });

        modelBuilder.Entity<Permission>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Permissi__3214EC0755E8D962");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<SupportContact>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__SupportC__3214EC079778ADD9");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkPartner).WithMany(p => p.SupportContacts)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__SupportCo__FK_Pa__20CCCE1C");
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Users__3214EC079E0CD22F");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkPartner).WithMany(p => p.Users)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PartnerUsers_PartnerId");
        });

        modelBuilder.Entity<UserPermissionsRel>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__UserPerm__3214EC07146BA2BE");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkPermission).WithMany(p => p.UserPermissionsRels)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserPermi__FK_Pe__3B80C458");

            entity.HasOne(d => d.FkUser).WithMany(p => p.UserPermissionsRels)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserPermi__FK_Us__39237A9A");
        });

        modelBuilder.Entity<UserToken>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__UserToke__3214EC0785FBBCB8");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkUser).WithMany(p => p.UserTokens)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserToken__FK_Us__3FD07829");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
