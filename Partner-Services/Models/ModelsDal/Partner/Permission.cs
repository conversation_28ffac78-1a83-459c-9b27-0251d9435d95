using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("Permissions", Schema = "partner")]
public partial class Permission
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public int SortOrder { get; set; }

    [StringLength(255)]
    public string Key { get; set; } = null!;

    [InverseProperty("FkPermission")]
    public virtual ICollection<PartnerPermission> PartnerPermissions { get; set; } = new List<PartnerPermission>();

    [InverseProperty("FkPermission")]
    public virtual ICollection<UserPermissionsRel> UserPermissionsRels { get; set; } = new List<UserPermissionsRel>();
}
