using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("PartnerDatas", Schema = "partner")]
public partial class PartnerData
{
    [Key]
    public long Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    [Precision(0)]
    public DateTime? PartnerCreatedDate { get; set; }

    [Precision(0)]
    public DateTime? PartnerLastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(200)]
    public string Uuid { get; set; } = null!;

    [StringLength(200)]
    public string Name { get; set; } = null!;

    [StringLength(200)]
    public string Country { get; set; } = null!;

    [StringLength(1000)]
    public string? Ids { get; set; }

    [Column("FK_MerchantId")]
    public int? FkMerchantId { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [ForeignKey("FkPartnerId")]
    [InverseProperty("PartnerData")]
    public virtual Partner FkPartner { get; set; } = null!;

    [InverseProperty("FkPartnerData")]
    public virtual ICollection<PartnerDataMerchant> PartnerDataMerchants { get; set; } = new List<PartnerDataMerchant>();
}
