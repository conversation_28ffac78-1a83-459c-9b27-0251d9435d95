using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("UserTokens", Schema = "partner")]
public partial class UserToken
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(1000)]
    public string Token { get; set; } = null!;

    [Precision(0)]
    public DateTime? Expire { get; set; }

    [Column("FK_UserId")]
    public int FkUserId { get; set; }

    [ForeignKey("FkUserId")]
    [InverseProperty("UserTokens")]
    public virtual User FkUser { get; set; } = null!;
}
