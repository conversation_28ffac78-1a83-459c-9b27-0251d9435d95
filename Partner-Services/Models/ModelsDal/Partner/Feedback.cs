using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Partner_Services.Models.ModelsDal.Partner;

[Table("Feedback", Schema = "partner")]
public partial class Feedback
{
    [Key]
    public int Id { get; set; }

    public int FkPartnerId { get; set; }

    [StringLength(255)]
    public string Email { get; set; } = null!;

    [Column("Feedback")]
    public string FeedbackMessage { get; set; } = null!;

    public DateTime CreatedDate { get; set; }

    [ForeignKey("FkPartnerId")]
    [InverseProperty("Feedbacks")]
    public virtual Partner FkPartner { get; set; } = null!;
}
