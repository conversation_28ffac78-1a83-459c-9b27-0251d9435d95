using CsvHelper.Configuration;
using Partner_Services.Models.Dto;

namespace Partner_Services.Models.Mappers.HappyPay;

public sealed class HappyPayDebtorDtoMap : ClassMap<HappyPayDebtorDto>
{
    public HappyPayDebtorDtoMap()
    {
        Map(m => m.Id).Index(0);
        Map(m => m.FirstName).Index(1);
        Map(m => m.LastName).Index(2);
        Map(m => m.Email).Index(3);
        Map(m => m.HasMarketingConsent).Index(4);
        Map(m => m.CreatedDate).Index(5);
        Map(m => m.Age).Index(6);
        Map(m => m.Gender).Index(7);
        Map(m => m.Phone).Index(8)
            .Convert(args =>
            {
                var phone = args.Row.GetField(8);
                return !string.IsNullOrEmpty(phone) && phone.StartsWith('0')
                    ? phone[1..]
                    : phone;
            });
        Map(m => m.City).Index(9);
    }
}