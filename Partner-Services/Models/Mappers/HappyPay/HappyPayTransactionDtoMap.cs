using CsvHelper.Configuration;
using Partner_Services.Models.Dto;

namespace Partner_Services.Models.Mappers.HappyPay;

public sealed class HappyPayTransactionDtoMap : ClassMap<HappyPayTransactionDto>
{
    public HappyPayTransactionDtoMap()
    {
        Map(m => m.MerchantId).Index(0);
        Map(m => m.BusinessName).Index(1);
        Map(m => m.BrandName).Index(2);
        Map(m => m.Categories).Index(3);
        Map(m => m.OrderDate).Index(4);
        Map(m => m.CustomerEmail).Index(5);
        Map(m => m.ProductName).Index(6);
        Map(m => m.ProductPrice).Index(7);
        Map(m => m.ProductCategory).Index(8);
    }
}