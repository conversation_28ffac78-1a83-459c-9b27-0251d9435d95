using CsvHelper.Configuration;
using Partner_Services.Models.Dto;

namespace Partner_Services.Models.Mappers.HappyPay;

public sealed class HappyPayMerchantDtoMap : ClassMap<HappyPayMerchantDto>
{
    public HappyPayMerchantDtoMap()
    {
        Map(m => m.Id).Index(0);
        Map(m => m.BrandName).Index(1);
        Map(m => m.BusinessName).Index(2);
        Map(m => m.MerchantCategory).Index(3);
    }
}