using System.ComponentModel.DataAnnotations;

namespace Partner_Services.Models.Dto;
public class HappyPayTransactionDto
{
    public string? MerchantId { get; set; }
    public string? BusinessName { get; set; }
    public string? BrandName { get; set; }
    public string? Categories { get; set; }
    [Required]
    public string OrderDate { get; set; }
    public string? CustomerEmail { get; set; }
    public string? ProductName { get; set; }
    public string? ProductPrice { get; set; }
    public string? ProductCategory { get; set; }
}
