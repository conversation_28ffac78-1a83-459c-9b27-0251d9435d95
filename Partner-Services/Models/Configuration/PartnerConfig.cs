using System.Text.Json.Serialization;

namespace Partner_Services.Models.Configuration;

public class PartnerConfig
{
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("adsName")]
    public string AdsName { get; set; } = string.Empty;

    [JsonPropertyName("languageCodes")]
    public string[] LanguageCodes { get; set; } = [];
    
    [JsonPropertyName("defaultLang")]
    public string DefaultLang { get; set; } = string.Empty;
    
    [JsonPropertyName("termsAndConditionsUrl")]
    public string TermsAndConditionsUrl { get; set; } = string.Empty;

    [JsonPropertyName("defaultCurrency")]
    public string DefaultCurrency { get; set; } = string.Empty;

    [JsonPropertyName("logoUrl")]
    public string LogoUrl { get; set; } = string.Empty;

    [JsonPropertyName("onboardingConfigs")]
    public OnboardingConfig[] OnboardingConfigs { get; set; } = [];
}

public class OnboardingConfig
{
    [JsonPropertyName("cmsType")]
    public string CmsType { get; set; } = string.Empty;

    [JsonPropertyName("totalOnboardingSteps")]
    public int TotalOnboardingSteps { get; set; }

    [JsonPropertyName("pluginUrl")]
    public string PluginUrl { get; set; } = string.Empty;
} 