<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <RootNamespace>Partner_Services</RootNamespace>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Customer-Services\Customer-Services.csproj" />
        <ProjectReference Include="..\Shared\Shared.csproj" />
        <ProjectReference Include="..\Merchant-Services\Merchant-Services.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Update="Models\Data\BothNames.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Models\Data\MaleNames.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Models\Data\WomenNames.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Models\ModelsDal\" />
        <Folder Include="Models\ViaBill\" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="CsvHelper" Version="33.0.1" />
    </ItemGroup>

</Project>