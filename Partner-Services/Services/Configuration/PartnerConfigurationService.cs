using Microsoft.Extensions.Configuration;
using Partner_Services.Models.Configuration;

namespace Partner_Services.Services.Configuration;

public interface IPartnerConfigurationService
{
    PartnerConfig? GetPartnerConfig(int partnerId);
    string GetPartnerAdsName(int partnerId);
    string[] GetLanguageCodes(int partnerId);
    string GetDefaultLang(int partnerId);
    string GetTermsAndConditionsUrl(int partnerId);
    string GetPartnerDefaultCurrency(int partnerId);
    OnboardingConfig[] GetOnboardingConfigs(int partnerId);
    string GetPartnerLogoUrl(int partnerId);
}

public class PartnerConfigurationService : IPartnerConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly PartnerConfig[] _partners;

    public PartnerConfigurationService(IConfiguration configuration)
    {
        _configuration = configuration;
        _partners = _configuration.GetSection("Partners").Get<PartnerConfig[]>() ?? [];
    }

    public PartnerConfig? GetPartnerConfig(int partnerId)
    {
        return _partners.FirstOrDefault(p => p.Id == partnerId);
    }

    public string GetPartnerAdsName(int partnerId)
    {
        return GetPartnerConfig(partnerId)?.AdsName ?? string.Empty;
    }

    public string[] GetLanguageCodes(int partnerId)
    {
        return GetPartnerConfig(partnerId)?.LanguageCodes ?? [];
    }

    public string GetDefaultLang(int partnerId)
    {
        return GetPartnerConfig(partnerId)?.DefaultLang ?? string.Empty;
    }

    public OnboardingConfig[] GetOnboardingConfigs(int partnerId)
    {
        return GetPartnerConfig(partnerId)?.OnboardingConfigs ?? [];
    }
    
    public string GetTermsAndConditionsUrl(int partnerId)
    {
        return GetPartnerConfig(partnerId)?.TermsAndConditionsUrl ?? string.Empty;
    }

    public string GetPartnerDefaultCurrency(int partnerId)
    {
        return GetPartnerConfig(partnerId)?.DefaultCurrency ?? string.Empty;
    }

    public string GetPartnerLogoUrl(int partnerId)
    {
        return GetPartnerConfig(partnerId)?.LogoUrl ?? string.Empty;
    }
} 