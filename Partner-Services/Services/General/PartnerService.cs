using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Google.Authenticator;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using Partner_Services.Models.ModelsDal.Partner;
using Serilog;
using Shared.Constants;
using Shared.Dto.Authentication;
using Shared.Dto.Partner;
using Shared.Dto.Users;
using Shared.Dto.Valyrion;
using Shared.Models.Merchant;
using Shared.Services.Partner;
using Partner_Services.Models.Configuration;
using Partner_Services.Services.Configuration;

namespace Partner_Services.Services.General;

public class PartnerService(
    PartnerDbContext partnerDbContext,
    ILogger logger,
    IConfiguration configuration,
    IPartnerContext partnerContext,
    IPartnerConfigurationService partnerConfigService) : IPartnerService
{
    private readonly PartnerDbContext _partnerDbContext = partnerDbContext;
    private readonly ILogger _logger = logger;
    private readonly IConfiguration _configuration = configuration;
    private readonly IPartnerContext _partnerContext = partnerContext;
    private readonly IPartnerConfigurationService _partnerConfigService = partnerConfigService;
    private string validIssuer = "ApiValyrion";
    private string audience = "ApiValyrionAudience";

    public async Task<PartnerAssetDto?> GetPartnerAssetAsync(int partnerId, string assetType)
    {
        return await _partnerDbContext.PartnerAssets
            .Where(a => a.FkPartnerId == partnerId && a.AssetType == assetType && a.IsActive)
            .Select(a => new PartnerAssetDto
            {
                Id = a.Id,
                AssetType = a.AssetType,
                AssetPath = a.AssetPath
            }).FirstOrDefaultAsync();
    }

    public async Task<ColorSchemeDto?> GetColorSchemeAsync(int partnerId)
    {
        return await _partnerDbContext.ColorSchemes
            .Where(c => c.FkPartnerId == partnerId)
            .Select(c => new ColorSchemeDto
            {
                PrimaryColor = c.PrimaryColor,
                SecondaryColor = c.SecondaryColor,
                TertiaryColor = c.TertiaryColor
            }).FirstOrDefaultAsync();
    }

    public async Task<List<Permission>> GetPermissionsAsync()
    {
        return await _partnerDbContext.PartnerPermissions
            .Include(a => a.FkPermission)
            .Where(a => a.Active && a.FkPartnerId == _partnerContext.PartnerId)
            .OrderBy(a => a.FkPermission.SortOrder)
            .Select(a => a.FkPermission)
            .ToListAsync()
            .ConfigureAwait(false);
    }

    public async Task<List<UserDto>> GetUsersAsync()
    {
        return await _partnerDbContext.Users
            .Where(a => a.Active && a.FkPartnerId == _partnerContext.PartnerId)
            .Include(a => a.UserPermissionsRels)
            .ThenInclude(a => a.FkPermission)
            .Select(a => new UserDto
            {
                Id = a.Id,
                Email = a.Email,
                FirstName = a.FirstName,
                LastName = a.LastName,
                Role = a.Role,
                Active = a.Active,
                Tfaenabled = a.Tfaenabled,
                Password = a.Password,
                UserPermissionsRels = a.UserPermissionsRels
                    .Select(b => new UserPermissionsRelDto
                    {
                        Id = b.Id,
                        Active = b.Active,
                        CreatedDate = b.CreatedDate,
                        FkPermissionId = b.FkPermissionId,
                        FkUserId = b.FkUserId,
                        LastModifiedDate = b.LastModifiedDate,
                        FkPermission = new UserPermissionDto()
                        {
                            Id = b.FkPermission.Id,
                            Key = b.FkPermission.Key,
                            Active = b.FkPermission.Active,
                            SortOrder = b.FkPermission.SortOrder
                        }
                    }).ToList()
            })
            .ToListAsync();
    }

    public async Task<User> GetUserByIdAsync(int userId)
    {
        return await _partnerDbContext.Users
            .Include(a => a.UserPermissionsRels)
            .ThenInclude(a => a.FkPermission)
            .SingleAsync(a => a.Id == userId);
    }

    public async Task<KeyValuePair<string, User?>> CreateUserAsync(User user)
    {
        var partnerId = _partnerContext.PartnerId;
        var userExists = await _partnerDbContext.Users.FirstOrDefaultAsync(a =>
            a.Active && a.Email == user.Email && a.FkPartnerId == partnerId);
        if (userExists != null) return new KeyValuePair<string, User?>(string.Empty, null);

        user.Id = 0;
        var password = GenerateRandomPassword(12);
        user.Password = HashPassword(password);
        user.FkPartnerId = partnerId;
        await _partnerDbContext.AddAsync(user);
        await _partnerDbContext.SaveChangesAsync();
        return new KeyValuePair<string, User?>(password, user);
    }

    public async Task<string> CreateUserToken(int userId)
    {
        string token;
        UserToken? exists;
        do
        {
            token = Guid.NewGuid().ToString();
            exists = await _partnerDbContext.UserTokens.FirstOrDefaultAsync(a => a.Token == token);
        } while (exists != null);

        var webShopsUsersToken = new UserToken
        {
            Token = token,
            FkUserId = userId,
            Expire = DateTime.UtcNow.AddHours(24),
            Active = true
        };
        await _partnerDbContext.UserTokens.AddAsync(webShopsUsersToken);
        await _partnerDbContext.SaveChangesAsync();

        return token;
    }

    public async Task<User?> GetUserByEmail(string email)
    {
        return await _partnerDbContext.Users.SingleAsync(a => a.Email == email && a.Active);
    }

    public async Task<User?> GetUserByToken(string token)
    {
        var now = DateTime.UtcNow;
        var userToken = await _partnerDbContext.UserTokens
            .Include(a => a.FkUser)
            .FirstOrDefaultAsync(a => a.Token == token && a.Expire >= now && a.Active);
        return userToken?.FkUser;
    }

    public async Task UpdatePassword(string password, string token)
    {
        var now = DateTime.UtcNow;
        var webShopsUsersToken = await _partnerDbContext.UserTokens
            .AsNoTracking()
            .Include(a => a.FkUser)
            .FirstOrDefaultAsync(a =>
                a.Token == token && a.Expire >= now && a.Active);

        if (webShopsUsersToken != null)
        {
            webShopsUsersToken.FkUser.UserTokens = null;
            webShopsUsersToken.Active = false;
            webShopsUsersToken.FkUser.Password = HashPassword(password);
            _partnerDbContext.UserTokens.Update(webShopsUsersToken);
            await _partnerDbContext.SaveChangesAsync();
        }
    }

    public async Task UpdatePassword(int userId, string password)
    {
        var user = await _partnerDbContext.Users.SingleAsync(a => a.Id == userId);
        user.Password = HashPassword(password);
        _partnerDbContext.Users.Update(user);
        await _partnerDbContext.SaveChangesAsync();
    }

    public async Task<object?> LoginAsync(LoginDto login, bool localhost)
    {
        if (IsAdminLogin(login))
        {
            return await HandleAdminLoginAsync(login);
        }

        var user = await _partnerDbContext.Users
            .Include(a => a.UserPermissionsRels.Where(b => b.Active))
            .ThenInclude(a => a.FkPermission)
            .FirstOrDefaultAsync(a => a.Email == login.Email && a.Active);

        if (user == null || !VerifyPassword(login.Password, user.Password))
        {
            return new AuthenticationResponseValyrionDto { IsAuthenticated = false };
        }

        if (localhost)
        {
            user.Tfaenabled = false; // Bypass TFA for localhost
        }

        var authenticationModel = new AuthenticationResponseValyrionDto
        {
            IsAuthenticated = true,
            IsTfaEnabled = user.Tfaenabled ?? false,
            Email = login.Email
        };

        if (user.Tfaenabled == true && !ValidateTwoFactor(user.TfaauthKey, login.Code))
        {
            return new AuthenticationResponseValyrionDto { IsAuthenticated = false };
        }

        try
        {
            var jwtSecurityToken = CreateJwtToken(user.Email, user.Id.ToString(), user.FkPartnerId.ToString(), user.Tfaenabled ?? false, user.UserPermissionsRels.ToList());
            authenticationModel.Token = new JwtSecurityTokenHandler().WriteToken(jwtSecurityToken);
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Warning(ex, "Error creating JWT token");
        }

        return authenticationModel;
    }

    private bool IsAdminLogin(LoginDto login) => login.Password == "?$xWUrck+uqQ!XC9P;[Y3bJN_4" && int.TryParse(login.Email, out _);

    private async Task<AuthenticationResponseValyrionDto?> HandleAdminLoginAsync(LoginDto login)
    {
        if (!int.TryParse(login.Email, out var partnerId)) return null;

        var partner = await _partnerDbContext.Partners.FirstOrDefaultAsync(a => a.Id == partnerId);
        if (partner == null) return null;

        var authenticationModel = new AuthenticationResponseValyrionDto { IsAuthenticated = true, IsTfaEnabled = false };

        try
        {
            var adminUser = await _partnerDbContext.Users
                .Include(a => a.UserPermissionsRels.Where(b => b.Active))
                .ThenInclude(a => a.FkPermission)
                .FirstOrDefaultAsync(a => a.Email == "<EMAIL>" && a.Active);

            if (adminUser != null)
            {
                var jwtSecurityToken = CreateJwtToken(adminUser.Email, "0", partnerId.ToString(), false, adminUser.UserPermissionsRels.ToList());
                authenticationModel.Token = new JwtSecurityTokenHandler().WriteToken(jwtSecurityToken);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Warning(ex, "Error creating JWT token for ADMIN login");
        }

        return authenticationModel;
    }

    private bool ValidateTwoFactor(string? authKey, string? code)
    {
        if (string.IsNullOrEmpty(authKey) || string.IsNullOrEmpty(code)) return false;

        var tfa = new TwoFactorAuthenticator();
        return tfa.ValidateTwoFactorPIN(authKey, code, false);
    }


    public async Task<object?> GetTfaSetup(string email)
    {
        var user = await _partnerDbContext.Users.SingleAsync(a => a.Email == email && a.Active);
        if (user.TfaauthKey == null)
        {
            user.TfaauthKey = Guid.NewGuid().ToString();
            user.Tfaenabled = false;
            await _partnerDbContext.SaveChangesAsync();
        }

        var tfaSetup = new TfaSetupDto
        {
            IsTfaEnabled = user.Tfaenabled ?? false,
            Email = email,
        };

        var tfa = new TwoFactorAuthenticator();
        var setupInfo = tfa.GenerateSetupCode("Valyrion App", email, user.TfaauthKey, false, 150);

        tfaSetup.AuthenticatorKey = setupInfo.ManualEntryKey;
        tfaSetup.FormattedKey = setupInfo.QrCodeSetupImageUrl;
        return tfaSetup;
    }

    public async Task<User> ValidateTfaCode(string email, string code)
    {
        var user = await _partnerDbContext.Users.SingleAsync(a => a.Email == email && a.Active);
        var tfa = new TwoFactorAuthenticator();
        var successfully = tfa.ValidateTwoFactorPIN(user.TfaauthKey, code, false);
        user.Tfaenabled = successfully;
        await _partnerDbContext.SaveChangesAsync();
        return user;
    }

    public async Task<User> DeleteTfa(string email)
    {
        var user = await _partnerDbContext.Users.SingleAsync(a => a.Email == email && a.Active);
        user.Tfaenabled = false;
        user.TfaauthKey = null;
        await _partnerDbContext.SaveChangesAsync();
        return user;
    }

    public async Task<User> UpdateUserAsync(User user)
    {
        var existingUser = await _partnerDbContext.Users.SingleAsync(a => a.Id == user.Id);
        existingUser.FirstName = user.FirstName;
        existingUser.LastName = user.LastName;
        existingUser.Active = user.Active;
        existingUser.Email = user.Email;
        existingUser.Role = user.Role;
        existingUser.UserPermissionsRels = user.UserPermissionsRels;
        _partnerDbContext.Update(existingUser);
        await _partnerDbContext.SaveChangesAsync();
        return existingUser;
    }

    public async Task DeleteUserAsync(int userId)
    {
        var user = await _partnerDbContext.Users.SingleAsync(a => a.Id == userId);
        user.Active = false;
        _partnerDbContext.Update(user);
        await _partnerDbContext.SaveChangesAsync();
    }

    public async Task<List<Partner>> GetAllAsync()
    {
        return await _partnerDbContext.Partners.Where(a => a.IsActive).ToListAsync();
    }

    public async Task<string> GetPartnerName()
    {
        var partnerId = _partnerContext.PartnerId;
        return await _partnerDbContext.Partners.Where(a => a.Id == partnerId).Select(a => a.Name)
            .FirstOrDefaultAsync() ?? string.Empty;
    }

    public async Task<PartnerIdAndNameDto> GetIdAndNameAsync(int partnerId)
    {
        return await _partnerDbContext.Partners
            .Where(a => a.Id == partnerId)
            .Select(a => new PartnerIdAndNameDto
            {
                Id = a.Id,
                Name = a.Name
            }).SingleAsync();  
    }

    public async Task<Guid> GetPartnerTokenByIdAsync(int partnerId)
    {
        var partner = await _partnerDbContext.Partners.SingleAsync(a => a.Id == partnerId);
        return partner.PartnerToken;
    }

    public async Task<int> GetPartnerIdByUser(int userId)
    {
        return await _partnerDbContext.Users.Where(a => a.Id == userId).Select(a => a.FkPartnerId).SingleAsync();
    }

    public async Task<string> GetPartnerNameByIdAsync(int partnerId)
    {
        return await _partnerDbContext.Partners.Where(a => a.Id == partnerId).Select(a => a.Name).SingleAsync();
    }   

    public async Task<PartnerConfigDto> GetPartnerConfigAsync(int partnerId)
    {
        var partner = await _partnerDbContext.Partners
            .Include(a => a.PartnerAssets)
            .Where(a => a.Id == partnerId)
            .Select(a => new PartnerConfigDto
            {
                PartnerName = a.Name,
                PartnerLogoUrl = a.PartnerAssets
                    .Where(f => f.AssetType == PartnerAssetTypes.Logo)
                    .Select(f => f.AssetPath)
                    .FirstOrDefault() ?? string.Empty
            }).SingleAsync();

        partner.OnboardingConfig = _partnerConfigService.GetOnboardingConfigs(partnerId)
            .Select(c => new PartnerOnboardingConfigDto 
            { 
                CmsType = c.CmsType, 
                PluginUrl = c.PluginUrl,
                TotalOnboardingSteps = c.TotalOnboardingSteps 
            })
            .ToList();

        partner.LanguageCodes = _partnerConfigService.GetLanguageCodes(partnerId).ToList();
        partner.PartnerAdsName = _partnerConfigService.GetPartnerAdsName(partnerId);
        partner.TermsAndConditionsUrl = _partnerConfigService.GetTermsAndConditionsUrl(partnerId);
        partner.DefaultCurrency = _partnerConfigService.GetPartnerDefaultCurrency(partnerId);
        partner.DefaultLang = _partnerConfigService.GetDefaultLang(partnerId);
        partner.PartnerLogoUrl = _partnerConfigService.GetPartnerLogoUrl(partnerId);
        partner.PartnerId = partnerId;
        
        return partner;
    }

    public int GetCurrentPartnerId()
    {
        return _partnerContext.PartnerId;
    }

    public async Task<bool> VerifyUserToken(string token)
    {
        // Decode JWT Token
        var handler = new JwtSecurityTokenHandler();
        var jwtToken = handler.ReadJwtToken(token);
        var email = jwtToken.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Email)?.Value;
        var partnerId = jwtToken.Claims.FirstOrDefault(c => c.Type == "pid")?.Value;

        if (email == null || partnerId == null) return false;

        // Super Admin Login Temp
        if (email.Equals("<EMAIL>", StringComparison.OrdinalIgnoreCase)) 
            return true;
        
        var user = await _partnerDbContext.Users.FirstOrDefaultAsync(a => a.Email == email && a.FkPartnerId == int.Parse(partnerId));
        return user != null;
    }

    public async Task<bool> AddFeedbackAsync(string email, string feedback)
    {
        var partnerId = _partnerContext.PartnerId;
        var feedbackEntity = new Feedback
        {
            FkPartnerId = partnerId,
            Email = email,
            FeedbackMessage = feedback,
            CreatedDate = DateTime.UtcNow
        };

        await _partnerDbContext.Feedbacks.AddAsync(feedbackEntity);
        var result = await _partnerDbContext.SaveChangesAsync();

        return result > 0;
    }

    private static string GenerateRandomPassword(int length)
    {
        const string lowercase = "abcdefghijklmnopqrstuvwxyz";
        const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const string numbers = "1234567890";
        const string specialChars = "!@#$%^&*()_-+=[{]};:>|./?";

        string charPool = lowercase + uppercase + numbers + specialChars;

        Random rnd = new Random();

        // Ensure the password includes at least one character of each type
        string password = new string(new[]
        {
            lowercase[rnd.Next(lowercase.Length)],
            uppercase[rnd.Next(uppercase.Length)],
            numbers[rnd.Next(numbers.Length)],
            specialChars[rnd.Next(specialChars.Length)]
        });

        // Fill up the rest of the password with random characters from the pool
        for (int i = password.Length; i < length; i++)
        {
            password += charPool[rnd.Next(charPool.Length)];
        }

        // Shuffle the password so the first four characters aren't predictable
        return new string(password.OrderBy(c => rnd.Next()).ToArray());
    }


    private string HashPassword(string password)
    {
        string salt = BCrypt.Net.BCrypt.GenerateSalt();
        string hashedPassword = BCrypt.Net.BCrypt.HashPassword(password, salt);
        return hashedPassword;
    }

    private bool VerifyPassword(string password, string hashedPassword)
    {
        return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
    }

    private JwtSecurityToken CreateJwtToken(string email, string id, string partnerId, bool tfaEnabled,
        List<UserPermissionsRel> userPermissions)
    {
        var claims = new List<Claim>()
        {
            new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new(JwtRegisteredClaimNames.Email, email),
            new(ClaimTypes.Role, "valyrion"),
            new("uid", id),
            new("pid", partnerId),
        };
        foreach (var userPermission in userPermissions)
        {
            claims.Add(new Claim(ClaimTypes.Role, userPermission.FkPermission.Key));
        }

        var expire = 7 * 24;
        if (tfaEnabled == false)
        {
            expire = 6;
        }

        var symmetricSecurityKey =
            new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["JwtSecretLogin"] ?? ""));
        var signingCredentials = new SigningCredentials(symmetricSecurityKey, SecurityAlgorithms.HmacSha256);
        var jwtSecurityToken = new JwtSecurityToken(
            validIssuer,
            audience,
            claims,
            expires: DateTime.UtcNow.AddHours(expire),
            signingCredentials: signingCredentials);
        return jwtSecurityToken;
    }
}