using Partner_Services.Models.ModelsDal.Partner;
using Shared.Dto.Partner;
using Shared.Dto.Users;
using Shared.Models.Merchant;

namespace Partner_Services.Services.General;

public interface IPartnerService
{
    Task<PartnerAssetDto?> GetPartnerAssetAsync(int partnerId, string assetType);
    Task<ColorSchemeDto?> GetColorSchemeAsync(int partnerId);
    Task<List<Permission>> GetPermissionsAsync();
    Task<List<UserDto>> GetUsersAsync();
    Task<User> GetUserByIdAsync(int userId);
    Task<KeyValuePair<string, User?>> CreateUserAsync(User user);
    Task<User> UpdateUserAsync(User user);
    Task DeleteUserAsync(int userId);
    Task<string> CreateUserToken(int userId);
    Task<User?> GetUserByEmail(string email);
    Task<User?> GetUserByToken(string token);
    Task UpdatePassword(string password, string token);
    Task UpdatePassword(int userId, string password);
    Task<object?> LoginAsync(LoginDto login, bool localhost);
    Task<object?> GetTfaSetup(string email);
    Task<User> ValidateTfaCode(string email, string code);
    Task<User> DeleteTfa(string email);
    Task<List<Partner>> GetAllAsync();
    Task<string> GetPartnerName();
    Task<PartnerIdAndNameDto> GetIdAndNameAsync(int partnerId);
    Task<Guid> GetPartnerTokenByIdAsync(int partnerId);
    Task<int> GetPartnerIdByUser(int userId);
    Task<string> GetPartnerNameByIdAsync(int partnerId);
    Task<PartnerConfigDto> GetPartnerConfigAsync(int partnerId);
    int GetCurrentPartnerId();
    Task<bool> VerifyUserToken(string token);
    Task<bool> AddFeedbackAsync(string email, string feedback);
}