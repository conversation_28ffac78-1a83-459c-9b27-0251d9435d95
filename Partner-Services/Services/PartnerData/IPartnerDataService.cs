using Customer_Services.Models.ModelsDal.Customer;
using Partner_Services.Models;
using Partner_Services.Models.Dto;
using Partner_Services.Models.ModelsDal.Partner;
using Shared.Dto.Partner;

namespace Partner_Services.Services.PartnerData;

public interface IPartnerDataService
{
    Task<List<Models.ModelsDal.Partner.PartnerData>> GetAllPartnerMerchantDataAsync(int partnerId);
    Task<List<Models.ModelsDal.Partner.PartnerDataMerchant>> GetAllPartnerDataMerchantAsync();
    Task<PartnerDataDto> UpdatePartnerData(PartnerDataDto partnerData);
    Task Unsubscribe(List<Customer> contacts);
    
    Task<List<PointOfContactDto>> GetAllPointOfContactsAsync(int partnerId);
    Task UpdatePointOfContactAsync(PointOfContactDto contact);
    Task<PointOfContactDto> CreatePointOfContactAsync(int partnerId, PointOfContactDto contact);
    Task DeletePointOfContactAsync(int contactId);
    
    Task<List<PartnerIdAndNameDto>> GetAllPartnersAsync();
}