using System.Runtime.ExceptionServices;
using System.Text;
using System.Text.Json;
using Audience.Services.Audience;
using Customer_Services.Models.ModelsDal.Customer;
using Marlin_OS_Integration_API.Models.Order;
using Marlin_OS_Viabill_API.Models.ModelsDto.Webshop;
using Marlin_OS_Viabill_API.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Nest;
using Partner_Services.Models.Constants;
using Partner_Services.Models.Dto;
using Partner_Services.Models.ModelsDal.Partner;
using Renci.SshNet;
using Renci.SshNet.Sftp;
using Shared.Dto.Partner;
using Shared.Elastic.Models.PartnerOrder;
using Shared.Services.Cache;
using Viabill_Services.Models;
using Viabill_Services.Models.Debtor;
using Viabill_Services.Models.Transaction;
using Exception = System.Exception;
using ILogger = Serilog.ILogger;
using CsvHelper;
using CsvHelper.Configuration;
using System.Globalization;
using Partner_Services.Models.Mappers.HappyPay;

namespace Partner_Services.Services.PartnerDataSync;

public class PartnerDataSyncService(
    ILogger logger,
    PartnerDbContext partnerDbContext,
    IConfiguration configuration,
    ElasticClient elasticClient,
    ICustomerService customerService,
    ICacheService cacheService)
    : IPartnerDataSyncService
{
    private const string Viabill = "viabill";


    public async Task SyncDataAsync()
    {
        var partners = await GetPartners();
        foreach (var partner in partners)
        {
            PartnerFiles partnerFiles;
            if(partner.Name.Equals(Viabill, StringComparison.CurrentCultureIgnoreCase))
            {
                partnerFiles = GetViaBillFiles();
            }
            else if(partner.Name.Equals("happy pay", StringComparison.CurrentCultureIgnoreCase))
            {
                partnerFiles = GetHappyPayFiles(partner);
            }
            else
            {
                try
                {
                    partnerFiles = GetPartnerFiles(partner);
                }
                catch (Exception e)
                {
                    logger.ForContext("service_name", GetType().Name).Error(e, "Error getting partner files for partner: {PartnerName}", partner.Name);
                    continue;
                }
            }
            
            if (partnerFiles.Merchants.Count != 0)
            {
                await SyncMerchants(partnerFiles.Merchants, partner);
            }

            if (partnerFiles.Debtors.Count != 0)
            {
                await SyncDebtor(partnerFiles.Debtors, partner);
            }

            if (partnerFiles.Transactions.Count != 0)
            {
                await SyncTransaction(partnerFiles.Transactions, partner);
            }
        }
    }

    private PartnerFiles GetHappyPayFiles(Partner partner)
    {
        var partnerFiles = new PartnerFiles();
        var sftpClient = CreateClient("partnerfiles");
        sftpClient.Connect();
        
        var path = partner.UniqueIdentifier.ToString().ToUpper() + "/new";
        var files = sftpClient.ListDirectory(path).Where(x => x.IsRegularFile && !x.Name.EndsWith(".temp")).Take(9).ToList();
        
        // File type is CSV
        // File Name format is {FileContent}_{Date}.csv
        // File Content is either "CustomerPurchases", Merchants or "Customers"
        // Date is in format YYYY-MM-DD
        foreach (var file in files)
        {
            var fileName = file.Name;
            var fileContent = fileName.Split("_")[0];
            //var date = fileName.Split("_")[1];
            
            switch (fileContent)
            {
                case "CustomerPurchases":
                    var transactions = new List<HappyPayTransactionDto>();
                    using (var fileStream = new MemoryStream())
                    {
                        sftpClient.DownloadFile(file.FullName, fileStream);
                        fileStream.Position = 0;
                        using var reader = new StreamReader(fileStream, Encoding.UTF8, leaveOpen: true);
                        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
                        {
                            HasHeaderRecord = true,
                            MissingFieldFound = null,
                            BadDataFound = null,
                            TrimOptions = TrimOptions.Trim,
                        };
                        using var csv = new CsvReader(reader, config);
                        csv.Context.RegisterClassMap<HappyPayTransactionDtoMap>();
                        try
                        {
                            transactions = csv.GetRecords<HappyPayTransactionDto>().ToList();
                        }
                        catch (Exception e)
                        {
                            logger.ForContext("service_name", GetType().Name).Error(e, "Error parsing transactions CSV file");
                        }
                        partnerFiles.Transactions.Add(new TransactionRootViaBill()
                        {
                            transactions = ConvertTransactionsToTransactionRootViaBill(transactions),
                            Filename = file.Name,
                            FileCreationTime = file.LastWriteTime,
                            Size = file.Length
                        });
                    }
                    break;
                case "Merchants":
                    var merchants = new List<HappyPayMerchantDto>();
                    using (var fileStream = new MemoryStream())
                    {
                        sftpClient.DownloadFile(file.FullName, fileStream);
                        fileStream.Position = 0;
                        using var reader = new StreamReader(fileStream, Encoding.UTF8, leaveOpen: true);
                        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
                        {
                            HasHeaderRecord = true,
                            MissingFieldFound = null,
                            BadDataFound = null,
                            TrimOptions = TrimOptions.Trim,
                        };
                        using var csv = new CsvReader(reader, config);
                        csv.Context.RegisterClassMap<HappyPayMerchantDtoMap>();
                        try
                        {
                            merchants = csv.GetRecords<HappyPayMerchantDto>().ToList();
                        }
                        catch (Exception e)
                        {
                            logger.ForContext("service_name", GetType().Name).Error(e, "Error parsing merchants CSV file");
                        }
                    }
                    partnerFiles.Merchants.Add(new WebshopRootViaBill()
                    {
                        webshops = ConvertMerchantsToWebshopRootViaBill(merchants),
                        Filename = file.Name,
                        FileCreationTime = file.LastWriteTime,
                        Size = file.Length
                    });
                    break;
                case "Customers":
                    var debtors = new List<HappyPayDebtorDto>();
                    using (var fileStream = new MemoryStream())
                    {
                        sftpClient.DownloadFile(file.FullName, fileStream);
                        fileStream.Position = 0;
                        using var reader = new StreamReader(fileStream, Encoding.UTF8, leaveOpen: true);
                        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
                        {
                            HasHeaderRecord = true,
                            MissingFieldFound = null,
                            BadDataFound = null,
                            TrimOptions = TrimOptions.Trim,
                        };
                        using var csv = new CsvReader(reader, config);
                        csv.Context.RegisterClassMap<HappyPayDebtorDtoMap>();
                        try
                        {
                            debtors = csv.GetRecords<HappyPayDebtorDto>().ToList();
                        }
                        catch (Exception e)
                        {
                            logger.ForContext("service_name", GetType().Name).Error(e, "Error parsing debtor CSV file");
                        }
                        partnerFiles.Debtors.Add(new DebtorRootViaBill()
                        {
                            debtors = ConvertDebtorsToDebtorRootViaBill(debtors),
                            Filename = file.Name,
                            FileCreationTime = file.LastWriteTime,
                            Size = file.Length
                        });
                    }
                    break;
                default:
                    continue;
            }
        }

        sftpClient.Disconnect();
        return partnerFiles;
    }

    private List<DebtorViaBill> ConvertDebtorsToDebtorRootViaBill(List<HappyPayDebtorDto> debtors)
    {
        var debtorRoot = new List<DebtorViaBill>();
        foreach (var debtor in debtors)
        {
            debtorRoot.Add(new DebtorViaBill()
            {
                uuid = debtor.Id,
                firstName = debtor.FirstName,
                lastName = debtor.LastName,
                email = debtor.Email,
                emailMarketingAccepted = debtor.HasMarketingConsent,
                created = debtor.CreatedDate ?? string.Empty,
                age = decimal.Parse(debtor.Age ?? "0") > 0 ? decimal.Parse(debtor.Age ?? "0") : null,
                gender = debtor.Gender ?? "unknown",
                phoneNumber = debtor.Phone ?? string.Empty,
                countryCode = "+27",
                debtorAccounts = []
            });
        }
        return debtorRoot;
    }

    private List<TransactionViaBill> ConvertTransactionsToTransactionRootViaBill(List<HappyPayTransactionDto> transactions)
    {
        var transactionRoot = new List<TransactionViaBill>();
        foreach (var transaction in transactions)
        {
            transactionRoot.Add(new TransactionViaBill()
            {
                debtorEmail = transaction.CustomerEmail,
                merchantAccountId = long.Parse(transaction.MerchantId ?? "0"),
                webshopStoreId = long.Parse(transaction.MerchantId ?? "0"),
                status = "PAID",
                amount = decimal.Parse(transaction.ProductPrice?.Replace(".", ",") ?? "0"),
                created = transaction.OrderDate,
                updated = transaction.OrderDate,
                virtualCardTransactionDetails = [
                    new TransactionViaBill.VirtualCardTransactionDetail()
                    {
                        merchantCategory = transaction.Categories ?? string.Empty,
                        merchantName = transaction.BrandName ?? string.Empty,
                        productName = transaction.ProductName ?? string.Empty,
                        productCategory = transaction.ProductCategory ?? string.Empty,
                        productPrice = transaction.ProductPrice?.Replace(".", ",") ?? string.Empty
                    }
                ]

            });
        }
        return transactionRoot;
    }

    private List<WebshopViaBill> ConvertMerchantsToWebshopRootViaBill(List<HappyPayMerchantDto> merchants)
    {
        var webshops = new List<WebshopViaBill>();
        foreach (var merchant in merchants)
        {
            webshops.Add(new WebshopViaBill()
            {
                uuid = merchant.Id,
                name = merchant.BusinessName?.Replace("\"", ""),
                country = "South Africa",
                created = DateTime.MinValue.ToString("yyyy-MM-dd"), // Lowest possible date because it is not available
                lastUpdated = DateTime.MinValue.ToString("yyyy-MM-dd"), // Lowest possible date because it is not available
                webshopStores = [
                    new WebshopStoreViaBill()
                    {
                        id = 0,
                        name = merchant.BrandName?.Replace("\"", ""),
                        url = string.Empty,
                        active = true,
                        created = DateTime.MinValue.ToString("yyyy-MM-dd"), // Lowest possible date because it is not available
                        lastUpdated = DateTime.MinValue.ToString("yyyy-MM-dd"), // Lowest possible date because it is not available
                        categories = [
                            new()
                            {
                                title = merchant.MerchantCategory?.Replace("\"", "") ?? string.Empty
                            }
                        ]
                    }
                ],
                merchantAccounts = []
            });
        }
        return webshops;
    }

    public async Task<PartnerIdAndNameDto> GetPartnerByIdAsync(int partnerId)
    {
        return await partnerDbContext.Partners
            .Where(a => a.Id == partnerId)
            .Select(a => new PartnerIdAndNameDto()
            {
                Id = a.Id,
                Name = a.Name
            })
            .SingleAsync()
            .ConfigureAwait(false);
    }

    private PartnerFiles GetPartnerFiles(Partner partner)
    {
        var partnerFiles = new PartnerFiles();

        var sftpClient = CreateClient("partnerfiles");
        sftpClient.Connect();
        
        // TODO:
        // Foreach Partner in the list - get the files and use the name instead of _viabill
        // Use Partner Data Structure to get the correct files from each partner
        // Use FileName to get the correct file for each partner
        // Make a Default Structure for the Partner Data and use that to get the content of the files
        // Make a Custom Mapping for ViaBill as they are special.
        var path = partner.UniqueIdentifier.ToString().ToUpper() + "/new";
        var files = sftpClient.ListDirectory(path).Where(x => x.IsRegularFile && !x.Name.EndsWith(".temp")).Take(9).ToList();
        foreach (var file in files)
        {
            using (var fileStream = new MemoryStream())
            {
                try
                {
                    var partnerFile = partner.FileDataStructures.FirstOrDefault(a => a.FileName == file.Name);
                    if(partnerFile == null)
                    {
                        logger.ForContext("service_name", GetType().Name).Error("Error getting partner file data structure for partner: {PartnerName} with filename {FileName}", partner.Name, file.Name);
                        continue;
                    }
                    
                    sftpClient.DownloadFile(file.FullName, fileStream);
                    var jsonData = Encoding.UTF8.GetString(fileStream.ToArray());
                    switch (partnerFile.FileType)
                    {
                        case PartnerFileTypes.Merchants:
                        {
                            var merchant = JsonSerializer.Deserialize<WebshopRootViaBill>(jsonData);
                            if (merchant?.webshops != null)
                            {
                                merchant.Filename = file.Name;
                                merchant.FileCreationTime = file.LastWriteTime;
                                merchant.Size = file.Length;
                                partnerFiles.Merchants.Add(merchant);
                            }

                            break;
                        }
                        case PartnerFileTypes.Debtors:
                        {
                            var debtors = JsonSerializer.Deserialize<DebtorRootViaBill>(jsonData);
                            if (debtors?.debtors != null)
                            {
                                debtors.Filename = file.Name;
                                debtors.FileCreationTime = file.LastWriteTime;
                                debtors.Size = file.Length;
                                partnerFiles.Debtors.Add(debtors);
                            }

                            break;
                        }
                        case PartnerFileTypes.Transactions:
                        {
                            var transactions =
                                JsonSerializer.Deserialize<TransactionRootViaBill>(jsonData);
                            if (transactions?.transactions != null)
                            {
                                transactions.Filename = file.Name;
                                transactions.FileCreationTime = file.LastWriteTime;
                                transactions.Size = file.Length;
                                partnerFiles.Transactions.Add(transactions);
                            }

                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.ForContext("service_name", GetType().Name).Error(ex,
                        "Error downloading data from our ftp with viabill data with filename {FileFullName}",
                        file.FullName);
                }
            }
        }

        sftpClient.Disconnect();
        return partnerFiles;
    }

    private PartnerFiles GetViaBillFiles()
    {
        var partnerFiles = new PartnerFiles();
        //using FileStream file1 =
        //    System.IO.File.OpenRead(
        //        "C:/Users/<USER>/Desktop/Temp/2022-12-08-transactions_mcc"); //Transaction
        //using var streamReader1 = new StreamReader(file1);
        //using var jsonreader1 = new JsonTextReader(streamReader1);
        //while (jsonreader1.Read())
        //{
        //    if (jsonreader1.TokenType != JsonToken.StartObject) continue;
        //    var entry = new Newtonsoft.Json.JsonSerializer().Deserialize<TransactionRootViaBill>(jsonreader1);
        //    viaBillFiles.Transactions.Add(entry);
        //}

        var sftpClient = CreateClient("viaadsfiles");
        sftpClient.Connect();
        
        // TODO:
        // Foreach Partner in the list - get the files and use the name instead of _viabill
        // Use Partner Data Structure to get the correct files from each partner
        // Use FileName to get the correct file for each partner
        // Make a Default Structure for the Partner Data and use that to get the content of the files
        // Make a Custom Mapping for ViaBill as they are special.
        
        var files = sftpClient.ListDirectory(Viabill).Where(x => x.IsRegularFile).Take(9).ToList();
        foreach (var file in files)
        {
            using (MemoryStream fileStream = new MemoryStream())
            {
                try
                {
                    var fileName = file.Name.Split("_")[1];
                    sftpClient.DownloadFile(file.FullName, fileStream);
                    var jsonData = Encoding.UTF8.GetString(fileStream.ToArray());
                    if (fileName == "webshops")
                    {
                        var webshop = JsonSerializer.Deserialize<WebshopRootViaBill>(jsonData);
                        if (webshop?.webshops != null)
                        {
                            webshop.Filename = file.Name;
                            webshop.FileCreationTime = file.LastWriteTime;
                            webshop.Size = file.Length;
                            partnerFiles.Merchants.Add(webshop);
                        }
                    }
                    else if (fileName == "debtors")
                    {
                        var debtors = JsonSerializer.Deserialize<DebtorRootViaBill>(jsonData);
                        
                        if (debtors?.debtors != null)
                        {
                            // Filters out some of the debtors, so we only get the ones with the correct country code (DK)
                            debtors.debtors = debtors.debtors.Where(a => a.countryCode is "+45" or "").ToList();
                            
                            debtors.Filename = file.Name;
                            debtors.FileCreationTime = file.LastWriteTime;
                            debtors.Size = file.Length;
                            partnerFiles.Debtors.Add(debtors);
                        }
                    }
                    else if (fileName == "transactions")
                    {
                        var transactions =
                            JsonSerializer.Deserialize<TransactionRootViaBill>(jsonData);
                        if (transactions?.transactions != null)
                        {
                            transactions.Filename = file.Name;
                            transactions.FileCreationTime = file.LastWriteTime;
                            transactions.Size = file.Length;
                            partnerFiles.Transactions.Add(transactions);
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.ForContext("service_name", GetType().Name).Error(ex,
                        "Error downloading data from our ftp with viabill data with filename {FileFullName}",
                        file.FullName);
                }
            }
        }

        sftpClient.Disconnect();
        return partnerFiles;
    }

    private async Task SyncTransaction(List<TransactionRootViaBill> transactions, Partner partner)
    {
        var contactsDic = new Dictionary<string, string>();
        var isPartnerViabill = partner.Name.Equals(Viabill, StringComparison.CurrentCultureIgnoreCase);
        // if not all transactions have a debtorEmail, we need to get the email from the customer service
        var allTransactionsHaveDebtorEmail = transactions.All(a => a.transactions != null && a.transactions.Any(b => b.debtorEmail != null));
        if(!allTransactionsHaveDebtorEmail)
        {
            foreach (var contact in await customerService.GetAllRawAsync(isPartnerViabill).ConfigureAwait(false))
            {
                if (contact.PartnerId != null && !string.IsNullOrEmpty(contact.Email))
                {
                    contactsDic.Add(contact.PartnerId, contact.Email);
                }
            }
        }

        var partnerData = await partnerDbContext.PartnerDatas.Where(a => a.FkPartnerId == partner.Id)
            .Include(a => a.PartnerDataMerchants)
            .ToListAsync();
        var partnerDataMerchants = partnerData.SelectMany(a => a.PartnerDataMerchants).ToList();

        foreach (var transactionRoot in transactions)
        {
            var created = DateTime.UtcNow;
            var orders = new List<ElasticPartnerOrderEvent>();
            if (transactionRoot.transactions == null) continue;
            
            var partnerFile =
                await partnerDbContext.Files.SingleOrDefaultAsync(a =>
                    a.FileName == transactionRoot.Filename && a.FkPartnerId == partner.Id).ConfigureAwait(false);
            if (partnerFile != null)
            {
                if (partnerFile.Done)
                {
                    if(isPartnerViabill)
                        MoveFileSftpViaBill(transactionRoot.Filename); // TEMP For ViaBill
                    else
                        MoveFileSftp(transactionRoot.Filename, partner);
                    continue;
                }

                partnerFile.FileImportTime = DateTime.UtcNow;
            }
            else
            {
                partnerFile = new Models.ModelsDal.Partner.File
                {
                    FkPartnerId = partner.Id,
                    FileName = transactionRoot.Filename,
                    FileCreationTime = transactionRoot.FileCreationTime,
                    FileImportTime = DateTime.UtcNow,
                    Size = transactionRoot.Size,
                    ObjectCount = transactionRoot.transactions!.Count,
                    Done = false,
                    Type = "Transaction"
                };
                await partnerDbContext.AddAsync(partnerFile);
            }
            await partnerDbContext.SaveChangesAsync();

            foreach (var transaction in transactionRoot.transactions
                         .Select((value, i) => new {i, value}))
            {
                try
                {
                    var email = "none";
                    if(transaction.value.debtorEmail != null)
                    {
                        email = transaction.value.debtorEmail;
                    }
                    else if (!string.IsNullOrEmpty(transaction.value.debtorAccountId.ToString()) &&
                        transaction.value.debtorAccountId != null &&
                        contactsDic.ContainsKey(transaction.value.debtorAccountId.ToString()))
                    {
                        email = contactsDic[transaction.value.debtorAccountId.ToString()];
                    }

                    int? merchantId = null;
                    if (transaction.value.merchantAccountId != null)
                    {
                        merchantId = partnerData.SingleOrDefault(a =>
                            a.Ids != null && a.Ids.Split(",")
                                .Contains(transaction.value.merchantAccountId.ToString()))?.FkMerchantId;
                        if (merchantId == null && transaction.value.webshopStoreId != null)
                        {
                            merchantId = partnerDataMerchants
                                .SingleOrDefault(a => a.Uuid == transaction.value.webshopStoreId.ToString())
                                ?.FkPartnerData.FkMerchantId;
                        }
                        if(merchantId == null)
                        {
                            merchantId = partnerData
                                .SingleOrDefault(a => a.Uuid == transaction.value.merchantAccountId.ToString())
                                ?.FkMerchantId;
                        }
                    }

                    bool canceled = transaction.value.status == "CANCELLED" ||
                                    transaction.value.status == "REJECTED" ||
                                    transaction.value.refundTimestamp != null;

                    ElasticPartnerOrderEventCardTransactionDetails elasticViabillOrderEventCardTransactionDetails =
                        null;
                    var virtualCardTransactionsDetails = transaction.value.virtualCardTransactionDetails.First();
                    if (virtualCardTransactionsDetails.merchantCategory != null)
                    {
                        elasticViabillOrderEventCardTransactionDetails =
                            new ElasticPartnerOrderEventCardTransactionDetails
                            {
                                Authorization_method = virtualCardTransactionsDetails.authorizationMethod ?? "n/a",
                                Merchant_category = virtualCardTransactionsDetails.merchantCategory,
                                Merchant_name = virtualCardTransactionsDetails.merchantName ?? "n/a",
                            };
                    }
                    
                    // Partner Information for Elastic
                    var partnerOrderData = new ElasticPartnerOrderEventPartner()
                    {
                        Id = partner.Id.ToString(),
                        Name = partner.Name,
                    };

                    orders.Add(ElasticOrder(created, canceled, merchantId, email,
                        transactionRoot.Filename, transaction.i, transaction.value,
                        elasticViabillOrderEventCardTransactionDetails, partnerOrderData));
                }
                catch (Exception ex)
                {
                    logger.ForContext("service_name", GetType().Name).Error(ex,
                        "Error converting viabill transaction to elastic format");
                }
            }

            var error = false;
            try
            {
                var bulkAllObservable = elasticClient.BulkAll(orders, d => d
                    .Index("customers-orders-partners")
                    .BackOffRetries(10)
                    .BackOffTime("30s")
                    .RefreshOnCompleted()
                    .MaxDegreeOfParallelism(Environment.ProcessorCount)
                    .Size(1000)
                    .BufferToBulk((desc, docs) => desc.CreateMany(docs))
                );

                var waitHandle = new ManualResetEvent(false);
                ExceptionDispatchInfo exceptionDispatchInfo = null;

                var observer = new BulkAllObserver(
                    onNext: response => { },
                    onError: exception =>
                    {
                        exceptionDispatchInfo = ExceptionDispatchInfo.Capture(exception);
                        waitHandle.Set();
                    },
                    onCompleted: () => waitHandle.Set());

                bulkAllObservable.Subscribe(observer);
                waitHandle.WaitOne();
                exceptionDispatchInfo?.Throw();
            }
            catch (Exception ex)
            {
                logger.ForContext("service_name", GetType().Name)
                    .Error(ex, "Error transferring viabill order to elastic");
                error = true;
            }

            if (error == false)
            {
                partnerFile.RunTimeSeconds =
                    Convert.ToInt16((DateTime.UtcNow - partnerFile.FileImportTime).TotalSeconds);
                partnerFile.Done = true;
                partnerDbContext.Update(partnerFile);
                await partnerDbContext.SaveChangesAsync().ConfigureAwait(false);
                if(isPartnerViabill)
                    MoveFileSftpViaBill(transactionRoot.Filename);
                else
                    MoveFileSftp(transactionRoot.Filename, partner);
            }
        }
    }

    private async Task SyncDebtor(List<DebtorRootViaBill> debtors, Partner partner)
    {
        var errors = new List<string>();
        var countryCodesMapping = new CountryCodes().CountryCodesMapping;
        var isPartnerViabill = partner.Name.Equals(Viabill, StringComparison.CurrentCultureIgnoreCase);

        foreach (var debtor in debtors)
        {
            var partnerFile =
                await partnerDbContext.Files.SingleOrDefaultAsync(a => a.FileName == debtor.Filename && a.FkPartnerId == partner.Id)
                    .ConfigureAwait(false);
            if (partnerFile != null)
            {
                if (partnerFile.Done)
                {
                    if (isPartnerViabill)
                    {
                        MoveFileSftpViaBill(debtor.Filename);
                    } 
                    else
                    {
                        MoveFileSftp(debtor.Filename, partner);
                    }
                    continue;
                }
                else
                {
                    partnerFile.FileImportTime = DateTime.UtcNow;
                }
            }
            else
            {
                partnerFile = new Models.ModelsDal.Partner.File
                {
                    FkPartnerId = partner.Id,
                    FileName = debtor.Filename,
                    FileCreationTime = debtor.FileCreationTime,
                    FileImportTime = DateTime.UtcNow,
                    Size = debtor.Size,
                    ObjectCount = debtor.debtors!.Count,
                    Done = false,
                    Type = "Debtors",
                    CreatedDate = DateTime.UtcNow,
                    
                    LastModifiedDate = DateTime.UtcNow
                };
                await partnerDbContext.AddAsync(partnerFile);
                await partnerDbContext.SaveChangesAsync();
            }

            var contactsDic = new Dictionary<string, Customer>();
            var contacts = await customerService.GetAllWithoutCheckAsync(partner.Id).ConfigureAwait(false);
            foreach (var contact in contacts)
            {
                if (contact.PartnerGuid != null)
                {
                    contactsDic.TryAdd(contact.PartnerGuid, contact);
                }
            }

            foreach (var partnerDebtor in debtor.debtors)
            {
                var partnerProduct = "";
                byte? numberOfInstallments = null;
                foreach (var debtorProduct in partnerDebtor.debtorAccounts.SelectMany(debtorAccounts => debtorAccounts.debtorProducts.Where(a =>
                             a.replaced == null)))
                {
                    partnerProduct = debtorProduct.type;
                    numberOfInstallments = debtorProduct.numberOfInstallments;
                }

                try
                {
                    Customer? customerExists = null;
                    if (contactsDic.TryGetValue(partnerDebtor.uuid, out var value))
                    {
                        customerExists = value;
                    }

                    byte? age = partnerDebtor.age != null ? Convert.ToByte(partnerDebtor.age) : (byte?) null;
                    if (string.IsNullOrEmpty(partnerDebtor.gender))
                    {
                        var firstName = partnerDebtor.firstName ?? "";
                        firstName = firstName.Split(" ")[0];
                        var genders = contacts.Where(a =>
                                a.Gender != "Unknown" && a.FirstName.Split(' ').FirstOrDefault() == firstName)
                            .GroupBy(a => a.Gender)
                            .ToList();

                        var male = genders.FirstOrDefault(g => g.Key == "Male")?.Count() ?? 0;
                        var female = genders.FirstOrDefault(g => g.Key == "Female")?.Count() ?? 0;

                        double difference = Math.Abs(male - female);
                        double average = (double) (male + female) / 2;
                        double percentageDifference = (difference / average) * 100;
                        if (percentageDifference > 90 && firstName != "")
                        {
                            if (male > female)
                            {
                                partnerDebtor.gender = "Male";
                            }
                            else
                            {
                                partnerDebtor.gender = "Female";
                            }
                        }
                        else
                        {
                            partnerDebtor.gender = "Unknown";
                        }
                    }

                    if (customerExists == null)
                    {
                        if (partnerDebtor.email != null)
                        {
                            var countryCode = partnerDebtor.countryCode ?? "";
                            var country = countryCodesMapping
                                .FirstOrDefault(
                                    a => a.Value == countryCode.Replace("+", ""))
                                .Key;
                            customerExists = new Customer
                            {
                                FkPartnerId = partner.Id,
                                CreatedDate = Convert.ToDateTime(partnerDebtor.created),
                                LastModifiedDate = Convert.ToDateTime(partnerDebtor.lastUpdated),
                                Email = partnerDebtor.email.Trim().ToLowerInvariant() ?? "",
                                Country = country ?? "",
                                ZipCode = partnerDebtor.zipCode ?? "",
                                Active = !partnerDebtor.accountDisabled,
                                FirstName = partnerDebtor.firstName ?? "",
                                LastName = partnerDebtor.lastName ?? "",
                                PhoneNumber = $"{partnerDebtor.countryCode}{partnerDebtor.phoneNumber}",
                                PartnerGuid = partnerDebtor.uuid,
                                PartnerId = partnerDebtor.debtorAccounts.FirstOrDefault()?.id.ToString(),
                                CreditAvailability = 0,
                                PartnerProduct = partnerProduct,
                                PartnerInstallments = numberOfInstallments,
                                Gender = partnerDebtor.gender,
                                Age = age,
                                MarketingStatus = partnerDebtor.emailMarketingAccepted,
                                MissedOpenMails = 0,
                            };
                            await customerService.AddContactPartner(customerExists);
                        }
                    }
                    else
                    {
                        if (partnerDebtor.email != null)
                        {
                            customerExists.Email = partnerDebtor.email.Trim().ToLowerInvariant() ?? "";
                        }

                        customerExists.LastModifiedDate = Convert.ToDateTime(partnerDebtor.lastUpdated);
                        customerExists.CreditAvailability = 0;
                        customerExists.PartnerProduct = partnerProduct;
                        customerExists.Gender = partnerDebtor.gender;
                        customerExists.Age = age;
                        customerExists.PartnerInstallments = numberOfInstallments;
                        customerExists.MarketingStatus = partnerDebtor.emailMarketingAccepted;
                        customerExists.PhoneNumber = $"{partnerDebtor.countryCode}{partnerDebtor.phoneNumber}";
                        customerExists.Active = !partnerDebtor.accountDisabled;
                        customerExists.ZipCode = partnerDebtor.zipCode ?? "";
                        await customerService.UpdateContactPartner(customerExists);
                    }
                }
                catch (Exception ex)
                {
                    errors.Add(partnerDebtor.uuid);
                    logger.ForContext("service_name", GetType().Name).Error(ex,
                        "Error Creating/Updating viabill debtor with id: {Id}",
                        partnerDebtor.uuid);
                }
            }

            await customerService.SaveCustomerDb();

            //remove duplicates
            await customerService.RemoveDuplicatesAudience(partner.Id);

            //Check if any record failed to Create/Update
            if (errors.Count == 0)
            {
                partnerFile.RunTimeSeconds =
                    Convert.ToInt32((DateTime.UtcNow - partnerFile.FileImportTime).TotalSeconds);
                partnerFile.Done = true;
                partnerDbContext.Update(partnerFile);
                if(isPartnerViabill)
                    MoveFileSftpViaBill(debtor.Filename);
                else
                    MoveFileSftp(debtor.Filename, partner);
                await partnerDbContext.SaveChangesAsync().ConfigureAwait(false);
                
                cacheService.RemoveData($"AllCustomerEmails_{partner.Id}");
            }
        }

        // are these needed?
        await customerService.SaveCustomerDb();
        await partnerDbContext.SaveChangesAsync().ConfigureAwait(false);
    }

    private async Task SyncMerchants(List<WebshopRootViaBill> merchantsPartner, Partner partner)
    {
        foreach (var partnerMerchants in merchantsPartner)
        {
            var partnerFile =
                await partnerDbContext.Files.SingleOrDefaultAsync(a => a.FileName == partnerMerchants.Filename && a.FkPartnerId == partner.Id)
                    .ConfigureAwait(false);
            var isPartnerViabill = partner.Name.Equals(Viabill, StringComparison.CurrentCultureIgnoreCase);
            if (partnerFile != null)
            {
                if (partnerFile.Done)
                {
                    if(isPartnerViabill)
                        MoveFileSftpViaBill(partnerMerchants.Filename);
                    else
                        MoveFileSftp(partnerMerchants.Filename, partner);

                    continue;
                }
                else
                {
                    partnerFile.FileImportTime = DateTime.UtcNow;
                }
            }
            else
            {
                partnerFile = new Models.ModelsDal.Partner.File
                {
                    FkPartnerId = partner.Id,
                    FileName = partnerMerchants.Filename,
                    FileCreationTime = partnerMerchants.FileCreationTime,
                    FileImportTime = DateTime.UtcNow,
                    Size = partnerMerchants.Size,
                    ObjectCount = partnerMerchants.webshops!.Count,
                    Done = false,
                    Type = "Webshop",
                    CreatedDate = DateTime.UtcNow,
                    LastModifiedDate = DateTime.UtcNow
                };
                await partnerDbContext.AddAsync(partnerFile);
                await partnerDbContext.SaveChangesAsync();
            }

            foreach (var webshop in partnerMerchants.webshops)
            {
                //Webshop
                var partnerDataDb =
                    await partnerDbContext.PartnerDatas
                        .Include(a => a.PartnerDataMerchants)
                        .SingleOrDefaultAsync(a => a.Uuid == webshop.uuid && a.FkPartnerId == partner.Id).ConfigureAwait(false);
                if (partnerDataDb == null)
                {
                    partnerDataDb = new Models.ModelsDal.Partner.PartnerData
                    {
                        FkPartnerId = partner.Id,
                        Uuid = webshop.uuid,
                        CreatedDate = DateTime.UtcNow,
                        LastModifiedDate = DateTime.UtcNow,
                        PartnerCreatedDate = Convert.ToDateTime(webshop.created),
                        PartnerLastModifiedDate = Convert.ToDateTime(webshop.lastUpdated),
                        Country = webshop.country,
                        Active = true,
                        Name = webshop.name ?? "",
                        Ids = ""
                    };
                    //MerchantAccounts
                    var merchantAccounts = webshop.merchantAccounts?.Where(a => a.ledger == "DK")
                        .Select(a => a.id.ToString())
                        .ToList() ?? [];
                    partnerDataDb.Ids = string.Join(",", merchantAccounts);

                    await partnerDbContext.AddAsync(partnerDataDb);
                    await partnerDbContext.SaveChangesAsync().ConfigureAwait(false);
                }
                else
                {
                    partnerDataDb.Uuid = webshop.uuid;
                    partnerDataDb.Country = webshop.country;
                    partnerDataDb.Name = webshop.name ?? "";
                    partnerDataDb.PartnerLastModifiedDate = Convert.ToDateTime(webshop.lastUpdated);
                    partnerDataDb.LastModifiedDate = DateTime.UtcNow;
                    //MerchantAccounts
                    var merchantAccounts = webshop.merchantAccounts?.Where(a => a.ledger == "DK")
                        .Select(a => a.id.ToString())
                        .Distinct().ToList() ?? [];
                    partnerDataDb.Ids = string.Join(",", merchantAccounts);
                    partnerDbContext.Update(partnerDataDb);
                }

                //WebshopStores
                foreach (var webshopStore in webshop.webshopStores)
                {
                    var partnerDataMerchantDb =
                        partnerDataDb.PartnerDataMerchants.SingleOrDefault(a => a.Uuid == webshopStore.id.ToString());
                    if (partnerDataMerchantDb == null)
                    {
                        partnerDataMerchantDb = new PartnerDataMerchant()
                        {
                            Uuid = webshopStore.id.ToString(),
                            CreatedDate = DateTime.UtcNow,
                            LastModifiedDate = DateTime.UtcNow,
                            PartnerCreatedDate = Convert.ToDateTime(webshopStore.created),
                            PartnerLastModifiedDate = Convert.ToDateTime(webshopStore.lastUpdated),
                            Name = webshopStore.name ?? "",
                            Active = webshopStore.active,
                            Url = webshopStore.url,
                            FkPartnerDataId = partnerDataDb.Id,
                            Categories = ConvertCategories(webshopStore.categories)
                        };
                        partnerDataDb.PartnerDataMerchants.Add(partnerDataMerchantDb);
                    }
                    else
                    {
                        partnerDataMerchantDb.Uuid = webshopStore.id.ToString();
                        partnerDataMerchantDb.Name = webshopStore.name ?? "";
                        partnerDataMerchantDb.Active = webshopStore.active;
                        partnerDataMerchantDb.Url = webshopStore.url;
                        partnerDataMerchantDb.LastModifiedDate = DateTime.UtcNow;
                        partnerDataMerchantDb.PartnerLastModifiedDate = Convert.ToDateTime(webshopStore.lastUpdated);
                        partnerDataMerchantDb.Categories = ConvertCategories(webshopStore.categories);
                        partnerDbContext.Update(partnerDataMerchantDb);
                    }
                }
            }

            //Check if any record failed to Create/Update
            partnerFile.RunTimeSeconds = Convert.ToInt32((DateTime.UtcNow - partnerFile.FileImportTime).TotalSeconds);
            partnerFile.Done = true;
            partnerDbContext.Update(partnerFile);
            await partnerDbContext.SaveChangesAsync().ConfigureAwait(false);
            
            if(isPartnerViabill)
            {
                MoveFileSftpViaBill(partnerMerchants.Filename);
            }
            else
            {
                MoveFileSftp(partnerMerchants.Filename, partner);   
            }
        }

        await partnerDbContext.SaveChangesAsync().ConfigureAwait(false);
    }

    private string ConvertCategories(List<WebshopCategoryViaBill> webshopStoreCategories)
    {
        var categories = "";
        foreach (var category in webshopStoreCategories)
        {
            categories += $"{category.title},";
        }

        return categories.Substring(0, categories.Length - 1);
    }

    private ElasticPartnerOrderEvent ElasticOrder(DateTime created, bool canceled, int? merchantId, string email,
        string fileName,
        int index, TransactionViaBill transaction,
        ElasticPartnerOrderEventCardTransactionDetails? cardTransactionDetails, ElasticPartnerOrderEventPartner partner)
    {
        return new ElasticPartnerOrderEvent
        {
            Order_date = Convert.ToDateTime(transaction.created),
            Event = new ElasticEvent
            {
                created = created,
                ingested = DateTime.UtcNow
            },
            Shop_order = new ElasticPartnerOrderEventShopOrder
            {
                Last_modified = Convert.ToDateTime(transaction.updated),
                Order_number = transaction.orderNumber,
                Currency = transaction.currency,
                Status = transaction.status,
                Transaction = transaction.transaction,
                Total_price_tax_included = transaction.amount,
                Webshop_id = merchantId.ToString() ?? "n/a",
                Merchant_account_id = transaction.merchantAccountId?.ToString() ?? "n/a",
                Webshop_store_id = transaction.webshopStoreId?.ToString() ?? "n/a",
                IsCanceled = canceled
            },
            Customer = new ElasticPartnerOrderEventCustomer
            {
                Email = email,
            },
            File = new ElasticPartnerOrderEventFileOrder
            {
                Name = fileName,
                Object_index = index + 1
            },
            Card_transaction_details = cardTransactionDetails,
            Partner = partner
        };
    }

    private SftpClient CreateClient(string containerName)
    {
        //Init ViaBill sftp connection
        var keyString = configuration["ValyrionFtpKey"]!.Replace(" ", "\r\n");
        keyString = keyString.Replace("-----BEGIN\r\nRSA\r\nPRIVATE\r\nKEY-----",
            "-----BEGIN RSA PRIVATE KEY-----");
        keyString = keyString.Replace("-----END\r\nRSA\r\nPRIVATE\r\nKEY-----", "-----END RSA PRIVATE KEY-----");
        MemoryStream keyStream = new MemoryStream(Encoding.UTF8.GetBytes(keyString));
        var key = new PrivateKeyFile(keyStream);
        return new SftpClient("valyrion.blob.core.windows.net",
            $"valyrion.{containerName}.valyrion", key);
    }

    private void MoveFileSftpViaBill(string fileName)
    {
        var now = DateTime.UtcNow;
        var destinationDirectoryName = $"imported/{now.Year}-{now.Month:D2}";

        var sftpClient = CreateClient("viaadsfiles");
        sftpClient.Connect();
        var files = sftpClient.ListDirectory(Viabill).ToList();
        var fileExist = files.SingleOrDefault(a => a.Name == fileName);
        if (fileExist != null)
        {
            if (!sftpClient.Exists(destinationDirectoryName))
            {
                sftpClient.CreateDirectory(destinationDirectoryName);
            }

            sftpClient.RenameFile($"{Viabill}/{fileName}",
                $"{destinationDirectoryName}/{fileName}");
        }

        sftpClient.Disconnect();
    }

    private void MoveFileSftp(string fileName, Partner partner)
    {
        var now = DateTime.UtcNow;
        var destinationDirectoryName = $"{partner.UniqueIdentifier.ToString().ToUpper()}/imported/{now.Year}-{now.Month:D2}";
        var originDirectoryName = $"{partner.UniqueIdentifier.ToString().ToUpper()}/new";

        var sftpClient = CreateClient("partnerfiles");
        sftpClient.Connect();
        var files = sftpClient.ListDirectory(originDirectoryName).ToList();
        var fileExist = files.SingleOrDefault(a => a.Name == fileName);
        if (fileExist != null)
        {
            if (!sftpClient.Exists(destinationDirectoryName))
            {
                sftpClient.CreateDirectory(destinationDirectoryName);
            }

            sftpClient.RenameFile($"{originDirectoryName}/{fileName}",
                $"{destinationDirectoryName}/{fileName}");
        }

        sftpClient.Disconnect();
    }
    
    private async Task<List<Partner>> GetPartners()
    {
        return await partnerDbContext.Partners.Where(a => a.IsActive).AsNoTracking().ToListAsync();
    }
}


