using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Partner_Services.Models;
using Partner_Services.Models.Dto;
using Partner_Services.Models.ModelsDal.Partner;
using Partner_Services.Services.General;
using Partner_Services.Services.PartnerData;
using Partner_Services.Services.Configuration;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Dto.Partner;
using ILogger = Serilog.ILogger;

namespace Partner_Services.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class PartnerController( 
    IPartnerDataService partnerDataService, 
    IPartnerService partnerService,
    ILogger logger) : ControllerBase
{

    [HttpGet]
    public async Task<IActionResult> GetAllPartnerDataAsync([FromHeader] int partnerId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllPartnerDataAsync"))
            {
                return Ok(await partnerDataService.GetAllPartnerMerchantDataAsync(partnerId));
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting all partner data");
            return BadRequest(ex.ToString());
        }
    }

    [HttpGet]
    [Route("config/{partnerId:int}")]
    [AllowAnonymous]
    [PartnerAuthExempt]
    public async Task<IActionResult> GetPartnerConfig(int partnerId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetPartnerConfig"))
            {
                var partnerConfig = await partnerService.GetPartnerConfigAsync(partnerId);
                
                if (partnerConfig == null)
                {
                    return NotFound($"Partner with ID {partnerId} not found");
                }

                return Ok(partnerConfig);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting partner config for ID: {0}", partnerId);
            return BadRequest(ex.ToString());
        }
    }

    [HttpPut]
    public async Task<IActionResult> UpdatePartnerMerchantAsync(PartnerDataDto partnerData)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdatePartnerMerchantAsync"))
            {
                await partnerDataService.UpdatePartnerData(partnerData);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while updating partner merchant data");
            return BadRequest(ex.ToString());
        }
    }
    
    [HttpGet]
    [Route("pointOfContacts")]
    public async Task<IActionResult> GetAllPointOfContactsAsync([FromHeader] int partnerId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllPointOfContactsAsync"))
            {
                return Ok(await partnerDataService.GetAllPointOfContactsAsync(partnerId));
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting all points of contacts");
            return BadRequest(ex.ToString());
        }
    }
    
    [HttpPost]
    [Route("pointOfContacts")]
    public async Task<IActionResult> CreatePointOfContact([FromHeader] int partnerId, PointOfContactDto contact)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CreatePointOfContact"))
            {
                await partnerDataService.CreatePointOfContactAsync(partnerId, contact);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while creating Point of Contact");
            return BadRequest(ex.ToString());
        }
    }

    [HttpPut]
    [Route("pointOfContacts")]
    public async Task<IActionResult> UpdatePointOfContact(PointOfContactDto contact)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdatePointOfContact"))
            {
                await partnerDataService.UpdatePointOfContactAsync(contact);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while updating Point of Contact with Id: {0}", contact.Id);
            return BadRequest(ex.ToString());
        }
    }
    
    [HttpDelete]
    [Route("pointOfContacts/{contactId:int}")]
    public async Task<IActionResult> DeletePointOfContact(int contactId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "DeletePointOfContact"))
            {
                await partnerDataService.DeletePointOfContactAsync(contactId);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while Deleting Point of Contact with Id: {0}", contactId);
            return BadRequest(ex.ToString());
        }
    }
    
    
    [HttpGet]
    [Route("external/all")]
    [AllowAnonymous]
    [PartnerAuthExempt]
    public async Task<IActionResult> GetAllPartnersExternal()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllPartnersExternal"))
            {
                return Ok(await partnerDataService.GetAllPartnersAsync());
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting all Partners External");
            return BadRequest(ex.ToString());
        }
    }
}