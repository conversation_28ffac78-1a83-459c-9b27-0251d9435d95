using System.Text;
using Endpoints.Controllers.Integration;
using Endpoints.Controllers.Integration.Plugins;
using Integration.Services.Plugins.Integration;
using Integration.Services.Plugins.ShopifyIntegration;
using Integration.Services.SendGrid;
using Marlin_OS_Integration_API.Services.Plugins.CustomIntegration;
using Marlin_OS_Integration_API.Services.Plugins.DanDomainIntegration;
using Marlin_OS_MerchantSync_API.Services.Plugins.DanDomainIntegration;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Newtonsoft.Json;
using Shared.Dto.Custom.Event;
using Shared.Dto.Custom.Order;
using Shared.Dto.CustomDto;
using Shared.Dto.DandomainClassic;
using Shared.Elastic.Models.Behavior;
using Shared.Models;
using ShopifySharp;
using Webshop_Service.Services.DanDomain;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace Integration_Services.Tests.Integration.Controllers.Plugins;

[TestClass]
public class ShopifyIntegrationControllerTests
{
    private Mock<ILogger> _mockLogger;
    private Mock<IIntegrationService> _integrationService;
    private Mock<IMerchantService> _merchantService;
    private Mock<Webshop_Service.Services.Shopify.IShopifyService> _shopifyService;
    private Mock<ShopifyPluginService> _shopifyPluginService;

    [TestInitialize]
    public void Setup()
    {
        //Logger
        _mockLogger = new Mock<ILogger>();
        var mockContextLogger = new Mock<ILogger>();
        _mockLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);
        mockContextLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);

        //_danDomainClassicService = new Mock<IDanDomainClassicService>();
        _integrationService = new Mock<IIntegrationService>();
        _merchantService = new Mock<IMerchantService>();
        _shopifyPluginService = new Mock<ShopifyPluginService>();
        _shopifyService = new Mock<Webshop_Service.Services.Shopify.IShopifyService>();
    }

    ///////////////////////////////////////////////////////////////////////////
    ////////////////////////////////// Event //////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////

    /*[TestMethod]
    public async Task AddEventAsync_ValidateRequest_Ok()
    {
        //Arrange
        var mockHttpContext = new DefaultHttpContext
        {
            Request =
            {
                Headers =
                {
                    {"apikey", "123"}
                }
            }
        };

        var shopifyIntegrationController = new ShopifyIntegrationController(_mockLogger.Object,
            _integrationService.Object, _merchantService.Object, _shopifyService.Object)
        {
            ControllerContext = new ControllerContext
            {
                HttpContext = mockHttpContext
            }
        };

        _integrationService
            .Setup(service => service.AddBehaviorEventAsync(It.IsAny<ElasticBehaviorEvent>(), It.IsAny<string>()))
            .ReturnsAsync(new ResponseDto
            {
                Success = true
            });

        //Act
        var body =
            "{\"Url\": \"https://www.raw58.dk/products/tojstativ-vincent-sort\",\n  \"UserAgent\": \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36\",\n  \"EmailGuid\": \"\",\n  \"Email\": \"\",\n  \"EventType\": \"ProductLook\",\n  \"Session\": \"03373619-480a-44ec-b218-4b2422fc55ba\",\n  \"Fingerprint\": \"bbecd17c61148089a2562994dfd277368edde0738647395f96289a2e39e69a15\",\n  \"FingerprintOld\": \"\",\n  \"FingerprintRaw\": \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36MacInteltrueEurope/Copenhagenda-DKnullGoogle Inc.42|explicit|speakers|2|1|0|44100|suspended-261793329\",\n  \"ProductId\": \"6938135953490\",\n  \"VariantId\": \"40085849571410\",\n  \"Sku\": \"Fod1x2 + A4 + A5 + A6 + A7x2 + A9 + A10x2 + B7 + C8x4 + 4x100 cm rør + 2x50 cm sort:-)\",\n  \"WebShopId\": \"4595\",\n  \"PluginVersion\": \"1.0.2\"}";
        var result = await shopifyIntegrationController.PixelEventAsync(body);

        //Assert
        Assert.IsNotNull(result);
        Assert.IsInstanceOfType(result, typeof(OkObjectResult));
    }*/


    /*[TestMethod]
    public async Task AddEventAsync_ValidateRequest_BadRquest()
    {
        //Arrange
        var mockHttpContext = new DefaultHttpContext
        {
            Request =
            {
                Headers =
                {
                    {"apikey", "123"}
                }
            }
        };

        var shopifyIntegrationController = new ShopifyIntegrationController(_mockLogger.Object,
            _integrationService.Object, _merchantService.Object, _shopifyService.Object)
        {
            ControllerContext = new ControllerContext
            {
                HttpContext = mockHttpContext
            }
        };

        _integrationService
            .Setup(service => service.AddBehaviorEventAsync(It.IsAny<ElasticBehaviorEvent>(), It.IsAny<string>()))
            .ReturnsAsync(new ResponseDto
            {
                Success = true
            });

        //Act
        var body = "";
        var result = await shopifyIntegrationController.PixelEventAsync(body);

        //Assert
        Assert.IsNotNull(result);
        Assert.IsInstanceOfType(result, typeof(BadRequestResult));
    }*/
}