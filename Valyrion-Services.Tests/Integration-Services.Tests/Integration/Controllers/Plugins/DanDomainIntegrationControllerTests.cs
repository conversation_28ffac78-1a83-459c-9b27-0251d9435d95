using System.Text;
using Endpoints.Controllers.Integration;
using Endpoints.Controllers.Integration.Plugins;
using Integration.Services.Plugins.Integration;
using Integration.Services.SendGrid;
using Marlin_OS_Integration_API.Services.Plugins.CustomIntegration;
using Marlin_OS_Integration_API.Services.Plugins.DanDomainIntegration;
using Marlin_OS_MerchantSync_API.Services.Plugins.DanDomainIntegration;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Newtonsoft.Json;
using Shared.Dto.Custom.Event;
using Shared.Dto.Custom.Order;
using Shared.Dto.CustomDto;
using Shared.Dto.DandomainClassic;
using Shared.Elastic.Models.Behavior;
using Shared.Models;
using Webshop_Service.Services.DanDomain;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace Integration_Services.Tests.Integration.Controllers.Plugins;

[TestClass]
public class DandomainIntegrationControllerTests
{
    private Mock<ILogger> _mockLogger;
    private Mock<IIntegrationService> _integrationService;
    private Mock<IMerchantService> _merchantService;
    private Mock<DanDomainPluginService> _danDomainPluginService;

    [TestInitialize]
    public void Setup()
    {
        //Logger
        _mockLogger = new Mock<ILogger>();
        var mockContextLogger = new Mock<ILogger>();
        _mockLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);
        mockContextLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);

        //_danDomainClassicService = new Mock<IDanDomainClassicService>();
        _integrationService = new Mock<IIntegrationService>();
        _merchantService = new Mock<IMerchantService>();
        _danDomainPluginService = new Mock<DanDomainPluginService>();
    }

    ///////////////////////////////////////////////////////////////////////////
    ////////////////////////////////// Event //////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////

    /*[TestMethod]
    public async Task AddEventAsync_ValidateRequest_Ok()
    {
        //Arrange
        var mockHttpContext = new DefaultHttpContext
        {
            Request =
            {
                Headers =
                {
                    {"apikey", "123"}
                }
            }
        };

        var danDomainIntegrationController =
            new DanDomainIntegrationController(_mockLogger.Object, _integrationService.Object, _merchantService.Object)
            {
                ControllerContext = new ControllerContext
                {
                    HttpContext = mockHttpContext
                }
            };

        _integrationService
            .Setup(service => service.AddBehaviorEventAsync(It.IsAny<ElasticBehaviorEvent>(), It.IsAny<string>()))
            .ReturnsAsync(new ResponseDto
            {
                Success = true
            });

        //Act
        var danDomainBehaviorDto = new DanDomainBehaviorDto
        {
            ShopIdentifier = "123"
        };
        var result = await danDomainIntegrationController.AddBehaviorEventAsync(danDomainBehaviorDto);

        //Assert
        Assert.IsNotNull(result);
        Assert.IsInstanceOfType(result, typeof(OkResult));
    }*/
}