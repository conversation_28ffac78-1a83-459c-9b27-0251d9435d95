using System.Text;
using AutoMapper;
using Endpoints.Controllers.Integration;
using Endpoints.Controllers.Integration.Plugins;
using Integration.Services.Plugins.Integration;
using Integration.Services.SendGrid;
using Marlin_OS_Integration_API.Services.Plugins.CustomIntegration;
using Marlin_OS_Integration_API.Services.Plugins.DanDomainIntegration;
using Marlin_OS_Integration_API.Services.Plugins.WoocommerceIntegration;
using Marlin_OS_MerchantSync_API.Services.Plugins.DanDomainIntegration;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Newtonsoft.Json;
using Shared.Dto.BehaviorDto;
using Shared.Dto.Custom.Event;
using Shared.Dto.Custom.Order;
using Shared.Dto.CustomDto;
using Shared.Dto.DandomainClassic;
using Shared.Elastic.Models.Behavior;
using Shared.Models;
using Webshop_Service.Services.DanDomain;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace Integration_Services.Tests.Integration.Controllers.Plugins;

[TestClass]
public class WoocommerceIntegrationControllerTests
{
    private Mock<ILogger> _mockLogger;

    //private Mock<IDanDomainService> _danDomainService;
    private Mock<IIntegrationService> _integrationService;
    private Mock<IMerchantService> _merchantService;
    private Mock<WoocommercePluginService> _woocommercePluginService;
    private Mock<IMapper> _mapper;

    [TestInitialize]
    public void Setup()
    {
        //Logger
        _mockLogger = new Mock<ILogger>();
        var mockContextLogger = new Mock<ILogger>();
        _mockLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);
        mockContextLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);

        _integrationService = new Mock<IIntegrationService>();
        _merchantService = new Mock<IMerchantService>();
        _woocommercePluginService = new Mock<WoocommercePluginService>();
        _mapper = new Mock<IMapper>();
    }

    ///////////////////////////////////////////////////////////////////////////
    ////////////////////////////////// Event //////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////

    /*[TestMethod]
    public async Task AddEventAsync_ValidateRequest_Ok()
    {
        //Arrange
        var mockHttpContext = new DefaultHttpContext
        {
            Request =
            {
                Headers =
                {
                    {"apikey", "123"}
                }
            }
        };

        var woocomerceIntegrationController = new WoocommerceIntegrationController(_mockLogger.Object,
            _integrationService.Object, _merchantService.Object, _mapper.Object)
        {
            ControllerContext = new ControllerContext
            {
                HttpContext = mockHttpContext
            }
        };

        _integrationService
            .Setup(service => service.AddBehaviorEventAsync(It.IsAny<ElasticBehaviorEvent>(), It.IsAny<string>()))
            .ReturnsAsync(new ResponseDto
            {
                Success = true
            });

        //Act
        var behaviorEventDto = new BehaviorEventDto
        {
        };
        var result = await woocomerceIntegrationController.AddBehaviorEventAsync(behaviorEventDto);

        //Assert
        Assert.IsNotNull(result);
        Assert.IsInstanceOfType(result, typeof(OkObjectResult));
    }*/
}