using System.Text;
using Endpoints.Controllers.Integration;
using Endpoints.Controllers.Integration.Plugins;
using Integration.Services.Plugins.Integration;
using Integration.Services.SendGrid;
using Marlin_OS_Integration_API.Services.Plugins.CustomIntegration;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Newtonsoft.Json;
using Shared.Dto.Custom.Event;
using Shared.Dto.Custom.Order;
using Shared.Dto.CustomDto;
using Shared.Models;
using ILogger = Serilog.ILogger;

namespace Integration_Services.Tests.Integration.Controllers.Plugins;

[TestClass]
public class CustomIntegrationControllerTests
{
    private Mock<ILogger> _mockLogger;
    private Mock<ICustomIntegrationService> _customerIntegrationService;
    private Mock<IIntegrationService> _integrationService;

    [TestInitialize]
    public void Setup()
    {
        //Logger
        _mockLogger = new Mock<ILogger>();
        var mockContextLogger = new Mock<ILogger>();
        _mockLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);
        mockContextLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);

        _customerIntegrationService = new Mock<ICustomIntegrationService>();
    }

    ///////////////////////////////////////////////////////////////////////////
    ////////////////////////////////// Event //////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////

    /*[TestMethod]
    public async Task AddEventAsync_ValidateRequest_Ok()
    {
        //Arrange
        var mockHttpContext = new DefaultHttpContext
        {
            Request =
            {
                Headers =
                {
                    {"apikey", "123"}
                }
            }
        };

        var customerIntegrationController =
            new CustomIntegrationController(_mockLogger.Object, _customerIntegrationService.Object)
            {
                ControllerContext = new ControllerContext
                {
                    HttpContext = mockHttpContext
                }
            };

        _customerIntegrationService
            .Setup(service => service.AddEventAsync(It.IsAny<CustomEventDto>(), It.IsAny<string>()))
            .ReturnsAsync(new CustomResponse
            {
                Message = "Success",
                Status = StatusCode.Ok
            });

        //Act
        var customEvent = new CustomEventDto();
        var result = await customerIntegrationController.AddEventAsync(customEvent);

        //Assert
        Assert.IsNotNull(result);
        Assert.IsInstanceOfType(result, typeof(OkObjectResult));
    }*/

    /*[TestMethod]
    public async Task AddEventAsync_ValidateRequest_Unauthorized()
    {
        //Arrange
        var mockHttpContext = new DefaultHttpContext
        {
            Request =
            {
                Headers =
                {
                    {"apikey", "123"}
                }
            }
        };

        var customerIntegrationController =
            new CustomIntegrationController(_mockLogger.Object, _customerIntegrationService.Object, _integrationService.Object)
            {
                ControllerContext = new ControllerContext
                {
                    HttpContext = mockHttpContext
                }
            };

        _customerIntegrationService
            .Setup(service => service.AddEventAsync(It.IsAny<CustomEventDto>(), It.IsAny<string>()))
            .ReturnsAsync(new CustomResponse
            {
                Message = "Success",
                Status = StatusCode.Unauthorized
            });

        //Act
        var customEvent = new CustomEventDto();
        var result = await customerIntegrationController.AddEventAsync(customEvent);

        //Assert
        Assert.IsNotNull(result);
        Assert.IsInstanceOfType(result, typeof(UnauthorizedObjectResult));
    }*/

    /*[TestMethod]
    public async Task AddEventAsync_ValidateRequest_BadRequest()
    {
        //Arrange
        var mockHttpContext = new DefaultHttpContext
        {
            Request =
            {
                Headers =
                {
                    {"apikey", "123"}
                }
            }
        };

        var customerIntegrationController =
            new CustomIntegrationController(_mockLogger.Object, _customerIntegrationService.Object, _integrationService.Object)
            {
                ControllerContext = new ControllerContext
                {
                    HttpContext = mockHttpContext
                }
            };

        _customerIntegrationService
            .Setup(service => service.AddEventAsync(It.IsAny<CustomEventDto>(), It.IsAny<string>()))
            .ReturnsAsync(new CustomResponse
            {
                Message = "Success",
                Status = StatusCode.BadRequest
            });

        //Act
        var customEvent = new CustomEventDto();
        var result = await customerIntegrationController.AddEventAsync(customEvent);

        //Assert
        Assert.IsNotNull(result);
        Assert.IsInstanceOfType(result, typeof(UnprocessableEntityObjectResult));
    }*/

    ///////////////////////////////////////////////////////////////////////////
    ////////////////////////////////// Orders //////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////
    /*[TestMethod]
    public async Task AddOrdersAsync_ValidateRequest_Ok()
    {
        //Arrange
        var mockHttpContext = new DefaultHttpContext
        {
            Request =
            {
                Headers =
                {
                    {"apikey", "123"}
                }
            }
        };

        var customerIntegrationController =
            new CustomIntegrationController(_mockLogger.Object, _customerIntegrationService.Object, _integrationService.Object)
            {
                ControllerContext = new ControllerContext
                {
                    HttpContext = mockHttpContext
                }
            };

        _customerIntegrationService
            .Setup(service => service.AddOrderAsync(It.IsAny<List<CustomOrderDto>>(), It.IsAny<string>()))
            .ReturnsAsync(new CustomResponse
            {
                Message = "Success",
                Status = StatusCode.Ok
            });

        //Act
        var customerOrders = new List<CustomOrderDto>();
        var result = await customerIntegrationController.AddOrdersAsync(customerOrders);

        //Assert
        Assert.IsNotNull(result);
        Assert.IsInstanceOfType(result, typeof(OkObjectResult));
    }*/

    /*[TestMethod]
    public async Task AddOrdersAsync_ValidateRequest_BadRequest()
    {
        //Arrange
        var mockHttpContext = new DefaultHttpContext
        {
            Request =
            {
                Headers =
                {
                    {"apikey", "123"}
                }
            }
        };

        var customerIntegrationController =
            new CustomIntegrationController(_mockLogger.Object, _customerIntegrationService.Object, _integrationService.Object)
            {
                ControllerContext = new ControllerContext
                {
                    HttpContext = mockHttpContext
                }
            };

        _customerIntegrationService
            .Setup(service => service.AddOrderAsync(It.IsAny<List<CustomOrderDto>>(), It.IsAny<string>()))
            .ReturnsAsync(new CustomResponse
            {
                Message = "Success",
                Status = StatusCode.BadRequest
            });

        //Act
        var customerOrders = new List<CustomOrderDto>();
        var result = await customerIntegrationController.AddOrdersAsync(customerOrders);

        //Assert
        Assert.IsNotNull(result);
        Assert.IsInstanceOfType(result, typeof(BadRequestObjectResult));
    }*/

    ///////////////////////////////////////////////////////////////////////////
    ////////////////////////////////// Order //////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////
    /*[TestMethod]
    public async Task AddOrderAsync_ValidateRequest_Ok()
    {
        //Arrange
        var mockHttpContext = new DefaultHttpContext
        {
            Request =
            {
                Headers =
                {
                    {"apikey", "123"}
                }
            }
        };

        var customerIntegrationController =
            new CustomIntegrationController(_mockLogger.Object, _customerIntegrationService.Object, _integrationService.Object)
            {
                ControllerContext = new ControllerContext
                {
                    HttpContext = mockHttpContext
                }
            };

        _customerIntegrationService
            .Setup(service => service.AddOrderAsync(It.IsAny<List<CustomOrderDto>>(), It.IsAny<string>()))
            .ReturnsAsync(new CustomResponse
            {
                Message = "Success",
                Status = StatusCode.Ok
            });

        //Act
        var result = await customerIntegrationController.AddOrderAsync(new CustomOrderDto());

        //Assert
        Assert.IsNotNull(result);
        Assert.IsInstanceOfType(result, typeof(OkObjectResult));
    }*/

    /*[TestMethod]
    public async Task AddOrderAsync_ValidateRequest_BadRequest()
    {
        //Arrange
        var mockHttpContext = new DefaultHttpContext
        {
            Request =
            {
                Headers =
                {
                    {"apikey", "123"}
                }
            }
        };

        var customerIntegrationController =
            new CustomIntegrationController(_mockLogger.Object, _customerIntegrationService.Object, _integrationService.Object)
            {
                ControllerContext = new ControllerContext
                {
                    HttpContext = mockHttpContext
                }
            };

        _customerIntegrationService
            .Setup(service => service.AddOrderAsync(It.IsAny<List<CustomOrderDto>>(), It.IsAny<string>()))
            .ReturnsAsync(new CustomResponse
            {
                Message = "Success",
                Status = StatusCode.BadRequest
            });

        //Act
        var result = await customerIntegrationController.AddOrderAsync(new CustomOrderDto());

        //Assert
        Assert.IsNotNull(result);
        Assert.IsInstanceOfType(result, typeof(BadRequestObjectResult));
    }*/

    ///////////////////////////////////////////////////////////////////////////
    ////////////////////////////////// Javascript //////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////
    /*[TestMethod]
    public async Task AddEventJavascriptAsync_ValidateRequest_Ok()
    {
        //Arrange
        var mockHttpContext = new DefaultHttpContext
        {
            Request =
            {
                Headers =
                {
                    {"apikey", "123"}
                }
            }
        };

        var customerIntegrationController =
            new CustomIntegrationController(_mockLogger.Object, _customerIntegrationService.Object, _integrationService.Object)
            {
                ControllerContext = new ControllerContext
                {
                    HttpContext = mockHttpContext
                }
            };

        _customerIntegrationService
            .Setup(service => service.AddEventAsync(It.IsAny<CustomJavascriptEvenDto>()))
            .ReturnsAsync(new CustomResponse
            {
                Message = "Success",
                Status = StatusCode.Ok
            });

        //Act
        var customEvent = new CustomJavascriptEvenDto();
        var result = await customerIntegrationController.AddEventJavascriptAsync(customEvent);

        //Assert
        Assert.IsNotNull(result);
        Assert.IsInstanceOfType(result, typeof(OkResult));
    }*/

    /*[TestMethod]
    public async Task AddEventJavascriptAsync_ValidateRequest_Unauthorized()
    {
        //Arrange
        var mockHttpContext = new DefaultHttpContext
        {
            Request =
            {
                Headers =
                {
                    {"apikey", "123"}
                }
            }
        };

        var customerIntegrationController =
            new CustomIntegrationController(_mockLogger.Object, _customerIntegrationService.Object)
            {
                ControllerContext = new ControllerContext
                {
                    HttpContext = mockHttpContext
                }
            };

        _customerIntegrationService
            .Setup(service => service.AddEventAsync(It.IsAny<CustomJavascriptEvenDto>()))
            .ReturnsAsync(new CustomResponse
            {
                Message = "Success",
                Status = StatusCode.Unauthorized
            });

        //Act
        var customEvent = new CustomJavascriptEvenDto();
        var result = await customerIntegrationController.AddEventJavascriptAsync(customEvent);

        //Assert
        Assert.IsNotNull(result);
        Assert.IsInstanceOfType(result, typeof(UnauthorizedObjectResult));
    }*/

    /*[TestMethod]
    public async Task AddEventJavascriptAsync_ValidateRequest_BadRequest()
    {
        //Arrange
        var mockHttpContext = new DefaultHttpContext
        {
            Request =
            {
                Headers =
                {
                    {"apikey", "123"}
                }
            }
        };

        var customerIntegrationController =
            new CustomIntegrationController(_mockLogger.Object, _customerIntegrationService.Object)
            {
                ControllerContext = new ControllerContext
                {
                    HttpContext = mockHttpContext
                }
            };

        _customerIntegrationService
            .Setup(service => service.AddEventAsync(It.IsAny<CustomJavascriptEvenDto>()))
            .ReturnsAsync(new CustomResponse
            {
                Message = "Success",
                Status = StatusCode.BadRequest
            });

        //Act
        var customEvent = new CustomJavascriptEvenDto();
        var result = await customerIntegrationController.AddEventJavascriptAsync(customEvent);

        //Assert
        Assert.IsNotNull(result);
        Assert.IsInstanceOfType(result, typeof(BadRequestObjectResult));
    }*/
}