using System.Text;
using Endpoints.Controllers.Integration;
using Integration.Services.SendGrid;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Newtonsoft.Json;
using ILogger = Serilog.ILogger;

namespace Integration_Services.Tests.Integration.Controllers;

[TestClass]
public class SendGridControllerTests
{
    private Mock<ILogger> _mockLogger;
    private Mock<ISendGridWebHookService> _mockSendGridWebHookService;
    private SendGridController _sendGridController;

    [TestInitialize]
    public void Setup()
    {
        //Logger
        _mockLogger = new Mock<ILogger>();
        var mockContextLogger = new Mock<ILogger>();
        _mockLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);
        mockContextLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);

        _mockSendGridWebHookService = new Mock<ISendGridWebHookService>();
        _sendGridController = new SendGridController(_mockLogger.Object, _mockSendGridWebHookService.Object);
    }


    [TestMethod]
    public async Task SendGrid_ValidateRequest_Unauthorized()
    {
        //Arrange
        var requestBody = new
        {
            Property1 = "value1",
            Property2 = "value2"
        };
        var requestJson = JsonConvert.SerializeObject(requestBody);
        var byteArray = Encoding.UTF8.GetBytes(requestJson);
        await using var stream = new MemoryStream(byteArray);
        var mockHttpContext = new DefaultHttpContext
        {
            Request = {Body = stream}
        };

        var sendGridController = new SendGridController(_mockLogger.Object, _mockSendGridWebHookService.Object)
        {
            ControllerContext = new ControllerContext
            {
                HttpContext = mockHttpContext
            }
        };

        //Act
        var result = await sendGridController.SendGrid();

        //Assert
        Assert.IsNotNull(result);
        Assert.IsInstanceOfType(result, typeof(UnauthorizedResult));
    }
}