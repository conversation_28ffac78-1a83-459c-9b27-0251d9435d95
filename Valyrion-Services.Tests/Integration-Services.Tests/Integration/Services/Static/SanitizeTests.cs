using System.Text;
using Endpoints.Controllers.Integration;
using Integration.Services.SendGrid;
using Integration.Services.Static;
using Marlin_OS_Integration_API.Services.Static;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Newtonsoft.Json;
using ILogger = Serilog.ILogger;

namespace Integration_Services.Tests.Integration.Services.Static;

[TestClass]
public class SanitizeTests
{
    private Mock<ILogger> _mockLogger;

    [TestInitialize]
    public void Setup()
    {
        //Logger
        _mockLogger = new Mock<ILogger>();
        var mockContextLogger = new Mock<ILogger>();
        _mockLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);
        mockContextLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);
    }

    [TestMethod]
    [DataRow(null, "n/a")]
    [DataRow("abc12345678", "12345678")]
    [DataRow("12345678", "12345678")]
    public async Task Sanitize_Phone(string? input, string expected)
    {
        //Arrange

        //Act
        var result = Sanitize.Phone(input);

        //Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(result, expected);
    }
}