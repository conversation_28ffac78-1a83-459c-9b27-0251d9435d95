using System.Text;
using Endpoints.Controllers.Integration;
using Integration.Services.SendGrid;
using Integration.Services.Static;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Newtonsoft.Json;
using ILogger = Serilog.ILogger;

namespace Integration_Services.Tests.Integration.Services.Static;

[TestClass]
public class PriceConverterTests
{
    private Mock<ILogger> _mockLogger;

    [TestInitialize]
    public void Setup()
    {
        //Logger
        _mockLogger = new Mock<ILogger>();
        var mockContextLogger = new Mock<ILogger>();
        _mockLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);
        mockContextLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);
    }

    [TestMethod]
    [DataRow(2000, "2000-9999")]
    [DataRow(1500, "1000-1999")]
    [DataRow(700, "500-999")]
    [DataRow(400, "200-499")]
    [DataRow(150, "100-199")]
    [DataRow(50, "0-99")]
    [DataRow(null, "n/a")]
    public async Task PriceConvert_ConvertPrice(int? input, string expected)
    {
        //Arrange

        //Act
        var result = PriceConverter.ConvertPrice(input);

        //Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(result, expected);
    }
}