using Integration.Services.Error;
using Moq;
using Shared.Models;
using ILogger = Serilog.ILogger;

namespace Integration_Services.Tests.Integration.Services.Error;

[TestClass]
public class ErrorServiceControllerTests
{
    private Mock<ILogger> _mockLogger;
    private ErrorService _errorService;

    [TestInitialize]
    public void Setup()
    {
        //Logger
        _mockLogger = new Mock<ILogger>();
        var mockContextLogger = new Mock<ILogger>();
        _mockLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);
        mockContextLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);

        _errorService = new ErrorService(_mockLogger.Object);
    }

    [TestMethod]
    public void ErrorService_AddErrorAsync_Success()
    {
        //Arrange
        var error = new ErrorLoggingDto
        {
            Error = "Test error",
            Url = "test url"
        };

        //Act
        var result = _errorService.AddErrorAsync(error);

        //Assert
        Assert.IsNotNull(result);
        Assert.AreEqual("Success", result.Message);
        Assert.AreEqual(true, result.Success);
    }

    [TestMethod]
    public void ErrorService_AddDebugAsync_Success()
    {
        //Arrange
        var error = new ErrorLoggingDto
        {
            Error = "Test error",
            Url = "test url"
        };

        //Act
        var result = _errorService.AddDebugAsync(error);

        //Assert
        Assert.IsNotNull(result);
        Assert.AreEqual("Success", result.Message);
        Assert.AreEqual(true, result.Success);
    }

    [TestMethod]
    public void ErrorService_AddInformationAsync_Success()
    {
        //Arrange
        var error = new ErrorLoggingDto
        {
            Error = "Test error",
            Url = "test url"
        };

        //Act
        var result = _errorService.AddInformationAsync(error);

        //Assert
        Assert.IsNotNull(result);
        Assert.AreEqual("Success", result.Message);
        Assert.AreEqual(true, result.Success);
    }

    [TestMethod]
    public void ErrorService_AddWarningAsync_Success()
    {
        //Arrange
        var error = new ErrorLoggingDto
        {
            Error = "Test error",
            Url = "test url"
        };

        //Act
        var result = _errorService.AddWarningAsync(error);

        //Assert
        Assert.IsNotNull(result);
        Assert.AreEqual("Success", result.Message);
        Assert.AreEqual(true, result.Success);
    }
}