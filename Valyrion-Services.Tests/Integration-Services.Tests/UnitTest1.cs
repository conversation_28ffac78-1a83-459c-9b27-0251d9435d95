using Endpoints.Controllers.Integration;
using Integration.Services.SendGrid;
using Microsoft.Extensions.Logging;
using Moq;
using SerilogTimings.Extensions;
using ILogger = Serilog.ILogger;

namespace Integration_Services.Tests;

[TestClass]
public class UnitTest1
{
    private Mock<ILogger> _mockLogger;
    private Mock<ISendGridWebHookService> _mockSendGridWebHookService;
    private SendGridController _sendGridController;

    [TestInitialize]
    public void Setup()
    {
        //Logger
        _mockLogger = new Mock<ILogger>();
        var mockContextLogger = new Mock<ILogger>();
        _mockLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);
        mockContextLogger.Setup(logger => logger.ForContext(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<bool>()))
            .Returns(mockContextLogger.Object);

        _mockSendGridWebHookService = new Mock<ISendGridWebHookService>();
        _sendGridController = new SendGridController(_mockLogger.Object, _mockSendGridWebHookService.Object);
    }


    //naming: MethodName_Scenario_ExpectedOutcome
    [TestMethod]
    public async Task TestMethod1()
    {
        //Arrange
        //_mockSendGridWebHookService.Setup(service => service.WebHook()).ReturnsAsync();


        //mockContextLogger.Setup(logger => logger.TimeOperation(It.IsAny<string>(), It.IsAny<string>()))
        //   .Returns(DisposableAction(() => { }));


        //Act
        var response = await _sendGridController.SendGrid();

        //Assert
        Assert.IsNotNull(response);
        Console.WriteLine();
        //_mockLogger.Verify(logger => logger.);
    }
}