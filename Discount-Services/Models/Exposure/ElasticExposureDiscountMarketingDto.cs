using System.Text.Json.Serialization;

namespace Discount_Services.Models.Exposure
{
    public class ElasticExposureDiscountMarketingDto
    {
        [JsonPropertyName("Event")]
        public string Event { get; set; }

        [JsonPropertyName("Channel")]
        public string Channel { get; set; }

        [Json<PERSON>ropertyName("Page_number")]
        public int PageNumber { get; set; }

        [JsonPropertyName("Page_size")]
        public int PageSize { get; set; }
    }
}