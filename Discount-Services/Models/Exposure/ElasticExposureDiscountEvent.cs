using System.Text.Json.Serialization;
using Shared.Elastic.Models.ElasticExposure;

namespace Discount_Services.Models.Exposure
{
    public class ElasticExposureDiscountDisplayDto
    {
        [JsonPropertyName("Event_date")]
        public DateTime EventDate { get; set; }

        [JsonPropertyName("Event_received")]
        public DateTime EventReceived { get; set; }

        [Json<PERSON>ropertyName("Partner")]
        public ElasticExposurePartnerDto? Partner { get; set; }

        [JsonPropertyName("Merchant")]
        public ElasticExposureMerchantListDto? Merchant { get; set; }

        [JsonPropertyName("Customer")]
        public ElasticExposureCustomerDto? Customer { get; set; }

        [JsonPropertyName("Marketing")]
        public ElasticExposureDiscountMarketingDto? Marketing { get; set; }

        [JsonPropertyName("Discount")]
        public ElasticExposureDiscountListDto? Discount { get; set; }
    }
}