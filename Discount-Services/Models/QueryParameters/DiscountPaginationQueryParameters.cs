using Microsoft.AspNetCore.Mvc;

namespace Discount_Services.Models.QueryParameters;

public class DiscountPaginationQueryParameters
{
    public string Email { get; set; }
    public int Size { get; set; } = 10; // Default value
    public int Page { get; set; } = 1;  // Default value
    public string? Campaign { get; set; } = null;

    [FromQuery(Name = "use_mock")]
    public bool UseMock { get; set; } = false;
}