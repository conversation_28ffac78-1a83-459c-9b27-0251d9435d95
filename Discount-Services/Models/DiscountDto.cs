namespace Discount_Services.Models;

/// <summary>
/// Represents a discount with the information used to display the Overview, as well as the details.
/// </summary>
public class DiscountDto
{
    /// <summary>
    /// Represents the identifier of the discount.
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// Represents the source for the details image of the discount.
    /// </summary>
    public string ImageSrc { get; set; }

    /// <summary>
    /// Represents the title of the discount.
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    /// Represents the subtitle of the discount.
    /// </summary>
    public string Subtitle { get; set; }

    /// <summary>
    /// Represents the description of the discount.
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// Represents the expiration date of the discount.
    /// </summary>
    public DateOnly ExpireDate { get; set; }

    /// <summary>
    /// Represents the discount code associated with the current customer.
    /// </summary>
    public string? DiscountCode { get; set; }

    /// <summary>
    /// Represents the redirect link associated with the discount.
    /// </summary>
    public string RedirectLink { get; set; }

    /// <summary>
    /// Represents the type of Discount ('Coupon' or 'Discount').
    /// </summary>
    public string DiscountType { get; set; }

    /// <summary>
    /// Represents whether or not the Discount is a Coming Soon Discount.
    /// </summary>
    public bool ComingSoon { get; set; }

    /// <summary>   
    /// Represents whether or not the Discount is a New Discount.
    /// </summary>
    public bool IsNew { get; set; }

    /// <summary>
    /// Represents whether or not the Discount is an Expires Soon Discount.
    /// </summary>
    public bool ExpiresSoon { get; set; }

    /// <summary>
    /// Represents whether or not the Discount is a Favored Discount.
    /// </summary>
    public bool Favored { get; set; }

    /// <summary>
    /// Represents whether or not the Customer is subscribed to a Coming Soon Discount.
    /// </summary>
    public bool IsSubscribed { get; set; }
}