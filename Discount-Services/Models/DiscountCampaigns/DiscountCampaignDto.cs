namespace Discount_Services.Models.DiscountCampaigns;

public class DiscountCampaignDto
{
    public int Id { get; set; }

    public string Name { get; set; }
    public string Query { get; set; }

    public string BannerImageSrc { get; set; }

    public bool Active { get; set; }

    public bool Selected { get; set; }
    public bool Dev { get; set; }

    public int? DiscountQuantity { get; set; }
    public List<DiscountCampaignMetaDto>? DiscountCampaignMeta { get; set; }
    public List<DiscountAppDto>? Discounts { get; set; }
}