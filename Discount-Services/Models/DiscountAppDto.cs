using Discount_Services.Models.DiscountCampaigns;
using Shared.Models;

namespace Discount_Services.Models;

public class DiscountAppDto
{
    public long Id { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastModifiedDate { get; set; }
    public DateTime ActivationDate { get; set; }
    public int SortOrder { get; set; }
    public string Name { get; set; } = null!;
    public string? OverviewImageSrc { get; set; }
    public string? BannerImageSrc { get; set; }
    public string? Title { get; set; }
    public string? Subtitle { get; set; }
    public string? Description { get; set; }
    public DateOnly ExpireDate { get; set; }
    public int FkMerchantId { get; set; }
    public string? RedirectUrl { get; set; }
    public bool Active { get; set; }
    public bool Activatable { get; set; }
    public bool DeactivateWhenEmpty { get; set; }
    public bool Deleted { get; set; }

    public string DiscountType { get; set; } = null!;

    public bool ComingSoon { get; set; }
    public bool Dev { get; set; }
    public int? ParentId { get; set; }


    public List<DiscountAppDto>? Children { get; set; }
    public List<DiscountCodeAppDto>? DiscountCodes { get; set; }
    public List<DiscountCampaignDto>? DiscountCampaigns { get; set; }
    public FileDto? OverviewImage { get; set; }
    public FileDto? BannerImage { get; set; }
}