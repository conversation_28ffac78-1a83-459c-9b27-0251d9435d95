namespace Discount_Services.Models.DiscountsPartnerV1;

/// <summary>
/// Represents a campaign for discounts
/// </summary>
public class DiscountCampaignDto
{
    /// <summary>
    /// The URL for the banner image associated with the campaign
    /// </summary>
    public string ImageSrc { get; set; }

    /// <summary>
    /// The name used in the query to filter for this specific campaign
    /// </summary>
    public string QueryName { get; set; }

    /// <summary>
    /// The title associated with the campaign
    /// </summary>
    public string Title { get; set; }
} 