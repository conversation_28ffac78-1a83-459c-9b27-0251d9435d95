using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Discount_Services.Models.ModelsDal.Discount;

[Table("Discounts", Schema = "discount")]
public partial class Discount
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public int SortOrder { get; set; }

    [StringLength(100)]
    public string Name { get; set; } = null!;

    [StringLength(300)]
    public string OverviewImageSrc { get; set; } = null!;

    [StringLength(300)]
    public string BannerImageSrc { get; set; } = null!;

    [StringLength(100)]
    public string Title { get; set; } = null!;

    [StringLength(255)]
    public string Subtitle { get; set; } = null!;

    [StringLength(2000)]
    public string Description { get; set; } = null!;

    public DateOnly ExpireDate { get; set; }

    [StringLength(255)]
    public string RedirectUrl { get; set; } = null!;

    [StringLength(50)]
    public string DiscountType { get; set; } = null!;

    public bool DeactivateWhenEmpty { get; set; }

    public bool Deleted { get; set; }

    public bool ComingSoon { get; set; }

    public bool Dev { get; set; }

    [Precision(0)]
    public DateTime ActivationDate { get; set; }

    [Precision(0)]
    public DateTime? ReleaseDate { get; set; }

    [Column("FK_ParentId")]
    public int? FkParentId { get; set; }

    [Column("FK_MerchantId")]
    public int FkMerchantId { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [InverseProperty("FkDiscount")]
    public virtual ICollection<DiscountCampaignsRel> DiscountCampaignsRels { get; set; } = new List<DiscountCampaignsRel>();

    [InverseProperty("FkDiscount")]
    public virtual ICollection<DiscountCode> DiscountCodes { get; set; } = new List<DiscountCode>();

    [InverseProperty("FkDiscount")]
    public virtual ICollection<Favorite> Favorites { get; set; } = new List<Favorite>();

    [ForeignKey("FkParentId")]
    [InverseProperty("InverseFkParent")]
    public virtual Discount? FkParent { get; set; }

    [InverseProperty("FkParent")]
    public virtual ICollection<Discount> InverseFkParent { get; set; } = new List<Discount>();

    [InverseProperty("FkDiscount")]
    public virtual ICollection<Subscription> Subscriptions { get; set; } = new List<Subscription>();
}
