using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Discount_Services.Models.ModelsDal.Discount;

[Table("Campaigns", Schema = "discount")]
public partial class Campaign
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(200)]
    public string Name { get; set; } = null!;

    [StringLength(300)]
    public string BannerImageSrc { get; set; } = null!;

    public bool Selected { get; set; }

    public bool Dev { get; set; }

    [StringLength(300)]
    public string Query { get; set; } = null!;

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [InverseProperty("FkCampaign")]
    public virtual ICollection<CampaignMetum> CampaignMeta { get; set; } = new List<CampaignMetum>();

    [InverseProperty("FkCampaign")]
    public virtual ICollection<DiscountCampaignsRel> DiscountCampaignsRels { get; set; } = new List<DiscountCampaignsRel>();
}
