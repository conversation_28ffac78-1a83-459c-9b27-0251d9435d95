using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Discount_Services.Models.ModelsDal.Discount;

[Table("DiscountCodes", Schema = "discount")]
public partial class DiscountCode
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(200)]
    public string? Email { get; set; }

    [StringLength(100)]
    public string Code { get; set; } = null!;

    [Precision(0)]
    public DateTime? Locked { get; set; }

    [Column("FK_DiscountId")]
    public int FkDiscountId { get; set; }

    [ForeignKey("FkDiscountId")]
    [InverseProperty("DiscountCodes")]
    public virtual Discount FkDiscount { get; set; } = null!;
}
