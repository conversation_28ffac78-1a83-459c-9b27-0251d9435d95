using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Discount_Services.Models.ModelsDal.Discount;

[Table("Favorites", Schema = "discount")]
public partial class Favorite
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(255)]
    public string Email { get; set; } = null!;

    [Column("FK_DiscountId")]
    public int FkDiscountId { get; set; }

    [ForeignKey("FkDiscountId")]
    [InverseProperty("Favorites")]
    public virtual Discount FkDiscount { get; set; } = null!;
}
