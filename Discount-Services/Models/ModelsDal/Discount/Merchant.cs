using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Discount_Services.Models.ModelsDal.Discount;

[Table("Merchants", Schema = "discount")]
public partial class Merchant
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public DateOnly ActivationDate { get; set; }

    public DateOnly ExpirationDate { get; set; }

    [StringLength(2000)]
    public string Description { get; set; } = null!;

    [StringLength(50)]
    public string DiscountType { get; set; } = null!;

    [Column("FK_MerchantId")]
    public int FkMerchantId { get; set; }

    [InverseProperty("FkMerchant")]
    public virtual ICollection<MerchantDiscountCode> MerchantDiscountCodes { get; set; } = new List<MerchantDiscountCode>();
}
