using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Discount_Services.Models.ModelsDal.Discount;

[Table("CampaignMeta", Schema = "discount")]
public partial class CampaignMetum
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    [Column("FK_CampaignId")]
    public int FkCampaignId { get; set; }

    [StringLength(100)]
    public string MetaKey { get; set; } = null!;

    [StringLength(2000)]
    public string MetaValue { get; set; } = null!;

    [ForeignKey("FkCampaignId")]
    [InverseProperty("CampaignMeta")]
    public virtual Campaign FkCampaign { get; set; } = null!;
}
