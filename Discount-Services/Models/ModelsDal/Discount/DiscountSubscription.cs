namespace Discount_Services.Models.ModelsDal.Discount;

public partial class DiscountSubscription
{
    public int Id { get; set; }

    public int DebtorAccountId { get; set; }

    public string CustomerEmail { get; set; } = null!;

    public long FkDiscountId { get; set; }

    public bool Subscribed { get; set; }

    public bool Sent { get; set; }

    public virtual Discount FkDiscount { get; set; } = null!;
}