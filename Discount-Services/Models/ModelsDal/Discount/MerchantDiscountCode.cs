using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Discount_Services.Models.ModelsDal.Discount;

[Table("MerchantDiscountCodes", Schema = "discount")]
public partial class MerchantDiscountCode
{
    [Key]
    public int Id { get; set; }

    [StringLength(100)]
    public string Code { get; set; } = null!;

    [Column("FK_MerchantId")]
    public int FkMerchantId { get; set; }

    [ForeignKey("FkMerchantId")]
    [InverseProperty("MerchantDiscountCodes")]
    public virtual Merchant FkMerchant { get; set; } = null!;
}
