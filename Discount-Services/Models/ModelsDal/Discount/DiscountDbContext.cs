using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace Discount_Services.Models.ModelsDal.Discount;

public partial class DiscountDbContext : DbContext
{
    public DiscountDbContext(DbContextOptions<DiscountDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Campaign> Campaigns { get; set; }

    public virtual DbSet<CampaignMetum> CampaignMeta { get; set; }

    public virtual DbSet<Discount> Discounts { get; set; }

    public virtual DbSet<DiscountCampaignsRel> DiscountCampaignsRels { get; set; }

    public virtual DbSet<DiscountCode> DiscountCodes { get; set; }

    public virtual DbSet<Favorite> Favorites { get; set; }

    public virtual DbSet<Merchant> Merchants { get; set; }

    public virtual DbSet<MerchantDiscountCode> MerchantDiscountCodes { get; set; }

    public virtual DbSet<Subscription> Subscriptions { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Campaign>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Campaign__3214EC079DD15EE6");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<CampaignMetum>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Campaign__3214EC076B958A77");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkCampaign).WithMany(p => p.CampaignMeta)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__CampaignM__FK_Ca__02925FBF");
        });

        modelBuilder.Entity<Discount>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Discount__3214EC075F067B20");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkParent).WithMany(p => p.InverseFkParent).HasConstraintName("FK__Discounts__FK_Pa__5C6CB6D7");
        });

        modelBuilder.Entity<DiscountCampaignsRel>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Discount__3214EC0707932451");

            entity.HasOne(d => d.FkCampaign).WithMany(p => p.DiscountCampaignsRels)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__DiscountC__FK_Ca__7DCDAAA2");

            entity.HasOne(d => d.FkDiscount).WithMany(p => p.DiscountCampaignsRels)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__DiscountC__FK_Di__7CD98669");
        });

        modelBuilder.Entity<DiscountCode>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Discount__3214EC07A4586C03");

            entity.Property(e => e.Active).HasDefaultValue(true);

            entity.HasOne(d => d.FkDiscount).WithMany(p => p.DiscountCodes)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__DiscountC__FK_Di__753864A1");
        });

        modelBuilder.Entity<Favorite>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Favorite__3214EC07186703DC");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkDiscount).WithMany(p => p.Favorites)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Favorites__FK_Di__6319B466");
        });

        modelBuilder.Entity<Merchant>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Merchant__3214EC0770DD1BE3");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<MerchantDiscountCode>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Merchant__3214EC07EB401A9C");

            entity.HasOne(d => d.FkMerchant).WithMany(p => p.MerchantDiscountCodes)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantD__FK_Me__7167D3BD");
        });

        modelBuilder.Entity<Subscription>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Subscrip__3214EC0722294188");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkDiscount).WithMany(p => p.Subscriptions)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Subscript__FK_Di__68D28DBC");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
