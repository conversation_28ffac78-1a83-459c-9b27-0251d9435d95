using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Discount_Services.Models.ModelsDal.Discount;

[Table("DiscountCampaignsRel", Schema = "discount")]
public partial class DiscountCampaignsRel
{
    [Key]
    public int Id { get; set; }

    [Column("FK_DiscountId")]
    public int FkDiscountId { get; set; }

    [Column("FK_CampaignId")]
    public int FkCampaignId { get; set; }

    [ForeignKey("FkCampaignId")]
    [InverseProperty("DiscountCampaignsRels")]
    public virtual Campaign FkCampaign { get; set; } = null!;

    [ForeignKey("FkDiscountId")]
    [InverseProperty("DiscountCampaignsRels")]
    public virtual Discount FkDiscount { get; set; } = null!;
}
