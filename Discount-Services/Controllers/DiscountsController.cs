using Azure;
using Discount_Services.Models;
using Discount_Services.Models.DiscountCampaigns;
using Discount_Services.Models.DiscountFavorite;
using Discount_Services.Services.Discounts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Controllers;
using Shared.Controllers.ControllerExtensions.ActionFilters;
using Shared.Dto.Image;
using Shared.Services.Partner;
using ILogger = Serilog.ILogger;

namespace Discount_Services.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class DiscountsController(ILogger logger, IDiscountService discountsService, IPartnerContext partnerContext) : BasePartnerController
{
    /// <summary>
    /// This endpoint is used to retrieve a list of Discounts
    /// </summary>
    /// <param name="customerEmail"></param>
    /// <param name="size"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    [HttpGet]
    [Route("{customerEmail}/{size:int?}/{page:int?}")]
    [ProducesResponseType(typeof(DiscountPaginationDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [AllowAnonymous]
    public async Task<IActionResult> GetDiscountsByCustomerEmailAsync(string customerEmail, int size = 25, int page = 1,
        [FromQuery] string campaign = null)
    {
        try
        {
            if(partnerContext.IsFoundByDefault) return Unauthorized();
            
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetDiscountsByCustomerEmailAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information(
                    "GetDiscountsByCustomerEmailAsync customer: {CustomerEmail} requested page: {Page} size: {Size}",
                    customerEmail, page, size);
                var success = await discountsService
                    .GetDiscountByEmail(customerEmail, page, size, campaign, IsDevRequest, PartnerId)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving Discounts for customer: {CustomerEmail}", customerEmail);
            return BadRequest();
        }
    }

    /// <summary>
    /// This endpoint is used to retrieve a Discount incl. details by its Id
    /// </summary>
    /// <param name="discountId"></param>
    /// <param name="customerEmail"></param>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    [Route("{DiscountId:int}/{customerEmail}")]
    [ProducesResponseType(typeof(DiscountDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    public async Task<IActionResult> GetDiscountByIdAsync(int discountId, string customerEmail)
    {
        try
        {
            if(partnerContext.IsFoundByDefault) return Unauthorized();
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetDiscountByIdAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information(
                    "GetDiscountByIdAsync customer: {CustomerEmail} requested discount: {DiscountId}", customerEmail,
                    discountId);
                var success = await discountsService.GetDiscountById(customerEmail, discountId, IsDevRequest)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving ViaAds - Discount With Id: {DiscountId}, for customer: {CustomerEmail} ",
                discountId,
                customerEmail);
            return BadRequest();
        }
    }

    /// <summary>
    /// This endpoint is used for pushing Discounts Events, to track customer behavior
    /// </summary>
    /// <param name="discountEvent"></param>
    /// <returns></returns>
    [HttpPost]
    [AllowAnonymous]
    [ProducesResponseType(typeof(Response), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [Route("event")]
    public async Task<IActionResult> AddDiscountsEventsAsync(DiscountEventDto discountEvent)
    {
        try
        {
            if(partnerContext.IsFoundByDefault) return Unauthorized();
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddDiscountsEventsAsync"))
            {
                var response = await discountsService.AddDiscountEventAsync(discountEvent, PartnerId).ConfigureAwait(false);
                if (response.Success)
                    return Ok(response);
                return BadRequest(response);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error adding Discount Event: {DiscountEvent}, for DiscountId: {DiscountId}, for customer: {CustomerEmail}",
                discountEvent.EventType, discountEvent.DiscountId, discountEvent.CustomerEmail);
            return BadRequest($"Error trying to consume event with type: {discountEvent.EventType}");
        }
    }


    /// <summary>
    /// This endpoint is used for pushing Discounts Events, to track customer behavior
    /// </summary>
    /// <param name="partnerId"></param>
    /// <param name="discountEvent"></param>
    /// <returns></returns>
    [HttpPost]
    [AllowAnonymous]
    [Route("event/internal")]
    public async Task<IActionResult> AddDiscountsEventsInternalAsync([FromHeader] int partnerId, DiscountEventDto discountEvent)
    {
        try
        {
            if(partnerContext.IsFoundByDefault) return Unauthorized();
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddDiscountsEventsInternalAsync"))
            {
                var response = await discountsService.AddDiscountEventAsync(discountEvent, partnerId).ConfigureAwait(false);
                if (response.Success)
                    return Ok(response);
                return BadRequest(response);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error adding Discount Event: {DiscountEvent}, for DiscountId: {DiscountId}, for customer: {CustomerEmail}",
                discountEvent.EventType, discountEvent.DiscountId, discountEvent.CustomerEmail);
            return BadRequest($"Error trying to consume event with type: {discountEvent.EventType}");
        }
    }

    /// <summary>
    /// This endpoint is used to retrieve a list of Favorited Discounts
    /// </summary>
    /// <param name="customerEmail"></param>
    /// <param name="size"></param>
    /// <param name="page"></param>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    [Route("{customerEmail}/favorites/{size:int?}/{page:int?}")]
    [ProducesResponseType(typeof(DiscountPaginationDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [ProducesResponseType(typeof(string), 404)]
    public async Task<IActionResult> GetFavoredDiscountsByCustomerEmailAsync(string customerEmail, int size = 25,
        int page = 1)
    {
        try
        {
            if(partnerContext.IsFoundByDefault) return Unauthorized();
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetFavoredDiscountsByCustomerEmailAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information(
                    "GetFavoredDiscountsByCustomerEmailAsync customer: {CustomerEmail} requested page: {Page} size: {Size}",
                    customerEmail,
                    page, size);
                var success = await discountsService
                    .GetFavoredDiscountsByEmail(customerEmail, page, size, IsDevRequest)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving favored discounts for customer: {CustomerEmail}", customerEmail);
            return BadRequest();
        }
    }


    /// <summary>
    /// This endpoint is used for toggling the favorite status of a discount.
    /// </summary>
    /// <param name="favoriteRequest"></param>
    /// <returns></returns>
    [HttpPut]
    [AllowAnonymous]
    [ProducesResponseType(typeof(Response), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [Route("toggle-favorite")]
    public async Task<IActionResult> ToggleFavoriteAsync(DiscountFavoriteRequestDto favoriteRequest)
    {
        try
        {
            if(partnerContext.IsFoundByDefault) return Unauthorized();
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ToggleFavoriteAsync"))
            {
                var response = await discountsService.ToggleFavoriteAsync(favoriteRequest).ConfigureAwait(false);
                if (response.Success)
                    return Ok(response);
                return BadRequest(response);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error toggling favorite for DiscountId: {DiscountId}, for customer: {CustomerEmail}",
                favoriteRequest.DiscountId, favoriteRequest.Email);
            return BadRequest($"Error trying to toggle favorite for discount: {favoriteRequest.DiscountId}");
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("campaign")]
    public async Task<IActionResult> GetDiscountCampaignsPartnerAsync()
    {
        try
        {
            if(partnerContext.IsFoundByDefault) return Unauthorized();
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetDiscountCampaignsPartnerAsync"))
            {
                var discountCampaign = await discountsService.GetDiscountCampaignsPartner(PartnerId, IsDevRequest)
                    .ConfigureAwait(false);
                if (discountCampaign != null)
                {
                    return Ok(discountCampaign);
                }

                return NotFound();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while retrieving GetDiscountCampaignsPartnerAsync");
            return BadRequest();
        }
    }


    /* Internal Endpoints */
    
    // Get Expiring Soon Discount Count 
    [HttpGet]
    [Route("expiring-soon/count")]
    public async Task<IActionResult> GetExpiringSoonDiscountCountAsync()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetExpiringSoonDiscountCountAsync"))
            {
                var success = await discountsService.GetExpiringSoonDiscountCountAsync()
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving GetExpiringSoonDiscountCountAsync");
            return BadRequest();
        }
    }
    
    [HttpGet]
    public async Task<IActionResult> GetDiscountsAsync()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetDiscountsAsync"))
            {
                var success = await discountsService.GetDiscounts()
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving Discounts");
            return BadRequest();
        }
    }

    [HttpGet]
    [Route("deleted/{deleted}")]
    public async Task<IActionResult> GetDiscountsAsync(bool deleted)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetDiscountsAsync"))
            {
                var success = await discountsService.GetDiscounts(deleted)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving Discounts");
            return BadRequest();
        }
    }

    /// <summary>
    /// Get paginated discounts with filtering and sorting options
    /// </summary>
    /// <param name="partnerId">Partner ID from header</param>
    /// <param name="page">Page number</param>
    /// <param name="size">Page size</param>
    /// <param name="deleted">Include deleted discounts</param>
    /// <param name="dev">Development mode</param>
    /// <param name="sortName">Sort field: id, name, title, subtitle, expireDate, discountType</param>
    /// <param name="sortOrder">Sort order: asc, desc</param>
    /// <param name="searchString">Search string for title, name, subtitle</param>
    /// <param name="searchFilter">Filter options: active, inactive, expiring_soon</param>
    /// <returns>Paginated discount list</returns>
    [HttpGet]
    [Route("pagination/{page}/{size}/{deleted}/{dev}/{sortName}/{sortOrder}")]
    public async Task<IActionResult> GetDiscountsAsync(int page, int size, bool deleted, bool dev, string sortName,
        string sortOrder, [FromQuery] string searchString = "", [FromQuery] string searchFilter = "")
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetDiscountsAsync"))
            {
                var success = await discountsService.GetPaginationDiscounts(page, size, searchFilter, searchString,
                        deleted, dev, sortName, sortOrder)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving Discounts");
            return BadRequest();
        }
    }

    [HttpPost]
    [Route("AddDiscount")]
    public async Task<IActionResult> AddDiscountAsync([FromHeader] int partnerId, DiscountAppDto discountAppDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddDiscountAsync"))
            {
                var success = await discountsService.CreateDiscount(partnerId, discountAppDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error create Discount");
            return BadRequest($"Error trying to create a new Discount with Title: {discountAppDto.Title}");
        }
    }

    [HttpPost]
    [Route("AddChildDiscount")]
    public async Task<IActionResult> CreateChildDiscountAsync(DiscountAppDto discountAppDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CreateChildDiscountAsync"))
            {
                var success = await discountsService.CreateChildDiscount(discountAppDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error creating Child Discount");
            return BadRequest(
                $"Error trying to create a new Child Discount with Title: {discountAppDto.Title}, from Parent Discount with Id: {discountAppDto.Id}");
        }
    }

    [HttpPut]
    [Route("UpdateDiscount")]
    public async Task<IActionResult> UpdateDiscountsEventsAsync(DiscountAppDto discountAppDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateDiscountsEventsAsync"))
            {
                var success = await discountsService.UpdateDiscount(discountAppDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error update Discount");
            return BadRequest($"Error trying to consume event with type: {discountAppDto.Title}");
        }
    }

    [HttpPost]
    [Route("Duplicate")]
    public async Task<IActionResult> DuplicateDiscountAsync([FromHeader] int partnerId, DiscountAppDto discountAppDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "DuplicateDiscountAsync"))
            {
                var success = await discountsService.DuplicateDiscount(partnerId, discountAppDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error update DiscountCampaign");
            return BadRequest($"Error trying to duplicate Discount with id: {discountAppDto.Id}");
        }
    }

    [HttpDelete]
    [Route("{DiscountId:int}")]
    public async Task<IActionResult> UpdateDiscountsEventsAsync(int discountId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateDiscountsEventsAsync"))
            {
                var success = await discountsService.DeleteDiscount(discountId).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error delete Discount");
            return BadRequest($"Error trying to delete discount with id: {discountId}");
        }
    }

    [HttpDelete]
    [Route("codes/{DiscountId:long}/{createdDate:datetime}")]
    public async Task<IActionResult> DeleteDiscountCodesByCreatedDateAsync(long discountId, DateTime createdDate)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "DeleteDiscountCodesByCreatedDateAsync"))
            {
                var success = await discountsService.DeleteDiscountCodesByCreatedDate(discountId, createdDate)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error delete Discount");
            return BadRequest(
                $"Error trying to delete discount codes with id: {discountId} and created date {createdDate}");
        }
    }

    //Discount campaigns
    [HttpGet]
    [Route("Campaigns")]
    public async Task<IActionResult> GetDiscountCampaignsAsync()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetDiscountCampaignsAsync"))
            {
                var success = await discountsService.GetDiscountCampaigns()
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while retrieving GetDiscountCampaignsAsync");
            return BadRequest();
        }
    }

    //Discount campaigns
    [HttpGet]
    [Route("Campaigns/Simple")]
    public async Task<IActionResult> GetDiscountCampaignsSimple()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetDiscountCampaignsSimple"))
            {
                var success = await discountsService.GetDiscountCampaignsSimple()
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while retrieving GetDiscountCampaignsSimple");
            return BadRequest();
        }
    }

    [HttpPost]
    [Route("AddCampaign")]
    public async Task<IActionResult> AddDiscountCampaignAsync(DiscountCampaignDto discountCampaignDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddDiscountCampaignAsync"))
            {
                var success = await discountsService.AddDiscountCampaign(discountCampaignDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error create Discount");
            return BadRequest($"Error trying to create Discount Campaign with name: {discountCampaignDto.Name} from partnerId: {partnerContext.PartnerId}");
        }
    }

    [HttpPut]
    [Route("UpdateCampaign")]
    public async Task<IActionResult> UpdateDiscountCampaignAsync(DiscountCampaignDto discountCampaignDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateDiscountCampaignAsync"))
            {
                var success = await discountsService.UpdateDiscountCampaign(discountCampaignDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error update DiscountCampaign");
            return BadRequest($"Error trying to update Discount Campaign with name: {discountCampaignDto.Name}");
        }
    }

    [HttpDelete]
    [Route("campaign/{DiscountCampaignId:int}")]
    public async Task<IActionResult> DeleteDiscountCampaignAsync(int discountCampaignId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "DeleteDiscountCampaignAsync"))
            {
                var success = await discountsService.DeleteDiscountCampaign(discountCampaignId).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error delete DiscountCampaign");
            return BadRequest($"Error trying to delete Discount Campaign with id: {discountCampaignId}");
        }
    }

    // TODO - Check if this is still needed
    [HttpPost]
    [Route("GenerateDiscountImage")]
    public async Task<IActionResult> GenerateDiscountImageAsync(ImageCreateDiscountDto imageCreateDiscountDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ImageCreateDiscountDto"))
            {
                var success = await discountsService.GenerateDiscountImageAsync(imageCreateDiscountDto)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error create Discount");
            return BadRequest($"Error trying to generating discount image");
        }
    }

    /***************** Discount Partner Section ******************/
    // TODO - Check if this is still needed - it is probably not
    [HttpGet]
    [Route("partners/active/{active:bool}")]
    public async Task<IActionResult> GetDiscountPartnersAsync(bool active)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetDiscountPartnersAsync"))
            {
                var success = await discountsService.GetDiscountPartners(active)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving Discounts");
            return BadRequest();
        }
    }
    /***************** Discount Partner Section END ******************/
}