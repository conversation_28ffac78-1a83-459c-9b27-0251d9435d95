using Discount_Services.Models;
using Discount_Services.Models.DiscountsPartnerV1;
using Discount_Services.Models.QueryParameters;
using Discount_Services.Services.DiscountsPartnerV1;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using SerilogTimings.Extensions;
using Shared.Controllers;

namespace Discount_Services.Controllers.v1;

[Authorize(Roles = "valyrion")]
[Route("v1/discounts")]
[ApiController]
public class PartnerDiscountsV1Controller(ILogger logger, IDiscountPartnerV1Service discountsService) : BasePartnerController
{
    /// <summary>
    /// This endpoint is used to retrieve a list of Discounts
    /// </summary>
    /// <param name="queryParameters"></param>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    [ProducesResponseType(typeof(DiscountPartnerPaginationDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    public async Task<IActionResult> GetDiscountsByCustomerEmailAsync([FromQuery] DiscountPaginationQueryParameters queryParameters)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetDiscountsByCustomerEmailAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information(
                    "GetDiscountsByCustomerEmailAsync customer: {CustomerEmail} requested page: {Page} size: {Size}",
                    queryParameters.Email, queryParameters.Page, queryParameters.Size);
                var success = await discountsService
                    .GetDiscountByEmail(queryParameters.Email, queryParameters.Page, queryParameters.Size, queryParameters.Campaign, queryParameters.UseMock)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving Discounts for customer: {CustomerEmail}, from partner: {PartnerId}", queryParameters.Email, PartnerId);
            return BadRequest();
        }
    }
    
    /// <summary>
    /// This endpoint is used to retrieve a Discount incl. details by its Id
    /// </summary>
    /// <param name="discountId"></param>
    /// <param name="email"></param>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    [Route("{DiscountId:int}")]
    [ProducesResponseType(typeof(DiscountDto), 200)]
    [ProducesResponseType(typeof(string), 404)]
    public async Task<IActionResult> GetDiscountByIdAsync(int discountId, [FromQuery] string email, [FromQuery] bool useMock = false)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetDiscountByIdAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information(
                    "GetDiscountByIdAsync customer: {CustomerEmail} requested discount: {DiscountId}", email,
                    discountId);
                var success = await discountsService.GetDiscountById(email, discountId, useMock)
                    .ConfigureAwait(false);
                if(success == null)
                    return NotFound(new DiscountPartnerResponse { Success = false, Message = "Discount not found" });
                
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving ViaAds - Discount With Id: {DiscountId}, for customer: {CustomerEmail} ",
                discountId,
                email);
            return BadRequest();
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("campaign")]
    public async Task<IActionResult> GetDiscountCampaignsPartnerAsync([FromQuery] string lang, [FromQuery] bool useMock = false)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetDiscountCampaignsPartnerAsync"))
            {
                var discountCampaign = await discountsService.GetActiveDiscountCampaign(lang, useMock)
                    .ConfigureAwait(false);
                if (discountCampaign == null)
                    return NotFound(new DiscountPartnerResponse { Success = false, Message = "No Active Discount Campaign" });
                
                return Ok(discountCampaign);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while retrieving GetDiscountCampaignsPartnerAsync");
            return BadRequest();
        }
    }

    /// <summary>
    /// This endpoint is used to retrieve a list of Favorised Discounts
    /// </summary>
    /// <param name="queryParameters"></param>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    [Route("favorites")]
    [ProducesResponseType(typeof(DiscountPartnerPaginationDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [ProducesResponseType(typeof(string), 404)]
    public async Task<IActionResult> GetFavoredDiscountsByCustomerEmailAsync([FromQuery] DiscountPaginationQueryParameters queryParameters)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetFavoredDiscountsByCustomerEmailAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information(
                    "GetFavoredDiscountsByCustomerEmailAsync customer: {CustomerEmail} requested page: {Page} size: {Size}",
                    queryParameters.Email, queryParameters.Page, queryParameters.Size);
                var success = await discountsService
                    .GetFavoredDiscountsByEmail(queryParameters.Email, queryParameters.Page, queryParameters.Size, queryParameters.UseMock)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving favored discounts for customer: {CustomerEmail}", queryParameters.Email);
            return BadRequest();
        }
    }
    
    /// <summary>
    /// This endpoint is used for toggling the favorite status of a discount.
    /// </summary>
    /// <param name="favoriteRequest"></param>
    /// <returns></returns>
    [HttpPut]
    [AllowAnonymous]
    [ProducesResponseType(typeof(DiscountPartnerResponse), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [Route("toggle-favorite")]
    public async Task<IActionResult> ToggleFavoriteAsync(DiscountPartnerFavoriteRequestDto favoriteRequest)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ToggleFavoriteAsync"))
            {
                var response = await discountsService.ToggleFavoriteAsync(favoriteRequest).ConfigureAwait(false);
                if (response.Success)
                    return Ok(response);
                return BadRequest(response);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error toggling favorite for DiscountId: {DiscountId}, for customer: {CustomerEmail}",
                favoriteRequest.DiscountId, favoriteRequest.CustomerEmail);
            return BadRequest($"Error trying to toggle favorite for discount: {favoriteRequest.DiscountId}");
        }
    }

    /// <summary>
    /// This endpoint is used for pushing Discounts Events, to track customer behavior
    /// </summary>
    /// <param name="discountEvent"></param>
    /// <returns></returns>
    [HttpPost]
    [AllowAnonymous]
    [ProducesResponseType(typeof(DiscountPartnerEventResponse), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [Route("event")]
    public async Task<IActionResult> AddDiscountsEventsAsync(DiscountPartnerEventDto discountEvent)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddDiscountsEventsAsync"))
            {
                var response = await discountsService.AddDiscountEventAsync(discountEvent).ConfigureAwait(false);
                if (response.Success)
                    return Ok(response);
                return BadRequest(response);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error adding Discount Event: {DiscountEvent}, for DiscountId: {DiscountId}, for customer: {CustomerEmail}",
                discountEvent.EventType, discountEvent.DiscountIds, discountEvent.CustomerEmail);
            return BadRequest($"Error trying to consume event with type: {discountEvent.EventType}");
        }
    }
}