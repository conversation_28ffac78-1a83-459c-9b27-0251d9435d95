using Discount_Services.Models.ModelsDal.Discount;
using Discount_Services.Models.ModelsDal.Discount;
using EFCore.BulkExtensions;
using Microsoft.EntityFrameworkCore;
using Shared.Dto.Discount;
using Shared.Services.Notification;
using ILogger = Serilog.ILogger;

namespace Discount_Services.Services.DiscountPartners;

public class DiscountPartnerService : IDiscountPartnerService
{
    private readonly DiscountDbContextTracking _discountDbContext;
    private readonly IEmailService _emailService;

    public DiscountPartnerService(DiscountDbContextTracking discountDbContext, ILogger logger,
        IEmailService emailService)
    {
        _discountDbContext = discountDbContext;
        _emailService = emailService;
    }

    public async Task<List<Merchant>> GetDiscountPartnersByMerchantId(int webshopId)
    {
        return await _discountDbContext.Merchants
            .Include(a => a.MerchantDiscountCodes)
            .Where(a => a.FkMerchantId == webshopId && a.Active).ToListAsync();
    }

    public async Task<Merchant> CrudDiscounts(DiscountPartnerDto discountPartnerDto, string email)
    {
        var discountPartner = new Merchant()
        {
            Id = discountPartnerDto.Id,
            Active = discountPartnerDto.Active,
            Description = discountPartnerDto.Description,
            DiscountType = discountPartnerDto.DiscountType,
            ActivationDate = DateOnly.FromDateTime(discountPartnerDto.ActivationDate),
            FkMerchantId = discountPartnerDto.FkMerchantId,
            ExpirationDate = DateOnly.FromDateTime(discountPartnerDto.ExpirationDate),
            CreatedDate = DateTime.UtcNow,
            LastModifiedDate = DateTime.UtcNow,
            MerchantDiscountCodes = new List<MerchantDiscountCode>()
        };

        if (discountPartnerDto.Id == 0)
        {
            //Create
            await _discountDbContext.Merchants.AddAsync(discountPartner);
            await _discountDbContext.SaveChangesAsync();

            var discountPartnerCodes = new List<MerchantDiscountCode>();
            foreach (var discountPartnerCodeDto in discountPartnerDto.DiscountPartnerCodes)
            {
                discountPartnerCodes.Add(new MerchantDiscountCode
                {
                    Code = discountPartnerCodeDto.Code,
                    FkMerchantId = discountPartner.Id
                });
            }

            await _discountDbContext.BulkInsertAsync(discountPartnerCodes);
            await _discountDbContext.SaveChangesAsync();


            //MailBody
            var mailBody =
                $"User {email} created a new discount in partner app for webshop {discountPartner.FkMerchantId}<br>";
            await _emailService.SendEmailAsync(new List<string> {"<EMAIL>"},
                $"User {email} create a new discount in partner app for webshop {discountPartner.FkMerchantId}",
                mailBody);
        }
        else
        {
            var originalDiscount = await _discountDbContext.Merchants.AsNoTracking()
                .SingleAsync(a => a.Id == discountPartner.Id);

            //Update
            discountPartner.MerchantDiscountCodes = null;
            _discountDbContext.Merchants.Update(discountPartner);
            await _discountDbContext.SaveChangesAsync();

            //MailBody
            var mailBody =
                $"User {email} updated discount in partner app for webshop {discountPartner.FkMerchantId}<br>" +
                $"DiscountPartnerId: {discountPartner.Id}<br>" +
                $"Activation Date: {discountPartner.ActivationDate:dd-MM-yyyy} | Activation Date OLD: {originalDiscount.ActivationDate:dd-MM-yyyy}<br>" +
                $"Expiration Date: {discountPartner.ExpirationDate:dd-MM-yyyy} | Expiration Date OLD: {originalDiscount.ExpirationDate:dd-MM-yyyy}<br>" +
                $"Description: {discountPartner.Description} | Description OLD: {originalDiscount.Description}<br>";

            if (discountPartner.Active == false)
            {
                mailBody += "<br><strong>Discount have been deleted</strong>";
            }

            await _emailService.SendEmailAsync(new List<string> {"<EMAIL>"},
                $"User {email} updated discount in partner app for webshop {discountPartner.FkMerchantId}", mailBody);
        }

        return discountPartner;
    }

    public async Task<Merchant> GetDiscountPartners()
    {
        throw new NotImplementedException();
    }
}