using Discount_Services.Models.ModelsDal.Discount;
using Shared.Dto.Discount;

namespace Discount_Services.Services.DiscountPartners
{
    public interface IDiscountPartnerService
    {
        Task<List<Merchant>> GetDiscountPartnersByMerchantId(int merchantId);
        Task<Merchant> CrudDiscounts(DiscountPartnerDto discountPartnerDto, string email);
        Task<Merchant> GetDiscountPartners();
    }
}