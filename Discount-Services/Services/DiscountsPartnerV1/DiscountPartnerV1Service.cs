using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Audience.Services.Audience;
using Discount_Services.Models.ModelsDal.Discount;
using Discount_Services.Models;
using Discount_Services.Models.DiscountsPartnerV1;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using RabbitMQ.Client;
using Shared.Models;
using Shared.Models.Elastic.ElasticCustomerDiscounts;
using Shared.Services.Cache;
using Shared.Services.MerchantRelevance;
using Shared.Services.Partner;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;
using JsonSerializer = System.Text.Json.JsonSerializer;
using GrowthBook;
using Discount_Services.Models.Exposure;
using Shared.Elastic.Models.ElasticExposure;
using Partner_Services.Services.General;
using Merchant_Services.Models.ModelsDal.Merchant;
using ShopifySharp;
using ICustomerService = Audience.Services.Audience.ICustomerService;
using IPartnerService = Partner_Services.Services.General.IPartnerService;
using Microsoft.AspNetCore.Hosting;
using System.Text.Json;
using System.Reflection;

namespace Discount_Services.Services.DiscountsPartnerV1
{
    public class DiscountPartnerV1Service(
        DiscountDbContextTracking discountDbContext,
        ILogger logger,
        //IConnection rabbitConnection,
        [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud,
        IConfiguration configuration,
        VaultSettings vaultSettings,
        IMerchantService merchantService,
        ICustomerService customerService,
        ICacheService cacheService,
        IMerchantRelevanceService merchantRelevanceService,
        IMemoryCache memoryCache,
        IGrowthBook growthBook,
        IPartnerService partnerService,
        IPartnerContext partnerContext,
        IWebHostEnvironment environment,
        Repositories.IDiscountRepository discountRepository
        )
        : IDiscountPartnerV1Service
    {
        private const string AnonymousEmail = "<EMAIL>";
        private const int AgeInterval = 10;

        public async Task<DiscountPartnerPaginationDto> GetDiscountByEmail(string email, int page, int size, string campaignQueryName = null, bool useMock = false)
        {
            // TODO - Implement Non Mock Discounts and then filter
            var lastPage = 1;
            var discounts = new List<DiscountPartnerDto>();
            try
            {
                var now = DateTime.UtcNow;
                
                // Use the repository to get paginated discounts
                discounts = await discountRepository.GetDiscountsByPageAsync(email, page, size, campaignQueryName);
                
                // Get total count to calculate last page
                var allDiscounts = await discountRepository.GetAllDiscountsAsync(partnerContext.PartnerId.ToString());
                var totalCount = string.IsNullOrEmpty(campaignQueryName) 
                    ? allDiscounts.Count 
                    : allDiscounts.Count(d => d.Id % 2 == 0); // Mock campaign filtering (same as repository)
                    
                lastPage = (int)(Math.Ceiling((decimal)totalCount / size));
                
                // Create and send the event for analytics tracking
                var discountEvent = new DiscountPartnerEventDto
                {
                    CustomerEmail = email,
                    EventDate = now,
                    EventType = "discounts_list_viewed",
                    PageNumber = page,
                    PageSize = size 
                };
                
                var webShopIds = discounts.Select(d => d.MerchantId.ToString()).ToList();
                var discountIds = discounts.Select(d => d.Id.ToString()).ToList();
                if (useMock)
                {
                    discountEvent.EventType = "discounts_list_viewed_mock";
                }
                await AddDiscountEventAsync(discountEvent, webShopIds, discountIds);
            }
            catch (Exception ex)
            {
                logger.ForContext("service_name", GetType().Name).Error(
                    ex,
                    "Error in returning Discount Pagination for Page: {Page} with Size: {Size} for Customer: {CustomerEmail}",
                    page, size, email);
            }
            
            return new DiscountPartnerPaginationDto
            {
                LastPage = lastPage,
                Discounts = discounts
            };
        }

        public async Task<DiscountDto?> GetDiscountById(string email, int discountId, bool useMock)
        {
            try
            {
                // Use the repository to get the discount when using mock
                if (useMock)
                {
                    return await discountRepository.GetDiscountByIdAsync(email, discountId);
                }
                
                // Get the discount from the database 
                var datetime = DateTime.UtcNow.AddDays(-7);
                var existingDiscount =
                    await discountDbContext.Discounts.AsNoTracking()
                        .FirstOrDefaultAsync(a => a.Id == discountId);
                if (existingDiscount == null)
                {
                    return null;
                }

                var isSubscribed = false;
                if (existingDiscount.ComingSoon)
                {
                    isSubscribed = await discountDbContext.Subscriptions
                        .AnyAsync(a => a.FkDiscountId == existingDiscount.FkParentId && a.Email == email);
                }

                string? discountCode = null;
                string? discountCodeId = null;
                if (existingDiscount.DiscountType == "Coupon")
                {
                    if (email != AnonymousEmail)
                    {
                        var discountCodeObj = await discountDbContext.DiscountCodes.FirstOrDefaultAsync(a =>
                            a.FkDiscountId == existingDiscount.FkParentId && a.Email == email &&
                            (a.Locked > datetime || a.Locked == null) && a.Active);
                        if (discountCodeObj == null)
                        {
                            var existingParentDiscountCode =
                                await discountDbContext.DiscountCodes.FirstOrDefaultAsync(a =>
                                    a.Email == null && a.FkDiscountId == existingDiscount.FkParentId && a.Active);
                            if (existingParentDiscountCode != null)
                            {
                                existingParentDiscountCode.Email = email;
                                discountCode = existingParentDiscountCode.Code;
                                discountCodeId = existingParentDiscountCode.Id.ToString();
                                await discountDbContext.SaveChangesAsync().ConfigureAwait(false);
                            }
                        }
                        else
                        {
                            discountCode = discountCodeObj.Code;
                            discountCodeId = discountCodeObj.Id.ToString();
                        }
                    }
                    else
                    {
                        discountCode = "hidden";
                    }
                }

                var merchant = await merchantService.GetByIdAsync(existingDiscount.FkMerchantId);
                var merchantId = existingDiscount.FkMerchantId.ToString();
                var webshopName = string.Empty;
                if (merchant != null)
                {
                    merchantId = merchant.Id.ToString();
                    webshopName = merchant.Name;
                }

                var contact = await customerService.GetByEmailAsync(email);
                var partnerGuid = string.Empty;
                if (contact != null && contact.PartnerGuid != null)
                    partnerGuid = contact.PartnerGuid;

                var redirectUrl = configuration["RedirectService-Url"] + "redirectDiscount/" + JwtTokenEncode(email,
                    partnerGuid!,
                    existingDiscount.RedirectUrl!, existingDiscount.Id.ToString(), merchantId,
                    existingDiscount.DiscountType, webshopName, discountCode, discountCodeId);

                if (existingDiscount.DiscountType == "Coupon" && string.IsNullOrEmpty(discountCode) &&
                    !existingDiscount.ComingSoon)
                {
                    return null;
                }

                var isFavored = await discountDbContext.Favorites
                    .AnyAsync(f => f.FkDiscountId == existingDiscount.FkParentId && f.Email == email && f.Active);

                var discount = new DiscountDto
                {
                    Id = existingDiscount.Id,
                    Title = existingDiscount.Title,
                    Subtitle = existingDiscount.Subtitle,
                    Description = existingDiscount.Description,
                    ImageSrc = existingDiscount.BannerImageSrc,
                    ExpireDate = existingDiscount.ExpireDate,
                    RedirectLink = redirectUrl,
                    DiscountCode = discountCode,
                    ComingSoon = existingDiscount.ComingSoon,
                    DiscountType = existingDiscount.DiscountType,
                    IsSubscribed = isSubscribed,
                    IsNew = false,
                    ExpiresSoon = false,
                    /*IsNew = existingDiscount.ReleaseDate != null &&
                                now.Subtract((DateTime)existingDiscount.ReleaseDate).TotalDays <= 7,
                        ExpiresSoon = existingDiscount.ExpireDate.Subtract(now).TotalDays <= 7,*/
                    Favored = isFavored
                };

                return discount;
            }
            catch (Exception ex)
            {
                logger.ForContext("service_name", GetType().Name).Error(
                    ex,
                    "Error in returning Discount with Id:{DiscountId} for Customer:{CustomerEmail}",
                    discountId, email);

                return null;
            }
        }

        private string JwtTokenEncode(string email, string partnerGuid, string url, string discountId,
            string merchantId = "", string type = "", string webshopName = "", string? discountCode = "",
            string? discountCodeId = "")
        {
            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(vaultSettings.JwtTokenKey));

            var tokenHandler = new JwtSecurityTokenHandler();

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity([
                    new Claim("email", email),
                    new Claim("url", url),
                    new Claim("did", discountId),
                    new Claim("wid", merchantId),
                    new Claim("wname", webshopName),
                    new Claim("type", type),
                    new Claim("dcode", discountCode ?? ""),
                    new Claim("dcodeid", discountCodeId ?? ""),
                    //new Claim("vbdai", debtorAccountId)
                    new Claim("pguid", partnerGuid),
                    new Claim("parid", partnerContext.PartnerId.ToString())
                ]),
                Issuer = vaultSettings.JwtIssuer,
                SigningCredentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha512Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);

            return tokenHandler.WriteToken(token);
        }

        public async Task<ResponseDto> AddDiscountEventAsync(DiscountPartnerEventDto discountEventDto,
            List<string>? merchantIds = null,
            List<string>? discountIds = null)
        {
            try
            {
                discountEventDto.EventType = discountEventDto.EventType switch
                {
                    "coupon_details_viewed" => "discount_details_viewed",
                    "coupon_link_opened" => "discount_link_opened",
                    "coupon_code_copied" => "discount_code_copied",
                    _ => discountEventDto.EventType
                };

                if (discountEventDto.EventType != "discount_overview_viewed" &&
                    discountEventDto.EventType != "discounts_list_viewed" &&
                    discountEventDto.EventType != "favored_discount_overview_viewed" &&
                    discountEventDto.EventType != "discount_details_viewed" &&
                    discountEventDto.EventType != "discount_link_opened" &&
                    discountEventDto.EventType != "discount_code_copied" &&
                    discountEventDto.EventType != "discount_favorised" &&
                    discountEventDto.EventType != "discount_unfavorised" &&
                    discountEventDto.EventType != "discounts_list_viewed_mock")
                {
                    logger.ForContext("service_name", GetType().Name).Warning(
                        "Received and unknown discount event: {event} from Customer Email: {Email}",
                        discountEventDto.EventType, discountEventDto.CustomerEmail);

                    return new ResponseDto
                    {
                        Success = false,
                        Message =
                            $"The EventType: {discountEventDto.EventType} is unknown - known EventTypes are: 'discounts_list_viewed', " +
                            $"'discount_details_viewed', 'discount_link_opened', 'discount_code_copied', " +
                            $"'discount_favorised', 'discount_unfavorised'"
                    };
                }

                const string partnerGuid = "n/a";
                //Partner guid is not used anywhere (2024-04-29)
                //var contact = await _customerService.GetByEmailAsync(discountEventDto.CustomerEmail);
                //if (contact != null)
                //    partnerGuid = contact.PartnerGuid;
                const string merchantId = "n/a";
                const string discountId = "n/a";
                const string merchantName = "n/a";

                List<string> merchantNames = [];

                if (discountEventDto.DiscountIds != null)
                {
                    var discountIdsBatch = discountEventDto.DiscountIds.ToList();
                    var discounts = discountDbContext.Discounts
                        .Where(d => discountIdsBatch.Contains(d.Id))
                        .Select(d => new {d.Id, d.FkMerchantId})
                        .ToList();

                    merchantIds ??= [];
                    discountIds ??= [];

                    foreach (var discount in discounts)
                    {
                        merchantIds.Add(discount.FkMerchantId.ToString());
                        discountIds.Add(discount.Id.ToString());
                    }

                    merchantNames =
                        await merchantService.GetNamesByIdsAsync(discounts.Select(a => a.FkMerchantId).ToList()) ?? [];
                }

                if (growthBook.IsOff("valyrionservice_exposureindices"))
                {
                    var discountMainElastic = new ElasticCustomerDiscounts
                {
                    Event_received = DateTime.SpecifyKind(discountEventDto.EventDate, DateTimeKind.Local)
                        .ToUniversalTime(),
                    Discount = new ElasticCustomerDiscountDiscount
                    {
                        Event_type = discountEventDto.EventType,
                        Discount_id = [],
                        Page_number = discountEventDto.PageNumber ?? 0
                    },
                    Customer = new ElasticCustomerDiscountCustomer
                    {
                        Email = discountEventDto.CustomerEmail,
                        Viabill_debtor_account_id = partnerGuid
                    },
                    Shop_event = new ElasticCustomerDiscountShopEvent
                    {
                        Webshop_id = [],
                        Webshop_name = [],
                    }
                };

                //MerchantIds
                if (merchantIds == null)
                {
                    discountMainElastic.Shop_event.Webshop_id.Add(merchantId);
                    discountMainElastic.Shop_event.Webshop_name.Add(merchantName);
                }
                else
                {
                    discountMainElastic.Shop_event.Webshop_id = merchantIds;
                    discountMainElastic.Shop_event.Webshop_name = merchantNames;
                }

                //DiscountIds
                if (discountIds == null)
                {
                    discountMainElastic.Discount.Discount_id.Add(discountId);
                }
                else
                {
                    discountMainElastic.Discount.Discount_id = discountIds;
                }

                using (var publishChannel = rabbitConnectionCloud.CreateModel())
                {
                    var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(discountMainElastic));
                    publishChannel.BasicPublish(exchange: "customer",
                        routingKey: "customer_discount_event",
                        basicProperties: null,
                        body: actionBody);
                    }
            }
                else
            {
                var partnerInfo = await partnerService.GetIdAndNameAsync(partnerContext.PartnerId);

                var discountDisplayEvent = new ElasticExposureDiscountDisplayDto
                {
                    EventReceived = DateTime.SpecifyKind(discountEventDto.EventDate, DateTimeKind.Local).ToUniversalTime(),
                    Discount = new ElasticExposureDiscountListDto
                    {
                        Id = []
                    },
                    Customer = new ElasticExposureCustomerDto
                    {
                        Email = discountEventDto.CustomerEmail
                    },
                    Marketing = new ElasticExposureDiscountMarketingDto
                    {
                        Channel = "email",
                        Event = discountEventDto.EventType,
                        PageNumber = discountEventDto.PageNumber ?? 0,
                        PageSize = discountEventDto.PageSize ?? 0
                    },
                    Partner = new ElasticExposurePartnerDto
                    {
                        Id = partnerInfo.Id.ToString(),
                        Name = partnerInfo.Name
                    },
                    Merchant = new ElasticExposureMerchantListDto
                    {
                        Id = []
                    }
                };

                //MerchantIds
                if (merchantIds == null)
                {
                    discountDisplayEvent.Merchant.Id.Add(merchantId);
                    discountDisplayEvent.Merchant.Name.Add(merchantName);
                }
                else
                {
                    discountDisplayEvent.Merchant.Id = merchantIds;
                    discountDisplayEvent.Merchant.Name = merchantNames;
                }

                //DiscountIds
                if (discountIds == null)
                {
                    discountDisplayEvent.Discount.Id.Add(discountId);
                }
                else
                {
                    discountDisplayEvent.Discount.Id = discountIds;
                }

                using (var publishChannel = rabbitConnectionCloud.CreateModel())
                {
                    var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(discountDisplayEvent));
                    publishChannel.BasicPublish(exchange: "customer",
                        routingKey: "exposure_discount_display",
                        basicProperties: null,
                        body: actionBody);
                }
            }
        }
            catch (Exception ex)
            {
                logger.ForContext("service_name", GetType().Name).Error(
                    ex,
                    "{event} sending to {component} exchange '{exchange}' with routing key '{routingKey}' Discount Event object: {discountEvent}",
                    "Failed", "RabbitMQ", "customer", "customer_discount_event",
                    JsonSerializer.Serialize(discountEventDto));
                return new ResponseDto {Success = false, Message = "Adding the Event was unsuccessful"};
            }

            logger.ForContext("service_name", GetType().Name).Information(
                "Successfully added a new Event: {event} from Customer Email: {Email}",
                discountEventDto.EventType, discountEventDto.CustomerEmail);

            return new ResponseDto {Success = true, Message = "Successfully added the Event"};
        }


        public async Task<DiscountPartnerPaginationDto> GetFavoredDiscountsByEmail(string email, int page, int pageSize, bool useMock)
        {

            if (useMock)
            {
                var mockDiscounts = await discountRepository.GetFavoredDiscountsByEmailAsync(email, page, pageSize);
                // TODO -Mock Pagination 
                return new DiscountPartnerPaginationDto {LastPage = 1, Discounts = mockDiscounts};
            }

            var lastPage = 1;
            var discounts = new List<DiscountPartnerDto>();
            try
            {
                var now = DateTime.UtcNow;
                var nowDateOnly = DateOnly.FromDateTime(now);
                var start = pageSize * (page - 1);
                //var vbDev = _environment.IsDevelopment();

                // Fetch favored discount IDs for the customer.
                var favoredDiscountIds = await discountDbContext.Favorites
                    .Where(f => f.Email == email && f.Active)
                    .Select(f => f.FkDiscountId)
                    .ToListAsync();

                if (favoredDiscountIds.Count == 0)
                {
                    return new DiscountPartnerPaginationDto {LastPage = lastPage, Discounts = discounts};
                }

                var cacheKey = $"GetFavoredDiscountsByEmail_V1_{page}_{pageSize}";
                var discountValue = await cacheService.GetData<Tuple<List<DiscountPartnerDto>, int>>(cacheKey);
                if (discountValue == null)
                {
                    var query = discountDbContext.Discounts
                        .AsNoTracking()
                        .Where(a =>
                            a.ActivationDate.Date <= now.Date &&
                            a.ExpireDate >= nowDateOnly &&
                            !a.Deleted &&
                            favoredDiscountIds.Contains(a.Id)
                        )
                        .OrderBy(a => a.SortOrder).ThenBy(a => a.Id);

                    var fetchedDiscounts = await query
                        .Skip(start).Take(pageSize)
                        .ToListAsync();

                    var childDiscounts = fetchedDiscounts
                        .Select(fetchedDiscount =>
                            discountDbContext.Discounts
                                .FirstOrDefault(a => a.Active && !a.Deleted && a.FkParentId == fetchedDiscount.Id))
                        .OfType<Discount>()
                        .ToList();

                    var datetime = DateTime.UtcNow.AddDays(-7);
                    var mappedDiscounts = new List<DiscountPartnerDto>();
                    foreach (var discount in childDiscounts)
                    {
                        string? discountCode = null;
                        string? discountCodeId = null;
                        if (discount.DiscountType == "Coupon")
                        {
                            if (email != AnonymousEmail)
                            {
                                var discountCodeObj = await discountDbContext.DiscountCodes.FirstOrDefaultAsync(a =>
                                    a.FkDiscountId == discount.FkParentId && a.Email == email &&
                                    (a.Locked > datetime || a.Locked == null) && a.Active);
                                if (discountCodeObj == null)
                                {
                                    var existingParentDiscountCode =
                                        await discountDbContext.DiscountCodes.FirstOrDefaultAsync(a =>
                                            a.Email == null && a.FkDiscountId == discount.FkParentId && a.Active);
                                    if (existingParentDiscountCode != null)
                                    {
                                        existingParentDiscountCode.Email = email;
                                        discountCode = existingParentDiscountCode.Code;
                                        discountCodeId = existingParentDiscountCode.Id.ToString();
                                        await discountDbContext.SaveChangesAsync().ConfigureAwait(false);
                                    }
                                }
                                else
                                {
                                    discountCode = discountCodeObj.Code;
                                    discountCodeId = discountCodeObj.Id.ToString();
                                }
                            }
                            else
                            {
                                discountCode = "hidden";
                            }
                        }

                        var merchant = await merchantService.GetByIdAsync(discount.FkMerchantId);
                        var merchantId = discount.FkMerchantId.ToString();
                        var webshopName = string.Empty;
                        if (merchant != null)
                        {
                            merchantId = merchant.Id.ToString();
                            webshopName = merchant.Name;
                        }

                        var contact = await customerService.GetByEmailAsync(email);
                        var partnerGuid = string.Empty;
                        if (contact?.PartnerGuid != null)
                            partnerGuid = contact.PartnerGuid;

                        var redirectUrl = configuration["RedirectService-Url"] + "redirectDiscount/" + JwtTokenEncode(
                            email,
                            partnerGuid, discount.RedirectUrl, discount.Id.ToString(), merchantId,
                            discount.DiscountType, webshopName, discountCode, discountCodeId);

                        mappedDiscounts.Add(new DiscountPartnerDto
                        {
                            Id = discount.Id,
                            ImageSrc = discount.OverviewImageSrc,
                            Title = discount.Title,
                            Subtitle = discount.Subtitle,
                            ComingSoon = discount.ComingSoon,
                            MerchantId = discount.FkMerchantId,
                            IsNew = false,
                            ExpiresSoon = false,
                            Favored = true,
                            RedirectLink = redirectUrl,
                            DiscountCode = discountCode,
                            DiscountType = discount.DiscountType,
                            Description = discount.Description,
                            ExpireDate = discount.ExpireDate.ToString()
                        });
                    }

                    var totalDiscounts1 = await query.CountAsync();
                    discountValue = Tuple.Create(mappedDiscounts, totalDiscounts1);
                    if (discountValue.Item1.Count > 0)
                    {
                        cacheService.SetData(cacheKey, discountValue, TimeSpan.FromHours(1));
                    }
                }

                discounts = discountValue.Item1;
                var totalDiscounts = discountValue.Item2;
                lastPage = (int) (Math.Ceiling((decimal) totalDiscounts / pageSize));

                var discountEvent = new DiscountPartnerEventDto()
                {
                    CustomerEmail = email,
                    EventDate = now,
                    EventType = "favored_discount_overview_viewed",
                    PageNumber = page,
                    PageSize = pageSize
                };
                var webShopIds = discounts.Select(a => a.MerchantId.ToString()).ToList();
                var discountIds = discounts.Select(a => a.Id.ToString()).ToList();
                await AddDiscountEventAsync(discountEvent, webShopIds, discountIds);
            }
            catch (Exception ex)
            {
                logger.ForContext("service_name", GetType().Name).Error(
                    ex,
                    "Error in returning Favored Discount Pagination for Page: {Page} with Size: {PageSize} for Customer: {CustomerEmail}",
                    page, pageSize, email);
                throw;
            }

            var discountPagination = new DiscountPartnerPaginationDto
            {
                LastPage = lastPage,
                Discounts = discounts
            };

            return discountPagination;
        }

        public async Task<ResponseDto> ToggleFavoriteAsync(DiscountPartnerFavoriteRequestDto favoriteRequest)
        {
            try
            {
                var discount =
                    await discountDbContext.Discounts.FirstOrDefaultAsync(a => a.Id == favoriteRequest.DiscountId);

                if (discount == null)
                {
                    return new ResponseDto
                    {
                        Success = false,
                        Message = $"Invalid discountId provided. DiscountId: {favoriteRequest.DiscountId}"
                    };
                }


                var existingFavorite = await discountDbContext.Favorites
                    .FirstOrDefaultAsync(
                        f => f.FkDiscountId == discount.FkParentId && f.Email == favoriteRequest.CustomerEmail);

                if (favoriteRequest.Favored)
                {
                    if (existingFavorite == null)
                    {
                        var newFavorite = new Favorite
                        {
                            FkDiscountId = discount?.FkParentId ?? discount?.Id ?? favoriteRequest.DiscountId,
                            Email = favoriteRequest.CustomerEmail,
                            Active = true
                        };
                        discountDbContext.Favorites.Add(newFavorite);
                    }
                    else
                    {
                        existingFavorite.Active = favoriteRequest.Favored;
                    }
                }
                else
                {
                    if (existingFavorite != null)
                    {
                        existingFavorite.Active = favoriteRequest.Favored;
                    }
                }

                await discountDbContext.SaveChangesAsync();
                
                cacheService.RemoveDataWildcard("GetFavoredDiscountsByEmail_");
                memoryCache.Remove($"FavoredDiscounts_V1_{favoriteRequest.CustomerEmail}");

                return new ResponseDto
                {
                    Success = true,
                    Message = "Successfully toggled favorite status.".ToLower()
                };
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Error while toggling favorite for DiscountId: {DiscountId} and Email: {Email}",
                    favoriteRequest.DiscountId, favoriteRequest.CustomerEmail);

                return new ResponseDto
                {
                    Success = false,
                    Message = $"Error trying to toggle favorite for discount: {favoriteRequest.DiscountId}"
                };
            }
        }

        public async Task<DiscountCampaignDto?> GetActiveDiscountCampaign(string language, bool useMock = false)
        {
            if (useMock)
            {
                return await discountRepository.GetDiscountCampaignsPartnerAsync(language);
            }
            
            var partnerId = partnerContext.PartnerId;
            var cacheKey = $"GetDiscountCampaignsPartner_V1_{language}_{partnerId}";
            var discountCampaignDto = await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15);
                var discountCampaign = await discountDbContext.Campaigns
                    .AsNoTracking()
                    .Include(a => a.CampaignMeta)
                    // TODO Include Partner Id and Language
                    //.Where(a => a.PartnerId == partnerId && a.CampaignMeta.Any(b => b.MetaKey == "Language" && b.MetaValue == language))
                    .FirstOrDefaultAsync(a => a.Selected);
                if (discountCampaign == null) return null;

                var discountCampaignDto = new DiscountCampaignDto()
                {
                    QueryName = discountCampaign.Query,
                    ImageSrc = discountCampaign.BannerImageSrc,
                    Title = discountCampaign.CampaignMeta.FirstOrDefault(a => a.MetaKey == "Title")?.MetaValue ?? ""
                };
                cacheService.SetData(cacheKey, discountCampaignDto, TimeSpan.FromHours(1), true);
                return discountCampaignDto;
            });

            //Check if campaign was found or no campaign was active at last check
            if (discountCampaignDto?.Title != null)
            {
                return discountCampaignDto;
            }

            return null;
        }
    }
}