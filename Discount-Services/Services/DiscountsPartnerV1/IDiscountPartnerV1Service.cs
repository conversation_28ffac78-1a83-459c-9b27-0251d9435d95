using Discount_Services.Models;
using Discount_Services.Models.DiscountsPartnerV1;
using Shared.Models;

namespace Discount_Services.Services.DiscountsPartnerV1
{
    public interface IDiscountPartnerV1Service
    {
        Task<DiscountPartnerPaginationDto> GetDiscountByEmail(string email, int page, int pageSize, string? campaign, bool useMock);

        Task<DiscountDto?> GetDiscountById(string email, int discountId, bool useMock);

        Task<ResponseDto> AddDiscountEventAsync(DiscountPartnerEventDto couponEventDto, List<string>? merchantIds = null,
            List<string>? discountIds = null);

        Task<DiscountCampaignDto?> GetActiveDiscountCampaign(string language, bool useMock = false);
        Task<DiscountPartnerPaginationDto> GetFavoredDiscountsByEmail(string customerEmail, int page, int size, bool useMock);
        Task<ResponseDto> ToggleFavoriteAsync(DiscountPartnerFavoriteRequestDto favoriteRequest);
    }
}