using System.Diagnostics;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Text.RegularExpressions;
using Audience.Services.Audience;
using Discount_Services.Models.ModelsDal.Discount;
using Discount_Services.Models;
using Discount_Services.Models.DiscountCampaigns;
using Discount_Services.Models.DiscountFavorite;
using EFCore.BulkExtensions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using Partner_Services.Services.General;
using RabbitMQ.Client;
using Shared.Dto.Image;
using Shared.Dto.MerchantScore;
using Shared.Elastic.Models;
using Shared.Models;
using Shared.Models.Customer;
using Shared.Models.Elastic.ElasticCustomerDiscounts;
using Shared.Services.Cache;
using Shared.Services.Image;
using Shared.Services.MerchantRelevance;
using Shared.Services.Partner;
using SixLabors.ImageSharp.PixelFormats;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Drawing.Processing;
using SixLabors.ImageSharp.Formats.Webp;
using SixLabors.ImageSharp.Processing;
using Color = SixLabors.ImageSharp.Color;
using Font = SixLabors.Fonts.Font;
using FontCollection = SixLabors.Fonts.FontCollection;
using FontFamily = SixLabors.Fonts.FontFamily;
using Image = SixLabors.ImageSharp.Image;
using JsonSerializer = System.Text.Json.JsonSerializer;
using PointF = SixLabors.ImageSharp.PointF;
using Discount = Discount_Services.Models.ModelsDal.Discount.Discount;
using GrowthBook;
using Discount_Services.Models.Exposure;
using Shared.Elastic.Models.ElasticExposure;

namespace Discount_Services.Services.Discounts;

public class DiscountService(
    DiscountDbContextTracking discountDbContext,
    ILogger logger,
    //IConnection rabbitConnection,
    [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud,
    IConfiguration configuration,
    IWebHostEnvironment environment,
    VaultSettings vaultSettings,
    IMerchantService merchantService,
    ICustomerService customerService,
    ICacheService cacheService,
    IImageService imageService,
    IMerchantRelevanceService merchantRelevanceService,
    IMemoryCache memoryCache,
    IPartnerService partnerService,
    IPartnerContext partnerContext,
    IGrowthBook growthBook)
    : IDiscountService
{
    private const string AnonymousEmail = "<EMAIL>";

    private const int AgeInterval = 10;

    // Filter handlers for filtering Discounts
    // To add new filters, use AddFilter method or add directly to this dictionary
    // Example: AddFilter("new_filter", query => query.Where(a => /* your condition */));
    private readonly Dictionary<string, Func<IQueryable<Discount>, IQueryable<Discount>>> _filterHandlers = new()
    {
        ["active"] = query => query.Where(a => a.InverseFkParent.Any(b => b.Active && !b.Deleted) && a.ExpireDate >= DateOnly.FromDateTime(DateTime.UtcNow)),
        ["expiring_soon"] = query => query.Where(a => a.ExpireDate <= DateOnly.FromDateTime(DateTime.UtcNow.AddDays(14)) && 
                                                       a.ExpireDate >= DateOnly.FromDateTime(DateTime.UtcNow) && a.InverseFkParent.Any(b => b.Active && !b.Deleted)),
        ["expired"] = query => query.Where(a => a.ExpireDate < DateOnly.FromDateTime(DateTime.UtcNow))
    };

    // Sort handlers for sorting Discounts
    // To add new sorts, use AddSortOption method or add directly to this dictionary
    // Example: AddSortOption("new_sort", (query, order) => order == "asc" ? query.OrderBy(a => a.Property) : query.OrderByDescending(a => a.Property));
    private readonly Dictionary<string, Func<IQueryable<Discount>, string, IQueryable<Discount>>> _sortHandlers = new()
    {
        ["id"] = (query, order) => order == "asc" ? query.OrderBy(a => a.Id) : query.OrderByDescending(a => a.Id),
        ["name"] = (query, order) => order == "asc" ? query.OrderBy(a => a.Name) : query.OrderByDescending(a => a.Name),
        ["title"] = (query, order) => order == "asc" ? query.OrderBy(a => a.Title) : query.OrderByDescending(a => a.Title),
        ["subtitle"] = (query, order) => order == "asc" ? query.OrderBy(a => a.Subtitle) : query.OrderByDescending(a => a.Subtitle),
        ["expireDate"] = (query, order) => order == "asc" ? query.OrderBy(a => a.ExpireDate) : query.OrderByDescending(a => a.ExpireDate),
        ["discountType"] = (query, order) => order == "asc" ? query.OrderBy(a => a.DiscountType) : query.OrderByDescending(a => a.DiscountType)
    };

    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////      Partner App Functionality         ///////////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    public async Task<DiscountPaginationDto> GetDiscountByEmail(string email, int page, int pageSize,
        string? campaignQuery, bool dev, int partnerId)
    {
        int lastPage;
        List<DiscountRefDto> discounts;
        try
        {
            var now = DateTime.Now;
            var nowDateOnly = DateOnly.FromDateTime(now);
            var start = pageSize * (page - 1);
            var contactCacheKey = $"GetContactForMerchantRelevance_{partnerId}_{email}";
            var contact = await cacheService.GetDataWithCacheLockAsync(
                contactCacheKey,
                async () =>
                {
                    var contact = await customerService.GetCustomerForMerchantRelevance(email);
                    if (contact == null)
                    {
                        return await customerService.GetAnonymousContact(email);
                    }

                    return contact;
                },
                memoryCacheExpiry: TimeSpan.FromMinutes(15),
                redisCacheExpiry: TimeSpan.FromMinutes(30)
            );

            var discountCacheKey = $"GetActiveDiscounts_{dev}_{campaignQuery}_{partnerId}";

            // Remove the existing cache entry to force refresh
            //cacheService.RemoveData(discountCacheKey);
            
            var allActiveDiscounts = await cacheService.GetDataWithCacheLockAsync(
                discountCacheKey,
                async () =>
                {
                    var parentsWithActiveChildren = discountDbContext.Discounts.AsNoTracking()
                        .Include(parent =>
                            parent.InverseFkParent.Where(child =>
                                !child.Deleted && child.Active)) // Include only active children
                        .AsSplitQuery()
                        .Where(parent =>
                            parent.FkPartnerId == partnerId &&
                            parent.FkParentId == null && // Only parents
                            parent.Dev == dev &&
                            parent.ActivationDate.Date <= now.Date &&
                            parent.ExpireDate >= nowDateOnly &&
                            !parent.Deleted &&
                            parent.InverseFkParent.Any(child => !child.Deleted && child.Active)
                        ); // Ensure at least one active child

                    if (!string.IsNullOrWhiteSpace(campaignQuery))
                    {
                        parentsWithActiveChildren = parentsWithActiveChildren
                            .Include(d => d.DiscountCampaignsRels)
                            .ThenInclude(dc => dc.FkCampaign)
                            .Where(d => d.DiscountCampaignsRels.Any(dc => dc.FkCampaign.Query == campaignQuery));
                    }
                    else
                    {
                        parentsWithActiveChildren =
                            parentsWithActiveChildren.Where(d => d.DiscountCampaignsRels.Count == 0);
                    }

                    var fetchedDiscounts = (await parentsWithActiveChildren.ToListAsync())
                        .Select(parent =>
                        {
                            // Flatten children into the parent's InverseFkParent
                            parent.InverseFkParent = parent.InverseFkParent
                                .Where(child => !child.Deleted && child.Active)
                                .ToList();
                            return parent;
                        })
                        .ToList();
                    //var fetchedDiscounts = await query.ToListAsync();
                    return fetchedDiscounts;
                },
                memoryCacheExpiry: TimeSpan.FromMinutes(15),
                redisCacheExpiry: TimeSpan.FromMinutes(30)
            );

            var topMerchantsCacheKey = $"TopMerchantsForEmail_{email}_{partnerId}";

            var topMerchants = await cacheService.GetDataWithCacheLockAsync(
                topMerchantsCacheKey,
                async () =>
                {
                    List<MerchantRelevance> topMerchants = [];
                    if (contact != null)
                    {
                        var customerList = new List<MerchantRelevanceCustomerDto> {contact};
                        try
                        {
                            var topMerchantsResult =
                                await merchantRelevanceService.GetMerchantRelevanceForBatch(customerList, AgeInterval, true);
                            topMerchants = topMerchantsResult.FirstOrDefault().Value;
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine(e);
                            topMerchants =
                                await merchantRelevanceService.GetMerchantRelevance(contact, AgeInterval, true);
                        }


                        if (topMerchants == null || topMerchants.Count < 20)
                        {
                            topMerchants =
                                await merchantRelevanceService.GetMerchantRelevance(contact, AgeInterval, true);
                        }
                    }

                    return topMerchants;
                },
                memoryCacheExpiry: TimeSpan.FromMinutes(15),
                redisCacheExpiry: TimeSpan.FromMinutes(30)
            ) ?? [];
            
            List<Discount>? filteredDiscounts;
            if (topMerchants.Count > 0)
            {
                // Ensure unique merchant IDs in HashSet
                var allowedMerchantsIds = new HashSet<int>(
                    topMerchants?.Select(a => a.MerchantId).Distinct() ?? []
                );

                var merchantIndexMap = topMerchants?
                    .Select((m, index) => new {m.MerchantId, Index = index})
                    .ToDictionary(x => x.MerchantId, x => x.Index);
                // Filter discounts by merchant relevance and group by merchant ID to select one discount per merchant randomly
                var filteredDiscountsCacheKey = $"FilteredDiscounts_{partnerId}_{email}_{dev}_{campaignQuery}";
                filteredDiscounts = await cacheService.GetDataWithCacheLockAsync(
                    filteredDiscountsCacheKey,
                    async () =>
                    {
                        var groupedDiscounts = allActiveDiscounts
                            .Where(a => allowedMerchantsIds.Contains(a.FkMerchantId) || allowedMerchantsIds.Count == 0)
                            .GroupBy(a => new { a.FkMerchantId, a.DiscountType })  // Distinguish by MerchantId AND Type
                            .SelectMany(g => g)
                            .ToList();

                        var random = new Random();
                        
                        // Separate discounts with and without SortOrder > 0
                        var fixedPositionDiscounts = groupedDiscounts
                            .Where(d => d.SortOrder > 0)
                            .OrderBy(d => d.SortOrder)
                            .GroupBy(d => d.SortOrder)  // Group by SortOrder to handle duplicates
                            .ToDictionary(
                                g => g.Key,
                                g => g.OrderBy(_ => random.Next()).First() // Randomly select one if there are multiple
                            );

                        var remainingDiscounts = groupedDiscounts
                            .Where(d => d.SortOrder == 0)
                            .OrderBy(d => merchantIndexMap.TryGetValue(d.FkMerchantId, out var value) ? value : int.MaxValue)
                            .ThenBy(_ => Guid.NewGuid()) // Random fallback to avoid repeated results
                            .ToList();

                        var sortedDiscounts = new List<Discount>();

                        // Fill positions according to fixed sort order
                        for (int i = 1; i <= groupedDiscounts.Count; i++)
                        {
                            if (fixedPositionDiscounts.TryGetValue(i, out var discount))
                            {
                                sortedDiscounts.Add(discount);
                            }
                            else if (remainingDiscounts.Count != 0)
                            {
                                sortedDiscounts.Add(remainingDiscounts.First());
                                remainingDiscounts.RemoveAt(0);
                            }
                        }

                        // If remaining discounts are still left, add them at the end
                        sortedDiscounts.AddRange(remainingDiscounts);

                        return sortedDiscounts;
                    },
                    memoryCacheExpiry: TimeSpan.FromMinutes(8),
                    redisCacheExpiry: TimeSpan.FromMinutes(14)
                );

                // Fallback logic if not enough discounts are found
                if (filteredDiscounts.Count < 20)
                {
                    filteredDiscounts = allActiveDiscounts
                        .DistinctBy(a => a.FkMerchantId)
                        .ToList();
                }
            }
            else
            {
                filteredDiscounts = allActiveDiscounts;
            }
            
            // Paginate the results
            var discountsAll = filteredDiscounts.Skip(start).Take(pageSize).ToList();

            var favoredDiscounts = new List<int>();
            if (contact is {IsAnonymous: false})
            {
                // Fetch favored discount IDs for the customer.
                var favoredCacheKey = $"FavoredDiscounts_{email}_{partnerId}";
                favoredDiscounts = await cacheService.GetDataWithCacheLockAsync(
                    favoredCacheKey,
                    async () =>
                    {
                        var discountIdsHashSet = new HashSet<int>(filteredDiscounts.Select(d => d.Id));

                        return await discountDbContext.Favorites
                            .AsNoTracking()
                            .Where(f => f.Email == email && discountIdsHashSet.Contains(f.FkDiscountId) && f.Active)
                            .Select(f => f.FkDiscountId)
                            .ToListAsync()
                            .ConfigureAwait(false);
                    },
                    memoryCacheExpiry: TimeSpan.FromMinutes(10),
                    redisCacheExpiry: TimeSpan.FromMinutes(30)
                ) ?? [];
            }

            // Prepare the prefixed discounts
            var prefixedDiscounts = new List<DiscountRefDto>();
            var rnd = new Random();
            for (var i = 0; i < discountsAll.Count; i++)
            {
                var discount = discountsAll[i];
                if (discount.InverseFkParent.Count == 0)
                    continue; // Check to avoid IndexOutOfRange

                var subDiscountIndex = rnd.Next(0, discount.InverseFkParent.Count);
                var subDiscount = discount.InverseFkParent.ElementAt(subDiscountIndex);
                prefixedDiscounts.Add(new DiscountRefDto
                {
                    Id = subDiscount.Id,
                    ImageSrc = subDiscount.OverviewImageSrc,
                    Title = dev ? string.Concat("#", (start + 1 + i), " ", subDiscount.Title) : subDiscount.Title,
                    Subtitle = subDiscount.Subtitle,
                    ComingSoon = discount.ComingSoon,
                    WebshopId = subDiscount.FkMerchantId,
                    IsNew = false,
                    ExpiresSoon = false,
                    Favored = favoredDiscounts?.Contains(discount.Id) ?? false
                });
            }

            var (paginatedDiscountList, totalDiscounts) = Tuple.Create(prefixedDiscounts, filteredDiscounts.Count);

            discounts = paginatedDiscountList;

            lastPage = (int) (Math.Ceiling((decimal) totalDiscounts / pageSize));

            var merchantIds = discounts.Select(a => a.WebshopId.ToString()).ToList();
            var discountIds = discounts.Select(a => a.Id).ToList();

            // Log discount event
            var discountEvent = new DiscountEventDto()
            {
                DiscountIds = discountIds,
                MerchantIds = merchantIds,
                CustomerEmail = email,
                EventDate = now,
                EventType = "discount_overview_viewed",
                PageNumber = page
            };
            
            // Fire and forget the event logging to make it fully async
            Task.Run(async () => await SendDiscountEventAsync(discountEvent, partnerId));
            //await SendDiscountEventAsync(discountEvent);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(
                ex,
                "Error in returning Discount Pagination for Page: {Page} with Size: {PageSize} for Customer: {CustomerEmail}",
                page, pageSize, email);
            throw;
        }

        var discountPagination = new DiscountPaginationDto
        {
            LastPage = lastPage,
            Discounts = discounts
        };

        return discountPagination;
    }
    
    private async Task SendDiscountEventAsync(DiscountEventDto discountEvent, int partnerId)
    {
        using (var httpClient = new HttpClient())
        {
            try
            {
                // TODO - Change this Setup to be more Dynamic and less hardcoded
                const string endpointUrl = "https://valyrion-services.azurewebsites.net/discounts/event/internal";
                //const string endpointUrl = "https://localhost:7100/discounts/event/internal";
                httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
                httpClient.DefaultRequestHeaders.Add("PartnerId", partnerId.ToString());

                // Serialize the object to JSON
                var jsonContent = JsonSerializer.Serialize(discountEvent);
                var httpContent = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // Send HTTP POST request
                await httpClient.PostAsync(endpointUrl, httpContent);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending discount event: {ex.Message}");
            }
        }
    }


    public async Task<DiscountDto> GetDiscountById(string email, int discountId, bool dev)
    {
        try
        {
            var datetime = DateTime.UtcNow.AddDays(-7);
            var existingDiscount =
                await discountDbContext.Discounts.AsNoTracking()
                    .FirstOrDefaultAsync(a => a.Id == discountId && a.Dev == dev);
            if (existingDiscount == null)
            {
                return new DiscountDto
                {
                    Id = 0,
                    Title = "Ikke fundet",
                    Subtitle = "",
                    Description = "",
                    ImageSrc = "",
                    ExpireDate = DateOnly.FromDateTime(DateTime.UtcNow),
                    RedirectLink = "",
                    DiscountCode = null,
                    ComingSoon = false,
                    DiscountType = "None",
                    IsSubscribed = false,
                    IsNew = false,
                    ExpiresSoon = false
                };
            }

            var isSubscribed = false;
            if (existingDiscount.ComingSoon)
            {
                isSubscribed = await discountDbContext.Subscriptions
                    .AnyAsync(a => a.FkDiscountId == existingDiscount.FkParentId && a.Email == email);
            }

            string? discountCode = null;
            string? discountCodeId = null;
            if (existingDiscount.DiscountType == "Coupon")
            {
                if (email != AnonymousEmail)
                {
                    var discountCodeObj = await discountDbContext.DiscountCodes.FirstOrDefaultAsync(a =>
                        a.FkDiscountId == existingDiscount.FkParentId && a.Email == email &&
                        (a.Locked > datetime || a.Locked == null) && a.Active);
                    if (discountCodeObj == null)
                    {
                        var existingParentDiscountCode =
                            await discountDbContext.DiscountCodes.FirstOrDefaultAsync(a =>
                                a.Email == null && a.FkDiscountId == existingDiscount.FkParentId && a.Active);
                        if (existingParentDiscountCode != null)
                        {
                            existingParentDiscountCode.Email = email;
                            discountCode = existingParentDiscountCode.Code;
                            discountCodeId = existingParentDiscountCode.Id.ToString();
                            await discountDbContext.SaveChangesAsync().ConfigureAwait(false);
                        }
                    }
                    else
                    {
                        discountCode = discountCodeObj.Code;
                        discountCodeId = discountCodeObj.Id.ToString();
                    }
                }
                else
                {
                    discountCode = "hidden";
                }
            }

            var merchant = await merchantService.GetByIdAsync(existingDiscount.FkMerchantId);
            var merchantId = existingDiscount.FkMerchantId.ToString();
            var webshopName = "Not Found";
            if (merchant != null)
            {
                merchantId = merchant.Id.ToString();
                webshopName = merchant.Name;
            }

            var contact = await customerService.GetByEmailAsync(email);
            var partnerGuid = string.Empty;
            if (contact != null && contact.PartnerGuid != null)
                partnerGuid = contact.PartnerGuid;

            var redirectUrl = configuration["RedirectService-Url"] + "redirectDiscount/" + JwtTokenEncode(email,
                partnerGuid!,
                existingDiscount.RedirectUrl!, existingDiscount.Id.ToString(), merchantId,
                existingDiscount.DiscountType, webshopName, discountCode, discountCodeId);

            DiscountDto discount;
            if (existingDiscount.DiscountType == "Coupon" && string.IsNullOrEmpty(discountCode) &&
                !existingDiscount.ComingSoon)
            {
                discount = new DiscountDto
                {
                    Id = existingDiscount.Id,
                    Title = $"{existingDiscount.Title} er desværre udløbet",
                    Subtitle = existingDiscount.Subtitle,
                    Description = existingDiscount.Description,
                    ImageSrc = existingDiscount.BannerImageSrc,
                    ExpireDate = existingDiscount.ExpireDate,
                    RedirectLink = redirectUrl,
                    DiscountCode = null,
                    ComingSoon = existingDiscount.ComingSoon,
                    DiscountType = existingDiscount.DiscountType,
                    IsSubscribed = isSubscribed,
                    IsNew = false,
                    ExpiresSoon = false,
                    /*IsNew = existingDiscount.ReleaseDate != null &&
                            now.Subtract((DateTime)existingDiscount.ReleaseDate).TotalDays <= 7,
                    ExpiresSoon = existingDiscount.ExpireDate.Subtract(now).TotalDays <= 7*/
                };
            }
            else
            {
                bool isFavored = await discountDbContext.Favorites
                    .AnyAsync(f => f.FkDiscountId == existingDiscount.FkParentId && f.Email == email && f.Active);

                discount = new DiscountDto
                {
                    Id = existingDiscount.Id,
                    Title = existingDiscount.Title,
                    Subtitle = existingDiscount.Subtitle,
                    Description = existingDiscount.Description,
                    ImageSrc = existingDiscount.BannerImageSrc,
                    ExpireDate = existingDiscount.ExpireDate,
                    RedirectLink = redirectUrl,
                    DiscountCode = discountCode,
                    ComingSoon = existingDiscount.ComingSoon,
                    DiscountType = existingDiscount.DiscountType,
                    IsSubscribed = isSubscribed,
                    IsNew = false,
                    ExpiresSoon = false,
                    /*IsNew = existingDiscount.ReleaseDate != null &&
                            now.Subtract((DateTime)existingDiscount.ReleaseDate).TotalDays <= 7,
                    ExpiresSoon = existingDiscount.ExpireDate.Subtract(now).TotalDays <= 7,*/
                    Favored = isFavored
                };
            }

            return discount;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(
                ex,
                "Error in returning Discount with Id:{DiscountId} for Customer:{CustomerEmail}",
                discountId, email);

            return new DiscountDto
            {
                Id = 0,
                Title = "Ikke fundet",
                Subtitle = "",
                Description = "",
                ImageSrc = "",
                ExpireDate = DateOnly.FromDateTime(DateTime.UtcNow),
                RedirectLink = "",
                DiscountCode = null,
                ComingSoon = false,
                DiscountType = "None",
                IsSubscribed = false
            };
        }
    }

    private string JwtTokenEncode(string email, string partnerGuid, string url, string discountId,
        string merchantId = "", string type = "", string webshopName = "", string? discountCode = "",
        string? discountCodeId = "")
    {
        var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(vaultSettings.JwtTokenKey));

        var tokenHandler = new JwtSecurityTokenHandler();

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(new Claim[]
            {
                new Claim("email", email),
                new Claim("url", url),
                new Claim("did", discountId),
                new Claim("wid", merchantId),
                new Claim("wname", webshopName),
                new Claim("type", type),
                new Claim("dcode", discountCode ?? ""),
                new Claim("dcodeid", discountCodeId ?? ""),
                //new Claim("vbdai", debtorAccountId)
                new Claim("pguid", partnerGuid),
                new Claim("parid", partnerContext.PartnerId.ToString())
            }),
            Issuer = vaultSettings.JwtIssuer,
            SigningCredentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha512Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);

        return tokenHandler.WriteToken(token);
    }

    public async Task<ResponseDto> AddDiscountEventAsync(DiscountEventDto discountEventDto, int partnerId)
    {
        try
        {
            switch (discountEventDto.EventType)
            {
                case "coupon_details_viewed":
                    discountEventDto.EventType = "discount_details_viewed";
                    break;
                case "coupon_link_opened":
                    discountEventDto.EventType = "discount_link_opened";
                    break;
                case "coupon_code_copied":
                    discountEventDto.EventType = "discount_code_copied";
                    break;
            }

            if (discountEventDto.EventType != "discount_overview_viewed" &&
                discountEventDto.EventType != "discounts_list_viewed" &&
                discountEventDto.EventType != "favored_discount_overview_viewed" &&
                discountEventDto.EventType != "discount_details_viewed" &&
                discountEventDto.EventType != "discount_link_opened" &&
                discountEventDto.EventType != "discount_code_copied" &&
                discountEventDto.EventType != "discount_favorised" &&
                discountEventDto.EventType != "discount_unfavorised")
            {
                logger.ForContext("service_name", GetType().Name).Warning(
                    "Received and unknown discount event: {event} from Customer Email: {Email}",
                    discountEventDto.EventType, discountEventDto.CustomerEmail);

                return new ResponseDto
                {
                    Success = false,
                    Message =
                        $"The EventType: {discountEventDto.EventType} is unknown - known EventTypes are: 'discounts_list_viewed', " +
                        $"'discount_details_viewed', 'discount_link_opened', 'discount_code_copied', " +
                        $"'discount_favorised', 'discount_unfavorised'"
                };
            }

            var partnerName = await partnerService.GetPartnerName();
            if(string.IsNullOrEmpty(partnerName))
                partnerName = "n/a";
            
            var merchantId = "n/a";
            var discountId = "n/a";
            var merchantName = "n/a";

            var stopWatch = new Stopwatch();
            stopWatch.Start();

            List<string>? merchantIds = null;
            List<string>? discountIds = null;
            if (discountEventDto.DiscountId != null && discountEventDto.DiscountId != 0)
            {
                var merchantIdInt = discountDbContext.Discounts.Single(a => a.Id == discountEventDto.DiscountId)
                    .FkMerchantId;
                merchantId = merchantIdInt.ToString();
                discountId = discountEventDto.DiscountId?.ToString() ?? "n/a";
                var merchant = await merchantService.GetByIdAsync(merchantIdInt);
                if (merchant != null)
                {
                    merchantName = merchant.Name;
                }
            }

            stopWatch.Stop();
            Console.WriteLine($"Get merchant and discount: {stopWatch.ElapsedMilliseconds}");

            stopWatch.Restart();
            if (discountEventDto.DiscountIds != null && discountEventDto.DiscountIds.Count > 0)
            {
                if (discountEventDto.MerchantIds != null && discountEventDto.MerchantIds.Count > 0)
                {
                    merchantIds = discountEventDto.MerchantIds;
                    discountIds = discountEventDto.DiscountIds.Select(a => a.ToString()).ToList();
                }
                else
                {
                    foreach (var id in discountEventDto.DiscountIds)
                    {
                        var merchantIdInt = discountDbContext.Discounts.Single(a => a.Id == id)
                            .FkMerchantId;
                        merchantIds ??= [];
                        discountIds ??= [];

                        merchantIds.Add(merchantIdInt.ToString());
                        discountIds.Add(id.ToString());


                        /*var merchant = await _merchantService.GetByIdAsync(merchantIdInt);
                        if (merchant != null)
                        {
                            merchantName = merchant.Name;
                        }*/
                    }
                }
            }

            stopWatch.Stop();
            Console.WriteLine($"Get merchant and discount ids: {stopWatch.ElapsedMilliseconds}");

            stopWatch.Restart();

            if (growthBook.IsOff("valyrionservice_exposureindices"))
            {
                var discountMainElastic = new ElasticCustomerDiscounts
            {
                Event_received = DateTime.SpecifyKind(discountEventDto.EventDate, DateTimeKind.Local).ToUniversalTime(),
                Discount = new ElasticCustomerDiscountDiscount
                {
                    Event_type = discountEventDto.EventType,
                    Discount_id = [],
                    Page_number = discountEventDto.PageNumber ?? 0
                },
                Customer = new ElasticCustomerDiscountCustomer
                {
                    Email = discountEventDto.CustomerEmail,
                    Viabill_debtor_account_id = "n/a"
                },
                Shop_event = new ElasticCustomerDiscountShopEvent
                {
                    Webshop_id = [],
                    Webshop_name = [],
                },
                Partner = new ElasticPartner
                {
                    Id = partnerId.ToString(),
                    Name = partnerName
                }
            };

            //MerchantIds
            if (merchantIds == null)
            {
                discountMainElastic.Shop_event.Webshop_id.Add(merchantId);
                discountMainElastic.Shop_event.Webshop_name.Add(merchantName);
            }
            else
            {
                discountMainElastic.Shop_event.Webshop_id = merchantIds;
                //Remove for optimizing, if needed find another way adding 300ms
                /*foreach (var merchantIdDb in merchantIds)
                {
                    var webshop = await _merchantService.GetByIdAsync(Convert.ToInt32(merchantIdDb));
                    if (webshop != null)
                    {
                        discountMainElastic.Shop_event.Webshop_name.Add(webshop.Name);
                    }
                }*/
            }

            //DiscountIds
            if (discountIds == null)
            {
                discountMainElastic.Discount.Discount_id.Add(discountId);
            }
            else
            {
                discountMainElastic.Discount.Discount_id = discountIds;
            }

            stopWatch.Stop();
            Console.WriteLine($"Prepare discount event: {stopWatch.ElapsedMilliseconds}");

            stopWatch.Restart();
            using (var publishChannel = rabbitConnectionCloud.CreateModel())
            {
                var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(discountMainElastic));
                publishChannel.BasicPublish(exchange: "customer",
                    routingKey: "customer_discount_event",
                    basicProperties: null,
                    body: actionBody);
            }

            stopWatch.Stop();
            Console.WriteLine($"Publish discount event: {stopWatch.ElapsedMilliseconds}");
            }
            else
            {
                var partnerInfo = await partnerService.GetIdAndNameAsync(partnerContext.PartnerId);

                var discountDisplayEvent = new ElasticExposureDiscountDisplayDto
                {
                    EventReceived = DateTime.SpecifyKind(discountEventDto.EventDate, DateTimeKind.Local).ToUniversalTime(),
                    Discount = new ElasticExposureDiscountListDto
                    {
                        Id = []
                    },
                    Customer = new ElasticExposureCustomerDto
                    {
                        Email = discountEventDto.CustomerEmail
                    },
                    Marketing = new ElasticExposureDiscountMarketingDto
                    {
                        Channel = "email",
                        Event = discountEventDto.EventType,
                        PageNumber = discountEventDto.PageNumber ?? 0,
                        PageSize = 0
                    },
                    Partner = new ElasticExposurePartnerDto
                    {
                        Id = partnerInfo.Id.ToString(),
                        Name = partnerInfo.Name
                    },
                    Merchant = new ElasticExposureMerchantListDto
                    {
                        Id = [],
                        Name = []
                    }
                };

                //MerchantIds
                if (merchantIds == null)
                {
                    discountDisplayEvent.Merchant.Id.Add(merchantId);
                    discountDisplayEvent.Merchant.Name.Add(merchantName);
                }
                else
                {
                    discountDisplayEvent.Merchant.Id = merchantIds;
                }

                //DiscountIds
                if (discountIds == null)
                {
                    discountDisplayEvent.Discount.Id.Add(discountId);
                }
                else
                {
                    discountDisplayEvent.Discount.Id = discountIds;
                }

                var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(discountDisplayEvent));

                using (var publishChannel = rabbitConnectionCloud.CreateModel())
                {
                    publishChannel.BasicPublish(exchange: "customer",
                        routingKey: "exposure_discount_display",
                        basicProperties: null,
                        body: actionBody);
                }
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(
                ex,
                "{event} sending to {component} exchange '{exchange}' with routing key '{routingKey}' Discount Event object: {discountEvent}",
                "Failed", "RabbitMQ", "customer", "customer_discount_event",
                JsonSerializer.Serialize(discountEventDto));
            return new ResponseDto {Success = false, Message = "Adding the Event was unsuccessful"};
        }

        logger.ForContext("service_name", GetType().Name).Information(
            "Successfully added a new Event: {event} from Customer Email: {Email}",
            discountEventDto.EventType, discountEventDto.CustomerEmail);

        return new ResponseDto {Success = true, Message = "Successfully added the Event"};
    }


    public async Task<DiscountPaginationDto> GetFavoredDiscountsByEmail(string email, int page, int pageSize, bool dev)
    {
        var lastPage = 1;
        var discounts = new List<DiscountRefDto>();
        try
        {
            var now = DateTime.UtcNow;
            var nowDateOnly = DateOnly.FromDateTime(now);
            var start = pageSize * (page - 1);
            //var vbDev = _environment.IsDevelopment();

            // Fetch favored discount IDs for the customer.
            var favoredDiscountIds = await discountDbContext.Favorites
                .Where(f => f.Email == email && f.Active)
                .Select(f => f.FkDiscountId)
                .ToListAsync();

            if (!favoredDiscountIds.Any())
            {
                return new DiscountPaginationDto {LastPage = lastPage, Discounts = discounts};
            }

            var cacheKey = $"FavoredDiscountService_GetFavoredDiscountsByEmail_{page}_{pageSize}_{email}_{dev}_{partnerContext.PartnerId}";
            var discountValue = await cacheService.GetData<Tuple<List<DiscountRefDto>, int>>(cacheKey);
            if (discountValue == null)
            {
                var query = discountDbContext.Discounts.AsNoTracking()
                    .Where(a =>
                        a.Dev == dev &&
                        a.ActivationDate.Date <= now.Date &&
                        a.ExpireDate >= nowDateOnly &&
                        !a.Deleted &&
                        favoredDiscountIds.Contains(a.Id)
                    )
                    .OrderBy(a => a.SortOrder).ThenBy(a => a.Id);

                var fetchedDiscounts = await query
                    .Skip(start).Take(pageSize)
                    .ToListAsync();

                var childDiscounts = fetchedDiscounts
                    .Select(fetchedDiscount =>
                        discountDbContext.Discounts
                            .FirstOrDefault(a => a.Active && !a.Deleted && a.FkParentId == fetchedDiscount.Id))
                    .OfType<Discount>()
                    .ToList();

                var prefix = dev ? "#" : "";
                var prefixedDiscounts = childDiscounts.Select((d, index) => new DiscountRefDto
                {
                    Id = d.Id,
                    ImageSrc = d.OverviewImageSrc,
                    Title = dev ? $"{prefix}{start + 1 + index} {d.Title}" : d.Title,
                    Subtitle = d.Subtitle,
                    ComingSoon = d.ComingSoon,
                    WebshopId = d.FkMerchantId,
                    //IsNew = false,
                    //ExpiresSoon = false,
                    Favored = true
                }).ToList();

                var totalDiscounts1 = await query.CountAsync();
                discountValue = Tuple.Create(prefixedDiscounts, totalDiscounts1);
                if (discountValue.Item1.Count > 0)
                {
                    cacheService.SetData(cacheKey, discountValue, TimeSpan.FromHours(1));
                }
            }

            discounts = discountValue.Item1;
            var totalDiscounts = discountValue.Item2;
            lastPage = (int) (Math.Ceiling((decimal) totalDiscounts / pageSize));

            var merchantIds = discounts.Select(a => a.WebshopId.ToString()).ToList();
            var discountIds = discounts.Select(a => a.Id).ToList();
            var discountEvent = new DiscountEventDto()
            {
                DiscountIds = discountIds,
                MerchantIds = merchantIds,
                CustomerEmail = email,
                EventDate = now,
                EventType = "favored_discount_overview_viewed",
                PageNumber = page
            };
            await AddDiscountEventAsync(discountEvent, partnerContext.PartnerId);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(
                ex,
                "Error in returning Favored Discount Pagination for Page: {Page} with Size: {PageSize} for Customer: {CustomerEmail}",
                page, pageSize, email);
            throw;
        }

        var discountPagination = new DiscountPaginationDto
        {
            LastPage = lastPage,
            Discounts = discounts
        };

        return discountPagination;
    }

    public async Task<List<Merchant>> GetDiscountPartners(bool active = true)
    {
        return await discountDbContext.Merchants
            .Include(a => a.MerchantDiscountCodes)
            .Where(a => a.Active == active)
            .ToListAsync();
    }

    public async Task<List<Discount>> GetActiveDiscount()
    {
        var now = DateTime.UtcNow;
        var discounts = discountDbContext.Discounts
            .Include(a => a.InverseFkParent.Where(b => b.Active && !b.Deleted))
            .Where(a => !a.Deleted && a.ExpireDate >= DateOnly.FromDateTime(now) && a.ActivationDate <= now &&
                        a.InverseFkParent.Count > 0 && a.FkPartnerId == partnerContext.PartnerId).ToList();
        return discounts;
    }


    public async Task<ResponseDto> ToggleFavoriteAsync(DiscountFavoriteRequestDto favoriteRequest)
    {
        try
        {
            var discount =
                await discountDbContext.Discounts.FirstOrDefaultAsync(a => a.Id == favoriteRequest.DiscountId);

            if (discount == null)
            {
                return new ResponseDto
                {
                    Success = false,
                    Message = $"Error trying to toggle favorite for discount: {favoriteRequest.DiscountId}"
                };
            }


            var existingFavorite = await discountDbContext.Favorites
                .FirstOrDefaultAsync(
                    f => f.FkDiscountId == discount.FkParentId && f.Email == favoriteRequest.Email);

            if (favoriteRequest.Favored)
            {
                if (existingFavorite == null)
                {
                    var newFavorite = new Favorite()
                    {
                        FkDiscountId = discount?.FkParentId ?? discount?.Id ?? favoriteRequest.DiscountId,
                        Email = favoriteRequest.Email,
                        Active = true
                    };
                    discountDbContext.Favorites.Add(newFavorite);
                }
                else
                {
                    existingFavorite.Active = favoriteRequest.Favored;
                }
            }
            else
            {
                if (existingFavorite != null)
                {
                    existingFavorite.Active = favoriteRequest.Favored;
                }
            }

            await discountDbContext.SaveChangesAsync();

            cacheService.RemoveDataWildcard("FavoredDiscountService_GetFavoredDiscountsByEmail_");
            await cacheService.InvalidateCacheAsync($"FavoredDiscounts_{favoriteRequest.Email}_{discount.FkPartnerId}");

            return new ResponseDto
            {
                Success = true,
                Message = "Successfully toggled favorite status.".ToLower()
            };
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error while toggling favorite for DiscountId: {DiscountId} and Email: {Email}",
                favoriteRequest.DiscountId, favoriteRequest.Email);

            return new ResponseDto
            {
                Success = false,
                Message = $"Error trying to toggle favorite for discount: {favoriteRequest.DiscountId}"
            };
        }
    }

    public async Task<DiscountCampaignsAppDto?> GetDiscountCampaignsPartner(int partnerId, bool dev)
    {
        var cacheKey = $"DiscountService_GetDiscountCampaignsPartner_{partnerId}_{dev}";
        var discountCampaignsAppDto = await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30);
            var discountCampaign = await discountDbContext.Campaigns
                .Include(a => a.CampaignMeta)
                .Where(a => a.Dev == dev && a.FkPartnerId == partnerId)
                .FirstOrDefaultAsync(a => a.Selected);
            if (discountCampaign != null)
            {
                var discountCampaignsAppDto = new DiscountCampaignsAppDto()
                {
                    queryName = discountCampaign.Query,
                    BannerImageUrl = discountCampaign.BannerImageSrc,
                    TitleDa =
                        discountCampaign.CampaignMeta.FirstOrDefault(a => a.MetaKey == "TitleDa")?.MetaValue ?? "",
                    TitleEn =
                        discountCampaign.CampaignMeta.FirstOrDefault(a => a.MetaKey == "TitleEn")?.MetaValue ?? ""
                };
                cacheService.SetData(cacheKey, discountCampaignsAppDto, TimeSpan.FromHours(1), true);
                return discountCampaignsAppDto;
            }

            return null;
        });
        
        //Check if campaign was found or no campaign was active at last check
        if (discountCampaignsAppDto?.TitleDa != null)
        {
            return discountCampaignsAppDto;
        }

        return null;
    }

    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////      Partner App Functionality DONE      /////////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////


    public async Task<DiscountPaginationAppDto> GetPaginationDiscounts(int page, int size, string searchFilter,
        string searchString, bool deleted, bool dev, string sortName, string sortOrder)
    {
        searchString = searchString.Trim().ToLowerInvariant();

        var discountsQuery = discountDbContext.Discounts
            .Include(d => d.DiscountCampaignsRels)
            .ThenInclude(a => a.FkCampaign)
            .Include(a => a.InverseFkParent)
            .Where(a => (!a.Deleted == !deleted || a.Deleted == deleted) &&
                        a.Dev == dev &&
                        a.FkParentId == null &&
                        a.FkPartnerId == partnerContext.PartnerId &&
                        (searchString == "" || a.Title.ToLower().Contains(searchString) ||
                         a.Name.ToLower().Contains(searchString) || a.Subtitle.ToLower().Contains(searchString)));

        // Apply filter using handler
        discountsQuery = ApplyFilter(discountsQuery, searchFilter);

        // Apply sorting using handler
        discountsQuery = ApplySorting(discountsQuery, sortName, sortOrder);


        var start = size * page;
        var discounts = await discountsQuery
            .Skip(start).Take(size)
            .ToListAsync();

        var discountDtos = new List<DiscountAppDto>();

        foreach (var discount in discounts)
        {
            var discountDto = CreateDiscountAppDtoFromDiscount(discount);

            discountDto.Children = discount.InverseFkParent
                .Where(child => deleted || !child.Deleted)
                .Select(CreateDiscountAppDtoFromDiscount)
                .ToList();

            if (discount.DiscountType == "Coupon")
            {
                var discountCodesQuery = discountDbContext.DiscountCodes
                    .Where(a => a.FkDiscountId == discount.Id && a.Active);

                var groupedDiscountCodes = await discountCodesQuery
                    .GroupBy(cc => cc.CreatedDate)
                    .Select(a => new
                    {
                        Created = a.Key,
                        Codes = a.Select(cc => cc.Code).ToList(),
                        Count = a.Count(),
                        QuantityLocked = a.Count(cc => cc.Email != null)
                    })
                    .ToListAsync();

                discountDto.DiscountCodes = groupedDiscountCodes
                    .Select(a =>
                    {
                        var isGeneric = a.Codes.Distinct().Count() == 1;
                        var discountCodeAppDto = new DiscountCodeAppDto
                        {
                            CodeCount = a.Count,
                            Created = a.Created,
                            Type = isGeneric ? "Generic" : "Unique",
                            QuantityLocked = a.QuantityLocked,
                            GenericCode = isGeneric ? a.Codes.First() : null
                        };
                        return discountCodeAppDto;
                    })
                    .ToList();
            }

            discountDtos.Add(discountDto);
        }

        var totalSize = await discountsQuery.CountAsync();
        var discountPagination = new DiscountPaginationAppDto()
        {
            Discounts = discountDtos,
            Size = totalSize,
            HasPreviousPage = page > 0,
            HasNextPage = (page + 1) * size < totalSize
        };
        return discountPagination;
    }

    private IQueryable<Discount> ApplyFilter(IQueryable<Discount> query, string searchFilter)
    {
        if (string.IsNullOrEmpty(searchFilter) || !_filterHandlers.ContainsKey(searchFilter))
            return query;

        return _filterHandlers[searchFilter](query);
    }

    private IQueryable<Discount> ApplySorting(IQueryable<Discount> query, string sortName, string sortOrder)
    {
        // Default sorting if no specific sort is requested
        if (string.IsNullOrEmpty(sortName) || !_sortHandlers.ContainsKey(sortName))
        {
            return query.OrderBy(a => a.SortOrder).ThenBy(a => a.Id);
        }

        return _sortHandlers[sortName](query, sortOrder);
    }

    public async Task<int> GetExpiringSoonDiscountCountAsync() {
        // Get all discounts that expire in the next 14 days
        var fourteenDaysFromNow = DateTime.UtcNow.AddDays(14);
        return await discountDbContext.Discounts
            .CountAsync(a => 
            a.ExpireDate <= DateOnly.FromDateTime(fourteenDaysFromNow) && 
            !a.Deleted && 
            a.FkParentId == null && 
            a.ExpireDate >= DateOnly.FromDateTime(DateTime.UtcNow) && 
            a.FkPartnerId == partnerContext.PartnerId);
    }
    
    public async Task<List<DiscountAppDto>> GetDiscounts(bool deleted = false)
    {
        var discountsQuery = discountDbContext.Discounts
            .AsNoTracking()
            .Include(d => d.DiscountCampaignsRels)
            .ThenInclude(a => a.FkCampaign)
            .Include(a => a.InverseFkParent)
            .AsSplitQuery()
            .Where(a => (!a.Deleted == !deleted || a.Deleted == deleted) && a.FkParentId == null && a.FkPartnerId == partnerContext.PartnerId)
            .OrderBy(a => a.SortOrder)
            .ThenBy(a => a.Id);

        var discounts = await discountsQuery.ToListAsync();

        var discountDtos = new List<DiscountAppDto>();

        foreach (var discount in discounts)
        {
            var discountDto = CreateDiscountAppDtoFromDiscount(discount);

            discountDto.Children = discount.InverseFkParent
                .Where(child => deleted || !child.Deleted)
                .Select(CreateDiscountAppDtoFromDiscount)
                .ToList();

            if (discount.DiscountType == "Coupon")
            {
                var discountCodesQuery = discountDbContext.DiscountCodes
                    .Where(a => a.FkDiscountId == discount.Id && a.Active);
                var groupedDiscountCodes = await discountCodesQuery
                    .GroupBy(cc => cc.CreatedDate)
                    .Select(a => new
                    {
                        Created = a.Key,
                        Codes = a.Select(cc => cc.Code).ToList(),
                        Count = a.Count(),
                        QuantityLocked = a.Count(cc => cc.Email != null)
                    })
                    .ToListAsync();

                discountDto.DiscountCodes = groupedDiscountCodes
                    .Select(a =>
                    {
                        var isGeneric = a.Codes.Distinct().Count() == 1;
                        var discountCodeAppDto = new DiscountCodeAppDto
                        {
                            CodeCount = a.Count,
                            Created = a.Created,
                            Type = isGeneric ? "Generic" : "Unique",
                            QuantityLocked = a.QuantityLocked,
                            GenericCode = isGeneric ? a.Codes.First() : null
                        };
                        return discountCodeAppDto;
                    })
                    .ToList();
            }

            discountDtos.Add(discountDto);
        }

        return discountDtos;
    }

    private DiscountAppDto CreateDiscountAppDtoFromDiscount(Discount discount)
    {
        var discountDto = new DiscountAppDto
        {
            Id = discount.Id,
            CreatedDate = discount.CreatedDate,
            LastModifiedDate = discount.LastModifiedDate,
            SortOrder = discount.SortOrder,
            Name = discount.Name,
            OverviewImageSrc = discount.OverviewImageSrc,
            BannerImageSrc = discount.BannerImageSrc,
            Title = discount.Title,
            Subtitle = discount.Subtitle,
            Description = discount.Description,
            ExpireDate = discount.ExpireDate,
            FkMerchantId = discount.FkMerchantId,
            RedirectUrl = discount.RedirectUrl,
            Active = discount.Active,
            Activatable = merchantService.CheckIfMarketingIsAllowed(discount.FkMerchantId).Result,
            DeactivateWhenEmpty = discount.DeactivateWhenEmpty,
            Deleted = discount.Deleted,
            DiscountType = discount.DiscountType,
            ComingSoon = discount.ComingSoon,
            Dev = discount.Dev,
            ActivationDate = discount.ActivationDate,
            ParentId = discount.FkParentId,
            Children = new List<DiscountAppDto>(),
            DiscountCampaigns = discount.DiscountCampaignsRels
                .Select(campaignRelation => new DiscountCampaignDto
                {
                    Id = campaignRelation.FkCampaignId,
                    Name = campaignRelation.FkCampaign.Name,
                    BannerImageSrc = campaignRelation.FkCampaign.BannerImageSrc,
                    Query = campaignRelation.FkCampaign.Query
                }).ToList()
        };

        return discountDto;
    }


    public async Task<Discount> CreateDiscount(int partnerId, DiscountAppDto discountAppDto)
    {
        var discountCodes = new List<DiscountCode>();
        var now = DateTime.UtcNow;

        DateTime? releaseDate = null;
        if (!discountAppDto.ComingSoon && discountAppDto.Active)
        {
            if (discountAppDto.ActivationDate.Date <= now.Date)
            {
                releaseDate = now;
            }
            else
            {
                releaseDate = discountAppDto.ActivationDate;
            }
        }

        var discount = new Discount
        {
            Title = discountAppDto.Title,
            Subtitle = discountAppDto.Subtitle,
            Description = discountAppDto.Description,
            SortOrder = discountAppDto.SortOrder,
            ExpireDate = discountAppDto.ExpireDate,
            RedirectUrl = discountAppDto.RedirectUrl,
            Active = discountAppDto.Active,
            CreatedDate = now,
            DeactivateWhenEmpty = discountAppDto.DeactivateWhenEmpty,
            Deleted = discountAppDto.Deleted,
            Name = discountAppDto.Name,
            LastModifiedDate = now,
            FkMerchantId = discountAppDto.FkMerchantId,
            DiscountCodes = discountCodes,
            BannerImageSrc = "",
            OverviewImageSrc = "",
            DiscountType = discountAppDto.DiscountType,
            ComingSoon = discountAppDto.ComingSoon,
            Dev = discountAppDto.Dev,
            ActivationDate = discountAppDto.ActivationDate,
            ReleaseDate = releaseDate,
            FkPartnerId = partnerId
        };
        discountDbContext.Add(discount);
        await discountDbContext.SaveChangesAsync();

        foreach (var couponCode in discountAppDto.DiscountCodes)
        {
            if (couponCode.Codes != null)
            {
                await discountDbContext.BulkInsertAsync(couponCode.Codes.Select(code => new DiscountCode
                {
                    FkDiscountId = discount.Id, Code = code, Locked = null,
                    Email = null, CreatedDate = now, Active = true
                })).ConfigureAwait(false);
            }
        }

        if (discountAppDto.BannerImage != null)
        {
            discount.BannerImageSrc = await UploadPictureDiscount(discountAppDto, discount, "bannerImage");
        }

        if (discountAppDto.OverviewImage != null)
        {
            discount.OverviewImageSrc = await UploadPictureDiscount(discountAppDto, discount, "overviewImage");
        }

        if (discountAppDto.DiscountCampaigns != null && discount.Id != 0)
        {
            var incomingCampaignIds = discountAppDto.DiscountCampaigns.Select(c => c.Id).ToList();

            var newRelationsToAdd = incomingCampaignIds
                .Select(id => new DiscountCampaignsRel()
                {
                    FkDiscountId = discount.Id,
                    FkCampaignId = id
                }).ToList();

            discountDbContext.DiscountCampaignsRels.AddRange(newRelationsToAdd);
        }

        discountDbContext.Update(discount);
        await discountDbContext.SaveChangesAsync();
        discountDbContext.ChangeTracker.Clear();
        discount.DiscountCampaignsRels = null;
        discount.DiscountCodes = new List<DiscountCode>();
        discount.FkParentId = discount.Id;
        discount.Id = 0;
        discountDbContext.Add(discount);
        await discountDbContext.SaveChangesAsync();

        ClearCache();
        return discount;
    }

    public async Task<Discount> CreateChildDiscount(DiscountAppDto discountAppDto)
    {
        var discount = await discountDbContext.Discounts.AsNoTracking().SingleAsync(a => a.Id == discountAppDto.Id);
        discount.FkParentId = discount.Id;
        discount.Id = 0;
        discount.CreatedDate = DateTime.UtcNow;
        discount.LastModifiedDate = DateTime.UtcNow;
        discountDbContext.Add(discount);
        await discountDbContext.SaveChangesAsync();
        return discount;
    }

    public async Task<Discount> UpdateDiscount(DiscountAppDto discountAppDto)
    {
        var existingDiscount = await discountDbContext.Discounts.FirstOrDefaultAsync(c => c.Id == discountAppDto.Id);

        if (existingDiscount == null)
        {
            throw new Exception($"Discount with Id: {discountAppDto.Id} does not exist");
        }

        if (!existingDiscount.ReleaseDate.HasValue ||
            (existingDiscount.ReleaseDate.HasValue && existingDiscount.ReleaseDate.Value.Date > DateTime.UtcNow.Date))
        {
            if (!discountAppDto.ComingSoon && discountAppDto.Active)
            {
                if (discountAppDto.ActivationDate.Date <= DateTime.UtcNow.Date)
                {
                    existingDiscount.ReleaseDate = DateTime.UtcNow;
                }
                else
                {
                    existingDiscount.ReleaseDate = discountAppDto.ActivationDate;
                }
            }
        }


        existingDiscount.Name = discountAppDto.Name;
        existingDiscount.Title = discountAppDto.Title;
        existingDiscount.Subtitle = discountAppDto.Subtitle;
        existingDiscount.Description = discountAppDto.Description;
        existingDiscount.SortOrder = discountAppDto.SortOrder;
        existingDiscount.Active = discountAppDto.Active;
        existingDiscount.ExpireDate = discountAppDto.ExpireDate;
        existingDiscount.DeactivateWhenEmpty = discountAppDto.DeactivateWhenEmpty;
        existingDiscount.ComingSoon = discountAppDto.ComingSoon;
        existingDiscount.DiscountType = discountAppDto.DiscountType;
        existingDiscount.RedirectUrl = discountAppDto.RedirectUrl;
        existingDiscount.FkMerchantId = discountAppDto.FkMerchantId;
        existingDiscount.ActivationDate = discountAppDto.ActivationDate;
        existingDiscount.LastModifiedDate = DateTime.UtcNow;
        existingDiscount.Dev = discountAppDto.Dev;

        //Update child discounts with expireDate data.
        var childDiscounts = await discountDbContext.Discounts.Where(a => a.FkParentId == existingDiscount.Id)
            .ToListAsync();
        foreach (var childDiscount in childDiscounts)
        {
            childDiscount.ExpireDate = existingDiscount.ExpireDate;
            childDiscount.ActivationDate = existingDiscount.ActivationDate;
        }


        if (existingDiscount.DiscountType != discountAppDto.DiscountType && existingDiscount.DiscountType == "Coupon")
        {
            var existingBatches = await discountDbContext.DiscountCodes
                .Where(a => a.FkDiscountId == existingDiscount.Id)
                .GroupBy(dc => dc.CreatedDate)
                .Select(a => a.Key)
                .ToListAsync();
            foreach (var existingBatch in existingBatches)
            {
                await DeleteDiscountCodesByCreatedDate(existingDiscount.Id, existingBatch);
            }
        }

        if (discountAppDto.DiscountType == "Coupon")
        {
            if (discountAppDto.DiscountCodes != null)
            {
                // Group incoming codes by batches
                var incomingCodesBatches = discountAppDto.DiscountCodes
                    .Where(a => a.Codes != null)
                    .GroupBy(dc => dc.Created)
                    .ToDictionary(g => g.Key, g => g.SelectMany(dc => dc.Codes));

                // Add or update incoming batches
                foreach (var batch in incomingCodesBatches)
                {
                    // This is a new batch
                    var now = batch.Key;
                    await discountDbContext.BulkInsertAsync(batch.Value.Select(code => new DiscountCode
                    {
                        FkDiscountId = existingDiscount.Id, Code = code, Locked = null,
                        Email = null, CreatedDate = now, Active = true
                    })).ConfigureAwait(false);
                }
            }
        }

        if (discountAppDto.BannerImage != null)
        {
            existingDiscount.BannerImageSrc =
                await UploadPictureDiscount(discountAppDto, existingDiscount, "bannerImage");
        }

        if (discountAppDto.OverviewImage != null)
        {
            existingDiscount.OverviewImageSrc =
                await UploadPictureDiscount(discountAppDto, existingDiscount, "overviewImage");
        }

        if (discountAppDto.DiscountCampaigns != null)
        {
            var existingRelations = await discountDbContext.DiscountCampaignsRels
                .Where(r => r.FkDiscountId == existingDiscount.Id).ToListAsync();

            var incomingCampaignIds = discountAppDto.DiscountCampaigns.Select(c => c.Id).ToList();

            var relationsToRemove = existingRelations
                .Where(er => !incomingCampaignIds.Contains(er.FkCampaignId))
                .ToList();

            discountDbContext.DiscountCampaignsRels.RemoveRange(relationsToRemove);

            var existingCampaignIds = existingRelations.Select(r => r.FkCampaignId).ToList();
            var newRelationsToAdd = incomingCampaignIds
                .Where(id => !existingCampaignIds.Contains(id))
                .Select(id => new DiscountCampaignsRel()
                {
                    FkDiscountId = existingDiscount.Id,
                    FkCampaignId = id
                }).ToList();

            discountDbContext.DiscountCampaignsRels.AddRange(newRelationsToAdd);
        }


        discountDbContext.Update(existingDiscount);
        await discountDbContext.SaveChangesAsync();

        ClearCache();
        return existingDiscount;
    }

    public async Task<Discount> DuplicateDiscount(int partnerId, DiscountAppDto discountAppDto)
    {
        var discount = new Discount
        {
            Name = discountAppDto.Name,
            Title = discountAppDto.Title,
            Subtitle = discountAppDto.Subtitle,
            SortOrder = 0,
            Active = false,
            Deleted = false,
            DiscountType = "General",
            ComingSoon = false,
            Description = discountAppDto.Description,
            ExpireDate = discountAppDto.ExpireDate,
            RedirectUrl = discountAppDto.RedirectUrl,
            CreatedDate = DateTime.UtcNow,
            DeactivateWhenEmpty = discountAppDto.DeactivateWhenEmpty,
            LastModifiedDate = DateTime.UtcNow,
            FkMerchantId = discountAppDto.FkMerchantId,
            BannerImageSrc = discountAppDto.BannerImageSrc,
            OverviewImageSrc = discountAppDto.OverviewImageSrc,
            Dev = discountAppDto.Dev,
            FkParentId = discountAppDto.ParentId,
            ActivationDate = discountAppDto.ActivationDate,
            ReleaseDate = DateTime.UtcNow,
            FkPartnerId = partnerId
        };

        await discountDbContext.AddAsync(discount);
        await discountDbContext.SaveChangesAsync();

        ClearCache();
        return discount;
    }


    private async Task<string> UploadPictureDiscount(DiscountAppDto discountAppDto, Discount discount, string type)
    {
        if (type == "bannerImage")
        {
            return imageService.CreateImages($"{discountAppDto.BannerImage!.Name}",
                discountAppDto.BannerImage.Data, "discounts");
        }

        return imageService.CreateImages($"{discountAppDto.OverviewImage!.Name}",
            discountAppDto.OverviewImage.Data, "discounts");
    }

    private async Task<string> UploadPictureDiscountCampaign(string name, string data)
    {
        var match = Regex.Match(data, @"data:(?<type>.*?);base64,(?<data>.*)");
        var mimeType = match.Groups["type"].Value;
        string ext = mimeType.Split('/')[1];
        return imageService.CreateImages($"{name}.{ext}",
            data, "discountCampaigns");
    }

    public async Task<ResponseDto> DeleteDiscount(int discountId)
    {
        var discount = await discountDbContext.Discounts.FirstOrDefaultAsync(d => d.Id == discountId);

        if (discount == null)
        {
            return new ResponseDto()
            {
                Success = false,
                Message = "Discount not found"
            };
        }

        discount.Deleted = true;

        discountDbContext.Discounts.Update(discount);
        await discountDbContext.SaveChangesAsync();

        return new ResponseDto()
        {
            Success = true,
            Message = "Successfully deleted discount"
        };
    }

    public async Task<ResponseDto> DeleteDiscountCodesByCreatedDate(long discountId, DateTime createdDate)
    {
        var discountCodes = await discountDbContext.DiscountCodes
            .Where(d => d.FkDiscountId == discountId && d.CreatedDate == createdDate)
            .ToListAsync();

        var deletables = discountCodes.Where(a => a.Email == null).ToList();
        discountDbContext.DiscountCodes.RemoveRange(deletables);

        var rest = discountCodes.Where(a => a.Email != null).ToList();
        rest.ForEach(a => a.Active = false);

        discountDbContext.DiscountCodes.UpdateRange(rest);
        await discountDbContext.SaveChangesAsync();

        return new ResponseDto()
        {
            Success = true,
            Message = "Successfully deleted discountCodes"
        };
    }

    public async Task HandleActivatedDiscounts(List<Merchant_Services.Models.ModelsDal.Merchant.Merchant> merchants)
    {
        var discountsAll = await discountDbContext.Discounts.Where(a => a.Active && a.Deleted == false).ToListAsync();
        foreach (var merchant in merchants.Where(a => !a.IsMarketingAllowed))
        {
            var discounts = discountsAll.Where(a => a.FkMerchantId == merchant.Id).ToList();
            foreach (var discount in discounts)
            {
                discount.Active = false;
            }
        }

        await discountDbContext.SaveChangesAsync();
    }

    public async Task<List<Discount>> GetAllActiveDiscountsWithChildrenAsync(string campaignQuery = "")
    {
        var parentsWithActiveChildren = discountDbContext.Discounts.AsNoTracking()
                        .Include(parent =>
                            parent.InverseFkParent.Where(child =>
                                !child.Deleted && child.Active)) // Include only active children
                        .AsSplitQuery()
                        .Where(parent =>
                            parent.FkPartnerId == partnerContext.PartnerId &&
                            parent.FkParentId == null && // Only parents
                            parent.Dev == false &&
                            parent.ActivationDate.Date <= DateTime.UtcNow.Date &&
                            parent.ExpireDate >= DateOnly.FromDateTime(DateTime.UtcNow) &&
                            !parent.Deleted &&
                            parent.InverseFkParent.Any(child => !child.Deleted && child.Active)
                        ); // Ensure at least one active child

                    if (!string.IsNullOrWhiteSpace(campaignQuery))
                    {
                        parentsWithActiveChildren = parentsWithActiveChildren
                            .Include(d => d.DiscountCampaignsRels)
                            .ThenInclude(dc => dc.FkCampaign)
                            .Where(d => d.DiscountCampaignsRels.Any(dc => dc.FkCampaign.Query == campaignQuery));
                    }
                    else
                    {
                        parentsWithActiveChildren =
                            parentsWithActiveChildren.Where(d => d.DiscountCampaignsRels.Count == 0);
                    }

                    var fetchedDiscounts = (await parentsWithActiveChildren.ToListAsync())
                        .Select(parent =>
                        {
                            // Flatten children into the parent's InverseFkParent
                            parent.InverseFkParent = parent.InverseFkParent
                                .Where(child => !child.Deleted && child.Active)
                                .ToList();
                            return parent;
                        })
                        .ToList();
                    //var fetchedDiscounts = await query.ToListAsync();
                    return fetchedDiscounts;
    }

    public async Task<Discount> GetDiscountWithChildren(int discountId)
    {
        return await discountDbContext.Discounts
            .AsNoTracking()
            .Include(a => a.InverseFkParent)
            .SingleAsync(a => a.Id == discountId);
    }

    public async Task<List<DiscountCampaignDto>> GetDiscountCampaigns()
    {
        var campaigns = await discountDbContext.Campaigns
            .AsNoTracking()
            .Include(a => a.CampaignMeta)
            .Include(a => a.DiscountCampaignsRels)
            .AsSplitQuery()
            .Where(a => a.Active && a.FkPartnerId == partnerContext.PartnerId).ToListAsync()
            .ConfigureAwait(false);
        var discounts = await GetDiscounts();

        return campaigns.Select(campaign => new DiscountCampaignDto()
            {
                Active = campaign.Active,
                BannerImageSrc = campaign.BannerImageSrc,
                Id = campaign.Id,
                Name = campaign.Name,
                Query = campaign.Query,
                Selected = campaign.Selected,
                Dev = campaign.Dev,
                DiscountQuantity = campaign.DiscountCampaignsRels.Count(a => a.FkCampaignId == campaign.Id),
                Discounts = discounts.Where(a => a.DiscountCampaigns.Any(b => b.Id == campaign.Id)).ToList(),
                DiscountCampaignMeta = campaign.CampaignMeta.Select(meta => new DiscountCampaignMetaDto()
                    {
                        Id = meta.Id,
                        MetaKey = meta.MetaKey,
                        MetaValue = meta.MetaValue
                    })
                    .ToList()
            })
            .ToList();
    }

    public async Task<List<DiscountCampaignDto>> GetDiscountCampaignsSimple()
    {
        var campaigns = await discountDbContext.Campaigns
            .Include(a => a.CampaignMeta)
            .Include(a => a.DiscountCampaignsRels)
            .Where(a => a.Active && a.FkPartnerId == partnerContext.PartnerId).ToListAsync()
            .ConfigureAwait(false);

        return campaigns.Select(campaign => new DiscountCampaignDto()
            {
                Active = campaign.Active,
                BannerImageSrc = campaign.BannerImageSrc,
                Id = campaign.Id,
                Name = campaign.Name,
                Query = campaign.Query,
                Selected = campaign.Selected,
                Dev = campaign.Dev,
                DiscountQuantity = campaign.DiscountCampaignsRels.Count(a => a.FkCampaignId == campaign.Id),
                //Discounts = campaign.DiscountCampaignRelations.Select(a => a.Discount).ToList(),
                //Discounts = discounts.Where(a => a.DiscountCampaigns.Any(b => b.Id == campaign.Id)).ToList(),
                DiscountCampaignMeta = campaign.CampaignMeta.Select(meta => new DiscountCampaignMetaDto()
                    {
                        Id = meta.Id,
                        MetaKey = meta.MetaKey,
                        MetaValue = meta.MetaValue
                    })
                    .ToList()
            })
            .ToList();
    }

    public async Task<Campaign> AddDiscountCampaign(DiscountCampaignDto discountCampaignDto)
    {
        var discountCampaign = new Campaign()
        {
            Name = discountCampaignDto.Name,
            Query = discountCampaignDto.Query,
            Active = discountCampaignDto.Active,
            Selected = discountCampaignDto.Selected,
            Dev = discountCampaignDto.Dev,
            BannerImageSrc = "",
            CampaignMeta = new List<CampaignMetum>(),
            FkPartnerId = partnerContext.PartnerId
        };

        if (discountCampaignDto.DiscountCampaignMeta != null)
            foreach (var discountCampaignMetaDto in discountCampaignDto.DiscountCampaignMeta)
            {
                discountCampaign.CampaignMeta.Add(new CampaignMetum()
                {
                    MetaKey = discountCampaignMetaDto.MetaKey,
                    MetaValue = discountCampaignMetaDto.MetaValue
                });
            }

        discountDbContext.Campaigns.Add(discountCampaign);
        await discountDbContext.SaveChangesAsync().ConfigureAwait(false);
        discountCampaign.BannerImageSrc =
            await UploadPictureDiscountCampaign($"{discountCampaign.Id}-{discountCampaign.Name}",
                discountCampaignDto.BannerImageSrc);
        discountDbContext.Update(discountCampaign);
        await discountDbContext.SaveChangesAsync().ConfigureAwait(false);
        return discountCampaign;
    }

    public async Task<FileDto> GenerateDiscountImageAsync(ImageCreateDiscountDto imageCreateDiscountDto)
    {
        FontCollection collection = new();
        FontFamily smallFamily = collection.Add("Models/StaticData/Poppins-Regular.ttf");
        Font fontSmall = smallFamily.CreateFont(100);

        FontFamily bigFamily = collection.Add("Models/StaticData/Poppins-ExtraBold.ttf");
        Font fontBig = bigFamily.CreateFont(197);

        var rawData = imageCreateDiscountDto.ImageData.Data.Split("base64,");
        imageCreateDiscountDto.ImageData.Data = rawData[1];
        byte[] imageBytes = Convert.FromBase64String(imageCreateDiscountDto.ImageData.Data);
        using (MemoryStream ms = new MemoryStream(imageBytes))
        {
            using Image<Rgba32> background = Image.Load<Rgba32>(ms);
            background.Mutate(x => x.Brightness(0.65f)); // The value should be between 0 and 1, the lower the darker
            background.Mutate(x =>
                x.DrawText(imageCreateDiscountDto.SmallText, fontSmall, Color.White, new PointF(52, 475)));
            background.Mutate(x =>
                x.DrawText(imageCreateDiscountDto.LargeText, fontBig, Color.White, new PointF(52, 600)));
            using (MemoryStream ms1 = new MemoryStream())
            {
                var webpEncoder = new WebpEncoder
                {
                    Quality = 100
                };
                await background.SaveAsync(ms1, webpEncoder);
                byte[] imageBytes1 = ms1.ToArray();

                // Convert byte array to base64 string
                return new FileDto
                {
                    Data = $"{rawData[0]}base64," + Convert.ToBase64String(imageBytes1)
                };
            }
        }
    }

    public async Task<Campaign> UpdateDiscountCampaign(DiscountCampaignDto discountCampaignDto)
    {
        var existingDiscountCampaign =
            await discountDbContext.Campaigns.FirstOrDefaultAsync(dc => dc.Id == discountCampaignDto.Id);

        if (existingDiscountCampaign == null)
        {
            throw new Exception($"DiscountCampaign with Id: {discountCampaignDto.Id} does not exist");
        }

        existingDiscountCampaign.Name = discountCampaignDto.Name;
        existingDiscountCampaign.Query = discountCampaignDto.Query;
        existingDiscountCampaign.Active = discountCampaignDto.Active;
        existingDiscountCampaign.Selected = discountCampaignDto.Selected;

        if (discountCampaignDto.BannerImageSrc.Contains("base64,"))
        {
            existingDiscountCampaign.BannerImageSrc = await UploadPictureDiscountCampaign(
                $"{existingDiscountCampaign.Id}-{existingDiscountCampaign.Name}",
                discountCampaignDto.BannerImageSrc);
        }

        if (discountCampaignDto.DiscountCampaignMeta?.Count > 0)
        {
            foreach (var discountCampaignMeta in discountCampaignDto.DiscountCampaignMeta)
            {
                var oldDiscountCampaignMeta =
                    await discountDbContext.CampaignMeta.FindAsync(discountCampaignMeta.Id);
                if (oldDiscountCampaignMeta != null)
                {
                    discountDbContext.Entry(oldDiscountCampaignMeta).CurrentValues.SetValues(discountCampaignMeta);
                }
                else
                {
                    discountDbContext.CampaignMeta.Add(new CampaignMetum()
                    {
                        FkCampaignId = existingDiscountCampaign.Id,
                        MetaKey = discountCampaignMeta.MetaKey,
                        MetaValue = discountCampaignMeta.MetaValue
                    });
                }
            }
        }

        await discountDbContext.SaveChangesAsync().ConfigureAwait(false);

        return existingDiscountCampaign;
    }


    public async Task<ResponseDto> DeleteDiscountCampaign(int discountCampaignId)
    {
        var discountCampaign =
            await discountDbContext.Campaigns.FirstOrDefaultAsync(d => d.Id == discountCampaignId);

        if (discountCampaign == null)
        {
            return new ResponseDto()
            {
                Success = false,
                Message = "DiscountCampaign not found"
            };
        }

        discountCampaign.Active = false;

        discountDbContext.Campaigns.Update(discountCampaign);
        await discountDbContext.SaveChangesAsync();

        return new ResponseDto()
        {
            Success = true,
            Message = "Successfully deleted discountCampaign"
        };
    }

    private void ClearCache()
    {
        cacheService.RemoveDataWildcard("GetActiveDiscounts_");
    }

    /// <summary>
    /// Add a new filter option to the discount service
    /// </summary>
    /// <param name="filterName">Name of the filter</param>
    /// <param name="filterFunc">Function to apply the filter</param>
    public void AddFilter(string filterName, Func<IQueryable<Discount>, IQueryable<Discount>> filterFunc)
    {
        _filterHandlers[filterName] = filterFunc;
    }

    /// <summary>
    /// Add a new sort option to the discount service
    /// </summary>
    /// <param name="sortName">Name of the sort field</param>
    /// <param name="sortFunc">Function to apply the sort</param>
    public void AddSortOption(string sortName, Func<IQueryable<Discount>, string, IQueryable<Discount>> sortFunc)
    {
        _sortHandlers[sortName] = sortFunc;
    }

    /// <summary>
    /// Get available filter options
    /// </summary>
    /// <returns>List of available filter names</returns>
    public IEnumerable<string> GetAvailableFilters() => _filterHandlers.Keys;

    /// <summary>
    /// Get available sort options
    /// </summary>
    /// <returns>List of available sort names</returns>
    public IEnumerable<string> GetAvailableSorts() => _sortHandlers.Keys;
}