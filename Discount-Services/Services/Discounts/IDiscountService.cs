using Discount_Services.Models.ModelsDal.Discount;
using Discount_Services.Models;
using Discount_Services.Models.DiscountCampaigns;
using Discount_Services.Models.DiscountFavorite;
using Shared.Dto.Image;
using Shared.Models;
using Microsoft.EntityFrameworkCore;

namespace Discount_Services.Services.Discounts
{
    public interface IDiscountService
    {
        Task<DiscountPaginationDto> GetDiscountByEmail(string email, int page, int pageSize, string? campaign, bool dev, int partnerId);

        Task<DiscountDto> GetDiscountById(string email, int couponId, bool dev);

        Task<ResponseDto> AddDiscountEventAsync(DiscountEventDto couponEventDto, int partnerId);

        Task<List<DiscountAppDto>> GetDiscounts(bool deleted = false);

        Task<DiscountPaginationAppDto> GetPaginationDiscounts(int page, int size, string searchFilter,
            string searchString, bool deleted, bool dev, string sortName, string sortOrder);

        Task<DiscountCampaignsAppDto?> GetDiscountCampaignsPartner(int partnerId, bool dev);
        Task<Discount> CreateDiscount(int partnerId, DiscountAppDto discountAppDto);
        Task<Discount> CreateChildDiscount(DiscountAppDto discountAppDto);
        Task<Discount> UpdateDiscount(DiscountAppDto discountAppDto);
        Task<Discount> DuplicateDiscount(int partnerId, DiscountAppDto discountAppDto);
        Task<ResponseDto> DeleteDiscount(int discountId);
        Task<ResponseDto> DeleteDiscountCodesByCreatedDate(long discountId, DateTime createdDate);

        Task<Discount> GetDiscountWithChildren(int discountId);

        //DiscountCampaigns
        Task<List<DiscountCampaignDto>> GetDiscountCampaigns();
        Task<List<DiscountCampaignDto>> GetDiscountCampaignsSimple();
        Task<Campaign> AddDiscountCampaign(DiscountCampaignDto discountCampaignDto);
        Task<FileDto> GenerateDiscountImageAsync(ImageCreateDiscountDto imageCreateDiscountDto);
        Task<Campaign> UpdateDiscountCampaign(DiscountCampaignDto discountCampaignDto);
        Task<ResponseDto> DeleteDiscountCampaign(int discountCampaignId);
        Task<ResponseDto> ToggleFavoriteAsync(DiscountFavoriteRequestDto favoriteRequest);

        Task<DiscountPaginationDto> GetFavoredDiscountsByEmail(string customerEmail, int page, int size, bool dev);

        //Discount partners
        Task<List<Merchant>> GetDiscountPartners(bool active = true);

        Task<List<Discount>> GetActiveDiscount();

        Task HandleActivatedDiscounts(List<Merchant_Services.Models.ModelsDal.Merchant.Merchant> merchants);
        Task<List<Discount>> GetAllActiveDiscountsWithChildrenAsync(string campaignQuery = "");
        Task<int> GetExpiringSoonDiscountCountAsync();
        
        // Filter and Sort Management
        void AddFilter(string filterName, Func<IQueryable<Discount>, IQueryable<Discount>> filterFunc);
        void AddSortOption(string sortName, Func<IQueryable<Discount>, string, IQueryable<Discount>> sortFunc);
        IEnumerable<string> GetAvailableFilters();
        IEnumerable<string> GetAvailableSorts();
    }
}