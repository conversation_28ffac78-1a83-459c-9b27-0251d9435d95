using Discount_Services.Models;
using Discount_Services.Models.DiscountFavorite;
using Discount_Services.Models.DiscountsPartnerV1;

namespace Discount_Services.Repositories;

public interface IDiscountRepository
{
    Task<List<DiscountPartnerDto>> GetAllDiscountsAsync(string partnerId);
    Task<List<DiscountPartnerDto>> GetDiscountsByPageAsync(string email, int page, int size, string campaignQueryName = null);
    Task<DiscountDto> GetDiscountByIdAsync(string email, long discountId, bool isDevRequest = false);
    Task<List<DiscountPartnerDto>> GetFavoredDiscountsByEmailAsync(string email, int page, int size);
    Task<DiscountPartnerResponse> ToggleFavoriteAsync(DiscountPartnerFavoriteRequestDto favoriteRequest);
    Task<DiscountPartnerEventResponse> AddDiscountEventAsync(DiscountPartnerEventDto discountEvent);
    Task<DiscountCampaignDto> GetDiscountCampaignsPartnerAsync(string lang);
} 