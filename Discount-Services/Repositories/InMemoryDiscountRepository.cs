using System.Text.Json;
using Discount_Services.Models;
using Discount_Services.Models.DiscountFavorite;
using Discount_Services.Models.DiscountsPartnerV1;
using Microsoft.Extensions.Logging;

namespace Discount_Services.Repositories;

public class InMemoryDiscountRepository : IDiscountRepository
{
    private readonly List<DiscountPartnerDto> _discounts;
    private readonly Dictionary<string, List<long>> _favoritesByEmail;
    private readonly List<DiscountCampaignDto> _campaigns;
    private readonly ILogger<InMemoryDiscountRepository> _logger;

    public InMemoryDiscountRepository(ILogger<InMemoryDiscountRepository> logger)
    {
        _logger = logger;
        _favoritesByEmail = new Dictionary<string, List<long>>();
        _campaigns = InitializeCampaigns();
        _discounts = LoadDiscountsFromJson();
    }

    private List<DiscountPartnerDto> LoadDiscountsFromJson()
    {
        try
        {
            // Try to load the JSON file
            // Discount-Services/MockData/MockDiscounts.json
            // Fix the path to the JSON file
            string filePath = Path.Combine(AppContext.BaseDirectory, "MockData", "MockDiscounts.json");
            
            if (File.Exists(filePath))
            {
                string json = File.ReadAllText(filePath);
                var discounts = JsonSerializer.Deserialize<List<DiscountDto>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                // Convert DiscountDto to DiscountPartnerDto
                return discounts?.Select(d => new DiscountPartnerDto
                {
                    Id = d.Id,
                    ImageSrc = d.ImageSrc,
                    Title = d.Title,
                    Subtitle = d.Subtitle,
                    Description = d.Description,
                    DiscountType = d.DiscountType,
                    DiscountCode = d.DiscountCode,
                    RedirectLink = d.RedirectLink,
                    ComingSoon = d.ComingSoon,
                    Favored = d.Favored,
                    IsNew = d.IsNew,
                    ExpiresSoon = d.ExpiresSoon,
                    // ExpireDate as string in yyyy-MM-dd format
                    ExpireDate = d.ExpireDate.ToString("yyyy-MM-dd")
                }).ToList() ?? CreateDefaultDiscounts();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading discount data from JSON file");
        }

        // Fallback to hardcoded mock data
        return CreateDefaultDiscounts();
    }

    private List<DiscountPartnerDto> CreateDefaultDiscounts()
    {
        // Fallback hardcoded discounts if JSON loading fails
        return new List<DiscountPartnerDto>
        {
            new DiscountPartnerDto
            {
                Id = 1,
                Title = "20% Off All Nike Products",
                Subtitle = "Limited time offer",
                Description = "Get 20% off all Nike products when you use this exclusive coupon code at checkout.",
                ImageSrc = "https://placehold.co/600x600?text=20%25+Off+All+Nike+Products",
                ExpireDate = "2028-07-15",
                DiscountCode = "NIKE20",
                RedirectLink = "https://www.nike.com?ref=valyrion",
                DiscountType = "Coupon",
                ComingSoon = false,
                IsNew = true,
                ExpiresSoon = false,
                Favored = false
            },
            new DiscountPartnerDto
            {
                Id = 2,
                Title = "Adidas Summer Sale",
                Subtitle = "Up to 50% off selected items",
                Description = "The Adidas summer sale is now on with discounts up to 50% on selected items across the entire range.",
                ImageSrc = "https://placehold.co/600x600?text=Adidas+Summer+Sale",
                ExpireDate = "2024-08-31",
                DiscountCode = null,
                RedirectLink = "https://www.adidas.com/sale?ref=valyrion",
                DiscountType = "General",
                ComingSoon = false,
                IsNew = false,
                ExpiresSoon = false,
                Favored = true
            },
            new DiscountPartnerDto
            {
                Id = 3,
                Title = "Samsung Galaxy Deals",
                Subtitle = "€100 off new Samsung Galaxy phones",
                Description = "Get €100 off when you purchase any new Samsung Galaxy phone with this special promotion code.",
                ImageSrc = "https://placehold.co/600x600?text=Samsung+Galaxy+Deals",
                ExpireDate = "2024-06-10",
                DiscountCode = "GALAXY100",
                RedirectLink = "https://www.samsung.com/mobile/offers?ref=valyrion",
                DiscountType = "Coupon",
                ComingSoon = false,
                IsNew = false,
                ExpiresSoon = true,
                Favored = false
            }
        };
    }

    private List<DiscountCampaignDto> InitializeCampaigns()
    {
        return new List<DiscountCampaignDto>
        {
            new DiscountCampaignDto
            {
                ImageSrc = "https://placehold.co/1200x400?text=Summer+Sale+Campaign",
                QueryName = "summer-sale",
                Title = "Summer Sale"
            },
            new DiscountCampaignDto
            {
                ImageSrc = "https://placehold.co/1200x400?text=Back+to+School",
                QueryName = "back-to-school",
                Title = "Back to School Deals"
            }
        };
    }

    public Task<List<DiscountPartnerDto>> GetAllDiscountsAsync(string partnerId)
    {
        // In a real implementation, you'd filter by partnerId
        // For mock, we'll return all discounts
        return Task.FromResult(_discounts);
    }

    public Task<List<DiscountPartnerDto>> GetDiscountsByPageAsync(string email, int page, int size, string campaignQueryName = null)
    {
        // Filter by campaign if specified
        var filteredDiscounts = string.IsNullOrEmpty(campaignQueryName)
            ? _discounts
            : _discounts.Where(d => d.Id % 2 == 0).ToList(); // Mock campaign filtering

        // Check for favored items for this user
        if (_favoritesByEmail.TryGetValue(email, out var favorites))
        {
            foreach (var discount in filteredDiscounts)
            {
                discount.Favored = favorites.Contains(discount.Id);
            }
        }

        // Apply pagination
        var paginatedDiscounts = filteredDiscounts
            .Skip((page - 1) * size)
            .Take(size)
            .ToList();

        return Task.FromResult(paginatedDiscounts);
    }

    public Task<DiscountDto> GetDiscountByIdAsync(string email, long discountId, bool isDevRequest = false)
    {
        var discount = _discounts.FirstOrDefault(d => d.Id == discountId);
        
        if (discount == null)
        {
            return Task.FromResult<DiscountDto>(null);
        }

        // Check if this discount is favored by the user
        if (_favoritesByEmail.TryGetValue(email, out var favorites))
        {
            discount.Favored = favorites.Contains(discount.Id);
        }

        // Convert to DiscountDto
        var discountDto = new DiscountDto
        {
            Id = discount.Id,
            ImageSrc = discount.ImageSrc,
            Title = discount.Title,
            Subtitle = discount.Subtitle,
            Description = discount.Description,
            DiscountType = discount.DiscountType,
            DiscountCode = discount.DiscountCode,
            RedirectLink = discount.RedirectLink,
            ComingSoon = discount.ComingSoon,
            Favored = discount.Favored,
            IsNew = discount.IsNew,
            ExpiresSoon = discount.ExpiresSoon,
            IsSubscribed = false // Default value
        };

        // Parse the string date to DateOnly
        if (DateOnly.TryParse(discount.ExpireDate, out var expireDate))
        {
            discountDto.ExpireDate = expireDate;
        }
        else
        {
            // Default to today if parsing fails
            discountDto.ExpireDate = DateOnly.FromDateTime(DateTime.Today);
        }

        return Task.FromResult(discountDto);
    }

    public Task<List<DiscountPartnerDto>> GetFavoredDiscountsByEmailAsync(string email, int page, int size)
    {
        if (!_favoritesByEmail.TryGetValue(email, out var favorites))
        {
            return Task.FromResult(new List<DiscountPartnerDto>());
        }

        var favoredDiscounts = _discounts
            .Where(d => favorites.Contains(d.Id))
            .Skip((page - 1) * size)
            .Take(size)
            .ToList();

        // Make sure the favored flag is set
        foreach (var discount in favoredDiscounts)
        {
            discount.Favored = true;
        }

        return Task.FromResult(favoredDiscounts);
    }

    public Task<DiscountPartnerResponse> ToggleFavoriteAsync(DiscountPartnerFavoriteRequestDto favoriteRequest)
    {
        // Ensure discount exists
        var discount = _discounts.FirstOrDefault(d => d.Id == favoriteRequest.DiscountId);
        if (discount == null)
        {
            return Task.FromResult(new DiscountPartnerResponse
            {
                Success = false,
                Message = "Discount not found"
            });
        }

        // Get or create favorites list for this email
        if (!_favoritesByEmail.TryGetValue(favoriteRequest.CustomerEmail, out var favorites))
        {
            favorites = [];
            _favoritesByEmail[favoriteRequest.CustomerEmail] = favorites;
        }

        // Toggle favorite
        bool isFavorite = favorites.Contains(favoriteRequest.DiscountId);
        if (isFavorite)
        {
            favorites.Remove(favoriteRequest.DiscountId);
            discount.Favored = false;
        }
        else
        {
            favorites.Add(favoriteRequest.DiscountId);
            discount.Favored = true;
        }

        return Task.FromResult(new DiscountPartnerResponse
        {
            Success = true,
            Message = isFavorite ? "Discount removed from favorites" : "Discount added to favorites"
        });
    }

    public Task<DiscountPartnerEventResponse> AddDiscountEventAsync(DiscountPartnerEventDto discountEvent)
    {
        // In a real implementation, you'd store these events
        // For mock, we'll just log and return success
        _logger.LogInformation("Event received: {EventType} for discount(s) {DiscountIds} from {CustomerEmail}", 
            discountEvent.EventType, string.Join(",", discountEvent.DiscountIds), discountEvent.CustomerEmail);

        return Task.FromResult(new DiscountPartnerEventResponse
        {
            Success = true,
            Message = "Event recorded successfully"
        });
    }

    public Task<DiscountCampaignDto> GetDiscountCampaignsPartnerAsync(string lang)
    {
        // Mock implementation - just return the first campaign
        return _campaigns.Count > 0 
            ? Task.FromResult(_campaigns[0]) 
            : Task.FromResult<DiscountCampaignDto>(null);
    }
} 