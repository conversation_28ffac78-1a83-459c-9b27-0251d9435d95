<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <RootNamespace>General_Services</RootNamespace>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
        <PackageReference Include="growthbook-c-sharp" Version="1.0.6" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Automation-Services\Automation-Services.csproj" />
        <ProjectReference Include="..\Campaign-Services\Campaign-Services.csproj" />
        <ProjectReference Include="..\Integration-Services\Integration-Services.csproj" />
        <ProjectReference Include="..\Recommendation-Services\Recommendation-Services.csproj" />
        <ProjectReference Include="..\Scheduler-Services\Scheduler-Services.csproj" />
        <ProjectReference Include="..\Shared\Shared.csproj" />
        <ProjectReference Include="..\Shop-Services\Shop-Services.csproj" />
        <ProjectReference Include="..\Statistics-Services\Statistics-Services.csproj" />
        <ProjectReference Include="..\Partner-Services\Partner-Services.csproj" />
    </ItemGroup>

</Project>