using General_Services.Services.General;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Attributes;
using Shared.Dto;
using ILogger = Serilog.ILogger;

namespace General_Services.Controllers;

[AllowAnonymous]
[Route("[controller]")]
[ApiController]
[PartnerAuthExempt]
public class CacheController(ILogger logger, IGeneralService generalService) : ControllerBase
{
    private const string Secret = "hP2Am%9HMEJmHmR8jm)sX(3qjhRPxb";

    [HttpGet]
    [Route("updateCampaignPagination/{key}")]
    public async Task<IActionResult> UpdateCampaignPagination(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();
            var now = DateTime.Now;
            var firstDayOfMonth = new DateTime(now.Year, now.Month, 1);
            //Update campaigns dashboard
            await generalService.GetCampaignsAsync(new PaginationSearchDto()
            {
                Page = 0,
                Size = 10000,
                SortName = "None",
                SortOrder = "None",
                From = firstDayOfMonth,
                To = now,
                Search = "",
                Type = "Customer",
                Filters = []
            }, true);
            return Ok();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while running UpdateCampaignPagination");
            return Ok();
        }
    }
}