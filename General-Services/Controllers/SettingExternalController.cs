using AutoMapper;
using Marlin_OS_Integration_API.Services.Static;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Models;
using Shared.Services;
using ILogger = Serilog.ILogger;

namespace General_Services.Controllers;

[AllowAnonymous]
[Route("[controller]")]
[ApiController]
public class SettingExternalController(
    ILogger logger,
    Shared.Services.Setting.ISettingService settingService)
    : ControllerBase
{
    [HttpGet]
    [Route("{name}")]
    public async Task<IActionResult> GetSettingExternalAsync(string name)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetSettingExternalAsync"))
            {
                if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                        .Value.ToString()))
                {
                    // TODO - Replace Hardcoded PartnerId with the actual PartnerId
                    var result = await settingService.GetSettingAsync(52876, name).ConfigureAwait(false);
                    return Ok(result);
                }

                return Forbid();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error GetSettingExternalAsync");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [Route("multiple")]
    public async Task<IActionResult> GetSettingsAsync(List<string> settings)
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetSettingsAsync"))
                {
                    var result = await settingService.GetSettingAsync(settings).ConfigureAwait(false);
                    return Ok(result);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error GetSettingsAsync");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("groupId/{groupId}")]
    public async Task<IActionResult> GetSettingExternalGroupAsync(int groupId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetSettingExternalGroupAsync"))
            {
                if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                        .Value.ToString()))
                {
                    var result = await settingService.GetSettingByGroupAsync(groupId).ConfigureAwait(false);
                    return Ok(result);
                }

                return Forbid();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error GetSettingExternalGroupAsync");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }
}