using System.Net;
using Admin_Services.Services.Admin;
using Admin_Services.Services.Invoice;
using Automation_Services.Services.Automation;
using Campaign_Services.Services.Campaign;
using General_Services.Services.Export;
using General_Services.Services.General;
using General_Services.Services.Import;
using GrowthBook;
using Integration.Services.Plugins.Integration;
using Integration.Services.Plugins.ShopifyIntegration;
using Marlin_OS_MerchantSync_API.Services.Plugins.ProductFeedIntegration;
using Merchant_Services.Services.ProductStatus;
using Message_Services.Services.Message;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Nest;
using Partner_Services.Services.PartnerDataSync;
using Scheduler_Services.Services.Scheduler;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Elastic.Elastic;
using Shared.Models.Customer;
using Shared.Services.Cache;
using Shared.Services.MerchantRelevance;
using Shop_Services.Models;
using Shop_Services.Services.Shop;
using Webshop.Webshop;
using ICustomerService = Audience.Services.Audience.ICustomerService;
using ILogger = Serilog.ILogger;
using Recommendation_Services.Services;
using Recommendation_Services;
using Recommendation_Services.Models;
using Shop_Services.Models.ModelsDal.Shop;

namespace General_Services.Controllers;

[AllowAnonymous]
[PartnerAuthExempt]
[Route("[controller]")]
[ApiController]
public class TestController : ControllerBase
{
    private readonly IPartnerDataSyncService _partnerDataSyncService;
    private readonly ILogger _logger;
    private const string Secret = "hP2Am%9HMEJmHmR8jm)sX(3qjhRPxb";
    private readonly IMerchantService _merchantService;
    private readonly IProductFeedIntegrationService _productFeedIntegrationService;
    private readonly ISchedulerService _schedulerService;
    private readonly IIntegrationService _integrationService;
    private readonly IAutomationService _automationService;
    private readonly IGeneralService _generalService;
    private readonly IMerchantRelevanceService _merchantRelevanceService;
    private readonly ICampaignService _campaignService;
    private readonly IAdminService _adminService;
    private readonly Dictionary<string, IPluginService> _pluginService;
    private readonly IConfiguration _configuration;
    private readonly IExportService _exportService;
    private readonly IImportService _importService;
    private readonly ICustomerService _customerService;
    private readonly IElasticService _elasticService;
    private readonly ElasticClient _elasticClient;
    private readonly ICacheService _cacheService;
    private readonly IGrowthBook _growthBook;
    private readonly IMessageService _messageService;
    private readonly IShopService _shopService;
    private readonly IProductStatusService? _productStatusService;
    private readonly IInvoiceService _invoiceService;
    private readonly IOfferRecommendationService _offerRecommendationService;

    public TestController(IPartnerDataSyncService partnerDataSyncService, ILogger logger,
        IMerchantService merchantService, IProductFeedIntegrationService productFeedIntegrationService,
        ISchedulerService schedulerService,
        IAutomationService automationService, ICampaignService campaignService,
        IEnumerable<IPluginService> pluginServices, IAdminService adminService,
        IGeneralService generalService, IConfiguration configuration, IExportService exportService,
        ICustomerService customerService, IImportService importService, IElasticService elasticService,
        IMerchantRelevanceService merchantRelevanceService, ElasticClient elasticClient, ICacheService cacheService, 
        IGrowthBook growthBook, IIntegrationService integrationService, IMessageService messageService, 
        IOfferRecommendationService offerRecommendationService,
        IShopService shopService, IProductStatusService? productStatusService = null, IInvoiceService invoiceService = null)
        {
        _partnerDataSyncService = partnerDataSyncService;
        _logger = logger;
        _merchantService = merchantService;
        _productFeedIntegrationService = productFeedIntegrationService;
        _schedulerService = schedulerService;
        _automationService = automationService;
        _campaignService = campaignService;
        _adminService = adminService;
        _generalService = generalService;
        _configuration = configuration;
        _exportService = exportService;
        _customerService = customerService;
        _importService = importService;
        _elasticService = elasticService;
        _merchantRelevanceService = merchantRelevanceService;
        _elasticClient = elasticClient;
        _cacheService = cacheService;
        _growthBook = growthBook;
        _integrationService = integrationService;
        _messageService = messageService;
        _shopService = shopService;
        _productStatusService = productStatusService;
        _invoiceService = invoiceService;
        _offerRecommendationService = offerRecommendationService;
        _pluginService = pluginServices.ToDictionary(
            service => service.GetType().Name.Replace("OnboardingService", "").ToLower(),
            service => service
        );
    }

    [HttpGet]
    [Route("MF/{key}")]
    public async Task<IActionResult> TestAsync(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CPAsync"))
            {
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error in TestAsync");
            return StatusCode(500, ex.Message);
        }
    }

    [HttpGet]
    [Route("recommendations-demo")]
    public async Task<IActionResult> GetCuratedProductsPriceDrop([FromQuery] string gender, [FromQuery] int? age)
    {
        var recommendations = await _offerRecommendationService.GetRecommendationsAsync(
            RecommendationStrategies.CuratedProductsPriceDrop, gender, age);
        // Fetch all products
        var products = await _merchantService.GetProductByProductIdMultiple(recommendations.SelectMany(r => r.OfferItems.Select(i => i.OfferId)).ToList());
        return Ok(products);
    }
}