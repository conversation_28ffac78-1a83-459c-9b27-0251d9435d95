using Admin_Services.Services.Admin;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Models;
using ILogger = Serilog.ILogger;

namespace General_Services.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class FlaggedOrdersController(ILogger logger, IAdminService adminService) : ControllerBase
{
    [HttpGet]
    public async Task<IActionResult> GetFlaggedOrdersAsync()
    {
        try
        {
            var result = await adminService.GetFlaggedOrdersAsync()
                .ConfigureAwait(false);
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error GetWebShopUserGroupAsync");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("handled/{invoiceLineFlaggedOrdersId}")]
    public async Task<IActionResult> UpdateFlaggedOrdersAsync(int invoiceLineFlaggedOrdersId)
    {
        try
        {
            await adminService.UpdateFlaggedOrdersAsync(invoiceLineFlaggedOrdersId)
                .ConfigureAwait(false);
            return Ok();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error GenerateWebshopAsset");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }
}