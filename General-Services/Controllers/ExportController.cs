using Audience.Services.Audience;
using General_Services.Services.Export;
using Marlin_OS_Integration_API.Services.Static;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Nest;
using SerilogTimings.Extensions;
using Shared.Services;
using StackExchange.Redis;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace General_Services.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class ExportController : ControllerBase
{
    private readonly ILogger _logger;
    private readonly IExportService _exportService;

    public ExportController(ILogger logger, IExportService exportService)
    {
        _logger = logger;
        _exportService = exportService;
    }

    //List with bought at for the last 2500 days and gender in each column
    [HttpGet]
    [Route("exportBoughtAtGender")]
    public async Task<IActionResult> ExportBoughtAtGender()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ExportBoughtAtGender"))
            {
                return Ok(await _exportService.ExportBoughtAtGender());
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error ExportBoughtAtGender");
            return BadRequest(ex.ToString());
        }
    }

    //List with invoiceLines gender
    [HttpGet]
    [Route("exportInvoiceLines")]
    public async Task<IActionResult> ExportInvoiceLines()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ExportBoughtAtGender"))
            {
                return Ok(await _exportService.ExportInvoiceLinesGender());
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error ExportBoughtAtGender");
            return BadRequest(ex.ToString());
        }
    }

    //Invoice with discount overview instead of clicking in
    [HttpGet]
    [Route("testExportInvoice")]
    public async Task<IActionResult> TestExportInvoice()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "TestExportInvoice"))
            {
                return Ok(await _exportService.TestExportInvoice());
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error TestExportInvoice");
            return BadRequest(ex.ToString());
        }
    }

    [HttpGet]
    [Route("CategoryCodes")]
    public async Task<IActionResult> CategoryCodes()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "TestExportInvoice"))
            {
                return Ok(await _exportService.CategoryCodes());
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error TestExportInvoice");
            return BadRequest(ex.ToString());
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("exportMarcus/{typeId}")]
    public async Task<IActionResult> ExportMarcus(int typeId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "LeadsPotentialLines"))
            {
                return Ok("Disabled");
                
                await _exportService.ExportMarcus(typeId);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error LeadsPotentialLines");
            return BadRequest(ex.ToString());
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("exportGenderAgeSegments")]
    public async Task<IActionResult> ExportGenderAgeSegments()
    {
        try
        {
            if (Validate.ValidateInternalExportKey(Request.Headers
                    .FirstOrDefault(a => a.Key.Equals("apikey", StringComparison.CurrentCultureIgnoreCase))
                    .Value.ToString()))
            {
                using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "ExportGenderAgeSegments"))
                {
                    return Ok(await _exportService.ExportGenderAgeSegments());
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error ExportGenderAgeSegments");
            return BadRequest(ex.ToString());
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("exportEmailOrderGenderAgeSegments")]
    public async Task<IActionResult> ExportEmailOrderGenderAgeSegments()
    {
        try
        {
            // Temporary Disabled to see if being used
            return Ok("Disabled");
            if (Validate.ValidateInternalExportKey(Request.Headers
                    .FirstOrDefault(a => a.Key.Equals("apikey", StringComparison.CurrentCultureIgnoreCase))
                    .Value.ToString()))
            {
                using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "ExportGenderAgeSegments"))
                {
                    var result = await _exportService.ExportEmailOrderGenderAgeSegments();
                    return Ok(result);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error ExportGenderAgeSegments");
            return BadRequest(ex.ToString());
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("exportActiveMerchantIds")]
    public async Task<IActionResult> ExportActiveMerchantIds()
    {
        try
        {
            if (Validate.ValidateInternalExportKey(Request.Headers
                    .FirstOrDefault(a => a.Key.Equals("apikey", StringComparison.CurrentCultureIgnoreCase))
                    .Value.ToString()))
            {
                using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "ExportActiveMerchantIds"))
                {
                    return Ok(await _exportService.ExportActiveMerchantIds());
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error ExportActiveMerchantIds");
            return BadRequest(ex.ToString());
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("exportVirk")]
    public async Task<IActionResult> ExportVirk()
    {
        try
        {
            if (Validate.ValidateInternalExportKey(Request.Headers
                    .FirstOrDefault(a => a.Key.Equals("apikey", StringComparison.CurrentCultureIgnoreCase))
                    .Value.ToString()))
            {
                using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "ExportActiveMerchantIds"))
                {
                    return Ok(await _exportService.ExportVirk());
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error ExportActiveMerchantIds");
            return BadRequest(ex.ToString());
        }
    }
}