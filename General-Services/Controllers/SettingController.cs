using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Dto.Setting;
using Shared.Models;
using Shared.Models.ModelsDal.Setting;
using ILogger = Serilog.ILogger;

namespace General_Services.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class SettingController(
    ILogger logger,
    Shared.Services.Setting.ISettingService settingService,
    IMapper mapperGlobal)
    : ControllerBase
{
    [HttpGet]
    public async Task<IActionResult> GetSettingsAsync([FromHeader] int partnerId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetSettingsAsync"))
            {
                var result = await settingService.GetSettingsAsync(partnerId).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error GetSettingsAsync");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("{name}")]
    public async Task<IActionResult> GetSettingAsync([FromHeader]int partnerId, string name)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetSettingAsync"))
            {
                var result = await settingService.GetSettingAsync(partnerId, name).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error GetSettingAsync");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpPut]
    public async Task<IActionResult> UpdateSettingAsync([FromHeader] int partnerId, SettingDto settingDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateSettingAsync"))
            {
                var setting = mapperGlobal.Map<SettingDto, Setting>(settingDto);
                setting.FkPartnerId = partnerId;
                var result = await settingService.UpdateSettingsAsync(setting).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error UpdateSettingAsync");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }
}