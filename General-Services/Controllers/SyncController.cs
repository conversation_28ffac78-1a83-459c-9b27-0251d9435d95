using Admin_Services.Services.Admin;
using Automation_Services.Services.Automation;
using Campaign_Services.Services.Campaign;
using Discount_Services.Services.Discounts;
using General_Services.Services.Export;
using General_Services.Services.General;
using General_Services.Services.Import;
using Integration.Services.Plugins.ShopifyIntegration;
using Marlin_OS_MerchantSync_API.Services.Plugins.ProductFeedIntegration;
using Merchant_Services.Models.ModelsDal.Merchant;
using Merchant_Services.Services.ProductStatus;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Partner_Services.Services.PartnerDataSync;
using Scheduler_Services.Services.Scheduler;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Dto;
using Shared.Models;
using Shared.Services;
using Shared.Services.Partner;
using Shop_Services.Services.Shop;
using Shop_Services.Services.ShopInternal;
using Webshop.Webshop;
using Webshop_Services.Services.Elastic;
using ILogger = Serilog.ILogger;
using IPartnerService = Partner_Services.Services.General.IPartnerService;

namespace General_Services.Controllers;

[AllowAnonymous]
[Route("[controller]")]
[ApiController]
[PartnerAuthExempt]
public class SyncController : ControllerBase
{
    private readonly IPartnerDataSyncService _partnerDataSyncService;
    private readonly ILogger _logger;
    private const string Secret = "hP2Am%9HMEJmHmR8jm)sX(3qjhRPxb";
    private readonly IMerchantService _merchantService;
    private readonly IProductFeedIntegrationService _productFeedIntegrationService;
    private readonly ISchedulerService _schedulerService;
    private readonly IAutomationService _automationService;
    private readonly IGeneralService _generalService;
    private readonly ICampaignService _campaignService;
    private readonly IDiscountService _discountService;
    private readonly IAdminService _adminService;
    private readonly Dictionary<string, IPluginService> _pluginService;
    private readonly IConfiguration _configuration;
    private readonly IExportService _exportService;
    private readonly IImportService _importService;
    private readonly IShopService _shopService;
    private readonly IPartnerService _partnerService;
    private readonly IPartnerContext _partnerContext;
    private readonly IShopInternalService _shopInternalService;
    private readonly IMerchantElasticSync _merchantElasticSync;

    public SyncController(IPartnerDataSyncService partnerDataSyncService, ILogger logger,
        IMerchantService merchantService, IProductFeedIntegrationService productFeedIntegrationService,
        ISchedulerService schedulerService,
        IAutomationService automationService, ICampaignService campaignService,
        IEnumerable<IPluginService> pluginServices, IAdminService adminService,
        IGeneralService generalService, IConfiguration configuration, IExportService exportService,
        IImportService importService, IDiscountService discountService, IShopService shopService, IPartnerService partnerService, 
        IPartnerContext partnerContext, IShopInternalService shopInternalService,
        IMerchantElasticSync merchantElasticSync)
    {
        _partnerDataSyncService = partnerDataSyncService;
        _logger = logger;
        _merchantService = merchantService;
        _productFeedIntegrationService = productFeedIntegrationService;
        _schedulerService = schedulerService;
        _automationService = automationService;
        _campaignService = campaignService;
        _adminService = adminService;
        _generalService = generalService;
        _configuration = configuration;
        _exportService = exportService;
        _importService = importService;
        _discountService = discountService;
        _shopService = shopService;
        _shopInternalService = shopInternalService;
        _partnerService = partnerService;
        _partnerContext = partnerContext;
        _merchantElasticSync = merchantElasticSync;
        _pluginService = pluginServices.ToDictionary(
            service => service.GetType().Name.Replace("OnboardingService", "").ToLower(),
            service => service
        );
    }

    //Sync partner data
    [HttpGet]
    [Route("partnerData/{key}")]
    public async Task<IActionResult> SyncDataAsync(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "SyncDataAsync"))
            {
                await _partnerDataSyncService.SyncDataAsync();
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while syncing data from partner");
            return BadRequest(ex.ToString());
        }
    }

    [HttpGet]
    [Route("webshopUpdate/{key}/{merchantId}/{lookBackDaysOrder}/{lookBackDaysProduct}/{fromTimer?}")]
    public async Task<IActionResult> WebShopUpdate(string key, int merchantId = 0, int lookBackDaysOrder = 0,
        int lookBackDaysProduct = 0, bool fromTimer = false)
    {
        var onlyNew = false;
        //Handle request from logic app so the merchant sync is only at night
        if (fromTimer)
        {
            const int startClosing = 7;
            const int endClosing = 23;
            var hour = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(DateTime.UtcNow, "Romance Standard Time").Hour;

            if (hour is > startClosing and < endClosing)
            {
                onlyNew = true;
            }
        }

        var shopsUpdated = "";
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "WebShopUpdate"))
            {
                var merchants = new List<Merchant>();
                if (merchantId == 0)
                {
                    merchants = await _merchantService
                        .GetMerchantsReadyForUpdate(lookBackDaysOrder, lookBackDaysProduct, onlyNew)
                        .ConfigureAwait(false);
                }
                else
                {
                    var merchant = await _merchantService.GetByIdReadyForUpdate(merchantId);
                    if (merchant != null)
                    {
                        merchants.Add(merchant);
                    }
                }

                var i = 0;
                var count = merchants.Count;

                foreach (var merchant in merchants)
                {
                    i++;
                    try
                    {
                        Console.WriteLine();
                        Console.WriteLine($"Syncing {merchant.Name}: Number {i} of {count}");
                        var start = DateTime.UtcNow;
                        if (merchant.LastOrderSync == null)
                        {
                            lookBackDaysOrder = StaticVariables.FirstSyncOrderLookBack;
                        }

                        if (merchant.LastProductSync == null)
                        {
                            lookBackDaysProduct = StaticVariables.FirstSyncProductLookBack;
                        }

                        //Fix circular dependency
                        foreach (var webshopsSetting in merchant.MerchantMeta)
                        {
                            webshopsSetting.FkMerchant = null;
                        }

                        foreach (var webshopsPayment in merchant.MerchantPayments)
                        {
                            webshopsPayment.FkMerchant = null;
                        }

                        var cms = _pluginService.FirstOrDefault(a =>
                            a.Key.Contains(merchant.Type.ToLower() + "pluginservice", StringComparison.CurrentCultureIgnoreCase)).Value;
                        if (cms != null)
                        {
                            // Deactivate all Merchants products before updating to remove old products
                            //await _merchantService.DeactivateAllMerchantProducts(merchant.Id);
                            
                            var partner = await _partnerDataSyncService.GetPartnerByIdAsync(merchant.FkPartnerId).ConfigureAwait(false);

                            await cms.UpdateAsync(merchant, partner, lookBackDaysOrder, lookBackDaysProduct)
                                .ConfigureAwait(false);
                        }
                        else
                        {
                            _logger.ForContext("service_name", GetType().Name)
                                .Error("Error while getting cms to be updated for type {Type}", merchant.Type);
                        }

                        shopsUpdated +=
                            $" | {merchant.Name} Product: {lookBackDaysProduct} days Order: {lookBackDaysOrder} days RunTime: {(DateTime.UtcNow - start).TotalMinutes} Min";
                    }
                    catch (Exception ex)
                    {
                        //Check if merchant have failed over x days then ignores errors
                        if (merchant.LastOrderSync.HasValue && merchant.LastOrderSync.Value >=
                            StaticVariables.StopLoggingAfterSyncError() ||
                            merchant.LastProductSync.HasValue && merchant.LastProductSync.Value >=
                            StaticVariables.StopLoggingAfterSyncError())
                        {
                            _logger.ForContext("service_name", GetType().Name)
                                .Error(ex, "Error while updating Merchant {MerchantName} Id: {MerchantId}",
                                    merchant.Name, merchant.Id);
                        }
                    }
                }

                return Ok(new ResponseDto
                {
                    Success = true,
                    Message = $"Successfully updated {shopsUpdated}"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while updating WebShopUpdate");
            return BadRequest();
        }
    }

    [HttpGet]
    [Route("productFeed/{key}/{fromTimer?}")]
    [PartnerAuthExempt]
    public async Task<IActionResult> ProductFeed(string key, bool fromTimer = false)
    {
        try
        {
            if (key != Secret) return Unauthorized();
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "productFeed"))
            {
                var onlyNew = false;
                if (fromTimer)
                {
                    var startClosing = 7;
                    var endClosing = 23;
                    var hour = TimeZoneInfo.ConvertTimeBySystemTimeZoneId(DateTime.UtcNow, "Romance Standard Time")
                        .Hour;

                    if (hour > startClosing && hour < endClosing)
                    {
                        onlyNew = true;
                    }
                }

                if (!onlyNew)
                {
                    return Ok(await _productFeedIntegrationService.HandleProductFeeds());
                }
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while updating productFeed");
            return Ok();
        }

        return Ok();
    }

    //Disabled pt
    /*[HttpGet]
    [Route("greyNumber/{key}")]
    public async Task<IActionResult> ElasticGreyNumberAsync(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ElasticGreyNumberAsync"))
            {
                var success = await _schedulerService.GreyNumber();
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while sync ElasticGreyNumberAsync");
            return BadRequest(ex.ToString());
        }
    }*/

    //Minify js files for faster loading on merchants sites
    [HttpGet]
    [Route("pluginJSMinify/{key}")]
    public async Task<IActionResult> PluginJsMinifyAsync(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PluginJsMinifyAsync"))
            {
                var success = await _schedulerService.MinifyJavascript();
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while sync PluginJsMinifyAsync");
            return BadRequest(ex.ToString());
        }
    }

    [HttpGet]
    [Route("UnengagedContacts/{key}")]
    public async Task<IActionResult> CalculateUnengagedContactsAsync(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CalculateUnengagedContactsAsync"))
            {
                return Ok(await _schedulerService.CalculateEngagementLevels());
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while updating CalculateUnengagedContactsAsync");
            return Ok();
        }
    }

    //Check gmail account for unsubscribes
    [HttpGet]
    [Route("UnsubscribeContacts/{key}")]
    public async Task<IActionResult> UnsubscribeContactsFromEmailAsync(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UnsubscribeContactsFromEmailAsync"))
            {
                return Ok(await _schedulerService.EmailContactUnsubscribe());
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while updating CalculateUnengagedContactsAsync");
            return Ok();
        }
    }

    // Retargeting mails
    [HttpGet]
    [Route("Retargeting/{key}")]
    public async Task<IActionResult> RetargetingCampaignAsync(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "RetargetingCampaignAsync"))
            {
                await _automationService.Run().ConfigureAwait(false);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while updating RetargetingCampaignAsync");
            return Ok();
        }
    }

    [HttpGet]
    [Route("Retargeting/test/{key}/{recipient}/{campaignId:int}/{InternalProductId}")]
    public async Task<IActionResult> RetargetingCampaignAsync(string key, string recipient, int campaignId,
        string internalProductId = "")
    {
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "RetargetingTestCampaignAsync"))
            {
                await _automationService.RunTest(recipient, campaignId, internalProductId).ConfigureAwait(false);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while running RetargetingTestCampaignAsync");
            return Ok();
        }
    }

    [HttpGet]
    [Route("CampaignTesting/{key}")]
    [PartnerAuthExempt]
    public async Task<IActionResult> CampaignTesting(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CampaignTesting"))
            {
                //Check if any campaignTesting
                await _campaignService.CampaignTesting();
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while CampaignTesting");
            return Ok();
        }
    }

    //Every half hour


    //Update merchants dashboard
    [HttpGet]
    [Route("UpdateCacheDataHalfHour/{key}")]
    public async Task<IActionResult> UpdateCacheDataHalfHour(string key)
    {
        if (key != Secret) return Unauthorized(); 
        var now = DateTime.UtcNow;
        var firstDayOfMonth = new DateTime(now.Year, now.Month, 1);
        try
        {
            var partners = await _partnerService.GetAllAsync();
            foreach (var partner in partners)
            {
                _partnerContext.SetPartnerId(partner.Id);
                
                await _generalService.GetMerchantsAsync(new PaginationSearchDto()
                {
                    Page = 0,
                    Size = 10000,
                    SortName = "None",
                    SortOrder = "None",
                    From = firstDayOfMonth,
                    To = now,
                    Search = "",
                    Filters = []
                }, true);
            }
            

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while running UpdateCacheDataHalfHour");
            return Ok();
        }
    }

    //Every hour
    [HttpGet]
    [Route("UpdateCacheData/{key}")]
    public async Task<IActionResult> UpdateCacheData(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();
            var now = DateTime.UtcNow;
            var lastMonth = DateTime.UtcNow.AddMonths(-1);

            var firstDayOfMonth = new DateTime(now.Year, now.Month, 1);
            var firstDayOfLastMonth = new DateTime(now.Year, now.Month, 01).AddMonths(-1);
            var lastDayOfLastMonth = new DateTime(now.Year, now.Month, 01).AddMilliseconds(-1);

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateCacheData"))
            {
                var partners = await _partnerService.GetAllAsync();
                //Only update for prod
                if (_configuration["Environment"] == "prod")
                {
                    foreach (var partner in partners.OrderByDescending(a => a.Id))
                    {
                        _partnerContext.SetPartnerId(partner.Id);
                        //Update main dashboard
                        await _generalService.GetDashboardProfileAsync(true, now.Year, now.Month);
                        await _generalService.GetDashboardProfileAsync(true, lastMonth.Year, lastMonth.Month); 
                        
                        //Update merchants dashboard for last month
                        await _generalService.GetMerchantsAsync(new PaginationSearchDto()
                        {
                            Page = 0,
                            Size = 10000,
                            SortName = "None",
                            SortOrder = "None",
                            From = firstDayOfLastMonth,
                            To = lastDayOfLastMonth,
                            Search = "",
                            Filters = []
                        }, true);

                        //Update campaigns dashboard
                        await _generalService.GetCampaignsAsync(new PaginationSearchDto()
                        {
                            Page = 0,
                            Size = 10000,
                            SortName = "None",
                            SortOrder = "None",
                            From = firstDayOfMonth,
                            To = now,
                            Search = "",
                            Type = "Customer",
                            Filters = []
                        }, true);
                    }
                    
                    //Update customer meta
                    //await _generalService.UpdateCustomerMeta();
                }
                //Update Merchant Dashboard Orders - All
                //await _customerService.GetCustomerExposureMetrics();
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while running UpdateCacheData");
            return Ok();
        }
    }

    //2024-05-31 runs at 6
    [HttpGet]
    [Route("UpdateCache/{key}/{days}")]
    public async Task<IActionResult> UpdateCacheDataEveryDay(string key, int days)
    {
        try
        {
            if (key != Secret) return Unauthorized();
            var now = DateTime.UtcNow;
            var lastMonth = DateTime.UtcNow.AddMonths(-1);

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateCacheDay" + days))
            {
                switch (days)
                {
                    case 1:
                        await _generalService.TopProductsForMerchants();
                        // ML is not in use right now
                        //await _importService.ImportMl();
                        break;
                    case 2:

                        break;
                    case 3:
                        // not in use right now
                        //await _exportService.ExportEmailOrderGenderAgeSegments(true);
                        break;
                }
                
                var partners = await _partnerService.GetAllAsync();
                
                foreach (var partner in partners)
                {
                    _partnerContext.SetPartnerId(partner.Id);

                    switch (days)
                    {
                        case 1:
                            await _generalService.UpdateOpenRate();
                            var merchants = await _merchantService.HandleIsMarketingAllowed();
                            await _discountService.HandleActivatedDiscounts(merchants);
                            break;
                        case 2:

                            break;
                        case 3:
                            // not in use right now
                            //await _exportService.ExportEmailOrderGenderAgeSegments(true);
                            break;
                    }
                }

                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while running UpdateCacheData");
            return Ok();
        }
    }

    /*[HttpGet]
    [Route("MailQueueExpire/{key}")]
    public async Task<IActionResult> MailQueueExpire(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "MailQueueExpire"))
            {
                await _messageService.MailQueueExpire();
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while running MailQueueExpire");
            return Ok();
        }
    }*/

    //Check if any new flagged orders have come or have been resolved
    [HttpGet]
    [Route("FlaggedOrders/{key}")]
    public async Task<IActionResult> FlaggedOrders(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "FlaggedOrders"))
            {
                await _adminService.FlaggedOrdersUpdateAsync();
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while running FlaggedOrders");
            return Ok();
        }
    }

    //Run stored procedure to defragment sql
    [HttpGet]
    [Route("Defragmentation/{key}")]
    public async Task<IActionResult> Defragmentation(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "Defragmentation"))
            {
                return Ok(await _merchantService.Defragmentation());
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while running Defragmentation");
            return Ok();
        }
    }

    // Calculates the relevance score for all merchants and save it to the database for all age and genders
    [HttpGet]
    [Route("CalculateRelevanceScore/{key}")]
    public async Task<IActionResult> CalculateRelevanceScore(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CalculateRelevanceScore"))
            {
                await _merchantService.CalculateActualPotentialLinesAsync();
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while running CalculateRelevanceScore");
            return Ok();
        }
    }
    
    [HttpGet]
    [Route("ValidateSectionProductsValidity/{key}")]
    public async Task<IActionResult> ValidateSectionProductsValidity(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ValidateSectionProductsValidity"))
            {
                await _shopInternalService.ValidateSectionProductsValidity();
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while running ValidateSectionProductsValidity");
            return Ok();
        }
    }
    
    // Temporary endpoint to sync products from SQL to Elastic
    [HttpGet]
    [Route("SyncProductsFromSqlToElastic/{key}")]
    public async Task<IActionResult> SyncProductsFromSqlToElastic(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "SyncProductsFromSqlToElastic"))
            {
                await _merchantElasticSync.SyncInactiveProductsFromSqlToElastic();
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while running SyncProductsFromSqlToElastic");
            return Ok();
        }
    }

    // IP and Domain Warmup Automation
    [HttpGet]
    [Route("IPAndDomainWarmupAutomation/{key}/{partnerId}")]
    public async Task<IActionResult> IPAndDomainWarmupAutomation(string key, int partnerId)
    {
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "IPAndDomainWarmupAutomation"))
            {
                //await _generalService.IPAndDomainWarmupAutomation(partnerId);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while running IPAndDomainWarmupAutomation");
            return Ok();
        }
    }

    // Pre-calculate Campaign Summary for all partners
    [HttpGet]
    [Route("PreCalculateCampaignSummary/{key}")]
    public async Task<IActionResult> PreCalculateCampaignSummary(string key)
    {
        try
        {
            if (key != Secret) return Unauthorized();

            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PreCalculateCampaignSummary"))
            {
                var partners = await _partnerService.GetAllAsync();
                var successCount = 0;
                var errorCount = 0;

                foreach (var partner in partners)
                {
                    try
                    {
                        _partnerContext.SetPartnerId(partner.Id);
                        await _generalService.PreCalculateCampaignSummaryAsync();
                        successCount++;
                        _logger.Information("Successfully pre-calculated campaign summary for partner {PartnerId}", partner.Id);
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        _logger.Error(ex, "Failed to pre-calculate campaign summary for partner {PartnerId}", partner.Id);
                    }
                }

                _logger.Information("Pre-calculation completed. Success: {SuccessCount}, Errors: {ErrorCount}", successCount, errorCount);
                return Ok(new { Success = successCount, Errors = errorCount });
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while running PreCalculateCampaignSummary");
            return Ok();
        }
    }
}