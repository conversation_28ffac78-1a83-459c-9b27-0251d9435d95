using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Models;
using Statistics_Services.Services.Statistics;
using ILogger = Serilog.ILogger;

namespace General_Services.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class StatisticsController : ControllerBase
{
    private readonly ILogger _logger;
    private readonly IStatisticsService _statisticsService;

    public StatisticsController(ILogger logger, IStatisticsService statisticsService)
    {
        _logger = logger;
        _statisticsService = statisticsService;
    }

    //Valyrion app campaign performance 
    [HttpGet]
    [Route("performanceDashboard/{campaignId}")]
    public async Task<IActionResult> GetInvoicesAsync(int campaignId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetInvoicesAsync"))
            {
                var success = await _statisticsService.PerformanceDashboard(campaignId)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving invoices");
            return Ok();
        }
    }

    //Valyrion app campaign top boxes performance stats
    [HttpGet]
    [Route("generalQueueStats/{lookBackDays:int}")]
    public async Task<IActionResult> GetGeneralQueueStats(int lookBackDays)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetGeneralQueueStats"))
            {
                var result = await _statisticsService.GeneralQueueStats(lookBackDays)
                    .ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error getting GetGeneralQueueStats for {Days} back",
                lookBackDays);
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    //Valyrion app discount performance stats on open
    [HttpGet]
    [Route("discount/{discountId}")]
    public async Task<IActionResult> GetStatisticsAsync(int discountId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetStatisticsAsync"))
            {
                var success = await _statisticsService.GetDiscountStatistics(discountId)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving discounts statistics");
            return BadRequest();
        }
    }
}