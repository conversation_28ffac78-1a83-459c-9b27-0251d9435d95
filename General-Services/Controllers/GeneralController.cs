using AutoMapper;
using General_Services.Models.Models;
using General_Services.Models.ModelsDal.Valyrion;
using General_Services.Services.General;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Dto;
using Shared.Models;
using ILogger = Serilog.ILogger;
using UserDto = General_Services.Models.Models.UserDto;

namespace General_Services.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class GeneralController : ControllerBase
{
    private readonly ILogger _logger;
    private readonly IMapper _mapper;
    private readonly IGeneralService _generalService;

    public GeneralController(ILogger logger, IGeneralService generalService)
    {
        _logger = logger;
        _generalService = generalService;
        var config = new MapperConfiguration(cfg =>
        {
            //Webshop
            cfg.CreateMap<Permission, PermissionDto>().ReverseMap();
            cfg.CreateMap<User, UserDto>().ReverseMap();
            cfg.CreateMap<UserPermissionsRel, UserPermissionsRelDto>().ReverseMap();
        });
        _mapper = config.CreateMapper();
    }

    ////////////////////////////////////////////////////////////////////////////////
    // Dashboard
    ////////////////////////////////////////////////////////////////////////////////
    [HttpGet]
    [Route("dashboard/{year}/{month}")]
    public async Task<IActionResult> DashboardProfile(int year, int month)
    {
        try
        {
            return Ok(await _generalService.GetDashboardProfileAsync(false, year, month));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while getting DashboardProfile Year: {Year} Month: {Month}", year, month);
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    // New Endpoint for Dashboard with date range 
    [HttpGet]
    [Route("dashboard/period/{from}/{to}")]
    public async Task<IActionResult> DashboardProfilePeriod(DateTime from, DateTime to)
    {
        try
        {
            return Ok(await _generalService.GetDashboardProfilePeriodAsync(from, to));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while getting DashboardProfilePeriod From: {From} To: {To}", from, to);
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    ////////////////////////////////////////////////////////////////////////////////
    // Merchants Pagniation
    ////////////////////////////////////////////////////////////////////////////////
    [HttpPost]
    [Route("merchants/pagination")]
    public async Task<IActionResult> Merchants(PaginationSearchDto paginationSearchDto)
    {
        try
        {
            return Ok(await _generalService.GetMerchantsAsync(paginationSearchDto));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting Merchants");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    ////////////////////////////////////////////////////////////////////////////////
    // Merchants export
    ////////////////////////////////////////////////////////////////////////////////
    [HttpPost]
    [Route("merchants/export")]
    public async Task<IActionResult> MerchantsExport(PaginationSearchDto paginationSearchDto)
    {
        try
        {
            return Ok(await _generalService.ExportMerchantsAsync(paginationSearchDto));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting MerchantsExport");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }
    ////////////////////////////////////////////////////////////////////////////////
    // Merchants Summary
    ////////////////////////////////////////////////////////////////////////////////
    [HttpGet]
    [Route("merchants/summary")]
    public async Task<IActionResult> MerchantsSummary()
    {
        try
        {
            return Ok(await _generalService.GetMerchantsSummaryAsync());
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting MerchantsSummary");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }


    ////////////////////////////////////////////////////////////////////////////////
    // Campaigns
    ////////////////////////////////////////////////////////////////////////////////
    [HttpPost]
    [Route("campaigns/pagination")]
    public async Task<IActionResult> Campaigns(PaginationSearchDto paginationSearchDto)
    {
        try
        {
            return Ok(await _generalService.GetCampaignsAsync(paginationSearchDto));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting Campaigns");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("campaigns/queued")]
    public async Task<IActionResult> CampaignsQueued()
    {
        try
        {
            return Ok(await _generalService.GetCampaignsQueuedAsync());
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting CampaignsQueued");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("campaigns/status/{from}/{to}")]
    public async Task<IActionResult> CampaignsStatus(DateTime from, DateTime to)
    {
        try
        {
            return Ok(await _generalService.GetCampaignsStatusAsync(from, to));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting CampaignsStatus");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("campaigns/summary")]
    public async Task<IActionResult> CampaignsSummary()
    {
        try
        {
            return Ok(await _generalService.GetCampaignSummaryAsync());
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting CampaignSummary");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    ////////////////////////////////////////////////////////////////////////////////
    // Campaigns export
    ////////////////////////////////////////////////////////////////////////////////
    [HttpPost]
    [Route("campaigns/export")]
    public async Task<IActionResult> CampaignsExport(PaginationSearchDto paginationSearchDto)
    {
        try
        {
            return Ok(await _generalService.ExportCampaignAsync(paginationSearchDto));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting CampaignsExport");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    ////////////////////////////////////////////////////////////////////////////////
    // Customer Recommendations
    ////////////////////////////////////////////////////////////////////////////////

    [HttpGet]
    [Route("customer/recommendations/products")]
    public async Task<IActionResult> GetProductRecommendations([FromQuery] string? email, [FromQuery] string strategy, [FromQuery] string? age, [FromQuery] string? gender)
    {
        try
        {
            return Ok(await _generalService.GetProductRecommendationsByStrategy(strategy, email, age, gender));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting ProductRecommendations");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    ////////////////////////////////////////////////////////////////////////////////
    // Send Feedback
    ////////////////////////////////////////////////////////////////////////////////
    [HttpPost]
    [Route("partner/feedback")]
    public async Task<IActionResult> SendFeedback([FromBody] FeedbackDto feedbackDto)
    {
        try
        {
            return Ok(await _generalService.SendFeedbackAsync(feedbackDto.Email, feedbackDto.Feedback));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while sending feedback");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }
}