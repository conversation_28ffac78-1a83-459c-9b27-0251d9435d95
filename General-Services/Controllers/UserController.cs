using System.Net;
using AutoMapper;
using General_Services.Models.Models;
using General_Services.Models.ModelsDal.Valyrion;
using General_Services.Services.Users;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Dto.General;
using Shared.Models;
using Shared.Models.Merchant;
using ILogger = Serilog.ILogger;
using User = Partner_Services.Models.ModelsDal.Partner.User;
using UserPermissionsRel = Partner_Services.Models.ModelsDal.Partner.UserPermissionsRel;
using Permission = Partner_Services.Models.ModelsDal.Partner.Permission;
using UserDto = General_Services.Models.Models.UserDto;
using IPartnerService = Partner_Services.Services.General.IPartnerService;

namespace General_Services.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class UserController : ControllerBase
{
    private readonly ILogger _logger;
    private readonly IMapper _mapper;
    private readonly IGeneralUserService _generalUserService;
    private readonly IPartnerService _partnerService;

    public UserController(ILogger logger, IGeneralUserService generalUserService, IPartnerService partnerService)
    {
        _logger = logger;
        _generalUserService = generalUserService;
        _partnerService = partnerService;
        var config = new MapperConfiguration(cfg =>
        {
            // Merchant
            cfg.CreateMap<Permission, PermissionDto>().ReverseMap();
            cfg.CreateMap<User, UserDto>().ReverseMap();
            cfg.CreateMap<UserPermissionsRel, UserPermissionsRelDto>().ReverseMap();
        });
        _mapper = config.CreateMapper();
    }

    [HttpGet]
    [Route("permissions")]
    public async Task<IActionResult> GetPermissionsAsync()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetPermissionsAsync"))
            {
                var permissions = await _partnerService.GetPermissionsAsync().ConfigureAwait(false);
                return Ok(permissions);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error GetImagesAsync");
            return BadRequest(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("users")]
    public async Task<IActionResult> GetUsersAsync()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetUsersAsync"))
            {
                var users = await _partnerService.GetUsersAsync().ConfigureAwait(false);
                return Ok(users);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error Getting Users");
            return BadRequest(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("users/{userId:int}")]
    public async Task<IActionResult> GetUserAsync(int userId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetUserAsync"))
            {
                var user = await _partnerService.GetUserByIdAsync(userId).ConfigureAwait(false);
                return Ok(_mapper.Map<User, UserDto>(user));
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error GetImagesAsync");
            return BadRequest(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    public async Task<IActionResult> CreateUserAsync(UserDto userDto)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CreateUserAsync"))
            {
                foreach (var permissionsRel in userDto.UserPermissionsRels)
                {
                    permissionsRel.FkPermission = null;
                }
                
                var mappedUser = _mapper.Map<UserDto, User>(userDto);
                var userKeyValue = await _partnerService.CreateUserAsync(mappedUser).ConfigureAwait(false);
                
                
                if (userKeyValue.Value != null)
                {
                    var result = await _generalUserService.SendNewUserEmail(userKeyValue.Value.Id, userKeyValue.Value.Email, userKeyValue.Key);
                    if (!result.IsSuccessStatusCode)
                    {
                        _logger.ForContext("service_name", GetType().Name)
                            .Error($"Error while sending email to user {userDto.Email}. Reason: {result.ReasonPhrase}, Code: {result.StatusCode}");
                        return BadRequest(result.ReasonPhrase);
                    }
                }
                
                return Ok(userKeyValue.Value);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error CreateUserAsync");
            return BadRequest(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPut]
    public async Task<IActionResult> UpdateUserAsync(UserDto userDto)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateUserAsync"))
            {
                foreach (var permissionsRel in userDto.UserPermissionsRels)
                {
                    //permissionsRel.FkPermissionId = permissionsRel.FkPermission?.Id ?? 0;
                    permissionsRel.FkPermission = null;
                }
                
                var user = await _partnerService.UpdateUserAsync(_mapper.Map<UserDto, User>(userDto)).ConfigureAwait(false);
                return Ok(user);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error UpdateUserAsync");
            return BadRequest(new ErrorDto(ex.ToString()));
        }
    }

    [HttpDelete]
    [Route("{userId:int}")]
    public async Task<IActionResult> DeleteUserAsync(int userId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "DeleteUserAsync"))
            {
                await _partnerService.DeleteUserAsync(userId).ConfigureAwait(false);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error DeleteUserAsync");
            return BadRequest(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [Route("users/resetPassword")]
    public async Task<IActionResult> ResetPasswordUsers(UserDto user)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ResetPasswordUsers"))
            {
                await _generalUserService.ResetPassword(user);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting ResetPasswordUsers");
            return BadRequest(new ErrorDto(ex.ToString()));
        }
    }

    [AllowAnonymous]
    [HttpGet]
    [Route("users/forgotPassword/{email}")]
    public async Task<IActionResult> ForgotPasswordUsers(string email)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ForgotPasswordUsers"))
            {
                await _generalUserService.ForgotPassword(email);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting ForgotPasswordUsers");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [AllowAnonymous]
    [HttpGet]
    [Route("users/token/{token}")]
    public async Task<IActionResult> GetUserByToken(string token)
    {
        try
        {
            var user = await _partnerService.GetUserByToken(token);
            return Ok(user);
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting GetUserByToken");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [AllowAnonymous]
    [Route("updatePassword")]
    public async Task<IActionResult> UpdatePassword(UpdatePasswordDto updatePasswordDto)
    {
        try
        {
            await _partnerService.UpdatePassword(updatePasswordDto.Password, updatePasswordDto.Token);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while updating password");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost("login")]
    [AllowAnonymous]
    public async Task<IActionResult> LoginAsync(LoginDto login)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "LoginAsync"))
            {
                var localhost = HttpContext.Connection.RemoteIpAddress != null &&
                                IPAddress.IsLoopback(HttpContext.Connection.RemoteIpAddress);
                var result = await _partnerService.LoginAsync(login, localhost).ConfigureAwait(false);
                
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Warning(ex, "Error while logging in");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet("tfa")]
    public async Task<IActionResult> GetTfa()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetTfaSetup"))
            {
                var email = User.Claims.FirstOrDefault(a => a.Value.Contains("@"))!.Value;
                var result = await _partnerService.GetTfaSetup(email)
                    .ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Warning(ex, "Error while getting tfa info");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet("tfa/{code}")]
    public async Task<IActionResult> ValidateTfaCode(string code)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ValidateTfaCode"))
            {
                var email = User.Claims.FirstOrDefault(a => a.Value.Contains("@"))!.Value;
                var result = await _partnerService.ValidateTfaCode(email, code)
                    .ConfigureAwait(false);
                return Ok(_mapper.Map<User, UserDto>(result));
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Warning(ex, "Error while validating tfa code");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpDelete("tfa")]
    public async Task<IActionResult> DeleteTfaCode()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ValidateTfaCode"))
            {
                var email = User.Claims.FirstOrDefault(a => a.Value.Contains("@"))!.Value;
                var result = await _partnerService.DeleteTfa(email)
                    .ConfigureAwait(false);
                return Ok(_mapper.Map<User, UserDto>(result));
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Warning(ex, "Error while deleting tfa code");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [Route("updateOwnPassword")]
    public async Task<IActionResult> UpdatePassword(PasswordDto dto)
    {
        try
        {
            var userId = Convert.ToInt32(User.Claims.First(a => a.Type == "uid").Value);
            await _partnerService.UpdatePassword(userId, dto.Password);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while updating password");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet("verifyUserToken/{token}")]
    [AllowAnonymous]
    public async Task<IActionResult> VerifyToken(string token)
    {
        try
        {
            var result = await _partnerService.VerifyUserToken(token);
            return Ok(new { success = result });
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Warning(ex, "Error while verifying external token");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }
}