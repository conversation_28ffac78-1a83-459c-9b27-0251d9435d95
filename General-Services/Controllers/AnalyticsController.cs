using General_Services.Services.Analytics.Email;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Dto.General;
using Shared.Models;
using ILogger = Serilog.ILogger;

namespace General_Services.Controllers;

[Authorize(Roles = "valyrion")]
[AllowAnonymous]
[Route("[controller]")]
[ApiController]
public class AnalyticsController : ControllerBase
{
    private readonly ILogger _logger;
    private readonly IAnalyticsEmailService _analyticsEmailService;

    public AnalyticsController(ILogger logger, IAnalyticsEmailService analyticsEmailService)
    {
        _logger = logger;
        _analyticsEmailService = analyticsEmailService;
    }

    [HttpPost]
    [Route("email/performanceOverTime")]
    public async Task<IActionResult> AnalyticsGetPerformanceOverTime(AnalyticsDto analyticsDto)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AnalyticsGetPerformanceOverTime"))
            {
                var result = await _analyticsEmailService.GetPerformanceOverTime(analyticsDto);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error AnalyticsGetPerformanceOverTime");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    [HttpPost]
    [Route("email/monitorPerformance")]
    public async Task<IActionResult> AnalyticsGetMonitorPerformance(AnalyticsDto analyticsDto)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AnalyticsGetPerformanceOverTime"))
            {
                var result = await _analyticsEmailService.GetMonitorPerformance(analyticsDto);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error AnalyticsGetPerformanceOverTime");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    [HttpPost]
    [Route("email/compareEmailsOverTime")]
    public async Task<IActionResult> AnalyticsCompareEmailsOverTime(AnalyticsDto analyticsDto)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AnalyticsCompareEmailsOverTime"))
            {
                var result = await _analyticsEmailService.GetCompareEmailsOverTime(analyticsDto);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error AnalyticsCompareEmailsOverTime");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    [HttpPost]
    [Route("email/trackConversions")]
    public async Task<IActionResult> AnalyticsTrackConversions(AnalyticsDto analyticsDto)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AnalyticsTrackConversions"))
            {
                var result = await _analyticsEmailService.GetTrackConversions(analyticsDto);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error AnalyticsTrackConversions");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }
}