using Audience.Services.Audience;
using General_Services.Models.Models;
using General_Services.Services.Export;
using General_Services.Services.MerchantScore;
using Marlin_OS_Integration_API.Services.Static;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Services;
using Shared.Services.Cache;
using Shared.Services.Partner;
using ILogger = Serilog.ILogger;

namespace General_Services.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class GeneralExternal(
    ILogger logger,
    IMerchantScoreService merchantScoreService,
    ICustomerService customerService,
    ICacheService cacheService,
    IPartnerContext partnerContext)
    : ControllerBase
{
    [HttpGet]
    [AllowAnonymous]
    [Route("MerchantScore/{email}")]
    public async Task<IActionResult> GetMerchantScoreAsync(string email)
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.Equals("apikey", StringComparison.CurrentCultureIgnoreCase))
                    .Value.ToString()))
            {
                using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetMerchantScoreAsync"))
                {
                    var webshops = await merchantScoreService.GetMerchantScoreAsync(email)
                        .ConfigureAwait(false);
                    return Ok(webshops);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error GetMerchantScoreAsync");
            return Ok();
        }
    }
    
    [HttpPost]
    [AllowAnonymous]
    [Route("MerchantScores")]
    public async Task<IActionResult> GetMerchantScoresAsync([FromBody] List<string> emails)
    {
        if (emails == null || emails.Count == 0)
            return BadRequest("Email list cannot be null or empty.");

        try
        {
            if (!Validate.ValidateInternalKey(Request.Headers
                    .FirstOrDefault(a => a.Key.Equals("apikey", StringComparison.CurrentCultureIgnoreCase))
                    .Value.ToString()))
            {
                return Forbid();
            }

            using (logger.ForContext("serilog_logtype", "timer")
                       .ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetMerchantScoresAsync"))
            {
                // Process emails in bulk
                var merchantScores = await merchantScoreService.GetMerchantScoresByEmailsAsync(emails).ConfigureAwait(false);
                return Ok(merchantScores);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error in GetMerchantScoresAsync");
            return Ok(new Dictionary<string, List<MerchantScoreDto>>());
        }
    }


    [HttpGet]
    [AllowAnonymous]
    [Route("ProductScore/{email}")]
    public async Task<IActionResult> GetProductScoreAsync(string email)
    {
        try
        {
            /*if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {*/
                using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetProductScoreAsync"))
                {
                    var merchants = await merchantScoreService.GetProductScoreAsync(email)
                        .ConfigureAwait(false);
                    return Ok(merchants);
                }
            /*}*/

            return Forbid();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error GetMerchantScoreAsync");
            return Ok();
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("PriceDropScores/{email}")]
    public async Task<IActionResult> GetPriceDropScoresAsync(string email)
    {
        try
        {
            /**if (!Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.Equals("apikey", StringComparison.CurrentCultureIgnoreCase))
                    .Value.ToString()))
                return Forbid();**/

            // TODO: This is a temporary solution to get the price drop scores - we should implement a Price Drop Score Method in the MerchantScoreService
            var priceDropScores = await merchantScoreService.GetPriceDropScoresAsync(email).ConfigureAwait(false);
            return Ok(priceDropScores);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error GetPriceDropScoresAsync");
            return Ok();
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("verify-customer-email/{email}")]
    public async Task<IActionResult> VerifyCustomerEmailAsync(string email)
    {
        try
        {
            if (!Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.Equals("apikey", StringComparison.CurrentCultureIgnoreCase))
                    .Value.ToString())) 
                return Forbid();
            
            var partnerId = partnerContext.PartnerId;
            var cacheKey = $"AllCustomerEmails_{partnerId}";
            var customerEmails = await cacheService.GetData<List<string>>(cacheKey);
            if (customerEmails == null)
            {
                customerEmails = await customerService.GetAllCustomerEmailsAsync();
                cacheService.SetData(cacheKey, customerEmails, TimeSpan.FromHours(24));
            }

            var validEmail = customerEmails.Contains(email);

            return Ok(validEmail);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error VerifyCustomerEmailAsync");
            return Ok();
        }
    }
}