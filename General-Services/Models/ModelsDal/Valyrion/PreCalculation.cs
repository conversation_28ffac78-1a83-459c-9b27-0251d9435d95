using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace General_Services.Models.ModelsDal.Valyrion;

[Table("PreCalculation", Schema = "valyrion")]
[Index("ContextType", "ContextId", "CalculationType", Name = "IX_PreCalculation_Keys")]
public partial class PreCalculation
{
    [Key]
    public long Id { get; set; }

    [StringLength(100)]
    public string ContextType { get; set; } = null!;

    [StringLength(100)]
    public string ContextId { get; set; } = null!;

    [StringLength(100)]
    public string CalculationType { get; set; } = null!;

    public double? ResultValue { get; set; }

    public string? ResultJson { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime ValidFrom { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? ValidTo { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime CalculatedAt { get; set; }
}
