using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace General_Services.Models.ModelsDal.Valyrion;

[Table("Users", Schema = "valyrion")]
public partial class User
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(255)]
    public string Email { get; set; } = null!;

    [StringLength(255)]
    public string Password { get; set; } = null!;

    [StringLength(255)]
    public string FirstName { get; set; } = null!;

    [StringLength(255)]
    public string LastName { get; set; } = null!;

    [Column("TFAAuthKey")]
    [StringLength(255)]
    public string? TfaauthKey { get; set; }

    [Column("TFAEnabled")]
    public bool? Tfaenabled { get; set; }

    [InverseProperty("FkUser")]
    public virtual ICollection<UserPermissionsRel> UserPermissionsRels { get; set; } = new List<UserPermissionsRel>();

    [InverseProperty("FkUser")]
    public virtual ICollection<UserToken> UserTokens { get; set; } = new List<UserToken>();
}
