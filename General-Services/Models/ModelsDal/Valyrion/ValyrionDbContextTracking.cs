using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Shared;
using IConnection = RabbitMQ.Client.IConnection;

namespace General_Services.Models.ModelsDal.Valyrion;

public class ValyrionDbContextTracking(
    DbContextOptions<ValyrionDbContext> options,
    IHttpContextAccessor httpContextAccessor,
    //IConnection rabbitConnection
    [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud)
    : ValyrionDbContext(options)
{
    public virtual async Task<int> SaveChangesAsync()
    {
        OnBeforeSaveChanges();
        var result = await base.SaveChangesAsync();
        return result;
    }

    public virtual int SaveChanges()
    {
        OnBeforeSaveChanges();
        var result = base.SaveChanges();
        return result;
    }

    private void OnBeforeSaveChanges()
    {
        AuditSaveDb.OnBeforeSaveChanges(ChangeTracker, httpContextAccessor, rabbitConnectionCloud);
    }
}