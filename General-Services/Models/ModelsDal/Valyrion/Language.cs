using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace General_Services.Models.ModelsDal.Valyrion;

[Table("Languages", Schema = "valyrion")]
public partial class Language
{
    [Key]
    public int Id { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime CreatedDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime LastModifiedDate { get; set; }

    public bool IsActive { get; set; }

    [StringLength(10)]
    public string LanguageCode { get; set; } = null!;

    [StringLength(100)]
    public string LanguageName { get; set; } = null!;
}
