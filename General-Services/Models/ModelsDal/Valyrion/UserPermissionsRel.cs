using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace General_Services.Models.ModelsDal.Valyrion;

[Table("UserPermissionsRel", Schema = "valyrion")]
public partial class UserPermissionsRel
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [Column("FK_UserId")]
    public int FkUserId { get; set; }

    [Column("FK_PermissionId")]
    public int FkPermissionId { get; set; }

    [ForeignKey("FkPermissionId")]
    [InverseProperty("UserPermissionsRels")]
    public virtual Permission FkPermission { get; set; } = null!;

    [ForeignKey("FkUserId")]
    [InverseProperty("UserPermissionsRels")]
    public virtual User FkUser { get; set; } = null!;
}
