using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace General_Services.Models.ModelsDal.Valyrion;

public partial class ValyrionDbContext : DbContext
{
    public ValyrionDbContext(DbContextOptions<ValyrionDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Export> Exports { get; set; }

    public virtual DbSet<Language> Languages { get; set; }

    public virtual DbSet<Permission> Permissions { get; set; }

    public virtual DbSet<PreCalculation> PreCalculations { get; set; }

    public virtual DbSet<User> Users { get; set; }

    public virtual DbSet<UserPermissionsRel> UserPermissionsRels { get; set; }

    public virtual DbSet<UserToken> UserTokens { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Export>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Export__3214EC0789403CD2");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<Language>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Language__3214EC07811A2624");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<Permission>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Permissi__3214EC0767E24AD9");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<PreCalculation>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__PreCalcu__3214EC079C6940AD");

            entity.Property(e => e.CalculatedAt).HasDefaultValueSql("(getutcdate())");
            entity.Property(e => e.ValidFrom).HasDefaultValueSql("(getutcdate())");
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Users__3214EC079E0CD22F");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<UserPermissionsRel>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__UserPerm__3214EC07A4D80E87");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkPermission).WithMany(p => p.UserPermissionsRels)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserPermi__FK_Pe__3A179ED3");

            entity.HasOne(d => d.FkUser).WithMany(p => p.UserPermissionsRels)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserPermi__FK_Us__39237A9A");
        });

        modelBuilder.Entity<UserToken>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__UserToke__3214EC07BAABB836");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkUser).WithMany(p => p.UserTokens)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserToken__FK_Us__3FD07829");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
