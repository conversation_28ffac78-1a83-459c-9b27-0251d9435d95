namespace General_Services.Models.Models;

public class UserDto
{
    public int Id { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastModifiedDate { get; set; }
    public bool Active { get; set; }
    public string Email { get; set; } = null!;
    public string Password { get; set; } = null!;
    public string FirstName { get; set; } = null!;
    public string LastName { get; set; } = null!;
    public string? Role { get; set; }
    public string? TfaauthKey { get; set; }
    public bool? Tfaenabled { get; set; }
    public List<UserPermissionsRelDto> UserPermissionsRels { get; set; }
}