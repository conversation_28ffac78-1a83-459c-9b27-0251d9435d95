namespace General_Services.Models.Models.Export;

public class ExportEmailOrderGenderAgeSegments
{
    public string Email { get; set; }
    public byte? Age { get; set; }
    public string Gender { get; set; }

    public List<ExportEmailOrderGenderAgeSegmentsTransactions> Transactions { get; set; } =
        new List<ExportEmailOrderGenderAgeSegmentsTransactions>();
}

public class ExportEmailOrderGenderAgeSegmentsTransactions
{
    public string MerchantId { get; set; }
    public DateTime OrdreDate { get; set; }
    public decimal TotalPrice { get; set; }
}