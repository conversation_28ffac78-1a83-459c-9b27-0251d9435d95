// Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);

public class ErstMaanedsbeskaeftigelse
{
    public string intervalKodeAntalAnsatte { get; set; }
    public int aar { get; set; }
    public DateTime sidstOpdateret { get; set; }
    public int maaned { get; set; }
    public string intervalKodeAntalAarsvaerk { get; set; }
    public double antalAarsvaerk { get; set; }
    public int antalAnsatte { get; set; }
}

public class Hit
{
    public string _index { get; set; }
    public string _type { get; set; }
    public string _id { get; set; }
    public double _score { get; set; }
    public Source _source { get; set; }
    public int total { get; set; }
    public double max_score { get; set; }
    public List<Hit> hits { get; set; }
}

public class NyesteNavn
{
    public string navn { get; set; }
}

public class Hits
{
    public int total { get; set; }
    public double max_Score { get; set; }
    public List<Hit> hits { get; set; }
}

public class ExportVirk
{
    public int took { get; set; }
    public bool timed_out { get; set; }
    public Shards _shards { get; set; }
    public Hits hits { get; set; }
}

public class Shards
{
    public int total { get; set; }
    public int successful { get; set; }
    public int skipped { get; set; }
    public int failed { get; set; }
}

public class Source
{
    public Vrvirksomhed Vrvirksomhed { get; set; }
}

public class VirksomhedMetadata
{
    public string stiftelsesDato { get; set; }
    public List<string> nyesteKontaktoplysninger { get; set; }
    public NyesteNavn nyesteNavn { get; set; }
}

public class Vrvirksomhed
{
    public int cvrNummer { get; set; }
    public List<object> obligatoriskEmail { get; set; }
    public List<ErstMaanedsbeskaeftigelse> erstMaanedsbeskaeftigelse { get; set; }
    public bool reklamebeskyttet { get; set; }
    public VirksomhedMetadata virksomhedMetadata { get; set; }
}