// Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);

public class Dokumenter
{
    public string dokumentType { get; set; }
    public string dokumentMimeType { get; set; }
    public string dokumentUrl { get; set; }
}

public class Godkendelse
{
    public string dirigent { get; set; }
    public string godkendelsesdato { get; set; }
}

public class Hit1
{
    public string _index { get; set; }
    public string _type { get; set; }
    public string _id { get; set; }
    public double _score { get; set; }
    public Source1 _source { get; set; }
    public int total { get; set; }
    public double max_score { get; set; }
    public List<Hit1> hits { get; set; }
}

public class Regnskab
{
    public Regnskabsperiode regnskabsperiode { get; set; }
    public Godkendelse godkendelse { get; set; }
}

public class Regnskabsperiode
{
    public string slutDato { get; set; }
    public string startDato { get; set; }
}

public class ExportVirkAccounting
{
    public int took { get; set; }
    public bool timed_out { get; set; }
    public Shards1 _shards { get; set; }
    public Hits1 hits { get; set; }
}

public class Hits1
{
    public int total { get; set; }
    public double max_Score { get; set; }
    public List<Hit1> hits { get; set; }
}

public class Shards1
{
    public int total { get; set; }
    public int successful { get; set; }
    public int skipped { get; set; }
    public int failed { get; set; }
}

public class Source1
{
    public string indlaesningsId { get; set; }
    public string sagsNummer { get; set; }
    public Regnskab regnskab { get; set; }
    public DateTime sidstOpdateret { get; set; }
    public int cvrNummer { get; set; }
    public List<Dokumenter> dokumenter { get; set; }
    public object regNummer { get; set; }
    public DateTime indlaesningsTidspunkt { get; set; }
    public DateTime offentliggoerelsesTidspunkt { get; set; }
    public bool omgoerelse { get; set; }
    public string offentliggoerelsestype { get; set; }
}