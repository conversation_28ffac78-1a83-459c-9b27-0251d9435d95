namespace General_Services.Models.Models;

public class ContactAnalysisDto
{
    public string Email { get; set; }
    public byte Age { get; set; }
    public string Gender { get; set; }
    public string Criteria { get; set; }
    public int ActiveExposures { get; set; }
    public int TotalMerchantScoreCount { get; set; }
    public List<ContactAnalysisMerchantScoreDto> MerchantScores { get; set; }
    public List<ContactAnalysisMerchantScoreDto> MerchantScoresNormalOrder { get; set; }
}

public class ContactAnalysisMerchantScoreDto
{
    public string WebshopName { get; set; }
    public double Sum { get; set; }
    public bool Exposure { get; set; }
}