namespace General_Services.Models.Models;

/// <summary>
/// Represents dashboard metrics for a specific time period with flexibility for any date range
/// </summary>
public class DashboardPeriodProfile
{
    /// <summary>
    /// Total revenue for the period
    /// </summary>
    public decimal Revenue { get; set; }

    /// <summary>
    /// Revenue driven through marketing activities
    /// </summary>
    public decimal RevenueDriven { get; set; }

    /// <summary>
    /// Total number of orders in the period
    /// </summary>
    public int OrdersCount { get; set; }

    /// <summary>
    /// Total number of exposures (including duplicates per customer)
    /// </summary>
    public long TotalExposuresCount { get; set; }

    /// <summary>
    /// Unique exposure count (distinct customers exposed)
    /// </summary>
    public long UniqueExposureCount { get; set; }

    /// <summary>
    /// Total number of displays across all channels
    /// </summary>
    public long TotalDisplayCount { get; set; }

    /// <summary>
    /// Number of marketing emails sent
    /// </summary>
    public int MailSentCount { get; set; }

    /// <summary>
    /// Revenue graph comparing current period with previous period
    /// </summary>
    public PlatformRevenueGraph RevenueGraph { get; set; } = new();

    /// <summary>
    /// MQL (Marketing Qualified Leads) metrics graph
    /// </summary>
    public PlatformMQLGraph MqlGraph { get; set; } = new();
}

/// <summary>
/// Revenue comparison graph between current and previous periods
/// </summary>
public class PlatformRevenueGraph
{
    /// <summary>
    /// Revenue data points for the current period
    /// </summary>
    public List<decimal> Period { get; set; } = new();

    /// <summary>
    /// Revenue data points for the previous comparable period
    /// </summary>
    public List<decimal> LastPeriod { get; set; } = new();
}

/// <summary>
/// Marketing Qualified Leads graph with daily metrics
/// </summary>
public class PlatformMQLGraph
{
    /// <summary>
    /// Daily count of marketing qualified leads
    /// </summary>
    public List<int> DayCount { get; set; } = new();

    /// <summary>
    /// Contact count per day for tracking engagement
    /// </summary>
    public List<long> ContactCountPrDay { get; set; } = new();
} 