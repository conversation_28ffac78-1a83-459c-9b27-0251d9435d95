using System.Text;
using System.Text.Json;
using Audience.Services.Audience;
using Campaign_Services.Services.Filter;
using General_Services.Models.Models;
using General_Services.Models.ModelsDal.Valyrion;
using Microsoft.Extensions.Configuration;
using Nest;
using Partner_Services.Services.PartnerData;
using Renci.SshNet;
using Shared.Elastic.Elastic;
using Shared.Models;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace General_Services.Services.Import;

public class ImportService : IImportService
{
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly IMerchantService _merchantService;
    private readonly IFilterService _filterService;
    private readonly IElasticService _elasticService;
    private readonly ICustomerService _customerService;
    private readonly ElasticClient _elasticClient;
    private readonly IPartnerDataService _partnerDataService;
    private readonly ValyrionDbContextTracking _valyrionDbContext;
    private int lookBackDays = 90;

    public ImportService(ILogger logger, IConfiguration configuration,
        ElasticClient elasticClient, IMerchantService merchantService, IFilterService filterService,
        ICustomerService customerService, IPartnerDataService partnerDataService, IElasticService elasticService,
        ValyrionDbContextTracking valyrionDbContext)
    {
        _logger = logger;
        _configuration = configuration;
        _merchantService = merchantService;
        _filterService = filterService;
        _customerService = customerService;
        _partnerDataService = partnerDataService;
        _elasticService = elasticService;
        _valyrionDbContext = valyrionDbContext;
        _elasticClient = elasticClient;
    }


    public async Task ImportMl()
    {
        var sftpClient = CreateClient();
        sftpClient.Connect();
        var files = sftpClient.ListDirectory("ml").Where(x => x.IsRegularFile).ToList();
        foreach (var file in files)
        {
            using (MemoryStream fileStream = new MemoryStream())
            {
                sftpClient.DownloadFile(file.FullName, fileStream);
                var jsonData = Encoding.UTF8.GetString(fileStream.ToArray());
                jsonData = jsonData.Substring(1, jsonData.Length - 2);
                jsonData = jsonData.Replace("\\", "");
                var recommendationsMl = JsonSerializer.Deserialize<List<MerchantRecommendationsMl>>(jsonData);
                if (recommendationsMl != null)
                {
                    await _customerService.MerchantRecommendationsM(recommendationsMl);
                    MoveFilSftp(file.Name);
                }
            }
        }
    }

    private SftpClient CreateClient()
    {
        //Init ViaBill sftp connection
        var keyString = _configuration["ValyrionFtpKey"]!.Replace(" ", "\r\n");
        keyString = keyString.Replace("-----BEGIN\r\nRSA\r\nPRIVATE\r\nKEY-----",
            "-----BEGIN RSA PRIVATE KEY-----");
        keyString = keyString.Replace("-----END\r\nRSA\r\nPRIVATE\r\nKEY-----", "-----END RSA PRIVATE KEY-----");
        MemoryStream keyStream = new MemoryStream(Encoding.UTF8.GetBytes(keyString));
        var key = new PrivateKeyFile(keyStream);
        return new SftpClient(_configuration["ValyrionFtpHost"],
            //"viabilldata.data.valyrion", key);
            "viabilldata.viaads.valyrion", key);
    }

    private void MoveFilSftp(string fileName)
    {
        var now = DateTime.UtcNow;
        var directoryName = $"ml/imported/{now.Year}-{now.Month:D2}";

        var sftpClient = CreateClient();
        sftpClient.Connect();
        var files = sftpClient.ListDirectory("ml").ToList();
        var fileExist = files.SingleOrDefault(a => a.Name == fileName);
        if (fileExist != null)
        {
            if (!sftpClient.Exists(directoryName))
            {
                sftpClient.CreateDirectory(directoryName);
            }

            sftpClient.RenameFile($"{"ml"}/{fileName}",
                $"{directoryName}/{fileName}");
        }

        sftpClient.Disconnect();
    }
}