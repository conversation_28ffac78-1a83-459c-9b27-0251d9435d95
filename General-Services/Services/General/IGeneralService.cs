using Customer_Services.Models.ModelsDal.Customer;
using Shared.Dto;
using Shared.Dto.Campaign.Pagination;
using Shared.Dto.Dashboard;
using Shared.Dto.Merchants;
using Shared.Dto.ProductScore;
using Shared.Dto.Shop.Product.Internal;
using Shared.Models;
using General_Services.Models.Models;

namespace General_Services.Services.General
{
    public interface IGeneralService
    {
        Task<DashboardProfile> UpdateDashboardProfileAsync(int partnerId, bool forceUpdate, int year, int month);
        Task<DashboardProfile> GetDashboardProfileAsync(bool forceUpdate, int year, int month);
        Task<DashboardPeriodProfile> GetDashboardProfilePeriodAsync(DateTime from, DateTime to);

        Task<MerchantPaginationDto>
            GetMerchantsAsync(PaginationSearchDto paginationSearchDto, bool forceUpdate = false);

        Task<FileDto> ExportMerchantsAsync(PaginationSearchDto paginationSearchDto);


        Task<FileDto> ExportCampaignAsync(PaginationSearchDto paginationSearchDto);
        Task UpdateCustomerMeta();
        Task TopProductsForMerchants();
        Task UpdateOpenRate();
        Task<MerchantSummaryDto> GetMerchantsSummaryAsync();

        
        // Campaigns

        Task<CampaignPaginationDto>
            GetCampaignsAsync(PaginationSearchDto paginationSearchDto, bool forceUpdate = false);
        Task<List<CampaignPaginationDataDto>> GetCampaignsQueuedAsync();
        Task<CampaignStatusDto> GetCampaignsStatusAsync(DateTime from, DateTime to);
        Task<CampaignSummaryDto> GetCampaignSummaryAsync();
        Task PreCalculateCampaignSummaryAsync();



        Task<List<ProductRefInternalDto>> GetProductRecommendationsByStrategy(string strategy, string? email, string? age, string? gender);
        Task IPAndDomainWarmupAutomation(int partnerId);
        Task<bool> SendFeedbackAsync(string email, string feedback);
    }

    
}