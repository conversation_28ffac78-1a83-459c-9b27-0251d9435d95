using System.Data;
using System.Diagnostics;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Admin_Services.Services.Admin;
using Audience.Services.Audience;
using Campaign_Services.Services.Campaign;
using ClosedXML.Excel;
using Customer_Services.Models.ModelsDal.Customer;
using Discount_Services.Services.Discounts;
using General_Services.Models.Models;
using General_Services.Models.ModelsDal.Valyrion;
using Google.Authenticator;
using Invoice_Services.Models.ModelsDal.Invoice;
using Merchant_Services.Models.ModelsDal.Merchant;
using Message_Services.Services.Message;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Shared.Dto;
using Shared.Dto.Campaign.Pagination;
using Shared.Dto.Curated.Product;
using Shared.Dto.Dashboard;
using Shared.Dto.Merchants;
using Shared.Dto.Webshop;
using Shared.Elastic.CampaignMailClick;
using Shared.Elastic.CampaignMailOpen;
using Shared.Elastic.DiscountOpen;
using Shared.Elastic.Elastic;
using Shared.Elastic.ShopProductsDisplays;
using Shared.Elastic.ShopProductsInteracts;
using Shared.Elastic.ShopProductsRedirects;
using Shared.Helpers.Converters;
using Shared.Models;
using Shared.Models.Merchant;
using Shared.Services.Cache;
using Shared.Services.Partner;
using Shared.Services.Setting;
using Statistics_Services.Services.Statistics;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;
using MerchantAssetType = Shared.Models.Merchant.Assets.MerchantAssetType;
using Recommendation_Services.Services;
using Shared.Constants;
using Shared.Dto.ProductScore;
using Recommendation_Services.Models;
using Microsoft.Extensions.Caching.Memory;
using System.Text.Json;
using Shared.Dto.Campaign.Enums;
using Shared.Dto.Shop.Product.Internal;
using System.Security.Cryptography;

namespace General_Services.Services.General;

public class GeneralService(
    ValyrionDbContextTracking valyrionDbContext,
    ILogger logger,
    IConfiguration configuration,
    IStatisticsService statisticsService,
    IMessageService messageService,
    ICacheService cacheService,
    IMerchantService merchantService,
    IDiscountService discountService,
    IElasticCampaignMailOpenService elasticCampaignMailOpenService,
    IElasticCampaignMailClickService elasticCampaignMailClickService,
    IElasticDiscountOpenService elasticDiscountOpenService,
    IElasticService elasticService,
    ISegmentService segmentService,
    IAdminService adminService,
    ICustomerService customerService,
    ICampaignService campaignService,
    IElasticShopProductDisplaysService elasticShopProductDisplaysService,
    IElasticShopProductInteractsService elasticShopProductInteractsService,
    IElasticShopProductRedirectsService elasticShopProductRedirectsService,
    IPartnerContext partnerContext,
    IOfferRecommendationService recommendationService,
    IMemoryCache memoryCache,
    ISettingService settingService)
    : IGeneralService
{
    public async Task<DashboardProfile> UpdateDashboardProfileAsync(int partnerId, bool forceUpdate, int year, int month)
    {
        // TODO: Cache Key does not make sense, as the functionality is different
        var cacheKey = $"PartnerPortalService_GetViabillProfileAsync_{year}_{month}_{partnerId}";
        var dashboardProfile = await cacheService.GetData<DashboardProfile>(cacheKey);
        if (dashboardProfile == null || forceUpdate)
        {
            dashboardProfile = await statisticsService.GetDashboardProfileAsync(year, month)
                .ConfigureAwait(false);
            cacheService.SetData(cacheKey, dashboardProfile, TimeSpan.FromHours(6));
        }

        return dashboardProfile;
    }
    
    public async Task<DashboardProfile> GetDashboardProfileAsync(bool forceUpdate, int year, int month)
    {
        var partnerId = partnerContext.PartnerId;
        var cacheKey = $"PartnerPortalService_GetViabillProfileAsync_{year}_{month}_{partnerId}";
        var dashboardProfile = await cacheService.GetData<DashboardProfile>(cacheKey);
        if (dashboardProfile == null || forceUpdate)
        {
            dashboardProfile = await statisticsService.GetDashboardProfileAsync(year, month)
                .ConfigureAwait(false);
            cacheService.SetData(cacheKey, dashboardProfile, TimeSpan.FromHours(2));
        }

        return dashboardProfile;
    }

    public Task<DashboardPeriodProfile> GetDashboardProfilePeriodAsync(DateTime from, DateTime to)
    {
        throw new NotImplementedException();
    }

    public async Task<MerchantPaginationDto> GetMerchantsAsync(PaginationSearchDto merchantSearchDto,
        bool forceUpdate = false)
    {
        var partnerId = partnerContext.PartnerId;
        merchantSearchDto.From = DateTimeExtensions.SetTimeToMinimum(merchantSearchDto.From);
        merchantSearchDto.To = DateTimeExtensions.SetTimeToMax(merchantSearchDto.To);
        var now = DateTime.UtcNow;

        //Hours
        var cachingHours = 2;
        var merchants = await merchantService.GetFullAsync();
        //Only get merchants there was created in the period asked after
        merchants = merchants.Where(a => a.CreatedDate <= merchantSearchDto.To).ToList();

        var cacheKey =
            $"GeneralService_GetMerchantsAsync_{merchantSearchDto.From:yyyy-MM-dd}_{merchantSearchDto.To:yyyy-MM-dd}_{configuration["Environment"]}_{partnerId}";
        var merchantPaginationDto = await cacheService.GetData<MerchantPaginationDto>(cacheKey, true) ??
                                    new MerchantPaginationDto();
        if (forceUpdate)
        {
            merchantPaginationDto = new MerchantPaginationDto();
        }

        merchantPaginationDto.HasPreviousPage = merchantSearchDto.Page > 0;
        merchantPaginationDto.HasNextPage = (merchantSearchDto.Page + 1) * merchantSearchDto.Size < merchants.Count;
        bool newData = false;

        //Check if any new update in the last period and get them again
        foreach (var merchant in merchants.Where(
                     a => a.LastModifiedDate > DateTime.UtcNow.AddHours(-(cachingHours / 2))))
        {
            var data = merchantPaginationDto.Merchants.SingleOrDefault(a => a.MerchantId == merchant.Id);
            if (data != null && merchant.LastModifiedDate != data.LastModifiedDate)
            {
                merchantPaginationDto.Merchants.Remove(data);
                merchantPaginationDto.Size--;
            }
        }

        //Test merchant
        /*var testData = merchantPaginationDto.Merchants.SingleOrDefault(a => a.MerchantId == 5557);
        if (testData != null)
        {
            merchantPaginationDto.Merchants.Remove(testData);
            merchantPaginationDto.Size--;
        }*/

        if (merchantPaginationDto.Size != merchants.Count || merchantPaginationDto.Size == 0)
        {
            newData = true;
            merchantPaginationDto.Size = merchants.Count;
            var discounts = await discountService.GetActiveDiscount();
            var merchantIds = merchants.Select(a => a.Id).ToList();
            //InvoiceStats
            // TODO - Add PartnerId to the search ?? Check if it works with merchantIds
            var invoiceLinesElastic =
                await elasticService.InvoiceStats("invoices-lines", merchantSearchDto.From, merchantSearchDto.To, merchantIds);
            var invoicePotentialsLinesElastic = await elasticService.InvoiceStats("invoices-potentiallines",
                merchantSearchDto.From, merchantSearchDto.To, merchantIds);
            foreach (var merchant in merchants)
            {
                var merchantPaginationDataDto =
                    merchantPaginationDto.Merchants.FirstOrDefault(a => a.MerchantId == merchant.Id);
                if (merchantPaginationDataDto == null)
                {
                    var merchantPayment = merchant.MerchantPayments.FirstOrDefault() ?? new MerchantPayment();
                    bool isCustomer = merchant.MerchantMeta
                        .Any(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer && a.StartDate != null &&
                                  (a.EndDate == null || a.EndDate >= merchantSearchDto.To));

                    //Cm Ready
                    var cmReady = isCustomer
                                  && merchant.MerchantMeta.Any(b =>
                                      b.FkMerchantMetaTypeName == MerchantMetaTypeNames.MarketingStatus
                                      && b.Value.Split(',').Contains("curated merchants"))
                                  && merchant.MerchantAssets.Any(b =>
                                      b.FkMerchantAssetTypeId == (int) MerchantAssetType.CuratedMerchant)
                                  && !string.IsNullOrEmpty(merchant.MerchantMeta
                                      .SingleOrDefault(b => b.FkMerchantMetaTypeName == MerchantMetaTypeNames.TagLine)
                                      ?.Value
                                      .Trim());

                    //Cp Ready
                    var cpReady = isCustomer
                                  && merchant.MerchantMeta.Any(b =>
                                      b.FkMerchantMetaTypeName == MerchantMetaTypeNames.MarketingStatus
                                      && b.Value.Split(',').Contains("curated products"))
                                  && !string.IsNullOrEmpty(merchant.MerchantMeta
                                      .SingleOrDefault(b => b.FkMerchantMetaTypeName == MerchantMetaTypeNames.TagLine)
                                      ?.Value
                                      .Trim());

                    var model = merchant.MerchantMeta.SingleOrDefault(meta =>
                        meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType)?.Value ?? "cpa";
                    decimal actual = 0;
                    decimal potential = 0;
                    int ordersGenerated = 0;
                    decimal revenue = 0;
                    int fullRefund = 0;
                    int partialRefunds = 0;
                    long emailDisplays = 0;
                    long emailDisplaysUnique = 0;
                    long emailRedirects = 0;
                    long discountDisplays = 0;
                    long discountInteracts = 0;
                    long discountRedirects = 0;
                    long productDisplays = 0;
                    long productInteracts = 0;
                    long productRedirects = 0;

                    var customerPeriods = merchant.MerchantMeta
                        .Where(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer)
                        .Where(a => (a.EndDate == null || a.EndDate > merchantSearchDto.From) &&
                                    a.StartDate <= merchantSearchDto.To)
                        .OrderBy(a => a.StartDate)
                        .ToList();

                    var baseFee = merchant.MerchantMeta
                        .FirstOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.BaseFee);

                    decimal totalBaseFee = CalculateBaseFee(ConvertToInt(baseFee?.Value ?? ""), merchantSearchDto.From,
                        merchantSearchDto.To);

                    foreach (var customerPeriod in customerPeriods)
                    {
                        var from = merchantSearchDto.From;
                        var to = merchantSearchDto.To;
                        if (customerPeriod.StartDate > from)
                        {
                            from = customerPeriod.StartDate.Value;
                        }

                        if (customerPeriod.EndDate != null)
                        {
                            to = customerPeriod.EndDate.Value.AddDays(-1);
                        }

                        if (to > merchantSearchDto.To)
                        {
                            to = merchantSearchDto.To;
                        }

                        var invoiceLines1 = await adminService.InvoiceLinesAsync(merchant.Id, from, to);
                        //Invoice
                        var latestOrdersList = invoiceLines1
                            .GroupBy(o => o.OrderId).ToList();
                        var invoiceLines = new List<OrderLine>();
                        foreach (var latestOrders in latestOrdersList)
                        {
                            var skipLine = false;

                            if (latestOrders.Last().ValyrionStatus == "fullRefund")
                            {
                                if (latestOrders.Last().OrderDate > latestOrders.Last().OrderLastModified.AddHours(-60))
                                {
                                    skipLine = true;
                                }
                            }

                            if (!skipLine)
                            {
                                invoiceLines.Add(latestOrders.Last());
                            }
                        }

                        revenue += invoiceLines.Where(a => a.TotalPrice > 0).Sum(a => a.TotalPrice);
                        ordersGenerated += invoiceLines.Select(o => o.OrderId).Distinct().Count();
                        //Refunds
                        var latestOrders1 = invoiceLines
                            .GroupBy(o => o.OrderId)
                            .Select(g => g.OrderByDescending(o => o.OrderLastModified).First()).ToList();
                        fullRefund += latestOrders1.Count(a => a.ValyrionStatus == "fullRefund");
                        partialRefunds += latestOrders1.Count(a => a.ValyrionStatus == "partialRefund");


                        if (model == MerchantAttributionTypes.CPA)
                        {
                            actual = Convert.ToDecimal(invoiceLinesElastic
                                .Where(a => a.MerchantId == merchant.Id.ToString()).Sum(a => a.Sum));
                            potential = Convert.ToDecimal(invoicePotentialsLinesElastic
                                .Where(a => a.MerchantId == merchant.Id.ToString()).Sum(a => a.Sum));
                        }
                        else if (model == MerchantAttributionTypes.CPM)
                        {
                            var cpm = Convert.ToDecimal(merchant.MerchantMeta
                                .SingleOrDefault(meta => meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPM)
                                ?.Value);
                            actual = await elasticCampaignMailOpenService.Opens(from, to, merchant.Id);
                            actual += await elasticDiscountOpenService.Opens(from, to, merchant.Id);
                            actual = actual / 1000 * cpm;
                            potential = Convert.ToDecimal(merchant.MerchantMeta.SingleOrDefault(meta =>
                                meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPMBudget)?.Value);
                            if (actual > potential)
                            {
                                actual = potential;
                            }

                            potential -= actual;
                        }

                        var opensTask = elasticCampaignMailOpenService.Opens(from, to, merchant.Id);
                        var uniqueOpensTask = elasticCampaignMailOpenService.UniqueOpens(from, to, merchant.Id);
                        var clicksTask = elasticCampaignMailClickService.Clicks(from, to, merchant.Id);
                        var discountOpensTask = elasticDiscountOpenService.Opens(from, to, merchant.Id);
                        var discountInteractsTask =
                            elasticDiscountOpenService.Opens(from, to, merchant.Id, null, "discount_details_viewed");
                        var discountRedirectsTask =
                            elasticDiscountOpenService.Opens(from, to, merchant.Id, null, "discount_link_opened");
                        var productDisplaysTask = elasticShopProductDisplaysService.Displays(from, to, merchant.Id);
                        var productInteractsTask = elasticShopProductInteractsService.Interacts(from, to, merchant.Id);
                        var productRedirectsTask = elasticShopProductRedirectsService.Redirects(from, to, merchant.Id);

                        await Task.WhenAll(opensTask, uniqueOpensTask, clicksTask, discountOpensTask,
                            discountInteractsTask, discountRedirectsTask, productDisplaysTask, productInteractsTask,
                            productRedirectsTask);

                        emailDisplays += await opensTask;
                        emailDisplaysUnique += await uniqueOpensTask;
                        emailRedirects += await clicksTask;
                        discountDisplays += await discountOpensTask;
                        discountInteracts += await discountInteractsTask;
                        discountRedirects += await discountRedirectsTask;
                        productDisplays += await productDisplaysTask;
                        productInteracts += await productInteractsTask;
                        productRedirects += await productRedirectsTask;
                    }

                    // Generate Merchant Hashed External Redirect Url
                    var merchantHashedExternalRedirectUrl = GenerateHashedExternalRedirectUrl(merchant);

                    merchantPaginationDataDto = new MerchantPaginationDataDto
                    {
                        MerchantId = merchant.Id,
                        MerchantName = merchant.Name,
                        Type = merchant.Type,
                        IsCustomer = isCustomer,
                        IsMarketingAllowed = merchant.IsMarketingAllowed,
                        Url = merchant.Url,
                        externalRedirectUrl = merchantHashedExternalRedirectUrl,
                        LastModifiedDate = merchant.LastModifiedDate,
                        //Default CPA
                        Model = model,
                        CPADisplayPercentage = merchantPayment.DisplayPaymentPercentage,
                        CPAInteractPercentage = merchantPayment.InteractPaymentPercentage,
                        CPARedirectPercentage = merchantPayment.RedirectPaymentPercentage,
                        CPADisplayExposureDays = merchantPayment.DisplayExposureDays,
                        CPAInteractExposureDays = merchantPayment.InteractExposureDays,
                        CPARedirectExposureDays = merchantPayment.RedirectExposureDays,
                        CPMBudget = ConvertToInt(merchant.MerchantMeta.SingleOrDefault(meta =>
                            meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPMBudget)?.Value ?? "0"),
                        CPM = ConvertToInt(merchant.MerchantMeta
                                               .SingleOrDefault(meta =>
                                                   meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPM)?.Value ??
                                           "0"),
                        Actual = Math.Round(actual, 2),
                        Potential = Math.Round(potential, 2),
                        ActualPotential = Math.Round(actual + potential, 2),
                        MKTEFF = 0,
                        //
                        CMReady = cmReady,
                        MRenewalDays = ConvertToInt(merchant.MerchantMeta.SingleOrDefault(meta =>
                            meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.MinExposureRenewalDays)?.Value ?? "0"),
                        CPReady = cpReady,
                        ActiveDiscount = discounts.Any(a => a.FkMerchantId == merchant.Id),
                        //
                        EmailDisplays = emailDisplays,
                        EmailDisplaysUnique = emailDisplaysUnique,
                        EmailRedirects = emailRedirects,
                        DiscountDisplays = discountDisplays,
                        DiscountInteracts = discountInteracts,
                        DiscountRedirects = discountRedirects,
                        ProductDisplays = productDisplays,
                        ProductInteracts = productInteracts,
                        ProductRedirects = productRedirects,
                        //
                        PartnerOrders = ordersGenerated,
                        PartnerRevenue = Math.Round(revenue, 2),
                        PartnerOrdersFullReturn = fullRefund,
                        PartnerOrdersPartialReturn = partialRefunds,
                        PartnerCost = Math.Round((actual + totalBaseFee), 2),
                        BaseFee = ConvertToInt(baseFee?.Value ?? "0")
                    };

                    if (merchantPaginationDataDto.Actual != 0)
                    {
                        merchantPaginationDataDto.MKTEFF = Math.Round(actual / (potential + actual), 4);
                    }
                }

                //Meta
                foreach (var merchantMetum in merchant.MerchantMeta)
                {
                    merchantPaginationDataDto.MerchantMeta.Add(new MerchantMetumDto
                    {
                        Id = merchantMetum.Id,
                        Value = merchantMetum.Value,
                        FkMerchantId = merchantMetum.FkMerchantId,
                        FkMerchantMetaTypeName = merchantMetum.FkMerchantMetaTypeName,
                    });
                }

                //Check if merchants already exists
                bool exists =
                    merchantPaginationDto.Merchants.Any(m => m.MerchantId == merchantPaginationDataDto.MerchantId);
                if (!exists)
                {
                    merchantPaginationDto.Merchants.Add(merchantPaginationDataDto);
                }
            }
        }

        if (newData)
        {
            cacheService.SetData(cacheKey, merchantPaginationDto, TimeSpan.FromHours(cachingHours), true);
        }

        //Search
        merchantSearchDto.Search = merchantSearchDto.Search.ToLowerInvariant();
        merchantPaginationDto.Merchants = merchantPaginationDto.Merchants
            .Where(a => merchantSearchDto.Search == "" || a.MerchantId.ToString() == merchantSearchDto.Search ||
                        a.MerchantName.ToLowerInvariant().Contains(merchantSearchDto.Search) ||
                        a.Type.ToLowerInvariant().Contains(merchantSearchDto.Search)).ToList();

        //Filter
        foreach (var filter in merchantSearchDto.Filters)
        {
            if (filter.Value != null)
            {
                switch (filter.Field)
                {
                    case "type":
                        merchantPaginationDto.Merchants = merchantPaginationDto.Merchants
                            .Where(a => a.Type.ToLowerInvariant() == filter.Value.ToLowerInvariant()).ToList();
                        break;
                    case "model":
                        merchantPaginationDto.Merchants = merchantPaginationDto.Merchants
                            .Where(a => a.Model.ToLowerInvariant() == filter.Value.ToLowerInvariant()).ToList();
                        break;
                    case "isCustomer":
                        merchantPaginationDto.Merchants = merchantPaginationDto.Merchants
                            .Where(a => a.IsCustomer == Convert.ToBoolean(filter.Value.ToLowerInvariant())).ToList();
                        break;
                }
            }
        }

        //Sort
        switch (merchantSearchDto.SortName)
        {
            case "merchantId":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.MerchantId).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.MerchantId).ToList();
                break;
            case "merchantName":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.MerchantName).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.MerchantName).ToList();
                break;
            case "model":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "model"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.Model).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.Model).ToList();
                break;
            case "isCustomer":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "isCustomer"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.IsCustomer).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.IsCustomer).ToList();
                break;
            case "cpaDisplayPercentage":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.CPADisplayPercentage).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.CPADisplayPercentage).ToList();
                break;
            case "cpaInteractPercentage":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.CPAInteractPercentage).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.CPAInteractPercentage).ToList();
                break;
            case "cpaRedirectPercentage":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.CPARedirectPercentage).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.CPARedirectPercentage).ToList();
                break;
            case "cpaDisplayExposureDays":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.CPADisplayExposureDays).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.CPADisplayExposureDays).ToList();
                break;
            case "cpaInteractExposureDays":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.CPAInteractExposureDays).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.CPAInteractExposureDays).ToList();
                break;
            case "cpaRedirectExposureDays":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.CPARedirectExposureDays).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.CPARedirectExposureDays).ToList();
                break;
            case "cpm":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.CPM).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.CPM).ToList();
                break;
            case "cpmBudget":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.CPMBudget).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.CPMBudget).ToList();
                break;
            case "actual":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.Actual).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.Actual).ToList();
                break;
            case "potential":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.Potential).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.Potential).ToList();
                break;
            case "actualPotential":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.ActualPotential).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.ActualPotential).ToList();
                break;
            case "mkteff":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.MKTEFF).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.MKTEFF).ToList();
                break;
            case "cmReady":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.CMReady).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.CMReady).ToList();
                break;
            case "mRenewalDays":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.MRenewalDays).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.MRenewalDays).ToList();
                break;
            case "cpReady":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.CPReady).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.CPReady).ToList();
                break;
            case "activeDiscount":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.ActiveDiscount).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.ActiveDiscount).ToList();
                break;
            case "emailDisplays":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.EmailDisplays).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.EmailDisplays).ToList();
                break;
            case "emailRedirects":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.EmailRedirects).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.EmailRedirects).ToList();
                break;
            case "discountDisplays":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.DiscountDisplays).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.DiscountDisplays).ToList();
                break;
            case "discountInteracts":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.DiscountInteracts).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.DiscountInteracts).ToList();
                break;
            case "discountRedirects":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.DiscountRedirects).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.DiscountRedirects).ToList();
                break;
            case "partnerOrders":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.PartnerOrders).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.PartnerOrders).ToList();
                break;
            case "partnerOrdersFullReturn":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.PartnerOrdersFullReturn).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.PartnerOrdersFullReturn).ToList();
                break;
            case "partnerOrdersPartialReturn":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.PartnerOrdersPartialReturn).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.PartnerOrdersPartialReturn).ToList();
                break;
            case "partnerRevenue":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.PartnerRevenue).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.PartnerRevenue).ToList();
                break;
            case "partnerCost":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.Actual).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.Actual).ToList();
                break;
            case "baseFee":
                merchantPaginationDto.Merchants = merchantSearchDto.SortOrder == "asc"
                    ? merchantPaginationDto.Merchants.OrderBy(a => a.BaseFee).ToList()
                    : merchantPaginationDto.Merchants.OrderByDescending(a => a.BaseFee).ToList();
                break;
            default:
                merchantPaginationDto.Merchants =
                    merchantPaginationDto.Merchants.OrderByDescending(a => a.ActualPotential).ToList();
                break;
        }

        var start = merchantSearchDto.Size * merchantSearchDto.Page;
        //Return the correct size to frontend
        merchantPaginationDto.Size = merchantPaginationDto.Merchants.Count;
        merchantPaginationDto.Merchants =
            merchantPaginationDto.Merchants.Skip(start).Take(merchantSearchDto.Size).ToList();
        return merchantPaginationDto;
    }

    private decimal CalculateBaseFee(int baseFee, DateTime from, DateTime to)
    {
        // Calculate the number of days in the period
        int daysInPeriod = (to - from).Days + 1;

        // Get the number of days in the month of the 'from' date
        int daysInMonth = DateTime.DaysInMonth(from.Year, from.Month);

        // Calculate the daily fee based on the monthly fee
        decimal dailyFee = baseFee / (decimal) daysInMonth;

        // Calculate the total base fee for the period
        decimal totalBaseFee = dailyFee * daysInPeriod;

        return totalBaseFee;
    }

    private string GenerateHashedExternalRedirectUrl(Merchant merchant)
    {
        var hashSecret = configuration["ExternalRedirect-HashSecret"];
        var partnerId = partnerContext.PartnerId;
        var merchantId = merchant.Id;
        
        // Create JSON data for the redirect
        var redirectData = new
        {
            p = partnerId.ToString(),
            m = merchantId.ToString(),
            ru = merchant.Url
        };
        
        // Serialize to JSON and encode to Base64
        var jsonData = JsonSerializer.Serialize(redirectData);
        var base64Data = Convert.ToBase64String(Encoding.UTF8.GetBytes(jsonData));
        
        // Generate hash for validation
        var hashInput = $"{partnerId}:{merchantId}::{hashSecret}";
        string hash;
        using (var sha256 = SHA256.Create())
        {
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(hashInput));
            var base64Hash = Convert.ToBase64String(hashBytes);
            // Replace characters for URL safety and take first 8 characters
            hash = base64Hash.Replace("+", "-").Replace("/", "_").Replace("=", "").Substring(0, 8);
        }
        
        // Combine hash + base64 data to create redirect code
        var redirectCode = hash + base64Data;
        
        // Generate the final URL based on environment
        var baseUrl = configuration["RedirectService-ValyrionUrl"];
        //var baseUrl = "https://localhost:7183/";

        return $"{baseUrl}external/r/{redirectCode}";
    }

    public async Task<FileDto> ExportMerchantsAsync(PaginationSearchDto paginationSearchDto)
    {
        var workbook = new XLWorkbook();
        workbook.AddWorksheet("Export");
        var ws = workbook.Worksheet("Export");
        ws.ColumnWidth = 20;
        ws.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;

        ws.Cell("A1").Value = "Id";
        ws.Cell("B1").Value = "Name";
        ws.Cell("C1").Value = "Type";
        ws.Cell("D1").Value = "Model";
        ws.Cell("E1").Value = "Is Customer";
        ws.Cell("F1").Value = "Base Fee";
        ws.Cell("G1").Value = "CPA - Display %";
        ws.Cell("H1").Value = "CPA - Interact %";
        ws.Cell("I1").Value = "CPA - Redirect %";
        ws.Cell("J1").Value = "CPA - Display days";
        ws.Cell("K1").Value = "CPA - Interact days";
        ws.Cell("L1").Value = "CPA - Redirect days";
        ws.Cell("M1").Value = "CPM";
        ws.Cell("N1").Value = "CPM - Budget";
        ws.Cell("O1").Value = "Period Actual";
        ws.Cell("P1").Value = "Period Potential";
        ws.Cell("Q1").Value = "Period Actual + Potential";
        ws.Cell("R1").Value = "MKT eff.";
        ws.Cell("S1").Value = "C. Merchants Ready";
        ws.Cell("T1").Value = "Min. Renewal days";
        ws.Cell("U1").Value = "C. Products Ready";
        ws.Cell("V1").Value = "Active discount";
        ws.Cell("W1").Value = "Email - Displays";
        ws.Cell("X1").Value = "Email - Redirects";
        ws.Cell("Y1").Value = "Discount - Displays";
        ws.Cell("Z1").Value = "Discount - Interactions";
        ws.Cell("AA1").Value = "Discount - Redirects";
        ws.Cell("AB1").Value = "Product - Displays";
        ws.Cell("AC1").Value = "Product - Interactions";
        ws.Cell("AD1").Value = "Product - Redirects";
        ws.Cell("AE1").Value = "Partner - Orders";
        ws.Cell("AF1").Value = "Partner - Full Returns";
        ws.Cell("AG1").Value = "Partner - Partial Returns";
        ws.Cell("AH1").Value = "Partner - Revenue";
        ws.Cell("AI1").Value = "Partner - Cost";
        ws.Cell("AJ1").Value = "Partner - ROAS";
        int count = 1;

        var merchantPagination = await GetMerchantsAsync(paginationSearchDto);

        foreach (var merchant in merchantPagination.Merchants)
        {
            decimal roas = 0;
            if (merchant.PartnerRevenue != 0 && merchant.PartnerCost != 0)
            {
                roas = merchant.PartnerRevenue / merchant.PartnerCost;
            }

            count++;
            ws.Cell($"A{count}").Value = merchant.MerchantId;
            ws.Cell($"B{count}").Value = merchant.MerchantName;
            ws.Cell($"C{count}").Value = merchant.Type;
            ws.Cell($"D{count}").Value = merchant.Model;
            ws.Cell($"E{count}").Value = merchant.IsCustomer;
            ws.Cell($"F{count}").Value = merchant.BaseFee;
            ws.Cell($"G{count}").Value = merchant.CPADisplayPercentage;
            ws.Cell($"H{count}").Value = merchant.CPAInteractPercentage;
            ws.Cell($"I{count}").Value = merchant.CPARedirectPercentage;
            ws.Cell($"J{count}").Value = merchant.CPADisplayExposureDays;
            ws.Cell($"K{count}").Value = merchant.CPAInteractExposureDays;
            ws.Cell($"L{count}").Value = merchant.CPARedirectExposureDays;
            ws.Cell($"M{count}").Value = merchant.CPM;
            ws.Cell($"N{count}").Value = merchant.CPMBudget;
            ws.Cell($"O{count}").Value = merchant.Actual;
            ws.Cell($"P{count}").Value = merchant.Potential;
            ws.Cell($"Q{count}").Value = merchant.ActualPotential;
            ws.Cell($"R{count}").Value = merchant.MKTEFF;
            ws.Cell($"S{count}").Value = merchant.CMReady;
            ws.Cell($"T{count}").Value = merchant.MRenewalDays;
            ws.Cell($"U{count}").Value = merchant.CPReady;
            ws.Cell($"V{count}").Value = merchant.ActiveDiscount;
            ws.Cell($"W{count}").Value = merchant.EmailDisplays;
            ws.Cell($"X{count}").Value = merchant.EmailRedirects;
            ws.Cell($"Y{count}").Value = merchant.DiscountDisplays;
            ws.Cell($"Z{count}").Value = merchant.DiscountInteracts;
            ws.Cell($"AA{count}").Value = merchant.DiscountRedirects;
            ws.Cell($"AB{count}").Value = merchant.ProductDisplays;
            ws.Cell($"AC{count}").Value = merchant.ProductInteracts;
            ws.Cell($"AD{count}").Value = merchant.ProductRedirects;
            ws.Cell($"AE{count}").Value = merchant.PartnerOrders;
            ws.Cell($"AF{count}").Value = merchant.PartnerOrdersFullReturn;
            ws.Cell($"AG{count}").Value = merchant.PartnerOrdersPartialReturn;
            ws.Cell($"AH{count}").Value = merchant.PartnerRevenue;
            ws.Cell($"AI{count}").Value = merchant.PartnerCost;
            ws.Cell($"AJ{count}").Value = Math.Round(roas, 1);
        }

        var path = $"Export {paginationSearchDto.From:yyyy-MM-dd} - {paginationSearchDto.To:yyyy-MM-dd}.xlsx";
        workbook.SaveAs(path);
        var bytes = await File.ReadAllBytesAsync(path);
        var file = Convert.ToBase64String(bytes);
        File.Delete(path);
        return new FileDto
        {
            Data = "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64," + file,
            Name = path
        };
    }

    public async Task<CampaignPaginationDto> GetCampaignsAsync(PaginationSearchDto merchantSearchDto,
        bool forceUpdate = false)
    {
        var partnerId = partnerContext.PartnerId;
        merchantSearchDto.From = DateTimeExtensions.SetTimeToMinimum(merchantSearchDto.From);
        merchantSearchDto.To = DateTimeExtensions.SetTimeToMax(merchantSearchDto.To);

        //Hours
        var cachingHours = 4;
        var campaigns = await campaignService.GetAllFullAsync(partnerId);
        var cacheKey = $"GeneralService_GetCampaignsAsync_PartnerId:{partnerId}_{configuration["Environment"]}";
        var campaignPaginationDto = await cacheService.GetData<CampaignPaginationDto>(cacheKey, true) ??
                                    new CampaignPaginationDto();
        campaignPaginationDto.HasPreviousPage = merchantSearchDto.Page > 0;
        campaignPaginationDto.HasNextPage = (merchantSearchDto.Page + 1) * merchantSearchDto.Size < campaigns.Count;
        bool newData = false;
        var daysToUpdateAfterSendingEnd = DateTime.UtcNow.AddMonths(-1);
        var toOldToUpdate = DateTime.UtcNow.AddMonths(-6);
        var segments = await segmentService.GetAllAsync();
        
        var queuedCampaigns = await messageService.GetCampaignsInQueue(partnerId);

        // Remove inactive campaigns from cache when force updating
        if (forceUpdate)
        {
            var activeCampaignIds = campaigns.Select(c => c.Id).ToHashSet();
            var campaignsToRemove = campaignPaginationDto.Campaigns
                .Where(c => !activeCampaignIds.Contains(c.CampaignId))
                .ToList();
            
            if (campaignsToRemove.Any())
            {
                foreach (var inactiveCampaign in campaignsToRemove)
                {
                    campaignPaginationDto.Campaigns.Remove(inactiveCampaign);
                }
                newData = true; // Force cache update since we removed inactive campaigns
                
                logger.ForContext("service_name", GetType().Name)
                    .Debug("Removed {Count} inactive campaigns from pagination cache for partner {PartnerId}", 
                           campaignsToRemove.Count, partnerId);
            }
        }

        campaignPaginationDto.Size = campaigns.Count;
        foreach (var campaign in campaigns)
        {
            var campaignPaginationDataDto =
                campaignPaginationDto.Campaigns.FirstOrDefault(a => a.CampaignId == campaign.Id);
            if (campaignPaginationDataDto == null)
            {
                campaignPaginationDataDto = new CampaignPaginationDataDto
                {
                    CampaignId = campaign.Id,
                    CampaignName = campaign.Name,
                    SubjectLine = campaign?.Subject ?? "None",
                    Audience = 0,
                    Delivered = 0,
                    Unsubscribe = 0,
                    UnsubscribeRate = 0,
                    Opens = 0,
                    UniqueOpens = 0,
                    TotalExposures = 0,
                    OpenRate = 0,
                    Redirects = 0,
                    UniqueRedirects = 0,
                    ClickThroughRate = 0,
                    Segment = "",
                    CampaignType = campaign.CampaignType,
                    SendingStart = null,
                    SendingEnd = null,
                    LastModifiedDate = campaign.LastModifiedDate,
                    Status = StringConverters.ConvertCampaignStatus(campaign.Status)
                };
                campaignPaginationDto.Campaigns.Add(campaignPaginationDataDto);
                newData = true;
            }
            
            if(queuedCampaigns.Any(a => a.Id == campaignPaginationDataDto.CampaignId))
            {
                campaignPaginationDataDto.Status = "In Queue";
            }

            campaignPaginationDataDto.CampaignName = campaign.Name;
            var openRate = campaign.FkFilter?.OpenRate;
            if (campaign.FkSegmentId != null)
            {
                var segment = segments.SingleOrDefault(a => a.Id == campaign.FkSegmentId);
                if (segment != null)
                {
                    campaignPaginationDataDto.Segment = segment.Name;
                }
            }
            else if (openRate == null)
            {
                var engaged = campaign.FkFilter?.OnlyUnengaged;
                if (engaged == 0)
                {
                    campaignPaginationDataDto.Segment = "Engaged ";
                    //campaignPaginationDataDto.Engagement = "Engaged";
                }
                else if (engaged == 1)
                {
                    campaignPaginationDataDto.Segment = "Unengaged ";
                    //campaignPaginationDataDto.Engagement = "Unengaged";
                }
                else
                {
                    campaignPaginationDataDto.Segment = "All contacts ";
                    //campaignPaginationDataDto.Engagement = "All contacts";
                }
            }
            else
            {
                campaignPaginationDataDto.Segment = $"{openRate} ";
                //campaignPaginationDataDto.Engagement = openRate;
            }

            if (campaign.FkSegmentId == null)
            {
                var gender = campaign.FkFilter?.FilterValues.FirstOrDefault(a => a.FkFilterTemplateId == 12);
                if (gender != null)
                {
                    if (gender.Value == "")
                    {
                        campaignPaginationDataDto.Segment += "Male";
                        //campaignPaginationDataDto.Gender = "Male";
                    }
                    else if (gender.Value == "yes")
                    {
                        campaignPaginationDataDto.Segment += "Female";
                        //campaignPaginationDataDto.Gender = "Female";
                    }
                    else
                    {
                        campaignPaginationDataDto.Segment += "Unknown";
                        //campaignPaginationDataDto.Gender = "Unknown";
                    }
                }
            }

            if ((campaign.Status != null || campaignPaginationDataDto.Audience != 0) && forceUpdate &&
                campaign.CreatedDate > toOldToUpdate &&
                (campaignPaginationDataDto.SendingStart == null || campaignPaginationDataDto.SendingEnd == null ||
                 campaignPaginationDataDto.SendingEnd >= daysToUpdateAfterSendingEnd))
            {
                newData = true;
                Console.WriteLine($"{campaign.Name} Updating data created {campaign.CreatedDate}");
                //Only get audience first time
                if (campaignPaginationDataDto.Audience == 0 || campaign.Status == null)
                {
                    campaignPaginationDataDto.Audience = await messageService.Messages(campaign.Id);
                }

                // Delivered
                campaignPaginationDataDto.Delivered = await messageService.Messages(campaign.Id, "sent");
                if (campaignPaginationDataDto.Delivered != 0)
                {
                    // SendingStart
                    if (campaignPaginationDataDto.SendingStart == null)
                    {
                        campaignPaginationDataDto.SendingStart =
                            (await messageService.MessagesSent(campaign.Id, true))?.ProcessedDate;
                    }

                    // SendingEnd
                    if (campaignPaginationDataDto.SendingEnd == null)
                    {
                        var totalQueueLeft = await messageService.QueueSizeByCampaignId(campaign.Id);
                        if (totalQueueLeft < 20)
                        {
                            campaignPaginationDataDto.SendingEnd =
                                (await messageService.MessagesSent(campaign.Id, false))?.ProcessedDate;
                            if (campaign.Status != "done")
                            {
                                campaign.Status = "done";
                                await campaignService.UpdateAsync(campaign);
                                //Update audience for last time
                                campaignPaginationDataDto.Audience = await messageService.Messages(campaign.Id);
                            }
                        }
                    }

                    //Get merchants in mail
                    var elasticCampaignsMailsOpens = await elasticCampaignMailOpenService.OpensData(campaign.Id);
                    var merchantsCount = elasticCampaignsMailsOpens?.Shop_event.Webshop_id.Count ?? 0;
                    campaignPaginationDataDto.Opens =
                        (int) await elasticCampaignMailOpenService.Opens(null, null, null, campaign.Id);
                    campaignPaginationDataDto.UniqueOpens =
                        (int) await elasticCampaignMailOpenService.UniqueOpens(null, null, null, campaign.Id);
                    campaignPaginationDataDto.OpenRate = campaignPaginationDataDto.Delivered > 0 
                        ? Math.Round(campaignPaginationDataDto.UniqueOpens / (decimal) campaignPaginationDataDto.Delivered * 100, 2)
                        : 0;
                    campaignPaginationDataDto.TotalExposures = campaignPaginationDataDto.Opens * merchantsCount;
                    campaignPaginationDataDto.Redirects =
                        (int) await elasticCampaignMailClickService.Clicks(null, null, null, campaign.Id);
                    campaignPaginationDataDto.UniqueRedirects =
                        (int) await elasticCampaignMailClickService.UniqueClicks(null, null, null, campaign.Id);
                    campaignPaginationDataDto.Unsubscribe = await customerService.UnsubscribedCustomers(campaign.Id);
                    campaignPaginationDataDto.UnsubscribeRate = campaignPaginationDataDto.Delivered > 0
                        ? Math.Round(campaignPaginationDataDto.Unsubscribe / (decimal) campaignPaginationDataDto.Delivered * 100, 2)
                        : 0;
                    campaignPaginationDataDto.ClickThroughRate = campaignPaginationDataDto.UniqueOpens > 0
                        ? Math.Min(100, Math.Round(campaignPaginationDataDto.UniqueRedirects / (decimal) campaignPaginationDataDto.UniqueOpens * 100, 2))
                        : 0;
                }
            }
        }

        if (newData)
        {
            cacheService.SetData(cacheKey, campaignPaginationDto, TimeSpan.FromHours(cachingHours), true);
        }

        //Search
        merchantSearchDto.Search = merchantSearchDto.Search.ToLowerInvariant();
        campaignPaginationDto.Campaigns = campaignPaginationDto.Campaigns
            .Where(a => (merchantSearchDto.Search == "" || a.CampaignId.ToString() == merchantSearchDto.Search ||
                         a.CampaignName.ToLowerInvariant().Contains(merchantSearchDto.Search)) &&
                        a.CampaignType == merchantSearchDto.Type).ToList();

        //var test = campaignPaginationDto.Campaigns.Where(a => a.SendingStart == null || a.SendingEnd == null || (a.SendingStart >= merchantSearchDto.From && a.SendingStart <= merchantSearchDto.To) || (a.SendingEnd >= merchantSearchDto.From && a.SendingEnd <= merchantSearchDto.To))
        campaignPaginationDto.Campaigns = campaignPaginationDto.Campaigns.Where(a =>
                a.SendingStart == null || a.SendingEnd == null ||
                ((a.SendingStart >= merchantSearchDto.From && a.SendingStart <= merchantSearchDto.To) ||
                 a.SendingEnd >= merchantSearchDto.From && a.SendingEnd <= merchantSearchDto.To))
            .ToList();

        //Filter
        foreach (var filter in merchantSearchDto.Filters)
        {
            if (filter.Value != null)
            {
                switch (filter.Field)
                {
                    case "segment":
                        campaignPaginationDto.Campaigns = campaignPaginationDto.Campaigns
                            .Where(a => a.Segment.Equals(filter.Value, StringComparison.InvariantCultureIgnoreCase)).ToList();
                        break;

                    /*case "engagement":
                        campaignPaginationDto.Campaigns = campaignPaginationDto.Campaigns.Where(a => a.Engagement.ToLowerInvariant() == filter.Value.ToLowerInvariant()).ToList();
                        break;
                    case "gender":
                        campaignPaginationDto.Campaigns = campaignPaginationDto.Campaigns.Where(a => a.Gender.ToLowerInvariant() == filter.Value.ToLowerInvariant()).ToList();
                        break;*/
                    case "status":
                        campaignPaginationDto.Campaigns = campaignPaginationDto.Campaigns
                            .Where(a => (a.Status?.ToLowerInvariant() ?? "draft").Equals(filter.Value, StringComparison.InvariantCultureIgnoreCase)).ToList();
                        break;
                }
            }
        }

        //Sort
        switch (merchantSearchDto.SortName)
        {
            case "campaignId":
                campaignPaginationDto.Campaigns = merchantSearchDto.SortOrder == "asc"
                    ? campaignPaginationDto.Campaigns.OrderBy(a => a.CampaignId).ToList()
                    : campaignPaginationDto.Campaigns.OrderByDescending(a => a.CampaignId).ToList();
                break;
            /*case "engagement":
                campaignPaginationDto.Campaigns = merchantSearchDto.SortOrder == "asc"
                    ? campaignPaginationDto.Campaigns.OrderBy(a => a.Engagement).ToList()
                    : campaignPaginationDto.Campaigns.OrderByDescending(a => a.Engagement).ToList();
                break;
            case "gender":
                campaignPaginationDto.Campaigns = merchantSearchDto.SortOrder == "asc"
                    ? campaignPaginationDto.Campaigns.OrderBy(a => a.Gender).ToList()
                    : campaignPaginationDto.Campaigns.OrderByDescending(a => a.Gender).ToList();
                break;*/
            case "segment":
                campaignPaginationDto.Campaigns = merchantSearchDto.SortOrder == "asc"
                    ? campaignPaginationDto.Campaigns.OrderBy(a => a.Segment).ToList()
                    : campaignPaginationDto.Campaigns.OrderByDescending(a => a.Segment).ToList();
                break;
            case "campaignName":
                campaignPaginationDto.Campaigns = merchantSearchDto.SortOrder == "asc"
                    ? campaignPaginationDto.Campaigns.OrderBy(a => a.CampaignName).ToList()
                    : campaignPaginationDto.Campaigns.OrderByDescending(a => a.CampaignName).ToList();
                break;
            case "audience":
                campaignPaginationDto.Campaigns = merchantSearchDto.SortOrder == "asc"
                    ? campaignPaginationDto.Campaigns.OrderBy(a => a.Audience).ToList()
                    : campaignPaginationDto.Campaigns.OrderByDescending(a => a.Audience).ToList();
                break;
            case "delivered":
                campaignPaginationDto.Campaigns = merchantSearchDto.SortOrder == "asc"
                    ? campaignPaginationDto.Campaigns.OrderBy(a => a.Delivered).ToList()
                    : campaignPaginationDto.Campaigns.OrderByDescending(a => a.Delivered).ToList();
                break;
            case "sendingStart":
                campaignPaginationDto.Campaigns = merchantSearchDto.SortOrder == "asc"
                    ? campaignPaginationDto.Campaigns.OrderBy(a => a.SendingStart).ToList()
                    : campaignPaginationDto.Campaigns.OrderByDescending(a => a.SendingStart).ToList();
                break;
            case "sendingEnd":
                campaignPaginationDto.Campaigns = merchantSearchDto.SortOrder == "asc"
                    ? campaignPaginationDto.Campaigns.OrderBy(a => a.SendingEnd).ToList()
                    : campaignPaginationDto.Campaigns.OrderByDescending(a => a.SendingEnd).ToList();
                break;
            case "openRate":
                campaignPaginationDto.Campaigns = merchantSearchDto.SortOrder == "asc"
                    ? campaignPaginationDto.Campaigns.OrderBy(a => a.OpenRate).ToList()
                    : campaignPaginationDto.Campaigns.OrderByDescending(a => a.OpenRate).ToList();
                break;
            case "clickThroughRate":
                campaignPaginationDto.Campaigns = merchantSearchDto.SortOrder == "asc"
                    ? campaignPaginationDto.Campaigns.OrderBy(a => a.ClickThroughRate).ToList()
                    : campaignPaginationDto.Campaigns.OrderByDescending(a => a.ClickThroughRate).ToList();
                break;
            case "unsubscribeRate":
                campaignPaginationDto.Campaigns = merchantSearchDto.SortOrder == "asc"
                    ? campaignPaginationDto.Campaigns.OrderBy(a => a.UnsubscribeRate).ToList()
                    : campaignPaginationDto.Campaigns.OrderByDescending(a => a.UnsubscribeRate).ToList();
                break;
            default:
                campaignPaginationDto.Campaigns =
                    campaignPaginationDto.Campaigns.OrderByDescending(a => a.CampaignId).ToList();
                break;
        }

        var start = merchantSearchDto.Size * merchantSearchDto.Page;
        campaignPaginationDto.Size = campaignPaginationDto.Campaigns.Count;
        campaignPaginationDto.Campaigns =
            campaignPaginationDto.Campaigns.Skip(start).Take(merchantSearchDto.Size).ToList();
        return campaignPaginationDto;
    }

    public async Task<List<CampaignPaginationDataDto>> GetCampaignsQueuedAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var queuedCampaigns = await messageService.GetCampaignsInQueue(partnerId);
        var campaignIds = queuedCampaigns.Select(a => a.Id).ToList();
        
        logger.Error(queuedCampaigns.Count + " campaigns in queue");

        logger.Error("CampaignsQueued: {CampaignIds}", campaignIds);

        var segments = await segmentService.GetAllAsync();
        
        var campaigns = await campaignService.GetByIdsAsync(campaignIds);
        var campaignPaginationDataDtos = new List<CampaignPaginationDataDto>();
        foreach (var campaign in campaigns)
        {
            var queuedCampaign = queuedCampaigns.FirstOrDefault(a => a.Id == campaign.Id);

            var campaignPaginationDataDto = new CampaignPaginationDataDto
            {
                CampaignId = campaign.Id,
                CampaignName = campaign.Name,
                SubjectLine = campaign?.Subject ?? "None",
                Audience = queuedCampaign?.Audience ?? 0,
                Delivered = queuedCampaign?.Delivered ?? 0,
                Unsubscribe = 0,
                UnsubscribeRate = 0,
                Opens = 0,
                UniqueOpens = 0,
                TotalExposures = 0,
                OpenRate = 0,
                Redirects = 0,
                UniqueRedirects = 0,
                ClickThroughRate = 0,
                Segment = "",
                CampaignType = campaign.CampaignType.ToString(),
                SendingStart = null,
                SendingEnd = null,
                LastModifiedDate = campaign.LastModifiedDate,
                Status = "In Queue",
                Priority = queuedCampaign?.Priority
            };


            var openRate = campaign.FkFilter?.OpenRate;
            if (campaign.FkSegmentId != null)
            {
                var segment = segments.SingleOrDefault(a => a.Id == campaign.FkSegmentId);
                if (segment != null)
                {
                    campaignPaginationDataDto.Segment = segment.Name;
                }
            }
            else if (openRate == null)
            {
                var engaged = campaign.FkFilter?.OnlyUnengaged;
                if (engaged == 0)
                {
                    campaignPaginationDataDto.Segment = "Engaged ";
                    //campaignPaginationDataDto.Engagement = "Engaged";
                }
                else if (engaged == 1)
                {
                    campaignPaginationDataDto.Segment = "Unengaged ";
                    //campaignPaginationDataDto.Engagement = "Unengaged";
                }
                else
                {
                    campaignPaginationDataDto.Segment = "All contacts ";
                    //campaignPaginationDataDto.Engagement = "All contacts";
                }
            }
            else
            {
                campaignPaginationDataDto.Segment = $"{openRate} ";
                //campaignPaginationDataDto.Engagement = openRate;
            }

            if (campaign.FkSegmentId == null)
            {
                var gender = campaign.FkFilter?.FilterValues.FirstOrDefault(a => a.FkFilterTemplateId == 12);
                if (gender != null)
                {
                    if (gender.Value == "")
                    {
                        campaignPaginationDataDto.Segment += "Male";
                        //campaignPaginationDataDto.Gender = "Male";
                    }
                    else if (gender.Value == "yes")
                    {
                        campaignPaginationDataDto.Segment += "Female";
                        //campaignPaginationDataDto.Gender = "Female";
                    }
                    else
                    {
                        campaignPaginationDataDto.Segment += "Unknown";
                        //campaignPaginationDataDto.Gender = "Unknown";
                    }
                }
            }

            if (campaignPaginationDataDto.Delivered != 0)
                {
                    // SendingStart
                    if (campaignPaginationDataDto.SendingStart == null)
                    {
                        campaignPaginationDataDto.SendingStart =
                            (await messageService.MessagesSent(campaign.Id, true))?.ProcessedDate;
                    }

                    // SendingEnd
                    if (campaignPaginationDataDto.SendingEnd == null)
                    {
                        var totalQueueLeft = await messageService.QueueSizeByCampaignId(campaign.Id);
                        if (totalQueueLeft == 0)
                        {
                            campaignPaginationDataDto.SendingEnd =
                                (await messageService.MessagesSent(campaign.Id, false))?.ProcessedDate;
                            if (campaign.Status != "done")
                            {
                                campaign.Status = "done";
                                await campaignService.UpdateAsync(campaign);
                                //Update audience for last time
                                campaignPaginationDataDto.Audience = await messageService.Messages(campaign.Id);
                            }
                        }
                    }

                    //Get merchants in mail
                    var elasticCampaignsMailsOpens = await elasticCampaignMailOpenService.OpensData(campaign.Id);
                    var merchantsCount = elasticCampaignsMailsOpens?.Shop_event.Webshop_id.Count ?? 0;
                    campaignPaginationDataDto.Opens =
                        (int) await elasticCampaignMailOpenService.Opens(null, null, null, campaign.Id);
                    campaignPaginationDataDto.UniqueOpens =
                        (int) await elasticCampaignMailOpenService.UniqueOpens(null, null, null, campaign.Id);
                    campaignPaginationDataDto.OpenRate = campaignPaginationDataDto.Delivered > 0 
                        ? Math.Round(campaignPaginationDataDto.UniqueOpens / (decimal) campaignPaginationDataDto.Delivered * 100, 2)
                        : 0;
                    campaignPaginationDataDto.TotalExposures = campaignPaginationDataDto.Opens * merchantsCount;
                    campaignPaginationDataDto.Redirects =
                        (int) await elasticCampaignMailClickService.Clicks(null, null, null, campaign.Id);
                    campaignPaginationDataDto.UniqueRedirects =
                        (int) await elasticCampaignMailClickService.UniqueClicks(null, null, null, campaign.Id);
                    campaignPaginationDataDto.Unsubscribe = await customerService.UnsubscribedCustomers(campaign.Id);
                    campaignPaginationDataDto.UnsubscribeRate = campaignPaginationDataDto.Delivered > 0
                        ? Math.Round(campaignPaginationDataDto.Unsubscribe / (decimal) campaignPaginationDataDto.Delivered * 100, 2)
                        : 0;
                    campaignPaginationDataDto.ClickThroughRate = campaignPaginationDataDto.UniqueOpens > 0
                        ? Math.Min(100, Math.Round(campaignPaginationDataDto.UniqueRedirects / (decimal) campaignPaginationDataDto.UniqueOpens * 100, 2))
                        : 0;
                }


            campaignPaginationDataDtos.Add(campaignPaginationDataDto);
        }
        return campaignPaginationDataDtos;
    }

    public async Task<FileDto> ExportCampaignAsync(PaginationSearchDto paginationSearchDto)
    {
        var workbook = new XLWorkbook();
        workbook.AddWorksheet("Export campaigns");
        var ws = workbook.Worksheet("Export campaigns");
        ws.ColumnWidth = 20;
        ws.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;

        ws.Cell("A1").Value = "Id";
        ws.Cell("B1").Value = "Sending Start";
        ws.Cell("C1").Value = "Sending End";
        ws.Cell("D1").Value = "Segment";
        ws.Cell("E1").Value = "Campaign Name";
        ws.Cell("F1").Value = "Subject Line";
        ws.Cell("G1").Value = "Audience";
        ws.Cell("H1").Value = "Delivered";
        ws.Cell("I1").Value = "Opens";
        ws.Cell("J1").Value = "Unique Opens";
        ws.Cell("K1").Value = "Eksponeringer (*merchant)";
        ws.Cell("L1").Value = "Redirects";
        ws.Cell("M1").Value = "Unique Redirects";
        ws.Cell("N1").Value = "Openrate";
        ws.Cell("O1").Value = "Click-through-rate";
        ws.Cell("P1").Value = "Unsubs";
        ws.Cell("Q1").Value = "Unsub rate";
        int count = 1;

        var merchantPagination = await GetCampaignsAsync(paginationSearchDto);
        foreach (var campaign in merchantPagination.Campaigns)
        {
            count++;
            ws.Cell($"A{count}").Value = campaign.CampaignId;
            ws.Cell($"B{count}").Value = campaign.SendingStart;
            ws.Cell($"C{count}").Value = campaign.SendingEnd;
            ws.Cell($"D{count}").Value = campaign.Segment;
            ws.Cell($"E{count}").Value = campaign.CampaignName;
            ws.Cell($"F{count}").Value = campaign.SubjectLine;
            ws.Cell($"G{count}").Value = campaign.Audience;
            ws.Cell($"H{count}").Value = campaign.Delivered;
            ws.Cell($"I{count}").Value = campaign.Opens;
            ws.Cell($"J{count}").Value = campaign.UniqueOpens;
            ws.Cell($"K{count}").Value = campaign.TotalExposures;
            ws.Cell($"L{count}").Value = campaign.Redirects;
            ws.Cell($"M{count}").Value = campaign.UniqueRedirects;
            ws.Cell($"N{count}").Value = campaign.OpenRate;
            ws.Cell($"O{count}").Value = campaign.ClickThroughRate;
            ws.Cell($"P{count}").Value = campaign.Unsubscribe;
            ws.Cell($"Q{count}").Value = campaign.UnsubscribeRate;
        }

        var path = $"Export campaigns {paginationSearchDto.From:yyyy-MM-dd} - {paginationSearchDto.To:yyyy-MM-dd}.xlsx";
        workbook.SaveAs(path);
        var bytes = await File.ReadAllBytesAsync(path);
        var file = Convert.ToBase64String(bytes);
        File.Delete(path);
        return new FileDto
        {
            Data = "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64," + file,
            Name = path
        };
    }

    public async Task<CampaignStatusDto> GetCampaignsStatusAsync(DateTime from, DateTime to)
    {
        var campaignsPagination = await GetCampaignsAsync(new PaginationSearchDto { 
            From = from, 
            To = to,
            Filters = new List<PaginationSearchFilterDto>(),
            Page = 0,
            Size = 10000,
            SortName = "None",
            SortOrder = "None",
            Search = "",
            Type = "Customer"
        });
        var campaigns = await campaignService.GetAllAsync(partnerContext.PartnerId);
        campaigns = campaigns.Where(a => campaignsPagination.Campaigns.Any(c => c.CampaignId == a.Id)).ToList();
    
        foreach (var campaign in campaigns.Where(c => c.Status == "locked"))
        {
            var totalQueueLeft = await messageService.QueueSizeByCampaignId(campaign.Id);
            if (totalQueueLeft >= 20) continue;
            
            campaign.Status = "done";
            await campaignService.UpdateStatusAsync(campaign.Id, "done");
        }


        return new CampaignStatusDto
        {
            All = campaigns.Count,
            Draft = campaigns.Count(c => c.Status == null || c.Status == "draft"),
            InQueue = campaigns.Count(c => c.Status == "locked"),
            Finished = campaigns.Count(c => c.Status == "done")
        };
    }

    public async Task<CampaignSummaryDto> GetCampaignSummaryAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var currentYear = DateTime.UtcNow.Year;
        
        // Try to get pre-calculated data first
        var preCalculation = await valyrionDbContext.PreCalculations
            .Where(pc => pc.ContextType == "CampaignSummary" 
                        && pc.ContextId == partnerId.ToString() 
                        && pc.CalculationType == currentYear.ToString()
                        && (pc.ValidTo == null || pc.ValidTo > DateTime.UtcNow))
            .OrderByDescending(pc => pc.CalculatedAt)
            .FirstOrDefaultAsync();

        if (preCalculation?.ResultJson != null)
        {
            try
            {
                var cachedResult = JsonSerializer.Deserialize<CampaignSummaryDto>(preCalculation.ResultJson);
                if (cachedResult != null)
                {
                    logger.Information("Retrieved pre-calculated campaign summary for partner {PartnerId}, year {Year}", partnerId, currentYear);
                    return cachedResult;
                }
            }
            catch (JsonException ex)
            {
                logger.Warning(ex, "Failed to deserialize pre-calculated campaign summary, falling back to real-time calculation");
            }
        }

        // Fallback to real-time calculation
        logger.Information("No valid pre-calculated data found, performing real-time campaign summary calculation for partner {PartnerId}", partnerId);
        return await CalculateCampaignSummaryRealTimeAsync();
    }

    public async Task PreCalculateCampaignSummaryAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var currentYear = DateTime.UtcNow.Year;
        var now = DateTime.UtcNow;
        
        logger.Information("Starting pre-calculation of campaign summary for partner {PartnerId}, year {Year}", partnerId, currentYear);
        
        try
        {
            var campaignSummary = await CalculateCampaignSummaryRealTimeAsync();
            
            // Serialize the result to JSON
            var resultJson = JsonSerializer.Serialize(campaignSummary);
            
            // Create new pre-calculation record
            var preCalculation = new PreCalculation
            {
                ContextType = "CampaignSummary",
                ContextId = partnerId.ToString(),
                CalculationType = currentYear.ToString(),
                ResultJson = resultJson,
                ValidFrom = now,
                ValidTo = null, // No expiry, will be overwritten next time
                CalculatedAt = now
            };
            
            // Remove any existing pre-calculations for this context to avoid duplicates
            var existingCalculations = await valyrionDbContext.PreCalculations
                .Where(pc => pc.ContextType == "CampaignSummary" 
                            && pc.ContextId == partnerId.ToString() 
                            && pc.CalculationType == currentYear.ToString())
                .ToListAsync();
                
            if (existingCalculations.Any())
            {
                valyrionDbContext.PreCalculations.RemoveRange(existingCalculations);
            }
            
            valyrionDbContext.PreCalculations.Add(preCalculation);
            await valyrionDbContext.SaveChangesAsync();
            
            logger.Information("Successfully pre-calculated and stored campaign summary for partner {PartnerId}, year {Year}", partnerId, currentYear);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Failed to pre-calculate campaign summary for partner {PartnerId}, year {Year}", partnerId, currentYear);
            throw;
        }
    }

    private async Task<CampaignSummaryDto> CalculateCampaignSummaryRealTimeAsync()
    {
        Console.WriteLine("Starting campaign statistics collection...");
        var overallStopwatch = new Stopwatch();
        var operationStopwatch = new Stopwatch();
        overallStopwatch.Start();

        operationStopwatch.Restart();
        Console.WriteLine("Fetching campaigns...");
        var campaigns = await campaignService.GetAllFromCurrentYearAsync(partnerContext.PartnerId);
        Console.WriteLine($"Fetched {campaigns.Count} campaigns in {operationStopwatch.ElapsedMilliseconds}ms");

        operationStopwatch.Restart();
        Console.WriteLine("Calculating messages sent...");
        var messagesSent = 0;
        var counter = 0;

        foreach (var campaign in campaigns)
        {
            counter++;
            Console.WriteLine($"Campaign {counter} of {campaigns.Count} - {campaign.Name}");
            messagesSent += await messageService.GetMessagesSentCountByCampaignIdThisYear(campaign.Id);
        }
        Console.WriteLine($"Found {messagesSent} messages sent in {operationStopwatch.ElapsedMilliseconds}ms");

        operationStopwatch.Restart();
        Console.WriteLine("Calculating email opens...");
        // Get opens and unique opens within this current year - Get first day of current year
        var currentYear = DateTime.UtcNow.Year;
        var firstDayOfYear = new DateTime(currentYear, 1, 1);
        var opens = (int) await elasticCampaignMailOpenService.OpensByCampaignIds(firstDayOfYear, DateTime.UtcNow,
            campaigns.Select(c => c.Id).ToList());
        var uniqueOpens =
            (int) await elasticCampaignMailOpenService.UniqueOpensByCampaignIds(firstDayOfYear, DateTime.UtcNow,
                campaigns.Select(c => c.Id).ToList());
        var openRate = messagesSent > 0
            ? Math.Round(uniqueOpens / (decimal) messagesSent * 100, 2)
            : 0;
        Console.WriteLine(
            $"Opens: {opens}, Unique opens: {uniqueOpens}, Open rate: {openRate}% in {operationStopwatch.ElapsedMilliseconds}ms");

        operationStopwatch.Restart();
        Console.WriteLine("Calculating redirects...");
        var uniqueRedirects =
            (int) await elasticCampaignMailClickService.UniqueClicksByCampaignIds(firstDayOfYear, DateTime.UtcNow,
                campaigns.Select(c => c.Id).ToList());
        Console.WriteLine($"Unique redirects: {uniqueRedirects} in {operationStopwatch.ElapsedMilliseconds}ms");

        operationStopwatch.Restart();
        Console.WriteLine("Calculating unsubscribes...");
        var unsubscribe =
            await customerService.UnsubscribedCustomersByCampaignIds(campaigns.Select(c => c.Id).ToList());
        var unsubscribeRate = messagesSent > 0
            ? Math.Round(unsubscribe / (decimal) messagesSent * 100, 2)
            : 0;
        Console.WriteLine(
            $"Unsubscribes: {unsubscribe}, Unsubscribe rate: {unsubscribeRate}% in {operationStopwatch.ElapsedMilliseconds}ms");

        operationStopwatch.Restart();
        Console.WriteLine("Calculating click-through rate...");
        var clickThroughRate = uniqueOpens > 0
            ? Math.Min(100, Math.Round(uniqueRedirects / (decimal) uniqueOpens * 100, 2))
            : 0;
        Console.WriteLine($"Click-through rate: {clickThroughRate}% in {operationStopwatch.ElapsedMilliseconds}ms");

        overallStopwatch.Stop();
        Console.WriteLine($"Campaign statistics collection completed in {overallStopwatch.ElapsedMilliseconds}ms");

        return new CampaignSummaryDto
        {   
            AvgOpenRate = openRate,
            AvgClickThroughRate = clickThroughRate,
            AvgUnsubRate = unsubscribeRate,
            TotalEmailsSent = messagesSent
        };
    }

    public async Task UpdateCustomerMeta()
    {
        var now = DateTime.UtcNow;
        var daysAgo = DateTime.UtcNow.AddHours(-2);
        Console.WriteLine("Start getting info");
        //Elastic query
        var campaignDisplayTask = elasticCampaignMailOpenService.OpensData(daysAgo, null, null, null);
        var campaignClicksTask = elasticCampaignMailClickService.ClicksData(daysAgo, null, null, null);
        var discountDisplayTask = elasticDiscountOpenService.OpensData(daysAgo, null, null, null);
        var discountInteractTask =
            elasticDiscountOpenService.OpensData(daysAgo, null, null, null, "discount_details_viewed");
        var discountRedirectTask =
            elasticDiscountOpenService.OpensData(daysAgo, null, null, null, "discount_link_opened");
        var productDisplayTask = elasticShopProductDisplaysService.DisplaysData(daysAgo, null, null, null);
        var productInteractTask = elasticShopProductInteractsService.InteractsData(daysAgo, null, null, null);
        var productRedirectTask = elasticShopProductRedirectsService.RedirectsData(daysAgo, null, null, null);
        //Db query
        // TODO - Add PartnerId to the queries
        var customers = await customerService.GetAllAsync();
        // TODO - Add MerchantId to the queries and remove the Hardcoded value
        var merchants = await merchantService.GetFullAsync();
        merchants = merchants.Where(a =>
            a.MerchantMeta.SingleOrDefault(b => b.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType)
                ?.Value == "cpa").ToList();

        await Task.WhenAll(campaignDisplayTask, campaignClicksTask, discountDisplayTask, discountInteractTask,
            discountRedirectTask, productDisplayTask, productInteractTask, productRedirectTask);
        var campaignDisplay = (await campaignDisplayTask).GroupBy(a => a.Customer.Email).ToDictionary(a => a.Key);
        var campaignClicks = (await campaignClicksTask).GroupBy(a => a.Customer.Email).ToDictionary(a => a.Key);
        var discountDisplay = (await discountDisplayTask).GroupBy(a => a.Customer.Email).ToDictionary(a => a.Key);
        var discountInteract = (await discountInteractTask).GroupBy(a => a.Customer.Email).ToDictionary(a => a.Key);
        var discountRedirect = (await discountRedirectTask).GroupBy(a => a.Customer.Email).ToDictionary(a => a.Key);
        var productDisplay = (await productDisplayTask).GroupBy(a => a.Customer.Email).ToDictionary(a => a.Key);
        var productInteract = (await productInteractTask).GroupBy(a => a.Customer.Email).ToDictionary(a => a.Key);
        var productRedirect = (await productRedirectTask).GroupBy(a => a.Customer.Email).ToDictionary(a => a.Key);

        Console.WriteLine("Done getting info");

        DataTable customerExposuresTable = new DataTable();
        customerExposuresTable.Columns.Add("CustomerId", typeof(long));
        customerExposuresTable.Columns.Add("MerchantId", typeof(int));
        customerExposuresTable.Columns.Add("CustomerExposuresTypeId", typeof(int));
        customerExposuresTable.Columns.Add("Active", typeof(int));
        customerExposuresTable.Columns.Add("ExposureStart", typeof(DateTime));
        customerExposuresTable.Columns.Add("ExposureEnd", typeof(DateTime));
        customerExposuresTable.Columns.Add("LastModifiedDate", typeof(DateTime));

        var count = merchants.Count;
        foreach (var merchant in merchants)
        {
            count--;
            Console.WriteLine($"Merchant's left: {count}");
            foreach (var customer in customers)
            {
                //Campaign Display
                if (campaignDisplay.TryGetValue(customer.Email, out var elasticDatasCampaignDisplay))
                {
                    var elasticData = elasticDatasCampaignDisplay
                        .Where(a => a.Shop_event.Webshop_id.Contains(merchant.Id.ToString()))
                        .MaxBy(a => a.EventObject.Created);
                    if (elasticData != null)
                    {
                        customerExposuresTable.Rows.Add(customer.Id, merchant.Id, 1, 1, elasticData.EventObject.Created,
                            elasticData.EventObject.Created.AddDays(merchant.MerchantPayments.First()
                                .InteractExposureDays), elasticData.EventObject.Created);
                    }
                }

                //Campaign Redirect
                if (campaignClicks.TryGetValue(customer.Email, out var elasticDatasCampaignRedirect))
                {
                    var elasticData = elasticDatasCampaignRedirect
                        .Where(a => a.Shop_event.Webshop_id.Contains(merchant.Id.ToString()))
                        .MaxBy(a => a.EventObject.Created);
                    if (elasticData != null)
                    {
                        customerExposuresTable.Rows.Add(customer.Id, merchant.Id, 2, 1, elasticData.EventObject.Created,
                            elasticData.EventObject.Created.AddDays(merchant.MerchantPayments.First()
                                .InteractExposureDays), elasticData.EventObject.Created);
                    }
                }

                //Discount Display
                if (discountDisplay.TryGetValue(customer.Email, out var elasticDatasDiscountDisplay))
                {
                    var elasticData = elasticDatasDiscountDisplay
                        .Where(a => a.Shop_event.Webshop_id.Contains(merchant.Id.ToString()))
                        .MaxBy(a => a.Event_received);
                    if (elasticData != null)
                    {
                        customerExposuresTable.Rows.Add(customer.Id, merchant.Id, 3, 1, elasticData.Event_received,
                            elasticData.Event_received.AddDays(merchant.MerchantPayments.First().InteractExposureDays),
                            elasticData.Event_received);
                    }
                }

                //Discount Interact
                if (discountInteract.TryGetValue(customer.Email, out var elasticDatasDiscountInteract))
                {
                    var elasticData = elasticDatasDiscountInteract
                        .Where(a => a.Shop_event.Webshop_id.Contains(merchant.Id.ToString()))
                        .MaxBy(a => a.Event_received);
                    if (elasticData != null)
                    {
                        customerExposuresTable.Rows.Add(customer.Id, merchant.Id, 4, 1, elasticData.Event_received,
                            elasticData.Event_received.AddDays(merchant.MerchantPayments.First().InteractExposureDays),
                            elasticData.Event_received);
                    }
                }

                //Discount Redirect
                if (discountRedirect.TryGetValue(customer.Email, out var elasticDatasDiscountRedirect))
                {
                    var elasticData = elasticDatasDiscountRedirect
                        .Where(a => a.Shop_event.Webshop_id.Contains(merchant.Id.ToString()))
                        .MaxBy(a => a.Event_received);
                    if (elasticData != null)
                    {
                        customerExposuresTable.Rows.Add(customer.Id, merchant.Id, 5, 1, elasticData.Event_received,
                            elasticData.Event_received.AddDays(merchant.MerchantPayments.First().InteractExposureDays),
                            elasticData.Event_received);
                    }
                }

                //Product Display
                if (productDisplay.TryGetValue(customer.Email, out var elasticDatasProductDisplay))
                {
                    var elasticData = elasticDatasProductDisplay
                        .Where(a => a.Merchant.Id.Contains(merchant.Id.ToString())).MaxBy(a => a.Action_date);
                    if (elasticData != null)
                    {
                        customerExposuresTable.Rows.Add(customer.Id, merchant.Id, 6, 1, elasticData.Action_date,
                            elasticData.Action_date.AddDays(merchant.MerchantPayments.First().InteractExposureDays),
                            elasticData.Action_date);
                    }
                }

                //Product Interact
                if (productInteract.TryGetValue(customer.Email, out var elasticDatasProductInteract))
                {
                    var elasticData = elasticDatasProductInteract
                        .Where(a => a.Merchant.Id.Contains(merchant.Id.ToString())).MaxBy(a => a.Action_date);
                    if (elasticData != null)
                    {
                        customerExposuresTable.Rows.Add(customer.Id, merchant.Id, 7, 1, elasticData.Action_date,
                            elasticData.Action_date.AddDays(merchant.MerchantPayments.First().InteractExposureDays),
                            elasticData.Action_date);
                    }
                }

                //Product Redirect
                if (productRedirect.TryGetValue(customer.Email, out var elasticDatasProductRedirect))
                {
                    var elasticData = elasticDatasProductRedirect
                        .Where(a => a.Merchant.Id.Contains(merchant.Id.ToString())).MaxBy(a => a.Action_date);
                    if (elasticData != null)
                    {
                        customerExposuresTable.Rows.Add(customer.Id, merchant.Id, 8, 1, elasticData.Action_date,
                            elasticData.Action_date.AddDays(merchant.MerchantPayments.First().InteractExposureDays),
                            elasticData.Action_date);
                    }
                }
            }
        }

        Console.WriteLine("Saving");
        if (customerExposuresTable.Rows.Count > 0)
        {
            using (var command = valyrionDbContext.Database.GetDbConnection().CreateCommand())
            {
                command.CommandTimeout = 1200;
                command.CommandText = "[customer].[Exposures_Import]";
                command.CommandType = CommandType.StoredProcedure;

                var parameter = command.CreateParameter();
                parameter.ParameterName = "@exposures";
                parameter.Value = customerExposuresTable;
                command.Parameters.Add(parameter);
                
                await valyrionDbContext.Database.OpenConnectionAsync();
                try
                {
                    await command.ExecuteNonQueryAsync();
                }
                finally
                {
                    await valyrionDbContext.Database.CloseConnectionAsync();
                }
            }
        }

        valyrionDbContext.ChangeTracker.Clear();
        Console.WriteLine("Done");
    }

    public async Task TopProductsForMerchants()
    {
        var merchants = await merchantService.GetFullAsync();
        var curatedPageEventsProducts = new List<CuratedProduct>();
        var curatedOrderProducts = new List<CuratedProduct>();
        merchants = merchants.Where(a => a.IsMarketingAllowed).ToList();
        var count = merchants.Count;
        foreach (var merchant in merchants)
        {
            count--;
            Console.WriteLine($"Count: {count}");
            var curatedProductEvents =
                await elasticService.PageEventsTopProducts(Convert.ToInt32(merchant.Id), 14, 12);

            if (curatedProductEvents.Count > 0)
            {
                foreach (var curatedProductEvent in curatedProductEvents)
                {
                    var product = await merchantService.GetInAppProduct(curatedProductEvent.Id);
                    if (product != null)
                    {
                        curatedProductEvent.ProductRefDto = product;
                    }
                }

                curatedPageEventsProducts.Add(new CuratedProduct
                {
                    MerchantId = merchant.Id,
                    Products = curatedProductEvents.Where(a => a.ProductRefDto != null).ToList()
                });
            }

            var curatedOrderEvents =
                await elasticService.OrderTopProducts(Convert.ToInt32(merchant.Id), 14, 6);
            if (curatedOrderEvents.Count > 0)
            {
                foreach (var curatedProductEvent in curatedOrderEvents)
                {
                    if (curatedProductEvent.Id != "")
                    {
                        var product = await merchantService.GetInAppProduct(curatedProductEvent.Id);
                        if (product != null)
                        {
                            curatedProductEvent.ProductRefDto = product;
                        }
                    }
                }

                curatedOrderProducts.Add(new CuratedProduct
                {
                    MerchantId = merchant.Id,
                    Products = curatedOrderEvents.Where(a => a.ProductRefDto != null).ToList()
                });
            }
        }

        Console.WriteLine("Saving cache");
        cacheService.SetData("GeneralService_TopProductsForMerchants_PageEvent", curatedPageEventsProducts,
            TimeSpan.FromDays(5), true);
        cacheService.SetData("GeneralService_TopProductsForMerchants_Order", curatedOrderProducts,
            TimeSpan.FromDays(5), true);
    }

    public async Task UpdateOpenRate()
    {
        var messages = await messageService.OpenRate();
        await customerService.UpdateOpenRate(messages);
        valyrionDbContext.ChangeTracker.Clear();
    }

    public async Task<MerchantSummaryDto> GetMerchantsSummaryAsync()
    {
        var merchants = await merchantService.GetAllCustomersAsync();   
        var merchantSummary = new MerchantSummaryDto
        {
            LiveMerchants = merchants.Count(a => a.MerchantMeta.Where(b => b.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType).Any(c => c.Value is "cpa" or "cpm")),
            CpaMerchants = merchants.Count(a => a.MerchantMeta.Where(b => b.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType).Any(c => c.Value == "cpa")),
            CpmMerchants = merchants.Count(a => a.MerchantMeta.Where(b => b.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType).Any(c => c.Value == "cpm"))
        };
        
        return merchantSummary;
    }

    private int ConvertToInt(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            return 0;
        }

        if (int.TryParse(value, out var result))
        {
            return result;
        }

        return 0;
    }

    public async Task<List<ProductRefInternalDto>> GetProductRecommendationsByStrategy(string strategy, string? email, string? age, string? gender)
    {
        var returnData = new List<ProductRefInternalDto>();
        
        try
        {
            var partnerId = partnerContext.PartnerId;

            // Handle customer data retrieval based on available parameters
            Customer? customer = null;
            int? customerAge = null;
            string? customerGender = null;

            if (!string.IsNullOrEmpty(email))
            {
                // Primary path: get customer by email
                customer = await customerService.GetByEmailAsync(email);
                if (customer != null)
                {
                    customerAge = customer.Age;
                    customerGender = customer.Gender;
                }
            }

            // If no customer found via email, or no email provided, use age/gender parameters
            if (customer == null)
            {
                if (int.TryParse(age, out var parsedAge))
                {
                    customerAge = parsedAge;
                }
                customerGender = gender;
            }

            // Fetch products from Recommendation Engine
            var recommendations = await GetRecommendationsAsync(strategy, 10, customerGender, customerAge, partnerId);

            var merchantAgeAndGenderRecommendationEntities = await GetRecommendationsAsync(
                OfferRecommendationTypes.MerchantAgeAndGenderRevenue, 100, customerGender, customerAge, partnerId);
            var merchantAgeAndGenderRecommendations = merchantAgeAndGenderRecommendationEntities
                .SelectMany(r => r.OfferItems ?? new List<OfferRecommendationItemDto>()).ToList();
            merchantAgeAndGenderRecommendations = merchantAgeAndGenderRecommendations.OrderByDescending(r => r.Score).ToList();
            
            // Create merchant score lookup for fast access
            var merchantScoreLookup = merchantAgeAndGenderRecommendations
                .GroupBy(r => r.MerchantId)
                .ToDictionary(g => g.Key, g => (decimal)g.Max(r => r.Score)); // Use max score per merchant and cast to decimal
            
            if (recommendations.Count > 0)
            {
                // Get all product IDs from recommendations
                var productIds = recommendations
                    .SelectMany(r => r.OfferItems ?? new List<OfferRecommendationItemDto>())
                    .Where(item => item.OfferType == "Product")
                    .Select(item => item.OfferId)
                    .Distinct()
                    .ToList();

                if (productIds.Count > 0)
                {
                    // Batch fetch all products at once instead of one by one
                    var products = await merchantService.GetProductByProductIdMultiple(productIds);
                    
                    // Create a lookup for faster access
                    var productLookup = products
                        .Where(p => p.Price.HasValue)
                        .ToDictionary(p => p.Id, p => p);

                    // Process products in parallel for better performance
                    var tasks = productIds
                        .Where(id => productLookup.ContainsKey(id))
                        .Select(async productId =>
                        {
                            var product = productLookup[productId];
                            
                            // Get image URL efficiently
                            string imageUrl = await GetProductImageUrlAsync(product);
                            
                            // Only return products with valid images
                            if (!string.IsNullOrEmpty(imageUrl))
                            {
                                // Get merchant score for ordering (default to 0 if not found)
                                var merchantScore = merchantScoreLookup.GetValueOrDefault(product.FkMerchantId, 0);
                                
                                return new ProductRefInternalDto
                                {
                                    Id = product.InternalProductId,
                                    MerchantId = product.FkMerchantId,
                                    MerchantName = product.FkMerchant.Name,
                                    Name = product.Name,
                                    Description = product.Description,
                                    Categories = product.Categories,
                                    IncludesVariants = false,
                                    NormalPrice = product.RegularPrice ?? 0,
                                    SalePrice = product.Price ?? 0,
                                    OnSale = product.Price.HasValue && product.Price.Value < product.RegularPrice.Value,
                                    ProductUrl = product.Permalink,
                                    ImageSrc = imageUrl,
                                    Score = (double?)merchantScore
                                };
                            }
                            return null;
                        });

                    var results = await Task.WhenAll(tasks);
                    var validResults = results.Where(r => r != null).ToList();
                    
                    // Implement round-robin distribution by merchant score
                    returnData = DistributeProductsRoundRobin(validResults);
                }
            }
        }
        catch (Exception e)
        {
            logger.Error(e, "Error getting product recommendations for strategy {Strategy}", strategy);
            throw;
        }

        return returnData;
    }

    private List<ProductRefInternalDto> DistributeProductsRoundRobin(List<ProductRefInternalDto> products)
    {
        // Group products by merchant and order merchants by their highest score
        var merchantGroups = products
            .GroupBy(p => p.MerchantId)
            .OrderByDescending(g => g.Max(p => p.Score ?? 0))
            .Select(g => new
            {
                MerchantId = g.Key,
                MerchantScore = g.Max(p => p.Score ?? 0),
                Products = g.OrderByDescending(p => p.Score ?? 0)
                           .ThenBy(p => p.SalePrice) // Secondary sort by price
                           .ToList()
            })
            .ToList();

        var result = new List<ProductRefInternalDto>();
        var maxProductsPerMerchant = merchantGroups.Max(g => g.Products.Count);

        // Round-robin distribution: take one product from each merchant in order, then repeat
        for (int round = 0; round < maxProductsPerMerchant; round++)
        {
            foreach (var merchantGroup in merchantGroups)
            {
                if (round < merchantGroup.Products.Count)
                {
                    result.Add(merchantGroup.Products[round]);
                }
            }
        }

        return result;
    }

    public async Task IPAndDomainWarmupAutomation(int partnerId)
    {
        var hourlyMailLimitSetting = await settingService.GetSettingAsync(partnerId, "HourlyMailLimit");

        if (hourlyMailLimitSetting == null) return;
        
        if(hourlyMailLimitSetting.LastModifiedDate.AddDays(1) > DateTime.UtcNow) return;

        var warmupSteps = new[] { 20, 28, 39, 55, 77, 108, 151, 211, 295, 413, 579, 810, 1000, 1587, 2222, 3111, 4356, 6000 };
        var hourlyMailLimitInt = Convert.ToInt32(hourlyMailLimitSetting.Value);

        if (hourlyMailLimitInt < 6000)
        {
            var currentIndex = Array.IndexOf(warmupSteps, hourlyMailLimitInt);
            if (currentIndex >= 0 && currentIndex < warmupSteps.Length - 1)
            {
                hourlyMailLimitSetting.Value = warmupSteps[currentIndex + 1].ToString();
                hourlyMailLimitSetting.LastModifiedDate = DateTime.UtcNow;
                await settingService.UpdateSettingsAsync(hourlyMailLimitSetting);
            }
        }
    }

    public Task<bool> SendFeedbackAsync(string email, string feedback)
    {
        throw new NotImplementedException();
    }

    private async Task<List<OfferRecommendationDto>> GetRecommendationsAsync(string strategy, int numberOfRecommendationItems, string? gender, int? age, int partnerId)
    {
        /*var httpClient = httpClientFactory.CreateClient();
        //var baseUrl = configuration["ValyrionServices-BaseUrl"] ?? "https://localhost:7001";
        var baseUrl = "https://localhost:7001";
        var requestUrl = $"{baseUrl}/OfferRecommendation?strategy={strategy}&numberOfRecommendationItems={numberOfRecommendationItems}&gender={gender}";

        if (age.HasValue)
        {
            requestUrl += $"&age={age}";
        }

        var response = await httpClient.GetAsync(requestUrl);
        if (response.IsSuccessStatusCode)
        {
            var jsonContent = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<List<OfferRecommendationDto>>(jsonContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            }) ?? new List<OfferRecommendationDto>();
        }*/

        var cacheKey = $"GeneralService_GetRecommendationsAsync_{strategy}_{numberOfRecommendationItems}_{gender}_{age}_{partnerId}";
        var recommendations = await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30); // Shorter cache for more dynamic recommendations
            entry.SlidingExpiration = TimeSpan.FromMinutes(10); // Refresh if accessed within 10 minutes
            var recommendations = await recommendationService.GetRecommendationEntitiesAsync(strategy, numberOfRecommendationItems, gender, age);
            return recommendations;
        });

        return recommendations;
    }

    private Task<string> GetProductImageUrlAsync(Product product)
    {
        // Use curated image if available
        if (!string.IsNullOrEmpty(product.CuratedImage))
        {
            return Task.FromResult(product.CuratedImage);
        }

        // Parse product images efficiently
        try
        {
            if (string.IsNullOrEmpty(product.ProductImages))
            {
                return Task.FromResult(string.Empty);
            }

            var images = JsonSerializer.Deserialize<List<string>>(product.ProductImages);
            return Task.FromResult(images?.FirstOrDefault() ?? string.Empty);
        }
        catch
        {
            return Task.FromResult(string.Empty);
        }
    }
}