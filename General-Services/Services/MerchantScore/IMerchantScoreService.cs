using General_Services.Models.Models;
using General_Services.Models.Models.Scores;
using Shared.Dto.ProductScore;

namespace General_Services.Services.MerchantScore
{
    public interface IMerchantScoreService
    {
        Task<List<MerchantScoreDto>> GetMerchantScoreAsync(string email);
        Task<Dictionary<string, List<MerchantScoreDto>>> GetMerchantScoresByEmailsAsync(List<string> emails);
        Task<List<ProductScoreDto>> GetProductScoreAsync(string email);
        Task<List<ProductScoreDto>> GetPriceDropScoresAsync(string email);
    }
}