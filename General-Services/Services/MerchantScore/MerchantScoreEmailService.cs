using System.Diagnostics;
using Audience.Services.Audience;
using AutoMapper;
using Customer_Services.Models.ModelsDal.Customer;
using General_Services.Models.Models;
using General_Services.Models.Models.Scores;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Shared.Dto.Curated.Product;
using Shared.Elastic.Elastic;
using Shared.Models.Customer;
using Shared.Models.Merchant;
using Shared.Services.MerchantRelevance;
using Shared.Services.Partner;
using Webshop.Webshop;
using MerchantAssetType = Shared.Models.Merchant.Assets.MerchantAssetType;
using System.Text.Json;
using Recommendation_Services.Models;
using Shared.Constants;
using Recommendation_Services.Services;
using Shared.Dto.ProductScore;

namespace General_Services.Services.MerchantScore;

public class MerchantScoreService(
    ICustomerService customerService,
    IMerchantService merchantService,
    IElasticService elasticService,
    IMerchantRelevanceService merchantRelevanceService,
    IMemoryCache memoryCache,
    MerchantDbContext merchantDbContext,
    IPartnerContext partnerContext,
    IOfferRecommendationService recommendationService)
    : IMerchantScoreService
{
    
    private const int AgeInterval = 10;
    
    public async Task<List<MerchantScoreDto>> GetMerchantScoreAsync(string email)
    {
        var partnerId = partnerContext.PartnerId;
        // Get contact by email
        var contact = await customerService.GetCustomerForMerchantRelevance(email);
        var cacheKey = $"MerchantService_GetCuratedMerchants_{partnerId}";
        // Get curated merchants
        var merchants = await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(6);
            return await merchantDbContext.Merchants
                .Include(a => a.MerchantMeta)
                .Include(a => a.MerchantAssets)
                .Where(a => a.IsMarketingAllowed && a.FkPartnerId == partnerId &&
                            a.MerchantMeta.Any(b => b.FkMerchantMetaTypeName == MerchantMetaTypeNames.MarketingStatus &&
                                                    b.Value.Contains("curated merchants")) &&
                            a.MerchantMeta.Any(b => b.FkMerchantMetaTypeName == MerchantMetaTypeNames.TagLine) &&
                            a.MerchantAssets.Any(b => b.FkMerchantAssetTypeId == (int) MerchantAssetType.CuratedMerchant && b.Active))
                .Select(a => new
                {
                    a.Id,
                    a.Name,
                    a.Url,
                    TagLine = a.MerchantMeta.FirstOrDefault(b => b.FkMerchantMetaTypeName == MerchantMetaTypeNames.TagLine).Value,
                    ImageUrl = a.MerchantAssets.FirstOrDefault(b => b.FkMerchantAssetTypeId == (int) MerchantAssetType.CuratedMerchant && b.Active).Src,
                    a.DisplayName
                })
                .ToListAsync();
        });

        // Get top merchants by relevance
        var topMerchants = await merchantRelevanceService.GetMerchantRelevance(contact, AgeInterval, true);
        var returnables = new List<MerchantScoreDto>();
        foreach (var merchantRelevance in topMerchants)
        {
            var merchant = merchants?.FirstOrDefault(a => a.Id == merchantRelevance.MerchantId);

            if (merchant != null)
            {
                returnables.Add(new MerchantScoreDto
                {
                    Id = merchantRelevance.MerchantId,
                    Name = !string.IsNullOrEmpty(merchant.DisplayName) ? merchant.DisplayName : merchant.Name,
                    Url = merchant.Url,
                    TagLine = merchant.TagLine,
                    ActiveExposure = merchantRelevance.ActiveExposures,
                    Sum = merchantRelevance.CostPrImpression,
                    ImageUrl = merchant.ImageUrl
                });
            }
        }

        return returnables;
    }
    
    public async Task<Dictionary<string, List<MerchantScoreDto>>> GetMerchantScoresByEmailsAsync(List<string> emails)
    {
        var results = new Dictionary<string, List<MerchantScoreDto>>();
        var customers = await customerService.GetCustomersForMerchantRelevance(emails);
        if(customers.Count == 0)
            return results;
        
        // Get curated merchants
        var merchants = await memoryCache.GetOrCreateAsync("MerchantService_GetCuratedMerchants", async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(6);
            return await merchantDbContext.Merchants
                .Include(a => a.MerchantMeta)
                .Include(a => a.MerchantAssets)
                .Where(a => a.IsMarketingAllowed &&
                            a.MerchantMeta.Any(b =>
                                b.FkMerchantMetaTypeName == MerchantMetaTypeNames.MarketingStatus &&
                                b.Value.Contains("curated merchants")) &&
                            a.MerchantMeta.Any(b =>
                                b.FkMerchantMetaTypeName == MerchantMetaTypeNames.TagLine) &&
                            a.MerchantAssets.Any(b =>
                                b.FkMerchantAssetTypeId == (int) MerchantAssetType.CuratedMerchant && b.Active))
                .Select(a => new
                {
                    a.Id,
                    a.Name,
                    a.Url,
                    TagLine = a.MerchantMeta
                        .FirstOrDefault(b => b.FkMerchantMetaTypeName == MerchantMetaTypeNames.TagLine).Value,
                    ImageUrl = a.MerchantAssets.FirstOrDefault(b =>
                        b.FkMerchantAssetTypeId == (int) MerchantAssetType.CuratedMerchant && b.Active).Src,
                    a.DisplayName
                })
                .ToListAsync();
        });

        var stopWatch = new Stopwatch();
        var merchantRelevanceList = await merchantRelevanceService.GetMerchantRelevanceForBatch(customers, AgeInterval, true);
        stopWatch.Stop();
        Console.WriteLine($"Time to get merchant relevance for {emails.Count} emails: {stopWatch.ElapsedMilliseconds} ms");

        stopWatch.Restart();
        foreach (var (key, relevantMerchants) in merchantRelevanceList)
        {
            var scores = new List<MerchantScoreDto>();
            foreach (var relevantMerchant in relevantMerchants)
            {
                var merchant = merchants?.FirstOrDefault(a => a.Id == relevantMerchant.MerchantId);
                if (merchant != null)
                {
                    scores.Add(new MerchantScoreDto
                    {
                        Id = relevantMerchant.MerchantId,
                        Name = !string.IsNullOrEmpty(merchant.DisplayName) ? merchant.DisplayName : merchant.Name,
                        Url = merchant.Url,
                        TagLine = merchant.TagLine,
                        ActiveExposure = relevantMerchant.ActiveExposures,
                        Sum = relevantMerchant.CostPrImpression,
                        ImageUrl = merchant.ImageUrl
                    });
                }
            }

            lock (results)
            {
                results[key] = scores;
            }
        }
        stopWatch.Stop();
        Console.WriteLine($"Time to map merchant relevance for {emails.Count} emails: {stopWatch.ElapsedMilliseconds} ms");
        
        // Fetch scores in parallel with throttling
        /*var semaphore = new SemaphoreSlim(10); // Adjust degree of parallelism
        var tasks = emails.Select(async email =>
        {
            await semaphore.WaitAsync();
            try
            {
                // Get contact by email
                var contact = mapper.Map<Customer?, CustomerDto?>(customers.FirstOrDefault(a => a.Email == email));

                // Get top merchants by relevance
                var topMerchants = await merchantRelevanceService.GetMerchantRelevance(contact, 4, true);
                var scores = new List<MerchantScoreDto>();
                
            }
            finally
            {
                semaphore.Release();
            }
        });

        await Task.WhenAll(tasks);*/
        return results;
    }


    public async Task<List<ProductScoreDto>> GetProductScoreAsync(string email)
    {
        return await GetProductScoresInternalAsync(email, OfferRecommendationTypes.CuratedProducts);
    }

    public async Task<List<ProductScoreDto>> GetPriceDropScoresAsync(string email)
    {
        return await GetProductScoresInternalAsync(email, OfferRecommendationTypes.CuratedProductsPriceDrop);
    }

    private async Task<List<ProductScoreDto>> GetProductScoresInternalAsync(string email, string recommendationType)
    {
        var returnData = new List<ProductScoreDto>();
        
        try
        {
            // Get customer information for demographics
            var contact = await customerService.GetCustomerForMerchantRelevance(email);
            var gender = contact?.Gender ?? "Unknown";
            var age = contact?.Age;

            var numberOfRecommendationItems = 8;

            // Fetch products from Recommendation Engine through API
            var recommendations = await GetRecommendationsAsync(recommendationType, numberOfRecommendationItems, gender, age);

            var merchantAgeAndGenderRecommendationEntities = await GetRecommendationsAsync(OfferRecommendationTypes.MerchantAgeAndGenderRevenue, 100, gender, age);
            var merchantAgeAndGenderRecommendations = merchantAgeAndGenderRecommendationEntities.SelectMany(r => r.OfferItems ?? new List<OfferRecommendationItemDto>()).ToList();
            merchantAgeAndGenderRecommendations = merchantAgeAndGenderRecommendations.OrderByDescending(r => r.Score).ToList();
            
            // Create merchant score lookup for fast access
            var merchantScoreLookup = merchantAgeAndGenderRecommendations
                .GroupBy(r => r.MerchantId)
                .ToDictionary(g => g.Key, g => (decimal)g.Max(r => r.Score)); // Use max score per merchant and cast to decimal
            
            if (recommendations?.Any() == true)
            {
                // Get all product recommendations
                var allRecommendationItems = recommendations
                    .SelectMany(r => r.OfferItems ?? new List<OfferRecommendationItemDto>())
                    .Where(item => item.OfferType == "Product")
                    .Select(item => item.OfferId)
                    .Distinct()
                    .Take(200) // Increase limit to get more products for round-robin
                    .ToList();

                if (allRecommendationItems.Any())
                {
                    // Batch fetch all products at once
                    var products = await merchantService.GetProductByProductIdMultiple(allRecommendationItems);
                    
                    // Create a lookup for faster access and filter valid products
                    var productLookup = products
                        .Where(p => p.Price.HasValue || p.RegularPrice.HasValue)
                        .ToDictionary(p => p.Id, p => p);

                    // Process products in parallel to get valid products with images
                    var tasks = allRecommendationItems
                        .Where(id => productLookup.ContainsKey(id))
                        .Select(async productId =>
                        {
                            var product = productLookup[productId];
                            
                            // Get image URL efficiently
                            string imageUrl = await GetProductImageUrlAsync(product);
                            
                            // Only return products with valid images
                            if (!string.IsNullOrEmpty(imageUrl))
                            {
                                // Get merchant score for ordering (default to 0 if not found)
                                var merchantScore = merchantScoreLookup.GetValueOrDefault(product.FkMerchantId, 0);
                                
                                return new ProductScoreDto
                                {
                                    ProductInternalId = product.InternalProductId,
                                    MerchantId = product.FkMerchantId,
                                    ProductName = product.Name,
                                    ProductPrice = product.RegularPrice ?? 0,
                                    ProductSalePrice = product.Price ?? product.RegularPrice ?? 0,
                                    ProductUrl = product.Permalink,
                                    ProductImageUrl = imageUrl,
                                    MerchantScore = merchantScore
                                };
                            }
                            return null;
                        });

                    var results = await Task.WhenAll(tasks);
                    var validResults = results.Where(r => r != null).ToList();
                    
                    // Implement round-robin distribution by merchant score
                    returnData = DistributeProductsRoundRobin(validResults);
                }
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }

        return returnData;
    }

    private List<ProductScoreDto> DistributeProductsRoundRobin(List<ProductScoreDto> products)
    {
        // Group products by merchant and order merchants by their highest score
        var merchantGroups = products
            .GroupBy(p => p.MerchantId)
            .OrderByDescending(g => g.Max(p => p.MerchantScore))
            .Select(g => new
            {
                MerchantId = g.Key,
                MerchantScore = g.Max(p => p.MerchantScore),
                Products = g.OrderByDescending(p => p.MerchantScore)
                           .ThenBy(p => p.ProductSalePrice) // Secondary sort by price
                           .ToList()
            })
            .ToList();

        var result = new List<ProductScoreDto>();
        var maxProductsPerMerchant = merchantGroups.Max(g => g.Products.Count);

        // Round-robin distribution: take one product from each merchant in order, then repeat
        for (int round = 0; round < maxProductsPerMerchant; round++)
        {
            foreach (var merchantGroup in merchantGroups)
            {
                if (round < merchantGroup.Products.Count)
                {
                    result.Add(merchantGroup.Products[round]);
                }
            }
        }

        return result;
    }

    private Task<string> GetProductImageUrlAsync(Product product)
    {
        // Use curated image if available
        if (!string.IsNullOrEmpty(product.CuratedImage))
        {
            return Task.FromResult(product.CuratedImage);
        }

        // Parse product images efficiently
        try
        {
            if (string.IsNullOrEmpty(product.ProductImages))
            {
                return Task.FromResult(string.Empty);
            }

            var images = JsonSerializer.Deserialize<List<string>>(product.ProductImages);
            return Task.FromResult(images?.FirstOrDefault() ?? string.Empty);
        }
        catch
        {
            return Task.FromResult(string.Empty);
        }
    }

    private async Task<List<OfferRecommendationDto>> GetRecommendationsAsync(string strategy, int numberOfRecommendationItems, string? gender, int? age)
    {
        /*var httpClient = httpClientFactory.CreateClient();
        //var baseUrl = configuration["ValyrionServices-BaseUrl"] ?? "https://localhost:7001";
        var baseUrl = "https://localhost:7001";
        var requestUrl = $"{baseUrl}/OfferRecommendation?strategy={strategy}&numberOfRecommendationItems={numberOfRecommendationItems}&gender={gender}";

        if (age.HasValue)
        {
            requestUrl += $"&age={age}";
        }

        var response = await httpClient.GetAsync(requestUrl);
        if (response.IsSuccessStatusCode)
        {
            var jsonContent = await response.Content.ReadAsStringAsync();
            return JsonSerializer.Deserialize<List<OfferRecommendationDto>>(jsonContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            }) ?? new List<OfferRecommendationDto>();
        }*/
        
        var cacheKey = $"MerchantScoreEmailService_GetRecommendationsAsync_{strategy}_{numberOfRecommendationItems}_{gender}_{age}";
        var recommendations = await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30); // Shorter cache for more dynamic recommendations
            entry.SlidingExpiration = TimeSpan.FromMinutes(10); // Refresh if accessed within 10 minutes
            var recommendations = await recommendationService.GetRecommendationEntitiesAsync(strategy, numberOfRecommendationItems, gender, age);
            return recommendations;
        });

        return recommendations;
    }
}