using Admin_Services.Services.Admin;
using Audience.Services.Audience;
using Campaign_Services.Services.Campaign;
using General_Services.Models.Models.Analytics.Email;
using General_Services.Models.ModelsDal.Valyrion;
using Message_Services.Services.Message;
using Microsoft.Extensions.Configuration;
using Shared.Dto.Analytics;
using Shared.Dto.General;
using Shared.Elastic.CampaignMailClick;
using Shared.Elastic.CampaignMailOpen;
using Shared.Elastic.Elastic;
using Shared.Services.Cache;
using ILogger = Serilog.ILogger;

namespace General_Services.Services.Analytics.Email;

public class AnalyticsEmailService : IAnalyticsEmailService
{
    private readonly ValyrionDbContextTracking _valyrionDbContext;
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly IMessageService _messageService;
    private readonly ICacheService _cacheService;
    private readonly IElasticService _elasticService;
    private readonly ICustomerService _customerService;
    private readonly IAdminService _adminService;
    private readonly ICampaignService _campaignService;
    private readonly IElasticCampaignMailOpenService _elasticCampaignMailOpenService;
    private readonly IElasticCampaignMailClickService _elasticCampaignMailClickService;


    public AnalyticsEmailService(ValyrionDbContextTracking valyrionDbContext, ILogger logger,
        IConfiguration configuration, IMessageService messageService, ICacheService cacheService,
        IElasticService elasticService, ICustomerService customerService, IAdminService adminService,
        ICampaignService campaignService, IElasticCampaignMailOpenService elasticCampaignMailOpenService,
        IElasticCampaignMailClickService elasticCampaignMailClickService)
    {
        _logger = logger;
        _configuration = configuration;
        _messageService = messageService;
        _cacheService = cacheService;
        _elasticService = elasticService;
        _customerService = customerService;
        _adminService = adminService;
        _campaignService = campaignService;
        _elasticCampaignMailOpenService = elasticCampaignMailOpenService;
        _elasticCampaignMailClickService = elasticCampaignMailClickService;
        _valyrionDbContext = valyrionDbContext;
    }

    public async Task<PerformanceOverTimeDto> GetPerformanceOverTime(AnalyticsDto analyticsDto)
    {
        //Time
        TimeSpan difference = analyticsDto.To - analyticsDto.From;
        var fromOld = analyticsDto.From.AddDays(-difference.Days);
        var toOld = analyticsDto.To.AddDays(-difference.Days);

        var emailSent = await _messageService.MessagesSent(analyticsDto.From, analyticsDto.To);
        //var emailSentTask = _elasticService.MailSentCount(analyticsDto.From, analyticsDto.To);
        var emailSentOld = await _messageService.MessagesSent(fromOld, toOld);
        var campaignsSent = await _messageService.MessagesSentUniqueCampaignCount(analyticsDto.From, analyticsDto.To);
        var campaignsSentOld = await _messageService.MessagesSentUniqueCampaignCount(fromOld, toOld);

        //await Task.WhenAll(emailSentTask, emailSentOldTask);
        //var emailSent = await emailSentTask.ConfigureAwait(false);
        //var emailSentOld = await emailSentOldTask.ConfigureAwait(false);

        decimal prDay = emailSent / (decimal) difference.Days;
        decimal prDayOld = emailSentOld / (decimal) difference.Days;

        //Open
        decimal openRate = 0;
        decimal openRateOld = 0;

        //graphData
        var graphData = new List<PerformanceOverTimeDataDto>();

        //Data
        Task<Dictionary<DateTime, decimal>> datasTask;
        Task<Dictionary<DateTime, decimal>> datasOldTask;

        if (analyticsDto.Type == "open")
        {
            //datasTask = _elasticService.OpensWebshopDayCount(analyticsDto.From, analyticsDto.To);
            datasTask = ConvertLongToDecimalAsync(
                _elasticCampaignMailOpenService.OpensDayCount(analyticsDto.From, analyticsDto.To));
            //datasOldTask = _elasticService.OpensWebshopDayCount(fromOld, toOld);
            datasOldTask = ConvertLongToDecimalAsync(_elasticCampaignMailOpenService.OpensDayCount(fromOld, toOld));
        }
        else if (analyticsDto.Type == "click")
        {
            datasTask = ConvertLongToDecimalAsync(
                _elasticService.ClicksWebshopDayCount(analyticsDto.From, analyticsDto.To));
            datasOldTask = ConvertLongToDecimalAsync(_elasticService.ClicksWebshopDayCount(fromOld, toOld));
        }
        else if (analyticsDto.Type == "unsubscribe")
        {
            datasTask = ConvertLongToDecimalAsync(
                _customerService.UnsubscribedCustomers(analyticsDto.From, analyticsDto.To, campaignsSent));
            //EfCore only 1 thread pr operation
            await Task.WhenAll(datasTask);
            datasOldTask =
                ConvertLongToDecimalAsync(_customerService.UnsubscribedCustomers(fromOld, toOld, campaignsSentOld));
        }
        else
        {
            datasTask = ConvertLongToDecimalAsync(
                _elasticService.OpensClickWebshopDayCount(analyticsDto.From, analyticsDto.To));
            datasOldTask = ConvertLongToDecimalAsync(_elasticService.OpensClickWebshopDayCount(fromOld, toOld));
        }

        await Task.WhenAll(datasTask, datasOldTask);
        foreach (var data in await datasTask)
        {
            if (data.Value != 0)
            {
                var rawData = data.Value;

                if (analyticsDto.Type != "openClick")
                {
                    rawData = data.Value / prDay;
                }

                graphData.Add(new PerformanceOverTimeDataDto
                {
                    Date = data.Key.ToString("dd-MM-yyyy"),
                    Value = Math.Round(rawData * 100, 3)
                });
                openRate += rawData * 100;
            }
            else
            {
                graphData.Add(new PerformanceOverTimeDataDto
                {
                    Date = data.Key.ToString("dd-MM-yyyy"),
                    Value = 0
                });
            }
        }

        foreach (var dataOld in await datasOldTask)
        {
            if (dataOld.Value != 0)
            {
                var rawData = dataOld.Value;

                if (analyticsDto.Type != "openClick")
                {
                    rawData = dataOld.Value / prDayOld;
                }

                var value = rawData;
                openRateOld += value * 100;
            }
        }


        openRate /= difference.Days;
        openRate = Math.Round(openRate, 2);
        openRateOld /= difference.Days;
        openRateOld = Math.Round(openRateOld, 2);


        return new PerformanceOverTimeDto
        {
            EmailCount = campaignsSent.Count,
            EmailSent = emailSent,
            OpenRate = openRate,
            OpenRateOld = openRateOld,
            Graph = graphData
        };
    }

    private async Task<Dictionary<DateTime, decimal>> ConvertLongToDecimalAsync(Task<Dictionary<DateTime, long>> task)
    {
        // Await the task to get the Dictionary<DateTime, long>
        var longDictionary = await task;

        // Convert each value from long to decimal and create a new dictionary
        var decimalDictionary = longDictionary.ToDictionary(pair => pair.Key, pair => (decimal) pair.Value);

        return decimalDictionary;
    }

    public async Task<MonitorPerformanceDto> GetMonitorPerformance(AnalyticsDto analyticsDto)
    {
        //Time
        TimeSpan difference = analyticsDto.To - analyticsDto.From;
        var fromOld = analyticsDto.From.AddDays(-difference.Days);
        var toOld = analyticsDto.To.AddDays(-difference.Days);

        var monitorPerformance = await _adminService.InvoiceLinesAsync(analyticsDto.From, analyticsDto.To, "Campaign");
        var monitorPerformanceOld = await _adminService.InvoiceLinesAsync(fromOld, toOld, "Campaign");

        //var datasTask = _elasticService.OpensUniqueWebshopDayCount(analyticsDto.From, analyticsDto.To);
        var datasTask = _elasticCampaignMailOpenService.OpensDayCountData(analyticsDto.From, analyticsDto.To);
        //var datasOldTask = _elasticService.OpensUniqueWebshopDayCount(fromOld, toOld);
        var datasOldTask = _elasticCampaignMailOpenService.OpensDayCountData(fromOld, toOld);
        await Task.WhenAll(datasTask, datasOldTask);

        var dataCount = (await datasTask).Count;
        var dataOldCount = (await datasOldTask).Count;

        return new MonitorPerformanceDto
        {
            Orders = monitorPerformance.Orders,
            Revenue = monitorPerformance.Revenue,
            AverageOrderRevenue = Math.Round(monitorPerformance.AverageOrderRevenue, 2),
            OrderRate = Math.Round(monitorPerformance.Orders / (decimal) (dataCount == 0 ? 1 : dataCount), 2),
            OrdersOld = monitorPerformanceOld.Orders,
            RevenueOld = monitorPerformanceOld.Revenue,
            AverageOrderRevenueOld = Math.Round(monitorPerformanceOld.AverageOrderRevenue, 2),
            OrderRateOld =
                Math.Round(monitorPerformanceOld.Orders / (decimal) (dataOldCount == 0 ? 1 : dataOldCount), 2),
        };
    }

    public async Task<List<CompareEmailsOverTimeDto>> GetCompareEmailsOverTime(AnalyticsDto analyticsDto)
    {
        var compareEmailsOverTimeDtos = new List<CompareEmailsOverTimeDto>();
        var campaignIds = await _messageService.MessagesSentUniqueCampaignCount(analyticsDto.From, analyticsDto.To);
        var campaigns = await _campaignService.GetNameAsync(campaignIds);

        if (analyticsDto.Type == "open")
        {
            var opens = (await _elasticCampaignMailOpenService.OpensDayCountData(analyticsDto.From, analyticsDto.To))
                .GroupBy(a => a.Customer.Campaign_Id).ToList();
            foreach (var campaign in campaigns)
            {
                var data = opens.SingleOrDefault(a => a.Key == campaign.Key.ToString());
                if (data != null)
                {
                    var emailSent =
                        await _messageService.MessagesSent(analyticsDto.From, analyticsDto.To, campaign.Key);
                    compareEmailsOverTimeDtos.Add(new CompareEmailsOverTimeDto
                    {
                        Rate = data.Count() / (decimal) emailSent * 100,
                        Name = campaign.Value
                    });
                }
            }
        }
        else if (analyticsDto.Type == "click")
        {
            var clicks = (await _elasticService.ClicksUniqueWebshopDayCount(analyticsDto.From, analyticsDto.To))
                .GroupBy(a => a.Customer.Campaign_Id).ToList();
            foreach (var campaign in campaigns)
            {
                var data = clicks.SingleOrDefault(a => a.Key == campaign.Key.ToString());
                if (data != null)
                {
                    var emailSent =
                        await _messageService.MessagesSent(analyticsDto.From, analyticsDto.To, campaign.Key);
                    compareEmailsOverTimeDtos.Add(new CompareEmailsOverTimeDto
                    {
                        Rate = data.Count() / (decimal) emailSent * 100,
                        Name = campaign.Value
                    });
                }
            }
        }
        else if (analyticsDto.Type == "emailSent")
        {
            foreach (var campaign in campaigns)
            {
                var emailSent =
                    await _messageService.MessagesSent(analyticsDto.From, analyticsDto.To, campaign.Key);
                compareEmailsOverTimeDtos.Add(new CompareEmailsOverTimeDto
                {
                    Rate = emailSent,
                    Name = campaign.Value
                });
            }
        }
        else if (analyticsDto.Type == "revenue")
        {
            foreach (var campaign in campaigns)
            {
                var monitorPerformance =
                    await _adminService.InvoiceSumAsync(analyticsDto.From, analyticsDto.To, "Campaign",
                        campaign.Key.ToString());
                compareEmailsOverTimeDtos.Add(new CompareEmailsOverTimeDto
                {
                    Rate = monitorPerformance,
                    Name = campaign.Value
                });
            }
        }

        return compareEmailsOverTimeDtos;
    }

    public async Task<TrackConversionsDto> GetTrackConversions(AnalyticsDto analyticsDto)
    {
        //Time
        TimeSpan difference = analyticsDto.To - analyticsDto.From;
        var fromOld = analyticsDto.From.AddDays(-difference.Days);
        var toOld = analyticsDto.To.AddDays(-difference.Days);

        var emailSent = await _messageService.MessagesSent(analyticsDto.From, analyticsDto.To);
        var emailSentOld = await _messageService.MessagesSent(fromOld, toOld);
        //var opensUniqueTask = _elasticService.OpensUniqueWebshopDayCount(analyticsDto.From, analyticsDto.To);
        var opensUniqueTask = _elasticCampaignMailOpenService.OpensDayCountData(analyticsDto.From, analyticsDto.To);
        //var clicksWebshopTask = _elasticService.ClicksWebshopCount(analyticsDto.From, analyticsDto.To);
        var clicksWebshopTask = _elasticCampaignMailClickService.Clicks(analyticsDto.From, analyticsDto.To);
        var orders = await _adminService.InvoiceSumAsync(analyticsDto.From, analyticsDto.To, "Campaign");
        var ordersOld = await _adminService.InvoiceSumAsync(fromOld, toOld, "Campaign");

        await Task.WhenAll(opensUniqueTask, clicksWebshopTask);

        //var emailSent = await emailSentTask;
        //var emailSentOld = await emailSentOldTask;
        var opensUnique = await opensUniqueTask;
        var clicksWebshop = await clicksWebshopTask;

        return new TrackConversionsDto
        {
            Datas = new List<TrackConversionsDataDto>
            {
                new()
                {
                    Name = "Deliveries",
                    Rate = 0,
                    Value = emailSent,
                    ValueProcent = 100,
                    ValueProcentText = "100%"
                },
                new()
                {
                    Name = "Opened",
                    Rate = 100,
                    Value = opensUnique.Count,
                    ValueProcent = Math.Round(opensUnique.Count / (decimal) emailSent * 100, 2),
                    ValueProcentText = $"{Math.Round(opensUnique.Count / (decimal) emailSent * 100, 2)}%"
                },
                new()
                {
                    Name = "Clicked",
                    Rate = 100,
                    Value = clicksWebshop,
                    ValueProcent = Math.Round(clicksWebshop / (decimal) emailSent * 100, 2),
                    ValueProcentText = $"{Math.Round(clicksWebshop / (decimal) emailSent * 100, 2)}"
                }
            },
            RevenuePrRecipient = Math.Round(orders / emailSent, 2),
            RevenuePrRecipientOld = Math.Round(ordersOld / emailSentOld, 2)
        };
    }
}