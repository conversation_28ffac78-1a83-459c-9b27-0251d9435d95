using General_Services.Models.Models.Analytics.Email;
using Shared.Dto.Analytics;
using Shared.Dto.General;

namespace General_Services.Services.Analytics.Email
{
    public interface IAnalyticsEmailService
    {
        Task<PerformanceOverTimeDto> GetPerformanceOverTime(AnalyticsDto analyticsDto);
        Task<MonitorPerformanceDto> GetMonitorPerformance(AnalyticsDto analyticsDto);
        Task<List<CompareEmailsOverTimeDto>> GetCompareEmailsOverTime(AnalyticsDto analyticsDto);
        Task<TrackConversionsDto> GetTrackConversions(AnalyticsDto analyticsDto);
    }
}