using System.Threading.Tasks;
using General_Services.Models.Models.Export;
using Shared.Dto.Webshop;

namespace General_Services.Services.Export
{
    public interface IExportService
    {
        Task<string> ExportBoughtAtGender();
        Task<string> ExportInvoiceLinesGender();
        Task<string> TestExportInvoice();
        Task<string> CategoryCodes();
        Task ExportMarcus(int typeId);
        Task<List<ExportGenderAgeSegments>> ExportGenderAgeSegments();
        Task<List<ExportEmailOrderGenderAgeSegments>> ExportEmailOrderGenderAgeSegments(bool update = false);
        Task<List<int>> ExportActiveMerchantIds();
        Task<string> ExportVirk();
    }
}