using System.Collections.Concurrent;
using System.IO.Compression;
using System.Net;
using System.Net.Http.Headers;
using System.Text;
using System.Text.RegularExpressions;
using Audience.Services.Audience;
using Campaign_Services.Models.ModelsDal.Campaign;
using Campaign_Services.Services.Filter;
using General_Services.Models.Models;
using General_Services.Models.Models.Export;
using General_Services.Models.ModelsDal.Valyrion;
using Invoice_Services.Models.ModelsDal.Invoice;
using Marlin_OS_Integration_API.Models.Order;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Nest;
using Newtonsoft.Json;
using Partner_Services.Services.PartnerData;
using Shared.Elastic.Elastic;
using Shared.Elastic.Invoice.InvoiceLine;
using Webshop.Webshop;
using Filter = Campaign_Services.Models.ModelsDal.Campaign.Filter;
//using Filter = Nest.Filter;
using ILogger = Serilog.ILogger;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace General_Services.Services.Export;

public class ExportService : IExportService
{
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly IMerchantService _merchantService;
    private readonly IFilterService _filterService;
    private readonly IElasticService _elasticService;
    private readonly ICustomerService _customerService;
    private readonly ElasticClient _elasticClient;
    private readonly IPartnerDataService _partnerDataService;
    private readonly ValyrionDbContextTracking _valyrionDbContext;
    private int lookBackDays = 90;

    public ExportService(ILogger logger, IConfiguration configuration,
        ElasticClient elasticClient, IMerchantService merchantService, IFilterService filterService,
        ICustomerService customerService, IPartnerDataService partnerDataService, IElasticService elasticService,
        ValyrionDbContextTracking valyrionDbContext)
    {
        _logger = logger;
        _configuration = configuration;
        _merchantService = merchantService;
        _filterService = filterService;
        _customerService = customerService;
        _partnerDataService = partnerDataService;
        _elasticService = elasticService;
        _valyrionDbContext = valyrionDbContext;
        _elasticClient = elasticClient;
    }


    public async Task<string> ExportBoughtAtGender()
    {
        var responseData = $"Name;Men;Lady;Unknown\r\n";
        var webshops = await _merchantService.GetAsync();
        webshops.Add(await _merchantService.GetByIdIgnoreActiveAsync(4532)); //MobilCovers
        webshops.Add(await _merchantService.GetByIdIgnoreActiveAsync(4533)); //TabletCovers
        webshops.Add(await _merchantService.GetByIdIgnoreActiveAsync(4598)); //BeautyCos
        webshops.Add(await _merchantService.GetByIdIgnoreActiveAsync(4778)); //Bambuni
        webshops.Add(await _merchantService.GetByIdIgnoreActiveAsync(4680)); //Dealshoppen 
        webshops.Add(await _merchantService.GetByIdIgnoreActiveAsync(4800)); //Neglefeber 
        webshops.Add(await _merchantService.GetByIdIgnoreActiveAsync(4798)); //GrydeGuru  
        webshops.Add(await _merchantService.GetByIdIgnoreActiveAsync(4770)); //NetLingeri    
        webshops.Add(await _merchantService.GetByIdIgnoreActiveAsync(4763)); //DreamShop     
        webshops.Add(await _merchantService.GetByIdIgnoreActiveAsync(4799)); //Nordly Living
        webshops = webshops.DistinctBy(a => a.Id).ToList();
        foreach (var webshop in webshops.Where(a => a.IsDev))
        {
            var merchantId = webshop.Id.ToString();

            var men = await GenerateFilterValue(merchantId, "");
            var lady = await GenerateFilterValue(merchantId, "yes");
            var unknown = await GenerateFilterValue(merchantId, "No");
            responseData += $"{webshop.Name};{men};{lady};{unknown}\r\n";
        }

        return responseData;
    }

    public async Task<string> ExportInvoiceLinesGender()
    {
        var responseData = $"Name;Men;Lady;Unknown;Total\r\n";
        var exportInvoiceLinesGender = new List<ExportInvoiceLinesGenderDto>();
        var audience = (await _customerService.GetAllConsentAsync()).Where(a => a.MarketingStatus)
            .DistinctBy(a => a.Email);
        var audienceFast = audience.ToDictionary(a => a.Email);
        var invoiceLinesMaster = (await InvoiceLines("invoices-lines")).OrderBy(a => a.Key);
        var invoicePotentiallinesMaster = (await InvoiceLines("invoices-potentiallines")).OrderBy(a => a.Key);
        foreach (var invoiceLinesHead in invoiceLinesMaster)
        {
            decimal menSum = 0;
            decimal ladySum = 0;
            decimal unknownSum = 0;

            foreach (var invoiceLine in invoiceLinesHead)
            {
                audienceFast.TryGetValue(invoiceLine.Invoice_line.Email, out var contact);
                if (contact?.Gender == "Male")
                {
                    //Men
                    menSum += invoiceLine.Invoice_line.Total_cut;
                }
                else if (contact?.Gender == "Female")
                {
                    //Female
                    ladySum += invoiceLine.Invoice_line.Total_cut;
                }
                else if (contact?.Gender == "Unknown")
                {
                    //Unknown
                    unknownSum += invoiceLine.Invoice_line.Total_cut;
                }
                else
                {
                    Console.WriteLine(invoiceLine.Invoice_line.Email);
                }
            }

            exportInvoiceLinesGender.Add(new ExportInvoiceLinesGenderDto
            {
                Men = menSum,
                Lady = ladySum,
                Unknown = unknownSum,
                WebshopName = invoiceLinesHead.Key
            });
        }

        foreach (var invoiceLinesHeadPotentiallines in invoicePotentiallinesMaster)
        {
            decimal menSum = 0;
            decimal ladySum = 0;
            decimal unknownSum = 0;
            foreach (var invoiceLine in invoiceLinesHeadPotentiallines)
            {
                audienceFast.TryGetValue(invoiceLine.Invoice_line.Email, out var contact);
                if (contact?.Gender == "Male")
                {
                    //Men
                    menSum += invoiceLine.Invoice_line.Total_cut;
                }
                else if (contact?.Gender == "Female")
                {
                    //Female
                    ladySum += invoiceLine.Invoice_line.Total_cut;
                }
                else if (contact?.Gender == "Unknown")
                {
                    //Unknown
                    unknownSum += invoiceLine.Invoice_line.Total_cut;
                }
                else
                {
                    Console.WriteLine(invoiceLine.Invoice_line.Email);
                }
            }

            var exists =
                exportInvoiceLinesGender.SingleOrDefault(a => a.WebshopName == invoiceLinesHeadPotentiallines.Key);
            if (exists != null)
            {
                exists.Men += menSum;
                exists.Lady += ladySum;
                exists.Unknown += unknownSum;
                exists.Men += menSum;
                exists.WebshopName = invoiceLinesHeadPotentiallines.Key;
            }
            else
            {
                exportInvoiceLinesGender.Add(new ExportInvoiceLinesGenderDto
                {
                    Men = menSum,
                    Lady = ladySum,
                    Unknown = unknownSum,
                    WebshopName = invoiceLinesHeadPotentiallines.Key
                });
            }
        }

        foreach (var invoiceLine in exportInvoiceLinesGender)
        {
            responseData +=
                $"{invoiceLine.WebshopName};{invoiceLine.Men};{invoiceLine.Lady};{invoiceLine.Unknown};{invoiceLine.Men + invoiceLine.Lady + invoiceLine.Unknown}\r\n";
        }

        return responseData;
    }

    private async Task<List<IGrouping<string, ElasticInvoiceLineMasterEvent>>> InvoiceLines(string index)
    {
        var orders = new List<ElasticInvoiceLineMasterEvent>();
        var searchResponse = await _elasticClient.SearchAsync<ElasticInvoiceLineMasterEvent>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Invoice_line.Webshop_id,
                        f => f.Invoice_line.Webshop_name,
                        f => f.Invoice_line.Total_cut,
                        f => f.Invoice_line.Order_date,
                        f => f.Invoice_line.Email,
                        f => f.Invoice_line.Order_id,
                        f => f.Invoice.Potential_line
                    )
                )
            )
            .Index(index)
            .Size(10_000)
            .Scroll("2m")
        );

        orders.AddRange(searchResponse.Documents);
        while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
               searchResponse.Documents.Count != 0)
        {
            searchResponse =
                await _elasticClient.ScrollAsync<ElasticInvoiceLineMasterEvent>("2m", searchResponse.ScrollId);
            orders.AddRange(searchResponse.Documents);
        }

        await _elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));

        /*var old = new DateTime(2023, 7, 1);
        var to = new DateTime(2023, 8, 7, 15, 22, 0);
        orders = orders.Where(a => a.Invoice_line.Order_date >= old && a.Invoice_line.Order_date <= to).ToList();*/
        return orders.Where(a => !string.IsNullOrEmpty(a.Invoice_line.Webshop_id))
            .GroupBy(a => a.Invoice_line.Webshop_name).ToList();
    }

    private async Task<int> GenerateFilterValue(string merchantId, string value)
    {
        var filter = new Filter
        {
            Match = "all",
            OnlyUnengaged = 0,
            FilterValues = new List<FilterValue>
            {
                new FilterValue
                {
                    Id = 3,
                    Value = merchantId,
                    Active = true,
                    Time = 2500,
                    Condition = ""
                },
                new FilterValue
                {
                    Id = 12,
                    Value = value,
                    Active = true,
                    Condition = "",
                    Time = 0,
                }
            }
        };
        return (await _filterService.GetAllFilterEmailsAsync(filter)).Count;
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////////
    //          Test ExportInvoice START
    ////////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////////
    private ConcurrentBag<OrderLine> invoiceLines = new ConcurrentBag<OrderLine>();

    public async Task<string> TestExportInvoice()
    {
        return "";
        /*var side1 = new List<int>
        {
            4796, 4772, 4592, 1932, 4541, 4795, 4627, 4684, 1076, 4530
        };
        var side2 = new List<int>
        {
            1623, 4677, 4796, 4646, 4770, 2888, 4792, 4767, 4579, 4773
        };
        var side3 = new List<int>
        {
            4608, 4570, 4557, 2029, 4615, 3200, 4650, 4664, 4588, 4764
        };
        var side4 = new List<int>
        {
            4660, 4669, 4503, 4535, 4617, 4500, 4552, 4782, 4678, 4797
        };
        var side5 = new List<int>
        {
            4584, 4634,
        };
        var merchantIds = side1.Concat(side2).Concat(side3).Concat(side4).Concat(side5);
        var responseData = $"";
        var invoiced = false;

        var audiences = await _audienceService.GetEmailPhoneAsync();
        //var contacts = await _audienceService.GetAllAsync();

        var orders = new List<ElasticOrderEvent>();
        var searchResponse = await _elasticClient.SearchAsync<ElasticOrderEvent>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Order_date,
                        f => f.Shop_order.Total_price,
                        f => f.Customer.Email,
                        f => f.Shop_order.Webshop_id,
                        f => f.Shop_order.Webshop_name,
                        f => f.Shop_order.Shipping_address.Email,
                        f => f.Shop_order.Billing_address.Email,
                        f => f.Shop_order.Billing_address.Phone_number,
                        f => f.Shop_order.Order_number_recevice,
                        f => f.Shop_order.Status,
                        f => f.Shop_order.IsCanceled
                    )
                )
            )
            .Query(q =>
                //q.Terms(t => t.Field(f => f.Shop_order.Webshop_id).Terms(merchantIds)) &&
                q.Bool(b => b
                    .Filter(f =>
                        f.DateRange(dt => dt
                            .Field(field => field.Order_date)
                            .GreaterThanOrEquals(new DateTime(2023, 8, 1))
                        )))
            )
            .Index("customers-orders")
            .Size(10_000)
            .Scroll("2m")
        );

        orders.AddRange(searchResponse.Documents);
        while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
               searchResponse.Documents.Count != 0)
        {
            searchResponse =
                await _elasticClient.ScrollAsync<ElasticOrderEvent>("2m", searchResponse.ScrollId);
            orders.AddRange(searchResponse.Documents);
        }

        orders = orders.OrderBy(a => a.Order_date).ToList();
        orders = orders.DistinctBy(m => new { m.Shop_order.Order_number_recevice, m.Shop_order.Webshop_id }).ToList();
        await _elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
        var count = 0;
        var webshops = (await _merchantService.GetFullAsync()).ToDictionary(a => a.Id.ToString());

        await Parallel.ForEachAsync(orders,
            new ParallelOptions
                { MaxDegreeOfParallelism = 12 },
            async (orderElastic, stoppingToken) =>
            {
                count++;
                Console.WriteLine($"Left: {orders.Count - count}");
                webshops.TryGetValue(orderElastic.Shop_order.Webshop_id, out var webShop);

                var pageNumber = "0";
                if (side1.Contains(Convert.ToInt32(orderElastic.Shop_order.Webshop_id)))
                {
                    pageNumber = "1";
                }
                else if (side2.Contains(Convert.ToInt32(orderElastic.Shop_order.Webshop_id)))
                {
                    pageNumber = "2";
                }
                else if (side3.Contains(Convert.ToInt32(orderElastic.Shop_order.Webshop_id)))
                {
                    pageNumber = "3";
                }
                else if (side4.Contains(Convert.ToInt32(orderElastic.Shop_order.Webshop_id)))
                {
                    pageNumber = "4";
                }
                else if (side5.Contains(Convert.ToInt32(orderElastic.Shop_order.Webshop_id)))
                {
                    pageNumber = "5";
                }

                //Find order email from internalCustomer
                var orderEmailSearch = orderElastic.Customer?.Email ?? "";

                //Find order email from shipping
                if (orderEmailSearch == "")
                {
                    orderEmailSearch = orderElastic.Shop_order?.Shipping_address?.Email ?? "";
                }

                //Find order email from billing
                if (orderEmailSearch == "")
                {
                    orderEmailSearch = orderElastic.Shop_order?.Billing_address?.Email ?? "";
                }

                //Find phone in shipping
                var orderPhoneSearch = orderElastic.Shop_order?.Shipping_address?.Phone_number ?? "";

                //Find phone in billing
                if (orderPhoneSearch == "")
                {
                    orderPhoneSearch = orderElastic.Shop_order?.Billing_address?.Phone_number ?? "";
                }

                if (orderPhoneSearch != "" && orderPhoneSearch != "n/a")
                {
                    orderPhoneSearch = SanitizePhoneNumber(orderPhoneSearch);
                    orderPhoneSearch = $"+45{orderPhoneSearch}";
                }

                //Default status
                if (orderElastic.Shop_order != null && orderElastic.Shop_order?.Status == null)
                {
                    orderElastic.Shop_order!.Status = "n/a";
                }

                //Skip if no mail is found
                if (orderEmailSearch != "" || orderPhoneSearch != "")
                {
                    //Check if found exposurePeriod
                    var foundExposurePeriod = false;
                    var contact =
                        audiences.FirstOrDefault(a =>
                            a.PhoneNumber == orderPhoneSearch && orderPhoneSearch != "")?.Email ?? orderEmailSearch;

                    //Mail Open
                    var campaignOpens =
                        (await CampaignOpenElastic(orderElastic.Shop_order.Webshop_id, orderEmailSearch,
                            contact).ConfigureAwait(false));
                    //Mail Click
                    var campaignClick = await CampaignClickElastic(orderElastic.Shop_order.Webshop_id, orderEmailSearch,
                        contact).ConfigureAwait(false);

                    //Discounts
                    var discountsClick =
                        await CustomersDiscountsElastic(pageNumber, orderEmailSearch,
                            "discount_overview_viewed").ConfigureAwait(false);
                    var discountsOpen =
                        await CustomersDiscountsElastic2(orderElastic.Shop_order.Webshop_id, orderEmailSearch,
                            "discount_details_viewed").ConfigureAwait(false);

                    //Discount redirect
                    if (foundExposurePeriod == false)
                    {
                        var discount =
                            discountsClick.FirstOrDefault(a =>
                                a.TimeStamp >= orderElastic.Order_date.AddDays(-(webShop.ExposureDays)) &&
                                a.TimeStamp <= orderElastic.Order_date);
                        if (discount != null)
                        {
                            foundExposurePeriod = true;
                            //TODO NEED TO BE FIXED IF SHOULD BE USED AGAIN
                            //invoiced = await CheckEvent(orderElastic, orderEmailSearch, "Discount", webShop,
                            //    discount.Discount.Discount_id, discount.TimeStamp, "Redirect");
                        }
                    }

                    //Discount display
                    if (foundExposurePeriod == false)
                    {
                        var discount =
                            discountsOpen.FirstOrDefault(a =>
                                a.TimeStamp >= orderElastic.Order_date.AddDays(-(webShop.ExposureDays)) &&
                                a.TimeStamp <= orderElastic.Order_date);
                        if (discount != null)
                        {
                            foundExposurePeriod = true;
                            //TODO NEED TO BE FIXED IF SHOULD BE USED AGAIN
                            //invoiced = await CheckEvent(orderElastic, orderEmailSearch, "Discount", webShop,
                            //    discount.Discount.Discount_id, discount.TimeStamp, "Display");
                        }
                    }

                    //Campaign redirect
                    if (webShop.MerchantPayments.First().MailRedirectInvoice == true &&
                        foundExposurePeriod == false)
                    {
                        var mailClickElastic =
                            campaignClick.FirstOrDefault(a =>
                                a.EventObject.Created >= orderElastic.Order_date.AddDays(-(webShop.ExposureDays)) &&
                                a.EventObject.Created <= orderElastic.Order_date);
                        if (mailClickElastic != null)
                        {
                            foundExposurePeriod = true;
                            invoiced = await CheckEvent(orderElastic, orderEmailSearch, "Campaign", webShop,
                                mailClickElastic.Customer.Campaign_Id, mailClickElastic.EventObject.Created,
                                "Redirect");
                        }
                    }

                    //Campaign display
                    if (webShop.MerchantPayments.First().MailOpenInvoice == true &&
                        foundExposurePeriod == false)
                    {
                        var mailOpen =
                            campaignOpens.FirstOrDefault(a =>
                                a.EventObject.Created >= orderElastic.Order_date.AddDays(-(webShop.ExposureDays)) &&
                                a.EventObject.Created <= orderElastic.Order_date);
                        if (mailOpen != null)
                        {
                            foundExposurePeriod = true;
                            invoiced = await CheckEvent(orderElastic, orderEmailSearch, "Campaign", webShop,
                                mailOpen.Customer.Campaign_Id, mailOpen.EventObject.Created, "Display");
                        }
                    }
                }
            });

        var totalPriceCampaign = invoiceLines.Where(a => a.ExposureTrigger == "Campaign").Sum(a => a.TotalPrice);
        var totalCutCampaign = invoiceLines.Where(a => a.ExposureTrigger == "Campaign").Sum(a => a.TotalCut);

        var totalPriceDiscount = invoiceLines.Where(a => a.ExposureTrigger == "Discount").Sum(a => a.TotalPrice);
        var totalCutDiscount = invoiceLines.Where(a => a.ExposureTrigger == "Discount").Sum(a => a.TotalCut);

        var totalPrice = invoiceLines.Sum(a => a.TotalPrice);
        var totalCut = invoiceLines.Sum(a => a.TotalCut);

        var test = invoiceLines.GroupBy(a => a.WebshopName).ToList();

        return responseData;*/
    }

    public async Task<string> CategoryCodes()
    {
        var data = new ConcurrentBag<MerchantCode>();
        var now = DateTime.UtcNow.AddDays(-90);
        var orders = new List<ElasticPartnerOrderEvent>();
        var searchResponse = await _elasticClient.SearchAsync<ElasticPartnerOrderEvent>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Order_date,
                        f => f.Customer.Email,
                        f => f.Shop_order.Webshop_id,
                        f => f.Shop_order.Merchant_account_id,
                        f => f.Shop_order.Status,
                        f => f.Shop_order.IsCanceled,
                        f => f.Shop_order.Total_price_tax_included,
                        f => f.Card_transaction_details.Authorization_method
                        //f => f.Card_transaction_details.Merchant_category,
                        //f => f.Card_transaction_details.Merchant_name
                    )
                )
            )
            .Query(q =>
                q.Bool(b => b
                    .Filter(f =>
                        f.DateRange(dt => dt
                            .Field(field => field.Order_date)
                            .GreaterThanOrEquals(now)
                        )))
            )
            .Index("customers-orders-partners")
            .Size(10_000)
            .Scroll("2m")
        );

        orders.AddRange(searchResponse.Documents);
        while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
               searchResponse.Documents.Count != 0)
        {
            searchResponse =
                await _elasticClient.ScrollAsync<ElasticPartnerOrderEvent>("2m", searchResponse.ScrollId);
            orders.AddRange(searchResponse.Documents);
        }

        foreach (var elasticViabillOrderEvent in orders.Where(a => a.Shop_order.Merchant_account_id != "n/a"))
        {
            var exists = data.SingleOrDefault(a =>
                a.MerchantAccountID == elasticViabillOrderEvent.Shop_order.Merchant_account_id);
            if (exists == null)
            {
                if (elasticViabillOrderEvent.Card_transaction_details != null)
                {
                    Console.WriteLine();
                }

                data.Add(new MerchantCode()
                {
                    WebshopId = elasticViabillOrderEvent.Shop_order?.Webshop_id ?? "none",
                    MerchantCategoryCode =
                        elasticViabillOrderEvent.Card_transaction_details?.Merchant_category ?? "none",
                    MerchantAccountID = elasticViabillOrderEvent.Shop_order.Merchant_account_id,
                    TransactionSize = elasticViabillOrderEvent.Shop_order.Total_price_tax_included ?? 0
                });
            }
            else
            {
                exists.TransactionSize += elasticViabillOrderEvent.Shop_order.Total_price_tax_included ?? 0;
            }
        }


        return "";
    }

    public async Task ExportMarcus(int typeId)
    {
        var _builder = new StringBuilder();
        var _lock = new object();
        if (typeId == 1)
        {
            /*var webshopsMerchantViabills =
                await _partnerDataService.GetAllWebshopMerchantViaBillAsync().ConfigureAwait(false);*/
            var contacts = await _customerService.GetAllAsync().ConfigureAwait(false);
            contacts = contacts
                .GroupBy(shop => shop.Email)
                .Select(group => group.OrderByDescending(shop => shop.LastModifiedDate).First())
                .ToList();

            var left = contacts.Count();
            await Parallel.ForEachAsync(contacts,
                new ParallelOptions
                    {MaxDegreeOfParallelism = 8},
                async (contact, stoppingToken) =>
                {
                    left--;
                    Console.WriteLine(left);
                    var age = contact.Age?.ToString() ?? "Nan";
                    var newValue = $"{contact.Email},{contact.Gender},{age},[";
                    var webshopsValues = new Dictionary<string, int>();

                    var elasticOrders = _elasticClient.Search<ElasticOrderEvent>((s => s
                            .Source(sf => sf
                                .Includes(i => i
                                    .Fields(
                                        f => f.Shop_order.Webshop_id,
                                        f => f.Customer.Email
                                    )
                                )
                            )
                            .Index("customers-orders-dashboard")
                            .Query(q => q.Match(m => m
                                .Field(f => f.Customer.Email).Query(contact.Email))
                            )
                            .Size(10_000)
                        ));
                    if (elasticOrders.Documents.Count != 0)
                    {
                        var merchantIds = elasticOrders.Documents.GroupBy(a => a.Shop_order.Webshop_id);
                        foreach (var merchantId in merchantIds)
                        {
                            webshopsValues.Add(merchantId.Key, merchantId.Count());
                        }
                    }

                    var elasticOrdersViabill = _elasticClient.Search<ElasticPartnerOrderEvent>((s => s
                            .Source(sf => sf
                                .Includes(i => i
                                    .Fields(
                                        f => f.Customer.Email,
                                        f => f.Shop_order.Webshop_id,
                                        f => f.Shop_order.Merchant_account_id
                                    )
                                )
                            )
                            .Index("customers-orders-partners")
                            .Query(q => q.Match(m => m
                                .Field(f => f.Customer.Email).Query(contact.Email))
                            )
                            .Size(10_000)
                        ));
                    if (elasticOrdersViabill.Documents.Count != 0)
                    {
                        //Own webshopId
                        var merchantIds = elasticOrdersViabill.Documents.Where(a =>
                                a.Shop_order.Webshop_id != "n/a" && a.Shop_order.Webshop_id != "")
                            .GroupBy(a => a.Shop_order.Webshop_id);
                        foreach (var webshopId in merchantIds)
                        {
                            if (webshopsValues.ContainsKey(webshopId.Key))
                            {
                                webshopsValues[webshopId.Key] += webshopId.Count();
                            }
                            else
                            {
                                webshopsValues.Add(webshopId.Key, webshopId.Count());
                            }
                        }

                        //Viabill guid
                        merchantIds = elasticOrdersViabill.Documents.Where(a =>
                                a.Shop_order.Webshop_id == "n/a" && a.Shop_order.Merchant_account_id != "n/a")
                            .GroupBy(a => a.Shop_order.Merchant_account_id);
                        /*foreach (var webshopId in merchantIds)
                        {
                            var key = webshopsMerchantViabills.FirstOrDefault(a => a.Id.ToString() == webshopId.Key)
                                ?.Uuid ?? "n/a";
                            if (key != "n/a")
                            {
                                if (webshopsValues.ContainsKey(key))
                                {
                                    webshopsValues[key] += webshopId.Count();
                                }
                                else
                                {
                                    webshopsValues.Add(key, webshopId.Count());
                                }
                            }
                        }*/
                    }

                    foreach (var webshopsValue in webshopsValues)
                    {
                        newValue += $"[{webshopsValue.Key},{webshopsValue.Value}],";
                    }

                    lock (_lock)
                    {
                        if (webshopsValues.Count > 0)
                        {
                            _builder.Append($"{newValue.Substring(0, newValue.Length - 1)}]\r\n");
                        }
                        else
                        {
                            _builder.Append($"{newValue}]\r\n");
                        }
                    }
                });
            File.WriteAllText("C:\\Users\\<USER>\\Desktop\\Marlin\\marcus\\ordercount.csv",
                _builder.ToString());
        }
        else
        {
            //TODO Christoffer
            /*var webshopsViabills = (await _partnerDataService.GetAllWebshopViaBillAsync().ConfigureAwait(false))
                .GroupBy(shop => shop.Uuid)
                .Select(group => group.OrderByDescending(shop => shop.LastModifiedDate).First())
                .ToList();

            var value = "id, url, active\r\n";
            foreach (var webshopsViabill in webshopsViabills)
            {
                webshops.TryGetValue(webshopsViabill.WebShopId ?? 0, out var webshop);
                var newestObject = webshopsViabill.WebshopsViabillStores.MaxBy(obj => obj.LastModifiedDate);
                if (webshop != null)
                {
                    value += $"{webshop.Id},{webshop.Url},{webshop.IsCustomer}\r\n";
                    webshops.Remove(webshop.Id);
                }
                else
                {
                    if (newestObject != null)
                    {
                        value += $"{newestObject.Uuid},{newestObject.Url},0\r\n";
                    }
                }
            }

            foreach (var webshop in webshops)
            {
                value += $"{webshop.Value.Id},{webshop.Value.Url},{webshop.Value.IsCustomer}\r\n";
            }

            File.WriteAllText("C:\\Users\\<USER>\\Desktop\\Marlin\\marcus\\webshopId.csv", value);*/
        }
    }

    //https://app.clickup.com/t/86bxg1wmb
    public async Task<List<ExportGenderAgeSegments>> ExportGenderAgeSegments()
    {
        var returnData = new List<ExportGenderAgeSegments>();
        var genders = new List<string> {"Male", "Female"};

        int interval = 4;
        int start = 18;
        int end = 69;

        List<int> ages = Enumerable.Range(start, end - start + 1)
            .Where(x => (x - start) % interval == 0)
            .ToList();

        var merchants = await _merchantService.GetAllAsync();

        foreach (var gender in genders)
        {
            var genderSegment = new ExportGenderAgeSegments
            {
                Gender = gender
            };

            foreach (var age in ages)
            {
                var topMerchants = await _elasticService.GetMerchantScore(gender, age, interval);
                var data = new ExportGenderAgeSegmentsData
                {
                    Range = $"{age}-{age + interval}",
                };
                foreach (var merchant in topMerchants.OrderByDescending(a => a.Sum).Take(20))
                {
                    data.TopMerchants.Add(new ExportGenderAgeSegmentsDataMerchant
                    {
                        Id = merchant.MerchantId,
                        Name = merchants.FirstOrDefault(a => a.Id.ToString() == merchant.MerchantId)?.Name ?? "Unknown?"
                    });
                }

                genderSegment.AgeSegments.Add(data);
            }

            returnData.Add(genderSegment);
        }

        var topMerchantsUnknown = await _elasticService.GetMerchantScore("Unknown", null, null);
        var dataUnknown = new ExportGenderAgeSegmentsData
        {
            Range = $"{start}-{end + 1}",
        };

        foreach (var merchant in topMerchantsUnknown.OrderByDescending(a => a.Sum).Take(20))
        {
            dataUnknown.TopMerchants.Add(new ExportGenderAgeSegmentsDataMerchant
            {
                Id = merchant.MerchantId,
                Name = merchants.FirstOrDefault(a => a.Id.ToString() == merchant.MerchantId)?.Name ?? "Unknown?"
            });
        }

        var genderSegmentUnknown = new ExportGenderAgeSegments
        {
            Gender = "unknown"
        };
        genderSegmentUnknown.AgeSegments.Add(dataUnknown);
        returnData.Add(genderSegmentUnknown);
        return returnData;
    }

    //https://app.clickup.com/t/86bxztu17
    public async Task<List<ExportEmailOrderGenderAgeSegments>> ExportEmailOrderGenderAgeSegments(bool update = false)
    {
        try
        {
            // Probably not in use, but use the correct partnerId to get the data if needed
            var partnerData = await _partnerDataService.GetAllPartnerMerchantDataAsync(52876);

            var name = "ExportEmailOrderGenderAgeSegments";
            var exportData = _valyrionDbContext.Exports.SingleOrDefault(a => a.Name == name);
            if (exportData == null)
            {
                exportData = new Models.ModelsDal.Valyrion.Export
                {
                    Active = true,
                    CreatedDate = DateTime.UtcNow,
                    LastModifiedDate = DateTime.UtcNow,
                    Name = name,
                    Data = ""
                };
                _valyrionDbContext.Exports.Add(exportData);
            }

            if (!update && exportData.Data != "")
            {
                return JsonConvert.DeserializeObject<List<ExportEmailOrderGenderAgeSegments>>(exportData.Data);
            }

            var export = new ConcurrentBag<ExportEmailOrderGenderAgeSegments>();
            var contacts = await _customerService.GetAllAsync().ConfigureAwait(false);
            contacts = contacts
                .GroupBy(shop => shop.Email)
                .Select(group => group.OrderByDescending(shop => shop.LastModifiedDate).First())
                .ToList();

            //Merchants orders
            Console.WriteLine("Start orders");
            var orders = new List<ElasticOrderEvent>();
            var searchResponse = await _elasticClient.SearchAsync<ElasticOrderEvent>((s => s
                    .Source(sf => sf
                        .Includes(i => i
                            .Fields(
                                f => f.Shop_order.Webshop_id,
                                f => f.Customer.Email,
                                f => f.Order_date,
                                f => f.Shop_order.Total_price
                            )
                        )
                    )
                    .Index("customers-orders-dashboard")
                    .Size(10_000)
                    .Scroll("1m")
                ));
            orders.AddRange(searchResponse.Documents);

            while (searchResponse.IsValid && searchResponse.Documents.Count != 0)
            {
                searchResponse = await _elasticClient.ScrollAsync<ElasticOrderEvent>("1m", searchResponse.ScrollId);
                orders.AddRange(searchResponse.Documents);
            }

            if (!string.IsNullOrEmpty(searchResponse.ScrollId))
            {
                await _elasticClient.ClearScrollAsync(c => c.ScrollId(searchResponse.ScrollId));
            }

            var ordersByEmail = orders
                .Select(order =>
                {
                    order.Customer.Email = order.Customer.Email.Trim().ToLower();
                    return order;
                })
                .GroupBy(order => order.Customer.Email)
                .ToDictionary(g => g.Key, g => g.ToList(), StringComparer.OrdinalIgnoreCase);

            //Loop partner orders
            Console.WriteLine("Start orders partner");
            var ordersPartner = new List<ElasticPartnerOrderEvent>();
            var searchResponsePartner = _elasticClient.Search<ElasticPartnerOrderEvent>((s => s
                    .Source(sf => sf
                        .Includes(i => i
                            .Fields(
                                f => f.Customer.Email,
                                f => f.Order_date,
                                f => f.Shop_order.Webshop_id,
                                f => f.Shop_order.Merchant_account_id,
                                f => f.Shop_order.Total_price,
                                f => f.Shop_order.Total_price_tax_included
                            )
                        )
                    )
                    .Index("customers-orders-partners")
                    .Size(10_000)
                    .Scroll("1m")
                ));

            ordersPartner.AddRange(searchResponsePartner.Documents);
            while (searchResponsePartner.IsValid && searchResponsePartner.Documents.Count != 0)
            {
                searchResponsePartner =
                    await _elasticClient.ScrollAsync<ElasticPartnerOrderEvent>("1m", searchResponsePartner.ScrollId);
                ordersPartner.AddRange(searchResponsePartner.Documents);
            }

            if (!string.IsNullOrEmpty(searchResponsePartner.ScrollId))
            {
                await _elasticClient.ClearScrollAsync(c => c.ScrollId(searchResponsePartner.ScrollId));
            }

            var ordersPartnerByEmail = ordersPartner
                .Select(order =>
                {
                    order.Customer.Email = order.Customer.Email.Trim().ToLower();
                    return order;
                })
                .GroupBy(order => order.Customer.Email)
                .ToDictionary(g => g.Key, g => g.ToList(), StringComparer.OrdinalIgnoreCase);

            Console.WriteLine("Orders: " + orders.Count);
            Console.WriteLine("Order partners" + ordersPartner.Count);
            var left = contacts.Count();

            await Parallel.ForEachAsync(contacts,
                new ParallelOptions
                    {MaxDegreeOfParallelism = 8},
                async (contact, stoppingToken) =>
                {
                    left--;
                    Console.Write(left);
                    var exportEmailOrderGenderAgeSegments = new ExportEmailOrderGenderAgeSegments
                    {
                        Age = contact.Age,
                        Email = contact.Email,
                        Gender = contact.Gender,
                        Transactions = new List<ExportEmailOrderGenderAgeSegmentsTransactions>()
                    };

                    //Loop merchants orders
                    if (ordersByEmail.TryGetValue(contact.Email.ToLowerInvariant(),
                            out List<ElasticOrderEvent> matchedOrders))
                    {
                        foreach (var matchedOrder in matchedOrders)
                        {
                            exportEmailOrderGenderAgeSegments.Transactions.Add(
                                new ExportEmailOrderGenderAgeSegmentsTransactions
                                {
                                    MerchantId = matchedOrder.Shop_order.Webshop_id,
                                    OrdreDate = matchedOrder.Order_date,
                                    TotalPrice = matchedOrder.Shop_order.Total_price
                                });
                        }
                    }

                    //Loop partner orders
                    if (ordersPartnerByEmail.TryGetValue(contact.Email.ToLowerInvariant(),
                            out List<ElasticPartnerOrderEvent> matchedOrdersPartner))
                    {
                        foreach (var matchedOrder in matchedOrdersPartner)
                        {
                            if (matchedOrder.Shop_order!.Webshop_id != "n/a" &&
                                matchedOrder.Shop_order.Webshop_id != "")
                            {
                                exportEmailOrderGenderAgeSegments.Transactions.Add(
                                    new ExportEmailOrderGenderAgeSegmentsTransactions
                                    {
                                        MerchantId = matchedOrder.Shop_order.Webshop_id,
                                        OrdreDate = matchedOrder.Order_date,
                                        TotalPrice = matchedOrder.Shop_order.Total_price_tax_included ?? 0
                                    });
                            }
                            else
                            {
                                try
                                {
                                    if (matchedOrder.Shop_order != null &&
                                        matchedOrder.Shop_order!.Merchant_account_id != "n/a" &&
                                        matchedOrder.Shop_order!.Merchant_account_id != "")
                                    {
                                        var id = partnerData.FirstOrDefault(a =>
                                        {
                                            var merchantAccountId = matchedOrder.Shop_order!.Merchant_account_id;
                                            return merchantAccountId != null && a.Ids != null &&
                                                   a.Ids.Split(",").Contains(merchantAccountId);
                                        })?.Uuid ?? matchedOrder.Shop_order!.Merchant_account_id;

                                        try
                                        {
                                            exportEmailOrderGenderAgeSegments.Transactions.Add(
                                                new ExportEmailOrderGenderAgeSegmentsTransactions
                                                {
                                                    MerchantId = id ?? "Not Found",
                                                    OrdreDate = matchedOrder.Order_date,
                                                    TotalPrice = matchedOrder.Shop_order.Total_price_tax_included ?? 0
                                                });
                                        }
                                        catch (Exception e)
                                        {
                                            Console.WriteLine(e);
                                            throw;
                                        }
                                    }
                                }
                                catch (Exception e)
                                {
                                    Console.WriteLine(e);
                                    throw;
                                }
                            }
                        }
                    }

                    export.Add(exportEmailOrderGenderAgeSegments);
                });


            exportData.Data = JsonConvert.SerializeObject(export);
            _valyrionDbContext.Database.SetCommandTimeout(2400);
            await _valyrionDbContext.SaveChangesAsync();

            return export.ToList();
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    public async Task<List<int>> ExportActiveMerchantIds()
    {
        return await _merchantService.GetAllIdsAsync();
    }

    // Engangs udtrak fra virk med data til salg
    public async Task<string> ExportVirk()
    {
        var rawData =
            "41257008\n42066389\n30527127\n34235937\n25483731\n38710370\n38202065\n39741296\n41781882\n18301709\n19857034\n38584340\n38819046\n34225362\n38342118\n41925345\n34613656\n71158713\n41343451\n27421369\n34694435\n34453780\n37137588\n37945935\n25319605\n20366532\n29211752\n33638523\n36735104\n38539345\n39974444\n16293598\n43938649\n41257199\n41492252\n32297846\n38170775\n32477267\n34233985\n21455946\n36101814\nHTTPSConnectionPool(host='www.billigtfiskegrej.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\n36931108\n28900732\n44295865\n31327369\n26629721\n38438301\nHTTPSConnectionPool(host='www.thistedmarinecenter.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.thistedmarinecenter.dk' doesn't match either of 'shop.thistedmarinecenter.dk', 'thistedmarinecenter.dk'\")))\n30278135\n38009850\n37749230\n30805763\n33838697\n38422847\n37323969\n30497007\n40676171\n47453518\n36698268\n40868666\n25706560\n33049269\nNo CVR number found.\n34841349\n30345223\n37971898\n39020505\n19280780\nNo CVR number found.\n39297183\n36666161\n20223731\n36596074\n39994208\n19831906\nNo CVR number found.\n42018961\n25706560\n39586207\n37203246\n25462742\n42604267\n27331939\n44258021\n25383214\nNo CVR number found.\n42630144\n31744237\n39991373\nNo CVR number found.\n17318489\nNo CVR number found.\n29574774\n34463522\n40334203\n41075864\n41244003\nNo CVR number found.\n38812718\n42961213\n26725305\n34603367\n36351403\n39232383\n33780419\n39939010\n36410949\nNo CVR number found.\n77948228\n14290842\n26441935\nNo CVR number found.\n37369705\n30299817\n21636231\n32071368\n27438407\n20759059\n42113867\nNo connection adapters were found for 'mailto:<EMAIL>'\n41399155\n40559779\n27814719\n40559779\nHTTPSConnectionPool(host='www.pink-mule.com', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.pink-mule.com' doesn't match either of 'e-pods.at', 'e-pods.de', 'e-pods.dk', 'e-pods.it', 'e-pods.nl', 'e-pods.se', 'epods.at', 'epods.be', 'epods.ch', 'epods.fi', 'epods.it', 'epods.nl', 'epods.pl', 'epods.se', 'flapour.de', 'nicotine-base.com', 'nicotine-base.dk', 'nicotinebase.dk', 'pink-mule.com', 'vapeson.dk', 'www.e-pods.at', 'www.e-pods.de', 'www.e-pods.dk', 'www.e-pods.it', 'www.e-pods.nl', 'www.e-pods.se', 'www.epods.at', 'www.epods.be', 'www.epods.ch', 'www.epods.fi', 'www.epods.it', 'www.epods.nl', 'www.epods.pl', 'www.epods.se', 'www.flapour.de', 'www.nicotine-base.com', 'www.nicotine-base.dk', 'www.nicotinebase.dk', 'www.vapeson.dk'\")))\n42692646\n43013734\n28252595\n28865031\n41515295\nHTTPSConnectionPool(host='www.dengyldneloeve.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.dengyldneloeve.dk' doesn't match either of '*.simply.com', '*.unoeuro.com', 'simply.com', 'unoeuro.com'\")))\n41079851\n30551869\n28252595\n38158724\n37332674\n25649133\n39202441\n27230709\n14982442\n32270700\n28867778\n32570674\nNo CVR number found.\n37898260\n40551832\n40373853\n40536035\n21281573\n35853367\nNo CVR number found.\n32067263\n27962076\nNo CVR number found.\n37031429\n26117275\n56570519\nNo CVR number found.\n37663484\n34687641\n32342396\n37584681\n14713239\n32337260\nNo CVR number found.\n87375617\n35620419\n39359944\n37430455\nNo CVR number found.\n41931507\nNo CVR number found.\n34570779\nHTTPSConnectionPool(host='www.stratek.dk', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001B2C7D6EA10>, 'Connection to www.stratek.dk timed out. (connect timeout=None)'))\n28645775\n30302591\n36482133\n38015613\n26065704\n25971612\n15328134\n37861227\n40410252\n37785121\n42058882\n40902406\n39807122\n42178357\n16222305\n66068714\n35033440\n41315075\n32324029\n36698268\n30611659\n31260485\n83818684\n12211090\nNo CVR number found.\n42938270\n38554565\n32895468\nNo CVR number found.\n66245918\nNo CVR number found.\nHTTPSConnectionPool(host='ny.mti.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1007)')))\n27985289\n27461131\n34583196\n18688441\n29211752\n26136040\n25273729\n39684160\n40337466\n42250430\nNo CVR number found.\n38129791\n41133791\n41818972\n39718626\n19216195\n40092064\n35678158\n14143335\nNo CVR number found.\n28297661\n33146574\nHTTPSConnectionPool(host='www.teknikken.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1007)')))\n41830875\n28679696\n34332606\n34926808\n36562943\n28529449\n29204136\n13798605\n31326087\n29617201\n39413264\n32281427\nNo CVR number found.\n33637772\nNo connection adapters were found for 'mailto:<EMAIL>'\n27397387\n31518474\n32718787\n39592797\n41993472\n34618631\n35656227\n33380712\n34217149\n43366084\n29614385\n29933820\n25169697\n36708689\n33259123\n37365513\nNo CVR number found.\n32092578\nNo CVR number found.\n33769369\n26517931\n42443611\n29829330\n39664178\n39126559\n32793169\n31960703\n39035960\n16964778\n32813267\n30337972\n13273847\n30612442\nNo CVR number found.\n19216195\n41163984\n27162789\n40290214\n21541702\n30908635\n29303711\n72282914\n35619135\n42514748\n27598846\n38848429\n33075758\n25947339\n18882434\n29038473\n41762926\n42428507\nNo CVR number found.\nNo CVR number found.\n41385065\nHTTPSConnectionPool(host='www.evape.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.evape.dk' doesn't match 'evape.dk'\")))\n41222093\n33512589\n41997133\n38295853\n33011199\n19257770\nNo CVR number found.\n38035266\n27373542\n37268399\n40916113\n33016689\nNo CVR number found.\nNo CVR number found.\n40337237\n20579641\n26207460\nHTTPSConnectionPool(host='www.beawear.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.beawear.dk' doesn't match either of '*.one.com', 'one.com'\")))\n38190091\nNo CVR number found.\n40554998\nNo CVR number found.\n13636907\nHTTPSConnectionPool(host='twelvesixteen.cc', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2CF1A17B0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n36082526\n30550269\nNo CVR number found.\nHTTPSConnectionPool(host='stylebydam.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2CF291780>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\n34227802\n42513636\n32999417\n43021524\n37383287\n41752866\n36030666\n41730412\n33576420\n19232298\nNo CVR number found.\nNo CVR number found.\n34710767\n42388637\n35386025\n37595179\n39470616\n29431361\n37759392\n31373719\n25966104\n38242636\nNo CVR number found.\n34072647\n39971836\n43256823\n40373721\n36728566\n41641592\n42554952\n34106185\n31327369\n39323109\n36714948\n33512589\n25706560\nNo CVR number found.\nNo CVR number found.\n33953291\n28843925\n38952986\n12072279\n17857878\n36977787\n43628704\n42537225\n36045043\n42194565\nNo CVR number found.\n81808619\nNo CVR number found.\n32367550\n18416646\nNo CVR number found.\n38887475\n37410381\n36029854\n29606390\n14853995\n39557614\n31705908\n36653671\n35237240\n33377002\n35824510\nNo CVR number found.\n16986704\n33059922\n35935991\n35532870\n36132655\nNo CVR number found.\nNo CVR number found.\n35737847\nNo CVR number found.\n44269473\n34879303\n28025467\n31266203\n27639070\n37460125\n33771398\n29424772\n32350100\n31839246\n39123940\n37045861\n20499044\n42157538\n41275286\n40976159\n28426887\n41963077\n17945505\n38911147\nHTTPSConnectionPool(host='campingdeals.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1007)')))\n43070665\n27921949\n42546267\n28693761\n33777175\n34607907\nNo CVR number found.\nNo CVR number found.\n42513636\n38242636\n38076183\n36044527\n38831380\n30035615\n32278590\n34481970\n33585071\n28692927\n47475910\n35833765\n73676428\n40915931\n43547747\nNo CVR number found.\n31480191\n31596084\n18962004\n19024091\n36942177\n25947339\n30204727\n41013508\n42232572\n39233991\n35842853\n35718273\n39490188\n37384631\n39600986\n37970336\n25706560\n31813891\n44293722\n32089127\nNo CVR number found.\n19111490\n31583489\n37245909\n20570377\n32261930\n26900921\n30982304\n43277774\n42081094\n35507132\n32290124\n35670939\n36931108\nNo CVR number found.\n31268052\nNo CVR number found.\nNo CVR number found.\n42349208\nHTTPSConnectionPool(host='www.lammeuld.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2D2B95330>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n40587438\n43277774\n40989781\n37302589\n42211087\n33082037\n43572407\n26594243\n37599662\n38748343\n33362455\n37453420\nNo CVR number found.\n43884581\nNo CVR number found.\n40229922\n16482781\n32338879\nNo CVR number found.\n27093299\n28679270\n21830895\n29529035\nNo CVR number found.\n31303176\n43077384\n38122703\n33864469\n32154344\nHTTPSConnectionPool(host='www.jagtogvildt.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self signed certificate (_ssl.c:1007)')))\nNo CVR number found.\nNo CVR number found.\n36022752\n27430333\n28252595\n43558048\n43725769\n34902380\n30582136\n42664944\n34465037\n41216905\n39094983\n20204192\n39839849\nNo CVR number found.\n39839849\n32339816\n30365178\n40092293\n35415602\n10177820\n36563338\n38411373\n35765786\nNo CVR number found.\n33377002\n34260990\n37386464\nNo CVR number found.\n10036925\n37268399\n35682716\n35532196\n34587116\n35642862\n36941715\n32887848\n29355096\nNo CVR number found.\nNo CVR number found.\nNo CVR number found.\n39813378\n27776175\n35134581\n43731742\n19195686\n10900492\n32773443\n42464996\n38515403\n31852242\n36891963\n37347108\nNo CVR number found.\n28702744\n45520811\n12263007\n27967027\n25432177\n42808679\nNo CVR number found.\n34688567\n35636919\n32788475\n35644113\n26457602\n38267604\n39806282\n35128980\n36560878\n37418323\n40641947\n33496362\n10147751\n38976540\nNo CVR number found.\nNo CVR number found.\nNo CVR number found.\nNo CVR number found.\n29728127\n25044738\n26989302\n36966394\n33739370\n38030914\n32564933\n35849793\n27515037\n36730307\n33777175\n43437933\n38760807\n36051698\n35874623\n20461888\n36540877\n34077819\n10108322\n37821845\n34098476\n35701036\nHTTPSConnectionPool(host='www.duft-natur.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.duft-natur.dk' doesn't match 'htz-serv-node4-pc.gpserver.dk'\")))\n42289019\n24981088\n38102303\n33575564\n43726722\nNo CVR number found.\n27711952\n28208510\n39081814\n36698268\n39354195\n34862982\n35727248\n26109124\n37798878\nNo CVR number found.\n34220980\n39411806\n38952986\n31048230\n36410949\n33392346\n43277774\n32003974\n39703491\n36738855\n41619783\n66022218\n26950627\n26124158\nNo CVR number found.\n11125441\n42506427\n42034622\n41660686\n34452091\n38059351\nNo CVR number found.\n30561317\n34688699\n80477716\n39768038\nNo CVR number found.\n41381353\n41507748\n31839513\n27159001\n27523242\n27230504\n27968120\n17857878\n31199166\nNo CVR number found.\n20623136\n26414628\n42523046\n10330076\nNo connection adapters were found for 'mailto:<EMAIL>'\n32784216\n42836508\n33785224\n31251699\nNo CVR number found.\n17523384\n38756001\n37453943\n37918075\n16194085\n30209648\n36340088\n26184355\nNo CVR number found.\n43078615\n27053521\nNo CVR number found.\nNo CVR number found.\n36229950\n34290253\nNo CVR number found.\n28943892\n36698268\nHTTPSConnectionPool(host='www.mettepshop.mettepshop.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2D7A72110>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n37833169\nNo CVR number found.\nNo CVR number found.\n39412926\n30080076\n41245085\n35640010\n29630631\n31846463\n34309132\n27640567\nNo CVR number found.\n40247270\n41639857\n37704113\n42948233\n42948233\n35516301\n35835199\n37898260\nNo CVR number found.\n17400193\n21536040\nNo CVR number found.\n35056297\n43919946\n82553711\n38973576\nNo CVR number found.\n39216280\n39178370\nNo CVR number found.\n33356579\n30523644\nNo CVR number found.\n39256088\n36946881\n77847715\nNo CVR number found.\n20962798\n33762828\n33575688\n41049286\nNo CVR number found.\n39541424\n38242636\n37919632\n38328530\n36465980\nNo CVR number found.\n41216506\n41353260\nNo CVR number found.\nNo CVR number found.\n43771469\n30097939\n38892142\n40672214\n40672214\n40552138\n41812516\n39812460\n43682490\n29402256\n82592318\n26085004\n38727060\n39329360\n34590222\nNo CVR number found.\n34603278\n21735132\n30723988\n27702937\n25937279\n42900133\n35522921\n37195073\n25826620\n41990546\nNo CVR number found.\n43663143\n80477716\n87310116\n40213511\n75082517\n13384843\n37074756\n61086013\n40170596\n38952986\n39593777\n30884175\n33009607\n33581947\nNo CVR number found.\nNo CVR number found.\n52843111\n27160239\n32788270\n33382979\n41821043\n44665603\nNo CVR number found.\n39322056\n38905937\n43865188\n40861637\nHTTPSConnectionPool(host='www.shop.adlr.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.shop.adlr.dk' doesn't match either of '*.myshopify.com', 'myshopify.com'\")))\n35974784\n44602385\n32829589\n40497684\n30686195\n38464973\nNo CVR number found.\n36698268\n26913039\n30608186\n68557828\n26573300\n38759906\n31736145\n31867940\nNo CVR number found.\n32097316\n38139320\n21804584\n26265878\n12681232\n20485337\n44470748\n40361626\nHTTPSConnectionPool(host='xn--velvreguiden-9cb.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2DAA3EE60>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\n34799725\n28544294\nNo CVR number found.\n31585538\nNo CVR number found.\n31338387\n30551869\n37469092\nNo CVR number found.\n41222093\n39667576\nNo CVR number found.\n56381813\n41406305\n33777175\n33777175\n27216811\nNo CVR number found.\n38444719\n30710177\nHTTPSConnectionPool(host='batteriforsyning.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1007)')))\n17468898\n32277020\n32443036\n29117055\n42281565\n42361283\n36201258\n12828209\n27178677\nNo CVR number found.\n28185480\n12750048\n32722954\n32373283\n41591471\n27331939\n39001233\nNo CVR number found.\n33777175\n29387958\n35327177\nNo CVR number found.\nNo CVR number found.\n39268124\n29518343\nHTTPSConnectionPool(host='www.sjoppii.com', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\n19519236\nNo CVR number found.\n57073128\n37412511\n38311697\n25330129\n32111602\n28129939\n35529918\n40954848\n36913568\n40131698\n35082107\nNo CVR number found.\n67430611\nNo CVR number found.\n32291309\n36453362\n41240512\nNo CVR number found.\n87824616\nNo CVR number found.\nHTTPSConnectionPool(host='webshop', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2DF2DE590>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n37439371\n33061102\n34428859\n12845138\n26208637\nNo CVR number found.\nNo CVR number found.\n25937279\n28504071\n35657606\n38576798\n25845846\n71158713\nNo CVR number found.\n36035811\nNo CVR number found.\n37318949\n29686882\nNo CVR number found.\n40704930\nNo CVR number found.\n36681136\n27255027\n37417327\n32626882\nHTTPSConnectionPool(host='www.spejdergear.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.spejdergear.dk' doesn't match 'spejdergear.dk'\")))\n35636919\n27231659\n31286328\n42564931\nHTTPSConnectionPool(host='www.svanel.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1007)')))\n41521112\n35252002\n16701076\n26571480\n42137782\nNo CVR number found.\n35991298\n14168893\n38067265\n30562569\n42951366\n82167315\n26664888\n36665793\n39681064\nNo CVR number found.\nNo CVR number found.\nNo CVR number found.\n37383597\n41972270\nNo CVR number found.\n41970634\n27380409\n38758640\n25706560\n27626920\n29191999\n41338520\n42504874\n31270529\nNo CVR number found.\nNo CVR number found.\n35210040\n35379118\n30805798\n37757519\nNo CVR number found.\nNo CVR number found.\n29449651\n35812709\n31267250\n35521658\n39340739\n41438541\n32262406\n10020611\n29179735\n40230386\n42686417\n41366583\n33360568\n31594510\n36699167\n40938729\n40337466\n37348902\nNo CVR number found.\nHTTPSConnectionPool(host='www.havehandel.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.havehandel.dk' doesn't match 'havehandel.dk'\")))\n28477643\nNo CVR number found.\n37839213\n41318163\n34223971\n31486513\nNo CVR number found.\n41993472\n29123721\n38411373\nHTTPSConnectionPool(host='www.vvsnetto.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1007)')))\n27921124\n30555880\nNo CVR number found.\nNo CVR number found.\n38188321\n26136938\n36408502\n40984429\nHTTPSConnectionPool(host='www.shop.poolworld.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2E0882A10>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='shop.bold.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'shop.bold.dk' doesn't match either of '*.magentohotel.dk', 'magentohotel.dk'\")))\n18609878\n31712793\n19248836\n39492210\n36447125\nNo connection adapters were found for 'mailto:<EMAIL>'\n30126653\n42826839\n30827961\n38372610\nNo CVR number found.\n43277774\n30821785\n43318810\n32205356\n28296924\n40262342\n41679212\n89935512\n19255573\nNo CVR number found.\n37356336\n32619584\n12959435\n44147157\n36931108\n41516291\nHTTPSConnectionPool(host='www.e-skilte.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.e-skilte.dk' doesn't match 'e-skilte.dk'\")))\n29788502\n28683979\n29168180\n35322116\n31890454\n31080541\n33082037\nNo CVR number found.\n11806600\n29678561\n42884375\n28205589\n37780162\n27085687\n30615689\nNo CVR number found.\n33954794\n44129965\n27216811\n41712759\nNo CVR number found.\n34614083\n37890235\n35056319\n36964863\nNo connection adapters were found for 'mailto:<EMAIL>'\nNo CVR number found.\n36698268\n31266947\nNo CVR number found.\n79301515\n34465037\nNo connection adapters were found for 'tel:3939 3102 (KUN salgs support i butik) - Ved online ordrer, brug kontaktformular'\n27043380\n32788475\n35481028\n27959903\n40018999\n25941330\n32562744\n20605405\n33052022\n28672764\n41222093\n30590937\n34802718\n37642010\n35377379\n37859702\n38354590\nHTTPSConnectionPool(host='www.tanjak.brandshop.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1007)')))\nHTTPSConnectionPool(host='www.obitsoe-viborg.brandshop.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1007)')))\n36698268\n19908844\n35904018\nNo connection adapters were found for 'mailto:<EMAIL>'\n31771382\n39156466\nNo connection adapters were found for 'mailto:<EMAIL>'\n39061023\n28896182\n39525879\n33069804\nNo CVR number found.\n42737275\n34468532\n38410393\n25731808\n35679197\n37490598\n36925876\n25063783\n26223199\nNo CVR number found.\n37357197\n37341754\n34970440\n31495172\n30515463\nHTTPSConnectionPool(host='www.kajakhuset.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2E4D0B550>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\n43727036\nNo CVR number found.\n28674473\n27382444\nNo CVR number found.\nNo connection adapters were found for 'mailto:<EMAIL>?&subject=Book fremvisning&body=Jeg Ã\u00b8nsker at booke fremvisning af jeres vogne. I kan kontakte mig via. denne mail eller ringe pÃ\u00a5 INDSÃ†T DIT NUMMER.'\n26957052\nNo CVR number found.\n33749740\n25168143\n25121139\n33772556\n33971184\nNo CVR number found.\n39730863\n41167939\n39372606\n34386161\n38735020\nNo CVR number found.\n44014254\n43277774\n34799725\nNo CVR number found.\nNo CVR number found.\nNo CVR number found.\nNo CVR number found.\n21224987\n41770562\nNo CVR number found.\n27907547\n34021155\n37283983\nNo CVR number found.\n40488022\n32553168\n38749250\nNo CVR number found.\nNo CVR number found.\n25641876\n32477534\nNo CVR number found.\n40731776\n30485491\n34054029\n34891494\n43079999\n26504481\n83142618\n34688567\n43277774\n40899057\n37861979\n36083581\n40704647\n42727458\nNo CVR number found.\n36595779\n12777574\n37548405\n40451609\n34082677\n35812512\n34418284\n43842226\nNo CVR number found.\n26386500\n37498378\n40620559\n54416814\n76843716\n40981772\n39717476\n38271725\n59297910\n36952458\n26499011\n39142805\n34132763\n33382979\n33261381\n37329479\n32270700\n40933727\n41170336\n39641259\n26402700\nNo CVR number found.\n43718487\n38393758\n27978908\n41260726\n37306770\n40334203\n21755575\nHTTPSConnectionPool(host='peterlarsenkaffeshop.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1007)')))\n38739999\n36028483\n37070025\nNo CVR number found.\n32337872\n39662620\n40641327\n61415912\n40348948\n75264400\n36950013\n43013734\nNo CVR number found.\n31857953\n36924594\n30365631\nNo CVR number found.\nNo CVR number found.\n20960140\nNo CVR number found.\n66029514\n41678453\n29827923\n27618545\nNo CVR number found.\n38374419\n30296249\n63749028\n33948786\n31810787\n28239513\n51568710\n28914458\n31332702\n43685120\n40957960\n33164866\n33050968\nNo CVR number found.\n32476449\n42133531\n42074039\n27774385\n41260734\n26921341\n33952414\n17272241\n31411793\n37787221\n34585261\n39676788\n29173648\n44182769\n33038623\n28122349\n33586833\n33147783\n42377686\n42288772\n36984066\n37136379\n42288772\n25549139\n36924489\n36931108\n29921032\nNo CVR number found.\nNo CVR number found.\n37103306\n31427215\n31932076\n36484640\n42504831\n41461470\n38179918\n26626749\n40621369\n41882735\n39422409\n35935150\n32467369\n33945507\n29794138\n18449943\n73707218\n33388691\n33595751\n43134418\nNo CVR number found.\n25706560\nHTTPSConnectionPool(host='www.bylind.com', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.bylind.com' doesn't match either of '*.mywebshop.io', 'mywebshop.io'\")))\n41198087\n13643709\n10651735\n43338404\n38152807\nHTTPSConnectionPool(host='www.kronometer.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.kronometer.dk' doesn't match either of '*.magentohotel.dk', 'magentohotel.dk'\")))\n35789111\nNo CVR number found.\n42308854\nNo CVR number found.\n37996424\nNo CVR number found.\n21672742\nNo CVR number found.\n32342930\n25965809\n42512583\nNo CVR number found.\n38704656\n37395455\n37820296\n35406980\n38411373\n18000199\n33445148\n26146925\n19176398\nNo connection adapters were found for 'mailto:<EMAIL>'\n42642975\n25921062\n32345395\nNo CVR number found.\n35568441\n37103551\n42549932\nNo CVR number found.\n41645741\n42413909\n43766937\n32570674\n41274603\n36964863\n29030898\n35118233\nNo CVR number found.\n16149845\n33157177\n43712772\n38999990\n20760036\n33455100\n19639231\n34605378\n31787904\n80994613\n41484438\n26074819\n34132763\nNo CVR number found.\n37191701\n40179372\nNo CVR number found.\n38183591\n42160628\n44527723\n42292419\n42360503\n42926841\n27085687\n26943892\n37191078\n71158713\n37836648\n29361479\n43141112\n32361730\n38748882\n25937279\nNo connection adapters were found for 'tel:Kontakt os via chatten'\n35516301\n71231518\n32341616\n43402056\n34589224\n39192624\nHTTPSConnectionPool(host='www.festissimo.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1007)')))\n42216542\n28123760\n35391584\n37538132\n35639306\n40294082\n34470030\n66920216\n15978104\nNo connection adapters were found for 'mailto:<EMAIL>'\n37764531\n43029991\n40562087\n32312373\nNo CVR number found.\n35622217\n38855301\n30711602\n27572006\n34298424\n44243539\n36024240\n15522089\n33577923\n28898541\nNo CVR number found.\n42742368\nNo CVR number found.\nNo CVR number found.\n10101913\n44458187\n34736537\nNo CVR number found.\n26091470\n31575974\n34710260\n36045043\n31743982\n35648739\n34833737\nHTTPSConnectionPool(host='www.manillo.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.manillo.dk' doesn't match '*.media.vip.interactio.io'\")))\n83818684\n39550164\n42239941\n34601534\n39281848\n35501746\n41579684\n27178448\n35109293\n38152807\n39731452\n21581437\n43409298\n35244514\n29744416\n30554949\n28181590\n14271406\n35817999\n35254501\n35867287\nNo CVR number found.\n30315014\n39431645\n42961825\n38703803\n44727773\n33042620\nNo CVR number found.\n28945930\n15854677\nNo CVR number found.\n40757295\n39176610\n36077980\n38538888\n40274960\n43765876\n40669841\n42427721\n41967218\n10138922\n43429779\n28491999\n41260734\n25706560\n27645720\n35412263\nNo CVR number found.\n35254501\n38945149\n36935952\n32291031\nNo CVR number found.\n21509841\n38360469\n32473342\n41872152\n35806040\n44728966\n37685224\n26517362\n40144625\n36303182\n32163807\n36477415\n42449245\n37411655\n42250392\n38588001\n37885223\n42169323\n39465949\n42164453\nNo CVR number found.\n10118751\n37298034\n44370034\n33042620\n34737347\n33041373\n41280913\n13824606\n34701237\n35656588\n41586036\nNo CVR number found.\n40250026\n31373328\n38451979\n18666685\n30613899\nNo CVR number found.\n29634602\n36542225\n35033737\nNo CVR number found.\nNo CVR number found.\nHTTPSConnectionPool(host='staybeautiful.dk%20+%20stay-beautiful.se', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2F1283010>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n39685000\n35775986\nNo CVR number found.\n37936820\n36696842\n76834113\n41260734\n29797714\n42417157\n43351621\n34602387\n70515113\n27292267\n33576420\nNo CVR number found.\n44127407\n38508474\n30737660\n33351682\n41557311\n28508565\n32325289\nHTTPSConnectionPool(host='ww.justfishing.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2F1944B80>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nNo CVR number found.\n35086749\n30159810\n27421369\n44705893\n34277311\n28506449\n26673267\n36202734\nNo CVR number found.\n34226261\n32077994\n27672388\n41144890\n39445409\nNo CVR number found.\n39504162\nNo CVR number found.\n36052899\n15006935\nNo CVR number found.\n39083310\nNo CVR number found.\n38511106\n33505655\n43334786\n13596492\n25248813\n37565016\n31703379\n27181961\n39668254\n43978616\n35792627\n44518066\n38893122\n37067342\n40056572\nNo connection adapters were found for 'mailto:<EMAIL>'\nNo CVR number found.\n39365944\n41222093\n41393785\nHTTPSConnectionPool(host='www.alex-cykler.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.alex-cykler.dk' doesn't match 'alex-cykler.dk'\")))\n37853909\n38567772\n29193630\n34046034\n35850287\n32778933\n38606891\n79018716\n29429006\n20425482\n28976577\nNo CVR number found.\n41885009\n26050790\n31418925\n39239264\n44224232\n35680624\n41979437\n40602542\nNo CVR number found.\n39159406\nHTTPSConnectionPool(host='xeedofficial.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'xeedofficial.dk' doesn't match either of '*.myshopify.com', 'myshopify.com'\")))\n41645342\n38760637\n30967224\n35834303\n41222093\n27470203\nNo CVR number found.\n36162457\n38297430\nHTTPSConnectionPool(host='www.vvsdesign.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2F34D4E80>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.xn--vrgumlopper-hgb.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.xn--vrgumlopper-hgb.dk' doesn't match either of '*.simply.com', '*.unoeuro.com', 'simply.com', 'unoeuro.com'\")))\nNo CVR number found.\n34880255\n36306165\n39780178\nNo CVR number found.\n38411373\n49879415\n33291353\n32784690\nNo CVR number found.\n32722954\n43626876\nHTTPSConnectionPool(host='www.unimati.biz', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\n40810366\n31308631\n37975753\n32520731\n33336209\n42170968\nNo CVR number found.\nHTTPSConnectionPool(host='www.tvbord.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\n38613073\nNo CVR number found.\nHTTPSConnectionPool(host='www.trustboxbackup.com', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1007)')))\nNo CVR number found.\n32601006\nNo CVR number found.\nNo CVR number found.\nHTTPSConnectionPool(host='www.trend.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\nHTTPSConnectionPool(host='www.traefork.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2F435A260>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n41566051\nHTTPSConnectionPool(host='www.toothpaste.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2F435AB00>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n34375038\n26404835\n34800804\n31334861\nHTTPSConnectionPool(host='www.thepets.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.thepets.dk' doesn't match either of '*.myshopify.com', 'myshopify.com'\")))\n42725285\n38076582\nNo CVR number found.\n35953906\n33391641\nHTTPSConnectionPool(host='www.tempobaadudstyr.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.tempobaadudstyr.dk' doesn't match either of '*.mywebshop.io', 'mywebshop.io'\")))\n38759906\n29641072\n35260617\n33773951\n35086412\n19248836\nHTTPSConnectionPool(host='www.syvspring.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2F43857B0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.syslriget.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2F55320E0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n18263696\n21928038\nNo CVR number found.\n40589090\n12768672\n30761650\nHTTPSConnectionPool(host='www.xn--stvlsighestefoder-10b.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.xn--stvlsighestefoder-10b.dk' doesn't match either of '*.mywebshop.io', 'mywebshop.io'\")))\nNo CVR number found.\n44366495\n33172818\n35070877\n39097168\n29817030\n10117658\nNo CVR number found.\nNo CVR number found.\n38401289\n30060156\nHTTPSConnectionPool(host='www.xn--sparkb-fya.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.xn--sparkb-fya.dk' doesn't match either of '*.simply.com', '*.unoeuro.com', 'simply.com', 'unoeuro.com'\")))\nNo CVR number found.\n41819820\n40705805\n41401184\n26596327\n26943892\n25816889\nHTTPSConnectionPool(host='www.sohu-wallstickers.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.sohu-wallstickers.dk' doesn't match either of '*.dandomain.dk', 'dandomain.dk'\")))\n21883573\n23680653\n36563958\n27085687\n26615542\n27308821\nNo CVR number found.\n43065246\n42237906\n36645709\n28912870\n18609878\nNo CVR number found.\nHTTPSConnectionPool(host='www.silkydream.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.silkydream.dk' doesn't match either of '*.simply.com', '*.unoeuro.com', 'simply.com', 'unoeuro.com'\")))\nNo CVR number found.\nNo CVR number found.\nHTTPSConnectionPool(host='www.sikkertnabolag.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.sikkertnabolag.dk' doesn't match 'pmr-staging.justsome.dev'\")))\nNo CVR number found.\n34764867\n35820477\n28922523\n36685867\n37204501\nHTTPSConnectionPool(host='www.shop-egolibris.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2F5E28EB0>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\n11806244\n43522299\n37382264\nHTTPSConnectionPool(host='www.shepherdshop.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: TLSV1_UNRECOGNIZED_NAME] tlsv1 unrecognized name (_ssl.c:1007)')))\n29805695\nNo CVR number found.\n33075839\n34929823\n39865408\n29197849\nHTTPSConnectionPool(host='www.sandri.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: EE certificate key too weak (_ssl.c:1007)')))\nHTTPSConnectionPool(host='www.samedic.com', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.samedic.com' doesn't match either of '*.mywebshop.io', 'mywebshop.io'\")))\n40382836\nHTTPSConnectionPool(host='www.ryttersrideudstyr.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2F5FA8550>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\n21014702\n28693761\nNo CVR number found.\nNo CVR number found.\nHTTPSConnectionPool(host='www.roba.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2F5FE4EB0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.rlstudioshop.dk', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001B2F7097100>, 'Connection to www.rlstudioshop.dk timed out. (connect timeout=None)'))\n31436184\n41587717\n38952986\n25447905\nHTTPSConnectionPool(host='www.regulatorcomplete.com', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.regulatorcomplete.com' doesn't match either of '*.curanet.dk', 'curanet.dk'\")))\n43357832\n41239379\n33772106\n14257047\n42944971\n32772234\nHTTPSConnectionPool(host='www.rabatshop.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2F7096C20>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n39684691\n44189623\n38488821\n27068812\n29490597\nHTTPSConnectionPool(host='www.proridershop.dk', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001B2F7097460>, 'Connection to www.proridershop.dk timed out. (connect timeout=None)'))\n38778986\n32871607\n16186201\n26950627\nNo CVR number found.\n38212311\n32890857\n38179292\n31300444\n38559125\nNo CVR number found.\n14260498\nNo CVR number found.\n38330527\nNo connection adapters were found for 'mailto:<EMAIL>'\n29066981\n37866474\nNo CVR number found.\nHTTPSConnectionPool(host='www.perfectbodycare.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.perfectbodycare.dk' doesn't match either of '*.mywebshop.io', 'mywebshop.io'\")))\n32880940\n38799487\nNo CVR number found.\nNo CVR number found.\n37980269\n35413499\n30451864\n37239321\n25258134\nHTTPSConnectionPool(host='www.outletfashion.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.outletfashion.dk' doesn't match 'outletfashion.dk'\")))\n38542222\n39715317\nNo CVR number found.\n38969579\n31617154\n31767989\nNo CVR number found.\n43380567\n29118035\n39935538\n33593481\n20847506\n38875477\n36716746\n41695056\n36981474\n40559779\n29010706\n35185593\nHTTPSConnectionPool(host='www.nordland-seeds.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.nordland-seeds.dk' doesn't match either of '*.simply.com', '*.unoeuro.com', 'simply.com', 'unoeuro.com'\")))\nNo CVR number found.\n36901357\nHTTPSConnectionPool(host='www.nordichauz.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2F939B220>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n37357839\n42110795\nHTTPSConnectionPool(host='www.nokian.center', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2F903B130>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n33267290\n28210701\n34276978\nHTTPSConnectionPool(host='www.newzealandboots.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1007)')))\n28109407\n25070089\n40408851\n30550919\n14684905\nNo CVR number found.\n37919632\n31285550\nNo CVR number found.\n41722479\nNo CVR number found.\n38603213\nHTTPSConnectionPool(host='www.mygreenelectric.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2F957D600>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.mybabysteps.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2F9A16680>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\nNo CVR number found.\nHTTPSConnectionPool(host='www.my-data.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1007)')))\n39028387\nHTTPSConnectionPool(host='www.mustang.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.mustang.dk' doesn't match 'mustang.dk'\")))\nNo CVR number found.\nNo CVR number found.\n42056626\n35866078\n40551077\n32998992\n39340194\n19318990\nNo CVR number found.\n40202005\n41033193\nNo CVR number found.\n29316082\n27066011\n27579485\n41034688\nNo CVR number found.\n39974444\nNo CVR number found.\n30855558\n35589031\n39892480\nNo CVR number found.\n30696271\nNo CVR number found.\n32551734\n26640180\n36456361\n27051170\n39967065\nNo CVR number found.\n28663730\nNo CVR number found.\n39981785\n27732585\n33322062\n34431264\nNo CVR number found.\n37520535\n42059706\n42513792\n43858114\n27233236\n44681501\n32747493\n32480616\n38579304\n38528637\n37186465\nNo CVR number found.\n33236514\n32120407\n35487409\n42296554\n42186619\n31476739\nHTTPSConnectionPool(host='www.loveurhome.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.loveurhome.dk' doesn't match either of '*.dandomain.dk', 'dandomain.dk'\")))\nNo connection adapters were found for 'mailto:<EMAIL>'\nHTTPSConnectionPool(host='www.lova.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1007)')))\n30285166\nHTTPSConnectionPool(host='www.losocofashion.com', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2FB9E4580>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\n44086573\nHTTPSConnectionPool(host='www.xn--lbeshop-q1a.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1007)')))\n34298807\n38375180\n27084176\n37775525\n41222093\n34855625\n34938482\n38799231\nNo CVR number found.\n44495147\n34857490\nHTTPSConnectionPool(host='www.lingrencosmetics.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2FC2E5DE0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n39737809\n26542847\n39255561\n37816620\n39010739\nHTTPSConnectionPool(host='www.lhsfur.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1007)')))\nHTTPSConnectionPool(host='www.leosradio.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.leosradio.dk' doesn't match either of '*.simply.com', '*.unoeuro.com', 'simply.com', 'unoeuro.com'\")))\n27583997\n43892207\n36974664\nNo CVR number found.\n39009854\n89482712\n12211090\n28500378\n27759734\n33895798\n39065037\n42121312\n42104485\n34218765\n21427136\n32942865\nNo CVR number found.\n36272023\n31499321\n28824858\n29211973\n43148699\nNo CVR number found.\n40736328\n27743994\n18101998\n36909870\n27203922\n14954376\nNo CVR number found.\n18609878\n40786309\nHTTPSConnectionPool(host='www.kaffenord.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2FCAF6230>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.kaffemesteren.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.kaffemesteren.dk' doesn't match either of '*.curanet.dk', 'curanet.dk'\")))\nHTTPSConnectionPool(host='www.jyderuprideudstyr.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.jyderuprideudstyr.dk' doesn't match either of '*.dandomain.dk', 'dandomain.dk'\")))\n27352375\nHTTPSConnectionPool(host='www.juvelgruppen.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.juvelgruppen.dk' doesn't match either of '*.one.com', 'one.com'\")))\n31759447\n34262144\n33164866\nNo CVR number found.\n30720393\n40808884\nHTTPSConnectionPool(host='www.joannaslashes.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.joannaslashes.dk' doesn't match 's21.kylos.pl'\")))\nHTTPSConnectionPool(host='www.jo-po.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.jo-po.dk' doesn't match either of '*.dandomain.dk', 'dandomain.dk'\")))\n31707285\nHTTPSConnectionPool(host='www.jewelea.com', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.jewelea.com' doesn't match either of '*.myshopify.com', 'myshopify.com'\")))\nNo CVR number found.\n18530473\n25542355\n30233174\n18721635\n32088570\n37329665\nNo CVR number found.\n37451584\n37383597\n30553101\nHTTPSConnectionPool(host='www.isadisakids.dk', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001B2FDC06080>, 'Connection to www.isadisakids.dk timed out. (connect timeout=None)'))\n37627666\n28952236\nHTTPSConnectionPool(host='www.illesliving.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.illesliving.dk' doesn't match either of '*.simply.com', '*.unoeuro.com', 'simply.com', 'unoeuro.com'\")))\n42390690\nHTTPSConnectionPool(host='www.ideshoppen.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.ideshoppen.dk' doesn't match 'htz-serv-node19-pc.gpserver.dk'\")))\n37271306\nNo CVR number found.\n39566060\n33293321\n12381239\n27578632\nNo CVR number found.\n36574216\n18619849\n30133153\nNo CVR number found.\n11757979\n40490485\n37603732\n39680440\n40126279\n38231812\n10126762\n36868074\n36147601\n19257770\n29089930\n25234782\n27412882\n32238203\nNo CVR number found.\nNo CVR number found.\n38917676\n32115268\n42691348\nHTTPSConnectionPool(host='www.hagila.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\n19883973\n31813891\n34024022\n32479324\n37647608\n20722797\nNo CVR number found.\n12519605\n28712669\nNo CVR number found.\nNo CVR number found.\n41489472\n89885019\n41482257\n35766898\nNo CVR number found.\nHTTPSConnectionPool(host='www.godstogethobby.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2FF45B4F0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nNo CVR number found.\n34655820\n28278829\nNo CVR number found.\nNo CVR number found.\n33772580\nNo CVR number found.\n42475041\n41408448\nHTTPSConnectionPool(host='www.gamestore.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1007)')))\n26357438\n25476255\n29077304\n17582542\nNo CVR number found.\nNo CVR number found.\n38161148\n25936035\n43871986\nNo CVR number found.\n40176896\n32312039\n41474505\nNo CVR number found.\n33178263\n26671256\n36132655\nNo CVR number found.\nNo CVR number found.\nNo CVR number found.\nHTTPSConnectionPool(host='www.fluegrejet.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.fluegrejet.dk' doesn't match either of '*.mywebshop.io', 'mywebshop.io'\")))\n38628399\n40933727\n28999291\n83687118\nNo CVR number found.\nHTTPSConnectionPool(host='www.feelthefeet.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1007)')))\nHTTPSConnectionPool(host='www.fashionshopping.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self signed certificate (_ssl.c:1007)')))\nNo CVR number found.\n32666493\nNo CVR number found.\n37930725\n27854613\n35036563\nNo CVR number found.\nHTTPSConnectionPool(host='www.extremecarparts.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2802C4640>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n35568263\n29366888\nHTTPSConnectionPool(host='www.etgodthelbred.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2809AFF10>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\n27255027\n28388535\n15689285\nNo CVR number found.\n37925942\n33440871\n43000640\n32948421\n12458304\n36164948\n10138698\nNo CVR number found.\nNo CVR number found.\nNo CVR number found.\nNo CVR number found.\n37530042\nNo CVR number found.\n36281847\n31947030\n40372539\nNo CVR number found.\n34840709\nNo CVR number found.\n28977905\n41457848\n32780709\n39483270\n37834262\n41939737\nNo CVR number found.\n12658931\n28712669\n37472301\n29415536\n38110977\nNo CVR number found.\nNo CVR number found.\n29791686\nHTTPSConnectionPool(host='www.dinonlineshop.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2828C6020>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n31414296\nHTTPSConnectionPool(host='www.dinguldsmedonline.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.dinguldsmedonline.dk' doesn't match either of '*.dandomain.dk', 'dandomain.dk'\")))\n18802694\n44791811\n29052301\n37689181\n30062574\n73505828\n33764421\n34585326\n29133182\n37284467\n16737143\n31780152\nHTTPSConnectionPool(host='www.demoniq.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.demoniq.dk' doesn't match either of '*.simply.com', '*.unoeuro.com', 'simply.com', 'unoeuro.com'\")))\n31473063\n39146347\nNo CVR number found.\nHTTPSConnectionPool(host='www.decorojewellery.com', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B282E7C220>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.decorbyk.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.decorbyk.dk' doesn't match either of '*.mywebshop.io', 'mywebshop.io'\")))\nNo CVR number found.\nExceeded 30 redirects.\nHTTPSConnectionPool(host='www.danskmont.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2832C2260>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\n29641021\n33267673\nNo CVR number found.\n26719127\nNo CVR number found.\n43327720\n36392819\n18440148\n28286473\nHTTPSConnectionPool(host='www.creakids.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\n33230370\nHTTPSConnectionPool(host='www.cosmage.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B28340DE40>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\n21475408\n21791040\nHTTPSConnectionPool(host='www.conceptmode.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.conceptmode.dk' doesn't match either of '*.mywebshop.io', 'mywebshop.io'\")))\n31559871\n36918942\n33599994\n37687901\n27513743\n53473458\n38044184\n38684027\nNo CVR number found.\n33425724\n39383942\n42079316\n32440649\n31425115\n30973860\nHTTPSConnectionPool(host='www.cc-trading.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B28340EFB0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n35780041\n38711466\n38856898\n27567606\n41183144\n33575629\n27591493\n43320416\n29778736\nNo CVR number found.\nNo CVR number found.\n37635715\n41488832\n25352238\nNo CVR number found.\n20592907\n31666422\n13240272\n29230544\n33663307\n40343334\n38900404\n28927967\n14889191\n26992192\n34754101\n41505656\n39878062\nNo CVR number found.\n40973389\n39945266\n21442992\n38973576\nNo CVR number found.\n37858927\n34449902\n33504314\n13587485\n37404535\n40198024\n17976796\n37269417\n35860339\n30560671\n30556844\n27256341\nHTTPSConnectionPool(host='www.billigtrampolin.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\n30552873\n95960456\n35268278\nNo CVR number found.\nHTTPSConnectionPool(host='www.billigeroser.dk', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001B2841AE980>, 'Connection to www.billigeroser.dk timed out. (connect timeout=None)'))\nNo CVR number found.\nHTTPSConnectionPool(host='www.billig-gevindundervogn.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\nHTTPSConnectionPool(host='www.billibi.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.billibi.dk' doesn't match either of '*.magentohotel.dk', 'magentohotel.dk'\")))\nNo CVR number found.\n40666044\n38821148\n38510193\n43155938\n38194186\n34691967\n27057721\nNo CVR number found.\nNo CVR number found.\n10049725\nHTTPSConnectionPool(host='www.bebelux.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1007)')))\n37259098\n41066741\n41390840\nNo CVR number found.\nNo CVR number found.\n39728362\n38262572\n44355426\n30819330\nHTTPSConnectionPool(host='www.balcicopenhagen.com', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2845BBD90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n40206418\n43538020\n33963920\nNo connection adapters were found for 'mailto:<EMAIL>'\nNo CVR number found.\nNo CVR number found.\n39802880\n28861494\n35855599\n19045846\n35385886\n15985704\n33954522\n38007238\n30776747\n38377043\n34311447\n29646899\nNo CVR number found.\n36954418\n31276764\n40075097\n41989564\n10980038\n36975385\n21385484\nHTTPSConnectionPool(host='www.adventuregrej.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.adventuregrej.dk' doesn't match either of '*.mywebshop.io', 'mywebshop.io'\")))\n26599326\nHTTPSConnectionPool(host='www.admirepreowned.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.admirepreowned.dk' doesn't match either of '*.dandomain.dk', 'dandomain.dk'\")))\n27449182\n42920940\n38206214\n36537043\n10165032\n40900632\n36410949\n39538431\n38211269\n40777636\n38389173\nHTTPSConnectionPool(host='ww.spotshop.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'ww.spotshop.dk' doesn't match either of '*.dandomain.dk', 'dandomain.dk'\")))\n40228799\nNo CVR number found.\n42324949\n29238405\nNo CVR number found.\nHTTPSConnectionPool(host='webdanes%20group%20a', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B285D5DE10>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n32340997\n41999780\nNo CVR number found.\n36650346\n41169842\n42132020\n39781999\nNo CVR number found.\n44117509\n42017213\n39701642\n36701757\nNo CVR number found.\nNo CVR number found.\n36494638\n41944560\n38905937\n34452091\n42830682\n41695277\n41493356\nNo CVR number found.\n30611659\n42232793\n44470748\n37851833\n40514260\n31347572\n39645580\n43414534\n33772017\n39435691\n43628704\nHTTPSConnectionPool(host='street-bill.com', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B28658A8C0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n29534055\n28647905\n43907573\n44265524\n43141791\n38243551\n41225149\nNo CVR number found.\n38556673\n41338520\n33111789\n42462098\nNo connection adapters were found for 'mailto:<EMAIL>'\n29693218\n40466541\n37896152\n70515113\n41756306\n42658472\nNo CVR number found.\nHTTPSConnectionPool(host='shop.incado.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\n29588139\n17746006\n44228254\n41852135\n32577962\nHTTPSConnectionPool(host='sevenmx.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2882571C0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='xn--selvklbendefolie-zob.dk', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001B288257880>, 'Connection to xn--selvklbendefolie-zob.dk timed out. (connect timeout=None)'))\n27280331\nHTTPSConnectionPool(host='sarar.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'sarar.dk' doesn't match either of '*.myshopify.com', 'myshopify.com'\")))\n39370034\nNo CVR number found.\n39610949\n41155353\n39711311\n43184067\n36641258\n35853960\n40250026\nNo CVR number found.\n44251426\n35093974\nNo CVR number found.\nNo CVR number found.\n42327166\n42337382\n38168169\n42183873\nHTTPSConnectionPool(host='phenumb.dk', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001B2897D1240>, 'Connection to phenumb.dk timed out. (connect timeout=None)'))\nNo CVR number found.\n35955445\n38334972\n41580135\n29689490\n44420139\n36966173\n36016744\n44049228\nNo CVR number found.\nNo connection adapters were found for 'mailto:<EMAIL>'\n44623994\n14333045\n37860476\nNo CVR number found.\n40051740\n44317710\n40300376\n42993336\nHTTPSConnectionPool(host='noanoa.dk', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001B28931AB90>, 'Connection to noanoa.dk timed out. (connect timeout=None)'))\nHTTPSConnectionPool(host='noa.chrschmidt.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self signed certificate (_ssl.c:1007)')))\n43077791\n36493151\n38898337\n43834967\n38539345\nNo CVR number found.\nNo CVR number found.\nNo CVR number found.\n38477773\nHTTPSConnectionPool(host='mobilopladere.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2899B55A0>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\nHTTPSConnectionPool(host='xn--mitarbejdstj-5jb.dk', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001B2899B5E10>, 'Connection to xn--mitarbejdstj-5jb.dk timed out. (connect timeout=None)'))\n43025260\n32675476\n43101773\n43512196\nHTTPSConnectionPool(host='mhome.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2899B66E0>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\n25336526\n36924594\n30139542\nHTTPSConnectionPool(host='marinecenter', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2899B7010>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nNo CVR number found.\nNo CVR number found.\nHTTPSConnectionPool(host='maggika.erhj1.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B28A55F880>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nNo CVR number found.\n25000374\n33722540\n40494669\n33576420\n43649809\n34614083\n41527307\nHTTPSConnectionPool(host='lm-scootershop.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1007)')))\n41255927\n30140117\n42926841\n31872898\n65534517\nNo CVR number found.\n26313791\n40982914\n43795570\n43425137\n38104179\n36279036\n43715089\nHTTPSConnectionPool(host='kasperkobke.com', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'kasperkobke.com' doesn't match either of '*.simply.com', '*.unoeuro.com', 'simply.com', 'unoeuro.com'\")))\n29312036\n34116415\n34662533\n40774386\n40335323\n26627435\nNo CVR number found.\n32480799\n38678442\n43847236\nNo CVR number found.\nNo CVR number found.\n36453362\nNo CVR number found.\n42867993\n37121630\n38716921\n30809564\n32286941\n33972253\nNo CVR number found.\n33320051\n44591723\nNo CVR number found.\n44144948\n40680497\nNo CVR number found.\nNo CVR number found.\nNo CVR number found.\n15707275\n30880412\n42236586\n42058882\nNo connection adapters were found for 'mailto:<EMAIL>'\n33042833\n35389474\n36408588\n42917885\nHTTPSConnectionPool(host='guldsmedenroende.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B28C109B70>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nNo CVR number found.\n44270463\n38772635\n38619446\nNo CVR number found.\n32878369\nHTTPSConnectionPool(host='frkwolff.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'frkwolff.dk' doesn't match 'htz-serv-node7-pc.gpserver.dk'\")))\n18310805\n39667347\nNo CVR number found.\n42868698\n41404035\n44504790\n42388637\n40273182\n41313862\n33437595\n34497621\nNo CVR number found.\n35409572\nNo CVR number found.\n19292495\n40371249\n43729128\n44058731\nNo connection adapters were found for 'mailto:<EMAIL>'\nNo CVR number found.\nHTTPSConnectionPool(host='e-cyklen.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B28CCF9DB0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n38798626\n39026406\nHTTPSConnectionPool(host='dorthes-hobbystue.dk', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001B28CCFA380>, 'Connection to dorthes-hobbystue.dk timed out. (connect timeout=None)'))\n18114143\n28424213\nNo CVR number found.\n28001975\nHTTPSConnectionPool(host='dealkoeb.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'dealkoeb.dk' doesn't match either of '*.simply.com', '*.unoeuro.com', 'simply.com', 'unoeuro.com'\")))\n44001667\n43668242\n21663379\nHTTPSConnectionPool(host='dan43335.danshop02.danhost.dk', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001B28CEE1900>, 'Connection to dan43335.danshop02.danhost.dk timed out. (connect timeout=None)'))\n35248056\n44095599\n39232189\n41851457\n36699191\nNo CVR number found.\n44122251\n36082526\n41177063\n25391462\n42474738\n41370831\n32764169\n25773837\n40260595\n34676283\n43883011\n40600116\n39394995\n41486163\n38783270\n43452460\n39362430\n30678591\n31985358\n43847147\n36561211\nHTTPSConnectionPool(host='butik.velowear.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B28D6CE290>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n40846786\nNo connection adapters were found for 'mailto:<EMAIL>'\n38136550\nNo CVR number found.\nHTTPSConnectionPool(host='bornejunglen.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'bornejunglen.dk' doesn't match either of '*.wordpress.com', 'wordpress.com'\")))\n36338555\nHTTPSConnectionPool(host='xn--bomlsesadler-yjb.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B28E00BCA0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n40614230\nNo CVR number found.\n36555580\n42974676\nNo CVR number found.\nHTTPSConnectionPool(host='beigebabybeige.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B28EA40400>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n10313937\n35938362\n42559504\nNo CVR number found.\n37980439\n37349968\n30035437\nHTTPSConnectionPool(host='b-o-f.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\nNo CVR number found.\nNo CVR number found.\nNo CVR number found.\n32877028\n32202136\n43055739\n41221763\n43175947\n27599486\n42447633\n42615226\n38173286\n83459816\n26478367\nHTTPSConnectionPool(host='2rservice.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname '2rservice.dk' doesn't match either of '*.simply.com', '*.unoeuro.com', 'simply.com', 'unoeuro.com'\")))\n32419275\nHTTPSConnectionPool(host='100.bananer.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B290DFB130>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.yogaga.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B290FA2950>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n42250392\nNo CVR number found.\n38194186\nNo CVR number found.\n43277774\n34458456\nHTTPSConnectionPool(host='www.tryhard.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.tryhard.dk' doesn't match either of 'topdeck.dk', 'www.topdeck.dk', 'www.notown.dk'\")))\n29429006\n31425115\n42250392\n34482853\n37091766\nHTTPSConnectionPool(host='www.supergolf.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.supergolf.dk' doesn't match either of '*.simply.com', '*.unoeuro.com', 'simply.com', 'unoeuro.com'\")))\nNo CVR number found.\nNo CVR number found.\n38969579\nHTTPSConnectionPool(host='www.stylingaudio.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B290FCB970>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n36950013\nNo CVR number found.\n26989302\nHTTPSConnectionPool(host='www.streetman.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\nHTTPSConnectionPool(host='www.squishyshoppen.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2910C8CA0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.sohu-posters.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2910C80D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.sohu-plakater.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.sohu-plakater.dk' doesn't match either of '*.dandodesign.dk', 'dandodesign.dk'\")))\nHTTPSConnectionPool(host='www.sohu-kidsstyle.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2910C9EA0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n27085687\nHTTPSConnectionPool(host='www.sneakersupply.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2910CA800>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.smykkeogsmykke.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2910CB160>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nNo CVR number found.\n27915833\nHTTPSConnectionPool(host='www.skatenation.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.skatenation.dk' doesn't match either of '*.vinmager.dk', 'vinmager.dk'\")))\n18609878\n38542222\nHTTPSConnectionPool(host='www.sigr.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B291C95180>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.shoemate.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B291C95870>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.shiatsumassage.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B291C95F30>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n31327369\nHTTPSConnectionPool(host='www.sexudstyr.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1007)')))\nNo CVR number found.\nHTTPSConnectionPool(host='www.segboardpro.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B291C97550>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.saltoshoppen.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2911E4280>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.rugsnmore.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2911E4B80>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\nNo CVR number found.\n43277774\nHTTPSConnectionPool(host='www.rabbitpets.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2911E58D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n28504071\n43884581\n29211752\nHTTPSConnectionPool(host='www.playfastgoal.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B291242C20>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nNo CVR number found.\n44602385\nHTTPSConnectionPool(host='www.outdoorsportstent.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B29126D2D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nNo CVR number found.\nNo CVR number found.\n30847539\nNo CVR number found.\n27511252\n35833765\nHTTPSConnectionPool(host='www.nibiler.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B291D4D3C0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.mywatermask.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B291D4DA20>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n30737660\nHTTPSConnectionPool(host='feelthefeet.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1007)')))\nHTTPSConnectionPool(host='www.magiskeminder.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.magiskeminder.dk' doesn't match either of '*.simply.com', '*.unoeuro.com', 'simply.com', 'unoeuro.com'\")))\n31199166\nHTTPSConnectionPool(host='www.littlenap.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.littlenap.dk' doesn't match either of '*.clickfunnels.com', 'clickfunnels.com'\")))\n27085787\nHTTPSConnectionPool(host='www.lavpris-teste.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.lavpris-teste.dk' doesn't match either of '*.cloudns.net', 'cloudns.net'\")))\n40251782\nNo CVR number found.\nHTTPSConnectionPool(host='www.koore.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B291D78DC0>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\n36698268\nNo CVR number found.\n31199166\n41280913\nNo CVR number found.\nHTTPSConnectionPool(host='www.jmstraepiller.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B29251F040>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n31199166\nNo CVR number found.\nNo CVR number found.\nNo CVR number found.\nHTTPSConnectionPool(host='www.horseshop.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.horseshop.dk' doesn't match 'karnevalsshoppen.dk'\")))\n27085687\nHTTPSConnectionPool(host='www.helenemollen-rideudstyr.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B293893FD0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n39550164\nNo CVR number found.\nHTTPSConnectionPool(host='www.godvarme.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.godvarme.dk' doesn't match 'godvarme.dk'\")))\n35833765\nHTTPSConnectionPool(host='www.frotte.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2938BDC90>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.freddystore.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2938BE5F0>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\n34800804\nNo CVR number found.\nHTTPSConnectionPool(host='www.fatlace.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2938BEFB0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nNo CVR number found.\n31327369\nNo CVR number found.\n26402700\n('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))\nHTTPSConnectionPool(host='www.emmamai.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B293B0D240>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))\nHTTPSConnectionPool(host='www.easymuscles.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B293B0E0B0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n31327369\n25937279\nHTTPSConnectionPool(host='www.dkaren.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B293B0E980>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n27887155\nHTTPSConnectionPool(host='www.dildomama.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B293B0F2E0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.dickiestowear.eu', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B293B0FC40>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n19778649\nHTTPSConnectionPool(host='www.danspa.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.danspa.dk' doesn't match either of '*.dandomain.dk', 'dandomain.dk'\")))\nHTTPSConnectionPool(host='www.danskhobbyhydro.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.danskhobbyhydro.dk' doesn't match either of '*.dandomain.dk', 'dandomain.dk'\")))\nHTTPSConnectionPool(host='www.daekster.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.daekster.dk' doesn't match either of '*.dandomain.dk', 'dandomain.dk'\")))\n41260734\n42250392\nNo CVR number found.\nHTTPSConnectionPool(host='www.xn--coolvelvre-k6a.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B293B36230>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n19318990\nHTTPSConnectionPool(host='www.capwear.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B293C7A410>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.byfroberg.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B293C7AD10>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\n26456096\nHTTPSConnectionPool(host='www.brandavenue.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.brandavenue.dk' doesn't match either of '*.myshopify.com', 'myshopify.com'\")))\nHTTPSConnectionPool(host='www.xn--brneuret-54a.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.xn--brneuret-54a.dk' doesn't match either of '*.dandomain.dk', 'dandomain.dk'\")))\n14201734\nHTTPSConnectionPool(host='www.boomin.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B293CACD60>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.bombgirl.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.bombgirl.dk' doesn't match either of '*.magentohotel.dk', 'magentohotel.dk'\")))\n40395954\n38759906\nHTTPSConnectionPool(host='www.blacph.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B293CADE70>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n26599326\nHTTPSConnectionPool(host='www.bjb-group.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B29417BD30>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))\n27421369\nNo CVR number found.\nNo CVR number found.\nNo CVR number found.\n36894288\n38133470\nNo CVR number found.\nHTTPSConnectionPool(host='www.barocque.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B294A36740>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.baratokids.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B294A36B60>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.bamsestovlers.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B294A374C0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n28693761\nNo CVR number found.\n36698268\nHTTPSConnectionPool(host='www.andless.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.andless.dk' doesn't match either of '*.simply.com', '*.unoeuro.com', 'simply.com', 'unoeuro.com'\")))\n36964863\nHTTPSConnectionPool(host='www.anabel.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.anabel.dk' doesn't match either of '*.magentohotel.dk', 'magentohotel.dk'\")))\n38007238\nHTTPSConnectionPool(host='www.amazingrugs.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B294A65D80>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.altomhaar.com', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001B294A666E0>, 'Connection to www.altomhaar.com timed out. (connect timeout=None)'))\nHTTPSConnectionPool(host='www.altijagt.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B294A67010>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.alt-lingeri.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\n18609878\nHTTPSConnectionPool(host='www.abusshoppen.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B29456C2E0>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.aboutvintage.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.aboutvintage.dk' doesn't match either of '*.simply.com', '*.unoeuro.com', 'simply.com', 'unoeuro.com'\")))\nHTTPSConnectionPool(host='www.xn--1000mbler-p8a.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'www.xn--1000mbler-p8a.dk' doesn't match either of '*.dandomain.dk', 'dandomain.dk'\")))\nHTTPSConnectionPool(host='www.frenchliving.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B29456E2C0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nNo CVR number found.\nNo CVR number found.\n36698268\nNo CVR number found.\n61415912\nNo CVR number found.\nHTTPSConnectionPool(host='sistie.dk', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001B2945FF9D0>, 'Connection to sistie.dk timed out. (connect timeout=None)'))\nHTTPSConnectionPool(host='samieshop.dk', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001B29460C250>, 'Connection to samieshop.dk timed out. (connect timeout=None)'))\n42611182\nHTTPSConnectionPool(host='rigtigedrenge.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\n10101913\n43317032\nNo CVR number found.\nHTTPSConnectionPool(host='www.klipogtorring.brandshop.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1007)')))\n31872898\n60746214\n35093974\n36698268\n31418518\n33382979\n43731742\nNo CVR number found.\n43317032\nNo connection adapters were found for 'mailto:<EMAIL>'\n70515113\n43311530\n28955405\n38508474\n36698268\n28324758\nNo CVR number found.\n38161148\n37898260\n31327369\nNo CVR number found.\n36465980\n32341616\nNo CVR number found.\nNo CVR number found.\n43315897\nHTTPSConnectionPool(host='mypetbot.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B296423160>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\n34468532\n43358235\n36698268\nHTTPSConnectionPool(host='kaffeshop.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'kaffeshop.dk' doesn't match either of '*.dandomain.dk', 'dandomain.dk'\")))\n36698268\nNo CVR number found.\n43508431\nHTTPSConnectionPool(host='god-koncert.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'god-koncert.dk' doesn't match either of '*.pressable.com', 'pressable.com'\")))\n43358235\n27203922\nHTTPSConnectionPool(host='xn--dialgt-sua.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B298C32440>: Failed to establish a new connection: [Errno 11002] getaddrinfo failed'))\n42611182\nHTTPSConnectionPool(host='chilloutbag.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B298D23FA0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nNo CVR number found.\n36465980\n36698268\nNo CVR number found.\n38508474\nHTTPSConnectionPool(host='2ofagerholt.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B298D45210>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\n37284408\nHTTPSConnectionPool(host='www.smallfashion.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: self signed certificate (_ssl.c:1007)')))\nHTTPSConnectionPool(host='www.gplbags.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\n27085687\n36698268\n78823917\n36698268\nHTTPSConnectionPool(host='dk.noanoaminiature.com', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLCertVerificationError(1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: certificate has expired (_ssl.c:1007)')))\n36698268\n36698268\n36698268\n36698268\n36698268\nHTTPSConnectionPool(host='www.brands4kids-shop.dk', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x000001B29918BB20>, 'Connection to www.brands4kids-shop.dk timed out. (connect timeout=None)'))\n36698268\nNo CVR number found.\nHTTPSConnectionPool(host='aabenraa-sejlclub', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2991BC640>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nNo CVR number found.\nNo CVR number found.\n44113910\nHTTPSConnectionPool(host='www.tagudstyr.', port=443): Max retries exceeded with url: /?dk (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B299264160>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.salonsmart.brandshop.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B299300BE0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.saloncaspian.brandshop.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1007)')))\nHTTPSConnectionPool(host='www.obitsoe-skave-borbjerg.brandshop.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1007)')))\nHTTPSConnectionPool(host='www.klippehjornet-auning.brandshop.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2993027A0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.haardesign.brandshop.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B299303100>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.gloskinbeauty', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B299303A60>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.frisorsmeden.brandshop.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B299324400>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.frisorhuset-maarslet.brandshop.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B299324D60>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.dions-shop.brandshop.dk', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B2993256C0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.cityfrisorerne-vejle.brandshop.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1007)')))\nHTTPSConnectionPool(host='just-half-price', port=443): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x000001B299326980>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))\nHTTPSConnectionPool(host='www.frisorsusanne.brandshop.dk', port=443): Max retries exceeded with url: / (Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1007)')))"
                .Split("\n").ToList();
        //var rawData = "37757519".Split("").ToList();
        //var rawData = "42066389".Split("").ToList();
        //var rawData = "34694435".Split("").ToList();
        //var rawData = "42066389".Split("").ToList();
        //rawData = "34235937".Split("").ToList();

        var httpClient = new HttpClient();
        var username = "Marlin_OS_CVR_I_SKYEN";
        var password = "1d327a98-770d-4a68-b065-920ccfb60396";
        var base64EncodedCredentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{username}:{password}"));
        //var requestUri = "http://distribution.virk.dk/cvr-permanent/virksomhed/_search";
        var requestUri = "http://distribution.virk.dk/offentliggoerelser/_search";
        httpClient.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Basic", base64EncodedCredentials);

        //var returnData = "Email\tPhone\tAd Protected\t Employee\r\n";
        var returnData = "Equity\tInventories\r\n";
        foreach (var data in rawData)
        {
            if (data != "No CVR number found." && !data.Contains("Max retries exceeded with url") &&
                !data.Contains("No connection adapters were fou") && !data.Contains("Exceeded") &&
                !data.Contains("Connection") && data != "")
            {
                var requestBody = $@"{{
    ""_source"": [

    ],
    ""query"": {{
        ""query_string"": {{
            ""default_field"": ""cvrNummer"",
            ""query"": ""{data}""
        }}
    }},
    ""size"": 20
}}";

                var request = new HttpRequestMessage
                {
                    Method = HttpMethod.Get,
                    RequestUri = new Uri(requestUri),
                    Content = new StringContent(requestBody, Encoding.UTF8, "application/json")
                };

                var equity = "";
                var inventories = "";
                var noAcountning = false;

                try
                {
                    var response = await httpClient.SendAsync(request);
                    var responseContent = await response.Content.ReadAsStringAsync();
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        var exportVirk = JsonSerializer.Deserialize<ExportVirkAccounting>(responseContent);
                        if (exportVirk?.hits.hits.Count != 0)
                        {
                            var exportVirkData = exportVirk.hits.hits
                                .OrderByDescending(a => a._source.regnskab.regnskabsperiode.slutDato).First()._source;
                            var document = exportVirkData.dokumenter.FirstOrDefault(a =>
                                a.dokumentMimeType == "application/xml" && a.dokumentType == "AARSRAPPORT");
                            if (document != null)
                            {
                                var httpClientDocument = new HttpClient();
                                httpClientDocument.DefaultRequestHeaders.AcceptEncoding.Add(
                                    new System.Net.Http.Headers.StringWithQualityHeaderValue("gzip"));
                                httpClientDocument.DefaultRequestHeaders.AcceptEncoding.Add(
                                    new System.Net.Http.Headers.StringWithQualityHeaderValue("deflate"));
                                HttpResponseMessage responseDocument =
                                    await httpClientDocument.GetAsync(document.dokumentUrl);
                                if (responseDocument.Content.Headers.ContentEncoding.Contains("gzip"))
                                {
                                    // Use GZipStream to decompress the content.
                                    using (Stream responseStream = await responseDocument.Content.ReadAsStreamAsync())
                                    using (GZipStream decompressionStream =
                                           new GZipStream(responseStream, CompressionMode.Decompress))
                                    using (StreamReader decompressedReader =
                                           new StreamReader(decompressionStream, Encoding.UTF8))
                                    {
                                        string decompressedContent = await decompressedReader.ReadToEndAsync();
                                        var contextLines = new List<string>();
                                        string patternContext = @"context\s(.*?)context>";

                                        MatchCollection matchesContext = Regex.Matches(decompressedContent,
                                            patternContext, RegexOptions.Singleline);
                                        if (matchesContext.Count > 0)
                                        {
                                            foreach (Match match in matchesContext)
                                            {
                                                contextLines.Add(match.Groups[^1].Value);
                                            }
                                        }
                                        else
                                        {
                                            Console.WriteLine("No content found between the specified tags context.");
                                        }

                                        string patternEquityFsa = @"<fsa:Equity(.*?)</fsa:Equity>";
                                        MatchCollection matchesEquityFsa = Regex.Matches(decompressedContent,
                                            patternEquityFsa, RegexOptions.Singleline);
                                        if (matchesEquityFsa.Count > 0)
                                        {
                                            foreach (Match match in matchesEquityFsa)
                                            {
                                                var line = match.Groups[1].Value;
                                                if (line.Contains("\"instant_only\""))
                                                {
                                                    equity = line.Split(">")[1];
                                                }
                                            }
                                        }

                                        if (equity == "")
                                        {
                                            string patternEquity = @"<([\w:]+):Equity\b(.*?)</\1:Equity>";
                                            MatchCollection matchesEquity = Regex.Matches(decompressedContent,
                                                patternEquity, RegexOptions.Singleline);
                                            if (matchesEquity.Count > 0)
                                            {
                                                foreach (Match match in matchesEquity)
                                                {
                                                    var line = match.Groups[^1].Value;
                                                    string contextRefValue = Regex
                                                        .Match(line, @"contextRef=""([^""]*)""").Groups[1].Value;
                                                    string decimals = Regex.Match(line, @"decimals=""([^""]*)""")
                                                        .Groups[1].Value;
                                                    string unitRef = Regex.Match(line, @"unitRef=""([^""]*)""")
                                                        .Groups[1].Value;
                                                    var contextLine = contextLines.FirstOrDefault(a =>
                                                        a.Contains($"\"{contextRefValue}\""));
                                                    if (contextLine != null)
                                                    {
                                                        if (contextLine.Contains(exportVirkData.regnskab
                                                                .regnskabsperiode.slutDato) &&
                                                            !contextLine.Contains(
                                                                "RetrospectiveInformationDimension") &&
                                                            !contextLine.Contains("explicitMember"))
                                                        {
                                                            if (decimals != "0" && decimals != "")
                                                            {
                                                                //Console.WriteLine();
                                                            }

                                                            equity = line.Split(">")[1];
                                                        }
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                if (decompressedContent.Contains("Equity"))
                                                {
                                                    Console.WriteLine();
                                                }
                                            }
                                        }


                                        string patternInventoriesFsa = @"<fsa:Inventories(.*?)</fsa:Inventories>";
                                        MatchCollection matchesInventoriesFsa = Regex.Matches(decompressedContent,
                                            patternInventoriesFsa, RegexOptions.Singleline);
                                        if (matchesEquityFsa.Count > 0)
                                        {
                                            foreach (Match match in matchesInventoriesFsa)
                                            {
                                                var line = match.Groups[1].Value;
                                                if (line.Contains("\"instant_only\""))
                                                {
                                                    inventories = line.Split(">")[1];
                                                }
                                            }
                                        }

                                        if (inventories == "")
                                        {
                                            string patternEquity = @"<([\w:]+):Inventories\b(.*?)</\1:Inventories>";
                                            MatchCollection matchesEquity = Regex.Matches(decompressedContent,
                                                patternEquity, RegexOptions.Singleline);
                                            if (matchesEquity.Count > 0)
                                            {
                                                foreach (Match match in matchesEquity)
                                                {
                                                    var line = match.Groups[^1].Value;
                                                    string contextRefValue = Regex
                                                        .Match(line, @"contextRef=""([^""]*)""").Groups[1].Value;
                                                    string decimals = Regex.Match(line, @"decimals=""([^""]*)""")
                                                        .Groups[1].Value;
                                                    string unitRef = Regex.Match(line, @"unitRef=""([^""]*)""")
                                                        .Groups[1].Value;
                                                    var contextLine = contextLines.FirstOrDefault(a =>
                                                        a.Contains($"\"{contextRefValue}\""));
                                                    if (contextLine != null)
                                                    {
                                                        if (contextLine.Contains(exportVirkData.regnskab
                                                                .regnskabsperiode.slutDato) &&
                                                            !contextLine.Contains(
                                                                "RetrospectiveInformationDimension") &&
                                                            !contextLine.Contains("explicitMember"))
                                                        {
                                                            if (decimals != "0" && decimals != "")
                                                            {
                                                                //Console.WriteLine();
                                                            }

                                                            inventories = line.Split(">")[1];
                                                        }
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                if (decompressedContent.Contains("Inventories"))
                                                {
                                                    Console.WriteLine();
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else
                        {
                            noAcountning = true;
                        }
                    }
                    else
                    {
                        Console.WriteLine("Failed 200");
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine("Error: " + e.Message);
                }

                Console.WriteLine($"{equity}\t{inventories}");
                if (equity == "" && !noAcountning)
                {
                    Console.WriteLine(data);
                }

                returnData += $"{equity}\t{inventories}\r\n";


                /*var requestBody = $@"
        {{
            ""_source"": [
                ""Vrvirksomhed.cvrNummer"",
                ""Vrvirksomhed.virksomhedMetadata.nyesteNavn.navn"",
                ""Vrvirksomhed.obligatoriskEmail"",
                ""Vrvirksomhed.virksomhedMetadata.nyesteKontaktoplysninger"",
                ""Vrvirksomhed.virksomhedMetadata.stiftelsesDato"",
                ""Vrvirksomhed.reklamebeskyttet"",
                ""Vrvirksomhed.erstMaanedsbeskaeftigelse""
            ],
            ""query"": {{
                ""query_string"": {{
                    ""default_field"": ""Vrvirksomhed.cvrNummer"",
                    ""query"": ""{data}""
                }}
            }},
            ""size"": 1
        }}

                var request = new HttpRequestMessage
                {
                    Method = HttpMethod.Get,
                    RequestUri = new Uri(requestUri),
                    Content = new StringContent(requestBody, Encoding.UTF8, "application/json")
                };

                var email = "";
                var phone = "";
                var adProtected = false;
                var employeeYears = "";

                try
                {
                    var response = await httpClient.SendAsync(request);
                    var responseContent = await response.Content.ReadAsStringAsync();
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        var exportVirk = JsonSerializer.Deserialize<ExportVirk>(responseContent);
                        if (exportVirk?.hits.hits.Count != 0)
                        {
                            var vrVirksomheded = exportVirk?.hits.hits.First()._source.Vrvirksomhed;
                            employeeYears = vrVirksomheded?.erstMaanedsbeskaeftigelse.OrderByDescending(a => a.aar).ThenByDescending(a => a.maaned).FirstOrDefault()?.antalAnsatte.ToString() ?? "Ukendt";
                            foreach (var contactInfo in vrVirksomheded.virksomhedMetadata.nyesteKontaktoplysninger)
                            {
                                if (contactInfo.Contains("@"))
                                {
                                    email = contactInfo;
                                } else if (Regex.IsMatch(contactInfo, @"^\d{8}$"))
                                {
                                    phone = contactInfo;
                                }
                            }

                            adProtected = vrVirksomheded.reklamebeskyttet;
                        }
                    }
                    else
                    {
                        Console.WriteLine("Failed 200");
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine("Error: " + e.Message);
                }


                returnData += $"{email}\t{phone}\t{adProtected}\t{employeeYears}\r\n";*/
            }

            else
            {
                returnData += "\r\n";
            }
        }

        return returnData;
    }
}