using General_Services.Models.Models;
using General_Services.Models.ModelsDal.Valyrion;
using Shared.Dto.Authentication;
using Shared.Dto.General;
using Shared.Dto.Webshop;
using Shared.Models.Merchant;
using UserDto = General_Services.Models.Models.UserDto;

namespace General_Services.Services.Users
{
    public interface IGeneralUserService
    {
        Task<List<Permission>> GetPermissions();
        Task<User> GetUser(int userId);
        Task<List<User>> GetUsers();
        Task<User?> CrudUserAsync(User user);
        Task ResetPassword(UserDto user);
        Task ForgotPassword(string email);
        Task<AuthenticationResponseValyrionDto?> LoginAsync(LoginDto loginDto, bool localhost);
        Task<TfaSetupDto> GetTfaSetup(string email);
        Task<User> ValidateTfaCode(string email, string code);
        Task<User> DeleteTfa(string email);
        Task<User?> GetUserByToken(string token);
        Task UpdatePassword(MerchantUserPartnerPortalPasswordDto merchantUserPartnerPortalPasswordDto);
        Task UpdatePassword(int UserId, PasswordDto passwordDto);
        Task<HttpResponseMessage> SendNewUserEmail(int userId, string email, string password);
    }
}