using Integration.Services.Error;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Models;
using Valyrion.Services.Models;
using ILogger = Serilog.ILogger;

namespace Valyrion_Services;

[PartnerAuthExempt]
[AllowAnonymous]
[ApiController]
[Route("")]
public class DefaultController : ControllerBase
{
    private readonly IErrorService _errorService;
    private readonly ILogger _logger;

    public DefaultController(ILogger logger, IErrorService errorService)
    {
        _logger = logger;
        _errorService = errorService;
    }

    [HttpHead]
    public IActionResult DefaultHeadAsync()
    {
        return Ok("Alive");
    }

    [HttpGet]
    public IActionResult DefaultAsync()
    {
        return Ok("Alive");
    }

    [HttpGet]
    [Route("alive")]
    public IActionResult TestAsync()
    {
        return Ok("Alive");
    }

    [HttpPost]
    [Route("error")]
    public IActionResult ErrorAsAsync(ErrorLoggingDto errorLogging)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ErrorAsync"))
            {
                var success = _errorService
                    .AddErrorAsync(errorLogging);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while logging an error");
            return Ok();
        }
    }

    [HttpPost("v1/error")]
    public async Task<IActionResult> TrackError(
        [FromHeader(Name = "X-Happy-Ads-Client-Id")] string clientId,
        [FromHeader(Name = "X-Happy-Ads-Event-Type")] string eventType,
        [FromBody] TrackingError error)
    {
        try
        {
            // Log the error
            _logger.ForContext("service_name", GetType().Name).Error($"Tracking error for event {eventType} from client {clientId}: {error.Error}");

            return Ok(new { success = true, message = "Error logged successfully" });
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error processing tracking error");
            return StatusCode(500, new { success = false, message = "Internal server error" });
        }
    }

    [HttpPost]
    [Route("debug")]
    public IActionResult DebugAsync(ErrorLoggingDto errorLogging)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ErrorAsync"))
            {
                var success = _errorService
                    .AddDebugAsync(errorLogging);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while logging an debug");
            return Ok();
        }
    }

    [HttpPost]
    [Route("information")]
    public IActionResult InformationAsync(ErrorLoggingDto errorLogging)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "InformationAsync"))
            {
                var success = _errorService
                    .AddInformationAsync(errorLogging);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while logging an information");
            return Ok();
        }
    }

    [HttpPost]
    [Route("warning")]
    public IActionResult WarningAsync(ErrorLoggingDto errorLogging)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "WarningAsync"))
            {
                var success = _errorService
                    .AddWarningAsync(errorLogging);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while logging an warning");
            return Ok();
        }
    }
}