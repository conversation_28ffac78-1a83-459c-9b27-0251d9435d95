{"$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:1356", "sslPort": 44353}}, "profiles": {"Development": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "swagger", "applicationUrl": "https://localhost:7100;http://localhost:5043", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Production": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "swagger", "applicationUrl": "https://localhost:7100;http://localhost:5043", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production"}}}}