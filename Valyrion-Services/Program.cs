using System.Net.Http.Headers;
using System.Text;
using System.Text.Json.Serialization;
using Admin_Services.Controllers;
using Admin_Services.Models.ModelsDal.Invoice;
using Admin_Services.Services.Admin;
using Admin_Services.Services.Invoice;
using Audience.Services.Audience;
using AutoMapper;
using Automation_Services.Controllers;
using Automation_Services.Services.Automation;
using Automation_Services.Services.AutomationElastic;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Campaign_Services.Controllers;
using Campaign_Services.Models.ModelsDal.Campaign;
using Campaign_Services.Services.Campaign;
using Campaign_Services.Services.Filter;
using Campaign_Services.Services.FilterElastic;
using Customer_Services.Models.ModelsDal.Customer;
using Discount_Services.Models.ModelsDal.Discount;
using Discount_Services.Services.DiscountPartners;
using Discount_Services.Services.Discounts;
using Discount_Services.Services.DiscountsPartnerV1;
using Elastic.Apm.SerilogEnricher;
using Elastic.CommonSchema.Serilog;
using Elasticsearch.Net;
using Gateway_Services.Services.Onboarding.Cms;
using General_Services.Services.Analytics.Email;
using General_Services.Services.Export;
using General_Services.Services.MerchantScore;
using General_Services.Services.Users;
using Integration.Services.Error;
using Integration.Services.Plugins.Integration;
using Integration.Services.Plugins.ShopifyIntegration;
using Integration.Services.SendGrid;
using Marlin_OS_Integration_API.Services.Plugins.CustomIntegration;
using Marlin_OS_Integration_API.Services.Plugins.DanDomainIntegration;
using Marlin_OS_Integration_API.Services.Plugins.MagentoIntegration;
using Marlin_OS_Integration_API.Services.Plugins.WoocommerceIntegration;
using Marlin_OS_Integration_API.Services.Static;
using Marlin_OS_MerchantSync_API.Services.Plugins.DanDomainIntegration;
using Marlin_OS_MerchantSync_API.Services.Plugins.ProductFeedIntegration;
using Message_Services.Models.ModelsDal.Message;
using Message_Services.Services.Message;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Logging;
using Microsoft.IdentityModel.Tokens;
using Nest;
using Notification_Service.Models.ModelsDal.Notification;
using Notification_Services.Models.ModelsDal.Notification;
using Notification_Services.Services.Notifications;
using RabbitMQ.Client;
using Scheduler_Services.Services.Scheduler;
using Serilog;
using Serilog.Core;
using Serilog.Events;
using Serilog.Exceptions;
using Serilog.Exceptions.Core;
using Serilog.Sinks.Elasticsearch;
using Shared.Models;
using Shared.Services.Cache;
using Statistics_Services.Services.Statistics;
using Valyrion_Services.Middleware;
using Valyrion_Services.Models;
using Webshop_Service.Services.DanDomain;
using Webshop_Service.Services.Webshop;
using Webshop.Webshop;
using AdminService = Admin_Services.Services.Admin.AdminService;
using Mapper = Shared.Mappings.Mapper;
using StackExchange.Redis;
using IShopifyService = Webshop_Service.Services.Shopify.IShopifyService;
using IShopService = Shop_Services.Services.Shop.IShopService;
using ShopifyService = Webshop_Service.Services.Shopify.ShopifyService;
using ShopService = Shop_Services.Services.Shop.ShopService;
using Endpoints.Controllers;
using General_Services.Controllers;
using General_Services.Models.ModelsDal.Valyrion;
using General_Services.Services.General;
using General_Services.Services.Import;
using GrowthBook;
using Invoice_Services.Models.ModelsDal.Invoice;
using Marlin_OS_Integration_API.Services.Plugins;
using Merchant_Services.Models.ModelsDal.Merchant;
using MerchantDashboard_Services.Controllers;
using MerchantDashboard_Services.Controllers.Event;
using MerchantDashboard_Services.Controllers.Feedback;
using MerchantDashboard_Services.Controllers.General;
using MerchantDashboard_Services.Controllers.Support;
using MerchantDashboard_Services.Services.Event;
using MerchantDashboard_Services.Services.MerchantDashboard;
using MerchantDashboard_Services.Services.Support;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Notification_Services;
using Partner_Services.Controllers;
using Partner_Services.Models.ModelsDal.Partner;
using Partner_Services.Services.General;
using Partner_Services.Services.PartnerData;
using Partner_Services.Services.PartnerDataSync;
using PartnerPortal_Services.Controllers;
using PartnerPortal_Services.Services.Onboarding;
using PartnerPortal_Services.Services.Onboarding.Cms;
using PartnerPortal_Services.Services.PartnerPortal;
using PartnerPortal_Services.Services.PartnerPortalPayment;
using Shared.Elastic.CampaignMailClick;
using Shared.Elastic.CampaignMailOpen;
using Shared.Elastic.DiscountOpen;
using Shared.Elastic.Elastic;
using Shared.Elastic.ShopProductsDisplays;
using Shared.Elastic.ShopProductsInteracts;
using Shared.Elastic.ShopProductsRedirects;
using Shared.Models.ModelsDal.Setting;
using Shared.Services.ExternalIntegrations;
using Shared.Services.ExternalIntegrations.ClickUp;
using Shared.Services.Image;
using Shared.Services.MerchantRelevance;
using Shared.Services.Notification;
using Shared.Services.Setting;
using Shop_Services.Controllers;
using Shop_Services.Controllers.Shop;
using Shop_Services.Models.ModelsDal.Shop;
using Shop_Services.Services.ShopInternal;
using Valyrion_Services;
using UserController = General_Services.Controllers.UserController;
using Shared.Elastic.MerchantCustomerEvent;
using Shared.Elastic.ShopProducts;
using Shop_Services.Services.Partner.V1;
using GrowthBookContext = GrowthBook.Context;
using IConnection = RabbitMQ.Client.IConnection;
using Valyrion_Services.Extensions;
using Webshop_Services.Services.Elastic;
using Partner_Services.Services.Configuration;
using Recommendation_Services;
using Recommendation_Services.Controllers;
using Recommendation_Services.ProductRelevanceService;
using Recommendation_Services.ProductRelevanceService.Controllers;
using Recommendation_Services.ProductRelevanceService.Models;
using Recommendation_Services.Services;
using Shop_Services.Services.ElasticSearchService;
using Swashbuckle.AspNetCore.SwaggerUI;

var configuration = new ConfigurationBuilder()
    .AddJsonFile(path: "appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile(path: "appsettings." + Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") + ".json",
        //.AddJsonFile(path: "appsettings.Production.json",
        optional: false, reloadOnChange: true)
    .Build();

var builder = WebApplication.CreateBuilder(args);
builder.WebHost.ConfigureKestrel(o =>
{
    o.Limits.MinRequestBodyDataRate = new MinDataRate(bytesPerSecond: 100, gracePeriod: TimeSpan.FromSeconds(10));
});

builder.Services.AddHttpClient();

#region =========== Scopes Section ===========

// Add services to the container.
builder.Services.AddScoped<IErrorService, ErrorService>();
builder.Services.AddScoped<IIntegrationService, IntegrationService>();
builder.Services.AddScoped<IShopifyService, ShopifyService>();
builder.Services.AddScoped<IDanDomainClassicService, DanDomainClassicService>();
builder.Services.AddScoped<ICustomIntegrationService, CustomIntegrationService>();
builder.Services.AddScoped<IProductFeedIntegrationService, ProductFeedIntegrationService>();
builder.Services.AddScoped<IPluginService, ShopifyPluginService>();
builder.Services.AddScoped<IPluginService, DanDomainClassicPluginService>();
builder.Services.AddScoped<IPluginService, DanDomainPluginService>();
builder.Services.AddScoped<IPluginService, MagentoPluginService>();
builder.Services.AddScoped<IPluginService, WoocommercePluginService>();
builder.Services.AddScoped<IPluginService, PrestaShopPluginService>();
//Audience
builder.Services.AddScoped<ICustomerService, CustomerService>();
builder.Services.AddScoped<ISegmentService, SegmentService>();
//Webshop
builder.Services.AddScoped<IMerchantService, MerchantService>();
//Discounts
builder.Services.AddScoped<IDiscountService, DiscountService>();
builder.Services.AddScoped<IDiscountPartnerService, DiscountPartnerService>();
builder.Services.AddScoped<IDiscountPartnerV1Service, DiscountPartnerV1Service>();
//Notifications
builder.Services.AddScoped<INotificationService, NotificationService>();
//Partner Portal
builder.Services.AddScoped<IPartnerPortalService, PartnerPortalService>();
builder.Services.AddScoped<IPartnerPortalPaymentService, PartnerPortalPaymentService>();
// Onboarding
builder.Services.AddScoped<ICmsOnboardingService, ShopifyOnboardingService>();
builder.Services.AddScoped<ICmsOnboardingService, WooCommerceOnboardingService>();
builder.Services.AddScoped<ICmsOnboardingService, DanDomainOnboardingService>();
builder.Services.AddScoped<ICmsOnboardingService, DanDomainClassicOnboardingService>();
builder.Services.AddScoped<ICmsOnboardingService, MagentoOnboardingService>();
builder.Services.AddScoped<IOnboardingService, OnboardingService>();
//Admin
builder.Services.AddScoped<IAdminService, AdminService>();
builder.Services.AddScoped<IStatisticsService, StatisticsService>();
builder.Services.AddScoped<IInvoiceService, InvoiceService>();
//Partner
builder.Services.AddScoped<IPartnerDataSyncService, PartnerDataSyncService>();
builder.Services.AddScoped<IPartnerDataService, PartnerDataService>();
//Elastic
builder.Services.AddScoped<IElasticService, ElasticService>();
builder.Services.AddScoped<IElasticCampaignMailClickService, ElasticCampaignMailClickService>();
builder.Services.AddScoped<IElasticCampaignMailOpenService, ElasticCampaignMailOpenService>();
builder.Services.AddScoped<IElasticDiscountOpenService, ElasticDiscountOpenService>();
builder.Services.AddScoped<IElasticShopProductDisplaysService, ElasticShopProductDisplaysService>();
builder.Services.AddScoped<IElasticShopProductInteractsService, ElasticShopProductInteractsService>();
builder.Services.AddScoped<IElasticShopProductRedirectsService, ElasticShopProductRedirectsService>();
builder.Services.AddScoped<IElasticShopProductService, ElasticShopProductService>();
builder.Services.AddScoped<IElasticMerchantCustomerEventService, ElasticMerchantCustomerEventService>();


//Campaign
builder.Services.AddScoped<ICampaignService, CampaignService>();
builder.Services.AddScoped<IFilterService, FilterService>();
builder.Services.AddScoped<IFilterElasticService, FilterElasticService>();
//Scheduler
builder.Services.AddScoped<ISchedulerService, SchedulerService>();
//Automation
builder.Services.AddScoped<IAutomationService, AutomationService>();
builder.Services.AddScoped<IAutomationElastic, AutomationElastic>();
//Message
builder.Services.AddScoped<IMessageService, MessageService>();
builder.Services.AddScoped<IMerchantScoreService, MerchantScoreService>();
//Shop
builder.Services.AddScoped<IShopService, ShopService>();
builder.Services.AddScoped<IShopInternalService, ShopInternalService>();

builder.Services.AddScoped<IElasticSearchService, ElasticSearchService>();
builder.Services.AddScoped<IProductService, ProductService>();
builder.Services.AddScoped<IBannerService, BannerService>();
builder.Services.AddScoped<ICategoryService, CategoryService>();
builder.Services.AddScoped<ISectionService, SectionService>();


// Comment out the Shared.Services.Product registration
// builder.Services.AddScoped<Shared.Services.Product.IProductStatusService, Shared.Services.Product.ProductStatusService>();
// Restore the original ProductStatusService registration
builder.Services.AddScoped<Merchant_Services.Services.ProductStatus.IProductStatusService, Merchant_Services.Services.ProductStatus.ProductStatusService>();
// Add memory cache service
builder.Services.AddMemoryCache();
//General
builder.Services.AddScoped<IGeneralService, GeneralService>();
builder.Services.AddScoped<IGeneralUserService, GeneralUserService>();
builder.Services.AddScoped<IExportService, ExportService>();
builder.Services.AddScoped<IAnalyticsEmailService, AnalyticsEmailService>();
builder.Services.AddScoped<IImportService, ImportService>();
//Shared
builder.Services.AddScoped<ICacheService, CacheService>();
builder.Services.AddScoped<ICacheLockManager, CacheLockManager>();
builder.Services.AddScoped<ISettingService, SettingService>();
builder.Services.AddScoped<IImageService, ImageService>();
builder.Services.AddScoped<IMerchantRelevanceService, MerchantRelevanceService>();
builder.Services.AddScoped<IExternalIntegrationService, ClickUpIntegrationService>();
// Comment out the ProductStatusCacheService since our simplified version doesn't use it
// builder.Services.AddSingleton<IProductStatusCacheService, ProductStatusCacheService>();
builder.Services.AddScoped<Microsoft.Data.SqlClient.SqlConnection>(sp => 
    new Microsoft.Data.SqlClient.SqlConnection(builder.Configuration["Connection-string"]));
//Sendgrid
builder.Services.AddScoped<ISendGridWebHookService, SendGridWebHookService>();
//Email service
builder.Services.AddScoped<IEmailService, EmailService>();
//MerchantDashboard
builder.Services.AddScoped<IMerchantDashboardService, MerchantDashboardService>();
builder.Services.AddScoped<IMerchantDashboardEventService, MerchantDashboardEventService>();
builder.Services.AddScoped<ISupportCenterService, SupportCenterService>();
builder.Services.AddScoped<IPartnerService, PartnerService>();
builder.Services.AddScoped<IMerchantElasticSync, MerchantElasticSync>();

// Add Partner Configuration Service
builder.Services.AddSingleton<IPartnerConfigurationService, PartnerConfigurationService>();

// Register the discount repository
builder.Services.AddSingleton<Discount_Services.Repositories.IDiscountRepository, Discount_Services.Repositories.InMemoryDiscountRepository>();

// Health Check
builder.Services.AddHealthChecks();

//HttpContext
builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

// Background Services
// Comment out the ProductStatusPreloadService since our simplified version doesn't need it
// builder.Services.AddHostedService<ProductStatusPreloadService>();

builder.Services.AddScoped<ICategoryService, CategoryService>();

builder.Services.AddScoped<ISectionService, SectionService>();

// Product Relevance
builder.Services.AddScoped<IProductRelevanceService, ProductRelevanceService>();

// Offer Recommendation
builder.Services.AddScoped<IOfferRecommendationService, OfferRecommendationService>();

#endregion

//=========== Key Vault ===========

/*var keyVault = configuration.GetSection("KeyVaultConfig").Get<KeyVaultConfig>();
var credential = new ClientSecretCredential(keyVault.TenantId, keyVault.ClientId, keyVault.ClientSecretId);
builder.Configuration.AddAzureKeyVault(new SecretClient(new Uri(keyVault.KVUrl), credential),
    new AzureKeyVaultConfigurationOptions());*/

var keyVaultConfig = builder.Configuration.GetSection("KeyVaultConfig").Get<KeyVaultConfig>();

// Create a ClientSecretCredential
var credential =
    new ClientSecretCredential(keyVaultConfig.TenantId, keyVaultConfig.ClientId, keyVaultConfig.ClientSecretId);

// Create a SecretClient
var secretClient = new SecretClient(new Uri(keyVaultConfig.KVUrl), credential);

// Function to retrieve all secrets
async Task<IDictionary<string, string>> GetAllSecretsAsync(SecretClient client)
{
    var secrets = new Dictionary<string, string>();

    await foreach (var secretProperties in client.GetPropertiesOfSecretsAsync())
    {
        var secret = await client.GetSecretAsync(secretProperties.Name);
        secrets.Add(secretProperties.Name, secret.Value.Value);
    }

    return secrets;
}

// Retrieve all secrets and add them to the configuration
var allSecrets = await GetAllSecretsAsync(secretClient);
builder.Configuration.AddInMemoryCollection(allSecrets);

//=========== Key Vault end ===========

// Add APM fields to config
builder.Configuration["ElasticApm:LogLevel"] =
    Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")! == "Development" ? "Trace" : "Info";
builder.Configuration["ElasticApm:ServerUrl"] = builder.Configuration["ElasticApmHost"];
builder.Configuration["ElasticApm:SecretToken"] = builder.Configuration["ElasticApmToken"];
builder.Configuration["ElasticApm:ApiKey"] = builder.Configuration["ElasticApmApiKey"];
builder.Configuration["ElasticApm:ServiceName"] = "Valyrion";

//Memory Cache
builder.Services.AddMemoryCache();
// =========== Redis cache =========== 

var config = ConfigurationOptions.Parse(builder.Configuration["Redis"]);
config.ConnectTimeout = 3000; // 3 seconds for connection timeout
config.SyncTimeout = 3000; // 3 seconds for operation timeout 
config.AllowAdmin = false; // Disable admin commands for security
config.AbortOnConnectFail = false; // Prevent throwing exceptions during transient connection issues
config.KeepAlive = 60; // Send a heartbeat every 60 seconds to keep the connection alive
config.BacklogPolicy = BacklogPolicy.FailFast; // Reject commands when backlog is exceeded

builder.Services.AddSingleton(ConnectionMultiplexer.Connect(config));
// =========== Redis cache end =========== 

//=========== Elasticsearch client logging ===========
// Setup the Elaticsearch connection pool for all clients
var pool = new SingleNodeConnectionPool(new Uri(builder.Configuration["LoggingElasticHost"]));
//=========== Elasticsearch client end logging ===========

//=========== Elasticsearch client read ===========
var poolRead = new SingleNodeConnectionPool(new Uri(builder.Configuration["ElasticHost"]));
var elasticClientRead = new ElasticClient(new ConnectionSettings(poolRead)
    .ApiKeyAuthentication(builder.Configuration["ElasticApiId"], builder.Configuration["ElasticApiKey"])
    .EnableApiVersioningHeader()
    .DisableDirectStreaming());
builder.Services.AddSingleton(elasticClientRead);

var ProdElasticUrl = "https://elasticcloud-production.es.privatelink.westeurope.azure.elastic-cloud.com:9243";
var ProdElasticApiKey = "T0l3N09ZOEI3d0NHQkIycEp1T3U6WFl5ZEhOaDhSLUtucVhZUGdpdXQ1dw==";

//=========== Elasticsearch client end read ===========

//=========== Logging Setup ===========

var sinkOptions = new ElasticsearchSinkOptions(pool)
{
    ModifyConnectionSettings = x =>
        x.ApiKeyAuthentication(builder.Configuration["SerilogId"], builder.Configuration["SerilogApiKey"]),
    CustomFormatter = new EcsTextFormatter(),
    MinimumLogEventLevel = LogEventLevel.Information,
    DetectElasticsearchVersion = true,
    RegisterTemplateFailure = RegisterTemplateRecovery.IndexAnyway,
    FailureCallback = e => Console.WriteLine("Unable to submit event " + e.MessageTemplate),
    TypeName = null,
    IndexFormat = "logs-marlin_services.runtime",
    BatchAction = ElasticOpType.Create,
};
var logger = new LoggerConfiguration()
    .Enrich.WithElasticApmCorrelationInfo()
    .Enrich.FromLogContext()
    .Enrich.WithProperty("tags", Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")!)
    .Enrich.WithProperty("service_name", "Valyrion-Services")
    .Enrich.WithExceptionDetails(new DestructuringOptionsBuilder()
        .WithDefaultDestructurers()
    )
    //.WriteTo.Console()
    .WriteTo.Elasticsearch(sinkOptions)
    .MinimumLevel.Override("Microsoft", new LoggingLevelSwitch(LogEventLevel.Warning))
    .MinimumLevel.Override("System", new LoggingLevelSwitch(LogEventLevel.Warning))
    .CreateLogger();
AppDomain.CurrentDomain.ProcessExit += (s, e) => Log.CloseAndFlush();

builder.Services.AddSingleton(logger);
builder.Host.UseSerilog(logger);
// ======== Logging setup end ========

// =========== GrowthBook feature flags =========== 
var growthBookContext = new GrowthBookContext
{
    Enabled = true,
    ApiHost = builder.Configuration["GrowthBook-ApiHost"],
    ClientKey = builder.Configuration["GrowthBook-ClientKey"]
};

var growthBook = new GrowthBook.GrowthBook(growthBookContext);
await growthBook.LoadFeatures();
builder.Services.AddSingleton<IGrowthBook>(_ => growthBook);
// =========== GrowthBook feature flags end =========== 

// ======== RabbitMQ start ========

// Cloud RabbitMQ
var factoryCloud = new ConnectionFactory
{
    HostName = builder.Configuration["CloudRabbitMq-Hostname"],
    VirtualHost = builder.Configuration["CloudRabbitMq-VirtualHost"],
    UserName = builder.Configuration["CloudRabbitMq-UserName"],
    Password = builder.Configuration["CloudRabbitMq-Password"],
    Port = 5671, // Default TLS port
    AutomaticRecoveryEnabled = true,
    RequestedHeartbeat = TimeSpan.FromSeconds(20),
    Ssl = new SslOption
    {
        Enabled = true,
        ServerName = builder.Configuration["CloudRabbitMq-Hostname"]
    }
};

var cloudRabbitConnection = factoryCloud.CreateConnection();

// Register Keyed Services
builder.Services.AddKeyedSingleton<IConnection>("cloud", cloudRabbitConnection);
//builder.Services.AddHostedService<ProductRelevanceDecayBackgroundService>();

//builder.Services.AddHostedService<RabbitMqRelevanceBackgroundService>();

// ======== RabbitMQ end ========

// Behavior Event Client Setup
builder.Services.AddHttpClient<IBehaviorEventClientService, BehaviorEventClientService>(client =>
{
    client.BaseAddress = new Uri(builder.Configuration["EventsHandler-BaseUrl"]);
    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
    client.Timeout = TimeSpan.FromSeconds(2); // Keep short to free up resources
});


// Partner DB Context
builder.Services.AddDbContext<PartnerDbContext>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);
// Audience DB Context
builder.Services.AddDbContext<CustomerDbContext>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);
builder.Services.AddDbContext<CustomerDbContextTracking>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);

// Merchant DB Context
builder.Services.AddDbContext<MerchantDbContext>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);

// Discount DB Context
builder.Services.AddDbContext<DiscountDbContext>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);
builder.Services.AddDbContext<DiscountDbContextTracking>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);

// Invoice DB Context
builder.Services.AddDbContext<InvoiceDbContext>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);

builder.Services.AddDbContext<InvoiceDbContextTracking>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);

// Message DB Context
builder.Services.AddDbContext<MessageDbContext>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);
builder.Services.AddDbContext<MessageDbContextTracking>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);

// Campaign DB Context
builder.Services.AddDbContext<CampaignDbContext>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);
builder.Services.AddDbContext<CampaignDbContextTracking>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);

// Notification DB Context
builder.Services.AddDbContext<NotificationDbContext>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);
builder.Services.AddDbContext<NotificationDbContextTracking>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);

// Valyrion DB Context
builder.Services.AddDbContext<ValyrionDbContext>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);
builder.Services.AddDbContext<ValyrionDbContextTracking>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);

// Setting DB Context
builder.Services.AddDbContext<SettingDbContext>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);

// Shop DB Context
builder.Services.AddDbContext<ShopDbContext>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);

// Product Relevance DB Context
builder.Services.AddDbContext<ProductRelevanceDbContext>(x =>
    x.UseSqlServer(builder.Configuration["Connection-string"],
        y => y.CommandTimeout(120))
);

var vaultConfig = new VaultSettings
{
    RedirectUrl = builder.Configuration["RedirectUrl"]!,
    JwtTokenKey = builder.Configuration["JwtTokenKey"]!,
    JwtIssuer = builder.Configuration["JwtIssuer"]!
};

builder.Services.AddSingleton(vaultConfig);

//Add automapper used to convert db models to dto
var mappingConfig = new MapperConfiguration(mc => { mc.AddProfile(new Mapper()); });
var autoMapper = mappingConfig.CreateMapper();
builder.Services.AddSingleton(autoMapper);

// Authentication
IdentityModelEventSource.ShowPII = true;
var jwtKey = Encoding.UTF8.GetBytes(builder.Configuration["JwtSecretLogin"]);
builder.Services.AddAuthentication(x =>
{
    x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    x.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
}).AddJwtBearer(x =>
{
    x.RequireHttpsMetadata = true;
    x.SaveToken = true;
    x.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(jwtKey),
        ValidateIssuer = false,
        ValidateAudience = false,
        ClockSkew = TimeSpan.Zero
    };
});

// Creating policies that wraps the authorization requirements
builder.Services.AddAuthorization();

builder.Services.AddPartnerServices();

// Add Recommendation Service Startup Extension somehow
builder.Services.AddRecommendationServices(builder.Configuration["Connection-string"]);

// CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy => { policy.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader(); });
});

builder.Services.AddControllers(o => o.InputFormatters.Insert(o.InputFormatters.Count, new TextPlainInputFormatter()))
    .AddJsonOptions(x =>
        x.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles)
    .AddApplicationPart(typeof(AutomationController).Assembly)
    .AddApplicationPart(typeof(FlaggedOrdersController).Assembly)
    .AddApplicationPart(typeof(AnalyticsController).Assembly)
    .AddApplicationPart(typeof(CampaignController).Assembly)
    .AddApplicationPart(typeof(CustomerController).Assembly)
    .AddApplicationPart(typeof(CustomerExternalController).Assembly)
    .AddApplicationPart(typeof(DefaultController).Assembly)
    .AddApplicationPart(typeof(ExportController).Assembly)
    .AddApplicationPart(typeof(GeneralController).Assembly)
    .AddApplicationPart(typeof(MerchantController).Assembly)
    .AddApplicationPart(typeof(UserController).Assembly)
    .AddApplicationPart(typeof(GeneralExternal).Assembly)
    .AddApplicationPart(typeof(NotificationsController).Assembly)
    .AddApplicationPart(typeof(PartnerPortalController).Assembly)
    .AddApplicationPart(typeof(SettingController).Assembly)
    .AddApplicationPart(typeof(ShopMerchantGendersController).Assembly)
    .AddApplicationPart(typeof(StatisticsController).Assembly)
    .AddApplicationPart(typeof(SyncController).Assembly)
    .AddApplicationPart(typeof(InvoiceController).Assembly)
    .AddApplicationPart(typeof(PartnerController).Assembly)
    .AddApplicationPart(typeof(PartnerPortalPaymentController).Assembly)
    .AddApplicationPart(typeof(SegmentController).Assembly)
    //In app shop start
    .AddApplicationPart(typeof(ProductController).Assembly)
    .AddApplicationPart(typeof(SectionController).Assembly)
    .AddApplicationPart(typeof(ShopInternalController).Assembly)
    //MerchantDashboard start
    .AddApplicationPart(typeof(MerchantDashboardController).Assembly)
    .AddApplicationPart(typeof(MerchantDashboardFeedbackController).Assembly)
    .AddApplicationPart(typeof(MerchantDashboardEventController).Assembly)
    .AddApplicationPart(typeof(MerchantDashboardFeedbackController).Assembly)
    .AddApplicationPart(typeof(MerchantDashboardSupportCenterController).Assembly)
    .AddApplicationPart(typeof(MerchantDashboardGeneralController).Assembly)
    .AddApplicationPart(typeof(OfferRecommendationController).Assembly)
    .AddApplicationPart(typeof(ProductRelevanceController).Assembly)
    ;
    
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

// APM always add at top level to catch as much as possible
//app.UseAllElasticApm(builder.Configuration);

// Health Check
app.MapHealthChecks("/healthz");

app.UseHttpsRedirection();      

// Swagger
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(options =>
    {
        options.SwaggerEndpoint("/swagger/v1/swagger.json", "Valyrion-Services v1");
    });
}

// CORS
app.UseCors();

// Middleware
app.UsePartnerContext();

app.UseAuthentication();
app.UseAuthorization();
app.UseExceptionHandlingMiddleware();

app.MapControllers();
app.Run();