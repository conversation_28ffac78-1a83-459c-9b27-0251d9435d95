using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Partner_Services.Models.ModelsDal.Partner;
using Microsoft.AspNetCore.Mvc.Controllers;
using Shared.Attributes;
using ILogger = Serilog.ILogger;

namespace Valyrion_Services.Middleware;

public class PartnerContextMiddleware(
    RequestDelegate next,
    ILogger logger,
    IServiceScopeFactory serviceScopeFactory,
    IMemoryCache cache)
{
    private static readonly TimeSpan CacheExpiration = TimeSpan.FromMinutes(10);
    private const int DEFAULT_PARTNER_ID = 52876;
    private static readonly Guid DEV_TOKEN = Guid.Parse("084af50a-cd6e-4a57-852c-3a1b9493ee87");

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            // Special case for health check endpoint
            // List of wildcard endpoints that are exempt from Partner authentication
            var exemptEndpoints = new[]
            {
                "/healthz",
                "/metrics",
                "/swagger",
                "/.well-known",
                "/favicon.ico",
                "/MF",
                "/admin/host",
                "/robots.txt",
                "/shopify/eventpixel",
                "/danDomainClassic/event",
                "/danDomain/event",
                "/dandomain/webhooks/ordercreated",
                "/age-de.xml",
                "/robots",
                "/robots933456.txt",
                "/merchantDashboard/general/partner-config",
                "/merchantDashboard/support/faq",
                "/user/login",
                "/.git/HEAD",
                "/partnerPortal/maintenanceMode"
            };

            if (exemptEndpoints.Any(endpoint => 
                context.Request.Path.StartsWithSegments(endpoint, StringComparison.OrdinalIgnoreCase)))
            {
                logger.Information("Exempt endpoint detected: {Path}, exempting from Partner authentication", 
                    context.Request.Path);
                context.Items["PartnerId"] = DEFAULT_PARTNER_ID;
                context.Items["IsPartnerIdExempt"] = true;
                context.Items["IsFoundByDefault"] = true;
                await next(context);
                return;
            }
            
            // Check if the endpoint has the PartnerAuthExempt attribute
            var endpoint = context.GetEndpoint();
            var isExempt = false;
            
            if (endpoint != null)
            {
                var controllerActionDescriptor = endpoint.Metadata.GetMetadata<ControllerActionDescriptor>();
                if (controllerActionDescriptor != null)
                {
                    // Check if either the controller or the action has the PartnerAuthExempt attribute
                    isExempt = controllerActionDescriptor.MethodInfo.GetCustomAttributes(typeof(PartnerAuthExemptAttribute), true).Any() ||
                              controllerActionDescriptor.ControllerTypeInfo.GetCustomAttributes(typeof(PartnerAuthExemptAttribute), true).Any();
                }
            }
            
            if (isExempt)
            {
                logger.Information("Request is exempt from Partner authentication requirement: {Path}", context.Request.Path);
                // For exempt endpoints, we still set a default Partner ID but mark it as exempt
                context.Items["PartnerId"] = DEFAULT_PARTNER_ID;
                context.Items["IsPartnerIdExempt"] = true;
                context.Items["IsFoundByDefault"] = true;
                await next(context);
                return;
            }

            var partnerId = await GetPartnerIdFromRequest(context);
            context.Items["PartnerId"] = partnerId;
            context.Items["IsPartnerIdExempt"] = false;
            await next(context);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error processing Partner ID from request");
            // Set default partner ID and continue
            context.Items["PartnerId"] = DEFAULT_PARTNER_ID;
            context.Items["IsPartnerIdExempt"] = false;
            await next(context);
        }
    }

    private async Task<int> GetPartnerIdFromRequest(HttpContext context)
    {
        // Try to get Partner ID from headers first
        if (TryGetPartnerIdFromHeaders(context, out int headerPartnerId))
        {
            logger.Information("Using Partner ID from header: {PartnerId}", headerPartnerId);
            context.Items["IsFoundByDefault"] = false;
            return headerPartnerId;
        }

        // Then try to get from token
        var token = GetPartnerToken(context);
        if (!string.IsNullOrEmpty(token))
        {
            if (Guid.TryParse(token, out Guid parsedToken))
            {
                // Handle Dev Token
                if (parsedToken == DEV_TOKEN)
                {
                    logger.Information("Dev request detected: Path: {Path}", context.Request.Path);
                    context.Items["IsDevPartnerRequest"] = true;
                    context.Items["IsFoundByDefault"] = false;
                    return DEFAULT_PARTNER_ID;
                }

                // Try to get Partner ID from cache or database
                var tokenPartnerId = await GetPartnerIdFromToken(parsedToken);
                if (tokenPartnerId > 0)
                {
                    context.Items["IsDevPartnerRequest"] = false;
                    context.Items["IsFoundByDefault"] = false;
                    return tokenPartnerId;
                }
            }
            else
            {
                logger.Warning("Invalid Partner token format: {Token}", token);
            }
        }

        // Try to get from claims if authenticated
        if (context.User.Identity?.IsAuthenticated == true)
        {
            var partnerIdClaim = context.User.Claims.FirstOrDefault(c => c.Type == "pid");
            if (partnerIdClaim != null && int.TryParse(partnerIdClaim.Value, out int claimPartnerId) && claimPartnerId > 0)
            {
                logger.Information("Using Partner ID from claims: {PartnerId}", claimPartnerId);
                context.Items["IsFoundByDefault"] = false;
                return claimPartnerId;
            }
        }

        // Fallback to default
        var requestInfo = new
        {
            Path = context.Request.Path,
            Method = context.Request.Method,
            Headers = context.Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString()),
            IsAuthenticated = context.User.Identity?.IsAuthenticated,
            UserClaims = context.User.Claims.Select(c => new { c.Type, c.Value })
        };
        logger.Information("No Partner ID found in request. Request details: {@RequestInfo}", requestInfo);
        context.Items["IsFoundByDefault"] = true;
        return DEFAULT_PARTNER_ID;
    }

    private bool TryGetPartnerIdFromHeaders(HttpContext context, out int partnerId)
    {
        partnerId = 0;

        try
        {
            // Try X-Partner-ID first
            if (context.Request.Headers.TryGetValue("X-Partner-ID", out var xPartnerIdValues))
            {
                var xPartnerId = xPartnerIdValues.ToString();
                if (!string.IsNullOrEmpty(xPartnerId) && int.TryParse(xPartnerId, out partnerId) && partnerId > 0)
                {
                    return true;
                }
            }

            // Then try PartnerId header
            if (context.Request.Headers.TryGetValue("PartnerId", out var partnerIdValues))
            {
                var plainPartnerId = partnerIdValues.ToString();
                if (!string.IsNullOrEmpty(plainPartnerId) && int.TryParse(plainPartnerId, out partnerId) && partnerId > 0)
                {
                    return true;
                }
            }
        }
        catch (Exception ex)
        {
            logger.Warning(ex, "Error parsing Partner ID from headers");
        }

        return false;
    }

    private string? GetPartnerToken(HttpContext context)
    {
        try
        {
            var apiKeyHeader = context.Request.Headers.FirstOrDefault(h =>
                string.Equals(h.Key, "apikey", StringComparison.OrdinalIgnoreCase) ||
                string.Equals(h.Key, "x-api-key", StringComparison.OrdinalIgnoreCase));

            return apiKeyHeader.Value.FirstOrDefault();
        }
        catch (Exception ex)
        {
            logger.Warning(ex, "Error getting Partner token from headers");
            return null;
        }
    }

    private async Task<int> GetPartnerIdFromToken(Guid token)
    {
        try
        {
            // Try to get from cache first
            if (cache.TryGetValue(token, out int partnerId))
            {
                return partnerId;
            }

            // If not in cache, get from database
            using var scope = serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PartnerDbContext>();
            
            partnerId = await dbContext.Partners
                .Where(p => p.PartnerToken == token)
                .Select(p => p.Id)
                .FirstOrDefaultAsync();

            if (partnerId > 0)
            {
                // Cache the result
                cache.Set(token, partnerId, new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = CacheExpiration
                });
                logger.Information("Partner validated: PartnerId: {PartnerId}, Token: {Token}", partnerId, token);
            }
            else
            {
                logger.Warning("Partner validation failed: Token: {Token} does not match any PartnerId", token);
            }

            return partnerId;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting Partner ID from token: {Token}", token);
            return 0;
        }
    }
}

// Extension method for easy middleware registration
public static class PartnerContextMiddlewareExtensions
{
    public static IApplicationBuilder UsePartnerContext(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<PartnerContextMiddleware>();
    }
} 