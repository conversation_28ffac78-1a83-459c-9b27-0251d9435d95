using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Partner_Services.Models.ModelsDal.Partner;
using ILogger = Serilog.ILogger;

namespace Valyrion_Services.Middleware;

using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;

public class PartnerTokenValidationMiddleware(
    RequestDelegate next,
    ILogger logger,
    IServiceScopeFactory serviceScopeFactory,
    IMemoryCache cache)
{
    private static readonly TimeSpan CacheExpiration = TimeSpan.FromMinutes(10);

    public async Task InvokeAsync(HttpContext context)
    {
        var partnerToken = context.Request.Headers.FirstOrDefault(h =>
            string.Equals(h.Key, "apikey", StringComparison.OrdinalIgnoreCase) ||
            string.Equals(h.Key, "x-api-key", StringComparison.OrdinalIgnoreCase)).Value.FirstOrDefault();

        switch (string.IsNullOrEmpty(partnerToken))
        {
            case false when Guid.TryParse(partnerToken, out Guid parsedToken) && parsedToken != Guid.Empty:
            {
                // ViaBill Dev Request - Skip PartnerToken validation - To Be Removed Later On
                if(parsedToken == Guid.Parse("084af50a-cd6e-4a57-852c-3a1b9493ee87"))
                {
                    logger.Information("ViaBill Dev request: Path: {Path}, Query: {Query}",
                        context.Request.Path, context.Request.QueryString);
            
                    // Add Dev to HttpContext for further use
                    context.Items["PartnerId"] = 52876;
                    context.Items["IsDevPartnerRequest"] = true;

                    // Call the next middleware in the pipeline
                    await next(context);
                }
                
                // Try to retrieve PartnerId from cache
                if (!cache.TryGetValue(parsedToken, out int partnerId))
                {
                    using (var scope = serviceScopeFactory.CreateScope())
                    {
                        var dbContext = scope.ServiceProvider.GetRequiredService<PartnerDbContext>();
                        partnerId = await dbContext.Partners
                            .Where(p => p.PartnerToken == parsedToken)
                            .Select(p => p.Id)
                            .FirstOrDefaultAsync();

                        if (partnerId > 0)
                        {
                            // Cache the PartnerId
                            cache.Set(parsedToken, partnerId, new MemoryCacheEntryOptions
                            {
                                AbsoluteExpirationRelativeToNow = CacheExpiration
                            });
                        }
                    }
                }

                if (partnerId > 0)
                {
                    // Store the PartnerId in HttpContext.Items
                    context.Items["PartnerId"] = partnerId;
                    // VB Specific - To Be Removed Later On
                    context.Items["IsDevPartnerRequest"] = false;
                    logger.Information("Partner validated: PartnerId: {PartnerId}, Token: {PartnerToken}", partnerId, partnerToken);
                }
                else
                {
                    logger.Warning("Partner validation failed: Token: {PartnerToken} does not match any PartnerId", partnerToken);
                }

                break;
            }
            case false:
                logger.Warning("Invalid PartnerToken format: {PartnerToken}", partnerToken);
                break;
        }

        // Continue to the next middleware in the pipeline
        await next(context);
    }
}