using System.Text.Json;
using ILogger = Serilog.ILogger;

namespace Valyrion_Services.Middleware;

public class ExceptionHandlingMiddleware(RequestDelegate next)
{
    public async Task Invoke(HttpContext context, ILogger logger)
    {
        try
        {
            context.Request.EnableBuffering();
            var bodyAsText = await new StreamReader(context.Request.Body).ReadToEndAsync();
            context.Request.Body.Position = 0;
            await next.Invoke(context);
            var path = context.Request.Path.Value ?? "";
            if (context.Response.StatusCode != 200 && context.Response.StatusCode != 301 &&
                context.Response.StatusCode != 302 && context.Response.StatusCode != 304 &&
                context.Response.StatusCode != 422)
            {
                if (path != "/favicon.ico" && !path.Contains("/robots") &&
                    !(context.Response.StatusCode == 404 && path.Contains("/discounts/campaign")))
                {
                    var host = context.Request.Host;
                    logger.ForContext("service_name", GetType().Name).Warning(
                        "Request Content: {RequestContent} StatusCode: {StatusCode} Method: {Method} Type: {ContentType} " +
                        "Path: {Path} HeaderAuth: {HeaderAuth} IP: {Host}",
                        bodyAsText,
                        context.Response.StatusCode, context.Request.Method, context.Request.ContentType,
                        path, JsonSerializer.Serialize(context.Request.Headers), host);
                }
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Warning(ex, "Middleware error");
            if ("Development" == Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"))
            {
                throw;
            }
        }
    }
}

public static class ExceptionHandlingMiddlewareExtensions
{
    public static IApplicationBuilder UseExceptionHandlingMiddleware(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<ExceptionHandlingMiddleware>();
    }
}