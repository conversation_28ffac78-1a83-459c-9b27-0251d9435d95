<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <RootNamespace>Valyrion_Services</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <!-- NuGet Packages -->
        <PackageReference Include="Azure.Identity" Version="1.11.4" />
        <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.2" />
        <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.6.0" />
        <PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.25.3" />
        <PackageReference Include="growthbook-c-sharp" Version="1.0.6" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.5" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.5">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.5" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="9.0.3" />
    </ItemGroup>

    <ItemGroup>
        <!-- Project References -->
        <ProjectReference Include="..\Automation-Services\Automation-Services.csproj" />
        <ProjectReference Include="..\Campaign-Services\Campaign-Services.csproj" />
        <ProjectReference Include="..\Customer-Services\Customer-Services.csproj" />
        <ProjectReference Include="..\Endpoints\Endpoints.csproj" />
        <ProjectReference Include="..\General-Services\General-Services.csproj" />
        <ProjectReference Include="..\Integration-Services\Integration-Services.csproj" />
        <ProjectReference Include="..\Invoice-Services\Invoice-Services.csproj" />
        <ProjectReference Include="..\Merchant-Services\Merchant-Services.csproj" />
        <ProjectReference Include="..\MerchantDashboard-Services\MerchantDashboard-Services.csproj" />
        <ProjectReference Include="..\Message-Services\Message-Services.csproj" />
        <ProjectReference Include="..\Notification-Services\Notification-Services.csproj" />
        <ProjectReference Include="..\PartnerPortal-Services\PartnerPortal-Services.csproj" />
        <ProjectReference Include="..\Recommendation-Services\Recommendation-Services.csproj" />
        <ProjectReference Include="..\Scheduler-Services\Scheduler-Services.csproj" />
        <ProjectReference Include="..\Shop-Services\Shop-Services.csproj" />
        <ProjectReference Include="..\Statistics-Services\Statistics-Services.csproj" />
    </ItemGroup>

    <ItemGroup>
        <!-- Static assets -->
        <None Update="favicon.ico">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Models\StaticData\Helvetica-Bold.ttf">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Models\StaticData\Poppins-ExtraBold.ttf">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Models\StaticData\Poppins-Regular.ttf">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>