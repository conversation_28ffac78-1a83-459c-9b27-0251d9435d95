using Microsoft.Extensions.DependencyInjection;
using Shared.Services.Partner;

namespace Valyrion_Services.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddPartnerServices(this IServiceCollection services)
    {
        services.AddHttpContextAccessor();
        services.AddMemoryCache();
        services.AddScoped<IPartnerContext, PartnerContext>();
        
        return services;
    }
} 