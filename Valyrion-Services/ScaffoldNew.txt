Merchant:
dotnet ef dbcontext scaffold "SERVER=marlin-sql.database.windows.net;DATABASE=ViaAds-dev;User Id=ViaAdsDev;Password=**********************;TrustServerCertificate=True" Microsoft.EntityFrameworkCore.SqlServer -o ../Merchant-Services/Models/ModelsDal/Merchant -c MerchantDbContext -f --no-onconfiguring --schema merchant --namespace Merchant_Services.Models.ModelsDal.Merchant
Customer:
dotnet ef dbcontext scaffold "SERVER=marlin-sql.database.windows.net;DATABASE=ViaAds-Dev;User Id=ViaAdsDev;Password=**********************;TrustServerCertificate=True" Microsoft.EntityFrameworkCore.SqlServer -o ../Customer-Services/Models/ModelsDal/Customer -c CustomerDbContext -d -f --no-onconfiguring --schema customer --namespace Customer_Services.Models.ModelsDal.Customer
Discounts:
dotnet ef dbcontext scaffold "SERVER=marlin-sql.database.windows.net;DATABASE=ViaAds-Dev;User Id=ViaAdsDev;Password=**********************;TrustServerCertificate=True" Microsoft.EntityFrameworkCore.SqlServer -o ../Discount-Services/Models/ModelsDal/Discount -c DiscountDbContext -d -f --no-onconfiguring --schema discount --namespace Discount_Services.Models.ModelsDal.Discount
Invoice
dotnet ef dbcontext scaffold "SERVER=marlin-sql.database.windows.net;DATABASE=ViaAds-Dev;User Id=ViaAdsDev;Password=**********************;TrustServerCertificate=True" Microsoft.EntityFrameworkCore.SqlServer -o ../Invoice-Services/Models/ModelsDal/Invoice -c InvoiceDbContext -d -f --no-onconfiguring --schema invoice --namespace Invoice_Services.Models.ModelsDal.Invoice
Notification
dotnet ef dbcontext scaffold "SERVER=marlin-sql.database.windows.net;DATABASE=ViaAds-Dev;User Id=ViaAdsDev;Password=**********************;TrustServerCertificate=True" Microsoft.EntityFrameworkCore.SqlServer -o ../Notification-Services/Models/ModelsDal/Notification -c NotificationDbContext -d -f --no-onconfiguring --schema notification --namespace Notification_Services.Models.ModelsDal.Notification
Message
dotnet ef dbcontext scaffold "SERVER=marlin-sql.database.windows.net;DATABASE=ViaAds-Dev;User Id=ViaAdsDev;Password=**********************;TrustServerCertificate=True" Microsoft.EntityFrameworkCore.SqlServer -o ../Message-Services/Models/ModelsDal/Message -c MessageDbContext -d -f --no-onconfiguring --schema message --namespace Message_Services.Models.ModelsDal.Message
Setting
dotnet ef dbcontext scaffold "SERVER=marlin-sql.database.windows.net;DATABASE=ViaAds-Dev;User Id=ViaAdsDev;Password=**********************;TrustServerCertificate=True" Microsoft.EntityFrameworkCore.SqlServer -o ../Shared/Models/ModelsDal/Setting -c SettingDbContext -d -f --no-onconfiguring --schema setting --namespace Shared.Models.ModelsDal.Setting
Valyrion
dotnet ef dbcontext scaffold "SERVER=marlin-sql.database.windows.net;DATABASE=ViaAds-Dev;User Id=ViaAdsDev;Password=**********************;TrustServerCertificate=True" Microsoft.EntityFrameworkCore.SqlServer -o ../General-Services/Models/ModelsDal/Valyrion -c ValyrionDbContext -d -f --no-onconfiguring --schema valyrion --namespace General_Services.Models.ModelsDal.Valyrion
Campaign
dotnet ef dbcontext scaffold "SERVER=marlin-sql.database.windows.net;DATABASE=ViaAds-Dev;User Id=ViaAdsDev;Password=**********************;TrustServerCertificate=True" Microsoft.EntityFrameworkCore.SqlServer -o ../Campaign-Services/Models/ModelsDal/Campaign -c CampaignDbContext -d -f --no-onconfiguring --schema campaign --namespace Campaign_Services.Models.ModelsDal.Campaign
Partner
dotnet ef dbcontext scaffold "SERVER=marlin-sql.database.windows.net;DATABASE=ViaAds-Dev;User Id=ViaAdsDev;Password=**********************;TrustServerCertificate=True" Microsoft.EntityFrameworkCore.SqlServer -o ../Partner-Services/Models/ModelsDal/Partner -c PartnerDbContext -d -f --no-onconfiguring --schema partner --namespace Partner_Services.Models.ModelsDal.Partner
Shop
dotnet ef dbcontext scaffold "SERVER=marlin-sql.database.windows.net;DATABASE=ViaAds-Dev;User Id=ViaAdsDev;Password=**********************;TrustServerCertificate=True" Microsoft.EntityFrameworkCore.SqlServer -o ../Shop-Services/Models/ModelsDal/Shop -c ShopDbContext -d -f --no-onconfiguring --schema shop --namespace Shop_Services.Models.ModelsDal.Shop
Recommendation
dotnet ef dbcontext scaffold "SERVER=marlin-sql.database.windows.net;DATABASE=ViaAds-Dev;User Id=ViaAdsDev;Password=**********************;TrustServerCertificate=True" Microsoft.EntityFrameworkCore.SqlServer -o ../REcommendation-Services/Models/ModelsDal/Recommendation -c RecommendationDbContext -d -f --no-onconfiguring --schema recommendation --namespace Recommendation_Services.Models.ModelsDal.Recommendation


Create table campaign.FilterTemplates( 
    Id int NOT NULL primary key identity(1,1), 
    CreatedDate datetime2(0) NOT NULL default getdate(), 
    LastModifiedDate datetime2(0) NOT NULL default getdate(), 
    Active bit NOT NULL default 1, 
    Name nvarchar(200) NOT NULL, 
    SortOrder int NULL 
) 



Add User
//Master
CREATE LOGIN Notification WITH PASSWORD = 'xxxxxx';
//New db
CREATE USER Notification FOR LOGIN Notification WITH DEFAULT_SCHEMA = dbo; 
ALTER ROLE db_owner ADD MEMBER Notification;




CREATE TRIGGER trg_LastModifiedDate_UserPermission
ON UserPermission
AFTER UPDATE 
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE d
    SET d.LastModifiedDate = GETDATE()
    FROM UserPermission d
    INNER JOIN inserted i ON d.Id = i.Id
END;