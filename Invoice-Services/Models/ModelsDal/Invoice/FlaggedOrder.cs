using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Invoice_Services.Models.ModelsDal.Invoice;

[Table("FlaggedOrders", Schema = "invoice")]
public partial class FlaggedOrder
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    [StringLength(1000)]
    public string OrderIds { get; set; } = null!;

    public bool Handled { get; set; }

    [Column("FK_MerchantId")]
    public int FkMerchantId { get; set; }
}
