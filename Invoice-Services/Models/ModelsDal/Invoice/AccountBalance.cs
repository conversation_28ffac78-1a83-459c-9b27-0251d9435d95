using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Invoice_Services.Models.ModelsDal.Invoice;

[Table("AccountBalances", Schema = "invoice")]
public partial class AccountBalance
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    [StringLength(200)]
    public string Name { get; set; } = null!;

    [StringLength(500)]
    public string Description { get; set; } = null!;

    [Column(TypeName = "decimal(19, 4)")]
    public decimal InvoicedAmount { get; set; }

    public bool Active { get; set; }

    [Column("FK_AccountBallanceInvoiceId")]
    public int? FkAccountBallanceInvoiceId { get; set; }

    [Column("FK_MerchantId")]
    public int FkMerchantId { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [ForeignKey("FkAccountBallanceInvoiceId")]
    [InverseProperty("AccountBalances")]
    public virtual AccountBalanceInvoice? FkAccountBallanceInvoice { get; set; }

    [InverseProperty("FkAccountBalance")]
    public virtual ICollection<OrderLine> OrderLines { get; set; } = new List<OrderLine>();
}
