using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Invoice_Services.Models.ModelsDal.Invoice;

[Table("AccountBalanceInvoices", Schema = "invoice")]
public partial class AccountBalanceInvoice
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Column(TypeName = "decimal(19, 4)")]
    public decimal Amount { get; set; }

    [Column(TypeName = "decimal(19, 4)")]
    public decimal PayedAmount { get; set; }

    [Column("FK_PayerId")]
    public int FkPayerId { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [InverseProperty("FkAccountBallanceInvoice")]
    public virtual ICollection<AccountBalance> AccountBalances { get; set; } = new List<AccountBalance>();
}
