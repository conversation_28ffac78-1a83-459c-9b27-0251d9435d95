using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace Invoice_Services.Models.ModelsDal.Invoice;

public partial class InvoiceDbContext : DbContext
{
    public InvoiceDbContext(DbContextOptions<InvoiceDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<AccountBalance> AccountBalances { get; set; }

    public virtual DbSet<AccountBalanceInvoice> AccountBalanceInvoices { get; set; }

    public virtual DbSet<FlaggedOrder> FlaggedOrders { get; set; }

    public virtual DbSet<OrderLine> OrderLines { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AccountBalance>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__AccountB__3214EC0789E6C782");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkAccountBallanceInvoice).WithMany(p => p.AccountBalances).HasConstraintName("FK__AccountBa__FK_Ac__7E8CC4B1");
        });

        modelBuilder.Entity<AccountBalanceInvoice>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__AccountB__3214EC0746055CE8");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<FlaggedOrder>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__FlaggedO__3214EC074822AA04");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<OrderLine>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__OrderLin__3214EC075D489EE2");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Handled).HasDefaultValue(false);
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkAccountBalance).WithMany(p => p.OrderLines).HasConstraintName("FK__OrderLine__FK_Ac__0A888742");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
