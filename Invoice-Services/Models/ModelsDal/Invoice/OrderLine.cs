using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Invoice_Services.Models.ModelsDal.Invoice;

[Table("OrderLines", Schema = "invoice")]
[Index("FkMerchantId", "OrderDate", Name = "nci_msft_1_OrderLines_6BCEF0BBBDB01D57C50E32663F4CF150")]
[Index("OrderId", "FkMerchantId", Name = "nci_msft_1_OrderLines_8278CF234B1377FCA8AB5B52CD9203ED")]
public partial class OrderLine
{
    [Key]
    public long Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    [StringLength(500)]
    public string OrderId { get; set; } = null!;

    [StringLength(500)]
    public string MerchantName { get; set; } = null!;

    [StringLength(200)]
    public string Status { get; set; } = null!;

    [Precision(0)]
    public DateTime OrderDate { get; set; }

    [Precision(0)]
    public DateTime OrderLastModified { get; set; }

    [Column(TypeName = "decimal(19, 4)")]
    public decimal TotalPrice { get; set; }

    [Column(TypeName = "decimal(19, 4)")]
    public decimal TotalPriceTax { get; set; }

    public bool Refund { get; set; }

    [Column(TypeName = "decimal(19, 4)")]
    public decimal? CorrectTax { get; set; }

    [StringLength(255)]
    public string Email { get; set; } = null!;

    [Column(TypeName = "decimal(19, 4)")]
    public decimal PaymentPercentage { get; set; }

    [Column(TypeName = "decimal(19, 4)")]
    public decimal TotalCut { get; set; }

    [StringLength(50)]
    public string ValyrionStatus { get; set; } = null!;

    [StringLength(500)]
    public string? CustomerName { get; set; }

    [StringLength(100)]
    public string ExposureTrigger { get; set; } = null!;

    [StringLength(500)]
    public string? ExposureTriggerId { get; set; }

    [Precision(0)]
    public DateTime ExposureDate { get; set; }

    [StringLength(100)]
    public string? ActionType { get; set; }

    public bool? EmailEngaged { get; set; }

    [StringLength(100)]
    public string? Gender { get; set; }

    public byte? Age { get; set; }

    [Column("FK_MerchantId")]
    public int FkMerchantId { get; set; }

    public bool? Handled { get; set; }

    [StringLength(100)]
    public string? AttributionType { get; set; }

    [Column("FK_AccountBalanceId")]
    public int? FkAccountBalanceId { get; set; }

    [StringLength(100)]
    public string? CustomerCity { get; set; }

    [StringLength(2000)]
    public string? CustomerExposureData { get; set; }

    [StringLength(200)]
    public string? Phone { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [ForeignKey("FkAccountBalanceId")]
    [InverseProperty("OrderLines")]
    public virtual AccountBalance? FkAccountBalance { get; set; }
}
