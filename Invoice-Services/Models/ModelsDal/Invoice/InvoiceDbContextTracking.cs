using Invoice_Services.Models.ModelsDal.Invoice;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Shared;
using IConnection = RabbitMQ.Client.IConnection;

namespace Admin_Services.Models.ModelsDal.Invoice;

public class InvoiceDbContextTracking(
    DbContextOptions<InvoiceDbContext> options,
    IHttpContextAccessor httpContextAccessor,
    [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud)
    //IConnection rabbitConnection)
    : InvoiceDbContext(options)
{
    public virtual async Task<int> SaveChangesAsync()
    {
        OnBeforeSaveChanges();
        var result = await base.SaveChangesAsync();
        return result;
    }

    public virtual int SaveChanges()
    {
        OnBeforeSaveChanges();
        var result = base.SaveChanges();
        return result;
    }

    private void OnBeforeSaveChanges()
    {
        AuditSaveDb.OnBeforeSaveChanges(ChangeTracker, httpContextAccessor, rabbitConnectionCloud);
    }
}