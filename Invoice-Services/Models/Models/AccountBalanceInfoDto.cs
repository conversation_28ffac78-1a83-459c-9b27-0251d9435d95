using Invoice_Services.Models.ModelsDal.Invoice;

namespace Admin_Services.Models.Models;

public class AccountBalanceInfoDto
{
    public long? Opens { get; set; }
    public DateTime? Period { get; set; }
    public int? TotalOrders { get; set; }
    public decimal? TotalPriceWithVat { get; set; }
    public decimal? TotalPriceVat { get; set; }
    public decimal? TotalPriceExVat { get; set; }
    public long? TotalExposures { get; set; }
}