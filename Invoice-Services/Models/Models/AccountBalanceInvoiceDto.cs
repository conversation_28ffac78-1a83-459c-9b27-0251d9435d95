using Invoice_Services.Models.ModelsDal.Invoice;

namespace Admin_Services.Models.Models;

public class AccountBalanceInvoiceDto
{
    public string PayerType { get; set; }
    public int Id { get; set; }
    public DateTime CreatedDate { get; set; }

    public decimal Amount { get; set; }
    public decimal LastMonth { get; set; }

    public decimal PayedAmount { get; set; }

    public int FkPayerId { get; set; }

    public virtual ICollection<AccountBalance> AccountBalances { get; set; } = new List<AccountBalance>();
}