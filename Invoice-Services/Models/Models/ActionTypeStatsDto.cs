namespace Invoice_Services.Models.Models;

/// <summary>
/// DTO for action type statistics response
/// </summary>
public class ActionTypeStatsDto
{
    /// <summary>
    /// The action type
    /// </summary>
    public string? ActionType { get; set; }
    
    /// <summary>
    /// Number of rows for this action type
    /// </summary>
    public int NumberOfRows { get; set; }
    
    /// <summary>
    /// Sum of total prices for this action type
    /// </summary>
    public decimal TotalPriceSum { get; set; }
    
    /// <summary>
    /// Sum of total price tax for this action type
    /// </summary>
    public decimal TotalPriceTaxSum { get; set; }
    
    /// <summary>
    /// Sum of total cut for this action type
    /// </summary>
    public decimal TotalCutSum { get; set; }
} 