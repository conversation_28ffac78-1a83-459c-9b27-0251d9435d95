using Admin_Services.Models.Models;
using Invoice_Services.Models.ModelsDal.Invoice;
using Invoice_Services.Models.Models;
using Shared.Models;

namespace Admin_Services.Services.Invoice;

public interface IInvoiceService
{
    //Stats
    Task<InvoiceOverviewStatsDto> GetInvoiceOverview(DateTime from, DateTime to, bool invoiced);
    Task<(decimal totalRevenue, decimal cpaRevenue, decimal cpmRevenue, decimal baseFeeRevenue)> CalculateRevenueByPeriodAsync(DateTime from, DateTime to);
    Task<List<decimal>> CalculateRevenuePerDayByPeriodAsync(DateTime from, DateTime to);

    //Invoice
    Task<List<AccountBalanceInvoiceDto>> GetReadyForInvoice();
    Task<List<AccountBalanceInvoiceDto>> GetInvoicesAsync();
    Task<FileDto> InvoiceAsync(int payerId);
    Task<FileDto> ExportInvoiceAsync(int payerId);

    Task<FileDto> GetInvoiceAsync(int accountBalanceInvoiceId, AccountBalanceInvoice? accountBalanceInvoice = null,
        bool invoiceExport = false);
    Task<List<OrderLine>> RemoveReturnInvoiceLines();
    Task<CurrentRevenueInfoDto> GetCurrentRevenue(DateTime from, DateTime to);
    
    // Action Type Stats
    Task<List<ActionTypeStatsDto>> GetActionTypeStatsByPartnerIdAsync(int partnerId, DateTime? from = null, DateTime? to = null);
}