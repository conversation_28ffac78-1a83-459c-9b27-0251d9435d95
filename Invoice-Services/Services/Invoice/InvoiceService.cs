#region

using System.Globalization;
using System.Text.Json;
using Admin_Services.Models.Models;
using Admin_Services.Models.ModelsDal.Invoice;
using ClosedXML.Excel;
using Invoice_Services.Models.ModelsDal.Invoice;
using Invoice_Services.Models.Models;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Shared.Dto.Merchants;
using Shared.Elastic.CampaignMailOpen;
using Shared.Elastic.DiscountOpen;
using Shared.Elastic.ShopProductsDisplays;
using Shared.Helpers.Converters;
using Shared.Models;
using Shared.Models.Merchant;
using Shared.Services.Cache;
using Shared.Services.Partner;
using Webshop.Webshop;

#endregion

namespace Admin_Services.Services.Invoice;

public class InvoiceService(
    InvoiceDbContextTracking invoiceDbContextTracking,
    IMerchantService merchantService,
    IElasticCampaignMailOpenService elasticCampaignMailOpenService,
    IElasticDiscountOpenService elasticDiscountOpenService,
    IElasticShopProductDisplaysService elasticShopProductDisplaysService,
    IConfiguration configuration,
    ICacheService cacheService,
    IPartnerContext partnerContext)
    : IInvoiceService
{
    private IInvoiceService _invoiceServiceImplementation;
    private const string Step1Name = "CPA";
    private const string Step1ReturnName = "CPA Returns";
    private const string Step2Name = "CPM";
    private const string Step3Name = "Base fee";

    public async Task<InvoiceOverviewStatsDto> GetInvoiceOverview(DateTime from, DateTime to, bool invoiced)
    {
        var partnerId = partnerContext.PartnerId;
        var invoiceOverviewStatsDto = new InvoiceOverviewStatsDto();

        var merchants = await merchantService.GetAllAsync();
        
        if (invoiced)
        {
            //Get month interval from dates
            //Minus current month
            //Cpm stats from invoice data
            //Cpa orderLines

            var monthNames = GetMonthNamesBetweenDates(from, to);
            //TimeZone
            //from = from.AddDays(-1);

            List<decimal> turnOver = [];
            List<decimal> returns = [];
            for (int i = 0; i < monthNames.Count; i++)
            {
                turnOver.Add(0);
                returns.Add(0);
            }

            //CPM
            var accountBalances = invoiceDbContextTracking.AccountBalances.Where(a => a.CreatedDate >= from)
                .Where(a => a.Name == "CPM" && a.FkPartnerId == partnerId).ToList();
            foreach (var accountBalance in accountBalances)
            {
                var accountBalanceInfo = JsonSerializer.Deserialize<AccountBalanceInfoDto>(accountBalance.Description);
                if (accountBalanceInfo == null)
                    continue;


                var createdDate = accountBalance.CreatedDate.AddMonths(-1);
                var monthIndex = GetMonthIndexFromDate(createdDate, monthNames);
                
                if (monthIndex >= 0 && monthIndex < turnOver.Count)
                {
                    turnOver[monthIndex] += accountBalance.InvoicedAmount;
                }
            }

            //Base Fee
            var accountBalancesBaseFee = invoiceDbContextTracking.AccountBalances.Where(a => a.CreatedDate >= from)
                .Where(a => a.Name == "Base fee" && a.FkPartnerId == partnerId).ToList();
            foreach (var accountBalance in accountBalancesBaseFee)
            {
                var accountBalanceInfo = JsonSerializer.Deserialize<AccountBalanceInfoDto>(accountBalance.Description);
                if (accountBalanceInfo == null)
                    continue;


                var createdDate = accountBalance.CreatedDate.AddMonths(-1);
                var monthIndex = GetMonthIndexFromDate(createdDate, monthNames);
                
                if (monthIndex >= 0 && monthIndex < turnOver.Count)
                {
                    turnOver[monthIndex] += accountBalance.InvoicedAmount;
                }
            }

            //CPA
            var invoiceLines = invoiceDbContextTracking.OrderLines
                .Where(a => a.OrderDate >= from && a.FkPartnerId == partnerId).ToList();
            foreach (var invoiceLine in invoiceLines)
            {
                var createdDate = DateTimeExtensions.ConvertToCopenhagenTime(invoiceLine.OrderDate);
                var monthIndex = GetMonthIndexFromDate(createdDate, monthNames);
                if (monthIndex == -1)
                    continue;

                if (monthIndex < 0 || monthIndex >= returns.Count || monthIndex >= turnOver.Count)
                    continue;

                if (invoiceLine.TotalCut < 0)
                {
                    var refunds = invoiceLine.TotalCut;
                    returns[monthIndex] += refunds;
                }
                else
                {
                    turnOver[monthIndex] += invoiceLine.TotalCut;
                }
            }

            invoiceOverviewStatsDto.Returns = returns;
            invoiceOverviewStatsDto.MonthNames = monthNames;
            invoiceOverviewStatsDto.TurnOver = turnOver;
        }
        else
        {
            //Current month stats not invoiced
            var stats = await GetReadyForInvoice();
            var monthNames = GetMonthNamesBetweenDates(from, to);
            //TimeZone
            //from = from.AddDays(-1);
            List<decimal> turnOver = [];
            List<decimal> returns = [];
            for (int i = 0; i < monthNames.Count; i++)
            {
                turnOver.Add(0);
                returns.Add(0);
            }

            //CPM
            foreach (var stat in stats)
            {
                foreach (var accountBalance in stat.AccountBalances.Where(a => a.Name == "CPM" && a.FkPartnerId == partnerId))
                {
                    var accountBalanceInfo =
                        JsonSerializer.Deserialize<AccountBalanceInfoDto>(accountBalance.Description);
                    if (accountBalanceInfo != null)
                    {
                        var createdDate = accountBalance.CreatedDate.AddMonths(-1);
                        int monthIndex = GetMonthIndexFromDate(createdDate, monthNames);
                        turnOver[monthIndex] += accountBalance.InvoicedAmount;
                    }
                }
            }

            //Base Fees
            foreach (var stat in stats)
            {
                foreach (var accountBalance in stat.AccountBalances.Where(a => a.Name == "Base fee" && a.FkPartnerId == partnerId))
                {
                    var accountBalanceInfo =
                        JsonSerializer.Deserialize<AccountBalanceInfoDto>(accountBalance.Description);
                    if (accountBalanceInfo != null)
                    {
                        var createdDate = accountBalance.CreatedDate.AddMonths(-1);
                        int monthIndex = GetMonthIndexFromDate(createdDate, monthNames);
                        turnOver[monthIndex] += accountBalance.InvoicedAmount;
                    }
                }
            }

            //CPA
            var invoiceLines = invoiceDbContextTracking.OrderLines
                .Where(a => a.OrderDate >= from &&
                            ((a.Handled == null || a.Handled == false) && a.FkAccountBalanceId == null) &&
                            a.FkPartnerId == partnerId).ToList();
            foreach (var invoiceLine in invoiceLines)
            {
                var isKeepMarketingFeeOnReturnMerchant = IsKeepMarketingFeeOnReturnMerchant(invoiceLine.FkMerchantId, merchants);
                var createdDate = DateTimeExtensions.ConvertToCopenhagenTime(invoiceLine.OrderDate);
                int monthIndex = GetMonthIndexFromDate(createdDate, monthNames);
                if (monthIndex != -1)
                {
                    if (invoiceLine.TotalCut < 0 && isKeepMarketingFeeOnReturnMerchant)
                    {
                        var refunds = invoiceLine.TotalCut;
                        returns[monthIndex] += refunds;
                    }
                    else
                    {
                        turnOver[monthIndex] += invoiceLine.TotalCut;
                    }
                }
            }

            invoiceOverviewStatsDto.Returns = returns;
            invoiceOverviewStatsDto.MonthNames = monthNames;
            invoiceOverviewStatsDto.TurnOver = turnOver;
        }

        return invoiceOverviewStatsDto;
    }

    private async Task<decimal> CalculateCpaRevenueAsync(DateTime from, DateTime to)
    {
        var calculationEndDate = to > DateTime.UtcNow ? DateTime.UtcNow : to;
        var partnerId = partnerContext.PartnerId;
        var invoiceLines = await invoiceDbContextTracking.OrderLines
            .Where(a => a.OrderDate >= from && a.OrderDate <= calculationEndDate && a.FkPartnerId == partnerId)
            .ToListAsync();
        return invoiceLines.Sum(invoiceLine => invoiceLine.TotalCut);
    }

    private async Task<decimal> CalculateCpmRevenueAsync(DateTime from, DateTime to)
    {
        var calculationEndDate = to > DateTime.UtcNow ? DateTime.UtcNow : to;
        var partnerId = partnerContext.PartnerId;
        var currentMonth = DateTime.UtcNow.ToString("MMMM", CultureInfo.CreateSpecificCulture("en-US"));
        var cpmMerchants = await merchantService.GetAllActiveCpmMerchants();
        var monthsInPeriod = GetMonthNamesBetweenDates(from, calculationEndDate);
        var relevantMonths = new List<(string MonthName, DateTime FirstDay, DateTime LastDay, int DaysInMonth)>();
        var accountBalanceMonths = new List<DateTime>();
        foreach (var monthName in monthsInPeriod)
        {
            var monthDate = DateTime.ParseExact(monthName, "MMMM", CultureInfo.InvariantCulture);
            var firstDayOfMonth = new DateTime(monthDate.Year, monthDate.Month, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);
            var daysInMonth = DateTime.DaysInMonth(monthDate.Year, monthDate.Month);
            relevantMonths.Add((monthName, firstDayOfMonth, lastDayOfMonth, daysInMonth));
            if (monthName != currentMonth)
            {
                accountBalanceMonths.Add(monthDate.AddMonths(1));
            }
        }
        var allCpmAccountBalances = new List<AccountBalance>();
        if (accountBalanceMonths.Any())
        {
            allCpmAccountBalances = await invoiceDbContextTracking.AccountBalances
                .Where(a => a.Name == "CPM" && 
                            a.FkPartnerId == partnerId && 
                            accountBalanceMonths.Any(m => a.CreatedDate.Month == m.Month && a.CreatedDate.Year == m.Year))
                .ToListAsync();
        }
        decimal totalCpmRevenue = 0;
        foreach (var (monthName, firstDayOfMonth, lastDayOfMonth, daysInMonth) in relevantMonths)
        {
            var periodStart = firstDayOfMonth < from ? from : firstDayOfMonth;
            var periodEnd = lastDayOfMonth > calculationEndDate ? calculationEndDate : lastDayOfMonth;
            if (periodStart > periodEnd) continue;
            int daysInPeriod = (int)(periodEnd - periodStart).TotalDays + 1;
            decimal periodRatio = (decimal)daysInPeriod / daysInMonth;
            decimal monthCpmRevenue = 0;
            if (monthName != currentMonth)
            {
                var monthDate = DateTime.ParseExact(monthName, "MMMM", CultureInfo.InvariantCulture);
                var invoiceMonth = monthDate.AddMonths(1);
                var cpmEntries = allCpmAccountBalances
                    .Where(a => a.CreatedDate.Month == invoiceMonth.Month && a.CreatedDate.Year == invoiceMonth.Year)
                    .ToList();
                if (cpmEntries.Any())
                {
                    monthCpmRevenue = cpmEntries.Sum(a => a.InvoicedAmount);
                    monthCpmRevenue *= periodRatio;
                }
            }
            else
            {
                foreach (var merchant in cpmMerchants)
                {
                    if (!merchant.MerchantMeta.Any(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPM))
                        continue;
                    var cpm = decimal.Parse(merchant.MerchantMeta.First(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPM).Value);
                    if (!merchant.MerchantMeta.Any(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPMBudget))
                        continue;
                    var cpmBudget = decimal.Parse(merchant.MerchantMeta.First(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPMBudget).Value);
                    var budgetForPeriod = cpmBudget * periodRatio;
                    var opens = await elasticCampaignMailOpenService.Opens(periodStart, periodEnd, merchant.Id);
                    var merchantCpmRevenue = cpm * opens / 1000;
                    if (cpmBudget > 0 && merchantCpmRevenue > budgetForPeriod)
                    {
                        merchantCpmRevenue = budgetForPeriod;
                    }
                    monthCpmRevenue += merchantCpmRevenue;
                }
            }
            totalCpmRevenue += monthCpmRevenue;
        }
        return totalCpmRevenue;
    }

    private async Task<decimal> CalculateBaseFeeRevenueAsync(DateTime from, DateTime to)
    {
        var calculationEndDate = to > DateTime.UtcNow ? DateTime.UtcNow : to;
        var partnerId = partnerContext.PartnerId;
        var currentMonth = DateTime.UtcNow.ToString("MMMM", CultureInfo.CreateSpecificCulture("en-US"));
        var baseFeeMerchants = await merchantService.GetAllActiveBaseFeeMerchants();
        var monthsInPeriod = GetMonthNamesBetweenDates(from, calculationEndDate);
        var relevantMonths = new List<(string MonthName, DateTime FirstDay, DateTime LastDay, int DaysInMonth)>();
        var accountBalanceMonths = new List<DateTime>();
        foreach (var monthName in monthsInPeriod)
        {
            var monthDate = DateTime.ParseExact(monthName, "MMMM", CultureInfo.InvariantCulture);
            var firstDayOfMonth = new DateTime(monthDate.Year, monthDate.Month, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);
            var daysInMonth = DateTime.DaysInMonth(monthDate.Year, monthDate.Month);
            relevantMonths.Add((monthName, firstDayOfMonth, lastDayOfMonth, daysInMonth));
            if (monthName != currentMonth)
            {
                accountBalanceMonths.Add(monthDate.AddMonths(1));
            }
        }
        var allBaseFeeAccountBalances = new List<AccountBalance>();
        if (accountBalanceMonths.Any())
        {
            allBaseFeeAccountBalances = await invoiceDbContextTracking.AccountBalances
                .Where(a => a.Name == "Base fee" && 
                            a.FkPartnerId == partnerId && 
                            accountBalanceMonths.Any(m => a.CreatedDate.Month == m.Month && a.CreatedDate.Year == m.Year))
                .ToListAsync();
        }
        decimal totalBaseFeeRevenue = 0;
        foreach (var (monthName, firstDayOfMonth, lastDayOfMonth, daysInMonth) in relevantMonths)
        {
            var periodStart = firstDayOfMonth < from ? from : firstDayOfMonth;
            var periodEnd = lastDayOfMonth > calculationEndDate ? calculationEndDate : lastDayOfMonth;
            if (periodStart > periodEnd) continue;
            int daysInPeriod = (int)(periodEnd - periodStart).TotalDays + 1;
            decimal periodRatio = (decimal)daysInPeriod / daysInMonth;
            decimal monthBaseFeeRevenue = 0;
            if (monthName != currentMonth)
            {
                var monthDate = DateTime.ParseExact(monthName, "MMMM", CultureInfo.InvariantCulture);
                var invoiceMonth = monthDate.AddMonths(1);
                var baseFeeEntries = allBaseFeeAccountBalances
                    .Where(a => a.CreatedDate.Month == invoiceMonth.Month && a.CreatedDate.Year == invoiceMonth.Year)
                    .ToList();
                if (baseFeeEntries.Any())
                {
                    monthBaseFeeRevenue = baseFeeEntries.Sum(a => a.InvoicedAmount);
                    monthBaseFeeRevenue *= periodRatio;
                }
            }
            else
            {
                foreach (var merchant in baseFeeMerchants)
                {
                    if (merchant.MerchantMeta.All(a => a.FkMerchantMetaTypeName != MerchantMetaTypeNames.BaseFee))
                        continue;
                    
                    var baseFeeValue = merchant.MerchantMeta.First(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.BaseFee).Value;
                    if (string.IsNullOrWhiteSpace(baseFeeValue) || !decimal.TryParse(baseFeeValue, out var baseFee))
                        continue;
                        
                    var baseFeeForPeriod = (baseFee / daysInMonth) * daysInPeriod;
                    monthBaseFeeRevenue += baseFeeForPeriod;
                }
            }
            totalBaseFeeRevenue += monthBaseFeeRevenue;
        }
        return totalBaseFeeRevenue;
    }

    // Returns the total revenue for the period and the specified parts of the revenue
    public async Task<(decimal totalRevenue, decimal cpaRevenue, decimal cpmRevenue, decimal baseFeeRevenue)> CalculateRevenueByPeriodAsync(DateTime from, DateTime to)
    {
        var cpaRevenue = await CalculateCpaRevenueAsync(from, to);
        var cpmRevenue = await CalculateCpmRevenueAsync(from, to);
        var baseFeeRevenue = await CalculateBaseFeeRevenueAsync(from, to);
        var totalRevenue = cpaRevenue + cpmRevenue + baseFeeRevenue;
        return (totalRevenue, cpaRevenue, cpmRevenue, baseFeeRevenue);
    }

    public async Task<List<decimal>> CalculateRevenuePerDayByPeriodAsync(DateTime from, DateTime to)
    {
        // Ensure we don't calculate beyond today
        var calculationEndDate = to > DateTime.UtcNow ? DateTime.UtcNow : to;
        int days = (int)(calculationEndDate - from).TotalDays + 1;
        var dailyRevenue = new List<decimal>(new decimal[days]);

        // --- CPA Daily ---
        var partnerId = partnerContext.PartnerId;
        var invoiceLines = await invoiceDbContextTracking.OrderLines
            .Where(a => a.OrderDate >= from && a.OrderDate <= calculationEndDate && a.FkPartnerId == partnerId)
            .ToListAsync();
        foreach (var invoiceLine in invoiceLines)
        {
            var dayIndex = (int)(invoiceLine.OrderDate.Date - from.Date).TotalDays;
            if (dayIndex >= 0 && dayIndex < dailyRevenue.Count)
            {
                dailyRevenue[dayIndex] += invoiceLine.TotalCut;
            }
        }

        // --- CPM Daily ---
        var currentMonth = DateTime.UtcNow.ToString("MMMM", CultureInfo.CreateSpecificCulture("en-US"));
        var cpmMerchants = await merchantService.GetAllActiveCpmMerchants();
        var monthsInPeriod = GetMonthNamesBetweenDates(from, calculationEndDate);
        var relevantMonths = new List<(string MonthName, DateTime FirstDay, DateTime LastDay, int DaysInMonth)>();
        var accountBalanceMonths = new List<DateTime>();
        foreach (var monthName in monthsInPeriod)
        {
            var monthDate = DateTime.ParseExact(monthName, "MMMM", CultureInfo.InvariantCulture);
            var firstDayOfMonth = new DateTime(monthDate.Year, monthDate.Month, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);
            var daysInMonth = DateTime.DaysInMonth(monthDate.Year, monthDate.Month);
            relevantMonths.Add((monthName, firstDayOfMonth, lastDayOfMonth, daysInMonth));
            if (monthName != currentMonth)
            {
                accountBalanceMonths.Add(monthDate.AddMonths(1));
            }
        }
        var allCpmAccountBalances = new List<AccountBalance>();
        if (accountBalanceMonths.Any())
        {
            allCpmAccountBalances = await invoiceDbContextTracking.AccountBalances
                .Where(a => a.Name == "CPM" && 
                            a.FkPartnerId == partnerId && 
                            accountBalanceMonths.Any(m => a.CreatedDate.Month == m.Month && a.CreatedDate.Year == m.Year))
                .ToListAsync();
        }
        foreach (var (monthName, firstDayOfMonth, lastDayOfMonth, daysInMonth) in relevantMonths)
        {
            var periodStart = firstDayOfMonth < from ? from : firstDayOfMonth;
            var periodEnd = lastDayOfMonth > calculationEndDate ? calculationEndDate : lastDayOfMonth;
            if (periodStart > periodEnd) continue;
            int daysInPeriod = (int)(periodEnd - periodStart).TotalDays + 1;
            decimal periodRatio = (decimal)daysInPeriod / daysInMonth;
            if (monthName != currentMonth)
            {
                var monthDate = DateTime.ParseExact(monthName, "MMMM", CultureInfo.InvariantCulture);
                var invoiceMonth = monthDate.AddMonths(1);
                var cpmEntries = allCpmAccountBalances
                    .Where(a => a.CreatedDate.Month == invoiceMonth.Month && a.CreatedDate.Year == invoiceMonth.Year)
                    .ToList();
                if (cpmEntries.Any())
                {
                    var monthCpmRevenue = cpmEntries.Sum(a => a.InvoicedAmount) * periodRatio;
                    var dailyCpm = monthCpmRevenue / daysInPeriod;
                    for (var date = periodStart; date <= periodEnd; date = date.AddDays(1))
                    {
                        var dayIndex = (int)(date.Date - from.Date).TotalDays;
                        if (dayIndex >= 0 && dayIndex < dailyRevenue.Count)
                        {
                            dailyRevenue[dayIndex] += dailyCpm;
                        }
                    }
                }
            }
            else
            {
                foreach (var merchant in cpmMerchants)
                {
                    if (!merchant.MerchantMeta.Any(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPM))
                        continue;
                    var cpm = decimal.Parse(merchant.MerchantMeta.First(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPM).Value);
                    if (!merchant.MerchantMeta.Any(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPMBudget))
                        continue;
                    var cpmBudget = decimal.Parse(merchant.MerchantMeta.First(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPMBudget).Value);
                    var budgetForPeriod = cpmBudget * periodRatio;
                    var opens = await elasticCampaignMailOpenService.Opens(periodStart, periodEnd, merchant.Id);
                    var merchantCpmRevenue = cpm * opens / 1000;
                    if (cpmBudget > 0 && merchantCpmRevenue > budgetForPeriod)
                    {
                        merchantCpmRevenue = budgetForPeriod;
                    }
                    var dailyCpmRevenue = merchantCpmRevenue / daysInPeriod;
                    for (var date = periodStart; date <= periodEnd; date = date.AddDays(1))
                    {
                        var dayIndex = (int)(date.Date - from.Date).TotalDays;
                        if (dayIndex >= 0 && dayIndex < dailyRevenue.Count)
                        {
                            dailyRevenue[dayIndex] += dailyCpmRevenue;
                        }
                    }
                }
            }
        }

        // --- Base Fee Daily ---
        var baseFeeMerchants = await merchantService.GetAllActiveBaseFeeMerchants();
        monthsInPeriod = GetMonthNamesBetweenDates(from, calculationEndDate);
        relevantMonths = new List<(string MonthName, DateTime FirstDay, DateTime LastDay, int DaysInMonth)>();
        accountBalanceMonths = new List<DateTime>();
        foreach (var monthName in monthsInPeriod)
        {
            var monthDate = DateTime.ParseExact(monthName, "MMMM", CultureInfo.InvariantCulture);
            var firstDayOfMonth = new DateTime(monthDate.Year, monthDate.Month, 1);
            var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);
            var daysInMonth = DateTime.DaysInMonth(monthDate.Year, monthDate.Month);
            relevantMonths.Add((monthName, firstDayOfMonth, lastDayOfMonth, daysInMonth));
            if (monthName != currentMonth)
            {
                accountBalanceMonths.Add(monthDate.AddMonths(1));
            }
        }
        var allBaseFeeAccountBalances = new List<AccountBalance>();
        if (accountBalanceMonths.Any())
        {
            allBaseFeeAccountBalances = await invoiceDbContextTracking.AccountBalances
                .Where(a => a.Name == "Base fee" && 
                            a.FkPartnerId == partnerId && 
                            accountBalanceMonths.Any(m => a.CreatedDate.Month == m.Month && a.CreatedDate.Year == m.Year))
                .ToListAsync();
        }
        foreach (var (monthName, firstDayOfMonth, lastDayOfMonth, daysInMonth) in relevantMonths)
        {
            var periodStart = firstDayOfMonth < from ? from : firstDayOfMonth;
            var periodEnd = lastDayOfMonth > calculationEndDate ? calculationEndDate : lastDayOfMonth;
            if (periodStart > periodEnd) continue;
            int daysInPeriod = (int)(periodEnd - periodStart).TotalDays + 1;
            decimal periodRatio = (decimal)daysInPeriod / daysInMonth;
            if (monthName != currentMonth)
            {
                var monthDate = DateTime.ParseExact(monthName, "MMMM", CultureInfo.InvariantCulture);
                var invoiceMonth = monthDate.AddMonths(1);
                var baseFeeEntries = allBaseFeeAccountBalances
                    .Where(a => a.CreatedDate.Month == invoiceMonth.Month && a.CreatedDate.Year == invoiceMonth.Year)
                    .ToList();
                if (baseFeeEntries.Any())
                {
                    var monthBaseFeeRevenue = baseFeeEntries.Sum(a => a.InvoicedAmount) * periodRatio;
                    var dailyBaseFee = monthBaseFeeRevenue / daysInPeriod;
                    for (var date = periodStart; date <= periodEnd; date = date.AddDays(1))
                    {
                        var dayIndex = (int)(date.Date - from.Date).TotalDays;
                        if (dayIndex >= 0 && dayIndex < dailyRevenue.Count)
                        {
                            dailyRevenue[dayIndex] += dailyBaseFee;
                        }
                    }
                }
            }
            else
            {
                foreach (var merchant in baseFeeMerchants)
                {
                    if (merchant.MerchantMeta.All(a => a.FkMerchantMetaTypeName != MerchantMetaTypeNames.BaseFee))
                        continue;
                    
                    var baseFeeValue = merchant.MerchantMeta.First(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.BaseFee).Value;
                    if (string.IsNullOrWhiteSpace(baseFeeValue) || !decimal.TryParse(baseFeeValue, out var baseFee))
                        continue;
                        
                    var baseFeeForPeriod = (baseFee / daysInMonth) * daysInPeriod;
                    var dailyBaseFee = baseFeeForPeriod / daysInPeriod;
                    for (var date = periodStart; date <= periodEnd; date = date.AddDays(1))
                    {
                        var dayIndex = (int)(date.Date - from.Date).TotalDays;
                        if (dayIndex >= 0 && dayIndex < dailyRevenue.Count)
                        {
                            dailyRevenue[dayIndex] += dailyBaseFee;
                        }
                    }
                }
            }
        }

        return dailyRevenue;
    }

    private IEnumerable<string> GetDaysBetweenDates(DateTime from, DateTime to)
    {
        var days = new List<string>();
        for (var date = from; date <= to; date = date.AddDays(1))
        {
            days.Add(date.ToString("yyyy-MM-dd"));
        }

        return days;
    }

    private List<string> GetMonthNamesBetweenDates(DateTime start, DateTime end)
    {
        List<string> monthNames = new List<string>();

        DateTime current = new DateTime(start.Year, start.Month, 1);
        DateTime endMonth = new DateTime(end.Year, end.Month, 1);

        while (current <= endMonth)
        {
            string monthName = current.ToString("MMMM", CultureInfo.CreateSpecificCulture("en-US"));
            monthNames.Add(monthName);

            current = current.AddMonths(1);
        }

        return monthNames;
    }

    private int GetMonthIndexFromDate(DateTime date, List<string> monthNames)
    {
        // Get the month name from the created date
        string monthName = date.ToString("MMMM", CultureInfo.CreateSpecificCulture("en-US"));

        // Find the index of the month name in the list
        int monthIndex = monthNames.IndexOf(monthName);

        return monthIndex;
    }

    public async Task<List<AccountBalanceInvoiceDto>> GetReadyForInvoice()
    {
        var today = DateTime.UtcNow;
        var firstDayOfThisMonth = new DateTime(today.Year, today.Month, 1);
        var lastDayOfLastMonth = firstDayOfThisMonth.AddMilliseconds(-1);
        var accountBalanceInvoicesDb = await InvoiceData(lastDayOfLastMonth);
        var merchantsRaw = await merchantService.GetPayers();
        //var merchants = await merchantService.GetAllAsync(partnerId);
        var accountBalanceInvoices = new List<AccountBalanceInvoiceDto>();
        foreach (var accountBalanceInvoice in accountBalanceInvoicesDb)
        {
            var lastMonth = (await invoiceDbContextTracking.AccountBalanceInvoices.OrderByDescending(a => a.Id)
                .Where(b => b.FkPayerId == accountBalanceInvoice.FkPayerId).FirstOrDefaultAsync())?.Amount ?? 0;

            //var isKeepMarketingFeeOnReturnMerchant = IsKeepMarketingFeeOnReturnMerchant(accountBalanceInvoice.FkPayerId, merchants);
            
            accountBalanceInvoices.Add(new AccountBalanceInvoiceDto
            {
                Id = accountBalanceInvoice.Id,
                PayerType = merchantsRaw.First(a => a.Id == accountBalanceInvoice.FkPayerId).InvoiceType,
                FkPayerId = accountBalanceInvoice.FkPayerId,
                Amount = accountBalanceInvoice.Amount,
                LastMonth = lastMonth,
                CreatedDate = accountBalanceInvoice.CreatedDate,
                PayedAmount = accountBalanceInvoice.PayedAmount,
                AccountBalances = accountBalanceInvoice.AccountBalances
            });
        }

        return accountBalanceInvoices;
    }

    public async Task<FileDto> InvoiceAsync(int payerId)
    {
        var today = DateTime.UtcNow;
        var firstDayOfThisMonth = new DateTime(today.Year, today.Month, 1);
        var lastDayOfLastMonth = firstDayOfThisMonth.AddMilliseconds(-1);
        var accountBalanceInvoice = (await InvoiceData(lastDayOfLastMonth, payerId, true)).First();
        return await GetInvoiceAsync(accountBalanceInvoice.Id, null, true);
    }

    public async Task<FileDto> ExportInvoiceAsync(int payerId)
    {
        var today = DateTime.UtcNow;
        var firstDayOfThisMonth = new DateTime(today.Year, today.Month, 1);
        var lastDayOfLastMonth = firstDayOfThisMonth.AddMilliseconds(-1);
        var accountBalanceInvoice = (await InvoiceData(lastDayOfLastMonth, payerId)).First();
        return await GetInvoiceAsync(accountBalanceInvoice.Id, accountBalanceInvoice, false);
    }

    public async Task<FileDto> GetInvoiceAsync(int accountBalanceInvoiceId,
        AccountBalanceInvoice? accountBalanceInvoice, bool invoiceExport)
    {
        if (accountBalanceInvoice == null)
        {
            accountBalanceInvoice = await invoiceDbContextTracking.AccountBalanceInvoices
                .Include(a => a.AccountBalances)
                .SingleAsync(a => a.Id == accountBalanceInvoiceId);
        }

        var merchants = await merchantService.GetAllAsync();

        var workbook = new XLWorkbook();
        workbook.AddWorksheet($"{accountBalanceInvoice.CreatedDate.Month}-{accountBalanceInvoice.CreatedDate.Year}");
        var ws = workbook.Worksheet(
            $"{accountBalanceInvoice.CreatedDate.Month}-{accountBalanceInvoice.CreatedDate.Year}");
        ws.ColumnWidth = 20;
        ws.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;

        workbook.AddWorksheet(
            $"{accountBalanceInvoice.CreatedDate.Month}-{accountBalanceInvoice.CreatedDate.Year}-Month");
        var wsSplit =
            workbook.Worksheet(
                $"{accountBalanceInvoice.CreatedDate.Month}-{accountBalanceInvoice.CreatedDate.Year}-Month");
        wsSplit.ColumnWidth = 20;
        wsSplit.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Right;


        int count = 1;
        int countSplit = 1;
        var name = "Invoice";
        if (!invoiceExport)
        {
            name = "Invoice Preview";
            for (char col = 'A'; col <= 'H'; col++)
            {
                ws.Cell(col.ToString() + count).Value = name;
            }

            for (char col = 'A'; col <= 'I'; col++)
            {
                wsSplit.Cell(col.ToString() + count).Value = name;
            }

            count++;
            countSplit++;
        }

        //Total
        ws.Cell("A" + count).Value = "Merchant_Name";
        ws.Cell("B" + count).Value = "Product";
        ws.Cell("C" + count).Value = "Opens_#";
        ws.Cell("D" + count).Value = "Total_orders_#";
        ws.Cell("E" + count).Value = "Total_price_with_vat";
        ws.Cell("F" + count).Value = "Total_price_vat";
        ws.Cell("G" + count).Value = "Total_price_ex_vat";
        ws.Cell("H" + count).Value = "Invoice_amount";

        //Split
        wsSplit.Cell("A" + count).Value = "Merchant_Name";
        wsSplit.Cell("B" + count).Value = "Product";
        wsSplit.Cell("C" + count).Value = "Period";
        wsSplit.Cell("D" + count).Value = "Opens_#";
        wsSplit.Cell("E" + count).Value = "Total_orders_#";
        wsSplit.Cell("F" + count).Value = "Total_price_with_vat";
        wsSplit.Cell("G" + count).Value = "Total_price_vat";
        wsSplit.Cell("H" + count).Value = "Total_price_ex_vat";
        wsSplit.Cell("I" + count).Value = "Invoice_amount";

        var groupedBalances = accountBalanceInvoice.AccountBalances
            .GroupBy(ab => ab.FkMerchantId)
            .ToList();
        foreach (var groupedBalance in groupedBalances)
        {
            long opens = 0;
            var totalOrders = 0;
            decimal totalPriceWithVat = 0;
            decimal totalPriceVat = 0;
            decimal totalPriceExVat = 0;
            decimal invoicedAmount = 0;
            long totalExposures = 0;
            
            
            // TODO - This should be removed at some point
            // Check if merchant should keep marketing fee on returns
            var isKeepMarketingFeeOnReturnMerchant = IsKeepMarketingFeeOnReturnMerchant(groupedBalance.Key, merchants);

            if (isKeepMarketingFeeOnReturnMerchant)
            {
                foreach (var accountBalanceData in groupedBalance)
                {
                    var accountBalanceInfo =
                        JsonSerializer.Deserialize<AccountBalanceInfoDto>(accountBalanceData.Description);
                    if (accountBalanceInfo != null)
                    {
                        opens += accountBalanceInfo?.Opens ?? 0;
                        totalOrders += accountBalanceInfo?.TotalOrders ?? 0;
                        totalPriceVat += accountBalanceInfo?.TotalPriceWithVat ?? 0;
                        totalPriceVat += accountBalanceInfo?.TotalPriceVat ?? 0;
                        totalPriceExVat += accountBalanceInfo?.TotalPriceExVat ?? 0;
                        invoicedAmount += accountBalanceData?.InvoicedAmount ?? 0;
                        totalExposures += accountBalanceInfo?.TotalExposures ?? 0;
                    }
                }
            }
            else
            {
                foreach (var accountBalanceData in groupedBalance.Where(a => a.InvoicedAmount > 0))
                {
                    var accountBalanceInfo =
                        JsonSerializer.Deserialize<AccountBalanceInfoDto>(accountBalanceData.Description);
                    if (accountBalanceInfo != null)
                    {
                        opens += accountBalanceInfo?.Opens ?? 0;
                        totalOrders += accountBalanceInfo?.TotalOrders ?? 0;
                        totalPriceVat += accountBalanceInfo?.TotalPriceWithVat ?? 0;
                        totalPriceVat += accountBalanceInfo?.TotalPriceVat ?? 0;
                        totalPriceExVat += accountBalanceInfo?.TotalPriceExVat ?? 0;
                        invoicedAmount += accountBalanceData?.InvoicedAmount ?? 0;
                        totalExposures += accountBalanceInfo?.TotalExposures ?? 0;
                    }
                }
            }

            

            var accountBalance = groupedBalance.First();
            //Check if the current is from step3 and see if there is another it can use instead
            if (accountBalance.Name == Step3Name)
            {
                accountBalance = groupedBalance.Last();
            }

            var step3BaseFee = "";

            //Step 3 (base fee)
            if (accountBalance.Name == Step3Name)
            {
                var merchant = merchants.FirstOrDefault(a => a.Id == accountBalance.FkMerchantId);
                if (merchant != null)
                {
                    var model = merchant.MerchantMeta.First(a =>
                        a.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType);
                    step3BaseFee = model.Value.ToUpper();
                }
            }

            //Step 1
            if (accountBalance.Name == Step1Name || accountBalance.Name == Step1ReturnName || step3BaseFee == Step1Name)
            {
                count++;
                ws.Cell($"A{count}").Value = merchants.First(a => a.Id == accountBalance.FkMerchantId).DisplayName;
                ws.Cell($"B{count}").Value = Step1Name;
                ws.Cell($"C{count}").Value = opens;
                ws.Cell($"D{count}").Value = totalOrders;
                ws.Cell($"E{count}").Value = totalPriceWithVat;
                ws.Cell($"F{count}").Value = totalPriceVat;
                ws.Cell($"G{count}").Value = totalPriceExVat;
                ws.Cell($"H{count}").Value = invoicedAmount;
            }

            //Step 2
            if (accountBalance.Name == Step2Name || step3BaseFee == Step2Name)
            {
                count++;
                ws.Cell($"A{count}").Value =
                    merchants.FirstOrDefault(a => a.Id == accountBalance.FkMerchantId)?.DisplayName ??
                    accountBalance.FkMerchantId.ToString();
                ws.Cell($"B{count}").Value = accountBalance.Name;
                ws.Cell($"C{count}").Value = totalExposures;
                ws.Cell($"H{count}").Value = invoicedAmount;
            }
        }

        //Split
        foreach (var accountBalance in accountBalanceInvoice.AccountBalances)
        {
            var accountBalanceInfo = JsonSerializer.Deserialize<AccountBalanceInfoDto>(accountBalance.Description);
            var period = "";
            
            // TODO - This should be removed at some point
            // Check if merchant should keep marketing fee on returns
            var isKeepMarketingFeeOnReturnMerchant = IsKeepMarketingFeeOnReturnMerchant(accountBalance.FkMerchantId, merchants);

            if (accountBalanceInfo != null)
            {
                if (accountBalanceInfo.Period.HasValue)
                {
                    period = accountBalanceInfo.Period.Value.ToString("yyyy-MM");
                }

                //Step 1
                if (accountBalance.Name == Step1Name || (accountBalance.Name == Step1ReturnName && isKeepMarketingFeeOnReturnMerchant))
                {
                    countSplit++;
                    wsSplit.Cell($"A{countSplit}").Value =
                        merchants.First(a => a.Id == accountBalance.FkMerchantId).DisplayName;
                    wsSplit.Cell($"B{countSplit}").Value = accountBalance.Name;
                    wsSplit.Cell($"C{countSplit}").Value = period;
                    wsSplit.Cell($"D{countSplit}").Value = accountBalanceInfo.Opens;
                    wsSplit.Cell($"E{countSplit}").Value = accountBalanceInfo.TotalOrders;
                    wsSplit.Cell($"F{countSplit}").Value = accountBalanceInfo.TotalPriceWithVat;
                    wsSplit.Cell($"G{countSplit}").Value = accountBalanceInfo.TotalPriceVat;
                    wsSplit.Cell($"H{countSplit}").Value = accountBalanceInfo.TotalPriceExVat;
                    wsSplit.Cell($"I{countSplit}").Value = accountBalance.InvoicedAmount;
                }

                //Step 2
                if (accountBalance.Name == Step2Name)
                {
                    countSplit++;
                    wsSplit.Cell($"A{countSplit}").Value =
                        merchants.FirstOrDefault(a => a.Id == accountBalance.FkMerchantId)?.DisplayName ??
                        accountBalance.FkMerchantId.ToString();
                    wsSplit.Cell($"B{countSplit}").Value = accountBalance.Name;
                    wsSplit.Cell($"C{countSplit}").Value = period;
                    wsSplit.Cell($"D{countSplit}").Value = accountBalanceInfo.TotalExposures;
                    wsSplit.Cell($"E{countSplit}").Value = accountBalance.InvoicedAmount;
                }

                //Step 3 base fee
                if (accountBalance.Name == Step3Name)
                {
                    countSplit++;
                    wsSplit.Cell($"A{countSplit}").Value =
                        merchants.FirstOrDefault(a => a.Id == accountBalance.FkMerchantId)?.DisplayName ??
                        accountBalance.FkMerchantId.ToString();
                    wsSplit.Cell($"B{countSplit}").Value = accountBalance.Name;
                    wsSplit.Cell($"C{countSplit}").Value = period;
                    wsSplit.Cell($"E{countSplit}").Value = accountBalance.InvoicedAmount;
                }
            }
        }

        var path = $"Payment.xlsx";
        workbook.SaveAs(path);
        var bytes = await File.ReadAllBytesAsync(path);
        var file = Convert.ToBase64String(bytes);
        File.Delete(path);
        return new FileDto
        {
            Data = "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64," + file,
            Name = $"{name} ({accountBalanceInvoice.CreatedDate.Month}-{accountBalanceInvoice.CreatedDate.Year}).xlsx"
        };
    }

    private bool IsKeepMarketingFeeOnReturnMerchant(int merchantId, List<Merchant>? merchants)
    {
        var merchant = merchants.FirstOrDefault(a => a.Id == merchantId);
        if (merchant != null)
        {
            var model = merchant.MerchantMeta.FirstOrDefault(a =>
                a.FkMerchantMetaTypeName == MerchantMetaTypeNames.KeepMarketingFeeOnReturns);
            return model?.Value == "true";
        }

        return false;
    }

    public async Task<List<AccountBalanceInvoiceDto>> GetInvoicesAsync()
    {
        var accountBalanceInvoices = new List<AccountBalanceInvoiceDto>();
        var accountBalanceInvoicesDb = await invoiceDbContextTracking.AccountBalanceInvoices
            .Include(a => a.AccountBalances)
            .Where(a => a.FkPartnerId == partnerContext.PartnerId)
            .OrderByDescending(a => a.Id)
            .ToListAsync();
        var merchantsRaw = await merchantService.GetPayers();
        foreach (var accountBalanceInvoice in accountBalanceInvoicesDb)
        {
            accountBalanceInvoices.Add(new AccountBalanceInvoiceDto
            {
                Id = accountBalanceInvoice.Id,
                PayerType = merchantsRaw.First(a => a.Id == accountBalanceInvoice.FkPayerId).InvoiceType,
                FkPayerId = accountBalanceInvoice.FkPayerId,
                Amount = accountBalanceInvoice.Amount,
                CreatedDate = accountBalanceInvoice.CreatedDate,
                PayedAmount = accountBalanceInvoice.PayedAmount,
                AccountBalances = accountBalanceInvoice.AccountBalances
            });
        }

        return accountBalanceInvoices;
    }

    private async Task<List<AccountBalanceInvoice>> InvoiceData(DateTime invoiceOlder,
        int? payerId = null,
        bool invoice = false)
    {
        var partnerId = partnerContext.PartnerId;
        var now = DateTime.UtcNow;
        var currentMonth = DateTime.UtcNow;
        var firstDayOfMonth = new DateTime(invoiceOlder.Year, invoiceOlder.Month, 01);
        var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddMilliseconds(-1);

        var merchantsRaw = await merchantService.GetMerchantPayers();
        var merchants = new List<Merchant>();
        foreach (var merchant in merchantsRaw)
        {
            var merchantPayer = merchant.MerchantPayers.FirstOrDefault();
            if (payerId != null || invoice)
            {
                if (merchantPayer?.FkPayerId == payerId || (merchantPayer?.FkPayerId == null && payerId == 1))
                {
                    merchants.Add(merchant);
                }
            }
            else
            {
                merchants.Add(merchant);
            }
        }

        var accountBalances = await invoiceDbContextTracking.AccountBalances
            .Where(a => a.Active && a.FkAccountBallanceInvoiceId == null && a.FkPartnerId == partnerId).ToListAsync();
        var accountBalanceInvoices = new List<AccountBalanceInvoice>();

        //Merchants stats
        var firstDayOfLastMonth = new DateTime(now.Year, now.Month, 01).AddMonths(-1);
        var lastDayOfLastMonth = new DateTime(now.Year, now.Month, 01).AddMilliseconds(-1);
        var cacheKeyMerchant =
            $"GeneralService_GetMerchantsAsync_{firstDayOfLastMonth:yyyy-MM-dd}_{lastDayOfLastMonth:yyyy-MM-dd}_{configuration["Environment"]}_{partnerId}";
        var merchantPaginationDto = await cacheService.GetData<MerchantPaginationDto>(cacheKeyMerchant, true) ??
                                    new MerchantPaginationDto();
        var allOrderLines = await invoiceDbContextTracking.OrderLines
            .Where(a => ((a.Handled == false || a.Handled == null) && a.FkAccountBalanceId == null &&
                         a.OrderDate <= lastDayOfMonth) &&
                        a.AttributionType == MerchantAttributionTypes.CPA &&
                        a.FkPartnerId == partnerId)
            .ToListAsync();

        //Test variables
        /*merchants = merchants.Where(a => a.Id == 1623).ToList();
        firstDayOfMonth = new DateTime(2024, 05, 01);
        lastDayOfMonth = new DateTime(2024, 05, 31);
        currentMonth = new DateTime(2024, 06, 01);*/

        foreach (var merchant in merchants)
        {
            var accountBalanceInvoice = accountBalanceInvoices.FirstOrDefault(a =>
                a.FkPayerId == (merchant.MerchantPayers.FirstOrDefault()?.FkPayerId ?? 1));
            if (accountBalanceInvoice == null)
            {
                accountBalanceInvoice = new AccountBalanceInvoice
                {
                    AccountBalances = new List<AccountBalance>(),
                    FkPayerId = merchant.MerchantPayers.FirstOrDefault()?.FkPayerId ?? 1,
                    Amount = 0,
                    PayedAmount = 0,
                    CreatedDate = DateTime.UtcNow,
                    FkPartnerId = partnerId
                };
                accountBalanceInvoices.Add(accountBalanceInvoice);
            }

            //Step init
            foreach (var accountBalance in accountBalances.Where(a => a.FkMerchantId == merchant.Id))
            {
                accountBalanceInvoice.AccountBalances.Add(accountBalance);
            }

            var model = merchant.MerchantMeta
                .SingleOrDefault(meta => meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType)?.Value;

            var customerPeriods = merchant.MerchantMeta
                .Where(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer)
                .Where(a => (a.EndDate == null || a.EndDate > firstDayOfMonth) && a.StartDate <= lastDayOfMonth)
                .OrderBy(a => a.StartDate)
                .ToList();

            //Step 1 (Cpa)
            List<OrderLine> orderLines = [];
            var customerPeriodsCpa = merchant.MerchantMeta
                .Where(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer)
                .OrderBy(a => a.StartDate)
                .ToList();

            foreach (var customerPeriod in customerPeriodsCpa)
            {
                var from = customerPeriod.StartDate;
                var to = customerPeriod.EndDate;

                if (customerPeriod.StartDate > from)
                {
                    from = customerPeriod.StartDate.Value;
                }

                if (customerPeriod.EndDate != null)
                {
                    to = customerPeriod.EndDate.Value.AddDays(-1);
                    if (to > lastDayOfMonth)
                    {
                        to = lastDayOfMonth;
                    }
                }

                if (to == null)
                {
                    to = lastDayOfMonth;
                }

                to = DateTimeExtensions.SetTimeToMax(to.Value);
                orderLines.AddRange(allOrderLines.Where(a =>
                    a.OrderDate >= from && a.OrderDate < to && a.FkMerchantId == merchant.Id));
            }

            var uniqueOpens = merchantPaginationDto.Merchants.SingleOrDefault(a => a.MerchantId == merchant.Id)
                ?.EmailDisplaysUnique ?? 0;
            /*if (uniqueOpens == 0)
            {
                uniqueOpens = await _elasticCampaignMailOpenService.UniqueOpens(firstDayOfMonth, lastDayOfMonth, merchant.Id);
            }*/

            var groupedOrders = orderLines
                .Select(order => new
                {
                    Order = order,
                    OrderDate = DateTimeExtensions.ConvertToCopenhagenTime(order.OrderDate)
                })
                .GroupBy(a => new {Year = a.OrderDate.Year, Month = a.OrderDate.Month, TurnOver = a.Order.TotalCut > 0})
                .ToList();
            foreach (var groupedOrder in groupedOrders)
            {
                var firstOrder = groupedOrder.First();
                var period = new DateTime(firstOrder.OrderDate.Year, firstOrder.OrderDate.Month, 1);
                long opens = 0;
                if (firstDayOfLastMonth.Year == period.Year && firstDayOfLastMonth.Month == period.Month)
                {
                    opens = uniqueOpens;
                }

                if (groupedOrder.Any())
                {
                    var name = Step1Name;
                    if (!groupedOrder.Key.TurnOver)
                    {
                        name = Step1ReturnName;
                        // TODO - This should be removed at some point
                        // Check if merchant should keep marketing fee on returns
                        /*if(!IsKeepMarketingFeeOnReturnMerchant(merchant.Id, merchants))
                            continue;*/
                    }

                    var orderLinesFound = groupedOrder.Select(a => a.Order).ToList();
                    var accountBalance = new AccountBalance()
                    {
                        Name = name,
                        FkMerchantId = merchant.Id,
                        Active = true,
                        CreatedDate = DateTime.UtcNow,
                        LastModifiedDate = DateTime.UtcNow,
                        Description = JsonSerializer.Serialize(new AccountBalanceInfoDto
                        {
                            Opens = opens,
                            Period = period,
                            TotalOrders = groupedOrder.GroupBy(a => a.Order.OrderId).Count(),
                            TotalPriceWithVat = groupedOrder.Sum(a => a.Order.TotalPrice + a.Order.TotalPriceTax),
                            TotalPriceVat = groupedOrder.Sum(a => a.Order.TotalPriceTax),
                            TotalPriceExVat = groupedOrder.Sum(a => a.Order.TotalPrice)
                        }),
                        InvoicedAmount = groupedOrder.Sum(a => a.Order.TotalCut),
                        OrderLines = orderLinesFound,
                        FkPartnerId = partnerId
                    };
                    accountBalanceInvoice.AccountBalances.Add(accountBalance);
                }
            }

            //Step 2 (CPM)
            if (model == MerchantAttributionTypes.CPM)
            {
                //Check if CPM already have been invoiced
                var checkInvoiced = invoiceDbContextTracking.AccountBalances.Where(a =>
                        a.FkMerchantId == merchant.Id && a.CreatedDate.Month == currentMonth.Month &&
                        a.CreatedDate.Year == currentMonth.Year && a.Name == Step2Name)
                    .OrderByDescending(a => a.CreatedDate).FirstOrDefault();
                if (checkInvoiced == null)
                {
                    var cpm = Convert.ToDecimal(merchant.MerchantMeta
                        .SingleOrDefault(meta => meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPM)?.Value);
                    long totalExposures = 0;
                    decimal actual = 0;

                    foreach (var customerPeriod in customerPeriods)
                    {
                        var from = firstDayOfMonth;
                        var to = lastDayOfMonth;
                        if (customerPeriod.StartDate > from)
                        {
                            from = customerPeriod.StartDate.Value;
                        }

                        if (customerPeriod.EndDate != null)
                        {
                            to = customerPeriod.EndDate.Value.AddDays(-1);
                            if (to > lastDayOfMonth)
                            {
                                to = lastDayOfMonth;
                            }
                        }

                        totalExposures += await elasticCampaignMailOpenService.Opens(from, to, merchant.Id);
                        totalExposures += await elasticDiscountOpenService.Opens(from, to, merchant.Id);
                        totalExposures += await elasticShopProductDisplaysService.Displays(from, to, merchant.Id);
                        actual += (decimal) totalExposures / 1000 * cpm;
                        var potential = Convert.ToDecimal(merchant.MerchantMeta
                            .SingleOrDefault(meta => meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPMBudget)
                            ?.Value);
                        if (actual > potential)
                        {
                            actual = potential;
                        }
                    }

                    if (totalExposures != 0)
                    {
                        var accountBalance = new AccountBalance()
                        {
                            Name = Step2Name,
                            FkMerchantId = merchant.Id,
                            Active = true,
                            CreatedDate = DateTime.UtcNow,
                            LastModifiedDate = DateTime.UtcNow,
                            Description = JsonSerializer.Serialize(new AccountBalanceInfoDto
                            {
                                TotalExposures = totalExposures
                            }),
                            InvoicedAmount = actual,
                            FkPartnerId = partnerId
                        };
                        accountBalanceInvoice.AccountBalances.Add(accountBalance);
                    }
                }
            }

            //STEP 3 (Base fee)
            var baseFeeValue = merchant.MerchantMeta
                .SingleOrDefault(meta => meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.BaseFee)
                ?.Value ?? "0";
            
            if (!string.IsNullOrWhiteSpace(baseFeeValue) && decimal.TryParse(baseFeeValue, out var baseFee) && baseFee != 0)
            {
                var checkInvoicedBaseFee = invoiceDbContextTracking.AccountBalances.Where(a =>
                        a.FkMerchantId == merchant.Id && a.CreatedDate.Month == currentMonth.Month &&
                        a.CreatedDate.Year == currentMonth.Year && a.Name == Step3Name)
                    .OrderByDescending(a => a.CreatedDate).FirstOrDefault();

                if (checkInvoicedBaseFee == null)
                {
                    int daysInMonth = DateTime.DaysInMonth(firstDayOfLastMonth.Year, firstDayOfLastMonth.Month);

                    foreach (var customerPeriod in customerPeriods)
                    {
                        var from = firstDayOfLastMonth;
                        var to = lastDayOfLastMonth;
                        if (customerPeriod.StartDate > from)
                        {
                            from = customerPeriod.StartDate.Value;
                        }

                        if (customerPeriod.EndDate != null)
                        {
                            to = customerPeriod.EndDate.Value.AddDays(-1);
                            if (to > lastDayOfMonth)
                            {
                                to = lastDayOfMonth;
                            }
                        }

                        var daysAsCustomer = (to - from).Days + 1;
                        if (daysAsCustomer > 0)
                        {
                            var fee = Math.Round(baseFee / daysInMonth * daysAsCustomer, 2);
                            var accountBalance = new AccountBalance()
                            {
                                Name = Step3Name,
                                FkMerchantId = merchant.Id,
                                Active = true,
                                CreatedDate = DateTime.UtcNow,
                                LastModifiedDate = DateTime.UtcNow,
                                Description = JsonSerializer.Serialize(new AccountBalanceInfoDto
                                {
                                }),
                                InvoicedAmount = fee,
                                FkPartnerId = partnerId
                            };
                            accountBalanceInvoice.AccountBalances.Add(accountBalance);
                        }
                    }
                }
            }
        }

        foreach (var accountBalanceInvoice in accountBalanceInvoices)
        {
            accountBalanceInvoice.Amount = accountBalanceInvoice.AccountBalances.Sum(a => a.InvoicedAmount);
            accountBalanceInvoice.AccountBalances = accountBalanceInvoice.AccountBalances
                .OrderByDescending(a => a.FkMerchantId).ToList();
        }

        if (invoice)
        {
            foreach (var accountBalanceInvoice in accountBalanceInvoices)
            {
                foreach (var accountBalance in accountBalanceInvoice.AccountBalances)
                {
                    if (accountBalance.FkPartnerId != 52876)
                        Console.WriteLine();
                }

                await invoiceDbContextTracking.AccountBalanceInvoices.AddAsync(accountBalanceInvoice);
            }

            await invoiceDbContextTracking.SaveChangesAsync();
        }

        /*if (invoice)
        {
            await _invoiceDbContext.AccountBalanceInvoices.AddRangeAsync(accountBalanceInvoices);
            await _invoiceDbContext.SaveChangesAsync();
            foreach (var accountBalanceInvoice in accountBalanceInvoices)
            {
                var merchantIds = accountBalanceInvoice.AccountBalances.Select(a => a.FkMerchantId).ToList();
                await _invoiceDbContext.OrderLines
                    .Where(a => (a.Handled == false || a.Handled == null) && a.OrderDate <= invoiceOlder &&
                                merchantIds.Contains(a.FkMerchantId))
                    .ExecuteUpdateAsync(a => a.SetProperty(n => n.Handled, n => true));
            }
        }*/

        return accountBalanceInvoices.Where(a => a.AccountBalances.Count != 0).ToList();
    }

    public async Task<List<OrderLine>> RemoveReturnInvoiceLines()
    {
        // Get All Merchants
        var merchants = await merchantService.GetAllAsync();

        // Get All Not Keep Marketing Fee on Return Merchants
        var notKeepMarketingFeeOnReturnMerchants = merchants.Where(a => !IsKeepMarketingFeeOnReturnMerchant(a.Id, merchants)).Select(a => a.Id).ToList();

        // Get all Invoice Lines that are partial or full returns and are NOT Keep Marketing Fee on Return Merchants and Created date is within this month
        var invoiceLines = await invoiceDbContextTracking.OrderLines
            .Where(a => a.ValyrionStatus != "paid" && notKeepMarketingFeeOnReturnMerchants.Contains(a.FkMerchantId) && a.FkAccountBalanceId == null && a.CreatedDate >= DateTime.UtcNow.AddDays(-30))
            .ToListAsync();

        // Delete the invoice lines
        invoiceDbContextTracking.OrderLines.RemoveRange(invoiceLines);
        await invoiceDbContextTracking.SaveChangesAsync();

        return invoiceLines;
    }


    public async Task<CurrentRevenueInfoDto> GetCurrentRevenue(DateTime from, DateTime to)
    {
        // Cache for 30 minutes
        var cacheKey = $"InvoiceService_GetCurrentRevenue_{from}_{to}_{partnerContext.PartnerId}";
        var currentRevenue = await cacheService.GetData<CurrentRevenueInfoDto>(cacheKey);
        if (currentRevenue != null)
        {
            return currentRevenue;
        }

        var (totalRevenue, cpaRevenue, cpmRevenue, baseFeeRevenue) = await CalculateRevenueByPeriodAsync(from, to);
        currentRevenue = new CurrentRevenueInfoDto
        {
            Revenue = totalRevenue,
            CPA = cpaRevenue,
            CPM = cpmRevenue,
            BaseFee = baseFeeRevenue
        };
        cacheService.SetData(cacheKey, currentRevenue, TimeSpan.FromMinutes(30));

        return currentRevenue;
    }

    public async Task<List<ActionTypeStatsDto>> GetActionTypeStatsByPartnerIdAsync(int partnerId, DateTime? from = null, DateTime? to = null)
    {
        var query = invoiceDbContextTracking.OrderLines
            .Where(ol => ol.FkPartnerId == partnerId);

        // Apply date filtering if provided
        if (from.HasValue)
        {
            query = query.Where(ol => ol.OrderDate >= from.Value);
        }
        
        if (to.HasValue)
        {
            // Set time to end of day for 'to' date to include entire day
            var toEndOfDay = to.Value.Date.AddDays(1).AddTicks(-1);
            query = query.Where(ol => ol.OrderDate <= toEndOfDay);
        }

        return await query
            .GroupBy(ol => ol.ActionType)
            .Select(g => new ActionTypeStatsDto
            {
                ActionType = g.Key,
                NumberOfRows = g.Count(),
                TotalPriceSum = g.Sum(ol => ol.TotalPrice),
                TotalPriceTaxSum = g.Sum(ol => ol.CorrectTax ?? ol.TotalPriceTax),
                TotalCutSum = g.Sum(ol => ol.TotalCut)
            })
            .ToListAsync();
    }
}