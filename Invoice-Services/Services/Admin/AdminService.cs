#region

using Admin_Services.Models.ModelsDal.Invoice;
using Invoice_Services.Models.ModelsDal.Invoice;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Shared.Dto.Analytics;
using Shared.Dto.Dashboard;
using Shared.Dto.Webshop.FlaggedOrders;
using Shared.Helpers.Converters;
using Shared.Services.Notification;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

#endregion

namespace Admin_Services.Services.Admin;

public class AdminService(
    InvoiceDbContextTracking dbContext,
    IEmailService emailService,
    IMerchantService merchantService)
    : IAdminService
{
    public async Task<List<OrderLine>> InvoiceLinesAsync(int campaignId)
    {
        return await dbContext.OrderLines
            .Where(a => a.ExposureTriggerId == campaignId.ToString() && a.ExposureTrigger == "campaign").ToListAsync();
    }
    
    public async Task<List<OrderLine>> InvoiceLinesByMerchantIdsAsync(DateTime oldestDayAllowed, List<int> merchantIds)
    {
        return await dbContext.OrderLines
            .Where(a => a.OrderDate >= oldestDayAllowed && merchantIds.Contains(a.FkMerchantId)).ToListAsync();
    }

    
    public async Task<List<OrderLine>> InvoiceLinesByPartnerIdAsync(DateTime oldestDayAllowed, int partnerId)
    {
        return await dbContext.OrderLines
            .Where(a => a.OrderDate >= oldestDayAllowed && a.FkPartnerId == partnerId).ToListAsync();
    }

    public async Task<List<OrderLine>> InvoiceLinesAsync(DateTime oldestDayAllowed, int merchantId)
    {
        return await dbContext.OrderLines
            .Where(a => a.OrderDate > oldestDayAllowed && a.FkMerchantId == merchantId).ToListAsync();
    }

    public async Task<List<OrderLine>> InvoiceLinesAsync(DateTime from, DateTime to, int merchantId)
    {
        return await dbContext.OrderLines
            .Where(a => a.OrderDate >= from && a.OrderDate <= to && a.FkMerchantId == merchantId).ToListAsync();
    }

    public async Task<List<OrderLine>> InvoiceLinesAsync(int merchantId, DateTime from, DateTime to)
    {
        to = DateTimeExtensions.SetTimeToMax(to);
        return await dbContext.OrderLines
            .Where(a => a.FkMerchantId == merchantId && a.OrderDate >= from && a.OrderDate <= to).ToListAsync();
    }

    public async Task<List<OrderLine>> InvoiceLinesAsync(int merchantId, string exposureTrigger, DateTime from,
        DateTime to, InvoiceDbContext? invoiceDbContext = null)
    {
        to = DateTimeExtensions.SetTimeToMax(to);
        if (invoiceDbContext == null)
        {
            invoiceDbContext = dbContext;
        }

        return await invoiceDbContext.OrderLines
            .Where(a => a.FkMerchantId == merchantId && a.OrderDate >= from && a.OrderDate <= to &&
                        a.ExposureTrigger == exposureTrigger).ToListAsync();
    }

    public async Task<Dictionary<DateTime, decimal>> InvoiceLinesDayCountAsync(int merchantId, string exposureTrigger,
        DateTime from, DateTime to, bool includeRefunds = false)
    {
        to = DateTimeExtensions.SetTimeToMax(to);
        return await dbContext.OrderLines
            .Where(a => a.FkMerchantId == merchantId && a.OrderDate >= from && a.OrderDate <= to &&
                        a.ExposureTrigger == exposureTrigger && (includeRefunds || !a.ValyrionStatus.ToLower().Contains("refund")))
            .GroupBy(a => a.OrderDate.Date)
            .Select(g => new
            {
                OrderDate = g.Key.Date,
                TotalSum = g.Sum(a => a.TotalPrice)
            })
            .ToDictionaryAsync(x => x.OrderDate, x => x.TotalSum);
    }

    public async Task<Dictionary<DateTime, decimal>> InvoiceLinesMonthCountAsync(int merchantId, string exposureTrigger,
        DateTime from, DateTime to, bool includeRefunds = false)
    {
        to = DateTimeExtensions.SetTimeToMax(to);
        return await dbContext.OrderLines
            .Where(a => a.FkMerchantId == merchantId && a.OrderDate >= from && a.OrderDate <= to &&
                        a.ExposureTrigger == exposureTrigger && (includeRefunds || !a.ValyrionStatus.ToLower().Contains("refund")))
            .GroupBy(a => new {a.OrderDate.Year, a.OrderDate.Month})
            .Select(g => new
            {
                YearMonth = new DateTime(g.Key.Year, g.Key.Month, 1),
                TotalSum = g.Sum(a => a.TotalPrice)
            })
            .ToDictionaryAsync(x => x.YearMonth, x => x.TotalSum);
    }

    public async Task<Dictionary<DateTime, decimal>> InvoiceLinesYearCountAsync(int merchantId, string exposureTrigger,
        DateTime from, DateTime to, bool includeRefunds = false)
    {
        to = DateTimeExtensions.SetTimeToMax(to);
        return await dbContext.OrderLines
            .Where(a => a.FkMerchantId == merchantId && a.OrderDate >= from && a.OrderDate <= to &&
                        a.ExposureTrigger == exposureTrigger && (includeRefunds || !a.ValyrionStatus.ToLower().Contains("refund")))
            .GroupBy(a => new {a.OrderDate.Year})
            .Select(g => new
            {
                YearMonth = new DateTime(g.Key.Year, 1, 1),
                TotalSum = g.Sum(a => a.TotalPrice)
            })
            .ToDictionaryAsync(x => x.YearMonth, x => x.TotalSum);
    }

    public async Task<List<OrderLine>> InvoiceLinesAsync(DateTime oldestDayAllowed, int merchantId, DateTime from,
        DateTime to, int day)
    {
        return await dbContext.OrderLines
            .Where(a => a.OrderDate > oldestDayAllowed && a.FkMerchantId == merchantId &&
                        a.OrderDate >= from &&
                        a.OrderDate <= to &&
                        a.OrderDate.Day <= day).ToListAsync();
    }

    public async Task<MonitorPerformanceDto> InvoiceLinesAsync(DateTime from, DateTime to, string exposureTrigger)
    {
        var lines = await dbContext.OrderLines.Where(a =>
            a.ExposureDate >= from && a.ExposureDate < to && a.ExposureTrigger != null &&
            a.ExposureTrigger == exposureTrigger).ToListAsync();
        var orders = lines.DistinctBy(m => new {m.OrderId, m.FkMerchantId}).ToList();

        var totalCut = lines.Sum(a => a.TotalCut);
        var averageOrderRevenue = orders.Count != 0 ? totalCut / orders.Count : 0;

        return new MonitorPerformanceDto
        {
            Revenue = totalCut,
            AverageOrderRevenue = averageOrderRevenue,
            Orders = orders.Count,
        };
    }

    public async Task<List<OrderLine>> PaginatedInvoiceLinesAsync(int merchantId, DateTime from, DateTime to, int page,
        int pageSize, string searchQuery = "")
    {
        var orderIds = new List<string>();

        // Step 1: Fetch paginated OrderIds
        if (!string.IsNullOrEmpty(searchQuery))
        {
            // Search for FirstName, LastName or Email with this Search String and it has to be extremely fast and include pagination like below 
            orderIds = await dbContext.OrderLines
                .Where(a => a.FkMerchantId == merchantId && a.OrderDate >= from && a.OrderDate <= to &&
                            ((a.CustomerName != null && a.CustomerName.Contains(searchQuery)) ||
                             a.Email.Contains(searchQuery)))
                .GroupBy(a => a.OrderId)
                .OrderByDescending(g => g.Max(a => a.OrderDate)) // Order by OrderDate descending
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(g => g.Key)
                .ToListAsync();
        }
        else
        {
            orderIds = await dbContext.OrderLines
                .Where(a => a.FkMerchantId == merchantId && a.OrderDate >= from && a.OrderDate <= to)
                .GroupBy(a => a.OrderId)
                .OrderByDescending(g => g.Max(a => a.OrderDate)) // Order by OrderDate descending
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(g => g.Key)
                .ToListAsync();
        }


        // Step 2: Fetch all OrderLines for the paginated OrderIds, ordered by OrderDate descending
        var orderLines = await dbContext.OrderLines
            .Where(a => orderIds.Contains(a.OrderId) && a.FkMerchantId == merchantId && a.OrderDate >= from &&
                        a.OrderDate <= to)
            .OrderBy(a => a.OrderLastModified) // Order the results by OrderDate descending
            .ToListAsync();

        return orderLines;
    }

    public async Task<decimal> InvoiceSumAsync(DateTime from, DateTime to, string exposureTrigger, string refId = "")
    {
        return await dbContext.OrderLines
            .Where(a => a.ExposureDate >= from && a.ExposureDate < to && a.ExposureTrigger != null &&
                        a.ExposureTrigger == exposureTrigger && (refId == "" || a.ExposureTriggerId == refId))
            .SumAsync(a => a.TotalCut);
    }
    
    public async Task<DashboardProfileBestMerchant> FetchBestMerchantAsync(List<int> merchantIds, DateTime? from = null, DateTime? to = null)
    {
        var bestMerchant = await dbContext.OrderLines
            .Where(a => merchantIds.Contains(a.FkMerchantId) && (from == null || a.OrderDate >= from) && (to == null || a.OrderDate <= to))
            .GroupBy(a => a.FkMerchantId)
            .Select(g => new DashboardProfileBestMerchant
            {
                MerchantId = g.Key,
                TotalCut = g.Sum(a => a.TotalCut),
                TotalRevenue = g.Sum(a => a.TotalPrice),
                TotalOrders = g.Select(a => a.OrderId).Distinct().Count()
                
            })
            .OrderByDescending(a => a.TotalCut)
            .FirstOrDefaultAsync();

        if(bestMerchant == null)
        {
            return new DashboardProfileBestMerchant();
        }
        
        bestMerchant.AverageOrderValue = bestMerchant.TotalRevenue / bestMerchant.TotalOrders;
        
        return bestMerchant;
    }

    public async Task FlaggedOrdersUpdateAsync()
    {
        var newer = DateTime.UtcNow.AddDays(-45);
        var gracePeriod = DateTime.UtcNow.AddHours(-24);
        var now = DateTime.UtcNow;
        var invoiceLineFlaggedOrdersDb = await dbContext.FlaggedOrders.ToListAsync();
        var emailHtmlBodyNew = "";
        var emailHtmlBodyUpdated = "";
        var webshops = await merchantService.GetAllAsync().ConfigureAwait(false);

        var invoiceLinesData = (await dbContext.OrderLines.ToListAsync())
            .Where(a => a.OrderDate > newer && a.OrderDate < gracePeriod)
            .OrderByDescending(a => a.OrderLastModified); // Sort by OrderDate
        var groupedInvoiceLines =
            new List<(string WebshopId, string Email, decimal totalPrice, List<OrderLine> Lines)>();
        const int intervalMinutes = 11;

        foreach (var line in invoiceLinesData)
        {
            bool isGrouped = false;
            foreach (var group in groupedInvoiceLines)
            {
                if (group.WebshopId == line.FkMerchantId.ToString() && group.Email == line.Email &&
                    group.totalPrice == line.TotalPrice)
                {
                    // If the current line's WebshopId, Email and TotalPrice match the group, check the OrderDate
                    if (group.Lines.Any(g => Math.Abs((g.OrderDate - line.OrderDate).TotalMinutes) <= intervalMinutes))
                    {
                        group.Lines.Add(line);
                        isGrouped = true;
                        break;
                    }
                }
            }

            if (!isGrouped)
            {
                // If the line doesn't fit into any existing group, start a new group
                groupedInvoiceLines.Add((line.FkMerchantId.ToString(), line.Email, line.TotalPrice,
                    new List<OrderLine> {line}));
            }
        }

        foreach (var invoiceLines in groupedInvoiceLines.Where(a => a.Lines.Count > 1))
        {
            var orders = new Dictionary<string, bool>();
            var invoiceLineFlaggedOrders = invoiceLineFlaggedOrdersDb.FirstOrDefault(a =>
                a.OrderIds.Contains(invoiceLines.Lines.First().OrderId) &&
                a.FkMerchantId.ToString() == invoiceLines.WebshopId);

            if (invoiceLineFlaggedOrders == null)
            {
                invoiceLineFlaggedOrders = new FlaggedOrder
                {
                    Id = 0,
                    Handled = false,
                    FkMerchantId = Convert.ToInt32(invoiceLines.WebshopId),
                    OrderIds = "",
                    CreatedDate = now,
                    LastModifiedDate = now
                };
            }

            foreach (var invoiceLine in invoiceLines.Lines)
            {
                if (!orders.ContainsKey(invoiceLine.OrderId))
                {
                    orders.Add(invoiceLine.OrderId, invoiceLine.Refund);
                    if (invoiceLineFlaggedOrders.Id == 0)
                    {
                        if (invoiceLineFlaggedOrders.OrderIds == "")
                        {
                            invoiceLineFlaggedOrders.OrderIds += $"{invoiceLine.OrderId}";
                        }
                        else
                        {
                            invoiceLineFlaggedOrders.OrderIds += $",{invoiceLine.OrderId}";
                        }
                    }
                }
            }

            //Check for any new duplicates
            if (orders.Count > 1)
            {
                if (invoiceLineFlaggedOrders.Id == 0)
                {
                    if (orders.Where(a => a.Value == false).Select(a => a.Value).Count() > 1)
                    {
                        await dbContext.AddAsync(invoiceLineFlaggedOrders);
                        emailHtmlBodyNew +=
                            $"New flagged orders have been added by merchant  {webshops.Single(a => a.Id == Convert.ToInt32(invoiceLines.WebshopId)).DisplayName} ({String.Join(",", orders.Select(a => a.Key))})<br>";
                    }
                }
            }

            //Check if any old duplicates have been fixed
            if (invoiceLineFlaggedOrders.Id != 0)
            {
                if (orders.Where(a => a.Value == false).Select(a => a.Value).Count() <= 1)
                {
                    if (!invoiceLineFlaggedOrders.Handled)
                    {
                        emailHtmlBodyUpdated +=
                            $"Flagged orders have been handled by merchant {webshops.Single(a => a.Id == Convert.ToInt32(invoiceLines.WebshopId)).DisplayName} ({String.Join(",", orders.Select(a => a.Key))})<br>";
                        invoiceLineFlaggedOrders.Handled = true;
                    }
                }
            }
        }

        var emails = new List<string> {"<EMAIL>"};
        if (emailHtmlBodyNew != "")
        {
            await emailService.SendEmailAsync(emails, "New Flagged orders", emailHtmlBodyNew);
        }

        if (emailHtmlBodyUpdated != "")
        {
            await emailService.SendEmailAsync(emails, "Updated Flagged orders", emailHtmlBodyUpdated);
        }

        await dbContext.SaveChangesAsync();
    }

    public async Task<List<InvoiceLineFlaggedOrderHeadDto>> GetFlaggedOrdersAsync()
    {
        var newer = DateTime.UtcNow.AddDays(-45);
        var webshops = await merchantService.GetAllAsync().ConfigureAwait(false);
        var invoiceLineFlaggedOrderDtos = new List<InvoiceLineFlaggedOrderHeadDto>();
        var invoiceLineFlaggedOrders =
            await dbContext.FlaggedOrders.Where(a => !a.Handled).ToListAsync();
        var invoiceLinesAll = await dbContext.OrderLines.Where(a => a.OrderDate > newer).ToListAsync();
        foreach (var invoiceLineFlaggedOrder in invoiceLineFlaggedOrders)
        {
            var orderIds = invoiceLineFlaggedOrder.OrderIds.Split(",");
            InvoiceLineFlaggedOrderHeadDto? invoiceLineFlaggedOrderHead = null;
            foreach (var orderId in orderIds)
            {
                decimal totalPrice = 0;
                var invoiceLines = invoiceLinesAll
                    .Where(a => a.OrderId == orderId && a.FkMerchantId == invoiceLineFlaggedOrder.FkMerchantId)
                    .ToList();
                foreach (var invoiceLine in invoiceLines)
                {
                    totalPrice += invoiceLine.TotalPrice;
                }

                if (invoiceLines.Any())
                {
                    if (invoiceLineFlaggedOrderHead == null)
                    {
                        invoiceLineFlaggedOrderHead = new InvoiceLineFlaggedOrderHeadDto
                        {
                            InvoiceLineFlaggedOrderId = invoiceLineFlaggedOrder.Id,
                            MerchantId = invoiceLineFlaggedOrder.FkMerchantId.ToString(),
                            MerchantName = webshops.SingleOrDefault(a =>
                                                   a.Id == Convert.ToInt32(invoiceLineFlaggedOrder.FkMerchantId))
                                               ?.DisplayName ??
                                           webshops.SingleOrDefault(a =>
                                                   a.Id == Convert.ToInt32(invoiceLineFlaggedOrder.FkMerchantId))
                                               ?.Name ?? "No Name",
                            BuyerEmail = invoiceLines.First().Email,
                            OrderDate = invoiceLines.First().OrderDate,
                            InvoiceLineFlaggedOrders = new List<InvoiceLineFlaggedOrderDto>()
                        };
                    }

                    invoiceLineFlaggedOrderHead.InvoiceLineFlaggedOrders.Add(new InvoiceLineFlaggedOrderDto
                    {
                        BuyerEmail = invoiceLines.First().Email,
                        BuyerName = invoiceLines.First().CustomerName ?? "Unknown",
                        OrderId = invoiceLines.First().OrderId,
                        MerchantStatus = invoiceLines.First().Status,
                        TotalPrice = totalPrice
                    });
                }
            }

            if (invoiceLineFlaggedOrderHead != null)
            {
                invoiceLineFlaggedOrderDtos.Add(invoiceLineFlaggedOrderHead);
            }
        }

        return invoiceLineFlaggedOrderDtos.OrderByDescending(a => a.OrderDate).ToList();
    }

    public async Task UpdateFlaggedOrdersAsync(int id)
    {
        var invoiceLineFlaggedOrder = await dbContext.FlaggedOrders.SingleAsync(a =>
            a.Id == id);
        invoiceLineFlaggedOrder.Handled = true;
        await dbContext.SaveChangesAsync();
    }

    public async Task UpdateLinesAsync(List<OrderLine> orderLines)
    {
        dbContext.OrderLines.UpdateRange(orderLines);
        await dbContext.SaveChangesAsync();
    }
}