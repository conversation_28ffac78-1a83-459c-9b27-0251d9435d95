using Admin_Services.Models.ModelsDal.Invoice;
using Invoice_Services.Models.ModelsDal.Invoice;
using Shared.Dto.Analytics;
using Shared.Dto.Dashboard;
using Shared.Dto.Webshop.FlaggedOrders;

namespace Admin_Services.Services.Admin;

public interface IAdminService
{
    Task<List<OrderLine>> InvoiceLinesAsync(int campaignId);
    Task<List<OrderLine>> InvoiceLinesByMerchantIdsAsync(DateTime oldestDayAllowed, List<int> merchantIds);
    Task<List<OrderLine>> InvoiceLinesByPartnerIdAsync(DateTime oldestDayAllowed,int partnerId);
    Task<List<OrderLine>> InvoiceLinesAsync(DateTime oldestDayAllowed, int merchantId);
    Task<List<OrderLine>> InvoiceLinesAsync(DateTime from, DateTime to, int merchantId);
    Task<List<OrderLine>> InvoiceLinesAsync(int merchantId, DateTime from, DateTime to);

    Task<List<OrderLine>> InvoiceLinesAsync(int merchantId, string exposureTrigger, DateTime from, DateTime to,
        InvoiceDbContext? invoiceDbContext = null);

    Task<Dictionary<DateTime, decimal>> InvoiceLinesDayCountAsync(int merchantId, string exposureTrigger, DateTime from,
        DateTime to, bool includeRefunds = false);

    Task<Dictionary<DateTime, decimal>> InvoiceLinesMonthCountAsync(int merchantId, string exposureTrigger,
        DateTime from, DateTime to, bool includeRefunds = false);

    Task<Dictionary<DateTime, decimal>> InvoiceLinesYearCountAsync(int merchantId, string exposureTrigger,
        DateTime from, DateTime to, bool includeRefunds = false);

    Task<List<OrderLine>> InvoiceLinesAsync(DateTime oldestDayAllowed, int merchantId, DateTime from, DateTime to,
        int day);

    Task<MonitorPerformanceDto> InvoiceLinesAsync(DateTime from, DateTime to, string exposureTrigger);

    Task<List<OrderLine>>
        PaginatedInvoiceLinesAsync(int merchantId, DateTime from, DateTime to, int page, int pageSize, string searchQuery = "");

    Task<decimal> InvoiceSumAsync(DateTime from, DateTime to, string exposureTrigger, string refId = "");

    Task<DashboardProfileBestMerchant> FetchBestMerchantAsync(List<int> merchantIds, DateTime? from = null, DateTime? to = null);
    
    Task FlaggedOrdersUpdateAsync();
    Task<List<InvoiceLineFlaggedOrderHeadDto>> GetFlaggedOrdersAsync();
    Task UpdateFlaggedOrdersAsync(int invoiceLineFlaggedOrdersId);
    Task UpdateLinesAsync(List<OrderLine> orderLines);
}