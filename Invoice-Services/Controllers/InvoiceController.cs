using Admin_Services.Services.Invoice;
using ClosedXML.Excel;
using Invoice_Services.Models.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using ILogger = Serilog.ILogger;

namespace Admin_Services.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class InvoiceController(ILogger logger, IInvoiceService invoiceService) : ControllerBase
{
    //Invoice stats
    [HttpGet]
    [Route("InvoiceStats/{from:datetime}/{to:datetime}/{invoiced:bool}")]
    public async Task<IActionResult> GetInvoiceOverviewAsync(DateTime from, DateTime to, bool invoiced)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetReadyForInvoiceAsync"))
            {
                var success = await invoiceService.GetInvoiceOverview(from, to, invoiced)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving ready for invoice");
            return Ok();
        }
    }

    //Invoice
    [HttpGet]
    [Route("ReadyForInvoice")]
    public async Task<IActionResult> GetReadyForInvoiceAsync()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetReadyForInvoiceAsync"))
            {
                var success = await invoiceService.GetReadyForInvoice()
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving ready for invoice");
            return Ok();
        }
    }

    [HttpGet]
    [Route("getInvoiced")]
    public async Task<IActionResult> GetInvoicedAsync()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "getInvoicedAsync"))
            {
                var success = await invoiceService.GetInvoicesAsync()
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while gettings invoiced");
            return Ok();
        }
    }

    [HttpGet]
    [Route("invoice/{payerId:int}")]
    public async Task<IActionResult> InvoiceAsync(int payerId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "InvoiceAsync"))
            {
                var success = await invoiceService.InvoiceAsync(payerId)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while invoicing payerId: {PayerId}", payerId);
            return Ok();
        }
    }

    [HttpGet]
    [Route("export/{payerId:int}")]
    public async Task<IActionResult> ExportInvoiceAsync(int payerId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "InvoiceAsync"))
            {
                var success = await invoiceService.ExportInvoiceAsync(payerId)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while invoicing payerId: {PayerId}", payerId);
            return Ok();
        }
    }

    [HttpGet]
    [Route("getInvoice/{accountBalanceId:int}")]
    public async Task<IActionResult> GetInvoiceAsync(int accountBalanceId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetInvoiceAsync"))
            {
                var success = await invoiceService.GetInvoiceAsync(accountBalanceId, null, true)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while getting invoicing accountBalanceId: {AccountBalanceId}", accountBalanceId);
            return Ok();
        }
    }


    [HttpGet]
    [Route("GetCurrentRevenue/{from:datetime}/{to:datetime}")]
    public async Task<IActionResult> GetCurrentRevenueAsync(DateTime from, DateTime to)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetCurrentRevenueAsync"))
            {
                var success = await invoiceService.GetCurrentRevenue(from, to)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting current revenue");
            return Ok();
        }
    }

    // Get Stats about Action Type for Orders
    [HttpGet]
    [Route("ActionTypeStats/{partnerId:int}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetActionTypeStatsByPartnerIdAsync(int partnerId, [FromQuery] DateTime? from = null, [FromQuery] DateTime? to = null)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetActionTypeStatsByPartnerIdAsync"))
            {
                var stats = await invoiceService.GetActionTypeStatsByPartnerIdAsync(partnerId, from, to)
                    .ConfigureAwait(false);

                // Create Excel workbook
                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("Action Type Statistics");

                // Set headers
                worksheet.Cell(1, 1).Value = "Action Type";
                worksheet.Cell(1, 2).Value = "Number of Rows";
                worksheet.Cell(1, 3).Value = "Total Price Sum";
                worksheet.Cell(1, 4).Value = "Total Price Tax Sum";
                worksheet.Cell(1, 5).Value = "Total Cut Sum";

                // Style headers
                var headerRange = worksheet.Range(1, 1, 1, 5);
                headerRange.Style.Font.Bold = true;
                headerRange.Style.Fill.BackgroundColor = XLColor.LightGray;
                headerRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thick;

                // Add data
                for (int i = 0; i < stats.Count; i++)
                {
                    var stat = stats[i];
                    worksheet.Cell(i + 2, 1).Value = stat.ActionType ?? "N/A";
                    worksheet.Cell(i + 2, 2).Value = stat.NumberOfRows;
                    worksheet.Cell(i + 2, 3).Value = stat.TotalPriceSum;
                    worksheet.Cell(i + 2, 4).Value = stat.TotalPriceTaxSum;
                    worksheet.Cell(i + 2, 5).Value = stat.TotalCutSum;
                }

                // Format currency columns
                worksheet.Column(3).Style.NumberFormat.Format = "#,##0.00";
                worksheet.Column(4).Style.NumberFormat.Format = "#,##0.00";
                worksheet.Column(5).Style.NumberFormat.Format = "#,##0.00";

                // Auto-fit columns
                worksheet.Columns().AdjustToContents();

                // Generate filename with date range
                var dateRangePart = "";
                if (from.HasValue && to.HasValue)
                {
                    dateRangePart = $"_{from.Value:yyyy-MM-dd}_to_{to.Value:yyyy-MM-dd}";
                }
                else if (from.HasValue)
                {
                    dateRangePart = $"_from_{from.Value:yyyy-MM-dd}";
                }
                else if (to.HasValue)
                {
                    dateRangePart = $"_to_{to.Value:yyyy-MM-dd}";
                }
                else
                {
                    dateRangePart = "_all_time";
                }

                var fileName = $"ActionTypeStats_Partner{partnerId}{dateRangePart}.xlsx";

                // Convert to byte array
                using var stream = new MemoryStream();
                workbook.SaveAs(stream);
                var fileBytes = stream.ToArray();

                return File(fileBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, 
                "Error while getting action type stats for partner: {PartnerId}, From: {From}, To: {To}", 
                partnerId, from, to);
            return StatusCode(500, "An error occurred while retrieving action type statistics");
        }
    }
}
