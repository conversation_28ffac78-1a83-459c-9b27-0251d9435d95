<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Invoice-Services\Invoice-Services.csproj" />
        <ProjectReference Include="..\Automation-Services\Automation-Services.csproj" />
        <ProjectReference Include="..\Campaign-Services\Campaign-Services.csproj" />
        <ProjectReference Include="..\Discount-Services\Discount-Services.csproj" />
        <ProjectReference Include="..\PartnerPortal-Services\PartnerPortal-Services.csproj" />
        <ProjectReference Include="..\General-Services\General-Services.csproj" />
        <ProjectReference Include="..\Notification-Services\Notification-Services.csproj" />
        <ProjectReference Include="..\Scheduler-Services\Scheduler-Services.csproj" />
        <ProjectReference Include="..\Shared\Shared.csproj" />
        <ProjectReference Include="..\Shop-Services\Shop-Services.csproj" />
        <ProjectReference Include="..\Statistics-Services\Statistics-Services.csproj" />
        <ProjectReference Include="..\Partner-Services\Partner-Services.csproj" />
    </ItemGroup>

</Project>