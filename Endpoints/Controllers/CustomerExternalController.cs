using Audience.Services.Audience;
using Marlin_OS_Integration_API.Services.Static;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Partner_Services.Services.PartnerData;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Services;
using ILogger = Serilog.ILogger;

namespace Endpoints.Controllers;

[AllowAnonymous]
[Route("[controller]")]
[ApiController]
public class CustomerExternalController(
    ILogger logger,
    ICustomerService customerService,
    IPartnerDataService partnerDataService)
    : ControllerBase
{
    [HttpGet]
    public async Task<IActionResult> GetAllConsentAsync()
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetAllAsync"))
                {
                    var success = await customerService.GetAllWithConsentForCampaignsAsync()
                        .ConfigureAwait(false);
                    return Ok(success);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving audience");
            return Ok();
        }
    }

    [HttpGet]
    [Route("All")]
    public async Task<IActionResult> GetAllAsync()
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetAllAsync"))
                {
                    var success = await customerService.GetAllAsync()
                        .ConfigureAwait(false);
                    return Ok(success);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving audience");
            return Ok();
        }
    }

    [HttpGet]
    [Route("Consenting")]
    public async Task<IActionResult> GetAllConsentListAsync()
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetAllConsentAsync"))
                {
                    var success = await customerService.GetAllConsentEmailListAsync()
                        .ConfigureAwait(false);
                    return Ok(success);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving audience consent");
            return Ok();
        }
    }

    
    // TODO - Check GetAllEmailPhoneAsync in Logs to see if it is being used
    [HttpGet]
    [Route("EmailPhone")]
    public async Task<IActionResult> GetAllEmailPhoneAsync()
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetAllEmailPhoneAsync"))
                {
                    var success = await customerService.GetEmailPhoneAsync()
                        .ConfigureAwait(false);
                    return Ok(success);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving audience phones");
            return Ok();
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("unsubscribe/{email}/{campaignId}")]
    [PartnerAuthExempt]
    public async Task<IActionResult> UnsubscribeByEmailAsync(string email, int? campaignId)
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetAllAsync"))
                {
                    var contacts = await customerService.UnsubscribeByEmailAsync(email, campaignId)
                        .ConfigureAwait(false);

                    if (contacts.Count == 0)
                    {
                        logger.Warning($"The Contact that tried to Unsubscribe does not exist, The email: {email}",
                            email);
                    }

                    await partnerDataService.Unsubscribe(contacts);

                    return Ok(contacts);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving audience");
            return Ok();
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("unsubscribePeriod/{lookBackDays:int}")]
    public async Task<IActionResult> UnsubscribeByEmailAsync(int lookBackDays)
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetAllAsync"))
                {
                    var unsubscribes = await customerService
                        .GetUnsubscribedCustomersPeriod(DateTime.Now.AddDays(-lookBackDays))
                        .ConfigureAwait(false);

                    var allContacts = await customerService.GetAllActiveAsync();
                    var contacts = allContacts.Where(a => unsubscribes.Any(b => b.Email == a.Email)).ToList();

                    if (contacts.Count == 0)
                    {
                        logger.Warning(
                            $"No Contacts found within the period provided: {DateTime.Now.AddDays(-lookBackDays)} - {DateTime.Now}",
                            DateTime.Now.AddDays(-lookBackDays), DateTime.Now);
                    }

                    await partnerDataService.Unsubscribe(contacts);

                    return Ok(contacts);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving audience");
            return Ok();
        }
    }
    
    [HttpGet]
    [Route("InvoiceWorker/{partnerId}")]
    [PartnerAuthExempt]
    public async Task<IActionResult> GetCustomersForInvoiceWorker(int partnerId)
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetCustomersForInvoiceWorker"))
                {
                    var success = await customerService.GetCustomersForInvoiceWorkerAsync(partnerId)
                        .ConfigureAwait(false);
                    return Ok(success);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving Customers for Invoice Worker");
            return Ok();
        }
    }
}