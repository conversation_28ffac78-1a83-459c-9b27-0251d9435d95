using Audience.Services.Audience;
using General_Services.Models.Models;
using Marlin_OS_Integration_API.Services.Static;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Partner_Services.Services.PartnerData;
using SerilogTimings.Extensions;
using Shared.Dto;
using Shared.Models;
using Shared.Services;
using ILogger = Serilog.ILogger;

namespace Endpoints.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class CustomerController(
    ILogger logger,
    ICustomerService customerService,
    IPartnerDataService partnerDataService)
    : ControllerBase
{
    [HttpGet]
    public async Task<IActionResult> GetAllAsync()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllConsentAsync"))
            {
                var success = await customerService.GetAllAsync()
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving audience");
            return Ok();
        }
    }

    [HttpGet]
    [Route("consent")]
    public async Task<IActionResult> GetAllConsentAsync()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllConsentAsync"))
            {
                var success = await customerService.GetAllConsentAsync()
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving audience");
            return Ok();
        }
    }

    //Keep for a period to support old mails
    [HttpGet]
    [AllowAnonymous]
    [Route("external/unsubscribe/{email}/{campaignId}")]
    public async Task<IActionResult> UnsubscribeByEmailAsync(string email, int? campaignId)
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetAllAsync"))
                {
                    var contacts = await customerService.UnsubscribeByEmailAsync(email, campaignId)
                        .ConfigureAwait(false);

                    if (contacts.Count == 0)
                    {
                        logger.Warning($"The Contact that tried to Unsubscribe does not exist, The email: {email}",
                            email);
                    }

                    await partnerDataService.Unsubscribe(contacts);

                    return Ok(contacts);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving audience");
            return Ok();
        }
    }

    [HttpPost]
    [Route("pagination")]
    public async Task<IActionResult> CustomersPagination(PaginationSearchDto paginationSearchDto)
    {
        try
        {
            return Ok(await customerService.GetPaginationAsync(paginationSearchDto));
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting pagination");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [Route("Import0")]
    public async Task<IActionResult> CustomersImport0(FileDto fileDto)
    {
        try
        {
            return Ok(await customerService.GetImport0Async(fileDto));
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error CustomersImport0");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [Route("Import1")]
    public async Task<IActionResult> CustomersImport1(CustomerImportDto customerImportDto)
    {
        try
        {
            return Ok(await customerService.GetImport1Async(customerImportDto));
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error CustomersImport0");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }
}