using System;

namespace Notification_Services.Models.Notification;

public class NotificationDto
{
    public int Id { get; set; }

    public DateTime Created { get; set; }

    public DateTime Updated { get; set; }

    public string Name { get; set; } = null!;

    public string Title { get; set; } = null!;

    public string Description { get; set; } = null!;

    public long FkMerchantId { get; set; }

    public bool Deleted { get; set; }

    public DateTime? Delivery { get; set; }

    public DateTime? Sent { get; set; }

    public string? Redirect { get; set; }
    public decimal Audience { get; set; }
    public decimal Delivered { get; set; }
    public decimal Clicks { get; set; }
    public decimal Conversion { get; set; }
}