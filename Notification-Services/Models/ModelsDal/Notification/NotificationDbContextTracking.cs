using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Notification_Service.Models.ModelsDal.Notification;
using Shared;
using IConnection = RabbitMQ.Client.IConnection;

namespace Notification_Services.Models.ModelsDal.Notification;

public class NotificationDbContextTracking(
    DbContextOptions<NotificationDbContext> options,
    IHttpContextAccessor httpContextAccessor,
    [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud)
    //IConnection rabbitConnection)
    : NotificationDbContext(options)
{
    public virtual async Task<int> SaveChangesAsync()
    {
        OnBeforeSaveChanges();
        var result = await base.SaveChangesAsync();
        return result;
    }

    public virtual int SaveChanges()
    {
        OnBeforeSaveChanges();
        var result = base.SaveChanges();
        return result;
    }

    private void OnBeforeSaveChanges()
    {
        AuditSaveDb.OnBeforeSaveChanges(ChangeTracker, httpContextAccessor, rabbitConnectionCloud);
    }
}