using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace Notification_Service.Models.ModelsDal.Notification;

public partial class NotificationDbContext : DbContext
{
    public NotificationDbContext(DbContextOptions<NotificationDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Notification_Services.Models.ModelsDal.Notification.Notification> Notifications { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Notification_Services.Models.ModelsDal.Notification.Notification>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Notifica__3214EC0706184653");

            entity.ToTable("Notifications", "notification");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Delivery).HasPrecision(0);
            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.FkMerchantId).HasColumnName("FK_MerchantId");
            entity.Property(e => e.LastModifiedDate)
                .HasPrecision(0)
                .HasDefaultValueSql("(getdate())");
            entity.Property(e => e.Name).HasMaxLength(200);
            entity.Property(e => e.Redirect).HasMaxLength(500);
            entity.Property(e => e.Sent).HasPrecision(0);
            entity.Property(e => e.Title).HasMaxLength(255);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}