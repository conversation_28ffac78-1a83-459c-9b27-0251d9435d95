namespace Notification_Services.Models.ModelsDal.Notification;

public partial class Notification
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public string Name { get; set; } = null!;

    public string Title { get; set; } = null!;

    public string Description { get; set; } = null!;

    public string? Redirect { get; set; }

    public DateTime? Delivery { get; set; }

    public DateTime? Sent { get; set; }

    public int FkMerchantId { get; set; }
}