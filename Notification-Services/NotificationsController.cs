using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Notification_Services.Models.ModelsDal.Notification;
using Notification_Services.Models.Notification;
using Notification_Services.Services.Notifications;
using SerilogTimings.Extensions;
using ILogger = Serilog.ILogger;

namespace Notification_Services;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class NotificationsController : ControllerBase
{
    private readonly ILogger _logger;
    private readonly INotificationService _notificationService;
    private readonly string _apiKey = "xxxx";
    private readonly string _apiKeyDev = "yyyy";
    private readonly IMapper _mapper;

    public NotificationsController(ILogger logger, INotificationService notificationService)
    {
        _logger = logger;
        _notificationService = notificationService;
        var config = new MapperConfiguration(cfg =>
        {
            //Webshop
            cfg.CreateMap<NotificationDto, Notification>().ReverseMap();
        });
        _mapper = config.CreateMapper();
    }

    [HttpGet]
    public async Task<IActionResult> GetNotificationsAsync()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetNotificationsAsync"))
            {
                var success = await _notificationService.GetNotifications()
                    .ConfigureAwait(false);
                return Ok(success);
                //return Ok(_mapper.Map<List<Notification>, List<NotificationDto>>(success));
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving notifications");
            return Ok();
        }
    }

    [HttpGet]
    [Route("stats/{key}")]
    public async Task<IActionResult> GetNotificationsStatsAsync(string key)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetNotificationsStatsAsync"))
            {
                var success = await _notificationService.GetNotificationsStats(key)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving notifications");
            return Ok();
        }
    }

    [HttpPost]
    [Route("AddNotification")]
    public async Task<IActionResult> AddDiscountsEventsAsync(NotificationDto notificationDto)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddNotification"))
            {
                var notification = _mapper.Map<NotificationDto, Notification>(notificationDto);
                var success = await _notificationService.AddNotification(notification).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error create notification");
            return BadRequest($"Error trying to consume event with type: {notificationDto.Name}");
        }
    }

    [HttpPut]
    [Route("UpdateNotification")]
    public async Task<IActionResult> UpdateNotificationEventsAsync(NotificationDto notificationDto)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateNotificationEventsAsync"))
            {
                var notification = _mapper.Map<NotificationDto, Notification>(notificationDto);
                var success = await _notificationService.UpdateNotification(notification).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error update notification");
            return BadRequest($"Error trying to consume event with type: {notificationDto.Name}");
        }
    }

    [HttpDelete]
    [Route("{notificationId:int}")]
    public async Task<IActionResult> DeleteNotificationAsync(int notificationId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "DeleteNotificationAsync"))
            {
                var success = await _notificationService.DeleteNotification(notificationId).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error delete notification");
            return BadRequest($"Error trying to delete notification with id: {notificationId}");
        }
    }

    private bool ValidateHeader()
    {
        return Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey").Value == _apiKey ||
               Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey").Value == _apiKeyDev;
    }
}