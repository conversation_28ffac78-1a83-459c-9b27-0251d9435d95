using System.Collections.Generic;
using System.Threading.Tasks;
using Notification_Service.Models.ModelsDal.Notification;
using Notification_Services.Models.ModelsDal.Notification;
using Notification_Services.Models.Notification;
using Shared.Models;

namespace Notification_Services.Services.Notifications
{
    public interface INotificationService
    {
        Task<List<NotificationDto>> GetNotifications();
        Task<StatsDto> GetNotificationsStats(string key);
        Task<Notification> AddNotification(Notification notification);
        Task<Notification> UpdateNotification(Notification notification);
        Task<ResponseDto> DeleteNotification(int notificationId);
    }
}