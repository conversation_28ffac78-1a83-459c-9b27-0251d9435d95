using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Notification_Service.Models.ModelsDal.Notification;
using Notification_Services.Models.ModelsDal.Notification;
using Notification_Services.Models.Notification;
using Renci.SshNet;
using Shared.Models;
using ILogger = Serilog.ILogger;

namespace Notification_Services.Services.Notifications;

public class NotificationService : INotificationService
{
    private readonly NotificationDbContextTracking _notificationDbContext;
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;


    public NotificationService(NotificationDbContextTracking notificationDbContext, ILogger logger,
        IConfiguration configuration)
    {
        _notificationDbContext = notificationDbContext;
        _logger = logger;
        _configuration = configuration;
    }


    public async Task<List<NotificationDto>> GetNotifications()
    {
        var config = new MapperConfiguration(cfg =>
        {
            //Webshop
            cfg.CreateMap<NotificationDto, Notification>().ReverseMap();
        });
        var mapper = config.CreateMapper();

        var notificationsRaw = await _notificationDbContext.Notifications.Where(a => a.Active == true).ToListAsync();
        var notifications = mapper.Map<List<Notification>, List<NotificationDto>>(notificationsRaw);
        foreach (var notification in notifications)
        {
            notification.Audience = 0;
            notification.Delivered = 0;
            notification.Clicks = 0;
            notification.Conversion = 0;
        }

        return notifications;
    }

    public async Task<StatsDto> GetNotificationsStats(string key)
    {
        var stats = new StatsDto
        {
            ConversionRate = 0,
            Clicked = 0,
            Clicks = 0,
            Delivered = 0,
            Viewed = 0,
        };
        return stats;
    }

    public async Task<Notification> AddNotification(Notification notification)
    {
        notification.CreatedDate = DateTime.UtcNow;
        notification.LastModifiedDate = DateTime.UtcNow;
        _notificationDbContext.Add(notification);
        await _notificationDbContext.SaveChangesAsync();
        return notification;
    }

    public async Task<Notification> UpdateNotification(Notification notification)
    {
        notification.LastModifiedDate = DateTime.UtcNow;
        _notificationDbContext.Update(notification);
        await _notificationDbContext.SaveChangesAsync();
        return notification;
    }

    public async Task<ResponseDto> DeleteNotification(int notificationId)
    {
        var notification = await _notificationDbContext.Notifications.SingleAsync(a => a.Id == notificationId);
        notification.Active = false;
        _notificationDbContext.Update(notification);
        await _notificationDbContext.SaveChangesAsync();
        return new ResponseDto
        {
            Success = true,
            Message = ""
        };
    }
}