<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Elastic.Apm.SerilogEnricher" Version="8.6.1" />
        <PackageReference Include="Elastic.CommonSchema.Serilog" Version="1.5.3" />
        <PackageReference Include="NEST" Version="7.17.5" />
        <PackageReference Include="RabbitMQ.Client" Version="6.8.1" />
        <PackageReference Include="SendGrid" Version="9.29.3" />
        <PackageReference Include="UAParser" Version="3.1.47" />
        <PackageReference Include="System.ServiceModel.Duplex" Version="6.0.0" />
        <PackageReference Include="System.ServiceModel.Http" Version="8.0.0" />
        <PackageReference Include="System.ServiceModel.NetTcp" Version="8.0.0" />
        <PackageReference Include="System.ServiceModel.Security" Version="6.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Campaign-Services\Campaign-Services.csproj" />
        <ProjectReference Include="..\Customer-Services\Customer-Services.csproj" />
        <ProjectReference Include="..\Discount-Services\Discount-Services.csproj" />
        <ProjectReference Include="..\Partner-Services\Partner-Services.csproj" />
        <ProjectReference Include="..\Shared\Shared.csproj" />
        <ProjectReference Include="..\Merchant-Services\Merchant-Services.csproj" />
    </ItemGroup>

</Project>