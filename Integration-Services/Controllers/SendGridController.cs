using System.Text;
using System.Text.Json;
using EllipticCurve;
using Integration.Models.SendGrid;
using Integration.Services.SendGrid;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using SerilogTimings.Extensions;
using Shared.Attributes;
using ILogger = Serilog.ILogger;

namespace Endpoints.Controllers.Integration;

[AllowAnonymous]
[PartnerAuthExempt]
[Route("[controller]")]
[ApiController]
public class SendGridController(
    ILogger logger,
    ISendGridWebHookService sendGridWebHookService)
    : ControllerBase
{
    private const string SignatureHeader = "X-Twilio-Email-Event-Webhook-Signature";
    private const string TimestampHeader = "X-Twilio-Email-Event-Webhook-Timestamp";

    private readonly string _sendGridWebHookPublicKey =
        "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEtAn0qFcvv/gVKMpzyknIkd33IwXlRVlKY0Eo+zh53wdN6JuVcB/ouNJVwRMDh0UZMVNj7C+faI1tFw3mcEBWmw==";

    // Valid template IDs and their merchant mappings - single source of truth
    private readonly Dictionary<string, string> _validTemplateMapping = new()
    {
        { "d-20327456b86b4dea9b11db6055e3f185", "5624,5653,5664,5667,5652,5625,5560,5630,5650" }, // Mens Template
        { "d-7b7b8a4b9a2f42c0b4a0383773a4b3a9", "5645,5653,5665,5667,5625,5625,5624,5664,5650" }, // Womens Template
        { "d-33b91e957cb441a99e065e37528c4e39", "5624,5653,5664,5667,5652,5625,5560,5630,5650" }
    };

    [HttpPost]
    public async Task<IActionResult> SendGrid()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "SendGrid"))
            {
                logger.Error("Request Object: {@Request}", Request);
                var body = await ReadStream(Request.Body);
                var jsonString = JsonConvert.SerializeObject(Request.Headers);
                // Log the JSON string
                logger.Error("Request Headers: {@RequestHeaders}", jsonString);
                if (IsValidRequest(Request, body))
                {
                    JsonDocument jsonDocument = JsonDocument.Parse(body);
                    var sendGridEvent = jsonDocument.RootElement.EnumerateArray().First();
                    sendGridEvent.TryGetProperty("sg_event_id", out var eventId);
                    sendGridEvent.TryGetProperty("sg_message_id", out var messageId);
                    sendGridEvent.TryGetProperty("event", out var eventType);
                    sendGridEvent.TryGetProperty("email", out var email);
                    sendGridEvent.TryGetProperty("timestamp", out var timestamp);
                    sendGridEvent.TryGetProperty("smtp-id", out var smtpId);
                    sendGridEvent.TryGetProperty("response", out var response);
                    sendGridEvent.TryGetProperty("reason", out var reason);
                    sendGridEvent.TryGetProperty("useragent", out var useragent);
                    sendGridEvent.TryGetProperty("ip", out var ip);
                    sendGridEvent.TryGetProperty("CampaignId", out var campaignId);
                    sendGridEvent.TryGetProperty("EmailGuid", out var emailGuid);
                    sendGridEvent.TryGetProperty("Merchants", out var merchants);
                    sendGridEvent.TryGetProperty("SendDate", out var sendDate);
                    sendGridEvent.TryGetProperty("PartnerId", out var partnerId);

                    var webHookDto = new SendGridWebHookDto
                    {
                        EventId = eventId.ToString(),
                        MessageId = messageId.ToString(),
                        Event = eventType.ToString(),
                        Email = email.ToString(),
                        Timestamp = timestamp.GetInt64(),
                        SmtpId = smtpId.ToString(),
                        Response = response.ToString(),
                        Useragent = useragent.ToString(),
                        Ip = ip.ToString(),
                        CampaignId = campaignId.ToString(),
                        EmailGuid = emailGuid.ToString(),
                        Merchants = merchants.ToString(),
                        SendDate = sendDate.ToString(),
                        PartnerId = partnerId.ToString() ?? ""
                    };
                    if (webHookDto.Response == "")
                    {
                        webHookDto.Response = reason.ToString();
                    }

                    await sendGridWebHookService.WebHook(webHookDto, body)
                        .ConfigureAwait(false);
                    return Ok();
                }


                logger.ForContext("service_name", GetType().Name)
                    .Error("SendGrid webhooks failed: Body||| " + body + " ||| " + jsonString);
                return Unauthorized();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while adding sendgrid Event");
            return BadRequest();
        }
    }

    [HttpPost]
    [Route("webhook/happypay")]
    public async Task<IActionResult> SendGridHappyPayWebhook()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "SendGridHappyPayWebhook"))
            {
                logger.Information("SendGrid HappyPay Webhook Request received");
                var body = await ReadStream(Request.Body);
                logger.Information("SendGrid HappyPay Webhook Body: {Body}", body);

                // No signature validation for open webhooks as per requirement
                JsonDocument jsonDocument = JsonDocument.Parse(body);
                var events = jsonDocument.RootElement.EnumerateArray().ToArray();
                
                int processedOpenEvents = 0;
                
                // Process each event in the array, but only handle "open" events
                foreach (var sendGridEvent in events)
                {
                    // Check if this is an "open" event
                    if (!sendGridEvent.TryGetProperty("event", out var eventType) || 
                        eventType.GetString() != "open")
                    {
                        continue; // Skip non-open events
                    }
                    
                    // Extract template ID first for early validation
                    string campaignId = "";
                    if (sendGridEvent.TryGetProperty("sg_template_id", out var templateId))
                    {
                        campaignId = templateId.GetString() ?? "";
                    }
                    
                    // Early return if template ID is not one of the valid templates
                    if (string.IsNullOrEmpty(campaignId) || !_validTemplateMapping.ContainsKey(campaignId))
                    {
                        logger.Information("Skipping open event - Template ID {TemplateId} not in valid list", campaignId);
                        return Ok();
                    }
                    
                    // Extract standard SendGrid event properties only for valid templates
                    sendGridEvent.TryGetProperty("sg_event_id", out var eventId);
                    sendGridEvent.TryGetProperty("sg_message_id", out var messageId);
                    sendGridEvent.TryGetProperty("email", out var email);
                    sendGridEvent.TryGetProperty("timestamp", out var timestamp);
                    sendGridEvent.TryGetProperty("useragent", out var useragent);
                    sendGridEvent.TryGetProperty("ip", out var ip);
                    
                    // Get merchant IDs from validated template mapping
                    string merchantIds = _validTemplateMapping[campaignId];
                    
                    logger.Information("Processing Open Event - Template ID: {TemplateId}, Mapped Merchants: {MerchantIds}, Email: {Email}", 
                        campaignId, merchantIds, email.GetString());

                    var webHookDto = new SendGridWebHookDto
                    {
                        EventId = eventId.GetString() ?? "n/a",
                        MessageId = messageId.GetString() ?? "n/a",
                        Event = "open",
                        Email = email.GetString() ?? "",
                        Timestamp = timestamp.GetInt64(),
                        //SmtpId = smtpId.GetString() ?? "n/a",
                        Response = "",
                        Useragent = useragent.GetString() ?? "n/a",
                        Ip = ip.GetString() ?? "0.0.0.0",
                        CampaignId = "SendGrid: " + campaignId, // Set to n/a as per requirement
                        EmailGuid = Guid.Empty.ToString(), // Set to n/a as per requirement
                        Merchants = merchantIds, // Use mapped merchants
                        SendDate = timestamp.GetInt64().ToString(),
                        PartnerId = "87317" // Hardcoded as per requirement
                    };

                    await sendGridWebHookService.WebHook(webHookDto, body)
                        .ConfigureAwait(false);
                        
                    processedOpenEvents++;
                }
                
                logger.Information("Processed {Count} open events from webhook payload", processedOpenEvents);
                    
                return Ok();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while processing SendGrid HappyPay webhook");
            return BadRequest();
        }
    }

    private bool IsValidRequest(HttpRequest request, string body)
    {
        try
        {
            request.Headers.TryGetValue(SignatureHeader, out var signatureHeaderValues);
            request.Headers.TryGetValue(TimestampHeader, out var timestampHeaderValues);

            var signature = signatureHeaderValues.FirstOrDefault();
            var timestamp = timestampHeaderValues.FirstOrDefault();

            if (string.IsNullOrWhiteSpace(signature) || string.IsNullOrWhiteSpace(timestamp))
            {
                return false;
            }

            return VerifySignature(_sendGridWebHookPublicKey, body, signature, timestamp);
        }
        catch (Exception)
        {
            var jsonHeaders = JsonConvert.SerializeObject(Request.Headers);

            logger.ForContext("service_name", GetType().Name).Error(
                "SendGrid webhooks failed to authorized headers: {JsonHeaders} Body: {Body}", jsonHeaders,
                body);

            return false;
        }
    }


    private bool VerifySignature(string verificationKey, string payload, string signature, string timestamp)
    {
        var publicKey = PublicKey.fromPem(verificationKey);

        var timestampedPayload = timestamp + payload;
        var decodedSignature = Signature.fromBase64(signature);

        return Ecdsa.verify(timestampedPayload, decodedSignature, publicKey);
    }

    private async Task<string> ReadStream(Stream stream)
    {
        using var ms = new MemoryStream();
        await stream.CopyToAsync(ms);
        return Encoding.UTF8.GetString(ms.GetBuffer(), 0, (int) ms.Length);
    }
}