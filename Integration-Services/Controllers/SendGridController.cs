using System.Text;
using System.Text.Json;
using EllipticCurve;
using Integration.Models.SendGrid;
using Integration.Services.SendGrid;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using SerilogTimings.Extensions;
using Shared.Attributes;
using ILogger = Serilog.ILogger;

namespace Endpoints.Controllers.Integration;

[AllowAnonymous]
[PartnerAuthExempt]
[Route("[controller]")]
[ApiController]
public class SendGridController(
    ILogger logger,
    ISendGridWebHookService sendGridWebHookService)
    : ControllerBase
{
    private const string SignatureHeader = "X-Twilio-Email-Event-Webhook-Signature";
    private const string TimestampHeader = "X-Twilio-Email-Event-Webhook-Timestamp";

    private readonly string _sendGridWebHookPublicKey =
        "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEtAn0qFcvv/gVKMpzyknIkd33IwXlRVlKY0Eo+zh53wdN6JuVcB/ouNJVwRMDh0UZMVNj7C+faI1tFw3mcEBWmw==";

    // Campaign name to merchant mapping - easy to configure
    // Keys are automatically converted to lowercase and trimmed for matching
    private readonly Dictionary<string, string> _campaignToMerchantMapping = new()
    {
        { "happy ads | month-end mailer | men", "1000,2000,3000,4000,5000,5500,5560" },
        { "cat facts", "7000,8000,9000" }, // Test mapping for the provided example
        // Add more mappings here as needed - use lowercase for keys
        // { "campaign name", "merchant1,merchant2,merchant3" }
    };

    [HttpPost]
    public async Task<IActionResult> SendGrid()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "SendGrid"))
            {
                logger.Error("Request Object: {@Request}", Request);
                var body = await ReadStream(Request.Body);
                var jsonString = JsonConvert.SerializeObject(Request.Headers);
                // Log the JSON string
                logger.Error("Request Headers: {@RequestHeaders}", jsonString);
                if (IsValidRequest(Request, body))
                {
                    JsonDocument jsonDocument = JsonDocument.Parse(body);
                    var sendGridEvent = jsonDocument.RootElement.EnumerateArray().First();
                    sendGridEvent.TryGetProperty("sg_event_id", out var eventId);
                    sendGridEvent.TryGetProperty("sg_message_id", out var messageId);
                    sendGridEvent.TryGetProperty("event", out var eventType);
                    sendGridEvent.TryGetProperty("email", out var email);
                    sendGridEvent.TryGetProperty("timestamp", out var timestamp);
                    sendGridEvent.TryGetProperty("smtp-id", out var smtpId);
                    sendGridEvent.TryGetProperty("response", out var response);
                    sendGridEvent.TryGetProperty("reason", out var reason);
                    sendGridEvent.TryGetProperty("useragent", out var useragent);
                    sendGridEvent.TryGetProperty("ip", out var ip);
                    sendGridEvent.TryGetProperty("CampaignId", out var campaignId);
                    sendGridEvent.TryGetProperty("EmailGuid", out var emailGuid);
                    sendGridEvent.TryGetProperty("Merchants", out var merchants);
                    sendGridEvent.TryGetProperty("SendDate", out var sendDate);
                    sendGridEvent.TryGetProperty("PartnerId", out var partnerId);

                    var webHookDto = new SendGridWebHookDto
                    {
                        EventId = eventId.ToString(),
                        MessageId = messageId.ToString(),
                        Event = eventType.ToString(),
                        Email = email.ToString(),
                        Timestamp = timestamp.GetInt64(),
                        SmtpId = smtpId.ToString(),
                        Response = response.ToString(),
                        Useragent = useragent.ToString(),
                        Ip = ip.ToString(),
                        CampaignId = campaignId.ToString(),
                        EmailGuid = emailGuid.ToString(),
                        Merchants = merchants.ToString(),
                        SendDate = sendDate.ToString(),
                        PartnerId = partnerId.ToString() ?? ""
                    };
                    if (webHookDto.Response == "")
                    {
                        webHookDto.Response = reason.ToString();
                    }

                    await sendGridWebHookService.WebHook(webHookDto, body)
                        .ConfigureAwait(false);
                    return Ok();
                }


                logger.ForContext("service_name", GetType().Name)
                    .Error("SendGrid webhooks failed: Body||| " + body + " ||| " + jsonString);
                return Unauthorized();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while adding sendgrid Event");
            return BadRequest();
        }
    }

    [HttpPost]
    [Route("webhook/happypay")]
    public async Task<IActionResult> SendGridHappyPayWebhook()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "SendGridHappyPayWebhook"))
            {
                logger.Information("SendGrid HappyPay Webhook Request received");
                var body = await ReadStream(Request.Body);
                logger.Information("SendGrid HappyPay Webhook Body: {Body}", body);

                // No signature validation for open webhooks as per requirement
                JsonDocument jsonDocument = JsonDocument.Parse(body);
                var events = jsonDocument.RootElement.EnumerateArray().ToArray();
                
                int processedOpenEvents = 0;
                
                // Process each event in the array, but only handle "open" events
                foreach (var sendGridEvent in events)
                {
                    // Check if this is an "open" event
                    if (!sendGridEvent.TryGetProperty("event", out var eventType) || 
                        eventType.GetString() != "open")
                    {
                        continue; // Skip non-open events
                    }
                    
                    // Extract standard SendGrid event properties
                    sendGridEvent.TryGetProperty("sg_event_id", out var eventId);
                    sendGridEvent.TryGetProperty("sg_message_id", out var messageId);
                    sendGridEvent.TryGetProperty("email", out var email);
                    sendGridEvent.TryGetProperty("timestamp", out var timestamp);
                    sendGridEvent.TryGetProperty("smtp-id", out var smtpId);
                    sendGridEvent.TryGetProperty("useragent", out var useragent);
                    sendGridEvent.TryGetProperty("ip", out var ip);
                    
                    // Extract campaign name for merchant mapping
                    string campaignName = "";
                    if (sendGridEvent.TryGetProperty("sg_template_name", out var templateName))
                    {
                        campaignName = templateName.GetString() ?? "";
                    }
                    else if (sendGridEvent.TryGetProperty("marketing_campaign_name", out var marketingCampaignName))
                    {
                        campaignName = marketingCampaignName.GetString() ?? "";
                    }
                    else if (sendGridEvent.TryGetProperty("category", out var category))
                    {
                        // Category might be an array, handle both cases
                        if (category.ValueKind == JsonValueKind.Array)
                        {
                            var categoryArray = category.EnumerateArray().ToArray();
                            campaignName = categoryArray.Length > 0 ? categoryArray[0].GetString() ?? "" : "";
                        }
                        else
                        {
                            campaignName = category.GetString() ?? "";
                        }
                    }
                    
                    // Map campaign name to merchants (case-insensitive and trimmed)
                    string merchantIds = "";
                    if (!string.IsNullOrEmpty(campaignName))
                    {
                        var normalizedCampaignName = campaignName.Trim().ToLowerInvariant();
                        if (_campaignToMerchantMapping.ContainsKey(normalizedCampaignName))
                        {
                            merchantIds = _campaignToMerchantMapping[normalizedCampaignName];
                        }
                    }
                    
                    logger.Information("Processing Open Event - Campaign: {CampaignName}, Mapped Merchants: {MerchantIds}, Email: {Email}", 
                        campaignName, merchantIds, email.GetString());

                    var webHookDto = new SendGridWebHookDto
                    {
                        EventId = eventId.GetString() ?? "n/a",
                        MessageId = messageId.GetString() ?? "n/a",
                        Event = "open",
                        Email = email.GetString() ?? "",
                        Timestamp = timestamp.GetInt64(),
                        SmtpId = smtpId.GetString() ?? "n/a",
                        Response = "",
                        Useragent = useragent.GetString() ?? "n/a",
                        Ip = ip.GetString() ?? "0.0.0.0",
                        CampaignId = "n/a", // Set to n/a as per requirement
                        EmailGuid = "n/a", // Set to n/a as per requirement
                        Merchants = merchantIds, // Use mapped merchants
                        SendDate = timestamp.GetInt64().ToString(),
                        PartnerId = "87317" // Hardcoded as per requirement
                    };

                    await sendGridWebHookService.WebHook(webHookDto, body)
                        .ConfigureAwait(false);
                        
                    processedOpenEvents++;
                }
                
                logger.Information("Processed {Count} open events from webhook payload", processedOpenEvents);
                    
                return Ok();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while processing SendGrid HappyPay webhook");
            return BadRequest();
        }
    }

    private bool IsValidRequest(HttpRequest request, string body)
    {
        try
        {
            request.Headers.TryGetValue(SignatureHeader, out var signatureHeaderValues);
            request.Headers.TryGetValue(TimestampHeader, out var timestampHeaderValues);

            var signature = signatureHeaderValues.FirstOrDefault();
            var timestamp = timestampHeaderValues.FirstOrDefault();

            if (string.IsNullOrWhiteSpace(signature) || string.IsNullOrWhiteSpace(timestamp))
            {
                return false;
            }

            return VerifySignature(_sendGridWebHookPublicKey, body, signature, timestamp);
        }
        catch (Exception)
        {
            var jsonHeaders = JsonConvert.SerializeObject(Request.Headers);

            logger.ForContext("service_name", GetType().Name).Error(
                "SendGrid webhooks failed to authorized headers: {JsonHeaders} Body: {Body}", jsonHeaders,
                body);

            return false;
        }
    }


    private bool VerifySignature(string verificationKey, string payload, string signature, string timestamp)
    {
        var publicKey = PublicKey.fromPem(verificationKey);

        var timestampedPayload = timestamp + payload;
        var decodedSignature = Signature.fromBase64(signature);

        return Ecdsa.verify(timestampedPayload, decodedSignature, publicKey);
    }

    private async Task<string> ReadStream(Stream stream)
    {
        using var ms = new MemoryStream();
        await stream.CopyToAsync(ms);
        return Encoding.UTF8.GetString(ms.GetBuffer(), 0, (int) ms.Length);
    }
}