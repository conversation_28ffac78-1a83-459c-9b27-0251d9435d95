using System.Net;
using System.Text.Json;
using Integration.Models;
using Integration.Services.Plugins.Integration;
using Marlin_OS_Integration_API.Services.Plugins.CustomIntegration;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Dto.Custom.Event;
using Shared.Dto.Custom.Order;
using Shared.Dto.CustomDto;
using ILogger = Serilog.ILogger;

namespace Endpoints.Controllers.Integration.Plugins;

[AllowAnonymous]
[PartnerAuthExempt]
[ApiController]
[Route("")]
public class CustomIntegrationController(ILogger logger, ICustomIntegrationService customIntegrationService, IIntegrationService integrationService)
    : ControllerBase
{
    [HttpPost]
    [Route("event")]
    public async Task<IActionResult> AddEventAsync(CustomEventDto customEvent)
    {
        // TODO - Temp Solution To Reduce Resource Usage
        return Ok();
        
        if (!ModelState.IsValid)
        {
            logger.ForContext("service_name", GetType().Name).Warning(
                "ModelState: {ModelState}: JSON {Json}", ModelState.IsValid, JsonSerializer.Serialize(customEvent));
            return BadRequest("Invalid data.");
        }

        var apiKey = Request.Headers.ToList().SingleOrDefault(a => a.Key.Equals("apikey", StringComparison.CurrentCultureIgnoreCase)).Value.ToString();
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddEventAsync"))
            {
                var success = await customIntegrationService
                    .AddEventAsync(customEvent, apiKey)
                    .ConfigureAwait(false);
                /*var response = new CustomResponse();
                //Validate eventType
                var eventTypeMappings = new Dictionary<string, string>(StringComparer.CurrentCultureIgnoreCase)
                {
                    {"addcart", "AddCart"},
                    {"removecart", "RemoveCart"},
                    {"addwishlist", "AddWishlist"},
                    {"removewishlist", "RemoveWishlist"},
                    {"productlook", "ProductLook"}
                };
                if (eventTypeMappings.TryGetValue(customEvent.EventType, out var mappedEventType))
                {
                    customEvent.EventType = mappedEventType;
                }
                else
                {
                    logger.ForContext("service_name", GetType().Name)
                        .Error($"Invalid event type - The provided event type: '{customEvent.EventType}', is not recognized by the system. Please check the documentation for a list of valid event types and try again. If you continue to experience issues, please contact our support team for assistance.");
                         
                    response = new CustomResponse
                    {
                        Status = Shared.Models.StatusCode.BadRequest,
                        Message = $"Invalid event type - The provided event type: '{customEvent.EventType}', is not recognized by the system. Please check the documentation for a list of valid event types and try again. If you continue to experience issues, please contact our support team for assistance."
                    };
                    //return BadRequest($"Invalid event type - The provided event type: '{customEvent.EventType}', is not recognized by the system. Please check the documentation for a list of valid event types and try again. If you continue to experience issues, please contact our support team for assistance.")
                }

                //Validate ip is correct format
                customEvent.ClientIpAddress =
                    IPAddress.TryParse(customEvent.ClientIpAddress, out _) ? customEvent.ClientIpAddress : null;
                if (customEvent.ClientIpAddress != null && !Validate.ValidateIPv4(customEvent.ClientIpAddress) && !Validate.ValidateIPv4(customEvent.ClientIpAddress))
                    //if (customEvent.ClientIpAddress != null)
                {
                    logger.ForContext("service_name", GetType().Name)
                        .Error($"Invalid IP address format - The provided IP address: '{customEvent.ClientIpAddress}', is not in the correct format. The expected format is 0.0.0.0. Please double-check the IP address and try again. If you continue to experience issues, please contact our support team for assistance.");
                    /*return BadRequest(
                        $"Invalid IP address format - The provided IP address: '{customEvent.ClientIpAddress}', is not in the correct format. The expected format is 0.0.0.0. Please double-check the IP address and try again. If you continue to experience issues, please contact our support team for assistance.");#1#
                    response = new CustomResponse
                    {
                        Status = Shared.Models.StatusCode.BadRequest,
                        Message = $"Invalid IP address format - The provided IP address: '{customEvent.ClientIpAddress}', is not in the correct format. The expected format is 0.0.0.0. Please double-check the IP address and try again. If you continue to experience issues, please contact our support team for assistance."
                    };
                }

                var behaviorEvent = new BehaviorEvent()
                {
                    ApiKey = apiKey,
                    EventDate = customEvent.EventDate,
                    ProductSku = customEvent.ProductSku,
                    Price = customEvent.Price,
                    Email = customEvent.Email,
                    Cookie = customEvent.Cookie,
                    Ip = customEvent.ClientIpAddress,
                    UserAgent = customEvent.UserAgent,
                    MerchantType = MerchantTypes.Custom,
                    EventType = customEvent.EventType
                };
                
                var success = await integrationService
                    .AddBehaviorEventForProcessingAsync(behaviorEvent)
                    .ConfigureAwait(false);*/
                
                if (success.Status == Shared.Models.StatusCode.Unauthorized)
                {
                    logger.ForContext("service_name", GetType().Name).Warning(
                        "Error while adding event1 for custom shop: {ApiKey} message: {Message} event: {Event}", apiKey,
                        success.Message, JsonSerializer.Serialize(customEvent));
                    return Unauthorized(success.Message);
                }

                if (success.Status == Shared.Models.StatusCode.BadRequest)
                {
                    logger.ForContext("service_name", GetType().Name).Warning(
                        "Error while adding event for custom shop: {ApiKey} message: {Message} event: {Event}", apiKey,
                        success.Message, JsonSerializer.Serialize(customEvent));
                    return UnprocessableEntity(success.Message);
                }

                if (success.Status == Shared.Models.StatusCode.Ok) 
                    return Ok(success);
                
                logger.ForContext("service_name", GetType().Name).Warning(
                    "Error while adding event2 for custom shop: {ApiKey} message: {Message} event: {Event}", apiKey,
                    success.Message, JsonSerializer.Serialize(customEvent));
                return UnprocessableEntity(success.Message);

            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while adding Event for custom shop with ApiKey: {ApiKey} | Data: {Data}", apiKey,
                    JsonSerializer.Serialize(customEvent));
            return StatusCode(500);
        }
    }

    /// <summary>
    /// This endpoint is used to receive a daily list of customer orders - REQUIRED
    /// </summary>
    /// <param name="orders"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("orders")]
    public async Task<IActionResult> AddOrdersAsync(List<CustomOrderDto> orders)
    {
        var apiKey = Request.Headers.ToList().SingleOrDefault(a => a.Key.ToLower() == "apikey").Value.ToString();
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0} ApiKey {1}", "AddOrdersAsync", apiKey))
            {
                var success = await customIntegrationService
                    .AddOrderAsync(orders, apiKey)
                    .ConfigureAwait(false);
                if (success.Status == Shared.Models.StatusCode.Unauthorized)
                {
                    logger.ForContext("service_name", GetType().Name)
                        .Error($"Error while adding Orders for custom shop: {apiKey} message: {success.Message}");
                    return Unauthorized(success);
                }

                if (success.Status == Shared.Models.StatusCode.BadRequest)
                {
                    logger.ForContext("service_name", GetType().Name)
                        .Error($"Error while adding Order for custom shop: {apiKey} message: {success.Message}");
                    return BadRequest(success);
                }

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while adding Order for custom shop: {ApiKey}", apiKey);
            return StatusCode(500);
        }
    }

    /// <summary>
    /// This endpoint is used to receive a single customer order. This enables more efficient retargeting if an order is sent directly after its created - OPTIONAL
    /// This endpoint is not included as an 'Event' and is therefore not restricted by the Marketing Cookie Acceptance
    /// </summary>
    /// <param name="order"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("order")]
    public async Task<IActionResult> AddOrderAsync(CustomOrderDto order)
    {
        var apiKey = Request.Headers.ToList().SingleOrDefault(a => a.Key.ToLower() == "apikey").Value.ToString();
        if(string.IsNullOrEmpty(apiKey))
        {
            apiKey = Request.Headers.ToList().SingleOrDefault(a => a.Key.ToLower() == "site-identifier").Value.ToString();
        }
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0} ApiKey {1}", "AddOrderAsync", apiKey))
            {
                var success = await customIntegrationService
                    .AddOrderAsync(new List<CustomOrderDto> {order}, apiKey)
                    .ConfigureAwait(false);
                if (success.Status == Shared.Models.StatusCode.Unauthorized)
                {
                    logger.ForContext("service_name", GetType().Name)
                        .Error($"Error while adding Order for custom shop: {apiKey} message: {success.Message}");
                    return Unauthorized(success);
                }

                if (success.Status == Shared.Models.StatusCode.BadRequest)
                {
                    logger.ForContext("service_name", GetType().Name)
                        .Error($"Error while adding Order for custom shop: {apiKey} message: {success.Message}");
                    return BadRequest(success);
                }

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while adding Order for custom shop: {ApiKey}", apiKey);
            return StatusCode(500);
        }
    }

    ///////////////////////// JAVASCRIPT /////////////////////////

    [HttpPost]
    [AllowAnonymous]
    [Route("javascriptEvent")]
    public async Task<IActionResult> AddEventJavascriptAsync(CustomJavascriptEvenDto customEvent)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddJavascriptEventAsync"))
            {
                var success = await customIntegrationService
                    .AddEventAsync(customEvent)
                    .ConfigureAwait(false);

                if (success.Status == Shared.Models.StatusCode.Unauthorized)
                {
                    logger.ForContext("service_name", GetType().Name).Warning(
                        $"Error while adding event for custom shop: {customEvent.Key} message: {success.Message}");
                    return Unauthorized(success.Message);
                }

                if (success.Status == Shared.Models.StatusCode.BadRequest)
                {
                    logger.ForContext("service_name", GetType().Name).Warning(
                        $"Error while adding event for custom shop: {customEvent.Key} message: {success.Message}");
                    return BadRequest(success.Message);
                }

                return Ok();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while adding javascript Event for custom shop {ApiKey}", customEvent.Key);
            return StatusCode(500);
        }
    }
}