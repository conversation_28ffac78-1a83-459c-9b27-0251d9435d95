using AutoMapper;
using Integration.Services.Plugins.Integration;
using Marlin_OS_Integration_API.Services.Plugins.WoocommerceIntegration;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Dto.BehaviorDto;
using Shared.Dto.OrderDto;
using Shared.Dto.PrestaShop;
using Shared.Dto.PrestaShop.product;
using Shared.Elastic.Behavior;
using Shared.Elastic.Models.Behavior;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace Endpoints.Controllers.Integration.Plugins;

[AllowAnonymous]
[PartnerAuthExempt]
[ApiController]
[Route("prestaShop")]
public class PrestaShopIntegrationController : ControllerBase
{
    private readonly ILogger _logger;
    private readonly IMapper _mapperGlobal;
    private readonly IMapper _mapper;

    private readonly PrestaShopPluginService _prestaShopPluginService;
    private readonly IIntegrationService _integrationService;

    public PrestaShopIntegrationController(ILogger logger,
        IMapper mapperGlobal, IIntegrationService integrationService,
        IMerchantService merchantService)
    {
        _logger = logger;
        _prestaShopPluginService =
            new PrestaShopPluginService(logger, integrationService, mapperGlobal, merchantService);

        _mapperGlobal = mapperGlobal;
        _integrationService = integrationService;
        var config = new MapperConfiguration(cfg =>
        {
            //Webshop
            cfg.CreateMap<Merchant, ProductPrestaShop>().ReverseMap();
            cfg.CreateMap<Variant, VariantPrestaShop>().ReverseMap();
            cfg.CreateMap<Product, ProductPrestaShop>()
                .ReverseMap();
        });
        _mapper = config.CreateMapper();
    }

    /// <summary>
    /// Adds Page look Tracking elements to RabbitMQ
    /// </summary>
    /// <param name="elasticEventDto"></param>
    /// <returns>Response</returns>
    [HttpPost]
    [Route("event")]
    public async Task<IActionResult> AddBehaviorEventAsync(BehaviorEventDto elasticEventDto)
    {
        // TODO - Temp Solution To Reduce Resource Usage
        return Ok();
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddBehaviorPageLookEventPrestaAsync"))
            {
                elasticEventDto.Event_received = DateTime.UtcNow;
                var success = await _integrationService
                    .AddBehaviorEventAsync(_mapperGlobal.Map<BehaviorEventDto, ElasticBehaviorEvent>(elasticEventDto),
                        elasticEventDto.ApiKey).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error while adding behaviorPage Event for PrestaShop");
            return Ok();
        }
    }

    [HttpPost]
    [Route("WebShopOrderHook")]
    public async Task<IActionResult> WebShopOrderHookAsync(List<OrderEventDto> orders)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "WebShopOrderHookAsync"))
            {
                if (orders.Count > 0)
                {
                    var success = await _prestaShopPluginService
                        .WebshopOrderHookAsync(orders, orders.First().ApiKey).ConfigureAwait(false);
                    return Ok(success);
                }

                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error while updating WebShopOrderHookAsync");
            return Ok();
        }
    }

    /*[HttpPost]
    [Route("WebShopData")]
    public async Task<IActionResult> UpdateWebShopDataAsync(ProductPrestaShop webshopWooCommerce)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateWebShopDataAsync"))
            {
                var webshop =
                    _mapper.Map<ProductPrestaShop, Merchant>(
                        webshopWooCommerce);
                var success = await _prestaShopPluginService
                    .UpdateWebShopDataAsync(webshop).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while updating UpdateWebShopDataAsync");
            return Ok();
        }
    }*/

    /*[HttpPost]
    [Route("WebShopOrderData")]
    public async Task<IActionResult> UpdatePrestaWebShopOrderDataAsync(List<OrderEventDto> orders)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdatePrestaWebShopOrderDataAsync"))
            {
                if (orders.Count > 0)
                {
                    var success = await _prestaShopPluginService
                        .UpdateWebShopOrderDataAsync(orders, orders.First().ApiKey).ConfigureAwait(false);
                    return Ok(success);
                }

                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while updating UpdatePrestaWebShopOrderDataAsync");
            return Ok();
        }
    }*/

    [HttpPost]
    [Route("ApiKey")]
    public async Task<IActionResult> ApiKeyAsync(PrestashopApiKey prestashopApiKey)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ApiKeyAsync"))
            {
                var success = await _prestaShopPluginService
                    .CreateApiKeyAsync(prestashopApiKey).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while updating ApiKeyAsync");
            return Ok();
        }
    }
}