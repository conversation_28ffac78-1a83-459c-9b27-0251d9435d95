using Integration.Models;
using Integration.Models.Shopify;
using Integration.Services.Plugins.Integration;
using Integration.Services.Plugins.ShopifyIntegration;
using Marlin_OS_Integration_API.Services.Plugins;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Models;
using Shared.Models.Merchant;
using ShopifySharp;
using UAParser;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;
using JsonSerializer = System.Text.Json.JsonSerializer;
using IShopifyService = Webshop_Service.Services.Shopify.IShopifyService;

namespace Endpoints.Controllers.Integration.Plugins;

[AllowAnonymous]
[ApiController]
[Route("shopify")]
[PartnerAuthExempt]
public class ShopifyIntegrationController(
    ILogger logger,
    IIntegrationService integrationService,
    IMerchantService merchantService,
    IShopifyService shopifyService,
    IBehaviorEventClientService behaviorEventClientService,
    IMemoryCache memoryCache)
    : ControllerBase
{
    private static readonly List<string> Tokens = [];

    private readonly List<string> _requiredPermissions =
    [
        "write_pixels", "read_customer_events", "read_orders", "read_products", "read_script_tags", "write_script_tags", "read_customers"
    ];

    private readonly ShopifyPluginService _shopifyPluginService = new(logger, integrationService, merchantService, shopifyService, memoryCache);
    
    private static readonly List<ShopifyAuthInformation> ShopifyAuthInformationList =
    [
        new()
        {
            ShopifyAppName = "ViaAds",
            Identifier = "4AFA0795-316C-4303-AB94-A9FC39052022",
            ClientId = "7eabbacf1eb1e664d7af50429ac833bf",
            ClientSecret = "b24b0f8362bb307b56e6650c8ff1f68f",
            DashboardRedirectUri = new Uri("https://partners.viaads.dk/sign-in")
        },

        new()
        {
            ShopifyAppName = "Happy Ads",
            Identifier = "FC9B7259-B1B3-47C3-A961-D21ABA891EC3",
            ClientId = "682e68339cb3839eca2b0aeb2d881905",
            ClientSecret = "0698f61c0223009fcfe4c73d2db46abe",
            DashboardRedirectUri = new Uri("https://partners.happypay.shop/sign-in")

        }
    ];

    [HttpPost]
    [Consumes("text/plain")]
    [Route("eventPixel")]
    [PartnerAuthExempt]
    public async Task<IActionResult> PixelEventAsync([FromBody] string rawJsonString)
    {
        // TODO - Temp Solution To Reduce Resource Usage
        behaviorEventClientService.FireAndForgetPost("shopify", new StringContent(rawJsonString ,System.Text.Encoding.UTF8, "application/json"));
        return Ok();
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PixelEventAsync"))
            {
                var jsonString = rawJsonString.Replace("\"undefined\"", "null");
                if (jsonString.Contains(",\"Data"))
                {
                    jsonString = jsonString.Split(",\"Data")[0] + "}";
                }

                var shopifyBehaviorDto = JsonConvert.DeserializeObject<ShopifyBehaviorDto>(jsonString);
                if (shopifyBehaviorDto == null)
                {
                    logger.ForContext("service_name", GetType().Name).Error("Shopify json is null: {RawJsonString}",
                        rawJsonString);
                    return BadRequest();
                }
                
                try
                {
                    if (!string.IsNullOrEmpty(shopifyBehaviorDto.UserAgent))
                    {
                        var uaParser = Parser.GetDefault();
                        var c = uaParser.Parse(shopifyBehaviorDto.UserAgent);
                        shopifyBehaviorDto.Name = c.OS.ToString();
                        shopifyBehaviorDto.Version = $"{c.UA.Major}.{c.UA.Minor}.{c.UA.Patch}";
                        shopifyBehaviorDto.DeviceName = c.Device.ToString();
                    }
                }
                catch (Exception ex)
                {
                    logger.ForContext("service_name", GetType().Name).Error(ex,
                        "Parse userAgent failed shopify json: {UserAgent}", shopifyBehaviorDto.UserAgent);
                }
                    
                /*var success = await _shopifyPluginService
                        .AddBehaviorEventAsync(shopifyBehaviorDto).ConfigureAwait(false);*/
                    
                var behaviorEvent = new BehaviorEvent
                {
                    Url = shopifyBehaviorDto.Url,
                    DeviceName = shopifyBehaviorDto.DeviceName,
                    Name = shopifyBehaviorDto.Name,
                    Version = shopifyBehaviorDto.Version,
                    Cookie = shopifyBehaviorDto.ViaAds,
                    Cookie2 = shopifyBehaviorDto.ViaAds2,
                    Email = shopifyBehaviorDto.Email,
                    Email2 = shopifyBehaviorDto.Email2,
                    EventType = shopifyBehaviorDto.EventType,
                    Session = shopifyBehaviorDto.Session,
                    Ip = shopifyBehaviorDto.Ip,
                    ProductId = shopifyBehaviorDto.ProductId,
                    VariantId = shopifyBehaviorDto.VariantId,
                    ProductSku = shopifyBehaviorDto.Sku,
                    UserAgent = shopifyBehaviorDto.UserAgent,
                    MerchantId = shopifyBehaviorDto.WebShopId,
                    PluginVersion = shopifyBehaviorDto.PluginVersion,
                    MerchantType = MerchantTypes.Shopify
                };
                
                var success = await integrationService
                    .AddBehaviorEventForProcessingAsync(behaviorEvent)
                    .ConfigureAwait(false);
                    
                return Ok(success);

            }
        }
        catch (Exception ex)
        {
            if (rawJsonString != "")
            {
                logger.ForContext("service_name", GetType().Name).Error(ex,
                    "Error while adding behaviorPage Event for Shopify json: {RawJsonString}",
                    rawJsonString);
            }

            return Ok();
        }
    }

    [HttpGet]
    [Route("Handshake/{identifier}")]
    public IActionResult Handshake([FromQuery] string shop, string identifier)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(shop))
            {
                return Problem("Request is missing shop querystring parameter.", statusCode: 422);
            }

            var uri = Auth(shop, identifier);
            logger.ForContext("service_name", GetType().Name).Information("Shopify handshake uri {S}", uri.ToString());
            return Redirect(uri.ToString());
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, $"Error while Handshake Shopify: Shop: {shop}, identifier: {identifier}");
            return Ok();
        }
    }

    [HttpGet]
    [Route("AuthResult/{identifier}")]
    public async Task<IActionResult> AuthResult([FromQuery] string? shop, [FromQuery] string? code,
        [FromQuery] string? state, string identifier)
    {
        var accessToken = string.Empty;
        try
        {
            logger.ForContext("service_name", GetType().Name)
                .Information("Tokens shopify: {Serialize}", JsonSerializer.Serialize(Tokens));
            ShopifyAuthInformation shopifyCredentials;

            if (!string.IsNullOrEmpty(identifier))
            {
                shopifyCredentials = ShopifyAuthInformationList.First(a => a.Identifier == identifier);   
            }
            else
            {
                shopifyCredentials = ShopifyAuthInformationList.First();   
            }
            
            // Exchange the code param for a permanent Shopify access token
            accessToken =
                await AuthorizationService.Authorize(code, shop, shopifyCredentials.ClientId,
                    shopifyCredentials.ClientSecret);
            // Use the access token to get the user's shop info
            logger.ForContext("service_name", GetType().Name)
                .Information("Shopify app Shop: {Url} AccessToken: {AccessToken} ", shop, accessToken);
            var shopService = new ShopService(shop, accessToken);
            var shopData = await shopService.GetAsync();
            var shopify = await _shopifyPluginService.AuthShopifyAsync(shop, accessToken, shopData.Name, shopData.Email);
            if (shopify.Success)
            {
                return Redirect(shopifyCredentials.DashboardRedirectUri.ToString());
            }

            logger.ForContext("service_name", GetType().Name).Error(
                "Error while updating key AuthResult Shopify Status: {ShopifySuccess} Message: {ShopifyMessage}",
                shopify.Success, shopify.Message);

            // If failed to create app force user to load page again to see if it fixes it.
            Thread.Sleep(2000);
            return Redirect($"{Request.Path}?shop={shop}&code={code}&state={state}");
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, $"Error while AuthResult Shopify - Shop: {shop}, code: {code}, state: {state}, accessToken: {accessToken}");
            return Redirect("https://partners.viaads.dk/");
        }
    }

    private Uri Auth(string shop, string identifier)
    {
        var token = Guid.NewGuid().ToString();
        Tokens.Add(token);

        ShopifyAuthInformation shopifyCredentials;

        if (!string.IsNullOrEmpty(identifier))
        {
            shopifyCredentials = ShopifyAuthInformationList.First(a => a.Identifier == identifier);   
        }
        else
        {
            shopifyCredentials = ShopifyAuthInformationList.First();   
        }

        var oauthUrl = AuthorizationService.BuildAuthorizationUrl(
            _requiredPermissions,
            shop,
            shopifyCredentials.ClientId,
            //$"https://localhost:7100/shopify/AuthResult/{identifier}",
            $"https://integration.valyrion.com/shopify/AuthResult/{identifier}",
            token);
        return oauthUrl;
    }

    //Temp removed because of orders ending on wrong shops
    [HttpPost]
    [Route("webhookOrder")]
    public async Task<IActionResult> AddWebHookOrderShopifyAsync()
    {
        return Ok();
        /*try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddWebHookOrderShopifyAsync"))
            {
                var body = await ReadRawBodyAsync();
                var requestHeaders = Request.Headers;

                if (AuthorizationService.IsAuthenticWebhook(requestHeaders, body, shopifySecretKey))
                {
                    var order = ShopifySharp.Infrastructure.Serializer.Deserialize<ShopifySharp.Order>(body);
                    //await _shopifyIntegrationService.WebhookOrder(order);
                }
                else
                {
                 _logger.ForContext("service_name", GetType().Name).Error(ex, "Shopify order webhook failed auth: " + body);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while adding order from shopify webhook");
        }

        return Ok();*/
    }

    
    // TODO - DELETE this endpoint at a certain point, when its no longer being hit  
    [HttpPost]
    [Route("webHookProductDeleted")]
    public async Task<IActionResult> AddWebHookProductDeletedShopifyAsync()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddWebHookProductDeletedShopifyAsync"))
            {
                var body = await ReadRawBodyAsync();
                var requestHeaders = Request.Headers;

                // TODO - Change this to be Dynamic and not First of ShopifyAuthInformationList
                if (AuthorizationService.IsAuthenticWebhook(requestHeaders, body, ShopifyAuthInformationList.First().ClientSecret))
                {
                    var url = "";
                    if (requestHeaders.TryGetValue("X-Shopify-Shop-Domain", out var shopifyDomain))
                    {
                        url = shopifyDomain.ToString();
                    }

                    var shopifyProductDeleted = JsonSerializer.Deserialize<ShopifyProductDeleted>(body);
                    if (shopifyProductDeleted != null)
                    {
                        await merchantService.DeactivateProduct(shopifyProductDeleted.id.ToString(), url);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while handling product delete from shopify webhook");
        }

        return Ok();
    }

    [HttpPost]
    [Route("webHookProductDeleted/{identifier}")]
    public async Task<IActionResult> AddWebHookProductDeletedShopifyAsync(string identifier)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddWebHookProductDeletedShopifyAsync"))
            {
                var body = await ReadRawBodyAsync();
                var requestHeaders = Request.Headers;

                var shopifyCredentials = ShopifyAuthInformationList.First(a => a.Identifier == identifier);
                if (AuthorizationService.IsAuthenticWebhook(requestHeaders, body, shopifyCredentials.ClientSecret))
                {
                    var url = "";
                    if (requestHeaders.TryGetValue("X-Shopify-Shop-Domain", out var shopifyDomain))
                    {
                        url = shopifyDomain.ToString();
                    }

                    var shopifyProductDeleted = JsonSerializer.Deserialize<ShopifyProductDeleted>(body);
                    if (shopifyProductDeleted != null)
                    {
                        await merchantService.DeactivateProduct(shopifyProductDeleted.id.ToString(), url);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while handling product delete from shopify webhook");
        }

        return Ok();
    }
    
    /// <summary>
    /// Reads the request stream into a string, while simultaneously ensuring request handlers further down the pipeline can still use the request stream without it being disposed or stuck at the end.
    /// </summary>
    private async Task<string> ReadRawBodyAsync()
    {
        // Request streams by default can only be read once. This method enables multiple reads for this request.
        Request.EnableBuffering();

        var stream = Request.Body;
        string rawBody;

        // While the stream can now be rewound, using streamreader will automatically dispose it which we don't want.
        // Instead we need to copy the stream to another temporary stream that can be safely disposed without disposing the request.
        using (var buffer = new MemoryStream())
        {
            await stream.CopyToAsync(buffer);

            // Move the buffer position back to 0. If we don't do this,
            // the streamreader will just read an empty string.
            buffer.Position = 0;

            using (var reader = new StreamReader(buffer))
            {
                rawBody = await reader.ReadToEndAsync();
            }
        }

        if (stream.CanSeek)
        {
            stream.Position = 0;
        }

        return rawBody;
    }

    /******************************** Mandatory GDPR Endpoints **********************************/
    [HttpPost]
    [Route("customer/data_request")]
    public async Task<IActionResult> SetCustomerDataToBeRetrieved(
        ShopifyCustomerDataRetrieveDto shopifyCustomerDataRetrieveRequest)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "SetCustomerDataToBeRetrieved"))
            {
                logger.ForContext("service_name", GetType().Name).Error(
                    $"Customer Data Retrieve - A Shopify Merchant with Domain: {shopifyCustomerDataRetrieveRequest.ShopDomain}" +
                    $" has requested a copy of their data on the following Orders: {string.Join(", ", shopifyCustomerDataRetrieveRequest.OrdersRequested)} " +
                    $"on the following Customer: {string.Join(", ", shopifyCustomerDataRetrieveRequest.Customer.Email)}");
                return Ok("Request Received");
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while retrieving customer data");
            return BadRequest(ex.ToString());
        }
    }

    [HttpPost]
    [Route("customer/redact")]
    public async Task<IActionResult> SetCustomerDataToBeRedacted(
        ShopifyCustomerDataRedactionDto shopifyCustomerDataRedactionRequest)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "SetCustomerDataToBeRedacted"))
            {
                logger.ForContext("service_name", GetType().Name).Error(
                    $"Customer Data Redaction - A Shopify Merchant with Domain: {shopifyCustomerDataRedactionRequest.ShopDomain}" +
                    $" has requested a redaction of their data on the following Orders: {string.Join(", ", shopifyCustomerDataRedactionRequest.OrdersRequested)} " +
                    $"on the following Customer: {string.Join(", ", shopifyCustomerDataRedactionRequest.Customer.Email)}");
                return Ok("Request Received");
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while setting customer data to be redacted");
            return BadRequest(ex.ToString());
        }
    }

    [HttpPost]
    [Route("shop/redact")]
    public async Task<IActionResult> SetWebshopDataToBeRedacted(
        ShopifyWebshopDataRedactionDto shopifyWebshopDataRedactionRequest)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "SetWebshopDataToBeRedacted"))
            {
                await _shopifyPluginService.RemoveShopifyAsync(shopifyWebshopDataRedactionRequest.ShopDomain);
                return Ok("Request Received");
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while setting merchant data to be redacted");
            return BadRequest(ex.ToString());
        }
    }
}