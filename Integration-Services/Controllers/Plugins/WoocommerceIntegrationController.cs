using AutoMapper;
using Integration.Models;
using Integration.Services.Plugins.Integration;
using Marlin_OS_Integration_API.Services.Plugins;
using Marlin_OS_Integration_API.Services.Plugins.WoocommerceIntegration;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Dto.BehaviorDto;
using Shared.Dto.OrderDto;
using Shared.Dto.WooCommerce;
using Shared.Models.Merchant;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Endpoints.Controllers.Integration.Plugins;

[AllowAnonymous]
[PartnerAuthExempt]
[ApiController]
[Route("woocommerce")]
public class WoocommerceIntegrationController(
    ILogger logger,
    IIntegrationService integrationService,
    IMerchantService merchantService,
    IMapper mapperGlobal,
    IBehaviorEventClientService behaviorEventClientService)
    : ControllerBase
{
    private readonly WoocommercePluginService _woocommercePluginService = new(logger, integrationService, merchantService, mapperGlobal);

    /*var config = new MapperConfiguration(cfg =>
        {
            //Webshop
            cfg.CreateMap<Merchant, WebshopWooCommerce>().ReverseMap();
            cfg.CreateMap<Variant, VariantWooCommerce>().ReverseMap();
            cfg.CreateMap<MerchantCategory, CategoryWooCommerce>().ReverseMap();
            cfg.CreateMap<ProductImage, ProductImageDto>().ReverseMap();
            cfg.CreateMap<Product, ProductWooCommerce>().ReverseMap();
        });
        _mapper = config.CreateMapper();*/

    [HttpPost]
    [Route("event")]
    public async Task<IActionResult> AddBehaviorEventAsync(BehaviorEventDto behaviorEventDto)
    {
        // TODO - Temp Solution To Reduce Resource Usage (TRY THIS!)
        behaviorEventClientService.FireAndForgetPost("woocommerce", new StringContent(JsonSerializer.Serialize(behaviorEventDto),System.Text.Encoding.UTF8, "application/json"));
        return Ok();
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddBehaviorPageLookEventAsync"))
            {
                /*var success = await integrationService
                    .AddBehaviorEventAsync(mapperGlobal.Map<BehaviorEventDto, ElasticBehaviorEvent>(behaviorEventDto),
                        behaviorEventDto.ApiKey).ConfigureAwait(false);*/
                
                var behaviorEvent = new BehaviorEvent
                {
                    ApiKey = behaviorEventDto.ApiKey,
                    EventDate = behaviorEventDto.Event_date,
                    Ip = behaviorEventDto.client?.ip,
                    Url = behaviorEventDto.url?.full,
                    DeviceName = behaviorEventDto.user_agent?.device_name,
                    Name = behaviorEventDto.user_agent?.name,
                    Original = behaviorEventDto.user_agent?.original,
                    Version = behaviorEventDto.user_agent?.version,
                    Email = behaviorEventDto.Customer?.Email,
                    Email2 = behaviorEventDto.Customer?.Email2,
                    Session = behaviorEventDto.Customer?.Session_id,
                    Cookie = behaviorEventDto.Customer?.ViaAds,
                    Cookie2 = behaviorEventDto.Customer?.ViaAds2,
                    EventType = behaviorEventDto.Shop_event?.Event_type,
                    CampaignId = behaviorEventDto.Shop_event?.Campaign_id,
                    MerchantId = behaviorEventDto.Shop_event?.Webshop_id,
                    ProductSku = behaviorEventDto.Shop_event?.Product_sku,
                    ProductId = behaviorEventDto.Shop_event?.Product_id.ToString(),
                    VariantId = behaviorEventDto.Shop_event?.Product_variant_id.ToString(),
                    InternalProductId = behaviorEventDto.Shop_event?.Product_internal_id,
                    Price = behaviorEventDto.Shop_event?.Price,
                    PriceRange = behaviorEventDto.Shop_event?.Price_range,
                    PluginVersion = behaviorEventDto.Plugin?.Version,
                    PluginName = behaviorEventDto.Plugin?.Name,
                    MerchantType = MerchantTypes.WooCommerce
                };
                
                var success = await integrationService
                    .AddBehaviorEventForProcessingAsync(behaviorEvent)
                    .ConfigureAwait(false);
                
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            var jsonString = JsonConvert.SerializeObject(behaviorEventDto);
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while adding behaviorPage Event for WooCommerce data: {JsonString}", jsonString);
            return Ok();
        }
    }
    
    [HttpPost]
    [Route("v1/events")]
    public async Task<IActionResult> AddBehaviorEventNewAsync(BehaviorEventDto behaviorEventDto)
    {
        try
        {
            // Get the api key from the header
            var apiKey = Request.Headers["X-Api-Key"];
            if (string.IsNullOrEmpty(apiKey))
            {
                logger.ForContext("service_name", GetType().Name).Error("API key is required");
                return BadRequest("API key is required");
            }

            behaviorEventDto.ApiKey = apiKey;
            // Fire and forget the behavior event
            behaviorEventClientService.FireAndForgetPost("woocommerce", new StringContent(JsonSerializer.Serialize(behaviorEventDto),System.Text.Encoding.UTF8, "application/json"));
            
            return Ok();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while adding behavior event for WooCommerce data: {JsonString}", JsonConvert.SerializeObject(behaviorEventDto));
            return StatusCode(StatusCodes.Status500InternalServerError, "Error while adding behavior event for WooCommerce data");
        }
    }

    [HttpPost]
    [Route("ApiKey")]
    public async Task<IActionResult> ApiKeyAsync(WooCommerceApiKey wooCommerceApiKey)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ApiKeyAsync"))
            {
                var success = await _woocommercePluginService
                    .CreateApiKeyAsync(wooCommerceApiKey).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while updating ApiKeyAsync");
            return Ok();
        }
    }

    [HttpPost]
    [Route("WebShopOrderHook")]
    public async Task<IActionResult> WebshopOrderHookAsync(OrderEventDto order)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "WebshopOrderHookAsync"))
            {
                var success = await _woocommercePluginService
                    .WebhookOrderCreateAsync(new List<OrderEventDto> {order}, order.ApiKey).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while updating WebshopOrderHookAsync");
            return Ok();
        }
    }

    [HttpPost]
    [Route("webhooks/ProductDeleted")]
    public async Task<IActionResult> WebhooksProductDeletedAsync(
        WooCommerceWebhookProductDelete wooCommerceWebhookProductDelete)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "WebhooksProductDeletedAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information(
                    $"WebhooksProductDeletedAsync {JsonConvert.SerializeObject(wooCommerceWebhookProductDelete)}");
                return Ok(await _woocommercePluginService.WebhookProductDeleteASync(wooCommerceWebhookProductDelete));
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while updating WebshopOrderHookAsync");
            return Ok();
        }
    }

    // New API Key Validation Endpoint
    [HttpGet]
    [Route("v1/validate-api-key")]
    public async Task<IActionResult> ValidateApiKeyAsync()
    {
        // Fetch Api Key from Header
        var apiKey = Request.Headers["X-Api-Key"];
        if (string.IsNullOrEmpty(apiKey))
        {
            return BadRequest("API key is required");
        }

        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ValidateApiKeyAsync"))
            {
                var success = await _woocommercePluginService
                    .ValidateApiKeyAsync(apiKey).ConfigureAwait(false);

                if (!success)
                {
                    return BadRequest("Invalid API key");
                }

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                  .ForContext("api_key", apiKey)
                  .Error(ex, "Error while validating API key");  
            return StatusCode(StatusCodes.Status500InternalServerError, "Error while validating API key");
        }
    }
}