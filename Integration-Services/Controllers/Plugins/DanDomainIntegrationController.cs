using System.Text.Json;
using Endpoints.Models;
using Integration.Models;
using Integration.Services.Plugins.Integration;
using Marlin_OS_Integration_API.Services.Plugins;
using Marlin_OS_MerchantSync_API.Services.Plugins.DanDomainIntegration;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Dto.DandomainClassic;
using Shared.Models.Merchant;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace Endpoints.Controllers.Integration.Plugins;

[AllowAnonymous]
[PartnerAuthExempt]
[ApiController]
[Route("danDomain")]
public class DanDomainIntegrationController(
    ILogger logger,
    IIntegrationService integrationService,
    IMerchantService merchantService,
    IBehaviorEventClientService behaviorEventClientService)
    : ControllerBase
{
    private readonly DanDomainPluginService _danDomainPluginService = new(logger, integrationService, merchantService);
    private readonly string _secret = "ZJ&5dH+QGrPDfVF&XJ6uhxTnxubegZKS";

    /// <summary>
    /// Adds Page look Tracking elements to RabbitMQ
    /// </summary>
    /// <param name="danDomainBehaviorDto"></param>
    /// <returns>Response</returns>
    [HttpPost]
    [Route("event")]
    [PartnerAuthExempt]
    public async Task<IActionResult> AddBehaviorEventAsync(DanDomainBehaviorDto danDomainBehaviorDto)
    {
        // TODO - Temp Solution To Reduce Resource Usage
        behaviorEventClientService.FireAndForgetPost("dandomain", new StringContent(JsonSerializer.Serialize(danDomainBehaviorDto),System.Text.Encoding.UTF8, "application/json"));
        return Ok();
    }

    [HttpPost]
    [Route("webhooks/OrderCreated")]
    [PartnerAuthExempt]
    public async Task<IActionResult> WebHookOrderCreatedDanDomainAsync()
    {
        var providedHmac = string.Empty;
        var requestBody = "";
        var id = "";
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "WebHookOrderCreatedDanDomainAsync"))
            {
                return Ok();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex,
                    "Error while calling WebHook Endpoint for DanDomain with order ID: {Id}, HMAC: {ProvidedHmac}, FULL: {FULL}",
                    id,
                    providedHmac, requestBody);
            return Ok();
        }
    }

    [HttpPost]
    [Route("webhooks/OrderCreated/{shopIdentifier}")]
    [PartnerAuthExempt]
    public async Task<IActionResult> WebHookOrderCreatedDanDomainAsync(string shopIdentifier)
    {
        return Ok();
    }

    [HttpPost]
    [Route("webhooks/ProductDeleted/{shopIdentifier}")]
    [PartnerAuthExempt]
    public async Task<IActionResult> WebHookProductDeletedDanDomainAsync(string shopIdentifier)
    {
        var providedHmac = string.Empty;
        var providedXShopDomain = string.Empty;
        var requestBody = "";
        var id = "";
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "WebHookOrderCreatedDanDomainAsync"))
            {
                requestBody = await new StreamReader(Request.Body).ReadToEndAsync();
                id = JsonSerializer.Deserialize<DanDomainProductDelteHookDto>(requestBody)?.id ?? "";
                providedHmac = Request.Headers["X-HMAC-SHA256"];
                providedXShopDomain = Request.Headers["X-Shop-Domain"];
                logger.ForContext("service_name", GetType().Name)
                    .Error(
                        "Dandomain product hook delteed Url: {Url}, ShopIdentifier {ShopIdentifier} HMAC: {ProvidedHmac}, Id: {Id} FULL: {FULL}",
                        providedXShopDomain, shopIdentifier, providedHmac, id, requestBody);

                //await _danDomainPluginService.WebhookProductDeleteAsync(requestBody, providedHMAC, id);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex,
                    "Error while calling WebHook Endpoint for DanDomain with order ID: {Id}, HMAC: {ProvidedHmac}, FULL: {FULL}",
                    id,
                    providedHmac, requestBody);
            return Ok();
        }
    }
}