using System.Text.Json;
using System.Web;
using Integration.Models;
using Integration.Services.Plugins.Integration;
using Marlin_OS_Integration_API.Services.Plugins;
using Marlin_OS_Integration_API.Services.Plugins.DanDomainIntegration;
using Marlin_OS_MerchantSync_API.Models.Plugins.DanDomainClassic;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Partner_Services.Services.General;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Dto.DandomainClassic;
using Shared.Models.Merchant;
using Webshop_Service.Services.DanDomain;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace Endpoints.Controllers.Integration.Plugins;

[AllowAnonymous]
[PartnerAuthExempt]
[ApiController]
[Route("danDomainClassic")]
public class DanDomainClassicIntegrationController(
    ILogger logger,
    IIntegrationService integrationService,
    IMerchantService merchantService,
    IDanDomainClassicService danDomainClassicService,
    IBehaviorEventClientService behaviorEventClientService,
    IPartnerService partnerService)
    : ControllerBase
{
    private readonly DanDomainClassicPluginService _danDomainClassicPluginService = new(logger, integrationService, merchantService, danDomainClassicService, partnerService);

    /// <summary>
    /// Adds Page look Tracking elements to RabbitMQ
    /// </summary>
    /// <param name="danDomainBehaviorDto"></param>
    /// <returns>Response</returns>
    [HttpPost]
    [Route("event")]
    [PartnerAuthExempt]
    public async Task<IActionResult> AddBehaviorEventAsync(DanDomainBehaviorDto danDomainBehaviorDto)
    {
        
        // TODO - Temp Solution To Reduce Resource Usage
        behaviorEventClientService.FireAndForgetPost("dandomain", new StringContent(JsonSerializer.Serialize(danDomainBehaviorDto),System.Text.Encoding.UTF8, "application/json"));
        return Ok();
    }

    [HttpGet]
    [Route("install")]
    [PartnerAuthExempt]
    public async Task<IActionResult> InstallDanDomainClassic(string installEndpoint, string syncApiCredentialsEndpoint,
        Guid apiKey, string shophostname, int appId, int shopIdentifier)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "InstallDanDomainClassic"))
            {
                var result = await _danDomainClassicPluginService.UpsertDanDomainClassicAsync(installEndpoint,
                    syncApiCredentialsEndpoint,
                    apiKey, shophostname, appId, shopIdentifier);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error while calling Install App Endpoint for DanDomain");
            return Ok();
        }
    }

    [HttpGet]
    [Route("uninstall")]
    [PartnerAuthExempt]
    public async Task<IActionResult> UninstallDanDomainClassic(int shopIdentifier)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UninstallDanDomainClassic"))
            {
                var result = await _danDomainClassicPluginService.UninstallDanDomainClassicAsync(shopIdentifier);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error while calling Uninstall App Endpoint for DanDomain for shopIdentifier: {shopIdentifier}");
            return Ok();
        }
    }

    [HttpPost]
    [Route("webhook")]
    [PartnerAuthExempt]
    public async Task<IActionResult> WebHookDanDomainClassic()
    {
        try
        {
            var json = await ReadRawBodyAsync();

            List<DanDomainClassicProductHook?>? danDomainClassicProductHook = new List<DanDomainClassicProductHook?>();
            try
            {
                danDomainClassicProductHook = JsonSerializer.Deserialize<List<DanDomainClassicProductHook?>>(json);
            }
            catch (Exception ex)
            {
                //Json failed should be orders
                if (json.Contains("<EMAIL>"))
                {
                    logger.ForContext("service_name", GetType().Name)
                        .Error(ex, "DandomainClassic webhook json: " + json);
                }
            }

            if (danDomainClassicProductHook != null && danDomainClassicProductHook.Count > 0)
            {
                var data = danDomainClassicProductHook.First();
                if (data != null && data?.newValues == null)
                {
                    await _danDomainClassicPluginService.WebhookProduct(data);
                }
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error while calling WebHook Endpoint for DanDomainClassic");
            return Ok();
        }
        
        return Ok();
    }

    /// <summary>
    /// Reads the request stream into a string, while simultaneously ensuring request handlers further down the pipeline can still use the request stream without it being disposed or stuck at the end.
    /// </summary>
    private async Task<string> ReadRawBodyAsync()
    {
        // Request streams by default can only be read once. This method enables multiple reads for this request.
        Request.EnableBuffering();

        var stream = Request.Body;
        string rawBody;

        // While the stream can now be rewound, using streamreader will automatically dispose it which we don't want.
        // Instead we need to copy the stream to another temporary stream that can be safely disposed without disposing the request.
        using (var buffer = new MemoryStream())
        {
            await stream.CopyToAsync(buffer);

            // Move the buffer position back to 0. If we don't do this, 
            // the streamreader will just read an empty string.
            buffer.Position = 0;

            using (var reader = new StreamReader(buffer))
            {
                rawBody = await reader.ReadToEndAsync();
            }
        }

        if (stream.CanSeek)
        {
            stream.Position = 0;
        }

        return rawBody;
    }
}