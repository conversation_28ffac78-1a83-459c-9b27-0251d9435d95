namespace Integration.Models.Integrations;

public class AdtractionFeed
{
    public string ProductId { get; set; }        // Unique identifier for the product
    public string ProductName { get; set; }      // Name of the product
    public string Description { get; set; }      // Product description
    public decimal Price { get; set; }           // Product price
    public decimal DiscountedPrice { get; set; } // Discounted price (if applicable)
    public string Currency { get; set; }         // Currency of the price (e.g. USD, EUR)
    public string MerchantId { get; set; }       // ID of the merchant providing the feed
    public string Category { get; set; }         // Product category
    public string ImageUrl { get; set; }         // URL for the product image
    public string ProductUrl { get; set; }       // URL to the product page on the merchant's site
    public DateTime LastUpdated { get; set; }    // When the product data was last updated
    public bool IsAvailable { get; set; }        // Availability status (true/false)

    // Optional properties depending on the feed data
    public string Brand { get; set; }            // Product brand name
    public string EAN { get; set; }              // European Article Number (if applicable)
    public string SKU { get; set; }              // Stock Keeping Unit (if applicable)
    public int StockQuantity { get; set; }       // Available stock quantity
}

