namespace Integration.Models;

public class BehaviorEvent
{
    // General
    public string? ApiKey { get; set; }
    public string? Url { get; set; }
    public string? Name { get; set; }
    public string? EventType { get; set; }
    public string? MerchantId { get; set; }
    public string? PluginVersion { get; set; }
    public string? PluginName { get; set; }
    public string? CampaignId { get; set; }
    public string? ShopIdentifier { get; set; }
    public string MerchantType { get; set; }
    public DateTime EventDate { get; set; }
    
    // User
    public string? DeviceName { get; set; }
    public string? Version { get; set; }
    public string? Cookie { get; set; }
    public string? Cookie2 { get; set; }
    public string? Email { get; set; }
    public string? Email2 { get; set; }
    public string? Session { get; set; }
    public string? Ip { get; set; }
    public string? UserAgent { get; set; }
    public string? Original { get; set; }
    
    // Product
    public string? ProductId { get; set; }
    public string? VariantId { get; set; }
    public string? ProductSku { get; set; }
    public decimal? Price { get; set; }
    public string? PriceRange { get; set; }
    public string? InternalProductId { get; set; }
}