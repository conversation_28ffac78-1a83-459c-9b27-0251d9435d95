namespace Integration.Models.Shopify;

// Root myDeserializedClass = JsonConvert.DeserializeObject<List<Root>>(myJsonResponse);
public class CustomAttributeVariant
{
    public string attribute_code { get; set; }
    public object value { get; set; }
}

public class MagentoVariant
{
    public int id { get; set; }
    public string sku { get; set; }
    public string name { get; set; }
    public int attribute_set_id { get; set; }
    public double price { get; set; }
    public int status { get; set; }
    public int visibility { get; set; }
    public string type_id { get; set; }
    public string created_at { get; set; }
    public string updated_at { get; set; }
    public int weight { get; set; }
    public List<object> product_links { get; set; }
    public List<object> tier_prices { get; set; }
    public List<CustomAttribute> custom_attributes { get; set; }
}