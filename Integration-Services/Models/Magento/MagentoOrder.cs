namespace Integration.Models.Shopify;

// Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
public class AddressMagentoOrder
{
    public string address_type { get; set; }
    public string city { get; set; }
    public string country_id { get; set; }
    public int customer_address_id { get; set; }
    public string email { get; set; }
    public int entity_id { get; set; }
    public string firstname { get; set; }
    public string lastname { get; set; }
    public int parent_id { get; set; }
    public string postcode { get; set; }
    public string region { get; set; }
    public string region_code { get; set; }
    public int region_id { get; set; }
    public List<string> street { get; set; }
    public string telephone { get; set; }
}

public class AppliedTaxisMagentoOrder
{
    public string code { get; set; }
    public string title { get; set; }
    public int percent { get; set; }
    public double amount { get; set; }
    public double base_amount { get; set; }
}

public class BillingAddressMagentoOrder
{
    public string address_type { get; set; }
    public string city { get; set; }
    public string country_id { get; set; }
    public string email { get; set; }
    public int entity_id { get; set; }
    public string firstname { get; set; }
    public string lastname { get; set; }
    public int parent_id { get; set; }
    public string postcode { get; set; }
    public string region { get; set; }
    public string region_code { get; set; }
    public int region_id { get; set; }
    public List<string> street { get; set; }
    public string telephone { get; set; }
    public int? customer_address_id { get; set; }
}

public class ConfigurableItemOptionMagentoOrder
{
    public string option_id { get; set; }
    public int option_value { get; set; }
}

public class ExtensionAttributesMagentoOrder
{
    public List<ConfigurableItemOptionMagentoOrder> configurable_item_options { get; set; }
    public List<ShippingAssignmentMagentoOrder> shipping_assignments { get; set; }
    public List<PaymentAdditionalInfoMagentoOrder> payment_additional_info { get; set; }
    public List<AppliedTaxisMagentoOrder> applied_taxes { get; set; }
    public List<ItemAppliedTaxisMagentoOrder> item_applied_taxes { get; set; }
    public bool converting_from_quote { get; set; }
}

public class FilterMagentoOrder
{
    public string field { get; set; }
    public string value { get; set; }
    public string condition_type { get; set; }
}

public class FilterGroupMagentoOrder
{
    public List<FilterMagentoOrder> filters { get; set; }
}

public class ItemMagentoOrder
{
    public string base_currency_code { get; set; }
    public double base_discount_amount { get; set; }
    public double base_grand_total { get; set; }
    public int base_discount_tax_compensation_amount { get; set; }
    public int base_shipping_amount { get; set; }
    public int base_shipping_discount_amount { get; set; }
    public int base_shipping_discount_tax_compensation_amnt { get; set; }
    public int base_shipping_incl_tax { get; set; }
    public int base_shipping_tax_amount { get; set; }
    public double base_subtotal { get; set; }
    public double base_subtotal_incl_tax { get; set; }
    public double base_tax_amount { get; set; }
    public double base_total_due { get; set; }
    public int base_to_global_rate { get; set; }
    public int base_to_order_rate { get; set; }
    public int billing_address_id { get; set; }
    public string coupon_code { get; set; }
    public string created_at { get; set; }
    public string customer_email { get; set; }
    public string customer_firstname { get; set; }
    public int customer_group_id { get; set; }
    public int customer_id { get; set; }
    public int customer_is_guest { get; set; }
    public string customer_lastname { get; set; }
    public int customer_note_notify { get; set; }
    public double discount_amount { get; set; }
    public int email_sent { get; set; }
    public int entity_id { get; set; }
    public string global_currency_code { get; set; }
    public double grand_total { get; set; }
    public int discount_tax_compensation_amount { get; set; }
    public string increment_id { get; set; }
    public int is_virtual { get; set; }
    public string order_currency_code { get; set; }
    public string protect_code { get; set; }
    public int quote_id { get; set; }
    public int shipping_amount { get; set; }
    public string shipping_description { get; set; }
    public int shipping_discount_amount { get; set; }
    public int shipping_discount_tax_compensation_amount { get; set; }
    public int shipping_incl_tax { get; set; }
    public int shipping_tax_amount { get; set; }
    public string state { get; set; }
    public string status { get; set; }
    public string store_currency_code { get; set; }
    public int store_id { get; set; }
    public string store_name { get; set; }
    public int store_to_base_rate { get; set; }
    public int store_to_order_rate { get; set; }
    public double subtotal { get; set; }
    public double subtotal_incl_tax { get; set; }
    public double tax_amount { get; set; }
    public double total_due { get; set; }
    public int total_item_count { get; set; }
    public int total_qty_ordered { get; set; }
    public string updated_at { get; set; }
    public int weight { get; set; }
    public List<ItemMagentoOrder> items { get; set; }
    public BillingAddressMagentoOrder billing_address { get; set; }
    public PaymentMagentoOrder payment { get; set; }
    public List<StatusHistoryMagentoOrder> status_histories { get; set; }
    public ExtensionAttributesMagentoOrder extension_attributes { get; set; }
    public string remote_ip { get; set; }
    public string x_forwarded_for { get; set; }
    public int amount_refunded { get; set; }
    public int base_amount_refunded { get; set; }
    public int base_discount_invoiced { get; set; }
    public double base_original_price { get; set; }
    public double base_price { get; set; }
    public double base_price_incl_tax { get; set; }
    public int base_row_invoiced { get; set; }
    public double base_row_total { get; set; }
    public double base_row_total_incl_tax { get; set; }
    public int base_tax_invoiced { get; set; }
    public int discount_invoiced { get; set; }
    public int discount_percent { get; set; }
    public int free_shipping { get; set; }
    public int is_qty_decimal { get; set; }
    public int item_id { get; set; }
    public string name { get; set; }
    public int no_discount { get; set; }
    public int order_id { get; set; }
    public double original_price { get; set; }
    public double price { get; set; }
    public double price_incl_tax { get; set; }
    public int product_id { get; set; }
    public string product_type { get; set; }
    public int qty_canceled { get; set; }
    public int qty_invoiced { get; set; }
    public int qty_ordered { get; set; }
    public int qty_refunded { get; set; }
    public int qty_shipped { get; set; }
    public int quote_item_id { get; set; }
    public int row_invoiced { get; set; }
    public double row_total { get; set; }
    public double row_total_incl_tax { get; set; }
    public int row_weight { get; set; }
    public string sku { get; set; }
    public int tax_invoiced { get; set; }
    public int tax_percent { get; set; }
    public ProductOptionMagentoOrder product_option { get; set; }
    public int? parent_item_id { get; set; }
    public ParentItemMagentoOrder parent_item { get; set; }
}

public class ItemAppliedTaxisMagentoOrder
{
    public string type { get; set; }
    public int item_id { get; set; }
    public List<AppliedTaxisMagentoOrder> applied_taxes { get; set; }
}

public class ParentItemMagentoOrder
{
    public int amount_refunded { get; set; }
    public int base_amount_refunded { get; set; }
    public double base_discount_amount { get; set; }
    public int base_discount_invoiced { get; set; }
    public int base_discount_tax_compensation_amount { get; set; }
    public double base_original_price { get; set; }
    public double base_price { get; set; }
    public double base_price_incl_tax { get; set; }
    public int base_row_invoiced { get; set; }
    public double base_row_total { get; set; }
    public double base_row_total_incl_tax { get; set; }
    public double base_tax_amount { get; set; }
    public int base_tax_invoiced { get; set; }
    public string created_at { get; set; }
    public double discount_amount { get; set; }
    public int discount_invoiced { get; set; }
    public int discount_percent { get; set; }
    public int free_shipping { get; set; }
    public int discount_tax_compensation_amount { get; set; }
    public int is_qty_decimal { get; set; }
    public int is_virtual { get; set; }
    public int item_id { get; set; }
    public string name { get; set; }
    public int no_discount { get; set; }
    public int order_id { get; set; }
    public double original_price { get; set; }
    public double price { get; set; }
    public double price_incl_tax { get; set; }
    public int product_id { get; set; }
    public string product_type { get; set; }
    public int qty_canceled { get; set; }
    public int qty_invoiced { get; set; }
    public int qty_ordered { get; set; }
    public int qty_refunded { get; set; }
    public int qty_shipped { get; set; }
    public int quote_item_id { get; set; }
    public int row_invoiced { get; set; }
    public double row_total { get; set; }
    public double row_total_incl_tax { get; set; }
    public int row_weight { get; set; }
    public string sku { get; set; }
    public int store_id { get; set; }
    public double tax_amount { get; set; }
    public int tax_invoiced { get; set; }
    public int tax_percent { get; set; }
    public string updated_at { get; set; }
    public int weight { get; set; }
    public ProductOptionMagentoOrder product_option { get; set; }
}

public class PaymentMagentoOrder
{
    public object account_status { get; set; }
    public List<string> additional_information { get; set; }
    public double amount_ordered { get; set; }
    public double base_amount_ordered { get; set; }
    public int base_shipping_amount { get; set; }
    public string cc_exp_year { get; set; }
    public object cc_last4 { get; set; }
    public string cc_ss_start_month { get; set; }
    public string cc_ss_start_year { get; set; }
    public int entity_id { get; set; }
    public string method { get; set; }
    public int parent_id { get; set; }
    public int shipping_amount { get; set; }
}

public class PaymentAdditionalInfoMagentoOrder
{
    public string key { get; set; }
    public string value { get; set; }
}

public class ProductOptionMagentoOrder
{
    public ExtensionAttributesMagentoOrder extension_attributes { get; set; }
}

public class MagentoOrder
{
    public List<ItemMagentoOrder> items { get; set; }
    public SearchCriteriaMagentoOrder search_criteria { get; set; }
    public int total_count { get; set; }
}

public class SearchCriteriaMagentoOrder
{
    public List<FilterGroupMagentoOrder> filter_groups { get; set; }
    public int page_size { get; set; }
    public int current_page { get; set; }
}

public class ShippingMagentoOrder
{
    public AddressMagentoOrder address { get; set; }
    public string method { get; set; }
    public TotalMagentoOrder total { get; set; }
}

public class ShippingAssignmentMagentoOrder
{
    public ShippingMagentoOrder shipping { get; set; }
    public List<ItemMagentoOrder> items { get; set; }
}

public class StatusHistoryMagentoOrder
{
    public string comment { get; set; }
    public string created_at { get; set; }
    public int entity_id { get; set; }
    public string entity_name { get; set; }
    public int is_customer_notified { get; set; }
    public int is_visible_on_front { get; set; }
    public int parent_id { get; set; }
    public string status { get; set; }
}

public class TotalMagentoOrder
{
    public int base_shipping_amount { get; set; }
    public int base_shipping_discount_amount { get; set; }
    public int base_shipping_discount_tax_compensation_amnt { get; set; }
    public int base_shipping_incl_tax { get; set; }
    public int base_shipping_tax_amount { get; set; }
    public int shipping_amount { get; set; }
    public int shipping_discount_amount { get; set; }
    public int shipping_discount_tax_compensation_amount { get; set; }
    public int shipping_incl_tax { get; set; }
    public int shipping_tax_amount { get; set; }
}