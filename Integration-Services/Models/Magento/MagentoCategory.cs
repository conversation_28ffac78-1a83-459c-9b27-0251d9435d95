namespace Integration.Models.Shopify;

// Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
public class ChildrenDatumMagento
{
    public int id { get; set; }
    public int parent_id { get; set; }
    public string name { get; set; }
    public bool is_active { get; set; }
    public int position { get; set; }
    public int level { get; set; }
    public int product_count { get; set; }
    public List<object> children_data { get; set; }
}

public class MagentoCategory
{
    public int id { get; set; }
    public int parent_id { get; set; }
    public string name { get; set; }
    public bool is_active { get; set; }
    public int position { get; set; }
    public int level { get; set; }
    public int product_count { get; set; }
    public List<ChildrenDatumMagento> children_data { get; set; }
}