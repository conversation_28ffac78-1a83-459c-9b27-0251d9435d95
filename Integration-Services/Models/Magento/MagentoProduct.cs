namespace Integration.Models.Shopify;

// Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);
public class CategoryLink
{
    public int position { get; set; }
    public string category_id { get; set; }
}

public class ConfigurableProductOption
{
    public int id { get; set; }
    public string attribute_id { get; set; }
    public string label { get; set; }
    public int position { get; set; }
    public List<Value> values { get; set; }
    public int product_id { get; set; }
}

public class CustomAttribute
{
    public string attribute_code { get; set; }
    public object value { get; set; }
}

public class ExtensionAttributes
{
    public List<int> website_ids { get; set; }
    public List<CategoryLink> category_links { get; set; }
    public List<ConfigurableProductOption> configurable_product_options { get; set; }
    public List<int> configurable_product_links { get; set; }
}

public class Filter
{
    public string field { get; set; }
    public string value { get; set; }
    public string condition_type { get; set; }
}

public class FilterGroup
{
    public List<Filter> filters { get; set; }
}

public class Item
{
    public int id { get; set; }
    public string sku { get; set; }
    public string name { get; set; }
    public int attribute_set_id { get; set; }
    public double price { get; set; }
    public int status { get; set; }
    public int visibility { get; set; }
    public string type_id { get; set; }
    public string created_at { get; set; }
    public string updated_at { get; set; }
    public int weight { get; set; }
    public ExtensionAttributes extension_attributes { get; set; }
    public List<object> product_links { get; set; }
    public List<object> options { get; set; }
    public List<MediaGalleryEntry> media_gallery_entries { get; set; }
    public List<object> tier_prices { get; set; }
    public List<CustomAttribute> custom_attributes { get; set; }
}

public class MediaGalleryEntry
{
    public int id { get; set; }
    public string media_type { get; set; }
    public object label { get; set; }
    public int position { get; set; }
    public bool disabled { get; set; }
    public List<string> types { get; set; }
    public string file { get; set; }
}

public class MagentoProduct
{
    public List<Item> items { get; set; }
    public SearchCriteria search_criteria { get; set; }
    public int total_count { get; set; }
}

public class SearchCriteria
{
    public List<FilterGroup> filter_groups { get; set; }
    public int page_size { get; set; }
    public int current_page { get; set; }
}

public class Value
{
    public int value_index { get; set; }
}