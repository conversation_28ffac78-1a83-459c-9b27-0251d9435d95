namespace Integration.Models.WooCommerce;

// Root myDeserializedClass = JsonConvert.DeserializeObject<List<Root>>(myJsonResponse);
public class AttributeVariant
{
    public int id { get; set; }
    public string name { get; set; }
    public string option { get; set; }
}

public class CollectionVariant
{
    public string href { get; set; }
}

public class DimensionsVariant
{
    public string length { get; set; }
    public string width { get; set; }
    public string height { get; set; }
}

public class ImageVariant
{
    public int id { get; set; }
    public DateTime date_created { get; set; }
    public DateTime date_created_gmt { get; set; }
    public DateTime date_modified { get; set; }
    public DateTime date_modified_gmt { get; set; }
    public string src { get; set; }
    public string name { get; set; }
    public string alt { get; set; }
}

public class LinksVariant
{
    public List<SelfVariant> self { get; set; }
    public List<CollectionVariant> collection { get; set; }
    public List<UpVariant> up { get; set; }
}

public class WooCommerceVariant
{
    public int id { get; set; }
    public DateTime date_created { get; set; }
    public DateTime date_created_gmt { get; set; }
    public DateTime date_modified { get; set; }
    public DateTime date_modified_gmt { get; set; }
    public string description { get; set; }
    public string permalink { get; set; }
    public string sku { get; set; }
    public object price { get; set; }
    public object regular_price { get; set; }
    public object sale_price { get; set; }
    public object date_on_sale_from { get; set; }
    public object date_on_sale_from_gmt { get; set; }
    public object date_on_sale_to { get; set; }
    public object date_on_sale_to_gmt { get; set; }
    public bool on_sale { get; set; }
    public string status { get; set; }
    public bool purchasable { get; set; }
    public bool @virtual { get; set; }
    public bool downloadable { get; set; }
    public List<object> downloads { get; set; }
    public int download_limit { get; set; }
    public int download_expiry { get; set; }
    public string tax_status { get; set; }

    public string tax_class { get; set; }

    //public bool manage_stock { get; set; }
    public int? stock_quantity { get; set; }
    public string stock_status { get; set; }
    public string backorders { get; set; }
    public bool backorders_allowed { get; set; }
    public bool backordered { get; set; }
    public object low_stock_amount { get; set; }
    public string weight { get; set; }
    public DimensionsVariant dimensions { get; set; }
    public string shipping_class { get; set; }
    public int shipping_class_id { get; set; }
    public Image? image { get; set; }
    public List<AttributeVariant> attributes { get; set; }
    public int menu_order { get; set; }
    public List<object> meta_data { get; set; }
    public Links _links { get; set; }
}

public class SelfVariant
{
    public string href { get; set; }
}

public class UpVariant
{
    public string href { get; set; }
}