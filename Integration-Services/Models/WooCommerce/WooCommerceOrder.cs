using System.Text.Json.Serialization;
using Marlin_OS_Integration_API.Converters.Helpers;

namespace Integration.Models.WooCommerce;

// Root myDeserializedClass = JsonConvert.DeserializeObject<List<Root>>(myJsonResponse);
public class BillingOrder
{
    public string first_name { get; set; }
    public string last_name { get; set; }
    public string company { get; set; }
    public string address_1 { get; set; }
    public string address_2 { get; set; }
    public string city { get; set; }
    public string state { get; set; }
    public string postcode { get; set; }
    public string country { get; set; }
    public string email { get; set; }
    public string phone { get; set; }
}

public class CollectionOrder
{
    public string href { get; set; }
}

public class CouponLineOrder
{
    public long id { get; set; }
    public string code { get; set; }
    public string discount { get; set; }
    public string discount_tax { get; set; }
    public List<Metadataorder> meta_data { get; set; }

    public string discount_type { get; set; }

    //public int nominal_amount { get; set; }
    public bool free_shipping { get; set; }
}

public class Metadataorder
{
    public long id { get; set; }
    public string key { get; set; }
    public ValueOrder valueOrder { get; set; }

    public string display_key { get; set; }
    //public DisplayValueOrder display_value { get; set; }
}

public class CustomerOrder
{
    public string href { get; set; }
}

public class DateCreatedOrder
{
    public string date { get; set; }
    public int timezone_type { get; set; }
    public string timezone { get; set; }
}

public class DateModifiedOrder
{
    public string date { get; set; }
    public int timezone_type { get; set; }
    public string timezone { get; set; }
}

public class DisplayValueOrder
{
    public long id { get; set; }
    public string code { get; set; }
    public string amount { get; set; }
    public string status { get; set; }
    public DateCreatedOrder date_created { get; set; }
    public DateModifiedOrder date_modified { get; set; }
    public object date_expires { get; set; }
    public string discount_type { get; set; }
    public string description { get; set; }
    public int usage_count { get; set; }
    public bool individual_use { get; set; }
    public List<object> product_ids { get; set; }
    public List<object> excluded_product_ids { get; set; }
    public int usage_limit { get; set; }
    public int usage_limit_per_user { get; set; }
    public object limit_usage_to_x_items { get; set; }
    public bool free_shipping { get; set; }
    public List<object> product_categories { get; set; }
    public List<object> excluded_product_categories { get; set; }
    public bool exclude_sale_items { get; set; }
    public string minimum_amount { get; set; }
    public string maximum_amount { get; set; }
    public List<object> email_restrictions { get; set; }
    public bool @virtual { get; set; }
    public List<object> meta_data { get; set; }
}

public class ImageOrder
{
    public object id { get; set; }
    public string src { get; set; }
}

public class LineItemOrder
{
    public long id { get; set; }
    public string name { get; set; }
    public long product_id { get; set; }
    public long variation_id { get; set; }
    public decimal quantity { get; set; }
    public string tax_class { get; set; }
    public string subtotal { get; set; }
    public string subtotal_tax { get; set; }
    public string total { get; set; }

    public string total_tax { get; set; }

    //public List<TaxisOrder> taxes { get; set; }
    //public List<MetaDataOrder> meta_data { get; set; }
    public string sku { get; set; }

    public double price { get; set; }
    //public ImageOrder image { get; set; }
    //public string parent_name { get; set; }
}

public class LinksOrder
{
    public List<SelfOrder> self { get; set; }
    public List<CollectionOrder> collection { get; set; }
    public List<CustomerOrder> customer { get; set; }
}

[JsonConverter(typeof(MetaDataOrderConverter))]
public class MetaDataOrder
{
    public long id { get; set; }
    public string key { get; set; }
    public string value { get; set; }
    public string display_key { get; set; }
    public string display_value { get; set; }
}

public class WooCommerceOrder
{
    public long id { get; set; }
    public int parent_id { get; set; }
    public string status { get; set; }
    public string currency { get; set; }
    public string version { get; set; }
    public bool prices_include_tax { get; set; }
    public DateTime date_created { get; set; }
    public DateTime date_modified { get; set; }
    public string discount_total { get; set; }
    public string discount_tax { get; set; }
    public string shipping_total { get; set; }
    public string shipping_tax { get; set; }
    public string cart_tax { get; set; }
    public string total { get; set; }
    public string total_tax { get; set; }
    public int customer_id { get; set; }
    public string order_key { get; set; }
    public BillingOrder billing { get; set; }
    public ShippingOrder shipping { get; set; }
    public string payment_method { get; set; }
    public string payment_method_title { get; set; }
    public string transaction_id { get; set; }
    public string customer_ip_address { get; set; }
    public string customer_user_agent { get; set; }
    public string created_via { get; set; }
    public string customer_note { get; set; }
    public DateTime? date_completed { get; set; }
    public DateTime? date_paid { get; set; }
    public string cart_hash { get; set; }

    public string number { get; set; }

    //public List<MetaData> meta_data { get; set; }
    public List<LineItemOrder> line_items { get; set; }
    public List<TaxLineOrder> tax_lines { get; set; }

    public List<ShippingLineOrder> shipping_lines { get; set; }

    //public List<object> fee_lines { get; set; }
    public List<CouponLineOrder> coupon_lines { get; set; }
    public List<WooCommerceRefunds> refunds { get; set; }
    public string payment_url { get; set; }
    public bool is_editable { get; set; }
    public bool needs_payment { get; set; }
    public bool needs_processing { get; set; }
    public DateTime date_created_gmt { get; set; }
    public DateTime date_modified_gmt { get; set; }
    public DateTime? date_completed_gmt { get; set; }
    public DateTime? date_paid_gmt { get; set; }
    public string currency_symbol { get; set; }
    public Links _links { get; set; }


    /*public long id { get; set; }
    public long parent_id { get; set; }
    public string status { get; set; }
    public string currency { get; set; }
    public string version { get; set; }
    public bool prices_include_tax { get; set; }
    public DateTime date_created { get; set; }
    public DateTime date_modified { get; set; }
    public string discount_total { get; set; }
    public string discount_tax { get; set; }
    public string shipping_total { get; set; }
    public string shipping_tax { get; set; }
    public string cart_tax { get; set; }
    public string total { get; set; }
    public string total_tax { get; set; }
    public int customer_id { get; set; }
    public string order_key { get; set; }
    public BillingOrder billing { get; set; }
    public ShippingOrder shipping { get; set; }
    public string payment_method { get; set; }
    public string payment_method_title { get; set; }
    public string transaction_id { get; set; }
    public string customer_ip_address { get; set; }
    public string customer_user_agent { get; set; }
    public string created_via { get; set; }
    public string customer_note { get; set; }
    public DateTime? date_completed { get; set; }
    public DateTime? date_paid { get; set; }
    public string cart_hash { get; set; }
    public string number { get; set; }
    public List<LineItemOrder> line_items { get; set; }
    public List<TaxLineOrder> tax_lines { get; set; }
    public List<ShippingLineOrder> shipping_lines { get; set; }
    public List<object> fee_lines { get; set; }
    public List<CouponLineOrder> coupon_lines { get; set; }*/
    /*public List<object> refunds { get; set; }
    public string payment_url { get; set; }
    public bool is_editable { get; set; }
    public bool needs_payment { get; set; }
    public bool needs_processing { get; set; }
    public DateTime date_created_gmt { get; set; }
    public DateTime date_modified_gmt { get; set; }
    public DateTime? date_completed_gmt { get; set; }
    public DateTime? date_paid_gmt { get; set; }
    public string currency_symbol { get; set; }
    public Links _links { get; set; }*/
}

public class SelfOrder
{
    public string href { get; set; }
}

public class ShippingOrder
{
    public string first_name { get; set; }
    public string last_name { get; set; }
    public string company { get; set; }
    public string address_1 { get; set; }
    public string address_2 { get; set; }
    public string city { get; set; }
    public string state { get; set; }
    public string postcode { get; set; }
    public string country { get; set; }
    public string phone { get; set; }
}

public class ShippingLineOrder
{
    public long id { get; set; }
    public string method_title { get; set; }
    public string method_id { get; set; }
    public string instance_id { get; set; }
    public string total { get; set; }
    public string total_tax { get; set; }
    public List<TaxisOrder> taxes { get; set; }
    public List<MetaDataOrder> meta_data { get; set; }
}

public class TaxisOrder
{
    public long id { get; set; }
    public string total { get; set; }
    public string subtotal { get; set; }
}

public class TaxLineOrder
{
    public long id { get; set; }
    public string rate_code { get; set; }
    public long rate_id { get; set; }
    public string label { get; set; }
    public bool compound { get; set; }
    public string tax_total { get; set; }
    public string shipping_tax_total { get; set; }
    public int rate_percent { get; set; }
    public List<object> meta_data { get; set; }
}

public class ValueOrder
{
    public long id { get; set; }
    public string code { get; set; }
    public string amount { get; set; }
    public string status { get; set; }
    public DateCreatedOrder date_created { get; set; }
    public DateModifiedOrder date_modified { get; set; }
    public object date_expires { get; set; }
    public string discount_type { get; set; }
    public string description { get; set; }
    public int usage_count { get; set; }
    public bool individual_use { get; set; }
    public List<object> product_ids { get; set; }
    public List<object> excluded_product_ids { get; set; }
    public int usage_limit { get; set; }
    public int usage_limit_per_user { get; set; }
    public object limit_usage_to_x_items { get; set; }
    public bool free_shipping { get; set; }
    public List<object> product_categories { get; set; }
    public List<object> excluded_product_categories { get; set; }
    public bool exclude_sale_items { get; set; }
    public string minimum_amount { get; set; }
    public string maximum_amount { get; set; }
    public List<object> email_restrictions { get; set; }
    public bool @virtual { get; set; }
    public List<object> meta_data { get; set; }
}

public class WooCommerceRefunds
{
    //public string id { get; set; }
    public string reason { get; set; }
    public string total { get; set; }
}