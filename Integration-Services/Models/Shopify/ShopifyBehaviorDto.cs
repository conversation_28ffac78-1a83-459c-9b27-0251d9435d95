namespace Integration.Models.Shopify;

public class ShopifyBehaviorDto
{
    public string Url { get; set; }
    public string? DeviceName { get; set; }
    public string? Name { get; set; }
    public string? Version { get; set; }
    public string? ViaAds { get; set; }
    public string? ViaAds2 { get; set; }
    public string? Email { get; set; }
    public string? Email2 { get; set; }
    public string? EventType { get; set; }
    public string? Session { get; set; }
    public string? Ip { get; set; }
    public string? ProductId { get; set; }
    public string? VariantId { get; set; }
    public string? Sku { get; set; }
    public string? UserAgent { get; set; }
    public string? WebShopId { get; set; }
    public string? PluginVersion { get; set; }
}