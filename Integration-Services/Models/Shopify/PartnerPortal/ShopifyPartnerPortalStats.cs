namespace Integration.Models.Shopify;

public class ShopifyPartnerPortalStats
{
    public ShopifyPartnerPortalStats()
    {
        /*EmailStats = new EmailStatsDto();
        PhoneStats = new PhoneStatsDto();*/
        /*CustomerOrderInfos = new List<CustomerOrderInfoDto>();
        OrdersZipCode = new List<OrdersZipCodeDto>();*/
    }

    /*public int TotalOrders { get; set; }
    public int OrdersWithPhoneNumber { get; set; }
    public int OrdersWithSamePhoneNumber { get; set; }
    public int OrdersWithEmailAndPhoneNumber { get; set; }
    public int OrdersWithSameEmailAndPhoneNumber { get; set; }
    public double PercentageOfOrdersWithPhoneNumber { get; set; }
    public double PercentageOfOrdersWithSamePhoneNumber { get; set; }
    public double PercentageOfOrdersWithEmailAndPhoneNumber { get; set; }
    public double PercentageOfOrdersWithSameEmailAndPhoneNumber { get; set; }*/

    /*public EmailStatsDto EmailStats { get; set; }
    public PhoneStatsDto PhoneStats { get; set; }*/
    /*public List<OrdersZipCodeDto> OrdersZipCode { get; set; }

    public List<CustomerOrderInfoDto> CustomerOrderInfos { get; set; }*/
    
    public int RepeatEmails { get; set; }
    public int RepeatPhones { get; set; }
}