using System.Text.Json.Serialization;

namespace Integration.Models.Shopify;

public class ShopifyCustomerDataRetrieveDto
{
    [JsonPropertyName("shop_id")] public int ShopId { get; set; }

    [JsonPropertyName("shop_domain")] public string ShopDomain { get; set; }

    [JsonPropertyName("orders_requested")] public List<int> OrdersRequested { get; set; }

    [JsonPropertyName("customer")] public ShopifyCustomerObjectDto Customer { get; set; }

    [JsonPropertyName("data_request")] public DataRequest DataRequest { get; set; }
}

public class DataRequest
{
    [JsonPropertyName("id")] public int Id { get; set; }
}

public class ShopifyCustomerObjectDto
{
    [JsonPropertyName("id")] public int Id { get; set; }
    [JsonPropertyName("email")] public string Email { get; set; }

    [JsonPropertyName("phone")] public string Phone { get; set; }
}

public class ShopifyCustomerDataRedactionDto
{
    [JsonPropertyName("shop_id")] public int ShopId { get; set; }

    [JsonPropertyName("shop_domain")] public string ShopDomain { get; set; }

    [JsonPropertyName("orders_to_redact")] public List<int> OrdersRequested { get; set; }

    [JsonPropertyName("customer")] public ShopifyCustomerObjectDto Customer { get; set; }
}

public class ShopifyWebshopDataRedactionDto
{
    [JsonPropertyName("shop_id")] public int ShopId { get; set; }

    [JsonPropertyName("shop_domain")] public string ShopDomain { get; set; }
}