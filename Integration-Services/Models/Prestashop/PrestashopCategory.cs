namespace Integration.Models.WooCommerce;

// Root myDeserializedClass = JsonConvert.DeserializeObject<List<Root>>(myJsonResponse);
public class CategoryPrestashop1
{
    public int id { get; set; }
    public string id_parent { get; set; }
    public string level_depth { get; set; }
    public string nb_products_recursive { get; set; }
    public string active { get; set; }
    public string id_shop_default { get; set; }
    public string is_root_category { get; set; }
    public string position { get; set; }
    public string date_add { get; set; }
    public string date_upd { get; set; }
    public string name { get; set; }
    public string link_rewrite { get; set; }
    public string description { get; set; }
    public string meta_title { get; set; }
    public string meta_description { get; set; }
    public string meta_keywords { get; set; }
}

public class PrestashopCategory
{
    public List<CategoryPrestashop1> categories { get; set; }
}