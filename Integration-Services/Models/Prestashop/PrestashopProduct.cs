namespace Integration.Models.WooCommerce;

// Root myDeserializedClass = JsonConvert.DeserializeObject<List<Root>>(myJsonResponse);
public class AccessoryPrestashop
{
    public string id { get; set; }
}

public class AssociationsPrestashop
{
    public List<CategoryPrestashop> categories { get; set; }
    public List<ImagePrestashop> images { get; set; }
    public List<CombinationPrestashop> combinations { get; set; }
    public List<ProductOptionValuePrestashop> product_option_values { get; set; }
    public List<ProductFeaturePrestashop> product_features { get; set; }
    public List<StockAvailablePrestashop> stock_availables { get; set; }
    public List<AccessoryPrestashop> accessories { get; set; }
    public List<ProductBundlePrestashop> product_bundle { get; set; }
}

public class CategoryPrestashop
{
    public string id { get; set; }
}

public class CombinationPrestashop
{
    public string id { get; set; }
}

public class ImagePrestashop
{
    public string id { get; set; }
}

public class ProductPrestashop
{
    public int id { get; set; }
    public string id_manufacturer { get; set; }
    public string id_supplier { get; set; }
    public string id_category_default { get; set; }
    public object @new { get; set; }
    public string cache_default_attribute { get; set; }
    public string id_default_image { get; set; }
    public object id_default_combination { get; set; }
    public string id_tax_rules_group { get; set; }
    public string position_in_category { get; set; }
    public object manufacturer_name { get; set; }
    public string quantity { get; set; }
    public string type { get; set; }
    public string id_shop_default { get; set; }
    public string reference { get; set; }
    public string supplier_reference { get; set; }
    public string location { get; set; }
    public string width { get; set; }
    public string height { get; set; }
    public string depth { get; set; }
    public string weight { get; set; }
    public string quantity_discount { get; set; }
    public string ean13 { get; set; }
    public string isbn { get; set; }
    public string upc { get; set; }
    public string mpn { get; set; }
    public string cache_is_pack { get; set; }
    public string cache_has_attachments { get; set; }
    public string is_virtual { get; set; }
    public string state { get; set; }
    public string additional_delivery_times { get; set; }
    public string delivery_in_stock { get; set; }
    public string delivery_out_stock { get; set; }
    public string product_type { get; set; }
    public string on_sale { get; set; }
    public string online_only { get; set; }
    public string ecotax { get; set; }
    public string minimal_quantity { get; set; }
    public object low_stock_threshold { get; set; }
    public string low_stock_alert { get; set; }
    public string price { get; set; }
    public string wholesale_price { get; set; }
    public string unity { get; set; }
    public string unit_price_ratio { get; set; }
    public string additional_shipping_cost { get; set; }
    public string customizable { get; set; }
    public string text_fields { get; set; }
    public string uploadable_files { get; set; }
    public string active { get; set; }
    public string redirect_type { get; set; }
    public string id_type_redirected { get; set; }
    public string available_for_order { get; set; }
    public string available_date { get; set; }
    public string show_condition { get; set; }
    public string condition { get; set; }
    public string show_price { get; set; }
    public string indexed { get; set; }
    public string visibility { get; set; }
    public string advanced_stock_management { get; set; }
    public string date_add { get; set; }
    public string date_upd { get; set; }
    public string pack_stock_type { get; set; }
    public string meta_description { get; set; }
    public string meta_keywords { get; set; }
    public string meta_title { get; set; }
    public string link_rewrite { get; set; }
    public string name { get; set; }
    public string description { get; set; }
    public string description_short { get; set; }
    public string available_now { get; set; }
    public string available_later { get; set; }
    public AssociationsPrestashop associations { get; set; }
}

public class ProductBundlePrestashop
{
    public string id { get; set; }
    public string id_product_attribute { get; set; }
    public string quantity { get; set; }
}

public class ProductFeaturePrestashop
{
    public string id { get; set; }
    public string id_feature_value { get; set; }
}

public class ProductOptionValuePrestashop
{
    public string id { get; set; }
}

public class PrestashopProduct
{
    public List<ProductPrestashop> products { get; set; }
}

public class StockAvailablePrestashop
{
    public string id { get; set; }
    public string id_product_attribute { get; set; }
}