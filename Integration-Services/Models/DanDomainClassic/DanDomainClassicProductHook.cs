using Newtonsoft.Json;

namespace Marlin_OS_MerchantSync_API.Models.Plugins.DanDomainClassic;

public class DanDomainClassicProductHookMetadata
{
    public string correlationId { get; set; }
    public string initiatedBy { get; set; }
    public int priority { get; set; }
}

public class DanDomainClassicProductHookOldValues
{
    public int id { get; set; }
    public double price { get; set; }
    public double weight { get; set; }
    public DateTime createdDate { get; set; }
    public int stockCount { get; set; }
    public int stockLimit { get; set; }
    public int sortOrder { get; set; }
    public int minBuyAmount { get; set; }
    public int maxBuyAmount { get; set; }
    public bool isVariantMaster { get; set; }
    public int minBuyAmountB2B { get; set; }
    public bool isRateVariant { get; set; }
    public int pointsEarns { get; set; }
    public bool isReviewVariants { get; set; }
    public bool isGiftCertificate { get; set; }
    public bool showOnGoogleFeed { get; set; }
    public bool showOnFacebookFeed { get; set; }
    public bool showOnKelkooFeed { get; set; }
    public bool showOnPricerunnerFeed { get; set; }
    public bool isBundle { get; set; }

    [JsonProperty("medias[0].sortOrder")] public int medias0sortOrder { get; set; }

    [JsonProperty("medias[1].mime")] public string medias1mime { get; set; }

    [JsonProperty("medias[1].sortOrder")] public int medias1sortOrder { get; set; }

    [JsonProperty("prices[0].amount")] public int prices0amount { get; set; }

    [JsonProperty("prices[0].unitPrice")] public double prices0unitPrice { get; set; }

    [JsonProperty("prices[0].specialOfferPrice")]
    public double prices0specialOfferPrice { get; set; }

    [JsonProperty("prices[0].avance")] public double prices0avance { get; set; }

    [JsonProperty("options[0].siteId")] public int options0siteId { get; set; }

    [JsonProperty("options[0].isHidden")] public bool options0isHidden { get; set; }

    [JsonProperty("options[0].showAsNew")] public bool options0showAsNew { get; set; }

    [JsonProperty("options[0].retailPrice")]
    public double options0retailPrice { get; set; }

    [JsonProperty("options[0].productName")]
    public string options0productName { get; set; }

    [JsonProperty("categories[0].isDefault")]
    public bool categories0isDefault { get; set; }

    public string objectIdentifier { get; set; }
    public int version { get; set; }
}

public class DanDomainClassicProductHookNewValues
{
    public int id { get; set; }
    public int idDelta { get; set; }
    public string nr { get; set; }
    public string vendorNr { get; set; }
    public double price { get; set; }
    public double priceDelta { get; set; }
    public double weight { get; set; }
    public double weightDelta { get; set; }
    public string pictureLink { get; set; }
    public string pictureBigLink { get; set; }
    public DateTime createdDate { get; set; }
    public string createdDateDelta { get; set; }
    public int stockCount { get; set; }
    public int stockCountDelta { get; set; }
    public int stockLimit { get; set; }
    public int stockLimitDelta { get; set; }
    public string locationNumber { get; set; }
    public string barcodeNumber { get; set; }
    public int sortOrder { get; set; }
    public int sortOrderDelta { get; set; }
    public int minBuyAmount { get; set; }
    public int minBuyAmountDelta { get; set; }
    public int maxBuyAmount { get; set; }
    public int maxBuyAmountDelta { get; set; }
    public string fileSaleLink { get; set; }
    public bool isVariantMaster { get; set; }
    public string eDBPriserProdNr { get; set; }
    public string notes { get; set; }
    public string googleFeedCategory { get; set; }
    public int minBuyAmountB2B { get; set; }
    public int minBuyAmountB2BDelta { get; set; }
    public bool isRateVariant { get; set; }
    public int pointsEarns { get; set; }
    public int pointsEarnsDelta { get; set; }
    public bool isReviewVariants { get; set; }
    public bool isGiftCertificate { get; set; }
    public bool showOnGoogleFeed { get; set; }
    public bool showOnFacebookFeed { get; set; }
    public bool showOnKelkooFeed { get; set; }
    public bool showOnPricerunnerFeed { get; set; }
    public bool isBundle { get; set; }

    [JsonProperty("medias[0].itemId")] public string medias0itemId { get; set; }

    [JsonProperty("medias[0].url")] public string medias0url { get; set; }

    [JsonProperty("medias[0].mime")] public string medias0mime { get; set; }

    [JsonProperty("medias[0].sortOrder")] public int medias0sortOrder { get; set; }

    [JsonProperty("medias[0].sortOrderDelta")]
    public int medias0sortOrderDelta { get; set; }

    [JsonProperty("medias[1].itemId")] public string medias1itemId { get; set; }

    [JsonProperty("medias[1].url")] public string medias1url { get; set; }

    [JsonProperty("medias[1].mime")] public string medias1mime { get; set; }

    [JsonProperty("medias[1].sortOrder")] public int medias1sortOrder { get; set; }

    [JsonProperty("medias[1].sortOrderDelta")]
    public int medias1sortOrderDelta { get; set; }

    [JsonProperty("medias[1].thumbnail")] public string medias1thumbnail { get; set; }

    [JsonProperty("prices[0].itemId")] public string prices0itemId { get; set; }

    [JsonProperty("prices[0].amount")] public int prices0amount { get; set; }

    [JsonProperty("prices[0].amountDelta")]
    public int prices0amountDelta { get; set; }

    [JsonProperty("prices[0].currencyCode")]
    public string prices0currencyCode { get; set; }

    [JsonProperty("prices[0].b2BGroupId")] public string prices0b2BGroupId { get; set; }

    [JsonProperty("prices[0].unitPrice")] public double prices0unitPrice { get; set; }

    [JsonProperty("prices[0].unitPriceDelta")]
    public double prices0unitPriceDelta { get; set; }

    [JsonProperty("prices[0].specialOfferPrice")]
    public double prices0specialOfferPrice { get; set; }

    [JsonProperty("prices[0].specialOfferPriceDelta")]
    public double prices0specialOfferPriceDelta { get; set; }

    [JsonProperty("prices[0].avance")] public double prices0avance { get; set; }

    [JsonProperty("prices[0].avanceDelta")]
    public double prices0avanceDelta { get; set; }

    [JsonProperty("options[0].itemId")] public string options0itemId { get; set; }

    [JsonProperty("options[0].siteId")] public int options0siteId { get; set; }

    [JsonProperty("options[0].siteIdDelta")]
    public int options0siteIdDelta { get; set; }

    [JsonProperty("options[0].isHidden")] public bool options0isHidden { get; set; }

    [JsonProperty("options[0].showAsNew")] public bool options0showAsNew { get; set; }

    [JsonProperty("options[0].retailPrice")]
    public double options0retailPrice { get; set; }

    [JsonProperty("options[0].retailPriceDelta")]
    public double options0retailPriceDelta { get; set; }

    [JsonProperty("options[0].productName")]
    public string options0productName { get; set; }

    [JsonProperty("categories[0].itemId")] public string categories0itemId { get; set; }

    [JsonProperty("categories[0].categoryNumber")]
    public string categories0categoryNumber { get; set; }

    [JsonProperty("categories[0].isDefault")]
    public bool categories0isDefault { get; set; }

    public string objectIdentifier { get; set; }
    public int version { get; set; }
    public int versionDelta { get; set; }
}

public class DanDomainClassicProductHook
{
    public List<string> propertiesChanged { get; set; }
    public DanDomainClassicProductHookOldValues? oldValues { get; set; }
    public DanDomainClassicProductHookNewValues? newValues { get; set; }
    public string objectType { get; set; }
    public int version { get; set; }
    public int shopIdentifier { get; set; }
    public DanDomainClassicProductHookMetadata metadata { get; set; }
}