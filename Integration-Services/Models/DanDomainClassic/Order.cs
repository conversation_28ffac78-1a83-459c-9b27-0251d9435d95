namespace Marlin_OS_MerchantSync_API.Models.Plugins.DanDomainClassic;

// Root myDeserializedClass = JsonConvert.DeserializeObject<List<Root>>(myJsonResponse);
public class CustomerInfo
{
    public string address { get; set; }
    public string address2 { get; set; }
    public string attention { get; set; }
    public string city { get; set; }
    public string country { get; set; }
    public int countryId { get; set; }
    public string ean { get; set; }
    public string email { get; set; }
    public string fax { get; set; }
    public string id { get; set; }
    public string name { get; set; }
    public string phone { get; set; }
    public string state { get; set; }
    public string zipCode { get; set; }
}

public class DeliveryInfo
{
    public string address { get; set; }
    public string address2 { get; set; }
    public string attention { get; set; }
    public string city { get; set; }
    public string country { get; set; }
    public int countryId { get; set; }
    public string cvr { get; set; }
    public string ean { get; set; }
    public string email { get; set; }
    public string fax { get; set; }
    public string name { get; set; }
    public string phone { get; set; }
    public string state { get; set; }
    public string zipCode { get; set; }
}

public class InvoiceInfo
{
    public object creditNoteNumber { get; set; }
    public object date { get; set; }
    public bool isPaid { get; set; }
    public string number { get; set; }
    public string state { get; set; }
}

public class OrderLine
{
    public string fileUrl { get; set; }
    public int id { get; set; }
    public string productId { get; set; }
    public string productName { get; set; }
    public int quantity { get; set; }
    public decimal totalPrice { get; set; }
    public decimal unitPrice { get; set; }
    public string variant { get; set; }
    public decimal vatPct { get; set; }
    public object xmlParams { get; set; }
}

public class OrderState
{
    public bool exclStatistics { get; set; }
    public int id { get; set; }
    public bool isDefault { get; set; }
    public string name { get; set; }
}

public class PaymentInfo
{
    public decimal fee { get; set; }
    public bool feeInclVat { get; set; }
    public int id { get; set; }
    public string name { get; set; }
}

public class OrderDandomainClassic
{
    public object comment { get; set; }
    public DateTime createdDate { get; set; }
    public object creditNoteNumber { get; set; }
    public string currencyCode { get; set; }
    public string customerComment { get; set; }
    public CustomerInfo customerInfo { get; set; }
    public DeliveryInfo deliveryInfo { get; set; }
    public double giftCertificateAmount { get; set; }
    public object giftCertificateNumber { get; set; }
    public int id { get; set; }
    public bool incomplete { get; set; }
    public InvoiceInfo invoiceInfo { get; set; }
    public string? ip { get; set; }
    public bool modified { get; set; }
    public DateTime? modifiedDate { get; set; }
    public List<OrderLine> orderLines { get; set; }
    public OrderState orderState { get; set; }
    public PaymentInfo paymentInfo { get; set; }
    public string referenceNumber { get; set; }
    public string referrer { get; set; }
    public string reservedField1 { get; set; }
    public string reservedField2 { get; set; }
    public string reservedField3 { get; set; }
    public string reservedField4 { get; set; }
    public string reservedField5 { get; set; }
    public decimal salesDiscount { get; set; }
    public ShippingInfo shippingInfo { get; set; }
    public int siteId { get; set; }
    public decimal totalPrice { get; set; }
    public decimal totalWeight { get; set; }
    public object trackingNumber { get; set; }
    public object transactionNumber { get; set; }
    public decimal vatPct { get; set; }
    public string vatRegNumber { get; set; }
    public object xmlParams { get; set; }
}

public class ShippingInfo
{
    public decimal fee { get; set; }
    public bool feeInclVat { get; set; }
    public int id { get; set; }
    public string name { get; set; }
}