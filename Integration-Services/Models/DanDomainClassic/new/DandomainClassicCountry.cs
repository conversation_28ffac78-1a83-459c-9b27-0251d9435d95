using System.Text.Json.Serialization;

public class DandomainClassicCountry
{
    [Json<PERSON>ropertyName("code")]
    public string Code { get; set; }
    [JsonPropertyName("id")]
    public int Id { get; set; }
    [JsonPropertyName("name")]
    public string Name { get; set; }
    [JsonPropertyName("siteId")]
    public int SiteId { get; set; }
    [JsonPropertyName("active")]
    public bool Active { get; set; }
    [JsonPropertyName("correlationId")]
    public string CorrelationId { get; set; }
    [JsonPropertyName("vat")]
    public string Vat { get; set; }
    [JsonPropertyName("companyVat")]
    public string CompanyVat { get; set; }
    [JsonPropertyName("sortOrder")]
    public int SortOrder { get; set; }
}
