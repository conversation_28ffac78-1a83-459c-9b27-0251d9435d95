public class Domain
{
    public string name { get; set; }
    public string httpRedirect { get; set; }
    public bool defaultRedirect { get; set; }
}

public class Group
{
    public string name { get; set; }
    public int id { get; set; }
}

public class Item
{
    public string currencyCode { get; set; }
    public int countryId { get; set; }
    public string name { get; set; }
    public int id { get; set; }
    public string correlationId { get; set; }
    public List<Domain> domains { get; set; }
    public string culture { get; set; }
    public bool defaultSite { get; set; }
    public Group group { get; set; }
    public string title { get; set; }
    public Skin skin { get; set; }
    public SkinMobile skinMobile { get; set; }
    public string hrefLang { get; set; }
    public string iso { get; set; }
    public bool urlEncodeLinks { get; set; }
    public string favIcon { get; set; }
    public string codeField { get; set; }
    public string metaDescription { get; set; }
    public string metaKeywords { get; set; }
    public string metaCustom { get; set; }
    public string alias { get; set; }
}

public class DandomainSetting
{
    public List<Item> items { get; set; }
    public bool hasMore { get; set; }
    public int count { get; set; }
    public string correlationId { get; set; }
}

public class Skin
{
    public int id { get; set; }
    public string name { get; set; }
}

public class SkinMobile
{
    public int id { get; set; }
    public string name { get; set; }
}