public class DandomainProduct
{
    public int id { get; set; }
    public string number { get; set; }
    public string vendorNumber { get; set; }
    public double costPrice { get; set; }
    public double weight { get; set; }
    public string pictureLink { get; set; }
    public DateTime createdDate { get; set; }
    public DateTime editedDate { get; set; }
    public int stockCount { get; set; }
    public int stockLimit { get; set; }
    public bool allowPreOrder { get; set; }
    public bool allowBackOrder { get; set; }
    public int backOrderAvailabilityDays { get; set; }
    public string locationNumber { get; set; }
    public string barCodeNumber { get; set; }
    public string googleFeedCategory { get; set; }
    public int sortOrder { get; set; }
    public int minBuyAmount { get; set; }
    public int maxBuyAmount { get; set; }
    public string fileSaleLink { get; set; }
    public bool isVariantMaster { get; set; }
    public string variantMasterId { get; set; }
    public string edbPriserProductNumber { get; set; }
    public string defaultCategoryId { get; set; }
    public bool modified { get; set; }
    public string comments { get; set; }
    public int minBuyAmountB2B { get; set; }
    public bool rateVariants { get; set; }
    public bool reviewVariants { get; set; }
    public bool isGiftCertificate { get; set; }
    public int typeId { get; set; }
    public int salesCount { get; set; }
    public string primaryCategoryId { get; set; }
    public bool showOnGoogleFeed { get; set; }
    public bool showOnFacebookFeed { get; set; }
    public bool showOnPricerunnerFeed { get; set; }
    public bool showOnKelkooFeed { get; set; }
    public Settings settings { get; set; }
    public Categories categories { get; set; }
    public ProductVariants productVariants { get; set; }
    public Media media { get; set; }
    public Prices prices { get; set; }
    public string categoryNumber { get; set; }
    public string url { get; set; }
    public string name { get; set; }
    public int siteId { get; set; }
    public bool hidden { get; set; }
    public string link { get; set; }
    public string description { get; set; }
    public string icon { get; set; }
    public string @string { get; set; }
    public string image { get; set; }
    public string keywords { get; set; }
    public string metaDescription { get; set; }
    public string title { get; set; }
    public bool hiddenMobile { get; set; }
    public string urlName { get; set; }
    public string footer { get; set; }

    public class Categories
    {
        public List<CategoriesItem> items { get; set; }
        public bool hasMore { get; set; }
        public int count { get; set; }
        public string url { get; set; }
    }

    public class CategoriesItem
    {
        public string number { get; set; }
        public string b2BGroupId { get; set; }
        public int infoLayout { get; set; }
        public int listLayout { get; set; }
        public int customInfoLayout { get; set; }
        public int id { get; set; }
        public bool modified { get; set; }
        public DateTime editedDate { get; set; }
        public DateTime createdDate { get; set; }
        public Texts texts { get; set; }
        public List<object> segmentIds { get; set; }
        public List<string> parentIds { get; set; }
    }

    public class ProductVariants
    {
        public List<object> items { get; set; }
        public bool hasMore { get; set; }
        public int count { get; set; }
        public string url { get; set; }
    }

    public class Settings
    {
        public List<SettingItem> items { get; set; }
        public bool hasMore { get; set; }
        public int count { get; set; }
        public string url { get; set; }
    }

    public class SettingItem
    {
        public int languageId { get; set; }
        public string url { get; set; }
        public string? name { get; set; }
        public string shortDescription { get; set; }
        public string longDescription { get; set; }
        public string longDescription2 { get; set; }
        public string keyWords { get; set; }
        public int sortOrder { get; set; }
        public string metaDescription { get; set; }
        public string expectedDeliveryTime { get; set; }
        public string expectedDeliveryTimeNotInStock { get; set; }
        public string techDocLink { get; set; }
        public string techDocLink2 { get; set; }
        public string techDocLink3 { get; set; }
        public string techDocLinkText { get; set; }
        public string techDocLinkText2 { get; set; }
        public string techDocLinkText3 { get; set; }
        public string pageTitle { get; set; }
        public bool isHidden { get; set; }
        public bool isHiddenForMobile { get; set; }
        public bool showOnFrontPage { get; set; }
        public bool showAsNew { get; set; }
        public double retailSalesPrice { get; set; }
        public bool isToplistHidden { get; set; }
        public string imageAltText { get; set; }
        public string rememberToBuyTextHeading { get; set; }
        public string rememberToBuyTextSubheading { get; set; }
        public string giftCertificatePdfBackgroundImage { get; set; }
        public string unitNumber { get; set; }
        public string urlName { get; set; }
        public bool isExcludedFromFreeDeliveryLimit { get; set; }
    }

    public class TextItem
    {
        public int id { get; set; }
        public string categoryNumber { get; set; }
        public string url { get; set; }
        public string name { get; set; }
        public int siteId { get; set; }
        public bool hidden { get; set; }
        public int sortOrder { get; set; }
        public string link { get; set; }
        public string description { get; set; }
        public string icon { get; set; }
        public string @string { get; set; }
        public string image { get; set; }
        public string keywords { get; set; }
        public string metaDescription { get; set; }
        public string title { get; set; }
        public bool hiddenMobile { get; set; }
        public string urlName { get; set; }
        public string footer { get; set; }
    }

    public class Texts
    {
        public List<TextItem> items { get; set; }
        public bool hasMore { get; set; }
        public int count { get; set; }
        public string url { get; set; }
    }

    public class PriceItem
    {
        public int isoCode { get; set; }
        public decimal unitPrice { get; set; }
        public decimal specialOfferPrice { get; set; }
        public decimal avance { get; set; }
        public string b2bGroupId { get; set; }
        public int quantity { get; set; }
        public string currencyCode { get; set; }
    }

    public class Prices
    {
        public List<PriceItem> items { get; set; }
        public bool hasMore { get; set; }
        public int count { get; set; }
        public string url { get; set; }
    }

    public class Media
    {
        public List<MediaItem> items { get; set; }
        public bool hasMore { get; set; }
        public int count { get; set; }
        public string url { get; set; }
    }

    public class MediaItem
    {
        public int id { get; set; }
        public Translations translations { get; set; }
        public string mediaUrl { get; set; }
        public string mime { get; set; }
        public string thumbnail { get; set; }
        public int sortOrder { get; set; }
        public string width { get; set; }
        public string height { get; set; }
        public string thumbnailWidth { get; set; }
        public string thumbnailHeight { get; set; }
    }

    public class Translations
    {
        public List<TrnasLationItem> items { get; set; }
        public bool hasMore { get; set; }
        public int count { get; set; }
        public string url { get; set; }
    }

    public class TrnasLationItem
    {
        public int siteId { get; set; }
        public int id { get; set; }
        public string alternate { get; set; }
        public string name { get; set; }
    }
}