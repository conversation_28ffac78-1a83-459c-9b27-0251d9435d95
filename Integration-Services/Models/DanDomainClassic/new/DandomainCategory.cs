// Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse);

public class DandomainCategory
{
    public string number { get; set; }
    public string b2BGroupId { get; set; }
    public int infoLayout { get; set; }
    public int listLayout { get; set; }
    public int customInfoLayout { get; set; }
    public int id { get; set; }
    public bool modified { get; set; }
    public DateTime editedDate { get; set; }
    public TextsDandomainCategory texts { get; set; }
    public List<object> segmentIds { get; set; }
    public List<string> parentIds { get; set; }
    public DateTime? createdDate { get; set; }
    public string categoryNumber { get; set; }
    public string url { get; set; }
    public string name { get; set; }
    public int siteId { get; set; }
    public bool hidden { get; set; }
    public int sortOrder { get; set; }
    public string link { get; set; }
    public string description { get; set; }
    public string icon { get; set; }
    public string @string { get; set; }
    public string image { get; set; }
    public string? keywords { get; set; }
    public string metaDescription { get; set; }
    public string title { get; set; }
    public bool hiddenMobile { get; set; }
    public string urlName { get; set; }
    public string footer { get; set; }
}

public class DandomainCategoryPagination
{
    public List<DandomainCategory> items { get; set; }
    public bool hasMore { get; set; }
    public int count { get; set; }
    public string correlationId { get; set; }
}

public class TextsDandomainCategory
{
    public List<DandomainCategory> items { get; set; }
    public bool hasMore { get; set; }
    public int count { get; set; }
    public string url { get; set; }
}