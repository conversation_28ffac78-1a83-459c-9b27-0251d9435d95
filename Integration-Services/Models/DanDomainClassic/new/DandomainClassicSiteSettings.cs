using System.Text.Json.Serialization;

public class DandomainClassicSiteSettings
{
    [JsonPropertyName("productSettings")]
    public ProductSettings ProductSettings { get; set; }
}


public class ProductSettings
{
    [JsonPropertyName("priceSettings")]
    public PriceSettings PriceSettings { get; set; }
}

public class PriceSettings
{
    [JsonPropertyName("priceSumMultipleUnitPrices")]
    public bool PriceSumMultipleUnitPrices { get; set; }

    [JsonPropertyName("dbvatEnabled")]
    public bool DbvatEnabled { get; set; }

    [JsonPropertyName("shopVATVisible")]
    public bool ShopVATVisible { get; set; }

    [JsonPropertyName("selectCurrencyVisible")]
    public bool SelectCurrencyVisible { get; set; }

    [JsonPropertyName("roundEnabled")]
    public bool RoundEnabled { get; set; }

    [JsonPropertyName("excludeSpecialOffersOnCustomerDiscount")]
    public bool ExcludeSpecialOffersOnCustomerDiscount { get; set; }

    [JsonPropertyName("includeFeeEnabled")]
    public bool IncludeFeeEnabled { get; set; }
}

