using System.Text.Json.Serialization;
using Newtonsoft.Json;

namespace Marlin_OS_MerchantSync_API.Models.Plugins.DanDomainClassic;

public class MediumDanDomain
{
    [JsonProperty("id")]
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonProperty("url")]
    [JsonPropertyName("url")]
    public string Url { get; set; }
}

public class PriceDanDomain
{
    [JsonProperty("ISOCode")]
    [JsonPropertyName("ISOCode")]
    public int? ISOCode { get; set; }

    [JsonProperty("amount")]
    [JsonPropertyName("amount")]
    public int? Amount { get; set; }

    [JsonProperty("currencyCode")]
    [JsonPropertyName("currencyCode")]
    public string CurrencyCode { get; set; }

    [JsonProperty("specialOfferPrice")]
    [JsonPropertyName("specialOfferPrice")]
    public double? SpecialOfferPrice { get; set; }

    [JsonProperty("unitPrice")]
    [JsonPropertyName("unitPrice")]
    public double UnitPrice { get; set; }
}

public class ProductCategoryDanDomain
{
    [JsonProperty("defaultParentId")]
    [JsonPropertyName("defaultParentId")]
    public object DefaultParentId { get; set; }

    [JsonProperty("internalId")]
    [JsonPropertyName("internalId")]
    public int? InternalId { get; set; }

    [JsonProperty("number")]
    [JsonPropertyName("number")]
    public string Number { get; set; }

    [JsonProperty("parentIdList")]
    [JsonPropertyName("parentIdList")]
    public List<string> ParentIdList { get; set; }

    [JsonProperty("segmentIdList")]
    [JsonPropertyName("segmentIdList")]
    public List<object> SegmentIdList { get; set; }

    [JsonProperty("texts")]
    [JsonPropertyName("texts")]
    public List<TextDanDomain> Texts { get; set; }
}

public class ProductRelationDanDomain
{
    [JsonProperty("productNumber")]
    [JsonPropertyName("productNumber")]
    public string ProductNumber { get; set; }

    [JsonProperty("relatedType")]
    [JsonPropertyName("relatedType")]
    public int? RelatedType { get; set; }

    [JsonProperty("sortOrder")]
    [JsonPropertyName("sortOrder")]
    public int? SortOrder { get; set; }
}

public class ProductTypeDanDomain
{
    [JsonProperty("id")]
    [JsonPropertyName("id")]
    public int? Id { get; set; }

    [JsonProperty("name")]
    [JsonPropertyName("name")]
    public string Name { get; set; }

    [JsonProperty("vat")]
    [JsonPropertyName("vat")]
    public List<object> Vat { get; set; }
}

public class ProductDanDomain
{
    [JsonProperty("barCodeNumber")]
    [JsonPropertyName("barCodeNumber")]
    public string BarCodeNumber { get; set; }

    [JsonProperty("categoryIdList")]
    [JsonPropertyName("categoryIdList")]
    public List<string> CategoryIdList { get; set; }

    [JsonProperty("costPrice")]
    [JsonPropertyName("costPrice")]
    public double? CostPrice { get; set; }

    [JsonProperty("created")]
    [JsonPropertyName("created")]
    public DateTime? Created { get; set; }

    [JsonProperty("defaultCategoryId")]
    [JsonPropertyName("defaultCategoryId")]
    public string DefaultCategoryId { get; set; }

    [JsonProperty("id")]
    [JsonPropertyName("id")]
    public string Id { get; set; }

    [JsonProperty("isVariantMaster")]
    [JsonPropertyName("isVariantMaster")]
    public bool IsVariantMaster { get; set; }

    [JsonProperty("media")]
    [JsonPropertyName("media")]
    public List<MediumDanDomain> Media { get; set; }

    [JsonProperty("number")]
    [JsonPropertyName("number")]
    public string Number { get; set; }

    [JsonProperty("picture")]
    [JsonPropertyName("picture")]
    public string Picture { get; set; }

    [JsonProperty("prices")]
    [JsonPropertyName("prices")]
    public List<PriceDanDomain> Prices { get; set; }

    [JsonProperty("productCategories")]
    [JsonPropertyName("productCategories")]
    public List<ProductCategoryDanDomain> ProductCategories { get; set; }

    [JsonProperty("productRelations")]
    [JsonPropertyName("productRelations")]
    public List<ProductRelationDanDomain> ProductRelations { get; set; }

    [JsonProperty("productType")]
    [JsonPropertyName("productType")]
    public ProductTypeDanDomain ProductType { get; set; }

    /*[JsonProperty("segmentIdList")]
    [JsonPropertyName("segmentIdList")]
    public List<object> SegmentIdList { get; set; }

    [JsonProperty("segments")]
    [JsonPropertyName("segments")]
    public List<object> Segments { get; set; }*/

    [JsonProperty("siteSettings")]
    [JsonPropertyName("siteSettings")]
    public List<SiteSettingDanDomain> SiteSettings { get; set; }

    [JsonProperty("sortOrder")]
    [JsonPropertyName("sortOrder")]
    public int? SortOrder { get; set; }

    [JsonProperty("stockCount")]
    [JsonPropertyName("stockCount")]
    public int? StockCount { get; set; }

    [JsonProperty("updated")]
    [JsonPropertyName("updated")]
    public DateTime? Updated { get; set; }

    [JsonProperty("variantMasterId")]
    [JsonPropertyName("variantMasterId")]
    public string VariantMasterId { get; set; }

    [JsonProperty("specialOfferPrice")]
    [JsonPropertyName("specialOfferPrice")]
    public double? SpecialOfferPrice { get; set; }

    [JsonProperty("url")]
    [JsonPropertyName("url")]
    public string? Url { get; set; }

    [JsonProperty("priceIncVAT")]
    [JsonPropertyName("priceIncVAT")]
    public double? PriceIncVAT { get; set; }

    [JsonProperty("isSpecialOffer")]
    [JsonPropertyName("isSpecialOffer")]
    public bool IsSpecialOffer { get; set; }

    [JsonProperty("name")]
    [JsonPropertyName("name")]
    public string? Name { get; set; }

    [JsonProperty("related")]
    [JsonPropertyName("related")]
    public List<string> Related { get; set; }
}

public class SiteSettingDanDomain
{
    [JsonProperty("imageAltText")]
    [JsonPropertyName("imageAltText")]
    public string ImageAltText { get; set; }

    [JsonProperty("longDescription")]
    [JsonPropertyName("longDescription")]
    public string LongDescription { get; set; }

    [JsonProperty("name")]
    [JsonPropertyName("name")]
    public string Name { get; set; }

    [JsonProperty("shortDescription")]
    [JsonPropertyName("shortDescription")]
    public string ShortDescription { get; set; }

    [JsonProperty("siteID")]
    [JsonPropertyName("siteID")]
    public int? SiteID { get; set; }

    [JsonProperty("unitNumber")]
    [JsonPropertyName("unitNumber")]
    public string UnitNumber { get; set; }

    [JsonProperty("urlname")]
    [JsonPropertyName("urlname")]
    public string Urlname { get; set; }
}

public class TextDanDomain
{
    [JsonProperty("categoryNumber")]
    [JsonPropertyName("categoryNumber")]
    public string CategoryNumber { get; set; }

    [JsonProperty("description")]
    [JsonPropertyName("description")]
    public string Description { get; set; }

    [JsonProperty("id")]
    [JsonPropertyName("id")]
    public int? Id { get; set; }

    [JsonProperty("image")]
    [JsonPropertyName("image")]
    public string Image { get; set; }

    [JsonProperty("link")]
    [JsonPropertyName("link")]
    public string Link { get; set; }

    [JsonProperty("name")]
    [JsonPropertyName("name")]
    public string Name { get; set; }

    [JsonProperty("siteId")]
    [JsonPropertyName("siteId")]
    public int? SiteId { get; set; }

    [JsonProperty("urlname")]
    [JsonPropertyName("urlname")]
    public string Urlname { get; set; }
}

public class ProductRawDanDomain
{
    [JsonProperty("id")]
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonProperty("isVariantMaster")]
    [JsonPropertyName("isVariantMaster")]
    public bool IsVariantMaster { get; set; }

    [JsonProperty("media")]
    [JsonPropertyName("media")]
    public List<MediumDanDomain> Media { get; set; }

    [JsonProperty("number")]
    [JsonPropertyName("number")]
    public string Number { get; set; }

    [JsonProperty("picture")]
    [JsonPropertyName("picture")]
    public string Picture { get; set; }

    [JsonProperty("productCategories")]
    [JsonPropertyName("productCategories")]
    public List<ProductCategoryDanDomain> ProductCategories { get; set; }

    [JsonProperty("productRelations")]
    [JsonPropertyName("productRelations")]
    public List<ProductRelationDanDomain> ProductRelations { get; set; }

    [JsonProperty("productType")]
    [JsonPropertyName("productType")]
    public ProductTypeDanDomain ProductType { get; set; }

    [JsonProperty("stockCount")]
    [JsonPropertyName("stockCount")]
    public int StockCount { get; set; }

    [JsonProperty("variantMasterId")]
    [JsonPropertyName("variantMasterId")]
    public string VariantMasterId { get; set; }

    [JsonProperty("specialOfferPrice")]
    [JsonPropertyName("specialOfferPrice")]
    public double SpecialOfferPrice { get; set; }

    [JsonProperty("url")]
    [JsonPropertyName("url")]
    public string Url { get; set; }

    [JsonProperty("priceIncVAT")]
    [JsonPropertyName("priceIncVAT")]
    public double PriceIncVAT { get; set; }

    [JsonProperty("isSpecialOffer")]
    [JsonPropertyName("isSpecialOffer")]
    public bool? IsSpecialOffer { get; set; }

    [JsonProperty("name")]
    [JsonPropertyName("name")]
    public string Name { get; set; }

    [JsonProperty("related")]
    [JsonPropertyName("related")]
    public List<string> Related { get; set; }
}