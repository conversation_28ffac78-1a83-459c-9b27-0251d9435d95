using System.Text.Json;
using System.Text.Json.Serialization;
using Integration.Models.WooCommerce;

namespace Marlin_OS_Integration_API.Converters.Helpers;

public class MetaDataOrderConverter : JsonConverter<MetaDataOrder>
{
    public override MetaDataOrder Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        using (JsonDocument doc = JsonDocument.ParseValue(ref reader))
        {
            var root = doc.RootElement;

            if (root.TryGetProperty("value", out var valueElement) && valueElement.ValueKind != JsonValueKind.String)
            {
                return null;
            }

            return new MetaDataOrder
            {
                id = root.GetProperty("id").GetInt32(),
                key = root.GetProperty("key").GetString(),
                value = valueElement.GetString(),
                display_key = root.GetProperty("display_key").GetString(),
                display_value = root.GetProperty("display_value").GetString(),
            };
        }
    }

    public override void Write(Utf8JsonWriter writer, MetaDataOrder value, JsonSerializerOptions options)
    {
        throw new NotImplementedException();
    }
}