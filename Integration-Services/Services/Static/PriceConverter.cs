namespace Integration.Services.Static;

public static class PriceConverter
{
    public static string ConvertPrice(decimal? price)
    {
        if (price == null)
        {
            return "n/a";
        }

        var parseText = "";
        switch (price)
        {
            case >= 2000:
                parseText = "2000-9999";
                break;
            case >= 1000:
                parseText = "1000-1999";
                break;
            case >= 500:
                parseText = "500-999";
                break;
            case >= 200:
                parseText = "200-499";
                break;
            case >= 100:
                parseText = "100-199";
                break;
            case >= 0:
                parseText = "0-99";
                break;
        }

        return parseText;
    }
}