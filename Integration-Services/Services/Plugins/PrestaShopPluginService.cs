using System.Globalization;
using System.Net;
using System.Net.Http.Headers;
using System.Text.Json;
using AutoMapper;
using Integration.Models.WooCommerce;
using Integration.Services.Plugins.Integration;
using Integration.Services.Plugins.ShopifyIntegration;
using Marlin_OS_Integration_API.Models.Order;
using Merchant_Services.Models.ModelsDal.Merchant;
using Shared.Dto.OrderDto;
using Shared.Dto.Partner;
using Shared.Dto.PrestaShop;
using Shared.Dto.Webshop;
using Shared.Models;
using Shared.Models.Merchant;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace Marlin_OS_Integration_API.Services.Plugins.WoocommerceIntegration;

public class PrestaShopPluginService(
    ILogger logger,
    IIntegrationService integrationService,
    IMapper iMapper,
    IMerchantService merchantService)
    : IPluginService
{
    public async Task<ResponseDto> WebshopOrderHookAsync(List<OrderEventDto> orderDataDto,
        string apiKey)
    {
        foreach (var elasticOrder in orderDataDto)
        {
            if (elasticOrder.Shop_order!.Status != null)
            {
                elasticOrder.Shop_order.IsCanceled = IsOrderCancel(elasticOrder.Shop_order.Status);
            }
        }

        var orderData = iMapper.Map<List<OrderEventDto>, List<ElasticOrderEvent>>(orderDataDto);
        return await integrationService.AddOrderAsync(orderData, apiKey, false).ConfigureAwait(false);
    }

    public async Task<ResponseDto> UpdateAsync(Merchant merchant, PartnerIdAndNameDto partner,
        int lookBackDaysOrder, int lookBackDaysProduct)
    {
        var url = merchant.Url;
        var apiKey = merchant.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey).Value;
        var userKey =
            merchant.MerchantMeta.SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUserKey);

        if (userKey == null)
        {
            await RequestUrl(
                $"{url}?apiKeyViabillMarketing={apiKey}&step=setup");
        }
        else
        {
            if (lookBackDaysProduct != 0)
            {
                //Update webshopData
                await UpdateProductData(merchant, lookBackDaysProduct, userKey.Value)
                    .ConfigureAwait(false);
            }

            if (lookBackDaysOrder != 0)
            {
                //Update orderData
                await UpdateOrderData(merchant, lookBackDaysProduct, userKey.Value)
                    .ConfigureAwait(false);
            }
        }

        return new ResponseDto
        {
            Success = true
        };
    }

    private async Task UpdateOrderData(Merchant webshop, int lookBackDaysProduct, string userKeyValue)
    {
        throw new NotImplementedException();
    }

    private async Task UpdateProductData(Merchant webshop, int lookBackDays,
        string apiKey)
    {
        var baseUrl = $"{webshop.Url}/api/";
        var page = 0;
        var pageSize = 100;
        var moreProducts = true;
        var error = false;
        var daysAgo = DateTime.UtcNow.AddDays(-lookBackDays);
        //daysAgo = DateTime.UtcNow.AddDays(-600);

        Console.WriteLine("Fetching Products..");
        using (HttpClient httpClient = new HttpClient())
        {
            while (moreProducts)
            {
                page++;
                Console.Write($"\rProcessing page {page}...");

                httpClient.BaseAddress = new Uri(baseUrl);
                var byteArray = System.Text.Encoding.ASCII.GetBytes(apiKey + ":");
                httpClient.DefaultRequestHeaders.Authorization =
                    new AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));

                //Fix bot detection
                httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

                HttpResponseMessage response = await httpClient.GetAsync($"products?display=full&output_format=JSON");
                string content = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode)
                {
                    var responseCategories = await httpClient.GetAsync($"categories?display=full&output_format=JSON");
                    string contentCategories = await responseCategories.Content.ReadAsStringAsync();
                    var categoryData = JsonSerializer.Deserialize<PrestashopCategory>(contentCategories);

                    //Products
                    var productData = JsonSerializer.Deserialize<PrestashopProduct>(content);
                    var products = productData.products;

                    if (products?.Count < pageSize)
                    {
                        moreProducts = false;
                    }

                    foreach (var prestashopProduct in products)
                    {
                        var updateDate = DateTime.Parse(prestashopProduct.date_upd);
                        if (updateDate >= daysAgo)
                        {
                            var createdDate = DateTime.Parse(prestashopProduct.date_add);
                            //Price
                            decimal.TryParse(prestashopProduct.price.ToString(), NumberStyles.AllowDecimalPoint,
                                CultureInfo.InvariantCulture, out var price);

                            //Product
                            var product = new Product
                            {
                                MerchantProductId = prestashopProduct.id.ToString(),
                                Name = prestashopProduct.name,
                                Active = prestashopProduct.state == "1",
                                Status = prestashopProduct.state,
                                Description = prestashopProduct.description,
                                ShortDescription = prestashopProduct.description_short,
                                Permalink =
                                    $"{webshop.Url}/{prestashopProduct.id}/{prestashopProduct.link_rewrite}.html",
                                Price = price,
                                RegularPrice = price,
                                Sku = prestashopProduct.reference,
                                LastModifiedDate = DateTime.UtcNow,
                                MerchantCreatedDate = createdDate,
                                MerchantModifiedDate = updateDate,
                                StockQuantity = null,
                                //ProductImages = JsonSerializer.Serialize(productImages),
                                ProductImages = "",
                                Categories = ""
                            };

                            //Categories
                            foreach (var categoryPrestashop in prestashopProduct.associations.categories)
                            {
                                var categoryPrestashop1 =
                                    categoryData.categories.FirstOrDefault(
                                        a => a.id.ToString() == categoryPrestashop.id);
                                if (categoryPrestashop1 != null)
                                {
                                    product.Categories += categoryPrestashop1.name + ",";
                                }
                            }

                            product.Categories = product.Categories.TrimEnd(',');

                            //Images
                            var productImages = new List<ProductImageDto>();
                            var count = 1;
                            foreach (var categoryPrestashop in prestashopProduct.associations.images)
                            {
                                productImages.Add(new ProductImageDto
                                {
                                    Src = $"{webshop.Url}/img/p/{prestashopProduct.id}/{categoryPrestashop.id}.jpg"
                                });
                                count++;
                            }

                            product.ProductImages = JsonSerializer.Serialize(productImages);

                            Console.WriteLine();
                            //images/products/{productId}/{imageId}

                            //Variants
                            /*if (wooCommerceProduct.variations.Count > 0)
                            {
                                uriBuilder = new UriBuilder($"{baseUrl}products/{wooCommerceProduct.id}/variations");
                                query = System.Web.HttpUtility.ParseQueryString(uriBuilder.Query);
                                query["consumer_key"] = userKey;
                                query["consumer_secret"] = userSecret;
                                query["per_page"] = pageSize.ToString();
                                uriBuilder.Query = query.ToString();
                                url = uriBuilder.ToString();

                                //Variants
                                try
                                {
                                    HttpResponseMessage varResponse = await httpClient.GetAsync(url);
                                    string variationsJson = await varResponse.Content.ReadAsStringAsync();

                                    var variants = JsonSerializer.Deserialize<List<WooCommerceVariant>>(variationsJson);
                                    foreach (var wooCommerceVariant in variants)
                                    {
                                        //Price
                                        decimal.TryParse(wooCommerceVariant.price.ToString(),
                                            NumberStyles.AllowDecimalPoint,
                                            CultureInfo.InvariantCulture, out var priceVariant);
                                        decimal.TryParse(wooCommerceVariant.regular_price.ToString(),
                                            NumberStyles.AllowDecimalPoint,
                                            CultureInfo.InvariantCulture, out var regularPriceVariant);

                                        //Images
                                        var variantImages = new List<ProductImageDto>();
                                        if (wooCommerceVariant.image != null)
                                        {
                                            variantImages.Add(new()
                                            {
                                                Src = wooCommerceVariant.image.src
                                            });
                                        }

                                        product.Variants.Add(new Variant
                                        {
                                            MerchantProductId = wooCommerceVariant.id.ToString(),
                                            WebshopParentProductId = wooCommerceProduct.id.ToString(),
                                            Name = wooCommerceProduct.name,
                                            Active = wooCommerceVariant.status == "publish",
                                            Status = wooCommerceVariant.status,
                                            Description = wooCommerceVariant.description,
                                            Permalink = wooCommerceVariant.permalink,
                                            Price = priceVariant,
                                            RegularPrice = regularPriceVariant,
                                            Sku = wooCommerceVariant.sku,
                                            CreatedDate = wooCommerceVariant.date_created_gmt,
                                            LastModifiedDate = wooCommerceVariant.date_modified_gmt,
                                            StockQuantity = wooCommerceVariant.stock_quantity,
                                            ProductImages = JsonSerializer.Serialize(variantImages)
                                        });
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine();
                                }
                            }*/

                            //webshop.Products.Add(product);
                        }
                    }

                    Console.Write($"\rProcessed page {page}.");
                }
                else
                {
                    logger.ForContext("service_name", GetType().Name)
                        .Error(
                            "Error while updating products for woocommerce WebShop {webShopName} with {lookBackDays} days lookBack and response: {response}",
                            webshop.Name, lookBackDays, content);
                    Console.WriteLine($"Error occurred: {response.StatusCode}");
                    moreProducts = false;
                    error = true;
                }
            }
        }

        if (!error)
        {
            Console.WriteLine("\nAll products fetched and processed.");
            webshop.Products = webshop.Products.DistinctBy(a => a.MerchantProductId).ToList();
            await integrationService.SynchronizeWebshopDataAsync(webshop).ConfigureAwait(false);
        }
    }

    /*public async Task<ResponseDto> UpdateWebShopDataAsync(Merchant webshop)
    {
        var webshopData = await _merchantService.ValidateOrderApiKey(webshop.ApiKey);
        if (webshopData != null)
        {
            webshop.Name = webshopData.Name;
            webshop.Type = webshopData.Type;
            return await _integrationService.SynchronizeWebshopDataAsync(webshop).ConfigureAwait(false);
        }

        return new ResponseDto { Success = false, Message = "False" };
    }*/

    /*public async Task<ResponseDto> UpdateWebShopOrderDataAsync(List<OrderEventDto> orders, string apiKey)
    {
        foreach (var elasticOrder in orders)
        {
            if (elasticOrder.Shop_order!.Status != null)
            {
                elasticOrder.Shop_order.IsCanceled = IsOrderCancel(elasticOrder.Shop_order.Status);
            }
        }

        var orderData = _iMapper.Map<List<OrderEventDto>, List<ElasticOrderEvent>>(orders);
        return await _integrationService.AddOrderAsync(orderData, apiKey).ConfigureAwait(false);
    }*/

    private async Task RequestUrl(string url)
    {
        var httpClient = new HttpClient();
        using var response = await httpClient.GetAsync(url);
        if (response.StatusCode != HttpStatusCode.OK)
        {
            logger.ForContext("service_name", GetType().Name).Error(
                $"Error triggering Prestashop webshop data response: {url} with status {response.StatusCode} " +
                $"headers: {JsonSerializer.Serialize(httpClient.DefaultRequestHeaders)} Content: {response.Content}");
        }
    }

    private bool IsOrderCancel(string orderStatus)
    {
        switch (orderStatus.ToLower())
        {
            case "cancelled":
            case "canceled":
            case "failed":
            case "refunded":
                return true;
            default:
                return false;
        }
    }

    public async Task<ResponseDto> CreateApiKeyAsync(PrestashopApiKey prestashopApiKey)
    {
        var webshop = await merchantService.GetByApikeyAsync(prestashopApiKey.ApiKey);
        if (webshop != null)
        {
            await merchantService.AddOrUpdateMerchantMeta(webshop, MerchantMetaTypeNames.ApiUserKey,
                prestashopApiKey.GeneratedKey);
            await merchantService.UpdateMerchantAsync(webshop);
            return new ResponseDto
            {
                Success = true,
                Message = "Updated keys"
            };
        }

        return new ResponseDto()
        {
            Success = false,
            Message = "Can't find webshop by api key"
        };
    }
}