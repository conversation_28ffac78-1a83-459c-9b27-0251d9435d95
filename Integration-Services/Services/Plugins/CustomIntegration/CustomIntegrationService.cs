using System.Net;
using System.Text;
using Integration.Models;
using Integration.Models.Elastic.Behavior;
using Integration.Models.Elastic.Order;
using Integration.Services.Plugins.Integration;
using Integration.Services.Static;
using Marlin_OS_Integration_API.Models.Order;
using Partner_Services.Services.PartnerDataSync;
using RabbitMQ.Client;
using Shared.Dto.Custom.Event;
using Shared.Dto.Custom.Order;
using Shared.Dto.CustomDto;
using Shared.Elastic.Behavior;
using Shared.Elastic.Models.Behavior;
using Shared.Elastic.Order;
using Shared.Models;
using Shared.Models.Merchant;
using Shared.Services;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Marlin_OS_Integration_API.Services.Plugins.CustomIntegration;

public class CustomIntegrationService(
    IIntegrationService integrationService,
    IMerchantService merchantService,
    IPartnerDataSyncService partnerDataSyncService)
    : ICustomIntegrationService
{
    public async Task<CustomResponse> AddEventAsync(CustomEventDto customEvent, string apiKey)
    {
        var merchant = await merchantService.ValidateAsync(apiKey);
        if (merchant == null)
        {
            return new CustomResponse
            {
                Message =
                    "Unauthorized - The API key you have provided is not valid. Please double-check the key and try again. If you continue to experience issues, please contact our support team for assistance.",
                Status = StatusCode.Unauthorized
            };
        }

        /*var searchProduct = new SearchProductDto
        {
            WebshopId = merchant.Id,
            Sku = customEvent.ProductSku
        };
        var product = await merchantService.SearchProductAsync(searchProduct).ConfigureAwait(false);
        if (product?.InternalProductId == null)
        {
            return new CustomResponse
            {
                Message =
                    $"Product not found - The system was unable to locate a product with the specified SKU: '{customEvent.ProductSku}'. Please double-check the SKU and try again. If you continue to experience issues, please contact our support team for assistance.",
                Status = StatusCode.BadRequest
            };
        }*/

        //Validate eventType
        var eventTypeMappings = new Dictionary<string, string>(StringComparer.CurrentCultureIgnoreCase)
        {
            {"addcart", "AddCart"},
            {"removecart", "RemoveCart"},
            {"addwishlist", "AddWishlist"},
            {"removewishlist", "RemoveWishlist"},
            {"productlook", "ProductLook"}
        };

        if (eventTypeMappings.TryGetValue(customEvent.EventType, out var mappedEventType))
        {
            customEvent.EventType = mappedEventType;
        }
        else
        {
            return new CustomResponse
            {
                Message =
                    $"Invalid event type - The provided event type: '{customEvent.EventType}', is not recognized by the system. Please check the documentation for a list of valid event types and try again. If you continue to experience issues, please contact our support team for assistance.",
                Status = StatusCode.BadRequest
            };
        }

        //Validate ip is correct format
        customEvent.ClientIpAddress =
            IPAddress.TryParse(customEvent.ClientIpAddress, out _) ? customEvent.ClientIpAddress : null;
        if (customEvent.ClientIpAddress != null && !Validate.ValidateIPv4(customEvent.ClientIpAddress) && !Validate.ValidateIPv4(customEvent.ClientIpAddress))
        //if (customEvent.ClientIpAddress != null)
        {
            return new CustomResponse
            {
                Message =
                    $"Invalid IP address format - The provided IP address: '{customEvent.ClientIpAddress}', is not in the correct format. The expected format is 0.0.0.0. Please double-check the IP address and try again. If you continue to experience issues, please contact our support team for assistance.",
                Status = StatusCode.BadRequest
            };
        }

        var behaviorEvent = new BehaviorEvent()
        {
            ApiKey = apiKey,
            EventDate = customEvent.EventDate,
            ProductSku = customEvent.ProductSku,
            Price = customEvent.Price,
            Email = customEvent.Email,
            Cookie = customEvent.Cookie,
            Ip = customEvent.ClientIpAddress,
            UserAgent = customEvent.UserAgent,
            MerchantType = MerchantTypes.Custom,
            EventType = customEvent.EventType
        };
        
        await integrationService.AddBehaviorEventForProcessingAsync(behaviorEvent).ConfigureAwait(false);

        //Cookie
        /*var sessionId = "n/a";
        var email = "n/a";
        var viaAds = "n/a";

        var elasticEvent = new ElasticBehaviorEvent
        {
            Event_date = customEvent.EventDate,
            Event_received = DateTime.UtcNow,
            url = new ElasticBehaviorEventUrl
            {
                full = product.PermaLink
            },
            Customer = new ElasticBehaviorEventCustomer
            {
                Email = email.ToLower(),
                ViaAds = viaAds,
                Session_id = sessionId,
            },
            client = new ElasticBehaviorEventClient
            {
                ip = customEvent.ClientIpAddress ?? "0.0.0.0"
            },
            user_agent = new ElasticBehaviorEventUserAgent
            {
                original = customEvent.UserAgent ?? "n/a",
                device = new ElasticBehaviorEventDevice
                {
                    name = customEvent.UserAgent ?? "n/a"
                },
                name = customEvent.UserAgent ?? "n/a",
                version = customEvent.UserAgent ?? "n/a"
            },
            Shop_event = new ElasticBehaviorEventShopEvent
            {
                Event_type = customEvent.EventType,
                Price = customEvent.Price ?? product.Price,
                Price_range = PriceConverter.ConvertPrice(customEvent.Price ?? product.Price),
                Product_internal_id = product.InternalProductId,
                Product_sku = customEvent.ProductSku,
                Webshop_id = merchant.Id.ToString(),
                Webshop_name = merchant.Name
            },
            Plugin = new ElasticBehaviorEventPlugin
            {
                Name = "Custom",
                Version = "1.0.1"
            }
        };

        try
        {
            using (var publishChannel = rabbitConnection.CreateModel())
            {
                var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticEvent));

                publishChannel.BasicPublish(exchange: "customer",
                    routingKey: "customer_pageevent",
                    basicProperties: null,
                    body: actionBody);
            }
        }
        catch (Exception ex)
        {
            // Log this but do not fail.
            logger.ForContext("service_name", GetType().Name).Warning(
                "{event} sending to {component} exchange '{exchange}' with routing key '{routingKey}'",
                "Failed", "RabbitMQ", "customer", "customer_pageevent");
        }*/

        return new CustomResponse {Status = StatusCode.Ok, Message = "Success"};
    }

    public async Task<CustomResponse> AddEventAsync(CustomJavascriptEvenDto customJavascriptEvenDto)
    {
        var merchant = await merchantService.ValidateAsync(customJavascriptEvenDto.Key ?? "");
        if (merchant == null)
        {
            return new CustomResponse
            {
                Message =
                    $"Error validating api key",
                Status = StatusCode.Unauthorized
            };
        }

        var behaviorEvent = new BehaviorEvent()
        {
            ApiKey = customJavascriptEvenDto.Key,
            EventDate = DateTime.UtcNow,
            ProductSku = customJavascriptEvenDto.Sku,
            Email = customJavascriptEvenDto.Email,
            ProductId = customJavascriptEvenDto.ProductId,
            Cookie = customJavascriptEvenDto.EmailGuid,
            Session = customJavascriptEvenDto.Session,
            Url = customJavascriptEvenDto.Url,
            MerchantId = merchant.Id.ToString()
        };
        
        await integrationService.AddBehaviorEventForProcessingAsync(behaviorEvent).ConfigureAwait(false);
        
        /*var product = await merchantService.SearchProductAsync(new SearchProductDto
        {
            WebshopId = webshop.Id,
            ProductId = customJavascriptEvenDto.ProductId,
            VariantId = customJavascriptEvenDto.ProductId,
            Sku = customJavascriptEvenDto.Sku
        }).ConfigureAwait(false);

        if (product?.InternalProductId == null)
        {
            return new CustomResponse
            {
                Message =
                    $"Product does not exists",
                Status = StatusCode.BadRequest
            };
        }

        var elasticEvent = new ElasticBehaviorEvent
        {
            Event_date = DateTime.UtcNow,
            Event_received = DateTime.UtcNow,
            url = new ElasticBehaviorEventUrl
            {
                full = customJavascriptEvenDto.Url
            },
            Customer = new ElasticBehaviorEventCustomer
            {
                Email = customJavascriptEvenDto.Email?.ToLower() ?? "n/a",
                ViaAds = customJavascriptEvenDto.EmailGuid ?? "n/a",
                Session_id = customJavascriptEvenDto.Session
            },
            client = new ElasticBehaviorEventClient
            {
                ip = "0.0.0.0"
            },
            user_agent = new ElasticBehaviorEventUserAgent
            {
                original = "n/a",
                device = new ElasticBehaviorEventDevice
                {
                    name = "n/a"
                },
                name = "n/a",
                version = "n/a"
            },
            Shop_event = new ElasticBehaviorEventShopEvent
            {
                Event_type = customJavascriptEvenDto.EventType,
                Price = product.Price,
                Price_range = PriceConverter.ConvertPrice(product.Price),
                Product_internal_id = product.InternalProductId,
                Product_sku = product.Sku,
                Webshop_id = webshop.Id.ToString(),
                Webshop_name = webshop.Name
            },
            Plugin = new ElasticBehaviorEventPlugin
            {
                Name = "CustomJavascript",
                Version = "1.0.0"
            }
        };

        try
        {
            using (var publishChannel = rabbitConnection.CreateModel())
            {
                var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticEvent));

                publishChannel.BasicPublish(exchange: "customer",
                    routingKey: "customer_pageevent",
                    basicProperties: null,
                    body: actionBody);
            }
        }
        catch (Exception ex)
        {
            // Log this but do not fail.
            logger.ForContext("service_name", GetType().Name).Warning(
                ex,
                "{event} sending to {component} exchange '{exchange}' with routing key '{routingKey}'",
                "Failed", "RabbitMQ", "customer", "customer_pageevent");
        }*/

        return new CustomResponse {Status = StatusCode.Ok, Message = "Success"};
    }

    public async Task<CustomResponse> AddOrderAsync(List<CustomOrderDto> webshopOrders, string apiKey)
    {
        var merchant = await merchantService.ValidateOrderApiKey(apiKey).ConfigureAwait(false);
        if (merchant == null)
        {
            return new CustomResponse
            {
                Message =
                    "Unauthorized - The API key you have provided is not valid. Please double-check the key and try again. If you continue to experience issues, please contact our support team for assistance.",
                Status = StatusCode.Unauthorized
            };
        }
        
        var partner = await partnerDataSyncService.GetPartnerByIdAsync(merchant.FkPartnerId).ConfigureAwait(false);

        List<ElasticOrderEvent> orders = [];
        foreach (var webshopOrder in webshopOrders)
        {
            //Validate order
            if (string.IsNullOrEmpty(webshopOrder.Email) || !Validate.Email(webshopOrder.Email))
            {
                return new CustomResponse
                {
                    Message =
                        $"Invalid email format - The provided email address: '{webshopOrder.Email}', is not in the correct format. Please double-check the email address and try again. If you continue to experience issues, please contact our support team for assistance.",
                    Status = StatusCode.BadRequest
                };
            }

            if (webshopOrder.TotalPriceWithTax == 0)
            {
                return new CustomResponse
                {
                    Message =
                        "Invalid total price - The calculated total price with tax cannot be 0. Please double-check the input values and try again. If you continue to experience issues, please contact our support team for assistance.",
                    Status = StatusCode.BadRequest
                };
            }

            if (webshopOrder.TotalPriceWithOutTax == 0)
            {
                return new CustomResponse
                {
                    Message =
                        $"Invalid total price - The calculated total price without tax cannot be 0. Please double-check the input values and try again. If you continue to experience issues, please contact our support team for assistance.",
                    Status = StatusCode.BadRequest
                };
            }

            var orderItems = new List<ElasticOrderEventItem>();
            if (webshopOrder.Items != null)
            {
                foreach (var customOrderItem in webshopOrder.Items)
                {
                    orderItems.Add(new ElasticOrderEventItem
                    {
                        Price = customOrderItem.UnitPrice,
                        Total_price = customOrderItem.TotalPriceWithOutTax,
                        Total_price_tax_included = customOrderItem.TotalPriceWithTax,
                        Total_price_tax = customOrderItem.TotalPriceWithTax - customOrderItem.TotalPriceWithOutTax,
                        Quantity = customOrderItem.Quantity,
                        Name = customOrderItem?.Name ?? "n/a",
                        Sku = customOrderItem?.Sku ?? "n/a",
                        Price_range = PriceConverter.ConvertPrice(customOrderItem.UnitPrice),
                    });
                }
            }

            var order = new ElasticOrderEvent
            {
                Order_date = webshopOrder.CreatedGmt,
                Event_received = DateTime.UtcNow,
                Customer = new ElasticOrderEventCustomer
                {
                    Email = webshopOrder.Email.ToLower()
                },
                Shop_order = new ElasticOrderEventShopOrder
                {
                    Last_modified = webshopOrder.LastModifiedGmt,
                    Order_number = webshopOrder.OrderNumber,
                    Currency = webshopOrder.CurrencyIsoCode,
                    Status = webshopOrder.Status,
                    IsCanceled = webshopOrder.IsCanceled,
                    Total_price_shipping = webshopOrder.TotalShippingPrice,
                    Total_price = webshopOrder.TotalPriceWithOutTax,
                    Total_price_tax_included = webshopOrder.TotalPriceWithTax,
                    Total_price_tax = webshopOrder.TotalPriceWithTax - webshopOrder.TotalPriceWithOutTax,
                    Billing_address = new ElasticOrderEventAddress
                    {
                        Email = webshopOrder.Billing?.BillingEmail?.ToLower() ?? "",
                        Address1 = webshopOrder.Billing?.BillingAddress ?? "",
                        City = webshopOrder.Billing?.BillingCity ?? "",
                        Country = webshopOrder.Billing?.BillingCountry ?? "",
                        First_name = webshopOrder.Billing?.BillingFirstName ?? "",
                        Last_name = webshopOrder.Billing?.BillingLastName ?? "",
                        Phone_number = webshopOrder.Billing?.BillingPhoneNumber ?? "",
                        State = webshopOrder.Billing?.BillingState ?? "",
                        Zip_code = webshopOrder.Billing?.BillingZipCode ?? ""
                    },
                    Shipping_address = new ElasticOrderEventAddressShipping
                    {
                        Email = webshopOrder.Shipping?.ShippingEmail?.ToLower() ?? "",
                        First_name = webshopOrder.Shipping?.ShippingFirstName ?? "",
                        Last_name = webshopOrder.Shipping?.ShippingLastName ?? "",
                        Phone_number = webshopOrder.Shipping?.ShippingPhoneNumber ?? ""
                    },
                    Order_items = orderItems
                },
                Partner = new ElasticOrderEventPartner
                {
                    Id = partner?.Id.ToString() ?? "n/a",
                    Name = partner?.Name ?? "n/a"
                },
            };
            orders.Add(order);
        }

        await integrationService.AddOrderAsync(orders, apiKey, true, merchant).ConfigureAwait(false);
        return new CustomResponse {Status = StatusCode.Ok, Message = "Success"};
    }
}