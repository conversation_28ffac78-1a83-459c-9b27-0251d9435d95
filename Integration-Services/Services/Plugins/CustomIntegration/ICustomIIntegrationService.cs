using Shared.Dto.Custom.Event;
using Shared.Dto.Custom.Order;
using Shared.Dto.CustomDto;
using Shared.Models;

namespace Marlin_OS_Integration_API.Services.Plugins.CustomIntegration;

public interface ICustomIntegrationService
{
    Task<CustomResponse> AddEventAsync(CustomEventDto eEvent, string apiKey);
    Task<CustomResponse> AddEventAsync(CustomJavascriptEvenDto customJavascriptEvenDto);
    Task<CustomResponse> AddOrderAsync(List<CustomOrderDto> orders, string apiKey);
}