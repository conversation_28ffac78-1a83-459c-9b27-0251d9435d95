using System.Globalization;
using System.Net;
using System.Text.Json;
using AutoMapper;
using Integration.Models.Elastic.Order;
using Integration.Models.WooCommerce;
using Integration.Services.Plugins.Integration;
using Integration.Services.Plugins.ShopifyIntegration;
using Marlin_OS_Integration_API.Models.Order;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.Extensions.Primitives;
using Shared.Dto.BehaviorDto;
using Shared.Dto.OrderDto;
using Shared.Dto.Partner;
using Shared.Dto.Webshop;
using Shared.Dto.WooCommerce;
using Shared.Elastic.Order;
using Shared.Models;
using Shared.Models.Merchant;
using Shared.Services;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace Marlin_OS_Integration_API.Services.Plugins.WoocommerceIntegration;

public class WoocommercePluginService : IPluginService
{
    private readonly ILogger _logger;
    private readonly IMapper _iMapper;
    private readonly IIntegrationService _integrationService;
    private readonly IMerchantService _merchantService;

    public WoocommercePluginService(ILogger logger, IIntegrationService integrationService,
        IMerchantService merchantService, IMapper iMapper)
    {
        _integrationService = integrationService;
        _iMapper = iMapper;
        _logger = logger;
        _merchantService = merchantService;
    }

    public async Task<ResponseDto> WebhookOrderCreateAsync(List<OrderEventDto> orderDataDto,
        string apiKey)
    {
        foreach (var elasticOrder in orderDataDto)
        {
            if (elasticOrder.Shop_order!.Status != null)
            {
                elasticOrder.Shop_order.IsCanceled = IsOrderCancel(elasticOrder.Shop_order.Status);
            }
        }

        var orderData = _iMapper.Map<List<OrderEventDto>, List<ElasticOrderEvent>>(orderDataDto);
        return await _integrationService.AddOrderAsync(orderData, apiKey, false).ConfigureAwait(false);
    }

    public async Task<ResponseDto> WebhookProductDeleteASync(
        WooCommerceWebhookProductDelete wooCommerceWebhookProductDelete)
    {
        var merchant = await _merchantService.GetByApikeyAsync(wooCommerceWebhookProductDelete.ApiKey);
        if (merchant != null)
        {
            await _merchantService.DeactivateProduct(wooCommerceWebhookProductDelete.ProductId, "", merchant?.Id);
        }

        return new ResponseDto {Success = true, Message = "Success"};
    }

    public async Task<ResponseDto> UpdateAsync(Merchant merchant, PartnerIdAndNameDto partner,
        int lookBackDaysOrder, int lookBackDaysProduct)
    {
        Guid random = Guid.NewGuid();
        var url = merchant.Url;
        var apiKey = merchant.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey).Value;

        var partnerPath = merchant.FkPartnerId == 52876 ? "viaads" : "happyads";
        var partnerParam = merchant.FkPartnerId == 52876 ? "apiKeyViabillMarketing" : "apiKeyValyrionMarketing";
        //Old
        var productUrlDebug =
            $"{url}?{partnerParam}={apiKey}&event=webshop&lookbackdays={lookBackDaysProduct}&random={random}&debug=tEBLZTqKDLzhEjc2Kw9mqu5vrJUB";
        var orderUrlDebug =
            $"{url}?{partnerParam}={apiKey}&event=orders&lookbackdays={lookBackDaysOrder}&random={random}&debug=tEBLZTqKDLzhEjc2Kw9mqu5vrJUB";

        var userKey =
            merchant.MerchantMeta.SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUserKey);
        var userSecret =
            merchant.MerchantMeta.SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUserSecret);

        //If no access key exists try to get one
        var accessUrl = $"{url}/wp-json/{partnerPath}/v1/setup?{partnerParam}={apiKey}";
        if (userKey == null)
        {
            await RequestUrl(accessUrl, merchant);
        }

        if (userKey != null && userSecret != null)
        {
            if (lookBackDaysProduct != 0)
            {
                //Update webshopData
                await UpdateProductData(merchant, lookBackDaysProduct, userKey.Value, userSecret.Value)
                    .ConfigureAwait(false);
            }

            if (lookBackDaysOrder != 0)
            {
                //Update orderData
                await UpdateOrderData(merchant, partner, lookBackDaysOrder, userKey.Value, userSecret.Value)
                    .ConfigureAwait(false);
            }
        }

        return new ResponseDto
        {
            Success = true
        };
    }

    private async Task UpdateOrderData(Merchant merchant, PartnerIdAndNameDto partner, int lookBackDays,
        string userKey, string userSecret)
    {
        var orders = new List<ElasticOrderEvent>();
        var baseUrl = $"{merchant.Url}/wp-json/wc/v3/";
        var page = 0;
        var pageSize = 100;
        var moreOrders = true;
        var error = false;
        using (HttpClient httpClient = new HttpClient())
        {
            while (moreOrders)
            {
                page++;
                var uriBuilder = new UriBuilder($"{baseUrl}orders");
                var query = System.Web.HttpUtility.ParseQueryString(uriBuilder.Query);
                query["consumer_key"] = userKey;
                query["consumer_secret"] = userSecret;
                query["page"] = page.ToString();
                query["per_page"] = pageSize.ToString();
                query["modified_after"] = $"{DateTime.UtcNow.AddDays(-lookBackDays):yyyy-MM-dd}T00:00:00";

                uriBuilder.Query = query.ToString();
                var url = uriBuilder.ToString();
                //Fix bot detection
                httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

                //Single order lookup
                //https://tomorrowsdesign.dk/wp-json/wc/v3/orders?consumer_key=ck_078a9fc1d14045adabcd3f21d0d586c4f52d34b5&consumer_secret=cs_376dad5654c8151106acec72ac77e8674824af18&include=5704444115100
                var singleOrderDebug =
                    $"{baseUrl}/orders?consumer_key={userKey}&consumer_secret={userSecret}&include=ORDERID";

                HttpResponseMessage response = await httpClient.GetAsync(url);
                string content = await response.Content.ReadAsStringAsync();
                //There is a chance there are a error header in the request
                int indexOfFirstBracket = content.IndexOf('[');
                if (indexOfFirstBracket != -1)
                {
                    content = content.Substring(indexOfFirstBracket);
                }

                if (response.IsSuccessStatusCode)
                {
                    try
                    {
                        //Orders
                        var wooCommerceOrders = JsonSerializer.Deserialize<List<WooCommerceOrder>>(content);
                        if (wooCommerceOrders?.Count < pageSize)
                        {
                            moreOrders = false;
                        }

                        foreach (var wooCommerceOrder in wooCommerceOrders)
                        {
                            decimal.TryParse(wooCommerceOrder.shipping_total,
                                NumberStyles.AllowDecimalPoint,
                                CultureInfo.InvariantCulture, out var shipping);

                            var order = new ElasticOrderEvent
                            {
                                Plugin = new ElasticOrderEventPlugin
                                {
                                    Name = "Woocommerce",
                                    Version = "2.0.1"
                                },
                                Event_received = DateTime.UtcNow,
                                Customer = new ElasticOrderEventCustomer
                                {
                                    Email = wooCommerceOrder.billing.email,
                                },
                                client = new ElasticOrderEventClient
                                {
                                    ip = wooCommerceOrder.customer_ip_address
                                },
                                Discounts = new List<ElasticOrderEventDiscount>(),
                                Shop_order = new ElasticOrderEventShopOrder
                                {
                                    Billing_address = new ElasticOrderEventAddress
                                    {
                                        Email = wooCommerceOrder.billing.email,
                                        Address1 = wooCommerceOrder.billing.address_1,
                                        City = wooCommerceOrder.billing.city,
                                        Country = wooCommerceOrder.billing.country,
                                        First_name = wooCommerceOrder.billing.first_name,
                                        Last_name = wooCommerceOrder.billing.last_name,
                                        Phone_number = wooCommerceOrder.billing.phone,
                                        State = wooCommerceOrder.billing.state,
                                        Zip_code = wooCommerceOrder.billing.postcode
                                    },
                                    Shipping_address = new ElasticOrderEventAddressShipping
                                    {
                                        Email = wooCommerceOrder.billing.email,
                                        First_name = wooCommerceOrder.shipping.first_name,
                                        Last_name = wooCommerceOrder.shipping.last_name,
                                        Phone_number = wooCommerceOrder.shipping.phone
                                    },
                                    Status = wooCommerceOrder.status,
                                    IsCanceled = IsOrderCancel(wooCommerceOrder.status),
                                    Currency = wooCommerceOrder.currency,
                                    Total_price = 0,
                                    Total_price_tax = 0,
                                    Total_price_tax_included = 0,
                                    Total_price_shipping = shipping,
                                    Order_items = new List<ElasticOrderEventItem>(),
                                    Last_modified = wooCommerceOrder.date_modified_gmt,
                                    Order_number = wooCommerceOrder.id.ToString(),
                                    Vat_percentage = wooCommerceOrder.tax_lines.FirstOrDefault()?.rate_percent ?? 25,
                                    Webshop_id = merchant.Id.ToString(),
                                    Webshop_name = merchant.Name,
                                },
                                Order_date = wooCommerceOrder.date_created_gmt,
                                user_agent = new ElasticOrderEventUserAgent
                                {
                                    name = "n/a",
                                    device = new ElasticOrderEventDevice
                                    {
                                        name = "n/a"
                                    },
                                    original = "n/a",
                                    version = "n/a"
                                },
                                Partner = new ElasticOrderEventPartner()
                                {
                                    Id = partner.Id.ToString(),
                                    Name = partner.Name
                                }
                            };

                            //Line items
                            foreach (var lineItem in wooCommerceOrder.line_items)
                            {
                                decimal.TryParse(lineItem.total,
                                    NumberStyles.AllowDecimalPoint,
                                    CultureInfo.InvariantCulture, out var total);
                                decimal.TryParse(lineItem.total_tax,
                                    NumberStyles.AllowDecimalPoint,
                                    CultureInfo.InvariantCulture, out var totalTax);

                                order.Shop_order.Total_price += total;
                                order.Shop_order.Total_price_tax += totalTax;
                                order.Shop_order.Total_price_tax_included =
                                    order.Shop_order.Total_price + order.Shop_order.Total_price_tax;

                                order.Shop_order.Order_items.Add(new ElasticOrderEventItem
                                {
                                    Total_price = total,
                                    Total_price_tax = totalTax,
                                    Total_price_tax_included = total + totalTax,
                                    Name = lineItem.name,
                                    Price = Convert.ToDecimal(lineItem.price),
                                    Sku = lineItem.sku,
                                    Quantity = (long) Math.Floor(lineItem.quantity),
                                    Product_id = lineItem.product_id.ToString(),
                                    Product_variant_id = lineItem.variation_id.ToString(),
                                });
                            }

                            //Refunds
                            foreach (var refund in wooCommerceOrder.refunds)
                            {
                                decimal.TryParse(refund.total,
                                    NumberStyles.AllowDecimalPoint | NumberStyles.AllowLeadingSign,
                                    CultureInfo.InvariantCulture, out var refundAmount);
                                order.Shop_order.Total_price_tax_included += refundAmount;
                                if (order.Shop_order.Total_price_tax_included != null)
                                {
                                    var vat = order.Shop_order.Vat_percentage ?? 25;
                                    var withoutVat =
                                        StaticVariables.CalculatePriceExcludingVat(
                                            order.Shop_order.Total_price_tax_included ?? 0, vat / 100);
                                    order.Shop_order.Total_price = withoutVat;
                                    order.Shop_order.Total_price_tax =
                                        (order.Shop_order.Total_price_tax_included ?? 0) - withoutVat;
                                }
                            }

                            //Coupons
                            foreach (var couponLine in wooCommerceOrder.coupon_lines)
                            {
                                var type = "";
                                decimal.TryParse(couponLine.discount,
                                    NumberStyles.AllowDecimalPoint,
                                    CultureInfo.InvariantCulture, out var discountAmount);

                                switch (couponLine.discount_type)
                                {
                                    case "fixed_cart":
                                        type = "fixed";
                                        break;
                                    case "percent":
                                        type = "percentage";
                                        break;
                                }

                                order.Discounts.Add(new ElasticOrderEventDiscount
                                {
                                    Code = couponLine.code,
                                    Amount = discountAmount,
                                    Type = type
                                });
                            }

                            orders.Add(order);
                        }
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                        throw;
                    }
                }
                else
                {
                    //Check if merchant have failed over x days then ignores errors
                    if (merchant.LastOrderSync.HasValue &&
                        merchant.LastOrderSync.Value >= StaticVariables.StopLoggingAfterSyncError())
                    {
                        _logger.ForContext("service_name", GetType().Name)
                            .Error(
                                "Error while updating orders for woocommerce WebShop {MerchantName} with {lookBackDays} days lookBack and response: {response}",
                                merchant.Name, lookBackDays, content);
                    }

                    Console.WriteLine($"Error occurred: {response.StatusCode}");
                    moreOrders = false;
                    error = true;
                }
            }
        }

        if (orders.Count > 0)
        {
            await _integrationService.AddOrderAsync(orders, "", true, merchant).ConfigureAwait(false);
        }
        else
        {
            //Check if any error happened at sync
            if (!error && merchant.LastOrderSync != null)
            {
                await _merchantService.UpdateLastOrderSyncAsync(merchant);
            }
        }
    }

    private async Task UpdateProductData(Merchant merchant, int lookBackDays,
        string userKey, string userSecret)
    {
        var baseUrl = $"{merchant.Url}/wp-json/wc/v3/";
        var page = 0;
        var pageSize = 100;
        var moreProducts = true;
        var error = false;
        Console.WriteLine("Fetching Products..");
        using (HttpClient httpClient = new HttpClient())
        {
            while (moreProducts)
            {
                page++;
                Console.Write($"\rProcessing page {page}...");
                var uriBuilder = new UriBuilder($"{baseUrl}products");
                var query = System.Web.HttpUtility.ParseQueryString(uriBuilder.Query);
                query["consumer_key"] = userKey;
                query["consumer_secret"] = userSecret;
                query["page"] = page.ToString();
                query["per_page"] = pageSize.ToString();
                query["modified_after"] = $"{DateTime.UtcNow.AddDays(-lookBackDays):yyyy-MM-dd}T00:00:00";

                uriBuilder.Query = query.ToString();
                var url = uriBuilder.ToString();
                //Fix bot detection
                httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

                HttpResponseMessage response = await httpClient.GetAsync(url);
                string content = await response.Content.ReadAsStringAsync();
                //There is a chance there are a error header in the request
                int indexOfFirstBracket = content.IndexOf('[');
                if (indexOfFirstBracket != -1)
                {
                    content = content.Substring(indexOfFirstBracket);
                }

                if (response.IsSuccessStatusCode)
                {
                    //Products
                    var products = JsonSerializer.Deserialize<List<WooCommerceProduct>>(content);
                    if (products?.Count < pageSize)
                    {
                        moreProducts = false;
                    }

                    foreach (var wooCommerceProduct in products)
                    {
                        //Price
                        decimal.TryParse(wooCommerceProduct.price.ToString(), NumberStyles.AllowDecimalPoint,
                            CultureInfo.InvariantCulture, out var price);
                        decimal.TryParse(wooCommerceProduct.regular_price.ToString(), NumberStyles.AllowDecimalPoint,
                            CultureInfo.InvariantCulture, out var regularPrice);

                        //Images
                        var productImages = new List<ProductImageDto>();

                        foreach (var image in wooCommerceProduct.images.Where(a => !a.src.Contains(".gif"))
                                     .Take(StaticVariables.MaxImages))
                        {
                            productImages.Add(new ProductImageDto()
                            {
                                Src = image.src
                            });
                        }

                        var product = new Product
                        {
                            MerchantProductId = wooCommerceProduct.id.ToString(),
                            Name = wooCommerceProduct.name,
                            Active = wooCommerceProduct.status == "publish",
                            Status = wooCommerceProduct.status,
                            Description = wooCommerceProduct.description,
                            ShortDescription = wooCommerceProduct.short_description,
                            Permalink = wooCommerceProduct.permalink,
                            Price = price,
                            RegularPrice = regularPrice,
                            Sku = wooCommerceProduct.sku,
                            LastModifiedDate = DateTime.UtcNow,
                            MerchantCreatedDate = wooCommerceProduct.date_created_gmt,
                            MerchantModifiedDate = wooCommerceProduct.date_modified_gmt,
                            StockQuantity = wooCommerceProduct.stock_quantity,
                            ProductImages = JsonSerializer.Serialize(productImages),
                            Categories = string.Join(",", wooCommerceProduct.categories.Select(c => c.name))
                        };

                        //Variants
                        if (wooCommerceProduct.variations.Count > 0)
                        {
                            uriBuilder = new UriBuilder($"{baseUrl}products/{wooCommerceProduct.id}/variations");
                            query = System.Web.HttpUtility.ParseQueryString(uriBuilder.Query);
                            query["consumer_key"] = userKey;
                            query["consumer_secret"] = userSecret;
                            query["per_page"] = pageSize.ToString();
                            uriBuilder.Query = query.ToString();
                            url = uriBuilder.ToString();

                            //Variants
                            try
                            {
                                HttpResponseMessage varResponse = await httpClient.GetAsync(url);
                                string variationsJson = await varResponse.Content.ReadAsStringAsync();

                                var variants = JsonSerializer.Deserialize<List<WooCommerceVariant>>(variationsJson);
                                foreach (var wooCommerceVariant in variants)
                                {
                                    //Price
                                    decimal.TryParse(wooCommerceVariant.price.ToString(),
                                        NumberStyles.AllowDecimalPoint,
                                        CultureInfo.InvariantCulture, out var priceVariant);
                                    decimal.TryParse(wooCommerceVariant.regular_price.ToString(),
                                        NumberStyles.AllowDecimalPoint,
                                        CultureInfo.InvariantCulture, out var regularPriceVariant);

                                    //Images
                                    var variantImages = new List<ProductImageDto>();
                                    if (wooCommerceVariant.image != null)
                                    {
                                        variantImages.Add(new()
                                        {
                                            Src = wooCommerceVariant.image.src
                                        });
                                    }

                                    product.Variants.Add(new Variant
                                    {
                                        MerchantVariantId = wooCommerceVariant.id.ToString(),
                                        MerchantProductId = wooCommerceProduct.id.ToString(),
                                        Name = wooCommerceProduct.name,
                                        Active = wooCommerceVariant.status == "publish",
                                        Status = wooCommerceVariant.status,
                                        Description = wooCommerceVariant.description,
                                        Permalink = wooCommerceVariant.permalink,
                                        Price = priceVariant,
                                        RegularPrice = regularPriceVariant,
                                        Sku = wooCommerceVariant.sku,
                                        MerchantCreatedDate = wooCommerceVariant.date_created_gmt,
                                        MerchantModifiedDate = wooCommerceVariant.date_modified_gmt,
                                        CreatedDate = wooCommerceVariant.date_created_gmt,
                                        LastModifiedDate = wooCommerceVariant.date_modified_gmt,
                                        StockQuantity = wooCommerceVariant.stock_quantity,
                                        ProductImages = JsonSerializer.Serialize(variantImages)
                                    });
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine();
                            }
                        }

                        merchant.Products.Add(product);
                    }

                    Console.Write($"\rProcessed page {page}.");
                }
                else
                {
                    //Check if merchant have failed over x days then ignores errors
                    if (merchant.LastProductSync.HasValue &&
                        merchant.LastProductSync.Value >= StaticVariables.StopLoggingAfterSyncError())
                    {
                        _logger.ForContext("service_name", GetType().Name)
                            .Error(
                                "Error while updating products for woocommerce WebShop {webShopName} with {lookBackDays} days lookBack and response: {response}",
                                merchant.Name, lookBackDays, content);
                    }

                    Console.WriteLine($"Error occurred: {response.StatusCode}");
                    moreProducts = false;
                    error = true;
                }
            }
        }

        if (!error)
        {
            Console.WriteLine("\nAll products fetched and processed.");
            merchant.Products = merchant.Products.DistinctBy(a => a.MerchantProductId).ToList();
            await _integrationService.SynchronizeWebshopDataAsync(merchant).ConfigureAwait(false);
        }
    }

    public async Task<ResponseDto> UpdateWebShopOrderDataAsync(List<OrderEventDto> orders, string apiKey)
    {
        foreach (var elasticOrder in orders)
        {
            if (elasticOrder.Shop_order!.Status != null)
            {
                elasticOrder.Shop_order.IsCanceled = IsOrderCancel(elasticOrder.Shop_order.Status);
            }
        }

        var orderData = _iMapper.Map<List<OrderEventDto>, List<ElasticOrderEvent>>(orders);
        return await _integrationService.AddOrderAsync(orderData, apiKey).ConfigureAwait(false);
    }

    public async Task<ResponseDto> CreateApiKeyAsync(WooCommerceApiKey wooCommerceApiKey)
    {
        var webshop = await _merchantService.GetByApikeyAsync(wooCommerceApiKey.ApiKey);
        if (webshop != null)
        {
            await _merchantService.AddOrUpdateMerchantMeta(webshop, MerchantMetaTypeNames.ApiUserKey,
                wooCommerceApiKey.ConsumerKey);
            await _merchantService.AddOrUpdateMerchantMeta(webshop, MerchantMetaTypeNames.ApiUserSecret,
                wooCommerceApiKey.ConsumerSecret);
            await _merchantService.UpdateMerchantAsync(webshop);
            return new ResponseDto
            {
                Success = true,
                Message = "Updated keys"
            };
        }

        return new ResponseDto()
        {
            Success = false,
            Message = "Can't find webshop by api key"
        };
    }

    private async Task RequestUrl(string url, Merchant merchant)
    {
        var httpClient = new HttpClient();
        httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Accept",
            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7");
        httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Accept-Encoding", "gzip, deflate, br");
        httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Accept-Language",
            "da-DK,da;q=0.9,en-US;q=0.8,en;q=0.7");
        httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Cache-Control", "max-age=0");
        httpClient.DefaultRequestHeaders.TryAddWithoutValidation("User-Agent",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        using var response = await httpClient.GetAsync(url);
        if (response.StatusCode != HttpStatusCode.OK)
        {
            //Check if merchant have failed over x days then ignores errors
            if (merchant.LastOrderSync.HasValue &&
                merchant.LastOrderSync.Value >= StaticVariables.StopLoggingAfterSyncError())
            {
                _logger.ForContext("service_name", GetType().Name).Error(
                    $"Error triggering WooCommerce webshop data response: {url} with status {response.StatusCode} " +
                    $"headers: {JsonSerializer.Serialize(httpClient.DefaultRequestHeaders)} Content: {response.Content}");
            }

            Console.WriteLine(
                $"Error triggering WooCommerce webshop data response: {url} with status {response.StatusCode} " +
                $"headers: {JsonSerializer.Serialize(httpClient.DefaultRequestHeaders)} Content: {response.Content}");
        }
    }

    private bool IsOrderCancel(string orderStatus)
    {
        switch (orderStatus.ToLower())
        {
            case "cancelled":
            case "canceled":
            case "failed":
            case "refunded":
                return true;
            default:
                return false;
        }
    }

    internal async Task<bool> ValidateApiKeyAsync(string apiKey)
    {
        var merchant = await _merchantService.GetByApikeyAsync(apiKey);
        if (merchant == null)
        {
            return false;
        }

        return true;
    }

    internal async Task<object?> AddBehaviorEventAsync(BehaviorEventDto behaviorEventDto)
    {
        var merchant = await _merchantService.GetByApikeyAsync(behaviorEventDto.ApiKey);
        if (merchant == null)
        {
            return false;
        }

        return true;
    }
}