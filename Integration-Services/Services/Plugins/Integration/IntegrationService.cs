using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using Audience.Services.Audience;
using Integration.Models;
using Integration.Models.Elastic.Behavior;
using Integration.Models.Elastic.Order;
using Integration.Services.Static;
using Marlin_OS_Integration_API.Models.Order;
using Marlin_OS_Integration_API.Services.Static;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.Extensions.DependencyInjection;
using Nest;
using RabbitMQ.Client;
using Shared.Elastic.Models.Behavior;
using Shared.Elastic.Order;
using Shared.Elastic.OrderDiscount;
using Shared.Models;
using Shared.Models.Elastic.ElasticCampaignsMails;
using Shared.Models.Merchant;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Integration.Services.Plugins.Integration;

public class IntegrationService(
    ILogger logger,
    ElasticClient elasticClient,
    [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud,
    //IConnection rabbitConnection,
    IMerchantService merchantService,
    ICustomerService customerService)
    : IIntegrationService, IDisposable
{
    private readonly IModel _model = rabbitConnectionCloud.CreateModel();

    public Task<ResponseDto> AddBehaviorEventForProcessingAsync(BehaviorEvent behaviorEvent)
    {
        try
        {
            var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(behaviorEvent));
            _model.BasicPublish(
                exchange: "customer",
                routingKey: "customer_pageevent_incoming",
                basicProperties: null,
                body: actionBody
            );
        }
        catch (Exception ex)
        {
            logger.Warning(
                ex, "Failed to send message to RabbitMQ exchange 'customer' with routing key 'customer_pageevent_incoming'.");
        }

        return Task.FromResult(new ResponseDto { Success = true, Message = "Success" });
    }

    public async Task<ResponseDto> AddBehaviorEventAsync(ElasticBehaviorEvent elasticEvent, string apiKey)
    {
        //Validate merchant
        Merchant? merchant;
        if (string.IsNullOrEmpty(elasticEvent.Shop_event.Webshop_id))
        {
            merchant = await merchantService.ValidateAsync(apiKey);
            if (merchant != null)
            {
                elasticEvent.Shop_event.Webshop_id = merchant.Id.ToString();
            }

            if (string.IsNullOrEmpty(elasticEvent.Shop_event.Webshop_id))
            {
                return new ResponseDto
                    {Success = false, Message = "Could not validate Webshop with provided Api Key"};
            }
        }
        else
        {
            merchant = await merchantService.GetByIdAsync(Convert.ToInt32(elasticEvent.Shop_event.Webshop_id));
            if (string.IsNullOrEmpty(elasticEvent.Shop_event.Webshop_id))
            {
                return new ResponseDto
                {
                    Success = false,
                    Message = $"Could not find Webshop with provided Id: {elasticEvent.Shop_event.Webshop_id}"
                };
            }
        }

        if (merchant == null)
        {
            return new ResponseDto {Success = false, Message = $"No webshop found"};
        }

        elasticEvent.Shop_event.Webshop_name = merchant.Name;
        elasticEvent.Shop_event.Webshop_id = merchant.Id.ToString();

        //EmailGuid
        if (elasticEvent.Customer != null)
        {
            //Default if values is empty
            elasticEvent.Customer.Email2 = string.IsNullOrEmpty(elasticEvent.Customer.Email2)
                ? "n/a"
                : elasticEvent.Customer.Email2;
            elasticEvent.Customer.ViaAds2 = string.IsNullOrEmpty(elasticEvent.Customer.ViaAds2)
                ? "n/a"
                : elasticEvent.Customer.ViaAds2;

            //Check if email is n/a and email2 is set
            if (elasticEvent.Customer.Email == "n/a" && elasticEvent.Customer.Email2 != "n/a")
            {
                elasticEvent.Customer.Email = elasticEvent.Customer.Email2;
            }

            if (elasticEvent.Customer.ViaAds == "n/a" && elasticEvent.Customer.ViaAds2 != "n/a")
            {
                elasticEvent.Customer.ViaAds = elasticEvent.Customer.ViaAds2;
            }
        }

        //Validate ip or default to 0.0.0.0
        if (elasticEvent.client?.ip != null)
        {
            var ip = elasticEvent.client.ip.Split(",")[0].Trim();
            elasticEvent.client.ip = IPAddress.TryParse(elasticEvent.client.ip, out _) ? ip : "0.0.0.0";
        }
        else
        {
            elasticEvent.client = new ElasticBehaviorEventClient()
            {
                ip = "0.0.0.0"
            };
        }

        if (elasticEvent.Customer != null)
        {
            if (elasticEvent.Customer.ViaAds is "" or null)
            {
                elasticEvent.Customer.ViaAds = "n/a";
            }

            if (elasticEvent.Customer.ViaAds2 is "" or null)
            {
                elasticEvent.Customer.ViaAds2 = "n/a";
            }

            elasticEvent.Customer.ViaAds = elasticEvent.Customer!.ViaAds!.Split("&")[0];
            elasticEvent.Customer.ViaAds2 = elasticEvent.Customer!.ViaAds2!.Split("&")[0];
        }

        if (string.IsNullOrEmpty(elasticEvent.Customer?.Email) || elasticEvent.Customer?.Email == "n/a")
        {
            elasticEvent.Customer!.Email = "n/a";
            if (elasticEvent.Customer!.ViaAds != "n/a")
            {
                var rawValue = elasticEvent.Customer.ViaAds.Split("_")[1];
                //Check if email guid
                if (elasticEvent.Customer.ViaAds.StartsWith("1_"))
                {
                    //Find email_guid in elastic
                    var emailSentResult = elasticClient.Search<ElasticCampaignsMails>((s => s
                        .Source(sf => sf
                            .Includes(i => i
                                .Fields(
                                    f => f.Customer!.Email,
                                    f => f.Email,
                                    f => f.Email!.Webshop_id,
                                    f => f.Email!.Email_guid
                                )
                            )
                        )
                        .Index("campaigns-mails")
                        .Query(q => q.Term(t => t
                            .Field(f => f.Email!.Email_guid)
                            .Value(rawValue))
                        )));
                    var emailElastic = emailSentResult.Documents.ToList();
                    if (emailElastic.Count > 0)
                    {
                        foreach (var emailSent in emailElastic)
                        {
                            if (emailSent.Customer?.Email != null)
                            {
                                elasticEvent.Customer.Email = emailSent.Customer.Email;
                                break;
                            }
                        }
                    }
                }
                else if (elasticEvent.Customer.ViaAds.StartsWith("2_"))
                {
                    var contact = await customerService.GetByPartnerGuid(rawValue);
                    if (contact != null)
                    {
                        elasticEvent.Customer.Email = contact.Email;
                    }
                }
            }
        }

        //Email lowercase
        if (!string.IsNullOrEmpty(elasticEvent.Customer?.Email))
        {
            elasticEvent.Customer.Email = elasticEvent.Customer.Email.ToLower();
        }

        var start = DateTime.UtcNow;

        ProductReturnEventDto? product = null;
        SearchProductDto searchProduct;
        switch (merchant!.Type)
        {
            case "Shopify":
                var tempUrl = elasticEvent.url!.full!.Split("/");
                if (elasticEvent.Shop_event.Product_id != null)
                {
                    //Pixel
                    searchProduct = new SearchProductDto
                    {
                        WebshopId = Convert.ToInt32(elasticEvent.Shop_event.Webshop_id),
                        ProductId = elasticEvent.Shop_event.Product_id,
                        VariantId = elasticEvent.Shop_event.Product_variant_id,
                        Url = elasticEvent.url.full
                    };
                    product = await merchantService.SearchProductEventAsync(searchProduct).ConfigureAwait(false);
                }
                else if (tempUrl.Length == 5)
                {
                    var productName = tempUrl[4].Split("?");
                    searchProduct = new SearchProductDto
                    {
                        WebshopId = Convert.ToInt32(elasticEvent.Shop_event.Webshop_id),
                        ProductId = elasticEvent.Shop_event.Product_id,
                        VariantId = elasticEvent.Shop_event.Product_variant_id,
                        Url = $"{tempUrl[3]}/{productName[0]}"
                    };
                    product = await merchantService.SearchProductEventAsync(searchProduct).ConfigureAwait(false);
                }
                else if (tempUrl.Length == 7)
                {
                    var productName = tempUrl[6].Split("?");
                    searchProduct = new SearchProductDto
                    {
                        WebshopId = Convert.ToInt32(elasticEvent.Shop_event.Webshop_id),
                        ProductId = elasticEvent.Shop_event.Product_id,
                        VariantId = elasticEvent.Shop_event.Product_variant_id,
                        Url = $"{productName[0]}",
                    };
                    product = await merchantService.SearchProductEventAsync(searchProduct).ConfigureAwait(false);
                }

                break;

            case "Magento":
                var tempProductSku = elasticEvent.url?.full?.Split("/")[^1] ?? "";
                if (tempProductSku != "")
                {
                    tempProductSku = tempProductSku.Split(".")[0];
                }

                searchProduct = new SearchProductDto
                {
                    WebshopId = Convert.ToInt32(elasticEvent.Shop_event.Webshop_id),
                    ProductId = elasticEvent.Shop_event.Product_id,
                    VariantId = elasticEvent.Shop_event.Product_variant_id,
                    Sku = tempProductSku,
                    Url = elasticEvent.url.full,
                    Slug = tempProductSku
                };
                product = await merchantService.SearchProductEventAsync(searchProduct).ConfigureAwait(false);
                break;

            case "WooCommerce":
                searchProduct = new SearchProductDto
                {
                    WebshopId = Convert.ToInt32(elasticEvent.Shop_event.Webshop_id),
                    ProductId = elasticEvent.Shop_event.Product_id,
                    VariantId = elasticEvent.Shop_event.Product_variant_id,
                    Sku = elasticEvent.Shop_event?.Product_sku,
                    FullUrl = elasticEvent.url.full
                };
                product = await merchantService.SearchProductEventAsync(searchProduct).ConfigureAwait(false);
                break;

            case "DanDomain":
                var urlSplit = elasticEvent.url.full.Split("/");
                if (urlSplit.Length >= 6 && elasticEvent.url.full.Contains("-"))
                {
                    elasticEvent.Shop_event.Product_id = urlSplit[5].Split("&")[0].Split("-")[0];
                    searchProduct = new SearchProductDto
                    {
                        WebshopId = Convert.ToInt32(elasticEvent.Shop_event.Webshop_id),
                        ProductId = elasticEvent.Shop_event.Product_id,
                        VariantId = elasticEvent.Shop_event.Product_variant_id,
                        Sku = elasticEvent.Shop_event?.Product_sku,
                    };
                    product = await merchantService.SearchProductEventAsync(searchProduct).ConfigureAwait(false);
                }
                else
                {
                    return new ResponseDto {Success = false, Message = "Url is not a product page"};
                }

                break;
            case "DanDomainClassic":
                var regex = new Regex(@"-(\d+)p\.html$");
                var match = regex.Match(elasticEvent.url.full.Split("?")[0]);
                // If a match is found, extract the Product Id
                if (!match.Success)
                {
                    return new ResponseDto {Success = false, Message = "No MerchantProductId in provided "};
                }

                var productId = match.Groups[1].Value;
                searchProduct = new SearchProductDto
                {
                    WebshopId = Convert.ToInt32(elasticEvent.Shop_event.Webshop_id),
                    ProductId = productId,
                    VariantId = productId,
                };
                product = await merchantService.SearchProductEventAsync(searchProduct).ConfigureAwait(false);
                break;
            case "PrestaShop":
                searchProduct = new SearchProductDto
                {
                    WebshopId = Convert.ToInt32(elasticEvent.Shop_event.Webshop_id),
                    ProductId = elasticEvent.Shop_event.Product_id,
                    VariantId = elasticEvent.Shop_event.Product_variant_id,
                    Sku = elasticEvent.Shop_event?.Product_sku,
                    FullUrl = elasticEvent.url.full
                };
                product = await merchantService.SearchProductEventAsync(searchProduct).ConfigureAwait(false);
                break;
        }

        if (product?.InternalProductId == null)
        {
            // Log this but do not fail.
            /*_logger.ForContext("service_name", GetType().Name).Warning(
                "{EventType} failed when fetching product with the following properties: {Serialize}"
                , elasticEvent.Shop_event?.Event_type, JsonSerializer.Serialize(searchProduct));*/
            return new ResponseDto {Success = false, Message = "No Product Sku"};
        }

        elasticEvent = FieldsFromProduct(elasticEvent, product);
        //Default useragent
        if (elasticEvent.user_agent == null)
        {
            elasticEvent.user_agent = new ElasticBehaviorEventUserAgent
            {
                name = "n/a",
                original = "n/a",
                version = "n/a",
                device = new ElasticBehaviorEventDevice
                {
                    name = "n/a"
                }
            };
        }

        if ((DateTime.UtcNow - start).TotalMilliseconds > 6000)
        {
            logger.ForContext("service_name", GetType().Name).Error(
                "Integration request took over 6000 miliseconds: {Time}, {jsonString}",
                (DateTime.UtcNow - start).TotalMilliseconds, JsonSerializer.Serialize(elasticEvent));
        }

        try
        {
            using (var publishChannel = rabbitConnectionCloud.CreateModel())
            {
                var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticEvent));
                publishChannel.BasicPublish(exchange: "customer",
                    routingKey: "customer_pageevent",
                    basicProperties: null,
                    body: actionBody);
            }
            /*var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticEvent));
            _model.BasicPublish(exchange: "customer",
                routingKey: "customer_pageevent",
                basicProperties: null,
                body: actionBody);*/
        }
        catch (Exception ex)
        {
            // Log this but do not fail.
            logger.ForContext("service_name", GetType().Name).Warning(
                ex, "{event} sending to {component} exchange '{exchange}' with routing key '{routingKey}'",
                "Failed", "RabbitMQ", "customer", "customer_pageevent");
        }

        return new ResponseDto {Success = true, Message = "Success"};
    }

    public async Task<ResponseDto> AddOrderAsync(List<ElasticOrderEvent> orders, string apiKey,
        bool lastUpdateOrder = true, Merchant? merchant = null)
    {
        //Check if merchant exists
        if (merchant == null)
        {
            merchant = await merchantService.ValidateOrderApiKey(apiKey).ConfigureAwait(false);
        }

        var merchantId = merchant?.Id;
        if (merchantId == null || merchant == null)
        {
            logger.ForContext("service_name", GetType().Name).Error(
                "Error validating custom Merchant with key: {ApiKey} orders: {Orders}", apiKey,
                JsonSerializer.Serialize(orders));
            return new ResponseDto {Success = false, Message = "Could not validate Merchant with provided Api Key"};
        }

        var orderNumbers = orders.Select(a => a.Shop_order.Order_number).ToList();
        //Get all orders from elasticSearch
        var ordersElastic = new List<ElasticOrderEvent>();
        var searchResponse = elasticClient.Search<ElasticOrderEvent>((s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_order!.Order_number,
                            f => f.Shop_order!.Order_number_recevice,
                            f => f.Shop_order!.Webshop_id,
                            f => f.Shop_order!.Status,
                            f => f.Shop_order!.IsCanceled,
                            f => f.Shop_order!.Total_price,
                            f => f.Shop_order!.Total_price_tax,
                            f => f.Shop_order!.Total_price_tax_included,
                            f => f.Shop_order!.Last_modified
                        )
                    )
                )
                .Index("customers-orders")
                .Query(q =>
                    q.Match(m => m
                        .Field(f => f.Shop_order.Webshop_id)
                        .Query(merchantId.ToString())) &&
                    q.Bool(m => m.Should(
                        InnerBlahClick(orderNumbers)
                    ))
                )
                .Size(10_000)
                .Scroll("2m")
            ));

        ordersElastic.AddRange(searchResponse.Documents);
        while (searchResponse.IsValid && searchResponse.Documents.Count != 0)
        {
            searchResponse = await elasticClient.ScrollAsync<ElasticOrderEvent>("2m", searchResponse.ScrollId);
            ordersElastic.AddRange(searchResponse.Documents);
        }

        await elasticClient.ClearScrollAsync(cs => cs
            .ScrollId(searchResponse.ScrollId)
        );

        var distinctOrders = ordersElastic
            .GroupBy(o => o.Shop_order!.Order_number_recevice)
            .Select(g => g.OrderByDescending(o => o.Shop_order!.Last_modified).First())
            .ToList();

        Dictionary<string, ElasticOrderEvent> ordersElasticDict = distinctOrders
            .ToDictionary(p => p.Shop_order!.Order_number_recevice, p => p);

        var totalcount = orders.Count;
        var count = orders.Count;

        //Orders from elastic
        foreach (var order in orders)
        {
            try
            {
                count--;
                Console.Write($"\rWebshop:{merchant.Name} - Orders Left: {count} of {totalcount}");
                ordersElasticDict.TryGetValue(order.Shop_order!.Order_number, out var existingOrder);
                if (existingOrder?.Shop_order?.Last_modified != null)
                {
                    existingOrder.Shop_order.Last_modified =
                        existingOrder.Shop_order?.Last_modified.Value.AddHours(-10);
                }

                if (existingOrder == null ||
                    existingOrder.Shop_order?.Last_modified < order.Shop_order?.Last_modified &&
                    (existingOrder.Shop_order?.Status != order.Shop_order?.Status ||
                     existingOrder.Shop_order?.Total_price != order.Shop_order?.Total_price ||
                     existingOrder.Shop_order?.Total_price_tax != order.Shop_order?.Total_price_tax ||
                     existingOrder.Shop_order?.Total_price_tax_included !=
                     order.Shop_order?.Total_price_tax_included ||
                     existingOrder.Shop_order?.IsCanceled != order.Shop_order?.IsCanceled))
                {
                    //Custom order status
                    foreach (var MerchantOrderStatus in merchant.MerchantOrderStatuses)
                    {
                        if (!string.Equals(MerchantOrderStatus.Status, order.Shop_order.Status,
                                StringComparison.CurrentCultureIgnoreCase)) continue;
                        
                        if (MerchantOrderStatus.Denied)
                        {
                            order.Shop_order.IsCanceled = true;
                        }
                        else
                        {
                            order.Shop_order.IsCanceled = false;
                        }
                    }

                    //Check minimum order value before order is canceled
                    var minimumOrderAmount = Convert.ToInt32(merchant.MerchantMeta
                        .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.MinimumOrderAmount)
                        ?.Value ?? "0");
                    if (order.Shop_order.Total_price < minimumOrderAmount)
                    {
                        order.Shop_order.IsCanceled = true;
                    }

                    if (order.Shop_order != null)
                    {
                        order.Shop_order.Webshop_id = merchantId.ToString();
                        order.Shop_order.Webshop_name = merchant.Name;
                    }

                    //Check email set or n/a
                    if (order.Customer?.Email is null or "")
                    {
                        order.Customer!.Email = "n/a";
                    }

                    //Lowercase email
                    if (order.Customer?.Email != null)
                    {
                        order.Customer.Email = order.Customer.Email.ToLower();
                    }

                    //SourceName is null
                    if (order.Shop_order?.Source_name == null)
                    {
                        order.Shop_order!.Source_name = "n/a";
                    }

                    //Sanitize phone number and email Billing_address
                    if (order.Shop_order?.Billing_address != null)
                    {
                        order.Shop_order.Billing_address.Email = order.Shop_order.Billing_address.Email.ToLower();
                        order.Shop_order.Billing_address.Phone_number =
                            Sanitize.Phone(order.Shop_order.Billing_address.Phone_number);
                    }

                    //Sanitize phone number and email Shipping_address
                    if (order.Shop_order?.Shipping_address != null)
                    {
                        order.Shop_order.Shipping_address.Email = order.Shop_order.Shipping_address.Email.ToLower();
                        order.Shop_order.Shipping_address.Phone_number =
                            Sanitize.Phone(order.Shop_order.Shipping_address.Phone_number);
                    }

                    //Validate ip or default to 0.0.0.0
                    if (order.client?.ip != null)
                    {
                        var ip = order.client.ip.Split(",")[0].Trim();
                        order.client.ip = IPAddress.TryParse(order.client.ip, out _) ? ip : "0.0.0.0";
                    }
                    else
                    {
                        order.client = new ElasticOrderEventClient()
                        {
                            ip = "0.0.0.0"
                        };
                    }

                    //Loop all order items to get internalProductId and priceRange
                    foreach (var orderItem in order.Shop_order!.Order_items!)
                    {
                        ProductReturnEventDto? product = null;
                        try
                        {
                            product = await merchantService.SearchProductEventAsync(new SearchProductDto
                            {
                                WebshopId = merchantId,
                                ProductId = orderItem.Product_id,
                                VariantId = orderItem.Product_variant_id,
                                Sku = orderItem.Sku
                            }).ConfigureAwait(false);
                        }
                        catch (Exception ex)
                        {
                            logger.ForContext("service_name", GetType().Name)
                                .Warning(ex, "Error while getting product for WebShop {WebShopName} with values " +
                                             "ProductId {ProductId} VariantId {VariantId} sku: {Sku} ",
                                    merchant.Name,
                                    orderItem.Product_id, orderItem.Product_variant_id, orderItem.Sku);
                        }

                        if (product?.InternalProductId != null)
                        {
                            orderItem.Internal_product_id = !string.IsNullOrEmpty(product.ParentInternalProductId)
                                ? product.ParentInternalProductId
                                : product.InternalProductId;
                            if (!string.IsNullOrEmpty(product.ParentInternalProductId))
                            {
                                orderItem.Variant_internal_id = product.InternalProductId;
                            }
                            else
                            {
                                orderItem.Variant_internal_id = "n/a";
                            }
                        }
                        orderItem.Price_range = PriceConverter.ConvertPrice(orderItem.Price);
                    }

                    try
                    {
                        using (var publishChannel = rabbitConnectionCloud.CreateModel())
                        {
                            var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(order));
                            publishChannel.BasicPublish(exchange: "customer",
                                routingKey: "customer_order",
                                basicProperties: null,
                                body: actionBody);
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log this but do not fail.
                        logger.ForContext("service_name", GetType().Name).Warning(
                            ex,
                            "{event} sending to {component} exchange '{exchange}' with routing key '{routingKey}'",
                            "Failed", "RabbitMQ", "customer", "customer_order");
                    }

                    if (order.Discounts != null)
                    {
                        foreach (var elasticOrderEventDiscount in order.Discounts)
                        {
                            var discountEvent = new ElasticOrderDiscountEvent
                            {
                                Event_received = order.Event_received,
                                Customer = new ElasticOrderDiscountEventCustomer
                                {
                                    Email = order.Customer.Email
                                },
                                Shop_order = new ElasticOrderDiscountEventShopOrder
                                {
                                    Order_number = order.Shop_order.Order_number,
                                    Webshop_id = order.Shop_order.Webshop_id,
                                    Webshop_name = order.Shop_order.Webshop_name
                                },
                                Discount = new ElasticOrderDiscountEventDiscount
                                {
                                    Type = elasticOrderEventDiscount.Type,
                                    Amount = elasticOrderEventDiscount.Amount,
                                    Code = elasticOrderEventDiscount.Code
                                },
                                Order_date = order.Order_date
                            };

                            try
                            {
                                using (var publishChannel = rabbitConnectionCloud.CreateModel())
                                {
                                    var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(discountEvent));
                                    publishChannel.BasicPublish(exchange: "customer",
                                        routingKey: "customer_order_discount",
                                        basicProperties: null,
                                        body: actionBody);
                                }
                            }
                            catch (Exception ex)
                            {
                                // Log this but do not fail.
                                logger.ForContext("service_name", GetType().Name).Warning(
                                    ex,
                                    "{event} sending to {component} exchange '{exchange}' with routing key '{routingKey}'",
                                    "Failed", "RabbitMQ", "customer", "customer_order");
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        try
        {
            if (lastUpdateOrder)
            {
                await merchantService.UpdateLastOrderSyncAsync(merchant);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error update lastOrder sync for shop: {Webshop}", merchant.Name);
        }

        return new ResponseDto {Success = true, Message = "Success"};
    }

    private ElasticBehaviorEvent FieldsFromProduct(ElasticBehaviorEvent elasticEvent, ProductReturnEventDto? product)
    {
        if (product == null)
        {
            return elasticEvent;
        }

        if (elasticEvent.Shop_event != null)
        {
            elasticEvent.Shop_event.Product_sku = product.Sku;
            elasticEvent.Shop_event.Price ??= product.Price;
            if (elasticEvent.Shop_event.Price == 0)
            {
                if (product.Price != null)
                {
                    elasticEvent.Shop_event.Price = product.Price;
                }
            }

            elasticEvent.Shop_event.Price_range =
                PriceConverter.ConvertPrice(elasticEvent.Shop_event.Price);


            elasticEvent.Shop_event.Product_internal_id = !string.IsNullOrEmpty(product.ParentInternalProductId)
                ? product.ParentInternalProductId
                : product.InternalProductId;
            if (!string.IsNullOrEmpty(product.ParentInternalProductId))
            {
                elasticEvent.Shop_event.Variant_internal_id = product.InternalProductId;
            }
            else
            {
                elasticEvent.Shop_event.Variant_internal_id = "n/a";
            }
        }

        return elasticEvent;
    }

    private static QueryContainer[] InnerBlahClick(List<string> orders)
    {
        QueryContainer orQuery = null;
        var queryContainerList = new List<QueryContainer>();
        foreach (var item in orders)
        {
            orQuery = new MatchPhraseQuery {Field = "Shop_order.Order_number", Query = item};
            queryContainerList.Add(orQuery);
        }

        return queryContainerList.ToArray();
    }

    public async Task<ResponseDto> SynchronizeWebshopDataAsync(
        Merchant merchant, bool fullCatalog = false)
    {
        await merchantService.UpdateMerchantProductsAsync(merchant, fullCatalog);
        return new ResponseDto() {Success = true, Message = "Success"};
    }

    public void Dispose()
    {
        _model?.Dispose();
    }
}