using Integration.Models;
using Marlin_OS_Integration_API.Models.Order;
using Merchant_Services.Models.ModelsDal.Merchant;
using Shared.Elastic.Models.Behavior;
using Shared.Models;

namespace Integration.Services.Plugins.Integration;

public interface IIntegrationService
{
    Task<ResponseDto> AddBehaviorEventForProcessingAsync(BehaviorEvent behaviorEvent);
    Task<ResponseDto> AddBehaviorEventAsync(ElasticBehaviorEvent elasticEvents, string apiKey);

    Task<ResponseDto> AddOrderAsync(List<ElasticOrderEvent> orders, string apiKey, bool lastUpdateOrder = true,
        Merchant merchant = null);

    Task<ResponseDto> SynchronizeWebshopDataAsync(Merchant? merchant, bool fullCatalog = false);
}