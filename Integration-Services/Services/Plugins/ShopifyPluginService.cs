using System.Diagnostics;
using System.Globalization;
using Integration.Models.Elastic.Behavior;
using Integration.Models.Elastic.Order;
using Integration.Models.Shopify;
using Integration.Services.Plugins.Integration;
using Integration.Services.Static;
using Marlin_OS_Integration_API.Models.Order;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Shared.Dto;
using Shared.Dto.Partner;
using Shared.Dto.Webshop;
using Shared.Elastic.Behavior;
using Shared.Elastic.Models.Behavior;
using Shared.Elastic.Order;
using Shared.Models;
using Shared.Models.Merchant;
using Shared.Services;
using ShopifySharp;
using ShopifySharp.Filters;
using UAParser;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;
using IShopifyService = Webshop_Service.Services.Shopify.IShopifyService;
using Product = Merchant_Services.Models.ModelsDal.Merchant.Product;

namespace Integration.Services.Plugins.ShopifyIntegration;

public class ShopifyPluginService(
    ILogger logger,
    IIntegrationService integrationService,
    IMerchantService merchantService,
    IShopifyService shopifyService,
    IMemoryCache memoryCache)
    : IPluginService
{
    public async Task<ResponseDto> AuthShopifyAsync(string shop, string token, string name, string email)
    {
        var response = await shopifyService.GetShopifyShop(shop);
        var shopify = new PluginInstallInfo()
        {
            Url = shop,
            Name = name,
            ApiKey = token,
            Type = "Shopify",
            CreatedDate = DateTime.UtcNow,
            LastModifiedDate = DateTime.UtcNow,
            ShopEmail = email
        };
        if (response == null || response?.Id == 0)
        {
            await shopifyService.CreateShopifyShopAsync(shopify);
            return new ResponseDto {Success = true, Message = "Created shop"};
        }

        await shopifyService.UpdateShopifyShopAsync(shopify);
        return new ResponseDto {Success = true, Message = "Updated shop"};
    }

    public async Task<ResponseDto> RemoveShopifyAsync(string url)
    {
        await shopifyService.RemoveShopifyShopAsync(url);
        return new ResponseDto
        {
            Success = true,
        };
    }

    public async Task<ResponseDto> AddBehaviorEventAsync(ShopifyBehaviorDto shopifyBehaviorDto)
    {
        try
        {
            if (!string.IsNullOrEmpty(shopifyBehaviorDto.UserAgent))
            {
                var uaParser = Parser.GetDefault();
                var c = uaParser.Parse(shopifyBehaviorDto.UserAgent);
                shopifyBehaviorDto.Name = c.OS.ToString();
                shopifyBehaviorDto.Version = $"{c.UA.Major}.{c.UA.Minor}.{c.UA.Patch}";
                shopifyBehaviorDto.DeviceName = c.Device.ToString();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Parse userAgent failed shopify json: {UserAgent}", shopifyBehaviorDto.UserAgent);
        }

        //Check if merchantId can be found before going forward
        if (string.IsNullOrEmpty(shopifyBehaviorDto.WebShopId))
        {
            logger.ForContext("service_name", GetType().Name).Error("Cant find shopify shop with url {Url} {Json}",
                shopifyBehaviorDto.Url,
                JsonConvert.SerializeObject(shopifyBehaviorDto));

            return new ResponseDto
            {
                Success = false,
            };
        }

        var elasticEvent = new ElasticBehaviorEvent
        {
            client = new ElasticBehaviorEventClient
            {
                ip = shopifyBehaviorDto.Ip
            },
            url = new ElasticBehaviorEventUrl
            {
                full = shopifyBehaviorDto.Url
            },
            user_agent = new ElasticBehaviorEventUserAgent
            {
                device = new ElasticBehaviorEventDevice {name = shopifyBehaviorDto.DeviceName},
                name = shopifyBehaviorDto.Name,
                version = shopifyBehaviorDto.Version,
                original = $"{shopifyBehaviorDto.DeviceName},{shopifyBehaviorDto.Name},{shopifyBehaviorDto.Version}"
            },
            Customer = new ElasticBehaviorEventCustomer
            {
                Email = shopifyBehaviorDto.Email,
                ViaAds = shopifyBehaviorDto.ViaAds,
                Email2 = shopifyBehaviorDto.Email2,
                ViaAds2 = shopifyBehaviorDto.ViaAds2,
                Session_id = shopifyBehaviorDto.Session,
            },
            Event_date = DateTime.UtcNow,
            Event_received = DateTime.UtcNow,
            Shop_event = new ElasticBehaviorEventShopEvent
            {
                Event_type = shopifyBehaviorDto.EventType,
                Webshop_id = shopifyBehaviorDto.WebShopId,
                Product_id = shopifyBehaviorDto.ProductId,
                Product_variant_id = shopifyBehaviorDto.VariantId,
                Product_sku = shopifyBehaviorDto.Sku
            },
            Plugin = new ElasticBehaviorEventPlugin
            {
                Name = "Shopify",
                Version = shopifyBehaviorDto.PluginVersion ?? "n/a"
            }
        };

        await integrationService.AddBehaviorEventAsync(elasticEvent, "")
            .ConfigureAwait(false);

        return new ResponseDto
        {
            Success = true,
        };
    }

    public async Task<ResponseDto> UpdateAsync(Merchant merchant, PartnerIdAndNameDto partner, int lookBackDaysOrder,
        int lookBackDaysProduct)
    {
        if (lookBackDaysProduct != 0)
        {
            //ScriptTag
            await UpdateScriptTag(merchant);
            //webhook
            await UpdateWebHook(merchant);
            //Product Data
            await UpdateProductData(merchant, lookBackDaysProduct);
        }

        if (lookBackDaysOrder != 0)
        {
            //Order data
            await UpdateOrder(merchant, partner, lookBackDaysOrder);
        }

        return new ResponseDto
        {
            Success = true
        };
    }

    private async Task UpdateOrder(Merchant merchant, PartnerIdAndNameDto partner, int lookBackDays)
    {
        var apiUrl = merchant.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUrl).Value;
        var apiKey = merchant.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey).Value;

        var orderService = new OrderService(apiUrl, apiKey);
        var orderListFilter = new OrderListFilter
        {
            UpdatedAtMin = DateTime.UtcNow.AddDays(-lookBackDays),
            Limit = 250,
            Status = "any"
        };

        var orders = await orderService.ListAsync(orderListFilter);
        var orderList = orders.Items.ToList();
        var hasNextPage = orders.HasNextPage;
        while (hasNextPage)
        {
            orders = await orderService.ListAsync(orders.GetNextPageFilter());
            hasNextPage = orders.HasNextPage;
            orderList.AddRange(orders.Items);
        }

        await ShopifyOrder(orderList, merchant, partner);
    }

    private async Task ShopifyOrder(List<Order> orderList, Merchant merchant, PartnerIdAndNameDto partner,
        bool lastUpdateOrder = true)
    {
        var elasticOrders = new List<ElasticOrderEvent>();
        foreach (var order in orderList)
        {
            var vat = 1 + order.TaxLines.FirstOrDefault()?.Rate ?? 0;
            var orderItems = new List<ElasticOrderEventItem>();
            foreach (var lineItem in order.LineItems)
            {
                var quantity = lineItem.Quantity ?? 0;
                if (vat == 0)
                {
                    orderItems.Add(new ElasticOrderEventItem
                    {
                        Name = lineItem.Name,
                        Sku = lineItem.SKU,
                        Price = lineItem.Price ?? 0,
                        Quantity = quantity,
                        Total_price = 0,
                        Total_price_tax = 0,
                        Total_price_tax_included = 0,
                        Product_id = lineItem.ProductId.ToString(),
                        Product_variant_id = lineItem.VariantId.ToString(),
                        Price_range = PriceConverter.ConvertPrice(lineItem.Price),
                    });
                }
                else
                {
                    if (order.TaxesIncluded == true)
                    {
                        orderItems.Add(new ElasticOrderEventItem
                        {
                            Name = lineItem.Name,
                            Sku = lineItem.SKU,
                            Price = lineItem.Price ?? 0,
                            Quantity = quantity,
                            Total_price = lineItem.Price * quantity / vat ?? 0,
                            Total_price_tax = (lineItem.Price * quantity) - lineItem.Price * quantity / vat ?? 0,
                            Total_price_tax_included = lineItem.Price * quantity ?? 0,
                            Product_id = lineItem.ProductId.ToString(),
                            Product_variant_id = lineItem.VariantId.ToString(),
                            Price_range = PriceConverter.ConvertPrice(lineItem.Price),
                        });
                    }
                    else
                    {
                        orderItems.Add(new ElasticOrderEventItem
                        {
                            Name = lineItem.Name,
                            Sku = lineItem.SKU,
                            Price = lineItem.Price ?? 0,
                            Quantity = quantity,
                            Total_price = lineItem.Price * quantity ?? 0,
                            Total_price_tax = lineItem.Price * quantity * vat - lineItem.Price * quantity ?? 0,
                            Total_price_tax_included = lineItem.Price * quantity * vat,
                            Product_id = lineItem.ProductId.ToString(),
                            Product_variant_id = lineItem.VariantId.ToString(),
                            Price_range = PriceConverter.ConvertPrice(lineItem.Price),
                        });
                    }
                }
            }

            foreach (var refund in order.Refunds)
            {
                foreach (var refundLineItem in refund.RefundLineItems)
                {
                    var item = orderItems.Find(a => a.Sku == refundLineItem.LineItem.SKU);
                    var quantity = item.Quantity - refundLineItem.Quantity ?? 0;
                    item.Quantity = quantity;

                    if (vat == 0)
                    {
                        item.Total_price = 0;
                        item.Total_price_tax = 0;
                        item.Total_price_tax_included = 0;
                    }
                    else
                    {
                        if (order.TaxesIncluded == true)
                        {
                            item.Total_price = refundLineItem.LineItem.Price * quantity / vat ?? 0;
                            item.Total_price_tax = (refundLineItem.LineItem.Price * quantity) -
                                refundLineItem.LineItem.Price * quantity / vat ?? 0;
                            item.Total_price_tax_included = refundLineItem.LineItem.Price * quantity ?? 0;
                        }
                        else
                        {
                            item.Total_price = (refundLineItem.LineItem.Price ?? 0) * quantity;
                            item.Total_price_tax = item.Total_price * vat - item.Total_price;
                            item.Total_price_tax_included = item.Total_price + item.Total_price_tax;
                        }
                    }
                }
            }

            //Shipping tax
            var shipping = (order.ShippingLines.FirstOrDefault()?.Price ?? 0);

            //Price without vat
            var totalPrice = order.CurrentSubtotalPrice ?? 0;
            var totalPriceTax = order.CurrentSubtotalPrice * vat - order.CurrentSubtotalPrice ?? 0;
            var totalPriceTaxIncluded = order.CurrentSubtotalPrice * vat ?? 0;
            if (order.TaxesIncluded == true)
            {
                //Shipping
                decimal shippingTax = 0;
                if (vat != 0)
                {
                    shippingTax = shipping - (shipping / vat);
                }

                var currentTax = order.CurrentTotalTax - shippingTax;

                //Price with vat
                totalPrice = order.CurrentSubtotalPrice - currentTax ?? 0;
                totalPriceTax = currentTax ?? 0;
                totalPriceTaxIncluded = order.CurrentSubtotalPrice ?? 0;
            }

            var orderData = new ElasticOrderEvent
            {
                Order_date = order.CreatedAt!.Value.ToUniversalTime().DateTime,
                Event_received = DateTime.UtcNow,
                Customer = new ElasticOrderEventCustomer
                {
                    Email = order.Email.ToLower()
                },
                client = new ElasticOrderEventClient
                {
                    ip = order.BrowserIp,
                },
                user_agent = new ElasticOrderEventUserAgent
                {
                    name = order.ClientDetails?.UserAgent ?? "",
                },
                Shop_order = new ElasticOrderEventShopOrder
                {
                    Last_modified = order.UpdatedAt!.Value.ToUniversalTime().DateTime,
                    Status = order.FinancialStatus,
                    IsCanceled = IsOrderCancel(order.FinancialStatus),
                    Order_number = order.OrderNumber.ToString(),
                    Shipping_address = new ElasticOrderEventAddressShipping()
                    {
                        Email = order.Email.ToLower(),
                        First_name = order.ShippingAddress?.FirstName ?? "n/a",
                        Last_name = order.ShippingAddress?.LastName ?? "n/a",
                        Phone_number = order.ShippingAddress?.Phone ?? "n/a",
                    },
                    Webshop_id = merchant.Id.ToString(),
                    Total_price = totalPrice,
                    Total_price_tax = totalPriceTax,
                    Total_price_tax_included = totalPriceTaxIncluded,
                    Total_price_shipping = shipping,
                    Currency = order.Currency,
                    Vat_percentage = order.TaxLines.FirstOrDefault()?.Rate ?? 0,
                    Order_items = orderItems,
                    Source_name = order.SourceName
                },
                Plugin = new ElasticOrderEventPlugin
                {
                    Name = "Shopify",
                    Version = "1.0.0"
                },
                Discounts = [],
                Partner = new ElasticOrderEventPartner()
                {
                    Id = partner.Id.ToString(),
                    Name = partner.Name
                }
            };

            if (order.TaxLines.Count() > 1)
            {
                logger.ForContext("service_name", GetType().Name).Error(
                    "Shopify multiple taxlines on 1 order MerchantId: {MerchantId}, OrderId: {OrderId}", merchant.Id,
                    order.OrderNumber);
            }

            if (order.BillingAddress != null)
            {
                orderData.Shop_order.Billing_address = new ElasticOrderEventAddress
                {
                    Address1 = order.BillingAddress.Address1 ?? "n/a",
                    City = order.BillingAddress.City ?? "n/a",
                    Country = order.BillingAddress.Country ?? "n/a",
                    Zip_code = order.BillingAddress.Zip ?? "n/a",
                    State = order.BillingAddress.Province ?? "n/a",
                    Email = order.Email.ToLower(),
                    First_name = order.BillingAddress.FirstName ?? "n/a",
                    Last_name = order.BillingAddress.LastName ?? "n/a",
                    Phone_number = order.BillingAddress.Phone ?? "n/a"
                };
            }

            //Discount
            foreach (var discountCode in order.DiscountApplications)
            {
                decimal.TryParse(discountCode.Value,
                    NumberStyles.AllowDecimalPoint,
                    CultureInfo.InvariantCulture, out var amount);

                var type = "";
                switch (discountCode.ValueType)
                {
                    case "fixed_amount":
                        type = "fixed";
                        break;
                    case "percentage":
                        type = "percentage";
                        break;
                    case "shipping":
                        type = "";
                        break;
                    default:
                        logger.ForContext("service_name", GetType().Name)
                            .Error($"Unknown discount type found for {merchant.Id} with type {discountCode.Type}");
                        break;
                }

                if (type != "")
                {
                    orderData.Discounts.Add(new ElasticOrderEventDiscount
                    {
                        Amount = amount,
                        Code = discountCode.Code,
                        Type = type
                    });
                }
            }

            if (!string.IsNullOrEmpty(orderData.Customer.Email))
            {
                elasticOrders.Add(orderData);
            }
            else
            {
                Console.WriteLine($"Shopify order no email found for order {order.OrderNumber.ToString()}");
            }
        }

        if (elasticOrders.Count != 0)
        {
            await integrationService.AddOrderAsync(elasticOrders, "", true, merchant)
                .ConfigureAwait(false);
        }
        else
        {
            //No orders found sad
            await merchantService.UpdateLastOrderSyncAsync(merchant);
        }
    }

    private bool IsOrderCancel(string orderName)
    {
        switch (orderName.ToLowerInvariant())
        {
            case "overdue":
            case "expired":
            case "refunded":
            case "voided":
                return true;
            default:
                return false;
        }
    }

    private async Task UpdateScriptTag(Merchant webshop)
    {
        var apiUrl = webshop.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUrl).Value;
        var apiKey = webshop.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey).Value;

        var src = "https://files.viaads.dk/plugins/min/shopify.min.js";
        var service = new ScriptTagService(apiUrl, apiKey);
        var tags = await service.ListAsync();

        var found = tags.Items.FirstOrDefault(a => a.Src == src) != null;
        if (found == false)
        {
            var scriptTagService = new ScriptTagService(apiUrl, apiKey);
            var scriptTag = new ScriptTag()
            {
                Event = "onload",
                Src = src,
                DisplayScope = "all",
                CreatedAt = DateTimeOffset.Now,
                UpdatedAt = DateTimeOffset.Now
            };
            await scriptTagService.CreateAsync(scriptTag);
        }

        //Remove old file
        /*src = "https://viaadspublicfiles.blob.core.windows.net/plugins/Shopify/min/shopify.min.js";
        var deleteObject = tags.Items.FirstOrDefault(a => a.Src == src);
        if (deleteObject != null)
        {
            var scriptTagService = new ScriptTagService(webShop.AccessUrl, webShop.ApiKey);
            await scriptTagService.DeleteAsync(deleteObject.Id ?? 0);
        }*/
    }

    private async Task UpdateWebHook(Merchant webshop)
    {
        //TODO - Add Partner Uniquer Identifier to the webhooks Url
        var apiUrl = webshop.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUrl).Value;
        var apiKey = webshop.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey).Value;

        var service = new WebhookService(apiUrl, apiKey);
        var hooks = await service.ListAsync();

        //Order hook
        string url = "https://integration.valyrion.com/shopify/webHookOrder";
        var found = hooks.Items.SingleOrDefault(a =>
            a.Address == url) != null;
        if (found == false)
        {
            var hook = new Webhook()
            {
                Address = url,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Format = "json",
                Topic = "orders/create",
            };
            await service.CreateAsync(hook);
        }

        //Product deleted
        url = "https://integration.valyrion.com/shopify/webHookProductDeleted";
        found = hooks.Items.SingleOrDefault(a =>
            a.Address == url) != null;
        if (found == false)
        {
            var hook = new Webhook()
            {
                Address = url,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                Format = "json",
                Topic = "products/delete",
            };
            await service.CreateAsync(hook);
        }

        /*string url1 = "https://marlin-integration-service.azurewebsites.net/shopify/webHookOrder";
        var test = hooks.Items.SingleOrDefault(a =>
            a.Address == url1);
        if (test is { Id: not null })
        {
            await service.DeleteAsync(test.Id ?? 0);
        }*/
    }

    private async Task UpdateProductData(Merchant merchant, int lookBackDays)
    {
        var apiUrl = merchant.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUrl).Value;
        var apiKey = merchant.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey).Value;
        
        var stopWatch = new Stopwatch();
        stopWatch.Start();
        var products = await FetchProducts(apiUrl, apiKey, merchant, lookBackDays);
        stopWatch.Stop();
        Console.WriteLine($"Fetched {products.Count} products in {stopWatch.ElapsedMilliseconds} ms.");
        
        // Synchronize with Merchant data
        merchant.Products = products.DistinctBy(a => a.MerchantProductId).ToList();
        await integrationService.SynchronizeWebshopDataAsync(merchant).ConfigureAwait(false);
    }

    /*private async Task<List<Product>> FetchProducts(string shopUrl, string accessToken, Merchant merchant, int lookBackDays)
    {
        var graphService = new GraphService(shopUrl, accessToken);

        var products = new List<Product>();

        // Initial cursor value
        string? cursor = null;
        bool hasNextPage;
        var updatedAtMin = DateTime.UtcNow.AddDays(-lookBackDays).ToString("o");

        do
        {
            // GraphQL query with categories embedded
            var query = $@"
            {{
                products(first: 250, query: ""updated_at:>{updatedAtMin}""{(cursor != null ? $", after: \"{cursor}\"" : "")}) {{
                    edges {{
                        node {{
                            id
                            title
                            handle
                            createdAt
                            updatedAt
                            vendor
                            status
                            publishedAt
                            tags
                            bodyHtml
                            variants(first: 250) {{
                                edges {{
                                    node {{
                                        id
                                        title
                                        sku
                                        price
                                        compareAtPrice
                                        inventoryQuantity
                                        createdAt
                                        updatedAt
                                    }}
                                }}
                            }}
                            images(first: {StaticVariables.MaxImages}) {{
                                edges {{
                                    node {{
                                        originalSrc
                                    }}
                                }}
                            }}
                            collections(first: 250) {{
                                edges {{
                                    node {{
                                        id
                                        title
                                    }}
                                }}
                            }}
                        }}
                    }}
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                }}
            }}";

            // Execute the query
            var responseJson = await graphService.PostAsync(query);
            dynamic response = JObject.Parse(responseJson.ToString());

            // Collect products for processing
            var productNodes = ((IEnumerable<dynamic>)response.products.edges).Select(edge => edge.node).ToList();


            // Parse products and map to Product objects
            /*foreach (var edge in response.products.edges)
            {
                var productNode = edge.node;

                var isActive = string.Equals((string)productNode.status, "active", StringComparison.OrdinalIgnoreCase);
                var publishStatus = productNode.publishedAt != null ? "Published" : "Not published";
                var productId = productNode.id != null ? ((string) productNode.id).Split('/').Last() : string.Empty;
                var baseUrl = merchant.Url.TrimEnd('/');
                var productPermalink = $"{baseUrl}/products/{productNode.handle}";



                // Map Variants
                var variants = new List<Variant>();
                decimal lowestPrice = decimal.MaxValue;
                decimal highestPrice = 0;

                foreach (var variantEdge in productNode.variants.edges)
                {
                    var variantNode = variantEdge.node;

                    var price = decimal.TryParse((string)variantNode.price ?? "0", NumberStyles.Any, CultureInfo.InvariantCulture, out var parsedPrice)
                        ? parsedPrice
                        : 0;

                    if (lowestPrice > price) lowestPrice = price;
                    if (highestPrice < price) highestPrice = price;

                    var variant = new Variant
                    {
                        Description = productNode.bodyHtml,
                        Name = variantNode.title,
                        Permalink = productPermalink,
                        Price = price,
                        RegularPrice = decimal.TryParse((string)variantNode.compareAtPrice ?? (string)variantNode.price, NumberStyles.Any, CultureInfo.InvariantCulture, out var regularPrice)
                            ? regularPrice
                            : price,
                        Sku = variantNode.sku,
                        Status = publishStatus,
                        Active = isActive,
                        CreatedDate = DateTime.UtcNow,
                        LastModifiedDate = DateTime.UtcNow,
                        StockQuantity = Convert.ToInt32((string)variantNode.inventoryQuantity ?? "0"),
                        MerchantVariantId = variantNode.id != null ? ((string)variantNode.id).Split('/').Last() : string.Empty,
                        MerchantProductId = productId,
                        MerchantCreatedDate = variantNode.createdAt != null
                            ? DateTime.TryParse((string)variantNode.createdAt, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal, out var createdDate)
                                ? createdDate
                                : (DateTime?)null
                            : (DateTime?)null,

                        MerchantModifiedDate = variantNode.updatedAt != null
                            ? DateTime.TryParse((string)variantNode.updatedAt, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal, out var modifiedDate)
                                ? modifiedDate
                                : (DateTime?)null
                            : (DateTime?)null,
                    };

                    variants.Add(variant);
                }

                // Ensure tags is treated as a string
                // Handle tags as an array
                var tags = productNode.tags != null
                    ? string.Join(",", ((IEnumerable<dynamic>)productNode.tags).Select(tag => (string)tag))
                    : string.Empty;


                // Map Categories (Collections)
                var categories = ((IEnumerable<dynamic>)productNode.collections.edges)
                    .Select(collectionEdge => (string)collectionEdge.node.title)
                    .ToList();

                // Map Images
                var images = ((IEnumerable<dynamic>)productNode.images.edges)
                    .Select(imageEdge => new ProductImageDto
                    {
                        Src = (string)imageEdge.node.originalSrc
                    })
                    .ToList();

                // Map Product
                var firstVariant = variants.FirstOrDefault();

                // Map Product
                var product = new Product
                {
                    Name = productNode.title,
                    Description = productNode.bodyHtml,
                    CreatedDate = DateTime.UtcNow,
                    LastModifiedDate = DateTime.UtcNow,
                    Price = firstVariant?.Price ?? 0,
                    RegularPrice = firstVariant?.RegularPrice ?? 0,
                    Sku = firstVariant?.Sku,
                    MerchantProductId = productId,
                    FkMerchantId = merchant.Id,
                    Variants = variants,
                    Status = publishStatus,
                    Active = isActive,
                    Permalink = productPermalink,
                    MerchantCreatedDate = productNode.createdAt != null
                        ? DateTime.TryParse((string)productNode.createdAt, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal, out var productCreatedDate)
                            ? productCreatedDate
                            : (DateTime?)null
                        : (DateTime?)null,

                    MerchantModifiedDate = productNode.updatedAt != null
                        ? DateTime.TryParse((string)productNode.updatedAt, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal, out var productModifiedDate)
                            ? productModifiedDate
                            : (DateTime?)null
                        : (DateTime?)null,
                    Categories = string.Join(",", tags.Replace(", ", ",").Split(',').Concat(categories)),
                    ProductImages = System.Text.Json.JsonSerializer.Serialize(images)
                };

                products.Add(product);
            }#1#


            // Parallelize the mapping
        var mappedProducts = await Task.WhenAll(productNodes.Select(async productNode =>
        {
            // Parse Product Details
            var productId = ((string)productNode.id).Split('/').Last();
            var isActive = string.Equals((string)productNode.status, "active", StringComparison.OrdinalIgnoreCase);
            var publishStatus = productNode.publishedAt != null ? "Published" : "Not published";
            var baseUrl = merchant.Url.TrimEnd('/');
            var productPermalink = $"{baseUrl}/products/{productNode.handle}";

            // Parallelize Variant Mapping
            var variants = await Task.Run(() =>
            {
                return ((IEnumerable<dynamic>)productNode.variants.edges)
                    .Select(variantEdge =>
                    {
                        var variantNode = variantEdge.node;
                        var price = decimal.TryParse((string)variantNode.price ?? "0", NumberStyles.Any, CultureInfo.InvariantCulture, out var parsedPrice) ? parsedPrice : 0;

                        return new Variant
                        {
                            Description = productNode.bodyHtml,
                            Name = variantNode.title,
                            Permalink = productPermalink,
                            Price = price,
                            RegularPrice = decimal.TryParse((string)variantNode.compareAtPrice ?? (string)variantNode.price, NumberStyles.Any, CultureInfo.InvariantCulture, out var regularPrice)
                                ? regularPrice
                                : price,
                            Sku = variantNode.sku,
                            Status = publishStatus,
                            Active = isActive,
                            StockQuantity = Convert.ToInt32((string)variantNode.inventoryQuantity ?? "0"),
                            MerchantVariantId = variantNode.id != null ? ((string)variantNode.id).Split('/').Last() : string.Empty,
                            MerchantProductId = productId,
                            MerchantCreatedDate = variantNode.createdAt != null
                                ? DateTime.TryParse((string)variantNode.createdAt, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal, out var createdDate)
                                    ? createdDate
                                    : (DateTime?)null
                                : (DateTime?)null,

                            MerchantModifiedDate = variantNode.updatedAt != null
                                ? DateTime.TryParse((string)variantNode.updatedAt, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal, out var modifiedDate)
                                    ? modifiedDate
                                    : (DateTime?)null
                                : (DateTime?)null,
                        };
                    }).ToList();
            });

            // Parallelize Image Mapping
            var images = await Task.Run(() =>
            {
                return ((IEnumerable<dynamic>)productNode.images.edges)
                    .Select(imageEdge => new ProductImageDto
                    {
                        Src = (string)imageEdge.node.originalSrc
                    }).ToList();
            });

            // Parse Tags and Categories
            var tags = productNode.tags != null
                ? string.Join(",", ((IEnumerable<dynamic>)productNode.tags).Select(tag => (string)tag))
                : string.Empty;

            var categories = string.Join(",", ((IEnumerable<dynamic>)productNode.collections.edges)
                .Select(collectionEdge => (string)collectionEdge.node.title));

            // Create Product
            return new Product
            {
                Name = productNode.title,
                Description = productNode.bodyHtml,
                CreatedDate = DateTime.UtcNow,
                LastModifiedDate = DateTime.UtcNow,
                Price = variants.FirstOrDefault()?.Price ?? 0,
                RegularPrice = variants.FirstOrDefault()?.RegularPrice ?? 0,
                Sku = variants.FirstOrDefault()?.Sku,
                MerchantProductId = productId,
                FkMerchantId = merchant.Id,
                Variants = variants,
                Status = publishStatus,
                Active = isActive,
                Permalink = productPermalink,
                MerchantCreatedDate = productNode.createdAt != null ? DateTime.TryParse((string)productNode.createdAt, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal, out var productCreatedDate) ? productCreatedDate : (DateTime?)null : (DateTime?)null,
                MerchantModifiedDate = productNode.updatedAt != null ? DateTime.TryParse((string)productNode.updatedAt, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal, out var productModifiedDate) ? productModifiedDate : (DateTime?)null : (DateTime?)null,
                Categories = string.Join(",", tags.Split(',').Concat(categories.Split(','))),
                ProductImages = System.Text.Json.JsonSerializer.Serialize(images)
            };
        }));

        products.AddRange(mappedProducts);


            hasNextPage = response.products.pageInfo.hasNextPage;
            cursor = hasNextPage ? (string) response.products.pageInfo.endCursor : null;
        } while (hasNextPage);

        Console.WriteLine("All products fetched.");
        return products;
    }*/

    private async Task<List<Product>> FetchProducts(string shopUrl, string accessToken, Merchant merchant,
        int lookBackDays)
    {
        var stopWatch = new Stopwatch();
        stopWatch.Start();
        var graphService = new GraphService(shopUrl, accessToken);

        var products = new List<Product>();

        // Initial cursor value
        string? cursor = null;
        bool hasNextPage;
        var updatedAtMin = DateTime.UtcNow.AddDays(-lookBackDays).ToString("o");

        const string productQueryCount = $@"
                {{
                    productsCount {{
                        count 
                    }}
                }}";

        // Execute the query
        var responseJsonProductCount = await graphService.PostAsync(productQueryCount);
        dynamic responseProductCount = JObject.Parse(responseJsonProductCount.ToString());
        var totalProductCount = responseProductCount.productsCount.count;
        
        var currentProductCount = 0;
        do
        {
            // GraphQL query with categories embedded //  AND updated_at:>{updatedAtMin}
            var query = $@"
            {{
                products(first: 250, query: ""published_status:published""{(cursor != null ? $", after: \"{cursor}\"" : "")}) {{
                    edges {{
                        node {{
                            id
                            title
                            handle
                            createdAt
                            updatedAt
                            vendor
                            status
                            publishedAt
                            tags
                            description
                            onlineStoreUrl
                            productType
                            variants(first: 250) {{
                                edges {{
                                    node {{
                                        id
                                        title
                                        sku
                                        price
                                        compareAtPrice
                                        inventoryQuantity
                                        createdAt
                                        updatedAt
                                    }}
                                }}
                            }}
                            images(first: {StaticVariables.MaxImages}) {{
                                edges {{
                                    node {{
                                        originalSrc
                                    }}
                                }}
                            }}
                            collections(first: 250) {{
                                edges {{
                                    node {{
                                        id
                                        title
                                    }}
                                }}
                            }}
                        }}
                    }}
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                }}
            }}";

            // Execute the query
            var responseJson = await graphService.PostAsync(query);
            dynamic response = JObject.Parse(responseJson.ToString());

            // Parse products and map to Product objects
            foreach (var edge in response.products.edges)
            {
                var productNode = edge.node;

                var isActive = string.Equals((string) productNode.status, "active", StringComparison.OrdinalIgnoreCase);
                var publishStatus = productNode.publishedAt != null ? "Published" : "Not published";
                var productId = productNode.id != null ? ((string) productNode.id).Split('/').Last() : string.Empty;
                //var baseUrl = merchant.Url.TrimEnd('/');
                //var productPermalink = $"{baseUrl}/products/{productNode.handle}";
                var productPermalink = productNode.onlineStoreUrl;
                if(productPermalink == null)
                    continue;

                // Map Variants
                var variants = new List<Variant>();
                decimal lowestPrice = decimal.MaxValue;
                decimal highestPrice = 0;

                foreach (var variantEdge in productNode.variants.edges)
                {
                    var variantNode = variantEdge.node;

                    var price = decimal.TryParse((string) variantNode.price ?? "0", NumberStyles.Any,
                        CultureInfo.InvariantCulture, out var parsedPrice)
                        ? parsedPrice
                        : 0;

                    if (lowestPrice > price) lowestPrice = price;
                    if (highestPrice < price) highestPrice = price;

                    var variant = new Variant
                    {
                        Description = productNode.bodyHtml,
                        Name = variantNode.title,
                        Permalink = productPermalink,
                        Price = price,
                        RegularPrice =
                            decimal.TryParse((string) variantNode.compareAtPrice ?? (string) variantNode.price,
                                NumberStyles.Any, CultureInfo.InvariantCulture, out var regularPrice)
                                ? regularPrice
                                : price,
                        Sku = variantNode.sku,
                        Status = publishStatus,
                        Active = isActive,
                        CreatedDate = DateTime.UtcNow,
                        LastModifiedDate = DateTime.UtcNow,
                        StockQuantity = Convert.ToInt32((string) variantNode.inventoryQuantity ?? "0"),
                        MerchantVariantId = variantNode.id != null
                            ? ((string) variantNode.id).Split('/').Last()
                            : string.Empty,
                        MerchantProductId = productId,
                        MerchantCreatedDate = variantNode.createdAt != null
                            ? DateTime.TryParse((string) variantNode.createdAt, CultureInfo.InvariantCulture,
                                DateTimeStyles.AssumeUniversal, out var createdDate)
                                ? createdDate
                                : (DateTime?) null
                            : (DateTime?) null,

                        MerchantModifiedDate = variantNode.updatedAt != null
                            ? DateTime.TryParse((string) variantNode.updatedAt, CultureInfo.InvariantCulture,
                                DateTimeStyles.AssumeUniversal, out var modifiedDate)
                                ? modifiedDate
                                : (DateTime?) null
                            : (DateTime?) null,
                    };

                    variants.Add(variant);
                }
                
                // Map Categories (Collections)
                var categories = ((IEnumerable<dynamic>) productNode.collections.edges)
                    .Select(collectionEdge => (string) collectionEdge.node.title)
                    .ToList();

                // Map Images
                var images = ((IEnumerable<dynamic>) productNode.images.edges)
                    .Select(imageEdge => new ProductImageDto
                    {
                        Src = (string) imageEdge.node.originalSrc
                    })
                    .ToList();
 
                // Ensure tags is treated as a string
                // Handle tags as an array
                var tags = productNode.tags != null
                    ? string.Join(",", ((IEnumerable<dynamic>) productNode.tags).Select(tag => (string) tag))
                    : string.Empty;
                
                // Map Product
                var firstVariant = variants.FirstOrDefault();

                // Map Product
                var product = new Product
                {
                    Name = productNode.title,
                    Description = productNode.bodyHtml,
                    CreatedDate = DateTime.UtcNow,
                    LastModifiedDate = DateTime.UtcNow,
                    Price = firstVariant?.Price ?? 0,
                    RegularPrice = firstVariant?.RegularPrice ?? 0,
                    Sku = firstVariant?.Sku,
                    MerchantProductId = productId,
                    FkMerchantId = merchant.Id,
                    Variants = variants,
                    Status = publishStatus,
                    Active = isActive,
                    Permalink = productPermalink,
                    MerchantCreatedDate = productNode.createdAt != null
                        ? DateTime.TryParse((string) productNode.createdAt, CultureInfo.InvariantCulture,
                            DateTimeStyles.AssumeUniversal, out var productCreatedDate)
                            ? productCreatedDate
                            : (DateTime?) null
                        : (DateTime?) null,

                    MerchantModifiedDate = productNode.updatedAt != null
                        ? DateTime.TryParse((string) productNode.updatedAt, CultureInfo.InvariantCulture,
                            DateTimeStyles.AssumeUniversal, out var productModifiedDate)
                            ? productModifiedDate
                            : (DateTime?) null
                        : (DateTime?) null,
                    Categories = string.Join(",", tags.Replace(", ", ",").Split(',').Concat(categories)),
                    ProductImages = System.Text.Json.JsonSerializer.Serialize(images)
                };

                products.Add(product);
            }
            
            currentProductCount += 250;
            Console.WriteLine($"Fetched {currentProductCount} products of {totalProductCount} products.");

            hasNextPage = response.products.pageInfo.hasNextPage;
            cursor = hasNextPage ? (string) response.products.pageInfo.endCursor : null;
        } while (hasNextPage);

        Console.WriteLine("All products fetched.");
        stopWatch.Stop();
        Console.WriteLine($"Fetched {products.Count} products in {stopWatch.ElapsedMilliseconds} ms.");
        return products;
    }


    private async Task UpdateProductDataOld(Merchant merchant, int lookBackDays)
    {
        var apiUrl = merchant.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUrl).Value;
        var apiKey = merchant.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey).Value;

        var customCollectionService = new CustomCollectionService(apiUrl, apiKey);
        var collectService = new CollectService(apiUrl, apiKey);

        Console.WriteLine("Fetching Products..");

        var products = new List<Product>();
        //Get products to sync
        var productService = new ProductService(apiUrl, apiKey);
        var productGraphService = new GraphService(apiUrl, apiKey);
        var productGraphRequest = new GraphRequest();
        // new GraphRequest Like below ListAsync functionality but with GraphRequest which fetches the necessary data


        var productGraphs = await productGraphService.SendAsync(productGraphRequest);

        var allProducts1 = await productService.ListAsync(new ProductListFilter
            {Limit = 250, UpdatedAtMin = DateTime.UtcNow.AddDays(-lookBackDays)});

        var hasNextPage = allProducts1.HasNextPage;
        var allProducts = allProducts1.Items.ToList();
        while (hasNextPage)
        {
            Thread.Sleep(222);
            allProducts1 = await productService.ListAsync(allProducts1.GetNextPageFilter());
            hasNextPage = allProducts1.HasNextPage;
            allProducts.AddRange(allProducts1.Items.ToList());
        }

        //Categories
        var allCategories1 = await customCollectionService.ListAsync(new CustomCollectionListFilter {Limit = 250});
        List<CustomCollection> categories = allCategories1.Items.ToList();
        hasNextPage = allCategories1.HasNextPage;
        while (hasNextPage)
        {
            Thread.Sleep(222);
            allCategories1 = await customCollectionService.ListAsync(allCategories1.GetNextPageFilter());
            hasNextPage = allCategories1.HasNextPage;
            categories.AddRange(allCategories1.Items.ToList());
        }

        var collects1 = await collectService.ListAsync(new CollectListFilter {Limit = 250});
        var collects = collects1.Items.ToList();
        hasNextPage = collects1.HasNextPage;
        while (hasNextPage)
        {
            Thread.Sleep(222);
            collects1 = await collectService.ListAsync(collects1.GetNextPageFilter());
            hasNextPage = collects1.HasNextPage;
            collects.AddRange(collects1.Items.ToList());
        }

        int totalProducts = allProducts.Count;
        int currentProductNumber = 0;

        //Products
        foreach (var product in allProducts)
        {
            currentProductNumber++;
            Console.Write($"\rProcessing product {currentProductNumber} of {totalProducts}...");

            if (products.FirstOrDefault(a => a.MerchantProductId == product.Id.ToString()) == null)
            {
                var temp = await GetProduct(product, merchant, collects,
                        categories)
                    .ConfigureAwait(false);
                if (products.FirstOrDefault(a => a.MerchantProductId == temp.MerchantProductId) == null)
                {
                    products.Add(temp);
                }
            }

            Console.Write($"\rProcessed product {currentProductNumber} of {totalProducts}.");
        }

        //Discounts
        //var service = new PriceRuleService(webShop.AccessUrl, webShop.ApiKey);
        //var priceRules = await service.ListAsync();

        //var discountCodeService = new ShopifySharp.DiscountCodeService (webShop.AccessUrl, webShop.ApiKey);
        //var test = await discountCodeService.pr
        //var discounts = await discountCodeService.ListAsync( );

        Console.WriteLine("\nAll products processed.");

        merchant.Products = products.DistinctBy(a => a.MerchantProductId).ToList();
        await integrationService.SynchronizeWebshopDataAsync(merchant).ConfigureAwait(false);
    }

    private async Task<Product> GetProduct(ShopifySharp.Product found,
        Merchant webShop, List<Collect> collects,
        List<CustomCollection> categories)
    {
        var variants = new List<Variant>();
        var webshopUrl = webShop.Url;
        if (webshopUrl.EndsWith("/"))
        {
            webshopUrl = webshopUrl.Substring(0, webshopUrl.Length - 1);
        }

        decimal lowestPrice = 999999;
        decimal highestPrice = 0;
        foreach (var productVariant in found.Variants)
        {
            if (lowestPrice > productVariant.Price)
            {
                lowestPrice = productVariant.Price ?? 0;
            }

            if (highestPrice < productVariant.Price)
            {
                highestPrice = productVariant.Price ?? 0;
            }
        }

        var active = found.Status == "active";
        var publish = "Published";
        if (found.PublishedAt == null)
        {
            active = false;
            publish = "Not published";
        }

        if (found.Variants.Count() > 1)
        {
            foreach (var productVariant in found.Variants)
            {
                var variant = new Variant
                {
                    Description = found.BodyHtml,
                    Name = productVariant.Title,
                    Permalink = $"{webshopUrl}/products/{found.Handle}",
                    Price = productVariant.Price,
                    RegularPrice = productVariant.CompareAtPrice ?? productVariant.Price,
                    Sku = productVariant.SKU,
                    Status = found.Status + " " + publish,
                    Active = active,
                    CreatedDate = DateTime.UtcNow,
                    LastModifiedDate = DateTime.UtcNow,
                    StockQuantity = Convert.ToInt32(productVariant.InventoryQuantity),
                    MerchantVariantId = productVariant.Id.ToString(),
                    MerchantProductId = found.Id.ToString(),
                    MerchantCreatedDate = productVariant.CreatedAt?.UtcDateTime,
                    MerchantModifiedDate = productVariant.UpdatedAt?.UtcDateTime,
                };
                variants.Add(variant);
            }
        }

        var firstVariant = found.Variants.First();
        var product = new Product
        {
            Name = found.Title,
            Description = found.BodyHtml,
            CreatedDate = DateTime.UtcNow,
            LastModifiedDate = DateTime.UtcNow,
            Price = firstVariant.Price,
            RegularPrice = firstVariant.CompareAtPrice ?? firstVariant.Price,
            Sku = firstVariant.SKU,
            MerchantProductId = found.Id.ToString() ?? "",
            FkMerchantId = webShop.Id,
            Variants = variants,
            Status = found.Status + " " + publish,
            Active = active,
            Permalink = $"{webshopUrl}/products/{found.Handle}",
            MerchantCreatedDate = firstVariant.CreatedAt?.UtcDateTime,
            MerchantModifiedDate = firstVariant.UpdatedAt?.UtcDateTime,
        };

        var collectItems = collects.Where(a => a.ProductId == found.Id).ToList();
        var collections = "";
        foreach (var collect in collectItems)
        {
            var customCollection = categories.SingleOrDefault(a => a.Id == collect.CollectionId);
            if (customCollection != null)
            {
                collections += $"{customCollection.Title},";
            }
        }

        if (!string.IsNullOrEmpty(collections) && collections.EndsWith(","))
        {
            collections = collections.Remove(collections.Length - 1);
            collections = $",{collections}";
        }

        found.Tags = found.Tags.Replace(", ", ",");
        product.Categories = $"{found.Tags}{collections}";
        //Images
        var images = new List<ProductImageDto>();
        //Limit to x images
        foreach (var productImage in
                 found.Images?.Where(a => !a.Src.Contains(".gif")).Take(StaticVariables.MaxImages) ?? [])
        {
            images.Add(new ProductImageDto
            {
                Src = productImage.Src
            });
        }

        product.ProductImages = System.Text.Json.JsonSerializer.Serialize(images);
        return product;
    }


    /* Shopify Partner Section - Only for Shopify Review Team! */

    public async Task<int> PartnerPortalProductsCount(ShopifyLoginDto shopifyLoginDto)
    {
        var shopifyShop = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (shopifyShop == null || shopifyShop.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
            return 0;

        var graphService = new GraphService(shopifyShop.Url, shopifyShop.ApiKey);

        // GraphQL query with categories embedded
        const string query = $@"
                {{
                    productsCount {{
                        count 
                    }}
                }}";

        // Execute the query
        var responseJson = await graphService.PostAsync(query);
        dynamic response = JObject.Parse(responseJson.ToString());

        return response.productsCount.count;
    }

    public async Task<int> PartnerPortalOrdersCount(ShopifyLoginDto shopifyLoginDto)
    {
        var shopifyShop = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (shopifyShop == null || shopifyShop.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
            return 0;

        var graphService = new GraphService(shopifyShop.Url, shopifyShop.ApiKey);
        var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30).ToString("yyyy-MM-ddTHH:mm:ssZ");

        // GraphQL query with categories embedded
        var query = $@"
                {{
                    ordersCount(query: ""created_at:>{thirtyDaysAgo}"") {{
                        count 
                    }}
                }}";

        // Execute the query
        var responseJson = await graphService.PostAsync(query);
        dynamic response = JObject.Parse(responseJson.ToString());

        return response.ordersCount.count;
    }

    public async Task<string> PartnerPortalOrdersAov(ShopifyLoginDto shopifyLoginDto)
    {
        var shopifyShop = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (shopifyShop == null || shopifyShop.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
            return "0";

        var graphService = new GraphService(shopifyShop.Url, shopifyShop.ApiKey);
        var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30).ToString("yyyy-MM-ddTHH:mm:ssZ");


        List<decimal> orderValues = [];
        var currencyCode = "";

        // Initial cursor value
        string? cursor = null;
        bool hasNextPage;

        do
        {
            // GraphQL query for fetching orders with cursor pagination
            var query = $@"
                {{
                    orders(first: 250, query: ""created_at:>{thirtyDaysAgo}""{(cursor != null ? $", after: \"{cursor}\"" : "")}) {{
                        edges {{
                              node {{
                                currencyCode
                                totalPriceSet {{
                                    shopMoney {{
                                        amount
                                    }}
                                }}
                            }}
                        }}
                        pageInfo {{
                            hasNextPage
                            endCursor
                        }}
                    }}
                }}";

            // Execute the query
            var responseJson = await graphService.PostAsync(query);
            dynamic response = JObject.Parse(responseJson.ToString());

            foreach (var order in response.orders.edges)
            {
                if (currencyCode == "")
                    currencyCode = (string) order.node.currencyCode;

                var orderValue = Convert.ToDecimal(order.node.totalPriceSet.shopMoney.amount);
                orderValues.Add(orderValue);
            }

            hasNextPage = response.orders.pageInfo.hasNextPage;
            cursor = hasNextPage ? (string) response.orders.pageInfo.endCursor : null;
        } while (hasNextPage);

        var aov = orderValues.Select(a => a).Average();

        return $"{aov:0.00} {currencyCode}";
    }

    public async Task<List<LabelValue>> PartnerPortalMonthlyRevenue(ShopifyLoginDto shopifyLoginDto)
    {
        var shopifyShop = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (shopifyShop == null || shopifyShop.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
            return [];

        var graphService = new GraphService(shopifyShop.Url, shopifyShop.ApiKey);
        var startOfMonth = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1);
        var today = DateTime.UtcNow.Date;

        Dictionary<string, decimal> revenueByDate = new();
        string? cursor = null;
        bool hasNextPage;

        do
        {
            var query = $@"
            {{
                orders(first: 250, query: ""created_at:>{startOfMonth:yyyy-MM-ddTHH:mm:ssZ}""{(cursor != null ? $", after: \"{cursor}\"" : "")}) {{
                    edges {{
                        node {{
                            createdAt
                            totalPriceSet {{
                                shopMoney {{
                                    amount
                                }}
                            }}
                        }}
                    }}
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                }}
            }}";

            var responseJson = await graphService.PostAsync(query);
            dynamic response = JObject.Parse(responseJson.ToString());

            foreach (var order in response.orders.edges)
            {
                decimal orderValue = Convert.ToDecimal(order.node.totalPriceSet.shopMoney.amount);
                string orderDate = Convert.ToDateTime(order.node.createdAt).Date.ToString("yyyy-MM-dd");

                revenueByDate.TryAdd(orderDate, 0);

                revenueByDate[orderDate] += orderValue;
            }

            hasNextPage = response.orders.pageInfo.hasNextPage;
            cursor = hasNextPage ? (string)response.orders.pageInfo.endCursor : null;

        } while (hasNextPage);

        // Ensure all days of the current month are included
        List<LabelValue> result = [];
        decimal runningTotal = 0;

        for (var date = startOfMonth; date <= today; date = date.AddDays(1))
        {
            string dateKey = date.ToString("yyyy-MM-dd");
            decimal dailyRevenue = revenueByDate.TryGetValue(dateKey, out var value) ? value : 0;

            runningTotal += dailyRevenue;
            result.Add(new LabelValue { Label = dateKey, Value = runningTotal });
        }

        return result;
    }


    public async Task<List<dynamic>> PartnerPortalOrdersTable(ShopifyLoginDto shopifyLoginDto)
    {
        var shopifyShop = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (shopifyShop == null || shopifyShop.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
            return [];

        var graphService = new GraphService(shopifyShop.Url, shopifyShop.ApiKey);
        var startOfMonth = new DateTime(DateTime.UtcNow.Year, DateTime.UtcNow.Month, 1)
            .ToString("yyyy-MM-ddTHH:mm:ssZ");

        List<dynamic> orders = [];
        string? cursor = null;
        bool hasNextPage;

        do
        {
            // GraphQL query for fetching orders with cursor pagination
            var query = $@"
            {{
                orders(first: 250, query: ""created_at:>{startOfMonth}""{(cursor != null ? $", after: \"{cursor}\"" : "")}) {{
                    edges {{
                        node {{
                            name
                            createdAt
                            totalPriceSet {{
                                shopMoney {{
                                    amount
                                }}
                            }}
                            refunds {{
                                totalRefundedSet {{
                                    shopMoney {{
                                        amount
                                    }}
                                }}
                            }}
                        }}
                    }}
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                }}
            }}";

            // Execute the query
            var responseJson = await graphService.PostAsync(query);
            dynamic response = JObject.Parse(responseJson.ToString());

            foreach (var order in response.orders.edges)
            {
                decimal orderValue = Convert.ToDecimal(order.node.totalPriceSet.shopMoney.amount);
                decimal totalRefunds = 0;
                foreach (var refund in order.node.refunds)
                {
                    totalRefunds += Convert.ToDecimal(refund.totalRefundedSet.shopMoney.amount);
                }
                
                DateTime orderDate = Convert.ToDateTime(order.node.createdAt).Date;
                string orderName = (string) order.node.name;

                orders.Add(new
                {
                    OrderDate = orderDate,
                    OrderNumber = orderName,
                    TotalRefunds = totalRefunds,
                    Total = orderValue
                });
            }

            hasNextPage = response.orders.pageInfo.hasNextPage;
            cursor = hasNextPage ? (string) response.orders.pageInfo.endCursor : null;
        } while (hasNextPage);

        return orders;
    }


    public async Task<int> PartnerPortalCustomersCount(ShopifyLoginDto shopifyLoginDto)
    {
        var shopifyShop = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (shopifyShop == null || shopifyShop.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
            return 0;

        var graphService = new GraphService(shopifyShop.Url, shopifyShop.ApiKey);

        // GraphQL query for fetching orders with cursor pagination
        var query = $@"
            {{
                customersCount {{
                    count 
                }}
            }}";

        // Execute the query
        var responseJson = await graphService.PostAsync(query);
        dynamic response = JObject.Parse(responseJson.ToString());

        return response.customersCount.count;
    }


    public async Task<int> PartnerPortalCustomersUniqueEmails(ShopifyLoginDto shopifyLoginDto)
    {
        var shopifyShop = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (shopifyShop == null || shopifyShop.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
            return 0;

        var graphService = new GraphService(shopifyShop.Url, shopifyShop.ApiKey);

        var uniqueEmails = new HashSet<string>();
        string? cursor = null;
        bool hasNextPage;

        do
        {
            var query = $@"
            {{
                customers(first: 250{(cursor != null ? $", after: \"{cursor}\"" : "")}) {{
                    edges {{
                        node {{
                            email
                        }}
                    }}
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                }}
            }}";

            // Execute the query
            var responseJson = await graphService.PostAsync(query);
            dynamic response = JObject.Parse(responseJson.ToString());

            foreach (var customer in response.customers.edges)
            {
                string email = customer.node.email;

                if(!string.IsNullOrEmpty(email))
                    uniqueEmails.Add(email);
            }

            hasNextPage = response.customers.pageInfo.hasNextPage;
            cursor = hasNextPage ? (string) response.customers.pageInfo.endCursor : null;
        } while (hasNextPage);
        return uniqueEmails.Count;
    }


    public async Task<int> PartnerPortalCustomersUniquePhones(ShopifyLoginDto shopifyLoginDto)
    {
        var shopifyShop = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (shopifyShop == null || shopifyShop.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
            return 0;

        var graphService = new GraphService(shopifyShop.Url, shopifyShop.ApiKey);

        var uniquePhones = new HashSet<string>();
        string? cursor = null;
        bool hasNextPage;

        do
        {
            var query = $@"
            {{
                customers(first: 250{(cursor != null ? $", after: \"{cursor}\"" : "")}) {{
                    edges {{
                        node {{
                            phone
                        }}
                    }}
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                }}
            }}";

            // Execute the query
            var responseJson = await graphService.PostAsync(query);
            dynamic response = JObject.Parse(responseJson.ToString());

            foreach (var customer in response.customers.edges)
            {
                string phone = customer.node.phone;

                if(!string.IsNullOrEmpty(phone))
                    uniquePhones.Add(phone);
            }

            hasNextPage = response.customers.pageInfo.hasNextPage;
            cursor = hasNextPage ? (string) response.customers.pageInfo.endCursor : null;
        } while (hasNextPage);
        return uniquePhones.Count;
    }


    public async Task<Dictionary<string, int>> PartnerPortalCustomersActiveState(ShopifyLoginDto shopifyLoginDto)
    {
        var shopifyShop = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (shopifyShop == null || shopifyShop.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
            return new Dictionary<string, int>();

        var graphService = new GraphService(shopifyShop.Url, shopifyShop.ApiKey);

        var activeCustomers = 0;
        var inactiveCustomers = 0;
        
        string? cursor = null;
        bool hasNextPage;

        do
        {
            var query = $@"
            {{
                customers(first: 250{(cursor != null ? $", after: \"{cursor}\"" : "")}) {{
                    edges {{
                        node {{
                            lastOrder {{
                                processedAt
                            }}
                            state
                        }}
                    }}
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                }}
            }}";

            // Execute the query
            var responseJson = await graphService.PostAsync(query);
            dynamic response = JObject.Parse(responseJson.ToString());

            foreach (var customer in response.customers.edges)
            {
                DateTime processedAt = (DateTime) customer.node.lastOrder.processedAt;
                var state = (string) customer.node.state;

                if(processedAt > DateTime.UtcNow.AddYears(-1) && state == "ENABLED")
                    activeCustomers++;
                else
                    inactiveCustomers++;
            }

            hasNextPage = response.customers.pageInfo.hasNextPage;
            cursor = hasNextPage ? (string) response.customers.pageInfo.endCursor : null;
        } while (hasNextPage);
        return new Dictionary<string, int> { { "Active", activeCustomers }, { "Inactive", inactiveCustomers } };
    }


    public async Task<List<LabelValue>> PartnerPortalCustomersMarketingConsents(ShopifyLoginDto shopifyLoginDto)
    {
        var shopifyShop = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (shopifyShop == null || shopifyShop.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
            return [];

        var graphService = new GraphService(shopifyShop.Url, shopifyShop.ApiKey);

        var emailMarketingConsents = 0;
        var smsMarketingConsents = 0;
        
        string? cursor = null;
        bool hasNextPage;

        do
        {
            var query = $@"
            {{
                customers(first: 250{(cursor != null ? $", after: \"{cursor}\"" : "")}) {{
                    edges {{
                        node {{
                            emailMarketingConsent {{
                                marketingState
                            }}
                            smsMarketingConsent {{
                                marketingState
                            }}
                        }}
                    }}
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                }}
            }}";

            // Execute the query
            var responseJson = await graphService.PostAsync(query);
            dynamic response = JObject.Parse(responseJson.ToString());

            foreach (var customer in response.customers.edges)
            {
                var emailConsent = customer.node.emailMarketingConsent?.marketingState;
                var smsConsent = customer.node.smsMarketingConsent?.marketingState;

                if(emailConsent != null && emailConsent == "SUBSCRIBED")
                    emailMarketingConsents++;
                
                if(smsConsent != null && smsConsent == "SUBSCRIBED")
                    smsMarketingConsents++;
            }

            hasNextPage = response.customers.pageInfo.hasNextPage;
            cursor = hasNextPage ? (string) response.customers.pageInfo.endCursor : null;
        } while (hasNextPage);
        return
        [
            new LabelValue() { Label = "EmailConsents", Value = emailMarketingConsents },
            new LabelValue() { Label = "SmsConsents", Value = smsMarketingConsents }
        ];
    }


    public async Task<List<LabelValue>> PartnerPortalTopZipCodes(ShopifyLoginDto shopifyLoginDto)
    {
        var shopifyShop = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (shopifyShop == null || shopifyShop.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
            return [];

        var graphService = new GraphService(shopifyShop.Url, shopifyShop.ApiKey);

        var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30).ToString("yyyy-MM-ddTHH:mm:ssZ");
        var zipCodeOrders = new Dictionary<string, int>();
        
        string? cursor = null;
        bool hasNextPage;

        do
        {
            var query = $@"
            {{
                orders(first: 250, query: ""created_at:>{thirtyDaysAgo}""{(cursor != null ? $", after: \"{cursor}\"" : "")}) {{
                    edges {{
                        node {{
                            displayAddress {{
                                zip
                            }}
                        }}
                    }}
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                }}
            }}";

            // Execute the query
            var responseJson = await graphService.PostAsync(query);
            dynamic response = JObject.Parse(responseJson.ToString());

            foreach (var order in response.orders.edges)
            {
                var displayAddresses = order.node.displayAddress;
                string zipCode = displayAddresses.zip.ToString();
               
                if(!zipCodeOrders.TryAdd(zipCode, 1))
                    zipCodeOrders[zipCode]++;
            }

            hasNextPage = response.orders.pageInfo.hasNextPage;
            cursor = hasNextPage ? (string) response.orders.pageInfo.endCursor : null;
        } while (hasNextPage);
        return zipCodeOrders
            .OrderByDescending(a => a.Value)
            .Take(5)
            .Select(a => new LabelValue { Label = a.Key, Value = a.Value })
            .ToList();
    }


    public async Task<Dictionary<string, int>> PartnerPortalTopTags(ShopifyLoginDto shopifyLoginDto)
    {
        var shopifyShop = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (shopifyShop == null || shopifyShop.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
            return new Dictionary<string, int>();

        var graphService = new GraphService(shopifyShop.Url, shopifyShop.ApiKey);

        var customerTags = new Dictionary<string, int>();
        
        string? cursor = null;
        bool hasNextPage;

        do
        {
            var query = $@"
            {{
                customers(first: 250{(cursor != null ? $", after: \"{cursor}\"" : "")}) {{
                    edges {{
                        node {{
                            tags
                        }}
                    }}
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                }}
            }}";

            // Execute the query
            var responseJson = await graphService.PostAsync(query);
            dynamic response = JObject.Parse(responseJson.ToString());

            foreach (var customer in response.customers.edges)
            {
               var tags = customer.node.tags;

               var tagList = ((JArray)tags).Select(tag => tag.ToString()).ToArray();
               
               foreach (var tag in tagList)
               {
                   if(!customerTags.TryAdd(tag, 1))
                       customerTags[tag]++;
               }
            }

            hasNextPage = response.customers.pageInfo.hasNextPage;
            cursor = hasNextPage ? (string) response.customers.pageInfo.endCursor : null;
        } while (hasNextPage);
        return customerTags.OrderByDescending(a => a.Value).Take(5).ToDictionary(a => a.Key, a => a.Value);
    }

    
    public async Task<dynamic> PartnerPortalTopCustomerEmail(ShopifyLoginDto shopifyLoginDto)
    {
        var shopifyShop = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (shopifyShop == null || shopifyShop.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
            return string.Empty;

        var graphService = new GraphService(shopifyShop.Url, shopifyShop.ApiKey);

        dynamic topCustomerEmail = new 
        {
            numberOfOrder = 0,
            email = "",
            amountSpent = 0,
            lifetimeDuration = 0,
            emailMarketingConsent = ""
        };
        var highestNumberOfOrders = 0;
        
        string? cursor = null;
        bool hasNextPage;

        do
        {
            var query = $@"
            {{
                customers(first: 250{(cursor != null ? $", after: \"{cursor}\"" : "")}) {{
                    edges {{
                        node {{
                            firstName
                            lastName
                            numberOfOrders
                            email
                            amountSpent {{
                                amount
                            }}
                            lifetimeDuration
                            emailMarketingConsent {{
                                marketingState
                            }}
                        }}
                    }}
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                }}
            }}";

            // Execute the query
            var responseJson = await graphService.PostAsync(query);
            dynamic response = JObject.Parse(responseJson.ToString());

            foreach (var customer in response.customers.edges)
            {
                var numberOfOrders = Convert.ToInt32(customer.node.numberOfOrders);
                if (numberOfOrders > highestNumberOfOrders)
                {
                    topCustomerEmail = new
                    {
                        numberOfOrders = Convert.ToInt32(customer.node.numberOfOrders),
                        email = customer.node.email.ToString(),
                        amountSpent = Convert.ToDecimal(customer.node.amountSpent.amount),
                        lifetimeDuration = customer.node.lifetimeDuration.ToString(),
                        emailMarketingConsent = customer.node.emailMarketingConsent.marketingState == "SUBSCRIBED" ? "Subscribed" : "Not Subscribed"
                    };
                    highestNumberOfOrders = numberOfOrders;
                }
            }

            hasNextPage = response.customers.pageInfo.hasNextPage;
            cursor = hasNextPage ? (string) response.customers.pageInfo.endCursor : null;
        } while (hasNextPage);
        return topCustomerEmail;
    }


    
    public async Task<dynamic> PartnerPortalCustomersTable(ShopifyLoginDto shopifyLoginDto)
    {
        var shopifyShop = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (shopifyShop == null || shopifyShop.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
            return string.Empty;

        var graphService = new GraphService(shopifyShop.Url, shopifyShop.ApiKey);

        List<dynamic> customers = [];
        var highestNumberOfOrders = 0;
        
        string? cursor = null;
        bool hasNextPage;

        do
        {
            var query = $@"
            {{
                customers(first: 250{(cursor != null ? $", after: \"{cursor}\"" : "")}) {{
                    edges {{
                        node {{
                            firstName
                            lastName
                            numberOfOrders
                            email
                            amountSpent {{
                                amount
                            }}
                            lifetimeDuration
                            emailMarketingConsent {{
                                marketingState
                            }}
                        }}
                    }}
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                }}
            }}";

            // Execute the query
            var responseJson = await graphService.PostAsync(query);
            dynamic response = JObject.Parse(responseJson.ToString());

            foreach (var customer in response.customers.edges)
            {
                var name = $"{customer.node.firstName.ToString()} {customer.node.lastName.ToString()}";
                var numberOfOrders = Convert.ToInt32(customer.node.numberOfOrders);
                var email = customer.node.email.ToString();
                var amountSpent = Convert.ToDecimal(customer.node.amountSpent.amount);
                var lifetimeDuration = customer.node.lifetimeDuration.ToString();
                var emailMarketingConsent = customer.node.emailMarketingConsent.marketingState == "SUBSCRIBED" ? "Subscribed" : "Not Subscribed";
                customers.Add(new
                {
                    Name = name,
                    NumberOfOrders = numberOfOrders,
                    Email = email,
                    AmountSpent = amountSpent,
                    LifetimeDuration = lifetimeDuration,
                    EmailMarketingConsent = emailMarketingConsent
                });
            }

            hasNextPage = response.customers.pageInfo.hasNextPage;
            cursor = hasNextPage ? (string) response.customers.pageInfo.endCursor : null;
        } while (hasNextPage);
        return customers;
    }

    public async Task<IEnumerable<Product>> PartnerPortalProducts(ShopifyLoginDto shopifyLoginDto)
    {
        var shopifyShop = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (shopifyShop == null || shopifyShop.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
            return new List<Product>();

        var graphService = new GraphService(shopifyShop.Url, shopifyShop.ApiKey);

        var products = new List<Product>();

        // Initial cursor value
        string? cursor = null;
        bool hasNextPage;

        do
        {
            // GraphQL query with categories embedded
            var query = $@"
            {{
                products(first: 250, {(cursor != null ? $"after: \"{cursor}\"" : "")}) {{
                    edges {{
                        node {{
                            id
                            title
                            handle
                            createdAt
                            updatedAt
                            vendor
                            status
                            publishedAt
                            tags
                            bodyHtml
                            variants(first: 250) {{
                                edges {{
                                    node {{
                                        id
                                        title
                                        sku
                                        price
                                        compareAtPrice
                                        inventoryQuantity
                                        createdAt
                                        updatedAt
                                    }}
                                }}
                            }}
                            images(first: {StaticVariables.MaxImages}) {{
                                edges {{
                                    node {{
                                        originalSrc
                                    }}
                                }}
                            }}
                            collections(first: 250) {{
                                edges {{
                                    node {{
                                        id
                                        title
                                    }}
                                }}
                            }}
                        }}
                    }}
                    pageInfo {{
                        hasNextPage
                        endCursor
                    }}
                }}
            }}";

            // Execute the query
            var responseJson = await graphService.PostAsync(query);
            dynamic response = JObject.Parse(responseJson.ToString());

            // Parse products and map to Product objects
            foreach (var edge in response.products.edges)
            {
                var productNode = edge.node;

                var isActive = string.Equals((string) productNode.status, "active",
                    StringComparison.OrdinalIgnoreCase);
                var publishStatus = productNode.publishedAt != null ? "Published" : "Not published";
                var productId = productNode.id != null ? ((string) productNode.id).Split('/').Last() : string.Empty;
                var baseUrl = shopifyShop.Url.TrimEnd('/');
                var productPermalink = $"{baseUrl}/products/{productNode.handle}";


                // Map Variants
                var variants = new List<Variant>();
                decimal lowestPrice = decimal.MaxValue;
                decimal highestPrice = 0;

                foreach (var variantEdge in productNode.variants.edges)
                {
                    var variantNode = variantEdge.node;

                    var price = decimal.TryParse((string) variantNode.price ?? "0", NumberStyles.Any,
                        CultureInfo.InvariantCulture, out var parsedPrice)
                        ? parsedPrice
                        : 0;

                    if (lowestPrice > price) lowestPrice = price;
                    if (highestPrice < price) highestPrice = price;

                    var variant = new Variant
                    {
                        Description = productNode.bodyHtml,
                        Name = variantNode.title,
                        Permalink = productPermalink,
                        Price = price,
                        RegularPrice =
                            decimal.TryParse((string) variantNode.compareAtPrice ?? (string) variantNode.price,
                                NumberStyles.Any, CultureInfo.InvariantCulture, out var regularPrice)
                                ? regularPrice
                                : price,
                        Sku = variantNode.sku,
                        Status = publishStatus,
                        Active = isActive,
                        CreatedDate = DateTime.UtcNow,
                        LastModifiedDate = DateTime.UtcNow,
                        StockQuantity = Convert.ToInt32((string) variantNode.inventoryQuantity ?? "0"),
                        MerchantVariantId = variantNode.id != null
                            ? ((string) variantNode.id).Split('/').Last()
                            : string.Empty,
                        MerchantProductId = productId,
                        MerchantCreatedDate = variantNode.createdAt != null
                            ? DateTime.TryParse((string) variantNode.createdAt, CultureInfo.InvariantCulture,
                                DateTimeStyles.AssumeUniversal, out var createdDate)
                                ? createdDate
                                : (DateTime?) null
                            : (DateTime?) null,

                        MerchantModifiedDate = variantNode.updatedAt != null
                            ? DateTime.TryParse((string) variantNode.updatedAt, CultureInfo.InvariantCulture,
                                DateTimeStyles.AssumeUniversal, out var modifiedDate)
                                ? modifiedDate
                                : (DateTime?) null
                            : (DateTime?) null,
                    };

                    variants.Add(variant);
                }

                // Ensure tags is treated as a string
                // Handle tags as an array
                var tags = productNode.tags != null
                    ? string.Join(",", ((IEnumerable<dynamic>) productNode.tags).Select(tag => (string) tag))
                    : string.Empty;


                // Map Categories (Collections)
                var categories = ((IEnumerable<dynamic>) productNode.collections.edges)
                    .Select(collectionEdge => (string) collectionEdge.node.title)
                    .ToList();

                // Map Images
                var images = ((IEnumerable<dynamic>) productNode.images.edges)
                    .Select(imageEdge => new ProductImageDto
                    {
                        Src = (string) imageEdge.node.originalSrc
                    })
                    .ToList();

                // Map Product
                var firstVariant = variants.FirstOrDefault();

                // Map Product
                var product = new Product
                {
                    Name = productNode.title,
                    Description = productNode.bodyHtml,
                    CreatedDate = DateTime.UtcNow,
                    LastModifiedDate = DateTime.UtcNow,
                    Price = firstVariant?.Price ?? 0,
                    RegularPrice = firstVariant?.RegularPrice ?? 0,
                    Sku = firstVariant?.Sku,
                    MerchantProductId = productId,
                    Variants = variants,
                    Status = publishStatus,
                    Active = isActive,
                    Permalink = productPermalink,
                    MerchantCreatedDate = productNode.createdAt != null
                        ? DateTime.TryParse((string) productNode.createdAt, CultureInfo.InvariantCulture,
                            DateTimeStyles.AssumeUniversal, out var productCreatedDate)
                            ? productCreatedDate
                            : (DateTime?) null
                        : (DateTime?) null,

                    MerchantModifiedDate = productNode.updatedAt != null
                        ? DateTime.TryParse((string) productNode.updatedAt, CultureInfo.InvariantCulture,
                            DateTimeStyles.AssumeUniversal, out var productModifiedDate)
                            ? productModifiedDate
                            : (DateTime?) null
                        : (DateTime?) null,
                    Categories = string.Join(",", tags.Replace(", ", ",").Split(',').Concat(categories)),
                    ProductImages = System.Text.Json.JsonSerializer.Serialize(images)
                };

                products.Add(product);
            }


            hasNextPage = response.products.pageInfo.hasNextPage;
            cursor = hasNextPage ? (string) response.products.pageInfo.endCursor : null;
        } while (hasNextPage);

        return products;
    }

    public async Task<IEnumerable<Order>> PartnerPortalOrders(ShopifyLoginDto shopifyLoginDto)
    {
        var cacheKey = $"PartnerPortalOrders_{shopifyLoginDto.Username}";
        if (!memoryCache.TryGetValue(cacheKey, out List<Order> orderList))
        {
            var response = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
            if (response == null || response.Id == 0 || shopifyLoginDto.Password != "rakl23LKJAD213knsa3U12B")
                return new List<Order>();

            //Get all orders for full data
            var orderService = new OrderService(response.Url, response.ApiKey);
            var orders = await orderService.ListAsync(new OrderListFilter
            {
                Limit = 250,
                Status = "any"
            });
            orderList = orders.Items.ToList();
            var hasNextPage = orders.HasNextPage;
            while (hasNextPage)
            {
                orders = await orderService.ListAsync(orders.GetNextPageFilter());
                hasNextPage = orders.HasNextPage;
                orderList.AddRange(orders.Items);
            }

            var cacheEntryOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(10)
            };
            memoryCache.Set(cacheKey, orderList, cacheEntryOptions);
        }

        return orderList;
    }

    public async Task<ShopifyPartnerPortalStats> PartnerPortalOrderStats(ShopifyLoginDto shopifyLoginDto)
    {
        var stats = new ShopifyPartnerPortalStats();
        var phoneCount = new Dictionary<string, int>();
        var emailPhoneCount = new Dictionary<string, int>();
        var emailCount = new Dictionary<string, int>();
        var emailOrdersCount = new Dictionary<string, int>();
        var phoneOrdersCount = new Dictionary<string, int>();
        var customers = new Dictionary<string, CustomerOrderInfoDto>();

        var response = await shopifyService.GetShopifyShop(shopifyLoginDto.Username);
        if (response != null && response.Id != 0 && shopifyLoginDto.Password == "rakl23LKJAD213knsa3U12B")
        {
            var orderList = await PartnerPortalOrders(shopifyLoginDto);
            var orders = orderList.ToList();

            //stats.TotalOrders = orders.Count;

            foreach (var order in orders)
            {
                // Phone Statistics
                var phoneNumber = order.Phone ?? order.BillingAddress?.Phone ?? order.ShippingAddress?.Phone;
                if (!string.IsNullOrEmpty(phoneNumber))
                {
                    //stats.OrdersWithPhoneNumber++;
                    if (phoneCount.ContainsKey(phoneNumber))
                    {
                        phoneCount[phoneNumber]++;
                        if (phoneCount[phoneNumber] > 1)
                        {
                            //stats.OrdersWithSamePhoneNumber++;
                            stats.RepeatPhones++;
                        }
                    }
                    else
                    {
                        phoneCount[phoneNumber] = 1;
                        //stats.PhoneStats.UniquePhoneNumbers++;
                    }

                    /*if (!string.IsNullOrEmpty(order.Email))
                    {
                        //stats.OrdersWithEmailAndPhoneNumber++;
                        var emailPhoneCombo = $"{order.Email}_{phoneNumber}";
                        if (!emailPhoneCount.TryAdd(emailPhoneCombo, 1))
                        {
                            emailPhoneCount[emailPhoneCombo]++;
                            if (emailPhoneCount[emailPhoneCombo] > 1)
                            {
                                stats.OrdersWithSameEmailAndPhoneNumber++;
                            }
                        }
                    }*/
                }

                // Email statistics
                if (!string.IsNullOrEmpty(order.Email))
                {
                    if (emailCount.ContainsKey(order.Email))
                    {
                        emailCount[order.Email]++;
                        if (emailCount[order.Email] > 1)
                        {
                            stats.RepeatEmails++;
                        }
                    }
                    else
                    {
                        emailCount[order.Email] = 1;
                        //stats.EmailStats.UniqueEmails++;
                    }
                }

                // Customer Orders Tracking
                /*if (!string.IsNullOrEmpty(order.Email))
                {
                    emailOrdersCount.TryGetValue(order.Email, out var currentEmailCount);
                    emailOrdersCount[order.Email] = currentEmailCount + 1;
                    if (!customers.ContainsKey(order.Email))
                    {
                        customers[order.Email] = new CustomerOrderInfoDto {Email = order.Email, TotalOrders = 0};
                    }

                    customers[order.Email].TotalOrders++;
                    customers[order.Email].LastOrderPlaced = order.CreatedAt;
                }*/

                /*if (!string.IsNullOrEmpty(phoneNumber))
                {
                    phoneOrdersCount.TryGetValue(phoneNumber, out var currentPhoneCount);
                    phoneOrdersCount[phoneNumber] = currentPhoneCount + 1;
                    if (!customers.ContainsKey(order.Email))
                    {
                        customers[order.Email] = new CustomerOrderInfoDto
                            {Email = order.Email, Phone = phoneNumber, TotalOrders = 0};
                    }

                    customers[order.Email].Phone = phoneNumber;
                    customers[order.Email].TotalOrders++;
                    customers[order.Email].LastOrderPlaced = order.CreatedAt;
                }*/
            }

            //ZipCode
            /*var ordersByZipCodes = orders.GroupBy(a => a.BillingAddress.Zip).ToList();
            foreach (var ordersByZipCode in ordersByZipCodes)
            {
                stats.OrdersZipCode.Add(new OrdersZipCodeDto
                {
                    Orders = ordersByZipCode.Count(),
                    ZipCode = ordersByZipCode.Key ?? "Unknown"
                });
            }*/

            /*if (stats.TotalOrders > 0)
            {
                stats.PercentageOfOrdersWithPhoneNumber =
                    (double) stats.OrdersWithPhoneNumber / stats.TotalOrders * 100;
                stats.PercentageOfOrdersWithSamePhoneNumber =
                    (double) stats.OrdersWithSamePhoneNumber / stats.TotalOrders * 100;
                stats.PercentageOfOrdersWithEmailAndPhoneNumber =
                    (double) stats.OrdersWithEmailAndPhoneNumber / stats.TotalOrders * 100;
                stats.PercentageOfOrdersWithSameEmailAndPhoneNumber =
                    (double) stats.OrdersWithSameEmailAndPhoneNumber / stats.TotalOrders * 100;

                // Percentage of orders with unique emails
                stats.EmailStats.PercentageOfUniqueEmails =
                    (double) stats.EmailStats.UniqueEmails / stats.TotalOrders * 100;
                // Percentage of orders with repeat emails
                stats.EmailStats.PercentageOfRepeatEmails =
                    (double) stats.EmailStats.RepeatEmails / stats.TotalOrders * 100;

                // Percentage of orders with unique phone numbers
                stats.PhoneStats.PercentageOfUniquePhoneNumbers =
                    (double) stats.PhoneStats.UniquePhoneNumbers / stats.TotalOrders * 100;
                // Percentage of orders with repeat phone numbers
                stats.PhoneStats.PercentageOfRepeatPhoneNumbers =
                    (double) stats.PhoneStats.RepeatPhoneNumbers / stats.TotalOrders * 100;
            }*/


            // Top Customer By Emails
            /*if (emailOrdersCount.Count > 0)
            {
                var topEmailCustomer = emailOrdersCount.OrderByDescending(e => e.Value).First();
                stats.EmailStats.TopCustomerByEmail = topEmailCustomer.Key;
                stats.EmailStats.TopCustomerByEmailOrders = topEmailCustomer.Value;
                stats.EmailStats.TopCustomerByEmailPercentage =
                    (double) topEmailCustomer.Value / stats.TotalOrders * 100;
            }*/

            // Top Customer By Phone
            /*if (phoneOrdersCount.Count > 0)
            {
                var topPhoneCustomer = phoneOrdersCount.OrderByDescending(p => p.Value).First();
                stats.PhoneStats.TopCustomerByPhone = topPhoneCustomer.Key;
                stats.PhoneStats.TopCustomerByPhoneOrders = topPhoneCustomer.Value;
                stats.PhoneStats.TopCustomerByPhonePercentage =
                    (double) topPhoneCustomer.Value / stats.TotalOrders * 100;
            }*/

            // All customers ordered by total orders and last order date
            /*stats.CustomerOrderInfos = customers.Values.OrderByDescending(c => c.TotalOrders)
                .ThenByDescending(c => c.LastOrderPlaced).ToList();*/
        }

        return stats;
    }
}