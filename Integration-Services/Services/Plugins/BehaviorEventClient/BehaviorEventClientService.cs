using ILogger = Serilog.ILogger;

namespace Marlin_OS_Integration_API.Services.Plugins;

public class BehaviorEventClientService(HttpClient httpClient, ILogger logger) : IBehaviorEventClientService
{
    public void FireAndForgetPost(string endpoint, HttpContent content)
    {
        Task.Run(async () =>
        {
            try
            {
                httpClient.PostAsync(endpoint, content);
                /*if (!response.IsSuccessStatusCode)
                {
                    logger.Error("Failed to post to behavior event endpoint: {endpoint}. Status code: {statusCode}", endpoint, response.StatusCode);
                    Console.WriteLine("Failed to post to behavior event endpoint: {endpoint}. Status code: {statusCode}", endpoint, response.StatusCode);
                }*/
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Failed to post to behavior event endpoint: {endpoint}", endpoint);
                Console.WriteLine("Failed to post to behavior event endpoint: {endpoint}", endpoint);
            }
        });
    }
}