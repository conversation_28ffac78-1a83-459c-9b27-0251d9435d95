using System.Globalization;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Xml;
using Integration.Services.Plugins.Integration;
using Marlin_OS_MerchantSync_API.Services.Plugins.ProductFeedIntegration.Handlers;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.VisualBasic.FileIO;
using Shared.Dto.Webshop;
using Shared.Models;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace Marlin_OS_MerchantSync_API.Services.Plugins.ProductFeedIntegration;

public class ProductFeedIntegrationService : IProductFeedIntegrationService
{
    private readonly IIntegrationService _integrationService;
    private readonly IMerchantService _merchantService;


    public ProductFeedIntegrationService(ILogger logger, IIntegrationService integrationService,
        IMerchantService merchantService)
    {
        _integrationService = integrationService;
        _merchantService = merchantService;
    }
    
    public async Task<ResponseDto> HandleProductFeeds()
    {
        var merchant = await _merchantService.GetProductFeedsAsync();
        if (merchant == null)
        {
            return new ResponseDto { Success = true, Message = "No product feeds to load" };
        }

        var products = new List<Product>();
        var handlers = new Dictionary<string, IProductFeedHandler>
        {
            { "csv", new CsvProductFeedHandler() },
            { "xml", new XmlProductFeedHandler() },
            { "cdata", new CDataProductFeedHandler() }
        };

        foreach (var productFeed in merchant.MerchantProductFeeds)
        {
            if (handlers.TryGetValue(productFeed.Type.ToLower(), out var handler))
            {
                var feedProducts = await handler.HandleFeedAsync(productFeed);
                products.AddRange(feedProducts);
            }
        }

        if (products.Count == 0) return new ResponseDto {Success = true, Message = "Success"};
        
        
        var distinctProducts = products.DistinctBy(p => p.MerchantProductId).ToList();
        var webshopReturn = new Merchant
        {
            Id = merchant.Id,
            Type = "Custom",
            Products = distinctProducts,
        };
        await _integrationService.SynchronizeWebshopDataAsync(webshopReturn, true);

        return new ResponseDto { Success = true, Message = "Success" };
    }


    /*public async Task<ResponseDto> HandleProductFeeds()
    {
        var merchant = await _merchantService.GetProductFeedsAsync();
        if (merchant == null)
        {
            return new ResponseDto()
            {
                Success = true,
                Message = "No product feeds to load"
            };
        }

        var products = new List<Product>();
        var count = 0;

        foreach (var webshopsProductFeed in merchant.MerchantProductFeeds)
        {
            if (webshopsProductFeed.Type == "csv")
            {
                /*
                0 Id: string
                 1 Name: string
                 2 ProductLink: string
                 3 ProductImageLink: string
                 4 Price: decimal
                 5 OriginalPrice: decimal
                 6 OnStock: bool
                 7 Category: string (IT > Tv)
                 8: HideInMarketing: Bool
                 #1#

                var datas = new List<List<string>>();
                using (var webClient = new WebClient())
                {
                    webClient.Encoding = Encoding.UTF8;
                    var rows = webClient.DownloadString(webshopsProductFeed.Url);
                    TextFieldParser parser = new TextFieldParser(new StringReader(rows));
                    parser.HasFieldsEnclosedInQuotes = true;
                    parser.SetDelimiters(webshopsProductFeed.Delimiter);

                    string[] fields;

                    while (!parser.EndOfData)
                    {
                        fields = parser.ReadFields();
                        var temp = new List<string>();
                        foreach (string field in fields)
                        {
                            temp.Add(field);
                        }

                        datas.Add(temp);
                    }

                    parser.Close();
                }

                foreach (var data in datas)
                {
                    count++;
                    if (count == 1)
                    {
                        continue;
                    }

                    var price = Convert.ToDecimal(data[Convert.ToInt32(webshopsProductFeed.Price)]
                        .Replace(",", ".").Replace(" DKK", ""), CultureInfo.InvariantCulture);
                    var regularPrice = Convert.ToDecimal(data[Convert.ToInt32(webshopsProductFeed.RegularPrice)]
                        .Replace(",", ".").Replace(" DKK", ""), CultureInfo.InvariantCulture);

                    var product = new Product
                    {
                        MerchantProductId = data[Convert.ToInt32(webshopsProductFeed.Sku)],
                        Name = data[Convert.ToInt32(webshopsProductFeed.Name)],
                        Description = "",
                        ShortDescription = "",
                        Sku = data[Convert.ToInt32(webshopsProductFeed.Sku)],
                        Status = "Ukendt",
                        Active = true,
                        Price = price,
                        RegularPrice = regularPrice,
                        StockQuantity = null,
                        Permalink = data[Convert.ToInt32(webshopsProductFeed.PermaLink)],
                        CreatedDate = DateTime.UtcNow,
                        LastModifiedDate = DateTime.UtcNow,
                        MerchantCreatedDate = null,
                        MerchantModifiedDate = null
                    };

                    //Fix for Computersalg
                    var img = data[Convert.ToInt32(webshopsProductFeed.PictureLink)];
                    product.ProductImages = JsonSerializer.Serialize(new List<ProductImageDto>
                    {
                        new()
                        {
                            Src = img
                        }
                    });

                    products.Add(product);
                }
            }
            else if (webshopsProductFeed.Type == "xml")
            {
                var httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Accept.Clear();
                httpClient.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
                httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

                var errorCount = 0;
                var lineCount = 0;
                string tempFilePath = Path.GetTempFileName();

                while (errorCount < 5)
                {
                    try
                    {
                        using (var response = await httpClient.GetAsync(webshopsProductFeed.Url,
                                   HttpCompletionOption.ResponseHeadersRead))
                        {
                            response.EnsureSuccessStatusCode();

                            using (var stream = await response.Content.ReadAsStreamAsync())
                            using (var reader = new StreamReader(stream, Encoding.UTF8))
                            using (var writer = new StreamWriter(tempFilePath, false, Encoding.UTF8))
                            {
                                string line;
                                while ((line = await reader.ReadLineAsync()) != null)
                                {
                                    lineCount++;
                                    writer.WriteLine(line);
                                }
                            }
                        }

                        break;
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        await Task.Delay(1000 * 120 * errorCount); // Exponential backoff
                    }
                }

                if (lineCount < 10)
                {
                    return new ResponseDto();
                }

                XmlDocument xmlDoc = new XmlDocument();
                xmlDoc.Load(tempFilePath);
                File.Delete(tempFilePath);
                XmlElement root = xmlDoc.DocumentElement;
                foreach (XmlNode node in root.ChildNodes)
                {
                    foreach (XmlNode node1 in node.ChildNodes)
                    {
                        var id = "";
                        var name = "";
                        var description = "";
                        var link = "";
                        var image = "";
                        decimal? price = null;
                        decimal? sale_price = null;
                        var gtin = "";
                        var availability = "";
                        var product_type = "";
                        var active = true;

                        foreach (XmlNode item in node1.ChildNodes)
                        {
                            if (webshopsProductFeed.Name == item.Name)
                            {
                                name = item.InnerText;
                            }
                            else if (webshopsProductFeed.Sku == item.Name)
                            {
                                id = item.InnerText;
                            }
                            else if (webshopsProductFeed.Description == item.Name)
                            {
                                description = item.InnerText;
                            }
                            else if (webshopsProductFeed.PictureLink == item.Name)
                            {
                                image = item.InnerText;
                            }
                            else if (webshopsProductFeed.PermaLink == item.Name)
                            {
                                link = item.InnerText;
                            }
                            else if (webshopsProductFeed.StockStatus == item.Name)
                            {
                                if (item.InnerText == "out of stock")
                                {
                                    active = false;
                                }

                                availability = item.InnerText;
                            }
                            else if (webshopsProductFeed.Price == item.Name)
                            {
                                if (decimal.TryParse(
                                        item.InnerText
                                            .Replace(",", ".").Replace("DKK", "").Replace(" ", ""),
                                        NumberStyles.Any,
                                        CultureInfo.InvariantCulture,
                                        out decimal result
                                    ))
                                {
                                    sale_price = result;
                                }
                            }
                            else if (webshopsProductFeed.RegularPrice == item.Name)
                            {
                                if (decimal.TryParse(
                                        item.InnerText
                                            .Replace(",", ".").Replace("DKK", "").Replace(" ", ""),
                                        NumberStyles.Any,
                                        CultureInfo.InvariantCulture,
                                        out decimal result
                                    ))
                                {
                                    price = result;
                                }
                            }
                            else if (webshopsProductFeed.Categories == item.Name)
                            {
                                product_type = item.InnerText;
                            }
                        }

                        if (id != "")
                        {
                            products.Add(new Product
                            {
                                FkMerchantId = webshopsProductFeed.FkMerchantId,
                                MerchantProductId = id,
                                Name = name,
                                Description = description,
                                ShortDescription = "",
                                Sku = id,
                                Status = availability,
                                Active = active,
                                Price = sale_price,
                                RegularPrice = price,
                                StockQuantity = null,
                                Permalink = link,
                                Categories = product_type,
                                CreatedDate = DateTime.UtcNow,
                                LastModifiedDate = DateTime.UtcNow,
                                MerchantCreatedDate = null,
                                MerchantModifiedDate = null,
                                ProductImages = JsonSerializer.Serialize(new List<ProductImageDto>
                                {
                                    new()
                                    {
                                        Src = image
                                    }
                                })
                            });
                        }
                    }
                }
            }
            else if (webshopsProductFeed.Type == "CDATA")
            {
                var httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Accept.Clear();
                httpClient.DefaultRequestHeaders.Accept.Add(
                    new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/xml"));
                httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

                var errorCount = 0;
                var lineCount = 0;
                string tempFilePath = Path.GetTempFileName();

                while (errorCount < 5)
                {
                    try
                    {
                        using (var response = await httpClient.GetAsync(webshopsProductFeed.Url,
                                   HttpCompletionOption.ResponseHeadersRead))
                        {
                            response.EnsureSuccessStatusCode();

                            using (var stream = await response.Content.ReadAsStreamAsync())
                            using (var reader = new StreamReader(stream, Encoding.UTF8))
                            using (var writer = new StreamWriter(tempFilePath, false, Encoding.UTF8))
                            {
                                string line;
                                while ((line = await reader.ReadLineAsync()) != null)
                                {
                                    lineCount++;
                                    writer.WriteLine(line);
                                }
                            }
                        }

                        break;
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        await Task.Delay(1000 * 120 * errorCount); // Exponential backoff
                    }
                }

                if (lineCount < 10)
                {
                    return new ResponseDto();
                }

                XmlDocument xmlDoc = new XmlDocument();
                xmlDoc.Load(tempFilePath);
                File.Delete(tempFilePath);
                XmlElement root = xmlDoc.DocumentElement;
                foreach (XmlNode node in root.ChildNodes)
                {
                    foreach (XmlNode node1 in node.ChildNodes)
                    {
                        var id = "";
                        var name = "";
                        var description = "";
                        var link = "";
                        var image = "";
                        decimal? price = null;
                        decimal? sale_price = null;
                        var gtin = "";
                        var availability = "";
                        var product_type = "";
                        var active = true;

                        foreach (XmlNode item in node1.ChildNodes)
                        {
                            if (webshopsProductFeed.Name == item.Name)
                            {
                                name = item.InnerText.Trim();
                            }
                            else if (webshopsProductFeed.Sku == item.Name)
                            {
                                id = item.InnerText.Trim();
                            }
                            else if (webshopsProductFeed.Description == item.Name)
                            {
                                description = item.InnerText.Trim();
                            }
                            else if (webshopsProductFeed.PictureLink == item.Name)
                            {
                                image = item.InnerText.Trim();
                            }
                            else if (webshopsProductFeed.PermaLink == item.Name)
                            {
                                link = item.InnerText.Trim();
                            }
                            else if (webshopsProductFeed.StockStatus == item.Name)
                            {
                                if (item.InnerText.Trim().ToLower() == "out of stock")
                                {
                                    active = false;
                                }

                                availability = item.InnerText.Trim();
                            }
                            else if (webshopsProductFeed.Price == item.Name)
                            {
                                if (decimal.TryParse(
                                        item.InnerText
                                            .Replace(",", ".").Replace("DKK", "").Replace(" ", ""),
                                        NumberStyles.Any,
                                        CultureInfo.InvariantCulture,
                                        out decimal result
                                    ))
                                {
                                    sale_price = result;
                                }
                            }
                            else if (webshopsProductFeed.RegularPrice == item.Name)
                            {
                                if (decimal.TryParse(
                                        item.InnerText
                                            .Replace(",", ".").Replace("DKK", "").Replace(" ", ""),
                                        NumberStyles.Any,
                                        CultureInfo.InvariantCulture,
                                        out decimal result
                                    ))
                                {
                                    price = result;
                                }
                            }
                            if (!price.HasValue)
                            {
                                price = sale_price;
                            }
                            else if (webshopsProductFeed.Categories == item.Name)
                            {
                                product_type = item.InnerText.Trim();
                            }
                        }

                        if (!string.IsNullOrEmpty(id))
                        {
                            products.Add(new Product
                            {
                                FkMerchantId = webshopsProductFeed.FkMerchantId,
                                MerchantProductId = id,
                                Name = name,
                                Description = description,
                                ShortDescription = "",
                                Sku = id,
                                Status = availability,
                                Active = active,
                                Price = sale_price,
                                RegularPrice = price,
                                StockQuantity = null,
                                Permalink = link,
                                Categories = product_type,
                                CreatedDate = DateTime.UtcNow,
                                LastModifiedDate = DateTime.UtcNow,
                                MerchantCreatedDate = null,
                                MerchantModifiedDate = null,
                                ProductImages = JsonSerializer.Serialize(new List<ProductImageDto>
                                {
                                    new()
                                    {
                                        Src = image
                                    }
                                })
                            });
                        }
                    }
                }
            }


            if (products.Count != 0)
            {
                var webshopReturn = new Merchant
                {
                    Id = webshopsProductFeed.FkMerchantId,
                    Type = "Custom",
                    Products = products,
                };

                webshopReturn.Products = products.DistinctBy(a => a.MerchantProductId).ToList();
                await _integrationService.SynchronizeWebshopDataAsync(webshopReturn, true).ConfigureAwait(false);
            }
        }

        return new ResponseDto()
        {
            Success = true,
            Message = "Success"
        };
    }*/
}