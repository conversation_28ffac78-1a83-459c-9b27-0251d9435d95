using System.Globalization;
using System.Text.Json;
using System.Xml;
using Merchant_Services.Models.ModelsDal.Merchant;
using Shared.Dto.Webshop;

namespace Marlin_OS_MerchantSync_API.Services.Plugins.ProductFeedIntegration.Handlers;

public class CDataProductFeedHandler : IProductFeedHandler
{
    public async Task<List<Product>> HandleFeedAsync(MerchantProductFeed productFeed)
    {
        var products = new List<Product>();
        var httpClient = new HttpClient();
        httpClient.DefaultRequestHeaders.Accept.Clear();
        httpClient.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/xml"));
        httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

        string xmlContent = await FetchXmlContentAsync(httpClient, productFeed.Url);
        if (string.IsNullOrEmpty(xmlContent))
            return products;

        XmlDocument xmlDoc = new XmlDocument();
        xmlDoc.LoadXml(xmlContent);
        XmlElement root = xmlDoc.DocumentElement;

        foreach (XmlNode node in root.ChildNodes)
        {
            var product = ParseCDataXmlNodeToProduct(node, productFeed);
            if (product != null)
                products.Add(product);
        }

        return products;
    }

    private async Task<string> FetchXmlContentAsync(HttpClient httpClient, string url)
    {
        int errorCount = 0;
        while (errorCount < 5)
        {
            try
            {
                var response = await httpClient.GetAsync(url, HttpCompletionOption.ResponseHeadersRead);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsStringAsync();
            }
            catch
            {
                errorCount++;
                await Task.Delay(1000 * 120 * errorCount);
            }
        }
        return null;
    }

    private Product ParseCDataXmlNodeToProduct(XmlNode node, MerchantProductFeed productFeed)
    {
        var id = node.SelectSingleNode(productFeed.Sku)?.InnerText.Trim();
        if (string.IsNullOrEmpty(id))
            return null;

        var name = node.SelectSingleNode(productFeed.Name)?.InnerText.Trim();
        var description = node.SelectSingleNode(productFeed.Description)?.InnerText.Trim();
        var link = node.SelectSingleNode(productFeed.PermaLink)?.InnerText.Trim();
        var image = node.SelectSingleNode(productFeed.PictureLink)?.InnerText.Trim();
        var availability = node.SelectSingleNode(productFeed.StockStatus)?.InnerText.Trim();
        var priceNode = node.SelectSingleNode(productFeed.Price)?.InnerText.Trim();
        var regularPriceNode = node.SelectSingleNode(productFeed.RegularPrice)?.InnerText.Trim();

        decimal? salePrice = ParseDecimal(priceNode);
        decimal? price = ParseDecimal(regularPriceNode) ?? salePrice;

        var product = new Product
        {
            FkMerchantId = productFeed.FkMerchantId,
            MerchantProductId = id,
            Name = name,
            Description = description,
            ShortDescription = "",
            Sku = id,
            Status = availability,
            Active = true,
            Price = salePrice,
            RegularPrice = price,
            StockQuantity = null,
            Permalink = link,
            Categories = node.SelectSingleNode(productFeed.Categories)?.InnerText.Trim(),
            CreatedDate = DateTime.UtcNow,
            LastModifiedDate = DateTime.UtcNow,
            ProductImages = JsonSerializer.Serialize(new List<ProductImageDto>
            {
                new ProductImageDto { Src = image }
            })
        };

        return product;
    }

    private decimal? ParseDecimal(string value)
    {
        if (decimal.TryParse(value?.Replace(",", ".").Replace("DKK", "").Trim(), NumberStyles.Any, CultureInfo.InvariantCulture, out var result))
            return result;
        return null;
    }
}
