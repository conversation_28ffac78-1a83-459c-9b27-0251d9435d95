using System.Globalization;
using System.Text.Json;
using System.Xml;
using Merchant_Services.Models.ModelsDal.Merchant;
using Shared.Dto.Webshop;

namespace Marlin_OS_MerchantSync_API.Services.Plugins.ProductFeedIntegration.Handlers;

public class XmlProductFeedHandler : IProductFeedHandler
{
    public async Task<List<Product>> HandleFeedAsync(MerchantProductFeed productFeed)
    {
        var products = new List<Product>();
        var httpClient = new HttpClient();
        httpClient.DefaultRequestHeaders.Accept.Clear();
        httpClient.DefaultRequestHeaders.Accept.Add(
            new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/xml"));
        httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

        string xmlContent = await FetchXmlContentAsync(httpClient, productFeed.Url);
        if (string.IsNullOrEmpty(xmlContent))
            return products;

        XmlDocument xmlDoc = new XmlDocument();
        xmlDoc.LoadXml(xmlContent);
        XmlNamespaceManager namespaceManager = CreateNamespaceManager(xmlDoc);

        XmlElement root = xmlDoc.DocumentElement;

        foreach (XmlNode node in root.ChildNodes)
        {
            if (node.NodeType == XmlNodeType.Element)
            {
                var product = ParseXmlNodeToProduct(node, productFeed, namespaceManager);
                if (product != null)
                    products.Add(product);
            }
        }

        return products;
    }

    private async Task<string> FetchXmlContentAsync(HttpClient httpClient, string url)
    {
        int errorCount = 0;
        while (errorCount < 5)
        {
            try
            {
                var response = await httpClient.GetAsync(url, HttpCompletionOption.ResponseHeadersRead);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsStringAsync();
            }
            catch
            {
                errorCount++;
                await Task.Delay(1000 * 120 * errorCount);
            }
        }

        return null;
    }

    private Product ParseXmlNodeToProduct(XmlNode node, MerchantProductFeed productFeed, XmlNamespaceManager namespaceManager)
    {
        var id = GetNodeValue(node, productFeed.Sku, namespaceManager);
        if (string.IsNullOrEmpty(id))
            return null;

        var name = GetNodeValue(node, productFeed.Name, namespaceManager);
        var description = GetNodeValue(node, productFeed.Description, namespaceManager);
        var link = GetNodeValue(node, productFeed.PermaLink, namespaceManager);
        var image = GetNodeValue(node, productFeed.PictureLink, namespaceManager);
        var availability = GetNodeValue(node, productFeed.StockStatus, namespaceManager);
        var active = availability?.ToLower() != "out of stock";
        var priceNode = GetNodeValue(node, productFeed.Price, namespaceManager);
        var regularPriceNode = GetNodeValue(node, productFeed.RegularPrice, namespaceManager);

        decimal? salePrice = ParseDecimal(priceNode);
        decimal? price = ParseDecimal(regularPriceNode) ?? salePrice;

        var product = new Product
        {
            FkMerchantId = productFeed.FkMerchantId,
            MerchantProductId = id,
            Name = name,
            Description = description,
            ShortDescription = "",
            Sku = id,
            Status = availability,
            Active = active,
            Price = salePrice,
            RegularPrice = price,
            StockQuantity = null,
            Permalink = link,
            Categories = GetNodeValue(node, productFeed.Categories, namespaceManager),
            CreatedDate = DateTime.UtcNow,
            LastModifiedDate = DateTime.UtcNow,
            ProductImages = JsonSerializer.Serialize(new List<ProductImageDto>
            {
                new ProductImageDto { Src = image }
            })
        };

        return product;
    }

    private string GetNodeValue(XmlNode node, string fieldPath, XmlNamespaceManager namespaceManager)
    {
        if (string.IsNullOrEmpty(fieldPath))
            return null;

        // Check if this is a multi-node selector (contains |)
        if (fieldPath.Contains("|"))
        {
            var paths = fieldPath.Split('|', StringSplitOptions.RemoveEmptyEntries);
            var values = new List<string>();

            foreach (var path in paths)
            {
                var value = GetSingleNodeValue(node, path.Trim(), namespaceManager);
                if (!string.IsNullOrEmpty(value))
                    values.Add(value);
            }

            return values.Count > 0 ? string.Join(" > ", values) : null;
        }

        // Single node selector
        return GetSingleNodeValue(node, fieldPath, namespaceManager);
    }

    private string GetSingleNodeValue(XmlNode node, string fieldPath, XmlNamespaceManager namespaceManager)
    {
        if (string.IsNullOrEmpty(fieldPath))
            return null;

        // Check if this is an attribute selector (starts with @)
        if (fieldPath.StartsWith("@"))
        {
            var attributeName = fieldPath.Substring(1); // Remove the @ prefix
            return node.Attributes?[attributeName]?.Value?.Trim();
        }

        // Check if the fieldPath is just the attribute name (like "id" for the id attribute)
        if (node.Attributes?[fieldPath] != null)
        {
            return node.Attributes[fieldPath].Value?.Trim();
        }

        // Standard element text extraction - look in child nodes
        return node.SelectSingleNode(fieldPath, namespaceManager)?.InnerText.Trim();
    }

    private XmlNamespaceManager CreateNamespaceManager(XmlDocument xmlDoc)
    {
        var namespaceManager = new XmlNamespaceManager(xmlDoc.NameTable);

        var defaultNamespace = xmlDoc.DocumentElement?.NamespaceURI;
        if (!string.IsNullOrEmpty(defaultNamespace))
        {
            namespaceManager.AddNamespace("ns", defaultNamespace);
        }

        foreach (XmlAttribute attr in xmlDoc.DocumentElement.Attributes)
        {
            if (attr.Prefix == "xmlns" && !string.IsNullOrEmpty(attr.LocalName))
            {
                namespaceManager.AddNamespace(attr.LocalName, attr.Value);
            }
        }

        return namespaceManager;
    }

    private decimal? ParseDecimal(string value)
    {
        if (decimal.TryParse(value?.Replace(",", ".").Replace("DKK", "").Trim(), NumberStyles.Any,
                CultureInfo.InvariantCulture, out var result))
            return result;
        return null;
    }
}