using System.Globalization;
using System.Net;
using System.Text;
using System.Text.Json;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.VisualBasic.FileIO;
using Shared.Dto.Webshop;

namespace Marlin_OS_MerchantSync_API.Services.Plugins.ProductFeedIntegration.Handlers;

public class CsvProductFeedHandler : IProductFeedHandler
{
    public async Task<List<Product>> HandleFeedAsync(MerchantProductFeed productFeed)
    {
        var products = new List<Product>();
        var datas = new List<List<string>>();

        using (var webClient = new WebClient())
        {
            webClient.Encoding = Encoding.UTF8;
            var rows = await webClient.DownloadStringTaskAsync(productFeed.Url);
            using (TextFieldParser parser = new TextFieldParser(new StringReader(rows)))
            {
                parser.HasFieldsEnclosedInQuotes = true;
                parser.SetDelimiters(productFeed.Delimiter);

                while (!parser.EndOfData)
                {
                    var fields = parser.ReadFields();
                    datas.Add(new List<string>(fields));
                }
            }
        }

        for (int i = 1; i < datas.Count; i++) // Skip header row
        {
            var data = datas[i];
            var price = Convert.ToDecimal(data[Convert.ToInt32(productFeed.Price)]
                .Replace(",", ".").Replace(" DKK", "").Replace(" kr.", ""), CultureInfo.InvariantCulture);
            var regularPrice = Convert.ToDecimal(data[Convert.ToInt32(productFeed.RegularPrice)]
                .Replace(",", ".").Replace(" DKK", "").Replace(" kr.", ""), CultureInfo.InvariantCulture);

            var product = new Product
            {
                MerchantProductId = data[Convert.ToInt32(productFeed.Sku)],
                Name = data[Convert.ToInt32(productFeed.Name)],
                Description = "",
                ShortDescription = "",
                Sku = data[Convert.ToInt32(productFeed.Sku)],
                Status = "Ukendt",
                Active = true,
                Price = price,
                RegularPrice = regularPrice,
                StockQuantity = null,
                Permalink = data[Convert.ToInt32(productFeed.PermaLink)],
                CreatedDate = DateTime.UtcNow,
                LastModifiedDate = DateTime.UtcNow,
                ProductImages = JsonSerializer.Serialize(new List<ProductImageDto>
                {
                    new ProductImageDto { Src = data[Convert.ToInt32(productFeed.PictureLink)] }
                })
            };

            products.Add(product);
        }

        return products;
    }
}
