using System.Net;
using Integration.Models;
using Integration.Models.Elastic.Behavior;
using Integration.Models.Elastic.Order;
using Integration.Services.Plugins.Integration;
using Integration.Services.Plugins.ShopifyIntegration;
using Integration.Services.Static;
using Marlin_OS_Integration_API.Models.Order;
using Marlin_OS_Integration_API.ModelsDto;
using Marlin_OS_MerchantSync_API.Models.Plugins.DanDomainClassic;
using Merchant_Services.Models.ModelsDal.Merchant;
using Newtonsoft.Json;
using Partner_Services.Services.General;
using Shared.Dto.DandomainClassic;
using Shared.Dto.Partner;
using Shared.Dto.Webshop;
using Shared.Elastic.Behavior;
using Shared.Elastic.Models.Behavior;
using Shared.Elastic.Order;
using Shared.Models;
using Shared.Models.Merchant;
using Shared.Services;
using Webshop_Service.Services.DanDomain;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;
using JsonSerializer = System.Text.Json.JsonSerializer;
using Product = Merchant_Services.Models.ModelsDal.Merchant.Product;

namespace Marlin_OS_Integration_API.Services.Plugins.DanDomainIntegration;

public class DanDomainClassicPluginService(
    ILogger logger,
    IIntegrationService integrationService,
    IMerchantService merchantService,
    IDanDomainClassicService danDomainClassicService,
    IPartnerService partnerService)
    : IPluginService
{
    public async Task<ResponseDto> AddBehaviorEventAsync(DanDomainBehaviorDto danDomainBehaviorDto)
    {
        var email = string.IsNullOrEmpty(danDomainBehaviorDto.Email) ? "n/a" : danDomainBehaviorDto.Email;
        var viaAds = string.IsNullOrEmpty(danDomainBehaviorDto.ViaAds) ? "n/a" : danDomainBehaviorDto.ViaAds;
        var email2 = string.IsNullOrEmpty(danDomainBehaviorDto.Email2) ? "n/a" : danDomainBehaviorDto.Email2;
        var viaAds2 = string.IsNullOrEmpty(danDomainBehaviorDto.ViaAds2) ? "n/a" : danDomainBehaviorDto.ViaAds2;

        var merchantId = "";
        var merchantName = string.Empty;
        Merchant? merchant = null;

        //Check for shop identifier
        if (danDomainBehaviorDto.ShopIdentifier != null)
        {
            merchant = await merchantService.ValidateShopIdentifierAsync(danDomainBehaviorDto.ShopIdentifier);
        }

        //Try to validate through url
        merchant ??= await merchantService.ValidateUrlAsync(danDomainBehaviorDto.Url);

        if (merchant != null)
        {
            merchantId = merchant.Id.ToString();
            merchantName = merchant.Name;
        }

        var elasticEvent = new ElasticBehaviorEvent
        {
            client = new ElasticBehaviorEventClient
            {
                ip = danDomainBehaviorDto.Ip
            },
            url = new ElasticBehaviorEventUrl
            {
                full = danDomainBehaviorDto.Url
            },
            user_agent = new ElasticBehaviorEventUserAgent
            {
                device = new ElasticBehaviorEventDevice
                {
                    name = "n/a"
                },
                name = "n/a",
                original = "n/a",
                version = "n/a"
            },
            Customer = new ElasticBehaviorEventCustomer
            {
                Email = email.ToLower(),
                Email2 = email2.ToLower(),
                ViaAds = viaAds,
                ViaAds2 = viaAds2,
                Session_id = danDomainBehaviorDto.Session,
            },
            Event_date = DateTime.UtcNow,
            Event_received = DateTime.UtcNow,
            Shop_event = new ElasticBehaviorEventShopEvent
            {
                Event_type = danDomainBehaviorDto.EventType,
                Webshop_id = merchantId,
                Webshop_name = merchantName
            },
            Plugin = new ElasticBehaviorEventPlugin
            {
                Name = "DanDomainClassic",
                Version = "1.0.0"
            }
        };

        return await integrationService.AddBehaviorEventAsync(elasticEvent, "")
            .ConfigureAwait(false);
    }

    public async Task<ResponseDto> UpsertDanDomainClassicAsync(string installEndpoint,
        string syncApiCredentialsEndpoint,
        Guid apiKey, string shophostname, int appId, int shopIdentifier)
    {
        var response = await danDomainClassicService.GetDanDomainClassicShop(shopIdentifier);
        PluginInstallInfo danDomainClassicResponse;
        var name = shophostname.Split('/')[2]
            .Substring(0, shophostname.Split('/')[2].LastIndexOf(".", StringComparison.Ordinal));
        var newDanDomainClassicWebshop = new PluginInstallInfo
        {
            Id = 0,
            Name = name,
            //InstallEndpoint = installEndpoint,
            //SyncApiCredentialsEndpoint = syncApiCredentialsEndpoint,
            ApiKey = apiKey.ToString(),
            Url = shophostname,
            //AppId = appId,
            ShopIdentifier = shopIdentifier.ToString(),
            Type = "DanDomainClassic"
            //IsAppInstalled = true
        };
        if (response == null || response?.Id is 0 or null)
        {
            danDomainClassicResponse =
                await danDomainClassicService.CreateDanDomainClassicShopAsync(newDanDomainClassicWebshop);
            logger.ForContext("service_name", GetType().Name)
                .Error("Create DanDomain Classic Shop {Shop} return result {New}", danDomainClassicResponse,
                    newDanDomainClassicWebshop);
            if (danDomainClassicResponse?.Id != null && danDomainClassicResponse.Id != 0)
            {
                return new ResponseDto {Success = true, Message = "Created DanDomain Classic Shop"};
            }

            return new ResponseDto {Success = false, Message = "Dit not create DanDomain Classic Shop"};
        }

        danDomainClassicResponse =
            await danDomainClassicService.UpdateDanDomainClassicShopAsync(newDanDomainClassicWebshop);
        logger.ForContext("service_name", GetType().Name)
            .Error("Updating DanDomain Classic Shop {Shop} return result {New}", danDomainClassicResponse,
                newDanDomainClassicWebshop);

        if (danDomainClassicResponse?.Id != null && danDomainClassicResponse.Url != "")
        {
            return new ResponseDto {Success = true, Message = "Updated DanDomain Classic Shop"};
        }

        return new ResponseDto {Success = false, Message = "Dit not update DanDomain Classic Shop"};
    }

    public Task<ResponseDto> UninstallDanDomainClassicAsync(int shopIdentifier)
    {
        throw new NotImplementedException();
    }

    public async Task<ResponseDto> Webhook(List<DanDomainClassicHook> danDomainClassicHooks)
    {
        try
        {
            //Check if minimum 1 order change
            var order = danDomainClassicHooks.FirstOrDefault(a => a.objectType == "Order");
            if (order != null)
            {
                var danDomainClassicWebshop =
                    await danDomainClassicService.GetDanDomainClassicShop(order.shopIdentifier);
                if (danDomainClassicWebshop == null)
                {
                    logger.ForContext("service_name", GetType().Name)
                        .Error(
                            "Could not find DanDomain Classic webshop with the following Shop Identifier: {ShopIdentifier}",
                            order.shopIdentifier);
                    return new ResponseDto
                    {
                        Success = false
                    };
                }

                var merchant = await merchantService.ValidateAsync(danDomainClassicWebshop.ApiKey);
                if (merchant != null)
                {
                    var partner = await partnerService.GetIdAndNameAsync(merchant.FkPartnerId);
                    if (order.newValues?.id != null)
                    {
                        await UpdateOrder(merchant, partner, order.newValues.id, false);
                    }
                    else
                    {
                        logger.ForContext("service_name", GetType().Name).Warning(
                            "DanDomain Classic Webhook error. order.newValues.id is NULL. Order Object: {order}",
                            JsonConvert.SerializeObject(order));
                    }
                }
                else
                {
                    logger.ForContext("service_name", GetType().Name).Warning(
                        "Could not find DanDomain Classic webshop with the provided information: {Webshop}",
                        JsonSerializer.Serialize(danDomainClassicWebshop));
                }
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "DanDomain Classic Order Hook Error: {Object}",
                    JsonConvert.SerializeObject(danDomainClassicHooks));
        }

        return new ResponseDto
        {
            Success = true
        };
    }

    public async Task<ResponseDto> WebhookProduct(DanDomainClassicProductHook danDomainClassicProductHook)
    {
        var danDomainClassicWebshop =
            await danDomainClassicService.GetDanDomainClassicShop(danDomainClassicProductHook.shopIdentifier);
        if (danDomainClassicWebshop != null && danDomainClassicWebshop.ApiKey != null)
        {
            var merchant = await merchantService.ValidateAsync(danDomainClassicWebshop.ApiKey);
            if (danDomainClassicProductHook.oldValues != null)
            {
                /*_logger.ForContext("service_name", GetType().Name)
                    .Error("DandomainClassic webhook product deleted: " + danDomainClassicProductHook.oldValues.id + " | " + merchant?.Id);*/
                await merchantService.DeactivateProduct(danDomainClassicProductHook.oldValues.id.ToString(), "",
                    merchant?.Id);
            }
        }

        return new ResponseDto
        {
            Success = true
        };
    }

    public async Task<ResponseDto> UpdateAsync(Merchant merchant, PartnerIdAndNameDto partner,
        int lookBackDaysOrder, int lookBackDaysProduct)
    {
        if (lookBackDaysProduct != 0)
        {
            //WebShop Data
            await UpdateWebShopData1(merchant, lookBackDaysProduct);
        }

        if (lookBackDaysOrder != 0)
        {
            //Order data
            await UpdateOrder(merchant, partner, lookBackDaysOrder);
        }

        return new ResponseDto
        {
            Success = true
        };
    }

    private async Task UpdateOrder(Merchant merchant, PartnerIdAndNameDto partner, int lookBackDays, bool updateLastOrderSync = true)
    {
        using HttpClient client = new HttpClient();
        var apiUrl = merchant.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUrl).Value;
        var apiKey = merchant.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey).Value;

        client.BaseAddress = new Uri($"{apiUrl}/admin/webapi/Endpoints/v1_0/OrderService/");
        var startDate = DateTime.UtcNow.AddDays(-lookBackDays).ToString("yyyy-MM-dd");
        var endDate = DateTime.UtcNow.ToString("yyyy-MM-dd");

        var dataCheckForReturn = "OrderNumber;TotalPrice;VareAntal;Status";

        try
        {
            List<OrderDandomainClassic>? ordersDanDomain = [];
            for (int i = 1; i <= lookBackDays; i++)
            {
                endDate = DateTime.UtcNow.AddDays(-lookBackDays + i).ToString("yyyy-MM-dd");
                var response =
                    await client.GetAsync(
                        $"{apiKey + "/GetByDateInterval?start=" + startDate + "&end=" + endDate}");
                var contentResult = await response.Content.ReadAsStringAsync();
                var danOrders = JsonConvert.DeserializeObject<List<OrderDandomainClassic>>(contentResult);
                if (danOrders != null)
                {
                    ordersDanDomain.AddRange(danOrders.Where(a => !a.incomplete));
                }

                startDate = DateTime.UtcNow.AddDays(-lookBackDays + i).ToString("yyyy-MM-dd");
            }

            try
            {
                var elasticOrders = new List<ElasticOrderEvent>();
                foreach (var orderDandomainClassic in ordersDanDomain)
                {
                    try
                    {
                        var lastModified = orderDandomainClassic.createdDate.ToUniversalTime();
                        if (orderDandomainClassic.modifiedDate != null)
                        {
                            lastModified = ((DateTime) orderDandomainClassic.modifiedDate).ToUniversalTime();
                        }

                        var totalPrice = (orderDandomainClassic.totalPrice - orderDandomainClassic.shippingInfo.fee) /
                                         (orderDandomainClassic.vatPct / 100 + 1);

                        if (orderDandomainClassic.invoiceInfo.state != "RECIEVED" &&
                            orderDandomainClassic.invoiceInfo.state != "CREATED")
                        {
                            dataCheckForReturn +=
                                $"{orderDandomainClassic.id};{totalPrice};{orderDandomainClassic.orderLines.Count};{orderDandomainClassic.invoiceInfo.state}\r\n";
                        }

                        var orderItems = new List<ElasticOrderEventItem>();
                        foreach (var orderLine in orderDandomainClassic.orderLines)
                        {
                            if (orderLine.productId != "PACSOFT_PUPOPT")
                            {
                                var tax = orderLine.totalPrice * (1 + (orderLine.vatPct / 100));
                                orderItems.Add(new ElasticOrderEventItem
                                {
                                    Name = orderLine.productName,
                                    Quantity = orderLine.quantity,
                                    Price = orderLine.unitPrice,
                                    Total_price = orderLine.unitPrice * orderLine.quantity,
                                    Total_price_tax = (tax - orderLine.unitPrice) * orderLine.quantity,
                                    Total_price_tax_included = tax * orderLine.quantity,
                                    Price_range = PriceConverter.ConvertPrice(orderLine.unitPrice),
                                    Sku = orderLine.productId
                                });
                            }
                        }

                        try
                        {
                            var orderData = new ElasticOrderEvent
                            {
                                Order_date = orderDandomainClassic.createdDate.ToUniversalTime(),
                                Event_received = DateTime.UtcNow,
                                Customer = new ElasticOrderEventCustomer
                                {
                                    Email = orderDandomainClassic.customerInfo.email.ToLower()
                                },
                                client = new ElasticOrderEventClient
                                {
                                    ip = orderDandomainClassic.ip ?? "0.0.0.0",
                                },
                                user_agent = new ElasticOrderEventUserAgent
                                {
                                    name = "n/a",
                                    original = "n/a",
                                    version = "n/a",
                                    device = new ElasticOrderEventDevice
                                    {
                                        name = "n/a"
                                    }
                                },
                                Shop_order = new ElasticOrderEventShopOrder
                                {
                                    Last_modified = lastModified,
                                    Status = orderDandomainClassic.orderState.name,
                                    IsCanceled = IsOrderCancel(orderDandomainClassic.orderState),
                                    Order_number = orderDandomainClassic.id.ToString(),
                                    Shipping_address = new ElasticOrderEventAddressShipping()
                                    {
                                        Email = orderDandomainClassic.customerInfo.email.ToLower(),
                                        First_name = !string.IsNullOrEmpty(orderDandomainClassic.customerInfo.name)
                                            ? orderDandomainClassic.customerInfo.name
                                            : orderDandomainClassic.customerInfo.attention,
                                        Last_name = "n/a",
                                        Phone_number = orderDandomainClassic.customerInfo.phone,
                                    },
                                    Billing_address = new ElasticOrderEventAddress()
                                    {
                                        Email = orderDandomainClassic.customerInfo.email.ToLower(),
                                        First_name = !string.IsNullOrEmpty(orderDandomainClassic.customerInfo.name)
                                            ? orderDandomainClassic.customerInfo.name
                                            : orderDandomainClassic.customerInfo.attention,
                                        Last_name = "n/a",
                                        Phone_number = orderDandomainClassic.customerInfo.phone,
                                    },
                                    Webshop_id = merchant.Id.ToString(),
                                    Total_price = totalPrice,
                                    Total_price_tax =
                                        (orderDandomainClassic.totalPrice - orderDandomainClassic.shippingInfo.fee) -
                                        totalPrice,
                                    Total_price_tax_included =
                                        orderDandomainClassic.totalPrice - orderDandomainClassic.shippingInfo.fee,
                                    Total_price_shipping = orderDandomainClassic.shippingInfo.fee,
                                    Currency = orderDandomainClassic.currencyCode,
                                    Vat_percentage = orderDandomainClassic.vatPct / 100,
                                    Order_items = orderItems
                                },
                                Plugin = new ElasticOrderEventPlugin
                                {
                                    Name = "DanDomainClassic",
                                    Version = "1.0.0"
                                },
                                Partner = new ElasticOrderEventPartner()
                                {
                                    Id = partner.Id.ToString(),
                                    Name = partner.Name
                                }
                            };

                            if (orderData.Shop_order.Order_number != null)
                            {
                                elasticOrders.Add(orderData);
                            }
                        }
                        catch (NullReferenceException ex)
                        {
                            logger.ForContext("service_name", GetType().Name).Error(ex, "Null Error Reference 3");
                        }
                    }
                    catch (NullReferenceException ex)
                    {
                        logger.ForContext("service_name", GetType().Name).Error(ex, "Null Error Reference 2");
                    }
                }

                if (elasticOrders.Count != 0)
                {
                    await integrationService.AddOrderAsync(
                        elasticOrders.GroupBy(order => order.Shop_order?.Order_number).Select(group =>
                            group.OrderByDescending(order => order.Shop_order?.Last_modified).First()).ToList(),
                        "", updateLastOrderSync, merchant).ConfigureAwait(false);
                }
                else
                {
                    //No orders found sad
                    await merchantService.UpdateLastOrderSyncAsync(merchant);
                }
            }
            catch (NullReferenceException ex)
            {
                logger.ForContext("service_name", GetType().Name).Error(ex, "Null Error Reference 1");
            }
        }
        catch (Exception exception)
        {
            //Check if merchant have failed over x days then ignores errors
            if (merchant.LastOrderSync.HasValue &&
                merchant.LastOrderSync.Value >= StaticVariables.StopLoggingAfterSyncError())
            {
                logger.ForContext("service_name", GetType().Name).Error(exception.ToString(), "Merchant: {Merchant}",
                    JsonConvert.SerializeObject(merchant));
            }
        }
    }

    private async Task<List<DandomainProduct>> FetchProducts(int offset, int maxOffset, HttpClient client, string date, int siteId)
    {
        var products = new List<DandomainProduct>();

        while (offset < maxOffset)
        {
            var response = await client.GetAsync(
                $"products?modifiedStartDate={date}&include=settings,prices,media&offset={offset}");
            var contentResult = await response.Content.ReadAsStringAsync();
            var productPagination = JsonSerializer.Deserialize<DandomainProductPagination>(contentResult);
            if (productPagination != null)
            {
                offset += productPagination.items.Count;
                products.AddRange(productPagination.items);
                //Console.WriteLine($"Dandomain Classic product get: {offset} of {maxOffset}");
            }
        }

        return products;
    }

    private async Task<List<DandomainCategory>> FetchCategories(int offset, int maxOffset, HttpClient client)
    {
        var categories = new List<DandomainCategory>();

        while (offset < maxOffset)
        {
            var response = await client.GetAsync(
                $"categories?offset={offset}");
            var contentResult = await response.Content.ReadAsStringAsync();
            var productPagination = JsonSerializer.Deserialize<DandomainCategoryPagination>(contentResult);
            if (productPagination != null)
            {
                offset += productPagination.items.Count;
                categories.AddRange(productPagination.items);
                //Console.WriteLine($"Dandomain Classic product get: {offset} of {maxOffset}");
            }
        }

        return categories;
    }


    private async Task UpdateWebShopData1(Merchant merchant, int lookBackDays)
    {
        try
        {
            var apiUrl = merchant.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUrl)
                .Value;
            var apiKey = merchant.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey)
                .Value;
            var siteId1 = merchant.MerchantMeta
                .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.SiteId)?.Value;

            var date = DateTime.UtcNow.AddDays(-lookBackDays).ToString("yyyy-MM-dd");
            var client = new HttpClient();
            client.Timeout = TimeSpan.FromMinutes(2);
            var apiKey1 = System.Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes($":{apiKey}"));
            client.DefaultRequestHeaders.Add("Authorization", $"Basic {apiKey1}");
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(
                new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));

            client.BaseAddress = new Uri($"{apiUrl}/admin/WEBAPI/v2/");
            var offset = 0;
            var hasMore = true;
            var siteId = string.IsNullOrEmpty(siteId1) ? (int?) null : int.Parse(siteId1);
            var productsdandomains = new List<DandomainProduct>();
            var productsdandomainsDictionary = new Dictionary<string, DandomainProduct>();
            var products = new List<Product>();
            var response = await client.GetAsync("sites");
            var contentResult = await response.Content.ReadAsStringAsync();
            var settingsRaw = (JsonSerializer.Deserialize<DandomainSetting>(contentResult))!.items.ToList();
            var settings = settingsRaw.Where(a => a.id == siteId || (siteId == null && /*a.iso.ToLower() == "dk"
                                                                     ||*/ a.currencyCode.Equals("dkk", StringComparison.CurrentCultureIgnoreCase))).ToList();

            siteId ??= settings.FirstOrDefault()?.id;

            var pricesInclVat = true;
            if (siteId != null)
            {
                var responseSiteSetting = await client.GetAsync($"sites/{siteId}/settings");
                var contentResultVat = await responseSiteSetting.Content.ReadAsStringAsync();
                var siteSettings = JsonSerializer.Deserialize<DandomainClassicSiteSettings>(contentResultVat);
                if (siteSettings is {ProductSettings.PriceSettings.DbvatEnabled: false})
                {
                    pricesInclVat = false;
                }
            }

            var countryId = settings.FirstOrDefault()?.countryId ?? 0;
            decimal vat = (decimal) 1.00;
            if(!pricesInclVat && countryId != 0)
            {
                var responseVat = await client.GetAsync($"countries/{countryId}");
                var contentResultVat = await responseVat.Content.ReadAsStringAsync();
                var country = JsonSerializer.Deserialize<DandomainClassicCountry>(contentResultVat);
                decimal.TryParse(country?.Vat, out var countryVat);
                if(countryVat != 0)
                {
                    vat = 1 + (countryVat / 100);
                }
            }

            //var test = await client.GetAsync("products/2375571");
            var test = await client.GetAsync("products/F-BB-S7797405");
            var testResult = await test.Content.ReadAsStringAsync();

            Console.WriteLine();

            //Get all updated products
            var firstResponse = await client.GetAsync(
                $"products?modifiedStartDate={date}&include=settings,prices,media&offset=0");
            var firstContentResult = await firstResponse.Content.ReadAsStringAsync();
            var firstProductPagination = JsonSerializer.Deserialize<DandomainProductPagination>(firstContentResult);
            int total = firstProductPagination.count;
            var tasks = new List<Task<List<DandomainProduct>>>();
            int numberOfThreads = 6;
            int batchSize = total / numberOfThreads;
            for (int i = 0; i < numberOfThreads; i++)
            {
                int offsetStart = i * batchSize;
                int offsetEnd = (i == numberOfThreads - 1) ? total : offsetStart + batchSize;
                tasks.Add(FetchProducts(offsetStart, offsetEnd, client, date, siteId ?? 0));
            }

            var results = await Task.WhenAll(tasks);
            var allProducts = results.SelectMany(r => r).ToList();
            productsdandomains = allProducts.DistinctBy(a => a.id).ToList();

            //Categories
            firstResponse = await client.GetAsync(
                $"categories");
            firstContentResult = await firstResponse.Content.ReadAsStringAsync();
            var firstCategroeisPagination = JsonSerializer.Deserialize<DandomainProductPagination>(firstContentResult);
            total = firstCategroeisPagination.count;
            var tasksCategories = new List<Task<List<DandomainCategory>>>();
            numberOfThreads = 3;
            batchSize = total / numberOfThreads;
            for (int i = 0; i < numberOfThreads; i++)
            {
                int offsetStart = i * batchSize;
                int offsetEnd = (i == numberOfThreads - 1) ? total : offsetStart + batchSize;
                tasksCategories.Add(FetchCategories(offsetStart, offsetEnd, client));
            }

            var resultsCategories = await Task.WhenAll(tasksCategories);
            var allCategories = resultsCategories.SelectMany(r => r).ToList();
            var categoriesDandomains = allCategories.DistinctBy(a => a.id).ToList();

            //Check if any site id is forced
            if (siteId != null)
            {
                productsdandomains = productsdandomains.Where(a =>
                    a.settings.items.Any(b => b.languageId == siteId)).ToList();
            }

            productsdandomainsDictionary = productsdandomains.Where(a => a.variantMasterId != null).ToList()
                .DistinctBy(a => a.variantMasterId).ToDictionary(a => a.variantMasterId);

            //Get all variants
            var variantsToUpdate = productsdandomains
                .Where(a => !string.IsNullOrEmpty(a.variantMasterId) && a.number != a.variantMasterId).ToList();

            //Loop all variants to check if main products exists else get it
            foreach (var variantToUpdate in variantsToUpdate)
            {
                //Check if main product already exists
                var key = variantToUpdate.variantMasterId;
                if (!productsdandomainsDictionary.ContainsKey(key))
                {
                    response =
                        await client.GetAsync(
                            $"products/{variantToUpdate.variantMasterId}?include=settings,prices,media");
                    contentResult = await response.Content.ReadAsStringAsync();
                    try
                    {
                        var productPagination = JsonSerializer.Deserialize<DandomainProduct>(contentResult);
                        if (productPagination != null)
                        {
                            productsdandomains.Add(productPagination);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex);
                    }
                }
            }

            int totalProducts = productsdandomains.Count;
            int currentProductNumber = 0;
            Console.WriteLine();

            //Loop products
            productsdandomains = productsdandomains
                .Where(a => (string.IsNullOrEmpty(a.variantMasterId) || a.variantMasterId == a.number)).ToList();
            foreach (var productdandomain in productsdandomains)
            {
                currentProductNumber++;
                Console.Write($"\rProcessing product {currentProductNumber} of {totalProducts}...");
                try
                {
                    //Get settings with name
                    var product = new Product();
                    DandomainProduct.SettingItem productRaw = null;
                    try
                    {
                        if (productdandomain.settings is {items: not null})
                        {
                            foreach (var setting in productdandomain.settings.items.Where(a => a.languageId == siteId))
                            {
                                if (!string.IsNullOrEmpty(setting.name))
                                {
                                    productRaw = setting;
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.ForContext("service_name", GetType().Name)
                            .Error(ex, $"Error while reading product sync for dandomain classic merchantId: " +
                                       $"{merchant.Id} number: {productdandomain.number} setting");
                    }

                    if (productdandomain.id == 54109)
                    {
                        Console.WriteLine();
                    }

                    if (productRaw != null)
                    {
                        product.Name = productRaw.name;
                        product.Description = productRaw.longDescription;
                        product.ShortDescription = productRaw.shortDescription;
                        product.MerchantCreatedDate = productdandomain.createdDate;
                        product.MerchantModifiedDate = productdandomain.editedDate;
                        product.LastModifiedDate = DateTime.UtcNow;
                        var price = productdandomain.prices.items.FirstOrDefault(a => a.currencyCode.Equals("dkk", StringComparison.CurrentCultureIgnoreCase) && (string.IsNullOrEmpty(a.b2bGroupId) || a.b2bGroupId == "0"));
                        if (price != null)
                        {
                            product.RegularPrice = price.unitPrice * vat;
                            product.Price = price.specialOfferPrice * vat;
                            if (product.Price == 0)
                            {
                                product.Price = product.RegularPrice;
                            }
                        }
                        else
                        {
                            product.RegularPrice = 0;
                            product.Price = 0;
                        }

                        product.Sku = productdandomain.number;
                        product.MerchantProductId = productdandomain.id.ToString();
                        product.FkMerchantId = merchant.Id;
                        product.Status = (!productdandomain.hidden).ToString();
                        product.Active = !productdandomain.hidden;
                        product.Permalink = productRaw.url;
                        product.StockQuantity = productdandomain.stockCount;
                        product.Variants = new List<Variant>();
                        var category =
                            categoriesDandomains.FirstOrDefault(a => a.number == productdandomain.primaryCategoryId);
                        if (category != null)
                        {
                            var categoryText = category.texts.items.FirstOrDefault(a => a.siteId == siteId);
                            if (categoryText != null)
                            {
                                if (!string.IsNullOrEmpty(categoryText.keywords) && categoryText.keywords.Trim() != "")
                                {
                                    product.Categories = categoryText.keywords.Trim();
                                }
                                else
                                {
                                    product.Categories = categoryText.name;
                                }
                            }
                        }

                        //Images
                        var images = new List<ProductImageDto>();
                        var baseUrl = merchant.Url?.Split("/shop/frontpage")[0] ?? "";
                        if (!string.IsNullOrEmpty(productdandomain.pictureLink))
                        {
                            images.Add(new()
                                {
                                    Src = $"{baseUrl}/{productdandomain.pictureLink}"
                                }
                            );
                        }

                        product.ProductImages = JsonSerializer.Serialize(images);


                        //variants
                        var variants = variantsToUpdate.Where(a => a.variantMasterId == productdandomain.number)
                            .ToList();
                        foreach (var variantdandomain in variants)
                        {
                            //Get settings with name
                            DandomainProduct.SettingItem variantRaw = null;
                            foreach (var setting in variantdandomain.settings.items.Where(a =>
                                         a.languageId == siteId))
                            {
                                if (!string.IsNullOrEmpty(setting.name))
                                {
                                    variantRaw = setting;
                                }
                            }

                            if (variantRaw != null)
                            {
                                var variant = new Variant
                                {
                                    Name = variantRaw.name,
                                    Description = variantRaw.longDescription,
                                    ShortDescription = variantRaw.shortDescription,
                                    CreatedDate = variantdandomain.createdDate,
                                    LastModifiedDate = variantdandomain.editedDate,
                                };

                                var variantPrice = variantdandomain.prices.items.FirstOrDefault(a => a.currencyCode.Equals("dkk", StringComparison.CurrentCultureIgnoreCase) && (string.IsNullOrEmpty(a.b2bGroupId) || a.b2bGroupId == "0"));
                                if (variantPrice != null)
                                {
                                    variant.RegularPrice = variantPrice.unitPrice * vat;
                                    variant.Price = variantPrice.specialOfferPrice * vat;
                                }
                                else
                                {
                                    variant.RegularPrice = 0;
                                    variant.Price = 0;
                                }

                                variant.Sku = variantdandomain.number;
                                variant.MerchantVariantId = variantdandomain.id.ToString();
                                variant.MerchantProductId = productdandomain.id.ToString();
                                variant.Status = (!variantdandomain.hidden).ToString();
                                variant.Active = !variantdandomain.hidden;
                                variant.Permalink = variantRaw.url;
                                variant.StockQuantity = variantdandomain.stockCount;
                                product.Variants.Add(variant);
                            }
                        }

                        products.Add(product);
                    }
                }
                catch (Exception ex)
                {
                    logger.ForContext("service_name", GetType().Name)
                        .Error(ex, $"Error while reading product sync for dandomain classic merchantId: " +
                                   $"{merchant.Id} number: {productdandomain.number}");
                }

                Console.Write($"\rProcessed product {currentProductNumber} of {totalProducts}.");
            }

            Console.WriteLine($"\nAll products processed for Webshop: {merchant.DisplayName}");

            merchant.Products = products.DistinctBy(a => a.MerchantProductId).ToList();
            await integrationService.SynchronizeWebshopDataAsync(merchant).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            //Check if merchant have failed over x days then ignores errors
            if (merchant.LastProductSync.HasValue &&
                merchant.LastProductSync.Value >= StaticVariables.StopLoggingAfterSyncError())
            {
                logger.ForContext("service_name", GetType().Name)
                    .Error(ex, $"Error while reading product sync for dandomain classic merchantId: {merchant.Id}");
            }
        }
    }

    private bool IsOrderCancel(OrderState orderState)
    {
        if (orderState.id is 4 or 5 && orderState.isDefault)
        {
            return true;
        }

        switch (orderState.name)
        {
            case "Afviste":
            case "cancelled":
            case "canceled":
            case "failed":
            case "refunded":
                return true;
            default:
                return false;
        }
    }
}