using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using Integration.Models.Elastic.Order;
using Integration.Services.Plugins.Integration;
using Integration.Services.Plugins.ShopifyIntegration;
using Marlin_OS_Integration_API.Models.Order;
using Merchant_Services.Models.ModelsDal.Merchant;
using Shared.Dto.Partner;
using Shared.Dto.Webshop;
using Shared.Elastic.Order;
using Shared.Models;
using Shared.Models.Merchant;
using Shared.Services;
using Shared.WebServiceService;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;
using Product = Merchant_Services.Models.ModelsDal.Merchant.Product;

namespace Marlin_OS_MerchantSync_API.Services.Plugins.DanDomainIntegration;

public class DanDomainPluginService(
    ILogger logger,
    IIntegrationService integrationService,
    IMerchantService merchantService)
    : IPluginService
{
    public async Task<ResponseDto> UpdateAsync(Merchant merchant, PartnerIdAndNameDto partner,
        int lookBackDaysOrder, int lookBackDaysProduct)
    {
        var username = merchant.MerchantMeta.Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUserKey)
            .Value;
        var password = merchant.MerchantMeta
            .Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiUserSecret)
            .Value;

        int maxRetryAttempts = 3;
        int currentAttempt = 0;

        for (currentAttempt = 1; currentAttempt <= maxRetryAttempts; currentAttempt++)
        {
            try
            {
                var webServicePortClient = new WebServicePortClient();
                webServicePortClient.InnerChannel.OperationTimeout = new TimeSpan(0, 30, 0);
                await webServicePortClient.Solution_ConnectAsync(username, password);

                if (lookBackDaysProduct != 0)
                {
                    // WebShop Data
                    await UpdateWebShopData(merchant, webServicePortClient, lookBackDaysProduct);
                }

                if (lookBackDaysOrder != 0)
                {
                    // Order data
                    await UpdateOrder(merchant, webServicePortClient, lookBackDaysOrder);
                }

                await webServicePortClient.CloseAsync();
                break;
            }
            catch (Exception ex)
            {
                if (currentAttempt < maxRetryAttempts)
                {
                    await Task.Delay(TimeSpan.FromSeconds(30 * currentAttempt));
                }
                else
                {
                    //Check if merchant have failed over x days then ignores errors
                    if (merchant.LastOrderSync.HasValue &&
                        merchant.LastOrderSync.Value >= StaticVariables.StopLoggingAfterSyncError() ||
                        merchant.LastProductSync.HasValue && merchant.LastProductSync.Value >=
                        StaticVariables.StopLoggingAfterSyncError())
                    {
                        logger.ForContext("service_name", GetType().Name)
                            .Error(ex,
                                "DanDomain login webshop WebShopId: {WebShopId} Retry attempt: {CurrentAttempt}",
                                merchant.Id, currentAttempt);
                    }
                }
            }
        }

        return new ResponseDto
        {
            Success = true
        };
    }

    private static bool VerifyDanDomainWebhook(string payload, string hmacHeader, string webshopSecret)
    {
        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(webshopSecret));
        var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(payload));
        var calculatedHMAC = Convert.ToBase64String(hash);
        return hmacHeader.Equals(calculatedHMAC);
    }

    private async Task UpdateWebShopData(Merchant webShop,
        WebServicePortClient webServicePortClient, int lookBackDays)
    {
        var products = new List<Product>();

        try
        {
            await webServicePortClient.Product_SetFieldsAsync(
                "Id,Title");
            var startDate = DateTime.UtcNow.AddDays(-lookBackDays).ToString("yyyy-MM-dd");
            var productsDanDomain = await webServicePortClient.Product_GetByUpdatedDateAsync(startDate, null);
            var productsDanDomains = productsDanDomain.Product_GetByUpdatedDateResult.Where(a =>
                a.Title != null).ToList();

            await webServicePortClient.Product_SetFieldsAsync(
                "Id,Title,Variants,DateCreated,DateUpdated,Price,Discount,Category,SecondaryCategories,Status,Pictures,AutoStock,ProductUrl,Description,DescriptionShort,DescriptionLong,ItemNumber,Stock,Url,SeoLink,RelatedProductIds");
            await webServicePortClient.Product_SetVariantFieldsAsync(
                "Id,Title,Stock,Price,Discount,Description,DescriptionLong,ItemNumber");

            int totalProducts = productsDanDomains.Count;
            int currentProductNumber = 0;
            Console.WriteLine();

            foreach (var productDanRaw in productsDanDomains)
            {
                currentProductNumber++;
                Console.Write($"\rProcessing product {currentProductNumber} of {totalProducts}...");

                var productDanRaw1 = await webServicePortClient.Product_GetByIdAsync(productDanRaw.Id);
                var productDan = productDanRaw1.Product_GetByIdResult;
                if (productDan.Category == null)
                {
                    continue;
                }

                try
                {
                    var selfCalculatedUrl =
                        $"/shop/{productDan.Category.Id}-{GenerateSlug(productDan.Category.Title.ToLower())}/{productDan.Id}-{GenerateSlug(productDan.Title.ToLower())}/";
                    var link =
                        $"{webShop.Url}{productDan.ProductUrl ?? selfCalculatedUrl}";
                    var variants = new List<Variant>();
                    decimal lowestPrice = 999999;
                    decimal highestPrice = 0;
                    if (productDan.Variants.Length == 0)
                    {
                        lowestPrice = Convert.ToDecimal(productDan.Price);
                        highestPrice = Convert.ToDecimal(productDan.Price);
                    }

                    foreach (var productVariant in productDan.Variants)
                    {
                        if (!DateTime.TryParse(productDan.DateCreated, out DateTime createdDate))
                        {
                            createdDate = Convert.ToDateTime(productDan.DateUpdated);
                        }

                        var variant = new Variant
                        {
                            MerchantVariantId = productVariant.Id.ToString(),
                            MerchantProductId = productDan.Id.ToString(),
                            Name = productVariant.Title ?? string.Empty,
                            Description = productVariant.DescriptionLong,
                            ShortDescription = productVariant.Description,
                            Sku = productVariant.ItemNumber,
                            Price = Convert.ToDecimal(productVariant.Price - productVariant.Discount),
                            RegularPrice = Convert.ToDecimal(productVariant.Price),
                            StockQuantity = productVariant.Stock,
                            MerchantCreatedDate = createdDate,
                            MerchantModifiedDate = Convert.ToDateTime(productDan.DateUpdated),
                            LastModifiedDate = DateTime.UtcNow,
                            Permalink = link,
                            Status = productVariant.Status.ToString(),
                            Active = productVariant.Status
                        };
                        if (lowestPrice > Convert.ToDecimal(variant.Price))
                        {
                            lowestPrice = Convert.ToDecimal(variant.Price);
                        }

                        if (highestPrice < Convert.ToDecimal(variant.Price))
                        {
                            highestPrice = Convert.ToDecimal(variant.Price);
                        }

                        variants.Add(variant);
                    }

                    try
                    {
                        if (!DateTime.TryParse(productDan.DateCreated, out DateTime createdDate))
                        {
                            createdDate = Convert.ToDateTime(productDan.DateUpdated);
                        }

                        var product = new Product()
                        {
                            MerchantProductId = productDan.Id.ToString(),
                            Name = productDan.Title ?? string.Empty,
                            Description = productDan.Description,
                            ShortDescription = productDan.DescriptionShort,
                            Sku = productDan.ItemNumber,
                            Price = Convert.ToDecimal(productDan.Price - productDan.Discount),
                            RegularPrice = Convert.ToDecimal(productDan.Price),
                            StockQuantity = productDan.Stock,
                            MerchantCreatedDate = createdDate,
                            MerchantModifiedDate = Convert.ToDateTime(productDan.DateUpdated),
                            LastModifiedDate = DateTime.UtcNow,
                            Permalink = link,
                            Variants = variants,
                            Status = productDan.Status.ToString(),
                            Active = productDan.Status,
                            Categories = productDan.Category.Title,
                        };

                        //Images
                        var images = new List<ProductImageDto>();
                        //Limit to x images
                        foreach (var picture in productDan.Pictures.Where(a => !a.FileName.Contains(".gif"))
                                     .Take(StaticVariables.MaxImages))
                        {
                            images.Add(new ProductImageDto
                            {
                                Src = $"{webShop.Url}/upload_dir/shop/{picture.FileName}"
                            });
                        }

                        product.ProductImages = JsonSerializer.Serialize(images);
                        products.Add(product);
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                        throw;
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    throw;
                }

                Console.Write($"\rProcessed product {currentProductNumber} of {totalProducts}.");
            }

            /*var categories = new List<Webshop_Service.Models.ModelsDal.Webshop.Category>();
            try
            {
                var categoriesDanDomain = await webServicePortClient.Category_GetAllAsync();
                foreach (var collection in categoriesDanDomain.Category_GetAllResult)
                {
                    var category = new Webshop_Service.Models.ModelsDal.Webshop.Category
                    {
                        Name = collection.Title,
                        Slug = GenerateSlug(collection.Title),
                        WebshopCategoryId = Convert.ToInt64(collection.Id),
                        ParentId = collection.ParentId
                    };
                    categories.Add(category);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }*/

            Console.WriteLine("\nAll products processed.");

            webShop.Products = products.DistinctBy(a => a.MerchantProductId).ToList();
            await integrationService.SynchronizeWebshopDataAsync(webShop).ConfigureAwait(false);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    private async Task UpdateOrder(Merchant webshop,
        WebServicePortClient webServicePortClient,
        int lookBackDays, int? orderId = null, bool updateLastOrderSync = true)
    {
        var elasticOrders = new List<ElasticOrderEvent>();
        await webServicePortClient.Order_SetFieldsAsync(
            "Currency,Customer,DateUpdated,DateDelivered,Status,InvoiceNumber,Total,Vat,OrderLines,Id,Delivery,DiscountCodes");
        await webServicePortClient.Order_SetOrderLineFieldsAsync(
            "Amount,DiscountRounded,ItemNumber,PriceRounded,ProductId,ProductTitle,Status,VatRate");

        var orders = new List<Order>();
        var orderNewer = DateTime.UtcNow.AddDays(-lookBackDays);
        if (orderId != null)
        {
            var ordersDanDomain = await webServicePortClient.Order_GetByIdAsync((int) orderId);
            orders.Add(ordersDanDomain.Order_GetByIdResult);
        }
        else
        {
            try
            {
                var startDate = orderNewer.ToString("yyyy-MM-dd");
                webServicePortClient.InnerChannel.OperationTimeout = TimeSpan.FromMinutes(10);
                var ordersDanDomain = await webServicePortClient.Order_GetByDateUpdatedAsync(
                    startDate, null, "0,1,2,3,4,5,6,7,8,99,100");
                orders.AddRange(ordersDanDomain.Order_GetByDateUpdatedResult);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }

        foreach (var order in orders)
        {
            DateTime dateDelivered = DateTime.UtcNow;
            if (!DateTime.TryParse(order.DateDelivered, out dateDelivered))
            {
                dateDelivered = Convert.ToDateTime(order.DateUpdated);
            }

            //Check if order is updated in the last day
            if (dateDelivered < orderNewer)
            {
                continue;
            }

            var orderItems = new List<ElasticOrderEventItem>();
            var status = "";
            var cancelled = false;
            switch (order.Status)
            {
                case "5":
                    status = "cancelled";
                    cancelled = true;
                    break;
                case "99":
                    status = "draft";
                    break;
                default:
                    status = "completed";
                    break;
            }

            foreach (var orderLine in order.OrderLines)
            {
                var price = Convert.ToDecimal(orderLine.PriceRounded);
                if (orderLine.DiscountRoundedSpecified)
                {
                    price = price - Convert.ToDecimal(orderLine.DiscountRounded);
                }
                else if (orderLine.DiscountSpecified)
                {
                    price = price - Convert.ToDecimal(orderLine.Discount);
                }

                var vat = Convert.ToDecimal(1 + orderLine.VatRate);

                price = Math.Round(price, 3);

                var orderItem = new ElasticOrderEventItem
                {
                    Product_id = orderLine.ProductIdSpecified ? orderLine.ProductId.ToString() : "",
                    Name = orderLine.ProductTitle,
                    Sku = orderLine.ItemNumber,
                    Price = price,
                    Quantity = orderLine.Amount,
                    Total_price = Convert.ToDecimal(price * orderLine.Amount),
                    Total_price_tax = (price * vat - price) *
                                      orderLine.Amount,
                    Total_price_tax_included =
                        price * vat * orderLine.Amount,
                };
                orderItems.Add(orderItem);
            }

            var invoiceNumber = order.InvoiceNumber!.ToString();
            if (string.IsNullOrEmpty(invoiceNumber) || invoiceNumber == "0")
            {
                invoiceNumber = order.Id!.ToString();
            }

            DateTime.TryParse(order.DateUpdated, out var updatedDate);
            var orderVat = Convert.ToDecimal(1 + order.Vat);
            var total = Convert.ToDecimal(order.Total);
            var totalTax = total * orderVat - total;
            var orderData = new ElasticOrderEvent
            {
                Order_date = dateDelivered,
                Event_received = DateTime.UtcNow,
                Customer = new ElasticOrderEventCustomer
                {
                    Email = order.Customer.Email
                },
                Shop_order = new ElasticOrderEventShopOrder
                {
                    Last_modified = updatedDate < dateDelivered ? dateDelivered : updatedDate,
                    Status = status,
                    IsCanceled = cancelled,
                    Order_number = invoiceNumber,
                    Shipping_address = new ElasticOrderEventAddressShipping()
                    {
                        Email = order.Customer.ShippingEmail,
                        First_name = order.Customer.ShippingFirstname,
                        Last_name = order.Customer.ShippingLastname,
                        Phone_number = order.Customer.ShippingPhone,
                    },
                    Billing_address = new ElasticOrderEventAddress
                    {
                        Email = order.Customer.Email,
                        First_name = order.Customer.Firstname,
                        Last_name = order.Customer.Lastname,
                        Address1 = order.Customer.Address,
                        City = order.Customer.City,
                        Country = order.Customer.Country,
                        Phone_number = order.Customer.Phone,
                        State = order.Customer.State,
                        Zip_code = order.Customer.Zip
                    },
                    Webshop_id = webshop.Id.ToString(),
                    Total_price = total,
                    Total_price_tax = totalTax,
                    Total_price_tax_included = total + totalTax,
                    Currency = order.Currency.Iso,
                    Order_items = orderItems,
                    Vat_percentage = orderVat,
                    Total_price_shipping = Convert.ToDecimal(order.Delivery.Price * (double) orderVat)
                },
                Plugin = new ElasticOrderEventPlugin
                {
                    Name = "DanDomain",
                    Version = "1.0.0"
                },
                Discounts = new List<ElasticOrderEventDiscount>()
            };

            //Discounts
            if (order.DiscountCodes != null)
            {
                foreach (var discountCode in order.DiscountCodes)
                {
                    var type = "";
                    switch (discountCode.Type)
                    {
                        case "f":
                            type = "fixed";
                            break;
                        case "p":
                            type = "percentage";
                            break;
                        default:
                            logger.ForContext("service_name", GetType().Name)
                                .Error($"Unknown discount type found for {webshop.Id} with type {discountCode.Type}");
                            break;
                    }

                    if (type != "")
                    {
                        orderData.Discounts.Add(new ElasticOrderEventDiscount
                        {
                            Amount = Convert.ToDecimal(discountCode.Value),
                            Code = discountCode.Title,
                            Type = type
                        });
                    }
                }
            }

            elasticOrders.Add(orderData);
        }

        if (elasticOrders.Count != 0)
        {
            await integrationService.AddOrderAsync(elasticOrders, "", updateLastOrderSync, webshop)
                .ConfigureAwait(false);
        }
        else
        {
            //No orders found sad
            await merchantService.UpdateLastOrderSyncAsync(webshop);
        }
    }

    private static string GenerateSlug(string str)
    {
        str = str.Replace("å", "aa");
        str = str.Replace("ø", "oe");
        str = str.Replace("æ", "ae");

        // invalid chars           
        str = Regex.Replace(str, @"[^a-z0-9\s-]", "");
        // convert multiple spaces into one space   
        str = Regex.Replace(str, @"\s+", " ").Trim();
        // cut and trim 
        str = str.Substring(0, str.Length <= 45 ? str.Length : 45).Trim();
        str = Regex.Replace(str, @"\s", "-"); // hyphens   
        return str;
    }

    private static string DecryptString(string key, string cipherText)
    {
        byte[] iv = new byte[16];
        byte[] buffer = Convert.FromBase64String(cipherText);
        using (Aes aes = Aes.Create())
        {
            aes.Key = Encoding.UTF8.GetBytes(key);
            aes.IV = iv;
            ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);
            using (MemoryStream memoryStream = new MemoryStream(buffer))
            {
                using (CryptoStream cryptoStream =
                       new CryptoStream((Stream) memoryStream, decryptor, CryptoStreamMode.Read))
                {
                    using (StreamReader streamReader = new StreamReader((Stream) cryptoStream))
                    {
                        return streamReader.ReadToEnd();
                    }
                }
            }
        }
    }
}