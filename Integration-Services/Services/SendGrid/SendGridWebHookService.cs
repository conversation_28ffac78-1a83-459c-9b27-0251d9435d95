using System.Net;
using System.Text;
using System.Text.Json;
using Audience.Models;
using Audience.Services.Audience;
using Elasticsearch.Net;
using Integration.Models.SendGrid;
using Message_Services.Services.Message;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using Shared.Elastic.Models.ElasticCampaignsMailsOpens;
using Shared.Models.Elastic.ElasticCampaignsMailsOpens;
using RabbitMQ.Client;
using IConnection = RabbitMQ.Client.IConnection;
using Shared.Elastic.Models;
using Partner_Services.Services.General;
using Campaign_Services.Services.Campaign;

namespace Integration.Services.SendGrid;

public class SendGridWebHookService(
    ICustomerService customerService,
    ILogger logger,
    [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud,
    //IConnection rabbitConnection,
    IMessageService messageService,
    IPartnerService partnerService,
    ICampaignService campaignService)
    : ISendGridWebHookService
{
    public async Task WebHook(SendGridWebHookDto sendGridWebHookDto, string body)
    {
        if (sendGridWebHookDto.Event == "processed" || sendGridWebHookDto.Event == "delivered")
        {
            //Not using this event at the moment
        }
        else if (sendGridWebHookDto.Event == "dropped" || sendGridWebHookDto.Event == "deferred" ||
                 sendGridWebHookDto.Event == "bounced")
        {
            //Email failed to send
            await customerService.MailSentErrorAsync(new SendGridEmailStatus
            {
                Email = sendGridWebHookDto.Email, Status = sendGridWebHookDto.Event,
                Reason = sendGridWebHookDto.Response
            });
        }
        else if (sendGridWebHookDto.Event == "open")
        {
            try
            {
                if (sendGridWebHookDto.SendDate != "")
                {
                    var originalPartnerId = sendGridWebHookDto.PartnerId;
                    var partnerId = int.TryParse(originalPartnerId, out var parsedPartnerId) ? parsedPartnerId : 0;
                    if (partnerId == 0)
                    {
                        // Fetch the Partner Id from the CampaignId
                        var campaignId = sendGridWebHookDto.CampaignId;
                        partnerId = await campaignService.GetPartnerIdByCampaignIdAsync(int.Parse(campaignId));
                    }

                    if (partnerId == 0)
                    {
                        partnerId = await customerService.GetPartnerIdByEmailAsync(sendGridWebHookDto.Email);
                    }

                    if(partnerId == 0)
                    {
                        logger.Error("SendGrid open error: " + JsonSerializer.Serialize(sendGridWebHookDto) + " Invalid PartnerId: " + sendGridWebHookDto.PartnerId);
                    }
                    
                    sendGridWebHookDto.Ip = IPAddress.TryParse(sendGridWebHookDto.Ip, out _)
                        ? sendGridWebHookDto.Ip
                        : "0.0.0.0";
                    var merchantsIds = sendGridWebHookDto.Merchants.Split(",").ToList() ?? [];
                    var elasticEvent = new ElasticCampaignsMailsOpens()
                    {
                        Event_received = DateTime.UtcNow,
                        Customer = new ElasticCampaignsMailsOpensCustomer
                        {
                            Email = sendGridWebHookDto.Email,
                            Campaign_Id = sendGridWebHookDto.CampaignId,
                            Automation_id = "",
                            Email_guid = sendGridWebHookDto.EmailGuid
                        },
                        Shop_event = new ElasticCampaignsMailsOpensShopEvent
                        {
                            Webshop_id = merchantsIds,
                            Send_date = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt32(sendGridWebHookDto.SendDate))
                                .DateTime
                        },
                        client = new ElasticCampaignsMailsOpensClient
                        {
                            ip = sendGridWebHookDto.Ip,
                        },
                        EventObject = new ElasticCampaignsMailsOpensEvent
                        {
                            Created = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt32(sendGridWebHookDto.SendDate))
                                .DateTime
                        },
                        timestamp = DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt32(sendGridWebHookDto.SendDate))
                            .DateTime,
                        Partner = new ElasticPartner
                        {
                            Id = partnerId.ToString(),
                            Name = await partnerService.GetPartnerNameByIdAsync(partnerId) ?? "n/a"
                        }
                    };

                    if (!int.TryParse(originalPartnerId, out _))
                    {
                        logger.Error("SendGrid open error: " + JsonSerializer.Serialize(sendGridWebHookDto) + " Invalid Original PartnerId: " + sendGridWebHookDto.PartnerId);
                    }
                    
                    var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticEvent));
                    using (var publishChannel = rabbitConnectionCloud.CreateModel())
                    {
                        publishChannel.BasicPublish(exchange: "campaign",
                            routingKey: "campaign_mail_open",
                            basicProperties: null,
                            body: actionBody);
                    }

                    //Message first open handling
                    await messageService.FirstOpenHandling(sendGridWebHookDto.EmailGuid);
                }
                else
                {
                    logger.Warning("SendGrid open error: " + JsonSerializer.Serialize(sendGridWebHookDto));
                }
            }
            catch (Exception e)
            {
                logger.Error(e,
                    "{event_type} publishing {event} event to RabbitMQ queue campaign_mail_open for mail {MessageId}",
                    "Failed", "Open", sendGridWebHookDto.MessageId);
                throw;
            }
        }
        else
        {
            logger.Warning("Untracked event from sendgrid: " + JsonSerializer.Serialize(sendGridWebHookDto));
        }
    }
}