using Shared.Models;
using ILogger = Serilog.ILogger;

namespace Integration.Services.Error;

public class ErrorService : IErrorService
{
    private readonly ILogger _logger;

    public ErrorService(ILogger logger)
    {
        _logger = logger;
    }

    public ResponseDto AddErrorAsync(ErrorLoggingDto errorLoggingDto)
    {
        //Remove shopify error
        //"Name: SecurityError, Message: Failed to read the 'cookie' property from 'Document': The document is sandboxed and lacks the 'allow-same-origin' flag.,
        //Stack: Error: Failed to read the 'cookie' property from 'Document': The document is sandboxed and lacks the 'allow-same-origin' flag.
        if (!errorLoggingDto.Error.Contains("The document is sandboxed and lacks the"))
        {
            _logger.ForContext("service_name", GetType().Name).Error(
                "Plugin error for shop with url {ErrorDtoUrl} Error message: {ErrorDtoError}", errorLoggingDto.Url,
                errorLoggingDto.Error);
        }

        return new ResponseDto {Success = true, Message = "Success"};
    }

    public ResponseDto AddDebugAsync(ErrorLoggingDto errorLoggingDto)
    {
        _logger.ForContext("service_name", GetType().Name).Warning(
            "Plugin debug for shop with url {ErrorDtoUrl} Error message: {ErrorDtoError}", errorLoggingDto.Url,
            errorLoggingDto.Error);
        return new ResponseDto {Success = true, Message = "Success"};
    }

    public ResponseDto AddInformationAsync(ErrorLoggingDto errorLoggingDto)
    {
        _logger.ForContext("service_name", GetType().Name).Warning(
            "Plugin information for shop with url {ErrorDtoUrl} Error message: {ErrorDtoError}",
            errorLoggingDto.Url, errorLoggingDto.Error);
        return new ResponseDto {Success = true, Message = "Success"};
    }

    public ResponseDto AddWarningAsync(ErrorLoggingDto errorLoggingDto)
    {
        _logger.ForContext("service_name", GetType().Name).Warning(
            "Plugin warning for shop with url {ErrorDtoUrl} Error message: {ErrorDtoError}", errorLoggingDto.Url,
            errorLoggingDto.Error);
        return new ResponseDto {Success = true, Message = "Success"};
    }
}