using System.Net.Http.Headers;
using System.Web;
using Shared.Models;
using Shared.Services.Setting;
using Webshop.Webshop;
using IConfiguration = Microsoft.Extensions.Configuration.IConfiguration;

namespace Integration.Services.AffiliateIntegrations;

public class AdtractionIntegrationService(IMerchantService merchantService, IConfiguration configuration, ISettingService settingService) : IAffiliateIntegrationService
{
    private const string AffiliateType = "Adtraction";
    
    public async Task<ResponseDto> HandleProductFeeds()
    {
        // Step 1: Get Adtraction Merchants from the database
        //var merchants = await merchantService.GetAffiliateMerchantsAsync(AffiliateType);

        // TODO - Replace Hardcoded PartnerId with the actual PartnerId
        var baseUrl = settingService.GetSettingAsync(52876, "Intergrations_Adtraction_Products_Url").Result.Value;
        var uriBuilder = new UriBuilder(baseUrl);
        var query = HttpUtility.ParseQueryString(uriBuilder.Query);
        query["token"] = configuration["AdtractionApi-Token"];
        uriBuilder.Query = query.ToString();
        var url = uriBuilder.ToString();
        
        
        using var client = new HttpClient();
        client.BaseAddress = new Uri(url);
        
        client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json;charset=UTF-8"));
        

       
        return new ResponseDto
        {
            Success = true,
            Message = "Adtraction feeds handled successfully"
        };
    }
}