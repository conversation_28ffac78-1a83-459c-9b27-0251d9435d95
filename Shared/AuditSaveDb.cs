using System.Security.Claims;
using System.Text;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Shared.Elastic;
using RabbitMQ.Client;
using IConnection = RabbitMQ.Client.IConnection;

namespace Shared;

public static class AuditSaveDb
{
    public static void OnBeforeSaveChanges(Microsoft.EntityFrameworkCore.ChangeTracking.ChangeTracker changeTracker,
        IHttpContextAccessor httpContextAccessor, IConnection rabbitConnection)
    {
        changeTracker.DetectChanges();
        foreach (var entry in changeTracker.Entries())
        {
            if (entry.State == EntityState.Detached || entry.State == EntityState.Unchanged)
                continue;
            var email = httpContextAccessor.HttpContext?.User.Claims.SingleOrDefault(x => x.Type == ClaimTypes.Email)
                ?.Value;
            if (email != null)
            {
                var changeLog = new ChangeLog
                {
                    Event = new()
                    {
                        Values = new List<Values>(),
                    },
                    user = new()
                    {
                        email = email,
                    },
                    data_stream = new()
                    {
                        dataset = "audit",
                        type = "logs"
                    },
                    Event_date = DateTime.UtcNow
                };
                var oldValues = new Dictionary<string, object>();
                var newValues = new Dictionary<string, object>();
                foreach (var property in entry.Properties)
                {
                    string propertyName = property.Metadata.Name;
                    if (property.Metadata.IsPrimaryKey())
                    {
                        changeLog.Event.Primary_key = property.CurrentValue?.ToString() ?? "";
                        continue;
                    }

                    switch (entry.State)
                    {
                        case EntityState.Added:
                            changeLog.Event.action = "Added";
                            newValues[propertyName] = property.CurrentValue;
                            break;
                        case EntityState.Deleted:
                            changeLog.Event.action = "Deleted";
                            oldValues[propertyName] = property.OriginalValue;
                            break;
                        case EntityState.Modified:
                            if (property.IsModified)
                            {
                                changeLog.Event.action = "Updated";
                                oldValues[propertyName] = property.OriginalValue;
                                newValues[propertyName] = property.CurrentValue;
                            }

                            break;
                    }
                }

                changeLog.Event.Table = entry.Metadata.GetTableName() ?? "unknown table";

                foreach (var oldValue in oldValues)
                {
                    var exists = changeLog.Event.Values.SingleOrDefault(a => a.Name == oldValue.Key);
                    if (exists != null)
                    {
                        exists.Old = oldValue.Value?.ToString() ?? "";
                    }
                    else
                    {
                        changeLog.Event.Values.Add(new Values
                        {
                            Name = oldValue.Key,
                            Old = oldValue.Value?.ToString() ?? ""
                        });
                    }
                }

                foreach (var newValue in newValues)
                {
                    var exists = changeLog.Event.Values.SingleOrDefault(a => a.Name == newValue.Key);
                    if (exists != null)
                    {
                        exists.New = newValue.Value?.ToString() ?? "";
                    }
                    else
                    {
                        changeLog.Event.Values.Add(new Values
                        {
                            Name = newValue.Key,
                            New = newValue.Value?.ToString() ?? ""
                        });
                    }
                }

                using (var publishChannel = rabbitConnection.CreateModel())
                {
                    publishChannel.ConfirmSelect();

                    var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(changeLog));
                    publishChannel.BasicPublish(exchange: "log",
                        routingKey: "log_audit",
                        basicProperties: null,
                        body: actionBody);

                    publishChannel.WaitForConfirms(new TimeSpan(0, 0, 10));
                }
            }
        }
    }
}