using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace Shared.Controllers.ControllerExtensions.ActionFilters;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class PartnerEndpointAttribute : ActionFilterAttribute, IAllowAnonymous
{
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        // Retrieve PartnerId from HttpContext.Items
        var partnerId = context.HttpContext.Items.TryGetValue("PartnerId", out var partnerIdObject) && partnerIdObject is int id ? id : (int?)null;

        if (partnerId == null)
        {
            // Return Unauthorized if PartnerId is missing
            context.Result = new UnauthorizedObjectResult("API Key Incorrect");
        }
    }
}