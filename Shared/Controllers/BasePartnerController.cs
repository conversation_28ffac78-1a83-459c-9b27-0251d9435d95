using Microsoft.AspNetCore.Mvc;
using Shared.Services.Partner;

namespace Shared.Controllers;

public class BasePartnerController : ControllerBase
{
    private readonly IPartnerContext _partnerContext;

    public BasePartnerController(IPartnerContext partnerContext = null)
    {
        _partnerContext = partnerContext;
    }

    protected int PartnerId
    {
        get
        {
            // If we have the partner context service, use it
            if (_partnerContext != null)
            {
                return _partnerContext.PartnerId;
            }
            
            // Otherwise, fall back to HttpContext.Items
            if (HttpContext?.Items.TryGetValue("PartnerId", out var partnerIdObject) == true && partnerIdObject is int partnerId)
            {
                return partnerId;
            }
            
            return 0;
        }
    }
    
    protected bool IsDevRequest
    {
        get
        {
            // If we have the partner context service, use it
            if (_partnerContext != null)
            {
                return _partnerContext.IsDevRequest;
            }
            
            // Otherwise, fall back to HttpContext.Items
            if (HttpContext?.Items.TryGetValue("IsDevPartnerRequest", out var isDevRequestObject) == true && isDevRequestObject is bool isDevRequest)
            {
                return isDevRequest;
            }
            
            return false;
        }
    }
    
    protected bool IsPartnerIdExempt
    {
        get
        {
            // If we have the partner context service, use it
            if (_partnerContext != null)
            {
                return _partnerContext.IsExempt;
            }
            
            // Otherwise, fall back to HttpContext.Items
            if (HttpContext?.Items.TryGetValue("IsPartnerIdExempt", out var isExemptObject) == true && isExemptObject is bool isExempt)
            {
                return isExempt;
            }
            
            return false;
        }
    }
    
    protected bool IsFoundByDefault
    {
        get
        {
            // If we have the partner context service, use it
            if (_partnerContext != null)
            {
                return _partnerContext.IsFoundByDefault;
            }
            
            // Otherwise, fall back to HttpContext.Items
            if (HttpContext?.Items.TryGetValue("IsFoundByDefault", out var isFoundByDefaultObject) == true && isFoundByDefaultObject is bool isFoundByDefault)
            {
                return isFoundByDefault;
            }
            
            return false;
        }
    }
    
    protected void SetPartnerId(int partnerId)
    {
        // If we have the partner context service, use it
        if (_partnerContext != null)
        {
            _partnerContext.SetPartnerId(partnerId);
        }
        else if (HttpContext != null)
        {
            // Otherwise, set directly in HttpContext.Items
            HttpContext.Items["PartnerId"] = partnerId;
            HttpContext.Items["IsFoundByDefault"] = false;
        }
    }
}