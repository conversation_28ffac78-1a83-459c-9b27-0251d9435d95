using Newtonsoft.Json;

namespace Shared.Dto.EmailOnAcid;

public class ClientDetails
{
    public string Id { get; set; }
    public string DisplayName { get; set; }
    public string Client { get; set; }
    public string Os { get; set; }
    public string Category { get; set; }
    public Screenshot Screenshots { get; set; }
    public string Thumbnail { get; set; }
    public string Status { get; set; }
    public StatusDetails StatusDetails { get; set; }
}

public class Screenshot
{
    [JsonProperty("default")] public string Default { get; set; }
}

public class StatusDetails
{
    public long Submitted { get; set; }

    public int Attempts { get; set; }
    // If there are more properties in StatusDetails, add them here
}

// RootObject now becomes a dictionary
public class EmailOnAcidDto : Dictionary<string, ClientDetails>
{
}