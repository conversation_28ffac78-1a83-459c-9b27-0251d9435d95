using Shared.Dto.Webshop;

namespace Shared.Dto.Merchants
{
    public class MerchantPaginationDataDto
    {
        public string MerchantName { get; set; }
        public int MerchantId { get; set; }
        public string Type { get; set; }
        public bool IsCustomer { get; set; }
        public bool IsMarketingAllowed { get; set; }
        public string Url { get; set; }
        public string externalRedirectUrl { get; set; } = "External redirect url";
        public DateTime LastModifiedDate { get; set; }
        public List<MerchantMetumDto> MerchantMeta { get; set; } = new List<MerchantMetumDto>();
        public string Model { get; set; }

        public decimal CPADisplayPercentage { get; set; }
        public decimal CPAInteractPercentage { get; set; }
        public decimal CPARedirectPercentage { get; set; }
        public int CPADisplayExposureDays { get; set; }
        public int CPAInteractExposureDays { get; set; }
        public int CPARedirectExposureDays { get; set; }

        public int CPM { get; set; }
        public int CPMBudget { get; set; }

        public decimal Actual { get; set; }
        public decimal Potential { get; set; }
        public decimal ActualPotential { get; set; }
        public decimal MKTEFF { get; set; }


        public bool CMReady { get; set; }
        public bool CPReady { get; set; }
        public int MRenewalDays { get; set; }
        public bool ActiveDiscount { get; set; }

        public long EmailDisplays { get; set; }
        public long EmailDisplaysUnique { get; set; }
        public long EmailRedirects { get; set; }
        public long DiscountDisplays { get; set; }
        public long DiscountInteracts { get; set; }
        public long DiscountRedirects { get; set; }
        public long ProductDisplays { get; set; }
        public long ProductInteracts { get; set; }
        public long ProductRedirects { get; set; }

        public int PartnerOrders { get; set; }
        public int PartnerOrdersFullReturn { get; set; }
        public int PartnerOrdersPartialReturn { get; set; }
        public decimal PartnerRevenue { get; set; }
        public decimal PartnerCost { get; set; }
        public decimal BaseFee { get; set; }
    }
}