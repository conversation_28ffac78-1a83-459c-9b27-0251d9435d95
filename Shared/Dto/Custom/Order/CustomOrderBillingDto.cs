namespace Shared.Dto.Custom.Order;

/// <summary>
/// An object that represents the billing information for a customer order.
/// </summary>
public class CustomOrderBillingDto
{
    /// <summary>
    /// The first name of the person being billed for the order.
    /// </summary>
    public string? BillingFirstName { get; set; }

    /// <summary>
    /// The last name of the person being billed for the order.
    /// </summary>
    public string? BillingLastName { get; set; }

    /// <summary>
    /// The billing address on the order.
    /// </summary>
    public string? BillingAddress { get; set; }

    /// <summary>
    /// The city associated with the billing address on the order.
    /// </summary>
    public string? BillingCity { get; set; }

    /// <summary>
    /// The state or province associated with the billing address on the order.
    /// </summary>
    public string? BillingState { get; set; }

    /// <summary>
    /// The zip or postal code associated with the billing address on the order.
    /// </summary>
    public string? BillingZipCode { get; set; }

    /// <summary>
    /// The country associated with the billing address on the order.
    /// </summary>
    public string? BillingCountry { get; set; }

    /// <summary>
    /// The phone number associated with the billing address on the order.
    /// </summary>
    public string? BillingPhoneNumber { get; set; }

    /// <summary>
    /// The email address associated with the billing address on the order.
    /// </summary>
    public string? BillingEmail { get; set; }
}