using System.ComponentModel.DataAnnotations;

namespace Shared.Dto.Custom.Order;

/// <summary>
/// An object that represents a client event, such as adding an item to a cart or removing an item from a wishlist.
/// </summary>
public class CustomOrderDto
{
    /// <summary>
    /// The timestamp for when the order was created. This should be supplied in GMT.
    /// </summary>
    [Required]
    public DateTime CreatedGmt { get; set; }

    /// <summary>
    /// The timestamp for when the last modification was made on the order, e.g. a status update. This should be supplied in GMT.
    /// </summary>
    [Required]
    public DateTime LastModifiedGmt { get; set; }

    /// <summary>
    /// The order number on the order.
    /// </summary>
    [Required]
    public string OrderNumber { get; set; }

    /// <summary>
    /// The currency that the order was paid in. It's important to use standardized currency ISO codes, e.g. USD, DKK, EUR.
    /// </summary>
    [Required]
    public string CurrencyIsoCode { get; set; }

    /// <summary>
    /// The email of the ordering customer.
    /// </summary>
    [Required]
    public string Email { get; set; }

    /// <summary>
    /// The current status of the order.
    /// </summary>
    [Required]
    public string Status { get; set; }

    /// <summary>
    /// Whether or not the order has been canceled.
    /// </summary>
    [Required]
    public bool IsCanceled { get; set; }

    /// <summary>
    /// The total price of the order, including shipping costs.
    /// </summary>
    [Required]
    public decimal TotalShippingPrice { get; set; }

    /// <summary>
    /// The total price of the order, excluding taxes and shipping costs.
    /// </summary>
    [Required]
    [Range(1, int.MaxValue)]
    public decimal TotalPriceWithOutTax { get; set; }

    /// <summary>
    /// The total price of the order, including taxes but not shipping costs.
    /// </summary>
    [Required]
    [Range(1, int.MaxValue)]
    public decimal TotalPriceWithTax { get; set; }

    /// <summary>
    /// A list of the ordered items.
    /// </summary>
    [Required]
    public List<CustomOrderItemDto>? Items { get; set; }

    /// <summary>
    /// The billing information for the ordering customer.
    /// </summary>
    public CustomOrderBillingDto? Billing { get; set; }

    /// <summary>
    /// The shipping information for the ordering customer.
    /// </summary>
    public CustomOrderShippingDto? Shipping { get; set; }
}