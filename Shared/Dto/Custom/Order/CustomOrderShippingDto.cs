namespace Shared.Dto.Custom.Order;

/// <summary>
/// An object that represents the shipping information for a customer order.
/// </summary>
public class CustomOrderShippingDto
{
    /// <summary>
    /// The first name of the person receiving the order.
    /// </summary>
    public string? ShippingFirstName { get; set; }

    /// <summary>
    /// The last name of the person receiving the order.
    /// </summary>
    public string? ShippingLastName { get; set; }

    /// <summary>
    /// The phone number of the person receiving the order.
    /// </summary>
    public string? ShippingPhoneNumber { get; set; }

    /// <summary>
    /// The email address of the person receiving the order.
    /// </summary>
    public string? ShippingEmail { get; set; }
}