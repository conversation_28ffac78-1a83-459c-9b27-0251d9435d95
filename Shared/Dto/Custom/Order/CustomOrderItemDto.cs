using System.ComponentModel.DataAnnotations;

namespace Shared.Dto.Custom.Order;

/// <summary>
/// An object that represents an item on a customer order.
/// </summary>
public class CustomOrderItemDto
{
    /// <summary>
    /// The name of the ordered item.
    /// </summary>
    [Required]
    public string? Name { get; set; }

    /// <summary>
    /// The SKU (Stock Keeping Unit) of the ordered item.
    /// </summary>
    [Required]
    public string Sku { get; set; }

    /// <summary>
    /// The unit price of the ordered item.
    /// </summary>
    [Required]
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// The quantity of the ordered item.
    /// </summary>
    [Required]
    public long Quantity { get; set; }

    /// <summary>
    /// The total price of the ordered item, including taxes.
    /// </summary>
    [Required]
    public decimal TotalPriceWithTax { get; set; }

    /// <summary>
    /// The total price of the ordered item, excluding taxes.
    /// </summary>
    [Required]
    public decimal TotalPriceWithOutTax { get; set; }
}