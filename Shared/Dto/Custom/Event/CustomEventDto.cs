using System.ComponentModel.DataAnnotations;

namespace Shared.Dto.CustomDto;

//    [StringLength(0, MinimumLength = 0)]

/// <summary>
/// An object that represents a client event, such as adding an item to a cart or removing an item from a wishlist.
/// </summary>
public class CustomEventDto
{
    /// <summary>
    /// The timestamp for when the event was triggered.
    /// </summary>
    [Required]
    public DateTime EventDate { get; set; }

    /// <summary>
    /// The type of event that was triggered. This can be one of the following values: AddCart, RemoveCart, AddWishList, RemoveWishList, ProductLook.
    /// </summary>
    [Required]
    public string EventType { get; set; }

    /// <summary>
    /// The SKU of the product that the event is being triggered on.
    /// </summary>
    [Required]
    public string ProductSku { get; set; }

    /// <summary>
    /// The price of the product that the event is being triggered on.
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// The email of the user who triggered the event.
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// The content of the cookie.
    /// </summary>
    public string? Cookie { get; set; }

    /// <summary>
    /// The IP address of the client that triggered the event (e.g. 127.0.0.1).
    /// </summary>
    public string? ClientIpAddress { get; set; }

    /// <summary>
    /// The user agent string that was sent by the client's web browser. This string provides information about the user's system, such as the operating system and version.
    /// </summary>
    public string? UserAgent { get; set; }
}