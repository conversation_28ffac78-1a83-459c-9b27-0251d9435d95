namespace Shared.Dto.MerchantDashboard.Overview
{
    public class MerchantDashboardOverviewMetricsDto
    {
        public decimal TotalRevenue { get; set; }
        public long TotalOrders { get; set; }
        public decimal AverageOrderValue { get; set; }
        public long TotalExposures { get; set; }
        public long AudienceExposed { get; set; }
        public MerchantDashboardOverviewComparisionMetrics? ComparisonMetrics { get; set; }
        public Dictionary<string, List<MerchantDashboardOverviewMetricsChartDataPointDto>> ChartData { get; set; }
    }

    public class MerchantDashboardOverviewMetricsChartDataPointDto
    {
        public string Label { get; set; }
        public decimal Value { get; set; }
    }

    public class MerchantDashboardOverviewComparisionMetrics
    {
        public decimal TotalRevenue { get; set; }
        public long TotalOrders { get; set; }
        public decimal AverageOrderValue { get; set; }
        public long TotalExposures { get; set; }
        public long AudienceExposed { get; set; }
    }
}