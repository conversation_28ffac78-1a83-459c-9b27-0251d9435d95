namespace Shared.Dto.MerchantDashboard.Order
{
    public class MerchantDashboardSearchOrderDto
    {
        public DateTime? From { get; set; }
        public DateTime? To { get; set; }
        public int MerchantId { get; set; }
        public int PageNumber { get; set; } = 1; // Default to first page
        public int PageSize { get; set; } = 25; // Default page size
        public string? SearchQuery { get; set; }
    }
}