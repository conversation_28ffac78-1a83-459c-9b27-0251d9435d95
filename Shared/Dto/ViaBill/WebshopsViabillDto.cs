namespace Shared.Dto.ViaBill;

public class WebshopsViabillDto
{
    public string Uuid { get; set; } = null!;
    public string Name { get; set; } = null!;
    public string Country { get; set; } = null!;
    public DateTime Created { get; set; }
    public DateTime LastUpdated { get; set; }
    public int? WebShopId { get; set; }
    public List<WebshopsViabillMerchantAccountDto> WebshopsViabillMerchantAccounts { get; set; }
    public List<WebshopsViabillStoreDto> WebshopsViabillStores { get; set; }
}