using Shared.Dto.Campaign.Pagination;
using Shared.Dto.Segment.Pagination;

namespace Shared.Dto.Customer.Pagination
{
    public class SegmentPaginationDto
    {
        public int Size { get; set; }
        public List<SegmentPaginationDataDto> Segments { get; set; } = new List<SegmentPaginationDataDto>();
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }
}