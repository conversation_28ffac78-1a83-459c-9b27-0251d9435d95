using Newtonsoft.Json;

namespace Marlin_OS_Integration_API.ModelsDto;

// Root myDeserializedClass = JsonConvert.DeserializeObject<List<Root>>(myJsonResponse);
public class Metadata
{
    public string correlationId { get; set; }
    public string initiatedBy { get; set; }
    public int priority { get; set; }
}

public class NewValues
{
    public int id { get; set; }
    public DateTime createdDate { get; set; }
    public bool incomplete { get; set; }
    public int siteId { get; set; }
    public double totalPrice { get; set; }
    public double totalWeight { get; set; }
    public string currencyCode { get; set; }
    public string vatPercentage { get; set; }
    public double shippingFee { get; set; }
    public int shippingId { get; set; }
    public double paymentFee { get; set; }
    public int paymentId { get; set; }
    public bool shippingFeeIncludesVat { get; set; }
    public bool paymentFeeIncludesVat { get; set; }
    public int orderStateId { get; set; }

    [JsonProperty("deliveryInfo.address")] public string deliveryInfoaddress { get; set; }

    [JsonProperty("deliveryInfo.name")] public string deliveryInfoname { get; set; }

    [JsonProperty("deliveryInfo.companyName")]
    public string deliveryInfocompanyName { get; set; }

    [JsonProperty("deliveryInfo.city")] public string deliveryInfocity { get; set; }

    [JsonProperty("deliveryInfo.country")] public string deliveryInfocountry { get; set; }

    [JsonProperty("deliveryInfo.zipCode")] public string deliveryInfozipCode { get; set; }

    [JsonProperty("customerInfo.phone")] public string customerInfophone { get; set; }

    [JsonProperty("customerInfo.address")] public string customerInfoaddress { get; set; }

    [JsonProperty("customerInfo.address2")]
    public string customerInfoaddress2 { get; set; }

    [JsonProperty("customerInfo.name")] public string customerInfoname { get; set; }

    [JsonProperty("customerInfo.companyName")]
    public string customerInfocompanyName { get; set; }

    [JsonProperty("customerInfo.city")] public string customerInfocity { get; set; }

    [JsonProperty("customerInfo.country")] public string customerInfocountry { get; set; }

    [JsonProperty("customerInfo.ean")] public string customerInfoean { get; set; }

    [JsonProperty("customerInfo.email")] public string customerInfoemail { get; set; }

    [JsonProperty("customerInfo.fax")] public string customerInfofax { get; set; }

    [JsonProperty("customerInfo.state")] public string customerInfostate { get; set; }

    [JsonProperty("customerInfo.zipCode")] public string customerInfozipCode { get; set; }

    [JsonProperty("invoiceInfo.isPayed")] public bool invoiceInfoisPayed { get; set; }

    [JsonProperty("invoiceInfo.status")] public string invoiceInfostatus { get; set; }
    public object objectIdentifier { get; set; }
    public int version { get; set; }
    public int? idDelta { get; set; }
    public string nr { get; set; }
    public string vendorNr { get; set; }
    public double? price { get; set; }
    public double? priceDelta { get; set; }
    public double? weight { get; set; }
    public double? weightDelta { get; set; }
    public string pictureLink { get; set; }
    public string pictureBigLink { get; set; }
    public string createdDateDelta { get; set; }
    public int? stockCount { get; set; }
    public int? stockCountDelta { get; set; }
    public int? stockLimit { get; set; }
    public int? stockLimitDelta { get; set; }
    public string locationNumber { get; set; }
    public string barcodeNumber { get; set; }
    public int? sortOrder { get; set; }
    public int? sortOrderDelta { get; set; }
    public int? minBuyAmount { get; set; }
    public int? minBuyAmountDelta { get; set; }
    public int? maxBuyAmount { get; set; }
    public int? maxBuyAmountDelta { get; set; }
    public string fileSaleLink { get; set; }
    public bool? isVariantMaster { get; set; }
    public string eDBPriserProdNr { get; set; }
    public string notes { get; set; }
    public string googleFeedCategory { get; set; }
    public int? minBuyAmountB2B { get; set; }
    public int? minBuyAmountB2BDelta { get; set; }
    public bool? isRateVariant { get; set; }
    public int? pointsEarns { get; set; }
    public int? pointsEarnsDelta { get; set; }
    public bool? isReviewVariants { get; set; }
    public bool? isGiftCertificate { get; set; }
    public bool? showOnGoogleFeed { get; set; }
    public bool? showOnFacebookFeed { get; set; }
    public bool? showOnKelkooFeed { get; set; }
    public bool? showOnPricerunnerFeed { get; set; }

    [JsonProperty("medias[0].itemId")] public string medias0itemId { get; set; }

    [JsonProperty("medias[0].url")] public string medias0url { get; set; }

    [JsonProperty("medias[0].mime")] public string medias0mime { get; set; }

    [JsonProperty("medias[0].sortOrder")] public int? medias0sortOrder { get; set; }

    [JsonProperty("medias[0].sortOrderDelta")]
    public int? medias0sortOrderDelta { get; set; }

    [JsonProperty("medias[1].itemId")] public string medias1itemId { get; set; }

    [JsonProperty("medias[1].url")] public string medias1url { get; set; }

    [JsonProperty("medias[1].mime")] public string medias1mime { get; set; }

    [JsonProperty("medias[1].sortOrder")] public int? medias1sortOrder { get; set; }

    [JsonProperty("medias[1].sortOrderDelta")]
    public int? medias1sortOrderDelta { get; set; }

    [JsonProperty("medias[1].thumbnail")] public string medias1thumbnail { get; set; }

    [JsonProperty("prices[0].itemId")] public string prices0itemId { get; set; }

    [JsonProperty("prices[0].amount")] public int? prices0amount { get; set; }

    [JsonProperty("prices[0].amountDelta")]
    public int? prices0amountDelta { get; set; }

    [JsonProperty("prices[0].currencyCode")]
    public string prices0currencyCode { get; set; }

    [JsonProperty("prices[0].b2BGroupId")] public string prices0b2BGroupId { get; set; }

    [JsonProperty("prices[0].unitPrice")] public double? prices0unitPrice { get; set; }

    [JsonProperty("prices[0].unitPriceDelta")]
    public double? prices0unitPriceDelta { get; set; }

    [JsonProperty("prices[0].specialOfferPrice")]
    public double? prices0specialOfferPrice { get; set; }

    [JsonProperty("prices[0].specialOfferPriceDelta")]
    public double? prices0specialOfferPriceDelta { get; set; }

    [JsonProperty("prices[0].avance")] public double? prices0avance { get; set; }

    [JsonProperty("prices[0].avanceDelta")]
    public double? prices0avanceDelta { get; set; }

    [JsonProperty("options[0].itemId")] public string options0itemId { get; set; }

    [JsonProperty("options[0].siteId")] public int? options0siteId { get; set; }

    [JsonProperty("options[0].siteIdDelta")]
    public int? options0siteIdDelta { get; set; }

    [JsonProperty("options[0].isHidden")] public bool? options0isHidden { get; set; }

    [JsonProperty("options[0].showAsNew")] public bool? options0showAsNew { get; set; }

    [JsonProperty("options[0].retailPrice")]
    public double? options0retailPrice { get; set; }

    [JsonProperty("options[0].retailPriceDelta")]
    public double? options0retailPriceDelta { get; set; }

    [JsonProperty("options[0].productName")]
    public string options0productName { get; set; }

    [JsonProperty("categories[0].itemId")] public string categories0itemId { get; set; }

    [JsonProperty("categories[0].categoryNumber")]
    public string categories0categoryNumber { get; set; }

    [JsonProperty("categories[0].isDefault")]
    public bool? categories0isDefault { get; set; }

    public int? versionDelta { get; set; }
    public int? siteIdDelta { get; set; }
    public double? totalPriceDelta { get; set; }
    public double? totalWeightDelta { get; set; }
    public double? shippingFeeDelta { get; set; }
    public int? shippingIdDelta { get; set; }
    public double? paymentFeeDelta { get; set; }
    public int? paymentIdDelta { get; set; }
    public int? orderStateIdDelta { get; set; }
}

public class DanDomainClassicHook
{
    public List<string> propertiesChanged { get; set; }
    public NewValues newValues { get; set; }
    public string objectType { get; set; }
    public int version { get; set; }
    public int shopIdentifier { get; set; }
    public Metadata metadata { get; set; }
}