namespace Shared.Dto.DandomainClassic;

public class DanDomainBehaviorDto
{
    public string Url { get; set; }

    //public string? DeviceName { get; set; }
    //public string? Name { get; set; }
    //public string? Version { get; set; }
    public string? ShopIdentifier { get; set; }
    public string? ViaAds { get; set; }
    public string? ViaAds2 { get; set; }
    public string? Email { get; set; }
    public string? Email2 { get; set; }
    public string? EventType { get; set; }
    public string? Session { get; set; }
    public string? Ip { get; set; }
}