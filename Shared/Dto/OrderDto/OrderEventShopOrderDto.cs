namespace Shared.Dto.OrderDto;

public class OrderEventShopOrderDto
{
    public DateTime? Last_modified { get; set; }
    public string? Campaign_id { get; set; }
    public string? Webshop_id { get; set; }
    public string? Order_number { get; set; }
    public string? Currency { get; set; }
    public string? Status { get; set; }
    public decimal? Vat_percentage { get; set; }
    public bool? IsCanceled { get; set; }
    public decimal Total_price { get; set; }
    public decimal Total_price_tax { get; set; }
    public decimal? Total_price_tax_included { get; set; }
    public decimal? Total_price_shipping { get; set; }
    public List<OrderEventOrderItemDto>? Order_items { get; set; }
    public OrderEventAddressDto? Billing_address { get; set; }
    public OrderEventAddressShippingDto? Shipping_address { get; set; }
}