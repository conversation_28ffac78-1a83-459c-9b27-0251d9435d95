namespace Shared.Dto.OrderDto;

public class OrderEventDto
{
    public string ApiKey { get; set; }
    public DateTime Order_date { get; set; }
    public DateTime Event_received { get; set; }
    public OrderEventShopOrderDto? Shop_order { get; set; }
    public OrderEventClientDto? client { get; set; }
    public OrderEventUserAgentDto? user_agent { get; set; }
    public OrderEventCustomerDto? Customer { get; set; }
    public OrderEventPluginDto? Plugin { get; set; }
    public List<OrderEventDiscountDto>? Discounts { get; set; }
}