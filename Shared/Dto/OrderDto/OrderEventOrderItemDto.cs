namespace Shared.Dto.OrderDto;

public class OrderEventOrderItemDto
{
    public string? Name { get; set; }
    public string? Sku { get; set; }
    public string? Product_id { get; set; }
    public string? Product_variant_id { get; set; }
    public string? Internal_product_id { get; set; }
    public decimal Price { get; set; }
    public string? Price_range { get; set; }
    public long Quantity { get; set; }
    public decimal Total_price { get; set; }
    public decimal Total_price_tax { get; set; }
    public decimal? Total_price_tax_included { get; set; }
}