namespace Shared.Dto.Dashboard;

public class DashboardProfile
{
    public decimal RevenueYear { get; set; }
    public decimal RevenueDrivenYear { get; set; }
    public int OrdersYear { get; set; }
    public long TotalExposuresYear { get; set; }
    public long TotalDisplaysYear { get; set; }
    public int MailSentYear { get; set; }
    public int MerchantsYear { get; set; }
    public double TotalRevenueYear { get; set; }
    public int CouponLive { get; set; }
    public long CouponEvents { get; set; }

    public decimal Revenue { get; set; }
    public decimal RevenueDriven { get; set; }
    public decimal AverageOrder { get; set; }
    public int Orders { get; set; }
    public long TotalExposures { get; set; }
    public long AudienceExposures { get; set; }
    public long TotalDisplays { get; set; }
    public int MailSent { get; set; }
    public string PartnerName { get; set; }
    public DashboardProfileRevenueGraph RevenueGraph { get; set; }
    public DashboardProfileMQLGraph MqlGraph { get; set; }
    public DashboardProfileExposuresGraph ExposuresGraph { get; set; }
    public DashboardProfileBestMerchant BestMerchantMonth { get; set; }
    public DashboardProfileBestMerchant BestMerchantYearToDate { get; set; }
}

public class DashboardProfileBestMerchant
{
    public int MerchantId { get; set; }
    public string MerchantName { get; set; } = string.Empty;
    public decimal TotalRevenue { get; set; }
    public decimal TotalCut { get; set; }
    public int TotalOrders { get; set; }
    public long TotalExposures { get; set; }
    public long TotalDisplays { get; set; }
    public decimal AverageOrderValue { get; set; }
}

public class DashboardProfileRevenueGraph
{
    public List<decimal> Month { get; set; } = new();
    public List<decimal> LastMonth { get; set; } = new();
}

public class DashboardProfileMQLGraph
{
    //public List<long> Month { get; set; } = new();
    //public Dictionary<DateTime, long> Data { get; set; } = new();
    public List<int> Month { get; set; } = new();
    public List<long> Data { get; set; } = new();
}

public class DashboardProfileExposuresGraph
{
    public List<decimal> Month { get; set; } = new();
    public List<decimal> LastMonth { get; set; } = new();
}