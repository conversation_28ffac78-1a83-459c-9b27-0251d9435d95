using Shared.Dto.WooCommerce;
using Shared.Dto.WooCommerce.product;

namespace Shared.Dto.PrestaShop.product;

public class WebshopPrestaShop
{
    public int Id { get; set; }
    public string? ApiKey { get; set; }
    public string? Name { get; set; }
    public string? Type { get; set; }
    public string? Url { get; set; }
    public DateTime? LastUpdated { get; set; }
    public string? AccessUrl { get; set; }
    public ICollection<CategoryWooCommerce>? Categories { get; set; }
    public ICollection<ProductWooCommerce> Products { get; set; }
}