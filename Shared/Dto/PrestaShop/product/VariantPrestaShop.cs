namespace Shared.Dto.PrestaShop.product
{
    public class VariantPrestaShop
    {
        public int Id { get; set; }
        public int FkProductId { get; set; }
        public object MerchantProductId { get; set; }
        public string? MerchantParentProductId { get; set; } = "";
        public string Name { get; set; } = null!;
        public string? Sku { get; set; }
        public string? Slug { get; set; }
        public string? Permalink { get; set; }
        public DateTime? DateCreated { get; set; }
        public DateTime? DateModified { get; set; }
        public string? Status { get; set; }
        public string? Description { get; set; }
        public string? ShortDescription { get; set; }
        public decimal? Price { get; set; }
        public decimal? RegularPrice { get; set; }
        public decimal? SalePrice { get; set; }
        public DateTime? DateOnSaleFrom { get; set; }
        public DateTime? DateOnSaleTo { get; set; }
        public bool? OnSale { get; set; }
        public int? StockQuantity { get; set; }
        public string? StockStatus { get; set; }
        public string? InternalVariantId { get; set; } = null!;
    }
}