namespace Shared.Dto.Discount;

public class DiscountPartnerDto
{
    public int Id { get; set; }

    public DateTime ActivationDate { get; set; }

    public DateTime ExpirationDate { get; set; }

    public string Description { get; set; } = null!;

    public bool Active { get; set; }

    public int FkMerchantId { get; set; }

    public string DiscountType { get; set; } = null!;

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }
    public List<DiscountPartnerCodeDto> DiscountPartnerCodes { get; set; } = new List<DiscountPartnerCodeDto>();
}