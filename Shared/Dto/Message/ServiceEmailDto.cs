namespace Shared.Dto.Message;

public class ServiceEmailDto
{
    public int CampaignId { get; set; }
    public string SenderName { get; set; }
    public string SenderEmail { get; set; }

    public EmailRecipientDto Recipient { get; set; }

    public Dictionary<string, string> Parameters { get; set; }
}

public class EmailRecipientDto
{
    public string Email { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
}