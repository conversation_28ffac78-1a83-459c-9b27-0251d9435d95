namespace Shared.Dto.Webshop;

public class MerchantSettingDto
{
    public int Id { get; set; }

    public int FkMerchantId { get; set; }

    public int OrderDataMinutes { get; set; }

    public int ProductDataMinutes { get; set; }

    public int ProductLookMinutes { get; set; }

    public int AddCartMinutes { get; set; }

    public int RemoveCartMinutes { get; set; }

    public int AddWishListMinutes { get; set; }

    public int RemoveWishListMinutes { get; set; }
    public int? SiteId { get; set; }
    public int PointOfContact { get; set; }
    public bool DisableUserLogin { get; set; }
    public string TagLine { get; set; }
}