using Shared.Dto.PrestaShop.product;

namespace Shared.Dto.WooCommerce.product;

public class ProductWooCommerce
{
    public int Id { get; set; }
    public int FkMerchantId { get; set; }
    public object MerchantProductId { get; set; }
    public string Name { get; set; } = null!;
    public string? Sku { get; set; }
    public string? Slug { get; set; }
    public string? Permalink { get; set; }
    public DateTime? CreatedGmt { get; set; }
    public DateTime? LastModifiedGmt { get; set; }
    public string? Status { get; set; }
    public string? Description { get; set; }
    public string? ShortDescription { get; set; }
    public decimal? LowestPrice { get; set; }
    public decimal? HighestPrice { get; set; }
    public decimal? Price { get; set; }
    public decimal? RegularPrice { get; set; }
    public object? SalePrice { get; set; }
    public DateTime? DateOnSaleFrom { get; set; }
    public DateTime? DateOnSaleTo { get; set; }
    public bool? OnSale { get; set; }
    public int? StockQuantity { get; set; }
    public string? StockStatus { get; set; }
    public string? InternalProductId { get; set; } = null!;

    public ICollection<VariantWooCommerce>? Variants { get; set; }
    public List<CategoryWooCommerce>? Categories { get; set; }
    public List<ProductImageDto>? ProductImages { get; set; }
    public List<object>? RelatedProductIds { get; set; }
    public List<object>? UpSellProductIds { get; set; }
    public List<object>? CrossSellProductIds { get; set; }
}