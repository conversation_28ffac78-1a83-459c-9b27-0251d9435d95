namespace Shared.Dto.Shop.Sections.Internal;

public class SectionInternalDto
{
    public int Id { get; set; }
    public bool Active { get; set; }
    public bool Deleted { get; set; }
    public string Type { get; set; } = null!;
    public int SortOrder { get; set; }
    public string Name { get; set; } = null!;
    public List<SectionProductInternalDto> SectionProducts { get; set; } = new();
    public List<SectionTranslationDto> SectionTranslations { get; set; } = new();
}