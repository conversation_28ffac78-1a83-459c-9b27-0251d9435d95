namespace Shared.Dto.Shop.Sections.Internal;

public class SectionProductInternalDto
{
    public long Id { get; set; }
    public bool Active { get; set; }
    public string InternalProductId { get; set; }
    public string Name { get; set; }
    public string ImgSrc { get; set; }
    public string ProductUrl { get; set; }
    public int SortOrder { get; set; }
    public bool IsInvalidated { get; set; }
    public string? InvalidationReason { get; set; }
    public string? MerchantName { get; set; }
}