namespace Shared.Dto.Shop.Categories;

public class InternalCategoryDto
{
    public int Id { get; set; }
    public bool Active { get; set; }
    public string Name { get; set; }
    public string NameDA { get; set; }
    public string NameEN { get; set; }
    public string NameES { get; set; }
    public string ImageSrc { get; set; }
    public int Order { get; set; }
    public int? ParentId { get; set; }

    public string? PositiveTags { get; set; }

    public string? NegativeTags { get; set; }
    public List<int> MerchantIds { get; set; }
}