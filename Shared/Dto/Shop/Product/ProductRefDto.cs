using System.Text.Json.Serialization;

namespace Shared.Dto.Shop.Product;

public class ProductRefDto
{
    public string Id { get; set; }
    public string ImageSrc { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public decimal NormalPrice { get; set; }
    public decimal SalePrice { get; set; }
    public bool OnSale { get; set; }
    public bool? BestSeller { get; set; }
    public bool Favored { get; set; }
    public bool Interacted { get; set; }

    public bool IncludesVariants { get; set; }

    //The partner does not use the fields under so dont return in request
    [JsonIgnore] public long MerchantId { get; set; }

    public string MerchantName { get; set; }
}