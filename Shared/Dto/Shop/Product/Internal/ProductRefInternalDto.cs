namespace Shared.Dto.Shop.Product.Internal;

public class ProductRefInternalDto
{
    public string Id { get; set; }
    public string ImageSrc { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public decimal NormalPrice { get; set; }
    public decimal? SalePrice { get; set; }
    public bool OnSale { get; set; }
    public bool? BestSeller { get; set; }
    public bool Favored { get; set; }
    public bool IncludesVariants { get; set; }
    public long MerchantId { get; set; }
    public string MerchantName { get; set; }
    public string ProductUrl { get; set; }
    public double? Score { get; set; }
    public string? Categories { get; set; }
}