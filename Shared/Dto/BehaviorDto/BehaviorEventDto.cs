using Integration.Models.Elastic.Behavior;

namespace Shared.Dto.BehaviorDto;

public class BehaviorEventDto
{
    public string ApiKey { get; set; }
    public DateTime Event_date { get; set; }
    public DateTime Event_received { get; set; }
    public BehaviorEventClientDto? client { get; set; }
    public BehaviorEventUrlDto? url { get; set; }
    public BehaviorEventUserAgentDto? user_agent { get; set; }
    public BehaviorEventCustomerDto? Customer { get; set; }
    public BehaviorEventShopEventDto? Shop_event { get; set; }
    public BehaviorEventPluginDto? Plugin { get; set; }
}