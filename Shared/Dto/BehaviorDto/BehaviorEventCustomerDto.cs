using Newtonsoft.Json;

namespace Shared.Dto.BehaviorDto;

public class BehaviorEventCustomerDto
{
    public string? Email { get; set; }
    public string? Email2 { get; set; }
    public string? Session_id { get; set; }
    public string? ViaAds { get; set; }
    public string? ViaAds2 { get; set; }
    [JsonProperty("cookie_id")]
    public string? CookieId { get; set; }
    [JsonProperty("cookie_id_2")]
    public string? CookieId2 { get; set; }
}