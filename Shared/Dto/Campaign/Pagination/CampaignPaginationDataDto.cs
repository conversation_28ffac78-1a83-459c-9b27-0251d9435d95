namespace Shared.Dto.Campaign.Pagination
{
    public class CampaignPaginationDataDto
    {
        public int CampaignId { get; set; }
        public string CampaignName { get; set; }
        public string CampaignType { get; set; }
        public string SubjectLine { get; set; }
        public DateTime? SendingStart { get; set; }

        public DateTime? SendingEnd { get; set; }

        //public string Engagement { get; set; }
        //public string Gender { get; set; }
        public string Segment { get; set; }
        public int Audience { get; set; }
        public int Delivered { get; set; }
        public decimal OpenRate { get; set; }
        public int Opens { get; set; }
        public int UniqueOpens { get; set; }
        public int TotalExposures { get; set; }
        public int Redirects { get; set; }
        public int UniqueRedirects { get; set; }
        public decimal ClickThroughRate { get; set; }
        public int Unsubscribe { get; set; }
        public decimal UnsubscribeRate { get; set; }
        public DateTime LastModifiedDate { get; set; }
        public string? Status { get; set; }
        public int? Priority { get; set; }
    }
}