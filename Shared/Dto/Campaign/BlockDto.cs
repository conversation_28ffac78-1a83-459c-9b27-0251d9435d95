using System.Text.Json.Serialization;
using Shared.Dto.Campaign.Enums;
using Shared.Helpers.Converters;

namespace Shared.Dto.Campaign;

public class BlockDto
{
    public int Id { get; set; }

    [JsonConverter(typeof(BlockTypeDtoConverter))]
    public BlockTypeDto BlockType { get; set; }

    public string BlockGuid { get; set; }
    public int SortOrder { get; set; }
    public string Settings { get; set; }
}