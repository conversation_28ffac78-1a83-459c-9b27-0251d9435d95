using Newtonsoft.Json;
using Shared.Dto.Campaign.Enums;
using Shared.Helpers.Converters;
using Stripe;

namespace Shared.Dto.Campaign;

public class CampaignDto
{
    public int Id { get; set; }
    public int CampaignId { get; set; }
    public string? InternalCampaignId { get; set; }

    public string Name { get; set; }

    //public string PublishStatus { get; set; }
    //public bool Template { get; set; }
    //public string Body { get; set; }
    public string GlobalStyles { get; set; }
    public string PreviewText { get; set; }

    public string? FromName { get; set; }

    //public string SenderEmail { get; set; }
    public string Subject { get; set; }
    public List<BlockDto> Blocks { get; set; }

    public List<string>? IndividualRecipients { get; set; }

    //public DateTime StartDate { get; set; }
    //public DateTime EndDate { get; set; }
    public DateTime LastModifiedDate { get; set; }

    //public DateTime? LastPublishedDate { get; set; }
    public bool Active { get; set; }

    //public string? Preview { get; set; }
    public List<CampaignInstanceDto>? CampaignInstances { get; set; }
    public int? CampaignGroupId { get; set; }
    public CampaignGroupDto? CampaignGroup { get; set; }
    public FilterDto? FkFilter { get; set; }
    public int? FkFilterId { get; set; }
    public CampaignStatDto? CampaignStats { get; set; }

    public CampaignTestDto? CampaignTest { get; set; }

    public int FkSegmentId { get; set; }


    //Custom fields to angular
    public string? Status { get; set; }
    public List<DateTime>? Schedules { get; set; }
    public DateTime? LastSent { get; set; }
    public CampaignType CampaignType { get; set; }
}