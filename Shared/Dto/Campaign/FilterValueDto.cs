namespace Shared.Dto.Campaign
{
    public class FilterValueDto
    {
        public int Id { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime LastModifiedDate { get; set; }

        public bool Active { get; set; }

        public string Value { get; set; } = null!;

        public string? Condition { get; set; }

        public int? Time { get; set; }

        public int FkFilterTemplateId { get; set; }

        public int FkFilterId { get; set; }

        public FilterTemplateDto? FkFilterTemplate { get; set; } = null!;
    }
}