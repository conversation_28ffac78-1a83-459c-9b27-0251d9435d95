namespace Shared.Dto.Campaign;

public class CampaignInstanceDto
{
    public int Id { get; set; }
    public int CampaignId { get; set; }
    public string InternalCampaignId { get; set; }
    public string Body { get; set; }
    public string Subject { get; set; }
    public string PreviewText { get; set; }
    public string FromName { get; set; }
    public DateTime InitiatedDate { get; set; }
    public DateTime? ScheduledDate { get; set; }
    public bool ForceSend { get; set; }
}