namespace Shared.Dto.Campaign;

public class FilterDto
{
    public int Id { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastModifiedDate { get; set; }
    public string Match { get; set; } = null!;
    public byte OnlyUnengaged { get; set; }
    public string? PresetName { get; set; }
    public string? OpenRate { get; set; }
    public List<FilterValueDto> FilterValues { get; set; }
}