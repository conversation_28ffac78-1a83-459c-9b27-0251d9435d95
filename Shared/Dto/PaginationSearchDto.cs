namespace Shared.Dto;

public class PaginationSearchDto
{
    public int Page { get; set; }
    public int Size { get; set; }
    public string SortName { get; set; }
    public string SortOrder { get; set; }
    public DateTime From { get; set; }
    public DateTime To { get; set; }
    public string Search { get; set; }
    public string? Type { get; set; }
    public List<PaginationSearchFilterDto> Filters { get; set; }
}

public class PaginationSearchFilterDto
{
    public string Field { get; set; }
    public string MatchMode { get; set; }
    public string? Value { get; set; }
}