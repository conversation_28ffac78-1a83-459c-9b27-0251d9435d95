public class PartnerConfigDto
{
    public int PartnerId { get; set; }
    public string PartnerName { get; set; }
    public string PartnerLogoUrl { get; set; }
    public string PartnerAdsName { get; set; }
    public List<string> LanguageCodes { get; set; }
    public string DefaultLang { get; set; }
    public string TermsAndConditionsUrl { get; set; } 
    public string DefaultCurrency { get; set; }
    public List<PartnerOnboardingConfigDto> OnboardingConfig { get; set; }
}

public class PartnerOnboardingConfigDto
{
    public string CmsType { get; set; }
    public int TotalOnboardingSteps { get; set; }
    public string PluginUrl { get; set; }
}
