namespace Shared.Constants;

public static class ShopEventTypes
{
    private static readonly HashSet<string> KnownEventTypes = new()
    {
        "products_list_viewed",
        "product_viewed",
        "product_favorised",
        "product_unfavorised",
        "product_link_opened",
        "product_searched",
        "product_redirect"
    };

    private static readonly HashSet<string> DisplayEventTypes = new()
    {
        "products_list_viewed"
    };

    private static readonly HashSet<string> InteractEventTypes = new()
    {
        "product_viewed",
        "product_favorised",
        "product_unfavorised",
        "product_link_opened",
        "product_searched"
    };

    private static readonly HashSet<string> RedirectEventTypes = new()
    {
        "product_redirect"
    };

    public static IReadOnlyCollection<string> GetKnownEventTypes() => KnownEventTypes;

    public static bool IsValidEventType(string eventType)
    {
        return KnownEventTypes.Contains(eventType);
    }

    public static bool IsDisplayEvent(string eventType)
    {
        return DisplayEventTypes.Contains(eventType);
    }

    public static bool IsInteractEvent(string eventType)
    {
        return InteractEventTypes.Contains(eventType);
    }

    public static bool IsRedirectEvent(string eventType)
    {
        return RedirectEventTypes.Contains(eventType);
    }
}