using Nest;
using Shared.Elastic.Models.ElasticCampaignsMailsOpens;
using Shared.Helpers.Converters;
using ILogger = Serilog.ILogger;

namespace Shared.Elastic.CampaignMailOpen;

public class ElasticCampaignMailOpenService(ElasticClient elasticClient, ILogger logger)
    : IElasticCampaignMailOpenService
{
    private const string Index = "campaigns-mails-opens";

    public async Task<long> Opens(DateTime? from, DateTime? to, int? merchantId = null, int? campaignId = null,
        string? email = null)
    {
        try
        {
            if (from == null && to == null && merchantId == null && campaignId == null && email == null)
            {
                return 0;
            }

            var queryBlock = Query(from, to, email, merchantId, null, campaignId);

            return (await elasticClient.CountAsync<ElasticCampaignsMailsOpens>(s => s
                .Index(Index)
                .Query(queryBlock)
            )).Count;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error Opens");
            return -1;
        }
    }

    public async Task<long> OpensByCampaignIds(DateTime? from, DateTime? to, List<int> campaignIds)
    {
        var queryBlock = Query(from, to, null, null, null, campaignIds: campaignIds);
        var response = await elasticClient.CountAsync<ElasticCampaignsMailsOpens>(s => s
            .Index(Index)
            .Query(queryBlock)
        );

        return response.Count;
    }
    public async Task<long> OpensByMerchantIds(DateTime? from, DateTime? to, List<int> merchantIds, int? campaignId = null,
        string? email = null)
    {
        try
        {
            if (from == null && to == null && campaignId == null && email == null)
            {
                return 0;
            }

            var queryBlock = Query(from, to, email, null, merchantIds, campaignId);

            return (await elasticClient.CountAsync<ElasticCampaignsMailsOpens>(s => s
                .Index(Index)
                .Query(queryBlock)
            )).Count;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error OpensByMerchantIds");
            return -1;
        }
    }

    public async Task<long> ExposuresByMerchantIds(DateTime? from, DateTime? to, List<int> merchantIds, int? campaignId = null,
        string? email = null)
    {
        try
        {
            if (from == null && to == null && campaignId == null && email == null)
            {
                return 0;
            }

            var queryBlock = Query(from, to, email, null, merchantIds, campaignId);

            var opens = new List<ElasticCampaignsMailsOpens>();
            var searchResponse = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_event!.Webshop_id,
                            f => f.Customer.Email,
                            f => f.Customer.Campaign_Id,
                            f => f.EventObject.Created
                        )
                    )
                )
                .Index(Index)
                .Query(queryBlock)
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.EventObject.Created))
                .Scroll("2m")
            );

            opens.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
                   searchResponse.Documents.Count != 0)
            {
                searchResponse =
                    await elasticClient.ScrollAsync<ElasticCampaignsMailsOpens>("2m", searchResponse.ScrollId);
                opens.AddRange(searchResponse.Documents);
            }

            await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));

            return opens.Sum(a => a.Shop_event.Webshop_id?.Count(id => !string.IsNullOrEmpty(id)) ?? 0);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error ExposuresByMerchantIds");
            return -1;
        }
    }

    public async Task<long> UniqueExposuresByMerchantIds(DateTime? from, DateTime? to, List<int> merchantIds, int? campaignId = null,
        string? email = null)
    {
        try
        {
            if (from == null && to == null && campaignId == null && email == null)
            {
                return 0;
            }

            var queryBlock = Query(from, to, email, null, merchantIds, campaignId);

            var opens = new List<ElasticCampaignsMailsOpens>();
            var searchResponse = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_event!.Webshop_id,
                            f => f.Customer.Email,
                            f => f.Customer.Campaign_Id,
                            f => f.EventObject.Created
                        )
                    )
                )
                .Index(Index)
                .Query(queryBlock)
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.EventObject.Created))
                .Scroll("2m")
            );

            opens.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
                   searchResponse.Documents.Count != 0)
            {
                searchResponse =
                    await elasticClient.ScrollAsync<ElasticCampaignsMailsOpens>("2m", searchResponse.ScrollId);
                opens.AddRange(searchResponse.Documents);
            }

            await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));

            return opens.Sum(a => a.Shop_event.Webshop_id?.Count(id => !string.IsNullOrEmpty(id)) ?? 0);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error UniqueExposuresByMerchantIds");
            return -1;
        }
    }

    public async Task<long> UniqueOpens(DateTime? from, DateTime? to, int? merchantId = null, int? campaignId = null)
    {
        try
        {
            if (from == null && to == null && merchantId == null && campaignId == null)
            {
                return 0;
            }

            var queryBlock = Query(from, to, null, merchantId, null, campaignId);

            CompositeKey afterKey = null;
            List<CompositeBucket> allBuckets = [];
            do
            {
                var searchResponse = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
                    .Index(Index)
                    .Query(queryBlock)
                    .Aggregations(a => a
                        .Composite("unique_emails", c => c
                                .Sources(so => so
                                    .Terms("email", t => t
                                        .Field(f => f.Customer.Email)))
                                .After(afterKey)
                                .Size(10000) // Set the batch size
                        )
                    )
                );
                var compositeAggregation = searchResponse.Aggregations.Composite("unique_emails");
                allBuckets.AddRange(compositeAggregation.Buckets);
                afterKey = compositeAggregation.AfterKey;
            } while (afterKey != null);

            return allBuckets.Count;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error UniqueOpens");
            return -1;
        }
    }

    public async Task<long> UniqueOpensByCampaignIds(DateTime? from, DateTime? to, List<int> campaignIds)
    {
        var queryBlock = Query(from, to, null, null, null, campaignIds: campaignIds);
        var response = await elasticClient.CountAsync<ElasticCampaignsMailsOpens>(s => s
            .Index(Index)
            .Query(queryBlock)
        );

        return response.Count;
    }
    
    public async Task<long> UniqueOpensByMerchantIds(DateTime? from, DateTime? to, List<int> merchantIds, int? campaignId = null)
    {
        try
        {
            if (from == null && to == null && campaignId == null)
            {
                return 0;
            }

            var queryBlock = Query(from, to, null, null, merchantIds, campaignId);

            CompositeKey afterKey = null;
            List<CompositeBucket> allBuckets = [];
            do
            {
                var searchResponse = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
                    .Index(Index)
                    .Query(queryBlock)
                    .Aggregations(a => a
                        .Composite("unique_emails", c => c
                                .Sources(so => so
                                    .Terms("email", t => t
                                        .Field(f => f.Customer.Email)))
                                .After(afterKey)
                                .Size(10000) // Set the batch size
                        )
                    )
                );
                var compositeAggregation = searchResponse.Aggregations.Composite("unique_emails");
                allBuckets.AddRange(compositeAggregation.Buckets);
                afterKey = compositeAggregation.AfterKey;
            } while (afterKey != null);

            return allBuckets.Count;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error UniqueOpens");
            return -1;
        }
    }

    public async Task<List<ElasticCampaignsMailsOpens>> OpensData(DateTime? from, DateTime? to, string? email,
        int? merchantId)
    {
        try
        {
            if (from == null && to == null && email == null && merchantId == null)
            {
                return new List<ElasticCampaignsMailsOpens>();
            }

            var queryBlock = Query(from, to, email, merchantId);

            var orders = new List<ElasticCampaignsMailsOpens>();
            var searchResponse = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_event!.Webshop_id,
                            f => f.Customer.Email,
                            f => f.Customer.Campaign_Id,
                            f => f.EventObject.Created
                        )
                    )
                )
                .Index(Index)
                .Query(queryBlock)
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.EventObject.Created))
                .Scroll("2m")
            );

            orders.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
                   searchResponse.Documents.Count != 0)
            {
                searchResponse =
                    await elasticClient.ScrollAsync<ElasticCampaignsMailsOpens>("2m", searchResponse.ScrollId);
                orders.AddRange(searchResponse.Documents);
            }

            await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
            return orders.Where(a => !string.IsNullOrEmpty(a.Shop_event.Webshop_id.FirstOrDefault())).ToList();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error OpensData");
            return new List<ElasticCampaignsMailsOpens>();
        }
    }

    public async Task<List<ElasticCampaignsMailsOpens>> OpensDataByCampaignIds(List<int> campaignIds)
    {
        var queryBlock = Query(null, null, null, null, null, null, campaignIds: campaignIds);
        var searchResponse = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
            .Index(Index)
            .Query(queryBlock)
            .Size(10_000)
            .Sort(srt => srt.Descending(f => f.EventObject.Created))
            .Scroll("2m")
        );

        return searchResponse.Documents.ToList();
    }


    public async Task<List<ElasticCampaignsMailsOpens>> OpensDataMultipleEmails(DateTime? from, DateTime? to,
        List<string>? emails, int? merchantId)
    {
        try
        {
            if (from == null && to == null && (emails == null || !emails.Any()) && merchantId == null)
            {
                return new List<ElasticCampaignsMailsOpens>();
            }

            var queryBlock = QueryDescriptor(from, to, emails, merchantId);

            var orders = new List<ElasticCampaignsMailsOpens>();
            var searchResponse = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_event!.Webshop_id,
                            f => f.Customer.Email,
                            f => f.Customer.Campaign_Id,
                            f => f.EventObject.Created
                        )
                    )
                )
                .Index(Index)
                .Query(queryBlock)
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.EventObject.Created))
                .Scroll("2m")
            );

            orders.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
                   searchResponse.Documents.Count != 0)
            {
                searchResponse =
                    await elasticClient.ScrollAsync<ElasticCampaignsMailsOpens>("2m", searchResponse.ScrollId);
                orders.AddRange(searchResponse.Documents);
            }

            await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
            return orders.Where(a => !string.IsNullOrEmpty(a.Shop_event.Webshop_id.FirstOrDefault())).ToList();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error OpensData");
            return new List<ElasticCampaignsMailsOpens>();
        }
    }

    private Func<QueryContainerDescriptor<ElasticCampaignsMailsOpens>, QueryContainer> QueryDescriptor(DateTime? from,
        DateTime? to, List<string>? emails, int? merchantId, List<int>? merchantIds = null)
    {
        return q =>
        {
            var filters = new List<Func<QueryContainerDescriptor<ElasticCampaignsMailsOpens>, QueryContainer>>();

            if (from.HasValue)
            {
                filters.Add(
                    f => f.DateRange(r => r.Field(ff => ff.EventObject.Created).GreaterThanOrEquals(from.Value)));
            }

            if (to.HasValue)
            {
                filters.Add(f => f.DateRange(r => r.Field(ff => ff.EventObject.Created).LessThanOrEquals(to.Value)));
            }

            if (emails != null && emails.Any())
            {
                filters.Add(f => f.Terms(t => t.Field(ff => ff.Customer.Email).Terms(emails)));
            }

            if (merchantId.HasValue)
            {
                filters.Add(f =>
                    f.Term(t => t.Field(ff => ff.Shop_event!.Webshop_id).Value(merchantId.Value.ToString())));
            }

            return q.Bool(b => b.Must(filters));
        };
    }


    public async Task<Dictionary<DateTime, long>> OpensDayCount(DateTime from, DateTime to, int? merchantId)
    {
        try
        {
            var queryBlock = QueryDescriptor(from, to, null, merchantId);
            var response = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
                .Index(Index)
                .Query(queryBlock)
                .Aggregations(a => a
                    .DateHistogram("emails_by_day", dh => dh
                        .Field(f => f.EventObject.Created)
                        .CalendarInterval(DateInterval.Day)
                        .Aggregations(subAgg => merchantId.HasValue
                            ? subAgg.ValueCount("countData", vc => vc.Field(f => f.Shop_event.Webshop_id))
                            : null)
                    )
                )
            );

            var emailsByDay = response.Aggregations.DateHistogram("emails_by_day").Buckets
                .ToDictionary(
                    bucket => bucket.Date, // or bucket.Key.Date to get as DateTime
                    bucket => bucket.DocCount.Value
                );

            return emailsByDay;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error OpensDayCount");
            return new Dictionary<DateTime, long>();
        }
    }


    public async Task<Dictionary<DateTime, long>> OpensDayCountByMerchantIds(DateTime from, DateTime to, List<int> merchantIds)
    {
        try
        {
            var queryBlock = Query(from, to, null, null, merchantIds);
        
            var response = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
                .Index(Index)
                .Query(queryBlock)
                .Aggregations(a => a
                    .DateHistogram("emails_by_day", dh => dh
                        .Field(f => f.EventObject.Created)
                        .CalendarInterval(DateInterval.Day)
                        .Aggregations(subAgg => 
                                merchantIds != null && merchantIds.Any() 
                                    ? subAgg.ValueCount("countData", vc => vc.Field(f => f.Shop_event.Webshop_id))
                                    : null)
                    )
                )
            );

            var emailsByDay = response.Aggregations.DateHistogram("emails_by_day").Buckets
                .ToDictionary(
                    bucket => bucket.Date,
                    bucket => bucket.DocCount.Value
                );

            return emailsByDay;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error OpensDayCountByMerchantIds");
            return new Dictionary<DateTime, long>();
        }
    }

    public async Task<Dictionary<DateTime, long>> ExposuresDayCountByMerchantIds(DateTime from, DateTime to, List<int> merchantIds)
    {
        try
        {
            var queryBlock = Query(from, to, null, null, merchantIds);

            var opens = new List<ElasticCampaignsMailsOpens>();
            var searchResponse = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_event!.Webshop_id,
                            f => f.Customer.Email,
                            f => f.EventObject.Created
                        )
                    )
                )
                .Index(Index)
                .Query(queryBlock)
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.EventObject.Created))
                .Scroll("2m")
            );

            opens.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
                   searchResponse.Documents.Count != 0)
            {
                searchResponse =
                    await elasticClient.ScrollAsync<ElasticCampaignsMailsOpens>("2m", searchResponse.ScrollId);
                opens.AddRange(searchResponse.Documents);
            }

            await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));

            // Group by date and count webshop IDs for each day
            var emailsByDay = opens
                .Where(a => a.Shop_event?.Webshop_id != null)
                .SelectMany(a => a.Shop_event.Webshop_id
                    .Where(id => !string.IsNullOrEmpty(id))
                    .Select(id => new { Date = a.EventObject.Created.Date, WebshopId = id }))
                .GroupBy(x => x.Date)
                .ToDictionary(
                    group => group.Key,
                    group => (long)group.Count()
                );

            return emailsByDay;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error ExposuresDayCountByMerchantIds");
            return new Dictionary<DateTime, long>();
        }
    }

    public async Task<List<ElasticCampaignsMailsOpens>> OpensDayCountData(DateTime from, DateTime to)
    {
        try
        {
            var uniqueOpens = new List<ElasticCampaignsMailsOpens>();
            var queryBlock = Query(from, to);
            CompositeKey? afterKey = null;
            do
            {
                var searchResponse = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
                    .Index(Index)
                    .Query(queryBlock)
                    .Aggregations(a => a
                        .Composite("group_by_emailGuid", c => c
                            .Sources(src => src
                                .Terms("emailGuid", t => t.Field(f => f.Customer.Email_guid))
                            )
                            .Size(10000) // Use this to paginate through your results
                            .After(afterKey) // Use the afterKey for pagination
                            .Aggregations(aa => aa
                                .TopHits("top_hit", th => th
                                    .Size(1)
                                )
                            )
                        )
                    )
                );

                // Handle results
                var compositeAgg = searchResponse.Aggregations.Composite("group_by_emailGuid");
                if (compositeAgg != null)
                {
                    foreach (var bucket in compositeAgg.Buckets)
                    {
                        var topHit = bucket.TopHits("top_hit");
                        var doc = topHit.Documents<ElasticCampaignsMailsOpens>().First();
                        uniqueOpens.Add(doc);
                    }
                }

                afterKey = compositeAgg?.AfterKey ?? null;
            } while (afterKey != null);

            return uniqueOpens;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error OpensDayCountData");
            return new List<ElasticCampaignsMailsOpens>();
        }
    }

    public async Task<ElasticCampaignsMailsOpens?> OpensData(int campaignId)
    {
        try
        {
            var queryBlock = Query(null, null, null, null, null, campaignId);
            var searchResponse = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_event!.Webshop_id,
                            f => f.Customer.Email,
                            f => f.Customer.Campaign_Id,
                            f => f.EventObject.Created
                        )
                    )
                )
                .Index(Index)
                .Query(queryBlock)
                .Size(1)
                .Sort(srt => srt.Descending(f => f.EventObject.Created))
            );

            return searchResponse.Documents.FirstOrDefault();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error OpensData");
            return new ElasticCampaignsMailsOpens();
        }
    }

    public async Task<List<ElasticCampaignsMailsOpens>> CheckOpens(List<string> emailGuids)
    {
        try
        {
            var events = new List<ElasticCampaignsMailsOpens>();
            var searchResponse = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.timestamp,
                            f => f.Customer.Email,
                            f => f.Customer.Email_guid
                        )
                    )
                )
                .Index(Index)
                .Query(q =>
                    q.Bool(b => b
                        .Filter(f => f.Terms(t =>
                            t.Field(f => f.Customer.Email_guid).Terms(emailGuids))))
                )
                .Size(10000)
                .Scroll("2m")
            );
            events.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count != 0)
            {
                searchResponse =
                    await elasticClient.ScrollAsync<ElasticCampaignsMailsOpens>("2m", searchResponse.ScrollId);
                events.AddRange(searchResponse.Documents);
            }

            await elasticClient.ClearScrollAsync(cs => cs
                .ScrollId(searchResponse.ScrollId)
            );
            return events;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error CheckOpens");
            return new List<ElasticCampaignsMailsOpens>();
        }
    }

    private Func<QueryContainerDescriptor<ElasticCampaignsMailsOpens>, QueryContainer> Query(DateTime? from,
        DateTime? to, string? email = null, int? merchantId = null, List<int>? merchantIds = null, int? campaignId = null, List<int>? campaignIds = null)
    {
        QueryContainer QueryBlock(QueryContainerDescriptor<ElasticCampaignsMailsOpens> q)
        {
            QueryContainer query = new QueryContainer();
            if (to.HasValue)
            {
                to = DateTimeExtensions.SetTimeToMax(to.Value);
            }

            // Check from date
            if (from.HasValue && to.HasValue)
            {
                query &= q.DateRange(dt => dt.Field(field => field.EventObject.Created)
                    .GreaterThanOrEquals(from)
                    .LessThanOrEquals(to));
            }
            else if (from.HasValue)
            {
                query &= q.DateRange(dt => dt.Field(field => field.EventObject.Created)
                    .GreaterThanOrEquals(from));
            }
            else if (to.HasValue)
            {
                query &= q.DateRange(dt => dt.Field(field => field.EventObject.Created)
                    .LessThanOrEquals(to));
            }

            // Check for email
            if (!string.IsNullOrEmpty(email))
            {
                query &= q.Terms(t => t.Field(f => f.Customer.Email).Terms(email));
            }

            // Check for merchant
            if (merchantId.HasValue && merchantId != 0)
            {
                query &= q.Terms(t => t.Field(f => f.Shop_event.Webshop_id).Terms(merchantId));
            }

            // Merchant IDs filter - Fix to exclude all results if the list is empty
            if (merchantIds != null)
            {
                if (merchantIds.Any())
                {
                    query &= q.Terms(t => t.Field(f => f.Shop_event.Webshop_id).Terms(merchantIds));
                }
                else
                {
                    // If merchantIds is empty, create a query that matches nothing
                    query &= q.Bool(b => b.MustNot(mn => mn.MatchAll()));
                }
            }

            // Check for campaign
            if (campaignId.HasValue)
            {
                query &= q.Terms(t => t.Field(f => f.Customer.Campaign_Id).Terms(campaignId));
            }

            // Check for campaign IDs
            if (campaignIds != null)
            {
                query &= q.Terms(t => t.Field(f => f.Customer.Campaign_Id).Terms(campaignIds));
            }

            return query;
        }

        return QueryBlock;
    }
}