using Shared.Elastic.Models.ElasticCampaignsMailsOpens;
using Shared.Models.Elastic.ElasticCampaignsMailsOpens;

namespace Shared.Elastic.CampaignMailOpen;

public interface IElasticCampaignMailOpenService
{
    Task<long> Opens(DateTime? from, DateTime? to, int? merchantId = null, int? campaignId = null,
        string? email = null);
    Task<long> OpensByCampaignIds(DateTime? from, DateTime? to, List<int> campaignIds);

    Task<long> OpensByMerchantIds(DateTime? from, DateTime? to, List<int> merchantIds, int? campaignId = null,
        string? email = null);

    // Exposure methods (actual webshop ID counts)
    Task<long> ExposuresByMerchantIds(DateTime? from, DateTime? to, List<int> merchantIds, int? campaignId = null,
        string? email = null);
    // Unique exposures methods (actual webshop ID counts)
    Task<long> UniqueExposuresByMerchantIds(DateTime? from, DateTime? to, List<int> merchantIds, int? campaignId = null,
        string? email = null);

    Task<long> UniqueOpens(DateTime? from, DateTime? to, int? merchantId = null, int? campaignId = null);
    Task<long> UniqueOpensByCampaignIds(DateTime? from, DateTime? to, List<int> campaignIds);
    Task<long> UniqueOpensByMerchantIds(DateTime? from, DateTime? to, List<int> merchantIds, int? campaignId = null);
    Task<List<ElasticCampaignsMailsOpens>> OpensData(DateTime? from, DateTime? to, string? email, int? merchantId);
    Task<List<ElasticCampaignsMailsOpens>> OpensDataByCampaignIds(List<int> campaignIds);

    Task<List<ElasticCampaignsMailsOpens>> OpensDataMultipleEmails(DateTime? from, DateTime? to, List<string>? emails,
        int? merchantId);

    Task<Dictionary<DateTime, long>> OpensDayCount(DateTime from, DateTime to, int? merchantId = null);
    Task<Dictionary<DateTime, long>> OpensDayCountByMerchantIds(DateTime from, DateTime to, List<int> merchantId);
    
    // Exposure day count methods (actual webshop ID counts)
    Task<Dictionary<DateTime, long>> ExposuresDayCountByMerchantIds(DateTime from, DateTime to, List<int> merchantId);
    
    Task<List<ElasticCampaignsMailsOpens>> OpensDayCountData(DateTime from, DateTime to);
    Task<ElasticCampaignsMailsOpens?> OpensData(int campaignId);
    Task<List<ElasticCampaignsMailsOpens>> CheckOpens(List<string> emailGuids);
}