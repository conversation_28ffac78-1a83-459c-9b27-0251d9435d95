using Nest;
using Shared.Models.Merchant;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Serilog;
using System.Globalization;

namespace Shared.Elastic.MerchantCustomerEvent
{
    public class ElasticMerchantCustomerEventService(ElasticClient elasticClient, ILogger logger)
        : IElasticMerchantCustomerEventService
    {
        private const string Index = "merchants-products-lookup";

        public async Task<ProductReturnEventDto?> ProductLookUp(SearchProductDto searchProduct)
        {
            ProductReturnEventDto? result = null;

            var queryContainer = new List<QueryContainer>();

            if (!string.IsNullOrEmpty(searchProduct.ProductId))
                queryContainer.Add(new MatchQuery
                    {Field = "Product.Merchant_product_id", Query = searchProduct.ProductId});

            if (!string.IsNullOrEmpty(searchProduct.Sku))
                queryContainer.Add(new MatchQuery {Field = "Product.Sku", Query = searchProduct.Sku});

            if (!string.IsNullOrEmpty(searchProduct.FullUrl))
                queryContainer.Add(new MatchQuery {Field = "Product.Permalink", Query = searchProduct.FullUrl});

            if (!string.IsNullOrEmpty(searchProduct.Url))
                queryContainer.Add(new MatchQuery {Field = "Product.Permalink", Query = searchProduct.Url});

            if (queryContainer.Count > 0)
            {
                var searchResp = await elasticClient.SearchAsync<EventLookUpDto>(s => s
                    .Index(Index)
                    .Size(1)
                    .Source(s => s.Includes(i => i.Fields(new[]
                        {"Product.Internal_product_id", "Product.Internal_variant_id", "Product.Regular_price"})))
                    .Query(q => q
                        .Bool(b => b
                            .Should(queryContainer.ToArray())
                            .MinimumShouldMatch(1)
                            .Must(mu => mu
                                .Match(m => m.Field("Merchant.Id")
                                    .Query(searchProduct.WebshopId!.Value.ToString(CultureInfo.InvariantCulture)))
                            )
                        )
                    )
                );

                if (searchResp.IsValid)
                {
                    var document = searchResp.Documents.FirstOrDefault();

                    if (document != null)
                    {
                        result = new ProductReturnEventDto
                            {Sku = searchProduct.Sku ?? "", Price = (decimal) document.EventProductInfo.RegularPrice};

                        if (document.EventProductInfo.InternalProductId == document.EventProductInfo.InternalVariantId)
                        {
                            result.ParentInternalProductId = "";
                            result.InternalProductId = document.EventProductInfo.InternalProductId;
                        }
                        else
                        {
                            result.ParentInternalProductId = document.EventProductInfo.InternalProductId;
                            result.InternalProductId = document.EventProductInfo.InternalVariantId;
                        }
                    }
                }
            }

            return result;
        }
    }
}