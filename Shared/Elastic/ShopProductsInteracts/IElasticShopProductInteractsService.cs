using Shared.Elastic.Models.ElasticShopProductsDisplays;
using Shared.Elastic.Models.ElasticShopProductsInteracts;

namespace Shared.Elastic.ShopProductsInteracts;

public interface IElasticShopProductInteractsService
{
    Task<long> Interacts(DateTime? from, DateTime? to, int? merchantId = null, string? email = null);
    Task<long> UniqueInteracts(DateTime? from, DateTime? to, int? merchantId = null);

    Task<List<ElasticShopProductsInteracts>>
        InteractsData(DateTime? from, DateTime? to, string? email, int? merchantId);

    Task<List<ElasticShopProductsInteracts>> InteractsDataMultipleEmails(DateTime? from, DateTime? to,
        List<string>? emails, int? merchantId);

    Task<Dictionary<DateTime, long>> InteractsDayCount(DateTime from, DateTime to, int? merchantId = null);
}