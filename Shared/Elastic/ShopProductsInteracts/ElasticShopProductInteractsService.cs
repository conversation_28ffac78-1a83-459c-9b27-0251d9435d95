using Nest;
using Serilog;
using Shared.Elastic.Models.ElasticShopProductsInteracts;
using Shared.Helpers.Converters;

namespace Shared.Elastic.ShopProductsInteracts;

public class ElasticShopProductInteractsService(ElasticClient elasticClient, ILogger logger)
    : IElasticShopProductInteractsService
{
    private const string Index = "customers-shop.products-interacts";

    public async Task<long> Interacts(DateTime? from, DateTime? to, int? merchantId = null, string? email = null)
    {
        try
        {
            if (from == null && to == null && merchantId == null && email == null)
            {
                return 0;
            }

            var queryBlock = Query(from, to, email, merchantId);
            return (await elasticClient.CountAsync<ElasticShopProductsInteracts>(s => s
                .Index(Index)
                .Query(queryBlock)
            )).Count;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error Interacts");
            return -1;
        }
    }

    public async Task<long> UniqueInteracts(DateTime? from, DateTime? to, int? merchantId = null)
    {
        try
        {
            if (from == null && to == null && merchantId == null)
            {
                return 0;
            }

            var queryBlock = Query(from, to, null, merchantId);
            CompositeKey afterKey = null;
            List<CompositeBucket> allBuckets = [];
            do
            {
                var searchResponse =
                    await elasticClient.SearchAsync<ElasticShopProductsInteracts>(s => s
                        .Index(Index)
                        .Query(queryBlock)
                        .Aggregations(a => a
                            .Composite("unique_emails", c => c
                                    .Sources(so => so
                                        .Terms("email", t => t
                                            .Field(f => f.Customer.Email)))
                                    .After(afterKey)
                                    .Size(10000) // Set the batch size
                            )
                        )
                    );
                var compositeAggregation = searchResponse.Aggregations.Composite("unique_emails");
                allBuckets.AddRange(compositeAggregation.Buckets);
                afterKey = compositeAggregation.AfterKey;
            } while (afterKey != null);

            return allBuckets.Count;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error UniqueInteracts");
            return -1;
        }
    }

    public async Task<List<ElasticShopProductsInteracts>> InteractsData(DateTime? from,
        DateTime? to, string? email, int? merchantId)
    {
        try
        {
            if (from == null && to == null && email == null && merchantId == null)
            {
                return [];
            }

            var queryBlock = Query(from, to, email, merchantId);

            var orders = new List<ElasticShopProductsInteracts>();
            var searchResponse = await elasticClient.SearchAsync<ElasticShopProductsInteracts>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Merchant!.Id,
                            f => f.Customer.Email,
                            f => f.Action_date
                        )
                    )
                )
                .Index(Index)
                .Query(queryBlock)
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.Action_date))
                .Scroll("2m")
            );

            orders.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
                   searchResponse.Documents.Count != 0)
            {
                searchResponse =
                    await elasticClient.ScrollAsync<ElasticShopProductsInteracts>("2m", searchResponse.ScrollId);
                orders.AddRange(searchResponse.Documents);
            }

            await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
            return orders.ToList();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error InteractsData");
            return [];
            ;
        }
    }


    public async Task<List<ElasticShopProductsInteracts>> InteractsDataMultipleEmails(DateTime? from,
        DateTime? to, List<string>? emails, int? merchantId)
    {
        try
        {
            if (from == null && to == null && emails == null && merchantId == null)
            {
                return [];
            }

            var queryBlock = Query(from, to, null, merchantId);
            var block = queryBlock;
            queryBlock = q => block(q) && q.Terms(t => t.Field(f => f.Customer.Email).Terms(emails));

            var orders = new List<ElasticShopProductsInteracts>();
            var searchResponse = await elasticClient.SearchAsync<ElasticShopProductsInteracts>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Merchant!.Id,
                            f => f.Customer.Email,
                            f => f.Action_date
                        )
                    )
                )
                .Index(Index)
                .Query(queryBlock)
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.Action_date))
                .Scroll("2m")
            );

            orders.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
                   searchResponse.Documents.Count != 0)
            {
                searchResponse =
                    await elasticClient.ScrollAsync<ElasticShopProductsInteracts>("2m", searchResponse.ScrollId);
                orders.AddRange(searchResponse.Documents);
            }

            await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
            return orders.ToList();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error InteractsDataMultipleEmails");
            return [];
        }
    }


    public async Task<Dictionary<DateTime, long>> InteractsDayCount(DateTime from, DateTime to, int? merchantId)
    {
        try
        {
            var queryBlock = Query(from, to, null, merchantId);
            var response = await elasticClient.SearchAsync<ElasticShopProductsInteracts>(
                s => s
                    .Index(Index)
                    .Query(queryBlock)
                    .Aggregations(a => a
                        .DateHistogram("emails_by_day", dh => dh
                            .Field(f => f.Action_date)
                            .CalendarInterval(DateInterval.Day)
                            .Aggregations(subAgg => merchantId.HasValue
                                ? subAgg.ValueCount("countData", vc => vc.Field(f => f.Merchant))
                                : null)
                        )
                    )
            );

            var emailsByDay = response.Aggregations.DateHistogram("emails_by_day").Buckets
                .ToDictionary(
                    bucket => bucket.Date, // or bucket.Key.Date to get as DateTime
                    bucket => bucket.DocCount.Value
                );

            return emailsByDay;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error InteractsDayCount");
            return new Dictionary<DateTime, long>();
        }
    }

    private Func<QueryContainerDescriptor<ElasticShopProductsInteracts>, QueryContainer>
        Query(DateTime? from, DateTime? to, string? email = null, int? merchantId = null)
    {
        QueryContainer QueryBlock(
            QueryContainerDescriptor<ElasticShopProductsInteracts> q)
        {
            QueryContainer query = new QueryContainer();
            if (to.HasValue)
            {
                to = DateTimeExtensions.SetTimeToMax(to.Value);
            }

            // Check from date
            if (from.HasValue && to.HasValue)
            {
                query &= q.DateRange(dt => dt.Field(field => field.Action_date)
                    .GreaterThanOrEquals(from)
                    .LessThanOrEquals(to));
            }
            else if (from.HasValue)
            {
                query &= q.DateRange(dt => dt.Field(field => field.Action_date)
                    .GreaterThanOrEquals(from));
            }
            else if (to.HasValue)
            {
                query &= q.DateRange(dt => dt.Field(field => field.Action_date)
                    .LessThanOrEquals(to));
            }

            // Check for email
            if (!string.IsNullOrEmpty(email))
            {
                query &= q.Terms(t => t.Field(f => f.Customer.Email).Terms(email));
            }

            // Check for merchant
            if (merchantId.HasValue && merchantId != 0)
            {
                query &= q.Terms(t => t.Field(f => f.Merchant.Id).Terms(merchantId));
            }

            return query;
        }

        return QueryBlock;
    }
}