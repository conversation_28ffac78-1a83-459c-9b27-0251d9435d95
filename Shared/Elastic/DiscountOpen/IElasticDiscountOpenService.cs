using Shared.Models.Elastic.ElasticCampaignsMailsOpens;
using Shared.Models.Elastic.ElasticCustomerDiscounts;

namespace Shared.Elastic.DiscountOpen;

public interface IElasticDiscountOpenService
{
    Task<long> Opens(DateTime? from, DateTime? to, int? merchantId = null, string? email = null, string type = "");

    Task<long> OpensByMerchantIds(DateTime? from, DateTime? to, List<int>? merchantIds = null, string? email = null,
        string type = "");
    
    // Exposure methods (actual webshop ID counts)
    Task<long> ExposuresByMerchantIds(DateTime? from, DateTime? to, List<int>? merchantIds = null, string? email = null,
        string type = "");
    
    Task<long> UniqueOpens(DateTime? from, DateTime? to, int? merchantId = null, string type = "");

    Task<List<ElasticCustomerDiscounts>> OpensData(DateTime? from, DateTime? to, string? email, int? merchantId,
        string type = "");

    Task<List<ElasticCustomerDiscounts>> OpensDataMultipleEmails(DateTime? from, DateTime? to, List<string>? emails,
        int? merchantId, string type = "");

    Task<Dictionary<DateTime, long>>
        OpensDayCount(DateTime from, DateTime to, int? merchantId = null, string type = "");

    Task<Dictionary<DateTime, long>>
        OpensDayCountByMerchantIds(DateTime from, DateTime to, List<int> merchantIds, string type = "");
    
    // Exposure day count methods (actual webshop ID counts)
    Task<Dictionary<DateTime, long>>
        ExposuresDayCountByMerchantIds(DateTime from, DateTime to, List<int> merchantIds, string type = "");
}