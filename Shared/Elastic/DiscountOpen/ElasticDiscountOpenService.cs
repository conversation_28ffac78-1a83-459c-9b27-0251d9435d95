using Nest;
using Serilog;
using Shared.Helpers.Converters;
using Shared.Models.Elastic.ElasticCampaignsMailsOpens;
using Shared.Models.Elastic.ElasticCustomerDiscounts;

namespace Shared.Elastic.DiscountOpen;

public class ElasticDiscountOpenService(ElasticClient elasticClient, ILogger logger) : IElasticDiscountOpenService
{
    private const string Index = "customers-discounts-events";
    private const string DefaultType = "discounts_list_viewed";

    public async Task<long> Opens(DateTime? from, DateTime? to, int? merchantId = null, string? email = null,
        string type = DefaultType)
    {
        try
        {
            if (from == null && to == null && merchantId == null)
            {
                return 0;
            }

            var queryBlock = Query(type, from, to, email, merchantId);

            return (await elasticClient.CountAsync<ElasticCustomerDiscounts>(s => s
                .Index(Index)
                .Query(queryBlock)
            )).Count;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error Opens");
            return -1;
        }
    }
    
    public async Task<long> OpensByMerchantIds(DateTime? from, DateTime? to, List<int>? merchantIds = null, string? email = null,
        string type = DefaultType)
    {
        try
        {
            if (from == null && to == null && merchantIds == null && email == null)
            {
                return 0;
            }

            var queryBlock = Query(type, from, to, email, null, merchantIds);

            return (await elasticClient.CountAsync<ElasticCustomerDiscounts>(s => s
                .Index(Index)
                .Query(queryBlock)
            )).Count;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error OpensByMerchantIds");
            return -1;
        }
    }

    public async Task<long> ExposuresByMerchantIds(DateTime? from, DateTime? to, List<int>? merchantIds = null, string? email = null,
        string type = DefaultType)
    {
        try
        {
            if (from == null && to == null && merchantIds == null && email == null)
            {
                return 0;
            }

            var queryBlock = Query(type, from, to, email, null, merchantIds);

            var opens = new List<ElasticCustomerDiscounts>();
            var searchResponse = await elasticClient.SearchAsync<ElasticCustomerDiscounts>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_event!.Webshop_id,
                            f => f.Customer.Email,
                            f => f.Event_received
                        )
                    )
                )
                .Index(Index)
                .Query(queryBlock)
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.Event_received))
                .Scroll("2m")
            );

            opens.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
                   searchResponse.Documents.Count != 0)
            {
                searchResponse =
                    await elasticClient.ScrollAsync<ElasticCustomerDiscounts>("2m", searchResponse.ScrollId);
                opens.AddRange(searchResponse.Documents);
            }

            await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));

            return opens.Sum(a => a.Shop_event.Webshop_id?.Count(id => !string.IsNullOrEmpty(id)) ?? 0);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error ExposuresByMerchantIds");
            return -1;
        }
    }

    public async Task<long> UniqueOpens(DateTime? from, DateTime? to, int? merchantId = null, string type = DefaultType)
    {
        try
        {
            if (from == null && to == null && merchantId == null)
            {
                return 0;
            }

            var queryBlock = Query(type, from, to, null, merchantId);

            CompositeKey afterKey = null;
            List<CompositeBucket> allBuckets = new List<CompositeBucket>();
            do
            {
                var searchResponse = await elasticClient.SearchAsync<ElasticCustomerDiscounts>(s => s
                    .Index(Index)
                    .Query(queryBlock
                    )
                    .Aggregations(a => a
                        .Composite("unique_emails", c => c
                                .Sources(so => so
                                    .Terms("email", t => t
                                        .Field(f => f.Customer.Email)))
                                .After(afterKey)
                                .Size(10000) // Set the batch size
                        )
                    )
                );
                var compositeAggregation = searchResponse.Aggregations.Composite("unique_emails");
                allBuckets.AddRange(compositeAggregation.Buckets);
                afterKey = compositeAggregation.AfterKey;
            } while (afterKey != null);

            return allBuckets.Count;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error UniqueOpens");
            return -1;
        }
    }

    public async Task<List<ElasticCustomerDiscounts>> OpensData(DateTime? from, DateTime? to, string? email,
        int? merchantId, string type = DefaultType)
    {
        try
        {
            if (from == null && to == null && to == null)
            {
                return new List<ElasticCustomerDiscounts>();
            }

            var queryBlock = Query(type, from, to, email, merchantId);

            var orders = new List<ElasticCustomerDiscounts>();
            var searchResponse = await elasticClient.SearchAsync<ElasticCustomerDiscounts>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_event!.Webshop_id,
                            f => f.Customer.Email,
                            f => f.Event_received
                        )
                    )
                )
                .Index(Index)
                .Query(queryBlock)
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.Event_received))
                .Scroll("2m")
            );

            orders.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
                   searchResponse.Documents.Count != 0)
            {
                searchResponse =
                    await elasticClient.ScrollAsync<ElasticCustomerDiscounts>("2m", searchResponse.ScrollId);
                orders.AddRange(searchResponse.Documents);
            }

            await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
            return orders.Where(a => a.Shop_event.Webshop_id.Count != 0).ToList();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error OpensData");
            return new List<ElasticCustomerDiscounts>();
        }
    }

    public async Task<List<ElasticCustomerDiscounts>> OpensDataMultipleEmails(DateTime? from, DateTime? to,
        List<string>? emails, int? merchantId, string type = DefaultType)
    {
        try
        {
            if (from == null && to == null && to == null)
            {
                return new List<ElasticCustomerDiscounts>();
            }

            var queryBlock = Query(type, from, to, null, merchantId);

            var orders = new List<ElasticCustomerDiscounts>();
            var searchResponse = await elasticClient.SearchAsync<ElasticCustomerDiscounts>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_event!.Webshop_id,
                            f => f.Customer.Email,
                            f => f.Event_received
                        )
                    )
                )
                .Index(Index)
                .Query(queryBlock)
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.Event_received))
                .Scroll("2m")
            );

            orders.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
                   searchResponse.Documents.Count != 0)
            {
                searchResponse =
                    await elasticClient.ScrollAsync<ElasticCustomerDiscounts>("2m", searchResponse.ScrollId);
                orders.AddRange(searchResponse.Documents);
            }

            await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
            return orders.Where(a => a.Shop_event.Webshop_id.Count != 0).ToList();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error OpensDataMultipleEmails");
            return new List<ElasticCustomerDiscounts>();
        }
    }

    public async Task<Dictionary<DateTime, long>> OpensDayCount(DateTime from, DateTime to, int? merchantId,
        string type = DefaultType)
    {
        try
        {
            var queryBlock = Query(type, from, to, null, merchantId);
            var response = await elasticClient.SearchAsync<ElasticCustomerDiscounts>(s => s
                .Index(Index)
                .Query(queryBlock)
                .Aggregations(a => a
                    .DateHistogram("emails_by_day", dh => dh
                        .Field(f => f.Event_received)
                        .CalendarInterval(DateInterval.Day)
                        .Aggregations(subAgg => merchantId.HasValue
                            ? subAgg.ValueCount("countData", vc => vc.Field(f => f.Shop_event.Webshop_id))
                            : null)
                    )
                )
            );

            var datas = response.Aggregations.DateHistogram("emails_by_day").Buckets
                .ToDictionary(
                    bucket => bucket.Date, // or bucket.Key.Date to get as DateTime
                    bucket => bucket.DocCount.Value
                );

            return datas;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error OpensDayCount");
            return new Dictionary<DateTime, long>();
        }
    }

    public async Task<Dictionary<DateTime, long>> OpensDayCountByMerchantIds(DateTime from, DateTime to, List<int> merchantIds, string type = "")
    {
        try
        {
            var queryBlock = Query(type, from, to, null, null, merchantIds);

            var response = await elasticClient.SearchAsync<ElasticCustomerDiscounts>(s => s
                .Index(Index)
                .Query(queryBlock)
                .Aggregations(a => a
                    .DateHistogram("emails_by_day", dh => dh
                        .Field(f => f.Event_received)
                        .CalendarInterval(DateInterval.Day)
                        .Aggregations(subAgg => 
                                merchantIds != null && merchantIds.Any()
                                    ? subAgg.ValueCount("countData", vc => vc.Field(f => f.Shop_event.Webshop_id))
                                    : null)
                    )
                )
            );

            var datas = response.Aggregations.DateHistogram("emails_by_day").Buckets
                .ToDictionary(
                    bucket => bucket.Date, // or bucket.Key.Date to get as DateTime
                    bucket => bucket.DocCount.Value
                );

            return datas;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error OpensDayCountByMerchantIds");
            return new Dictionary<DateTime, long>();
        }
    }

    public async Task<Dictionary<DateTime, long>> ExposuresDayCountByMerchantIds(DateTime from, DateTime to, List<int> merchantIds, string type = "")
    {
        try
        {
            var queryBlock = Query(type, from, to, null, null, merchantIds);

            var opens = new List<ElasticCustomerDiscounts>();
            var searchResponse = await elasticClient.SearchAsync<ElasticCustomerDiscounts>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_event!.Webshop_id,
                            f => f.Customer.Email,
                            f => f.Event_received
                        )
                    )
                )
                .Index(Index)
                .Query(queryBlock)
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.Event_received))
                .Scroll("2m")
            );

            opens.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
                   searchResponse.Documents.Count != 0)
            {
                searchResponse =
                    await elasticClient.ScrollAsync<ElasticCustomerDiscounts>("2m", searchResponse.ScrollId);
                opens.AddRange(searchResponse.Documents);
            }

            await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));

            // Group by date and count webshop IDs for each day
            var datas = opens
                .Where(a => a.Shop_event?.Webshop_id != null)
                .SelectMany(a => a.Shop_event.Webshop_id
                    .Where(id => !string.IsNullOrEmpty(id))
                    .Select(id => new { Date = a.Event_received.Date, WebshopId = id }))
                .GroupBy(x => x.Date)
                .ToDictionary(
                    group => group.Key,
                    group => (long)group.Count()
                );

            return datas;
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error ExposuresDayCountByMerchantIds");
            return new Dictionary<DateTime, long>();
        }
    }

    private Func<QueryContainerDescriptor<ElasticCustomerDiscounts>, QueryContainer>
        Query(string type, DateTime? from, DateTime? to, string? email = null, int? merchantId = null, List<int>? merchantIds = null)
    {
        QueryContainer QueryBlock(
            QueryContainerDescriptor<ElasticCustomerDiscounts> q)
        {
            QueryContainer query = new QueryContainer();
            if (to.HasValue)
            {
                to = DateTimeExtensions.SetTimeToMax(to.Value);
            }

            //Event type
            query &= q.Terms(t => t.Field(f => f.Discount.Event_type).Terms(type));

            // Check from date
            if (from.HasValue && to.HasValue)
            {
                query &= q.DateRange(dt => dt.Field(field => field.Event_received)
                    .GreaterThanOrEquals(from)
                    .LessThanOrEquals(to));
            }
            else if (from.HasValue)
            {
                query &= q.DateRange(dt => dt.Field(field => field.Event_received)
                    .GreaterThanOrEquals(from));
            }
            else if (to.HasValue)
            {
                query &= q.DateRange(dt => dt.Field(field => field.Event_received)
                    .LessThanOrEquals(to));
            }

            // Check for email
            if (!string.IsNullOrEmpty(email))
            {
                query &= q.Terms(t => t.Field(f => f.Customer.Email).Terms(email));
            }
            
            // Merchant IDs filter - Fix to exclude all results if the list is empty
            if (merchantIds != null)
            {
                if (merchantIds.Any())
                {
                    query &= q.Terms(t => t.Field(f => f.Shop_event.Webshop_id).Terms(merchantIds));
                }
                else
                {
                    // If merchantIds is empty, create a query that matches nothing
                    query &= q.Bool(b => b.MustNot(mn => mn.MatchAll()));
                }
            }

            // Check for merchant
            if (merchantId.HasValue && merchantId != 0)
            {
                query &= q.Terms(t => t.Field(f => f.Shop_event.Webshop_id).Terms(merchantId));
            }

            return query;
        }

        return QueryBlock;
    }
}