using Shared.Elastic.Models.ElasticShopProductsInteracts;
using Shared.Elastic.Models.ElasticShopProductsRedirects;

namespace Shared.Elastic.ShopProductsRedirects;

public interface IElasticShopProductRedirectsService
{
    Task<long> Redirects(DateTime? from, DateTime? to, int? merchantId = null, string? email = null);
    Task<long> UniqueRedirects(DateTime? from, DateTime? to, int? merchantId = null);

    Task<List<ElasticShopProductsRedirects>>
        RedirectsData(DateTime? from, DateTime? to, string? email, int? merchantId);

    Task<List<ElasticShopProductsRedirects>> RedirectsDataMultipleEmails(DateTime? from, DateTime? to,
        List<string>? emails, int? merchantId);

    Task<Dictionary<DateTime, long>> RedirectsDayCount(DateTime from, DateTime to, int? merchantId = null);
}