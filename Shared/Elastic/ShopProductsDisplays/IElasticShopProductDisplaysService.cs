using Shared.Dto.Campaign;
using Shared.Elastic.Models.ElasticShopProductsDisplays;

namespace Shared.Elastic.ShopProductsDisplays;

public interface IElasticShopProductDisplaysService
{
    Task<long> Displays(DateTime? from, DateTime? to, int? merchantId = null, string? email = null);
    Task<long> DisplaysByMerchantIds(DateTime? from, DateTime? to, List<int> merchantIds, string? email = null);
    
    // Exposure methods (actual merchant ID counts)
    Task<long> ExposuresByMerchantIds(DateTime? from, DateTime? to, List<int> merchantIds, string? email = null);
    
    Task<long> UniqueDisplays(DateTime? from, DateTime? to, int? merchantId = null);

    Task<List<ElasticShopProductsDisplays>> DisplaysData(DateTime? from, DateTime? to, string? email,
        int? merchantId);

    Task<List<ElasticShopProductsDisplays>> DisplaysDataMultipleEmails(DateTime? from, DateTime? to,
        List<string>? emails, int? merchantId);

    Task<Dictionary<DateTime, long>> DisplaysDayCount(DateTime from, DateTime to, int? merchantId = null);
    Task<Dictionary<DateTime, long>> DisplaysDayCountByMerchantIds(DateTime from, DateTime to, List<int> merchantIds);
    
    // Exposure day count methods (actual merchant ID counts)
    Task<Dictionary<DateTime, long>> ExposuresDayCountByMerchantIds(DateTime from, DateTime to, List<int> merchantIds);
}