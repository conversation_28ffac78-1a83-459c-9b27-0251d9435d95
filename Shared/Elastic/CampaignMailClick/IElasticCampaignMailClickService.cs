using Shared.Dto.Campaign;
using Shared.Models.Elastic.ElasticCampaignsMailsOpens;

namespace Shared.Elastic.CampaignMailClick;

public interface IElasticCampaignMailClickService
{
    Task<long> Clicks(DateTime? from, DateTime? to, int? merchantId = null, int? campaignId = null,
        string? email = null);
    Task<long> ClicksByCampaignIds(DateTime? from, DateTime? to, List<int> campaignIds);
    Task<long> UniqueClicks(DateTime? from, DateTime? to, int? merchantId = null, int? campaignId = null);
    Task<long> UniqueClicksByCampaignIds(DateTime? from, DateTime? to, List<int> campaignIds);

    Task<List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>> ClicksData(DateTime? from, DateTime? to,
        string? email, int? merchantId);

    Task<List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>> ClicksDataMultipleEmails(DateTime? from,
        DateTime? to, List<string>? emails, int? merchantId);

    Task<Dictionary<DateTime, long>> ClicksDayCount(DateTime from, DateTime to, int? merchantId = null);

    Task<List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>> ClicksDayCountData(DateTime from,
        DateTime to);

    Task<List<CampaignClickInsightDto>> ClicksCampaignInsight(int campaignId);
}