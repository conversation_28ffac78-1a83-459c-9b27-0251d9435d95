using Nest;
using Serilog;
using Shared.Dto.Campaign;
using Shared.Helpers.Converters;
using Shared.Models.Elastic.ElasticCampaignsMailsOpens;

namespace Shared.Elastic.CampaignMailClick;

public class ElasticCampaignMailClickService : IElasticCampaignMailClickService
{
    private readonly ElasticClient _elasticClient;
    private readonly ILogger _logger;
    private const string Index = "campaigns-mails-clicks";

    public ElasticCampaignMailClickService(ElasticClient elasticClient, ILogger logger)
    {
        _elasticClient = elasticClient;
        _logger = logger;
    }

    public async Task<long> Clicks(DateTime? from, DateTime? to, int? merchantId = null, int? campaignId = null,
        string? email = null)
    {
        try
        {
            if (from == null && to == null && merchantId == null && campaignId == null && email == null)
            {
                return 0;
            }

            var queryBlock = Query(from, to, email, merchantId, campaignId);
            return (await _elasticClient.CountAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(s =>
                s
                    .Index(Index)
                    .Query(queryBlock)
            )).Count;
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error Clicks");
            return -1;
        }
    }

    public async Task<long> ClicksByCampaignIds(DateTime? from, DateTime? to, List<int> campaignIds)
    {
        var queryBlock = Query(from, to, null, null, campaignIds: campaignIds);
        var response = await _elasticClient.CountAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(s => s
            .Index(Index)
            .Query(queryBlock)
        );

        return response.Count;
    }

    public async Task<long> UniqueClicks(DateTime? from, DateTime? to, int? merchantId = null, int? campaignId = null)
    {
        try
        {
            if (from == null && to == null && merchantId == null && campaignId == null)
            {
                return 0;
            }

            var queryBlock = Query(from, to, null, merchantId, campaignId);
            CompositeKey afterKey = null;
            List<CompositeBucket> allBuckets = new List<CompositeBucket>();
            do
            {
                var searchResponse =
                    await _elasticClient.SearchAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(
                        s => s
                            .Index(Index)
                            .Query(queryBlock)
                            .Aggregations(a => a
                                .Composite("unique_emails", c => c
                                        .Sources(so => so
                                            .Terms("email", t => t
                                                .Field(f => f.Customer.Email)))
                                        .After(afterKey)
                                        .Size(10000) // Set the batch size
                                )
                            )
                    );
                var compositeAggregation = searchResponse.Aggregations.Composite("unique_emails");
                allBuckets.AddRange(compositeAggregation.Buckets);
                afterKey = compositeAggregation.AfterKey;
            } while (afterKey != null);

            return allBuckets.Count;
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error UniqueClicks");
            return -1;
        }
    }

    public async Task<long> UniqueClicksByCampaignIds(DateTime? from, DateTime? to, List<int> campaignIds)
    {
        var queryBlock = Query(from, to, null, null, campaignIds);
        var response = await _elasticClient.CountAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(s => s
            .Index(Index)
            .Query(queryBlock)
        );

        return response.Count;
    }

    public async Task<List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>> ClicksData(DateTime? from,
        DateTime? to, string? email, int? merchantId)
    {
        try
        {
            if (from == null && to == null && email == null && merchantId == null)
            {
                return new List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>();
            }

            var queryBlock = Query(from, to, email, merchantId);

            var orders = new List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>();
            var searchResponse =
                await _elasticClient.SearchAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(s => s
                    .Source(sf => sf
                        .Includes(i => i
                            .Fields(
                                f => f.Shop_event!.Webshop_id,
                                f => f.Customer.Email,
                                f => f.Customer.Campaign_Id,
                                f => f.EventObject.Created
                            )
                        )
                    )
                    .Index(Index)
                    .Query(queryBlock)
                    .Size(10_000)
                    .Sort(srt => srt.Descending(f => f.EventObject.Created))
                    .Scroll("2m")
                );

            orders.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
                   searchResponse.Documents.Count != 0)
            {
                searchResponse =
                    await _elasticClient.ScrollAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(
                        "2m",
                        searchResponse.ScrollId);
                orders.AddRange(searchResponse.Documents);
            }

            await _elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
            return orders.Where(a => !string.IsNullOrEmpty(a.Shop_event.Webshop_id)).ToList();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error ClicksData");
            return new List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>();
        }
    }

    public async Task<List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>> ClicksDataMultipleEmails(
        DateTime? from, DateTime? to, List<string>? emails, int? merchantId)
    {
        try
        {
            if (from == null && to == null && (emails == null || !emails.Any()) && merchantId == null)
            {
                return new List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>();
            }

            var queryBlock = Query(from, to, emails, merchantId);

            var orders = new List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>();
            var searchResponse =
                await _elasticClient.SearchAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(s => s
                    .Source(sf => sf
                        .Includes(i => i
                            .Fields(
                                f => f.Shop_event!.Webshop_id,
                                f => f.Customer.Email,
                                f => f.Customer.Campaign_Id,
                                f => f.EventObject.Created
                            )
                        )
                    )
                    .Index(Index)
                    .Query(queryBlock)
                    .Size(10_000)
                    .Sort(srt => srt.Descending(f => f.EventObject.Created))
                    .Scroll("2m")
                );

            orders.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
                   searchResponse.Documents.Count != 0)
            {
                searchResponse =
                    await _elasticClient.ScrollAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(
                        "2m", searchResponse.ScrollId);
                orders.AddRange(searchResponse.Documents);
            }

            await _elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
            return orders.Where(a => !string.IsNullOrEmpty(a.Shop_event.Webshop_id)).ToList();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error ClicksData");
            return new List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>();
        }
    }

    private Func<QueryContainerDescriptor<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>,
        QueryContainer> Query(DateTime? from, DateTime? to, List<string>? emails, int? merchantId, List<int>? campaignIds = null, bool isMultipleEmails = false)
    {
        return q =>
        {
            var filters =
                new List<Func<QueryContainerDescriptor<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>,
                    QueryContainer>>();

            if (from.HasValue)
            {
                filters.Add(
                    f => f.DateRange(r => r.Field(ff => ff.EventObject.Created).GreaterThanOrEquals(from.Value)));
            }

            if (to.HasValue)
            {
                filters.Add(f => f.DateRange(r => r.Field(ff => ff.EventObject.Created).LessThanOrEquals(to.Value)));
            }

            if (emails != null && emails.Any())
            {
                filters.Add(f => f.Terms(t => t.Field(ff => ff.Customer.Email).Terms(emails)));
            }

            if (merchantId.HasValue)
            {
                filters.Add(f =>
                    f.Term(t => t.Field(ff => ff.Shop_event!.Webshop_id).Value(merchantId.Value.ToString())));
            }

            if (campaignIds != null && campaignIds.Any())
            {
                filters.Add(f => f.Terms(t => t.Field(ff => ff.Customer.Campaign_Id).Terms(campaignIds)));
            }

            return q.Bool(b => b.Must(filters));
        };
    }

    public async Task<Dictionary<DateTime, long>> ClicksDayCount(DateTime from, DateTime to, int? merchantId)
    {
        try
        {
            var queryBlock = Query(from, to, null, merchantId, null);
            var response =
                await _elasticClient.SearchAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(
                    s => s
                        .Index(Index)
                        .Query(queryBlock)
                        .Aggregations(a => a
                            .DateHistogram("emails_by_day", dh => dh
                                .Field(f => f.EventObject.Created)
                                .CalendarInterval(DateInterval.Day)
                                .Aggregations(subAgg => merchantId.HasValue
                                    ? subAgg.ValueCount("countData", vc => vc.Field(f => f.Shop_event.Webshop_id))
                                    : null)
                            )
                        )
                );

            var emailsByDay = response.Aggregations.DateHistogram("emails_by_day").Buckets
                .ToDictionary(
                    bucket => bucket.Date, // or bucket.Key.Date to get as DateTime
                    bucket => bucket.DocCount.Value
                );

            return emailsByDay;
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error ClicksDayCount");
            return new Dictionary<DateTime, long>();
        }
    }

    public async Task<List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>> ClicksDayCountData(
        DateTime from, DateTime to)
    {
        try
        {
            var queryBlock = Query(from, to);
            var uniqueClicks = new List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>();
            CompositeKey? afterKey = null;
            do
            {
                var searchResponse =
                    await _elasticClient.SearchAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(
                        s => s
                            .Index(Index)
                            .Query(queryBlock)
                            .Aggregations(a => a
                                .Composite("group_by_emailGuid", c => c
                                    .Sources(src => src
                                        .Terms("emailGuid", t => t.Field(f => f.Customer.Email_guid))
                                    )
                                    .Size(10000) // Use this to paginate through your results
                                    .After(afterKey) // Use the afterKey for pagination
                                    .Aggregations(aa => aa
                                        .TopHits("top_hit", th => th
                                            .Size(1)
                                        )
                                    )
                                )
                            )
                    );

                // Handle results
                var compositeAgg = searchResponse.Aggregations.Composite("group_by_emailGuid");
                if (compositeAgg != null)
                {
                    foreach (var bucket in compositeAgg.Buckets)
                    {
                        var topHit = bucket.TopHits("top_hit");
                        var doc = topHit.Documents<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>()
                            .First();
                        uniqueClicks.Add(doc);
                    }
                }

                afterKey = compositeAgg?.AfterKey ?? null;
            } while (afterKey != null);

            return uniqueClicks;
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error ClicksDayCountData");
            return new List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>();
        }
    }

    public async Task<List<CampaignClickInsightDto>> ClicksCampaignInsight(int campaignId)
    {
        try
        {
            var queryBlock = Query(null, null, null, null, campaignId);
            var searchResponse =
                await _elasticClient.SearchAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(s => s
                    .Index(Index)
                    .Query(queryBlock)
                    .Aggregations(a => a
                        .Terms("group_by_block_guid", ag => ag
                            .Field(f => f.Shop_event.Block_guid)
                            .Aggregations(aag => aag
                                .Terms("group_by_block_index", aag2 => aag2
                                    .Field(f => f.Shop_event.Block_index)
                                    .Aggregations(aag3 => aag3
                                        .Cardinality("unique_email_count", c => c
                                            .Field(f => f.Customer.Email_guid)
                                        )
                                    )
                                )
                            )
                        )
                    )
                );


            var campaignClickInsight = new List<CampaignClickInsightDto>();
            var blockGuidAgg = searchResponse.Aggregations.Terms("group_by_block_guid");
            foreach (var blockGuidBucket in blockGuidAgg.Buckets)
            {
                var blockIndexAgg = blockGuidBucket.Terms("group_by_block_index");
                foreach (var blockIndexBucket in blockIndexAgg.Buckets)
                {
                    var uniqueEmailCount = blockIndexBucket.Cardinality("unique_email_count");

                    campaignClickInsight.Add(new CampaignClickInsightDto
                    {
                        BlockGuid = blockGuidBucket.Key,
                        BlockIndex = blockIndexBucket.Key,
                        Clicks = blockIndexBucket.DocCount ?? 0,
                        UniqueClicks = Convert.ToInt64(uniqueEmailCount.Value)
                    });
                }
            }

            return campaignClickInsight;
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error ClicksCampaignInsigt");
            return new List<CampaignClickInsightDto>();
        }
    }

    private
        Func<QueryContainerDescriptor<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>, QueryContainer>
        Query(DateTime? from, DateTime? to, string? email = null, int? merchantId = null, int? campaignId = null)
    {
        QueryContainer QueryBlock(
            QueryContainerDescriptor<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks> q)
        {
            QueryContainer query = new QueryContainer();
            if (to.HasValue)
            {
                to = DateTimeExtensions.SetTimeToMax(to.Value);
            }

            // Check from date
            if (from.HasValue && to.HasValue)
            {
                query &= q.DateRange(dt => dt.Field(field => field.EventObject.Created)
                    .GreaterThanOrEquals(from)
                    .LessThanOrEquals(to));
            }
            else if (from.HasValue)
            {
                query &= q.DateRange(dt => dt.Field(field => field.EventObject.Created)
                    .GreaterThanOrEquals(from));
            }
            else if (to.HasValue)
            {
                query &= q.DateRange(dt => dt.Field(field => field.EventObject.Created)
                    .LessThanOrEquals(to));
            }

            // Check for email
            if (!string.IsNullOrEmpty(email))
            {
                query &= q.Terms(t => t.Field(f => f.Customer.Email).Terms(email));
            }

            // Check for merchant
            if (merchantId.HasValue && merchantId != 0)
            {
                query &= q.Terms(t => t.Field(f => f.Shop_event.Webshop_id).Terms(merchantId));
            }

            // Check for campaign
            if (campaignId.HasValue)
            {
                query &= q.Terms(t => t.Field(f => f.Customer.Campaign_Id).Terms(campaignId));
            }

            return query;
        }

        return QueryBlock;
    }
}