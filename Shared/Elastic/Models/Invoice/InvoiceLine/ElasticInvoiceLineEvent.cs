using Nest;

namespace Shared.Elastic.Invoice.InvoiceLine;

public class ElasticInvoiceLineEvent
{
    [Keyword(Name = "Id")] public string Id { get; set; }
    [Keyword(Name = "Order_id")] public string Order_id { get; set; }
    [Keyword(Name = "Email")] public string Email { get; set; }
    [Date(Name = "Exposure_date")] public DateTime Exposure_date { get; set; }
    [Date(Name = "Order_date")] public DateTime Order_date { get; set; }
    [Keyword(Name = "Exposure_trigger")] public string Exposure_trigger { get; set; }
    [Keyword(Name = "Webshop_id")] public string Webshop_id { get; set; }
    [Keyword(Name = "Webshop_name")] public string Webshop_name { get; set; }
    [Keyword(Name = "Customer_name")] public string Customer_name { get; set; }
    [Keyword(Name = "ViaAds_status")] public string ViaAds_status { get; set; }
    [Keyword(Name = "Action_type")] public string Action_type { get; set; }
    [Boolean(Name = "Refund")] public bool Refund { get; set; }
    [Keyword(Name = "Ref_id")] public string Ref_id { get; set; }
    [Number(Name = "Total_price")] public decimal Total_price { get; set; }
    [Number(Name = "Total_price_tax")] public decimal Total_price_tax { get; set; }

    [Number(Name = "Webshop_payment_percentage")]
    public decimal Webshop_payment_percentage { get; set; }

    [Number(Name = "Total_cut")] public decimal Total_cut { get; set; }
    [Boolean(Name = "Email_engaged")] public bool Email_engaged { get; set; }
    [Keyword(Name = "Gender")] public string Gender { get; set; }
    [Keyword(Name = "Age")] public int? Age { get; set; }
}