using Nest;
using Shared.Elastic.Invoice.InvoiceLine.Update;

namespace Shared.Elastic.Invoice.InvoiceLine;

public class ElasticInvoiceLineMasterUpdateEvent1
{
    //[Date(Name = "@timestamp")] public DateTime TimeStamp { get; set; }
    //[Object(Name = "Invoice")] public ElasticInvoiceLineHeadEvent Invoice { get; set; }
    [Object(Name = "Invoice_line")] public ElasticInvoiceLineUpdateEvent Invoice_line { get; set; }
}