using Nest;
using Shared.Elastic.Models;

namespace Shared.Elastic.Invoice.InvoiceLine;

public class ElasticInvoiceLineMasterEvent
{
    [Date(Name = "@timestamp")] public DateTime TimeStamp { get; set; }
    [Object(Name = "Invoice")] public ElasticInvoiceLineHeadEvent Invoice { get; set; }
    [Object(Name = "Invoice_line")] public ElasticInvoiceLineEvent Invoice_line { get; set; }
    [Object(Name = "Partner")] public ElasticPartner Partner { get; set; }
    [Object(Name = "merchant")] public ElasticMerchant Merchant { get; set; }
}