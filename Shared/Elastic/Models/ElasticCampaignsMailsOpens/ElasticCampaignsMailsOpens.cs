using Nest;
using Shared.Models.Elastic.ElasticCampaignsMailsOpens;

namespace Shared.Elastic.Models.ElasticCampaignsMailsOpens;

public class ElasticCampaignsMailsOpens
{
    [PropertyName("Shop_event")] public ElasticCampaignsMailsOpensShopEvent Shop_event { get; set; }
    [PropertyName("Customer")] public ElasticCampaignsMailsOpensCustomer Customer { get; set; }
    [PropertyName("event")] public ElasticCampaignsMailsOpensEvent EventObject { get; set; }
    [Date(Name = "@timestamp")] public DateTime timestamp { get; set; }
    [PropertyName("Partner")] public ElasticPartner Partner { get; set; }

    public DateTime
        Event_received
    {
        get;
        set;
    } //Når refrakturere til ny elastic i fremtiden kald det timestamp eller noget fælles alle steder

    public ElasticCampaignsMailsOpensClient? client { get; set; }
}