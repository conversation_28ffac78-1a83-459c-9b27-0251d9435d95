using Nest;

namespace Shared.Elastic.Models.ElasticShopProductsRedirects;

public class ElasticShopProductsRedirects
{
    [PropertyName("@timestamp")] public DateTime Action_date { get; set; }
    public DateTime Event_received { get; set; }
    [PropertyName("Product")] public ElasticProduct Product { get; set; }
    [PropertyName("Merchant")] public ElasticMerchant Merchant { get; set; }
    [PropertyName("Marketing")] public ElasticMarketing Marketing { get; set; }
    [PropertyName("Customer")] public ElasticCustomer Customer { get; set; }
    [PropertyName("search")] public ElasticSearch Search { get; set; }
}