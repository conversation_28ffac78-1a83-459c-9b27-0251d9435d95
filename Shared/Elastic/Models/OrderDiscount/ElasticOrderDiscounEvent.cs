// ReSharper disable All

using Integration.Models.Elastic.Order;
using Nest;
using Shared.Elastic.Order;
using Shared.Elastic.OrderDiscount;

namespace Marlin_OS_Integration_API.Models.Order;

public class ElasticOrderDiscountEvent
{
    [Date(Name = "@timestamp")] public DateTime Order_date { get; set; }
    public DateTime Event_received { get; set; }
    [Object(Name = "Shop_order")] public ElasticOrderDiscountEventShopOrder? Shop_order { get; set; }
    [Object(Name = "Customer")] public ElasticOrderDiscountEventCustomer? Customer { get; set; }
    [Object(Name = "Discount")] public ElasticOrderDiscountEventDiscount? Discount { get; set; }
}