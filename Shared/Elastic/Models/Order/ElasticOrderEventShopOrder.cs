// ReSharper disable All

using Nest;
using Shared.Elastic.Order;

namespace Integration.Models.Elastic.Order;

public class ElasticOrderEventShopOrder

{
    [Date(Name = "Last_modified")] public DateTime? Last_modified { get; set; }
    [Keyword(Name = "Campaign_id")] public string? Campaign_id { get; set; }
    [Keyword(Name = "Webshop_id")] public string? Webshop_id { get; set; }
    [Keyword(Name = "Webshop_name")] public string? Webshop_name { get; set; }

    [Keyword(Name = "Last_Order_numbermodified")]
    public string? Order_number { get; set; }

    [Keyword(Name = "Order_number")] public string? Order_number_recevice { get; set; }
    [Keyword(Name = "Currency")] public string? Currency { get; set; }
    [Keyword(Name = "Source_name")] public string? Source_name { get; set; }
    [Keyword(Name = "Status")] public string? Status { get; set; }
    [Number(Name = "Vat_percentage")] public decimal? Vat_percentage { get; set; }
    [Boolean(Name = "IsCanceled")] public bool? IsCanceled { get; set; }

    [Number(Name = "Total_price_shipping")]
    public decimal Total_price_shipping { get; set; }

    [Number(Name = "Total_price")] public decimal Total_price { get; set; }
    [Number(Name = "Total_price_tax")] public decimal Total_price_tax { get; set; }

    [Number(Name = "Total_price_tax_included")]
    public decimal? Total_price_tax_included { get; set; }

    public List<ElasticOrderEventItem>? Order_items { get; set; }
    [Object(Name = "Billing_address")] public ElasticOrderEventAddress? Billing_address { get; set; }
    [Object(Name = "Shipping_address")] public ElasticOrderEventAddressShipping? Shipping_address { get; set; }
}