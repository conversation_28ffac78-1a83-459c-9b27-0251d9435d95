// ReSharper disable All

using Integration.Models.Elastic.Order;
using Nest;
using Shared.Elastic.Order;

namespace Marlin_OS_Integration_API.Models.Order;

public class ElasticOrderEvent
{
    [Date(Name = "@timestamp")] public DateTime Order_date { get; set; }
    public DateTime Event_received { get; set; }
    [Object(Name = "Shop_order")] public ElasticOrderEventShopOrder? Shop_order { get; set; }
    [Object(Name = "client")] public ElasticOrderEventClient? client { get; set; }
    [Object(Name = "user_agent")] public ElasticOrderEventUserAgent? user_agent { get; set; }
    [Object(Name = "Customer")] public ElasticOrderEventCustomer? Customer { get; set; }
    [Object(Name = "Plugin")] public ElasticOrderEventPlugin? Plugin { get; set; }
    [Object(Name = "Discounts")] public List<ElasticOrderEventDiscount>? Discounts { get; set; }
    [Object(Name = "Partner")] public ElasticOrderEventPartner? Partner { get; set; }
}