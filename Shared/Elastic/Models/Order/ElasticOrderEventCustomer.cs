using Nest;

namespace Shared.Elastic.Order;

public class ElasticOrderEventCustomer
{
    [Keyword(Name = "Email")] public string? Email { get; set; }
    [Keyword(Name = "Email2")] public string? Email2 { get; set; }
    [Keyword(Name = "Session_id")] public string? Session_id { get; set; }
    [Keyword(Name = "Email_guid")] public string? Email_guid { get; set; }
    [Keyword(Name = "Email_guid2")] public string? Email_guid2 { get; set; }
}