using Nest;
using Shared.Elastic.ElasticCampaignsMailsClicks;

namespace Shared.Elastic.Models.ElasticCampaignsMailsClicks;

public class ElasticCampaignsMailsClicks
{
    [PropertyName("Shop_event")] public ElasticCampaignsMailsClicksShopEvent Shop_event { get; set; }
    [PropertyName("Customer")] public ElasticCampaignsMailsClicksCustomer Customer { get; set; }
    [PropertyName("event")] public ElasticCampaignsMailsClicksEvent EventObject { get; set; }
    [Date(Name = "@timestamp")] public DateTime Event_received { get; set; }
}