// ReSharper disable All

using Integration.Models.Elastic.Order;
using Nest;
using Shared.Elastic.Order;
using Shared.Elastic.OrderTransformMerchants;

namespace Marlin_OS_Integration_API.Models.Order;

public class ElasticOrderTransformMerchantTransformMerchantEvent
{
    [Object(Name = "Shop_order")] public ElasticOrderTransformMerchantEventShopOrder? Shop_order { get; set; }
    [Object(Name = "Customer")] public ElasticOrderTransformMerchantEventCustomer? Customer { get; set; }
}