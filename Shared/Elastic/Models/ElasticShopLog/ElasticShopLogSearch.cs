using Nest;

namespace Shared.Elastic.Models.ElasticShopLog;

public class ElasticShopLogSearch
{
    [PropertyName("Id")] public string Id { get; set; }
    [PropertyName("Query")] public string Query { get; set; }
    [PropertyName("Query_negative")] public string Query_negative { get; set; }
    [PropertyName("Merchant_filter_ids")] public List<string> Merchant_filter_ids { get; set; }
    [PropertyName("Page")] public int Page { get; set; }
    [PropertyName("Hits")] public int Hits { get; set; }
    [PropertyName("Total_hits")] public long Total_hits { get; set; }
}