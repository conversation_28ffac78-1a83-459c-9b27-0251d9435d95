using Nest;

namespace Shared.Elastic.Discounts;

public class DiscountElastic
{
    [Object(Name = "Shop_event")] public DiscountShopEventElastic Shop_event { get; set; }
    [Object(Name = "Customer")] public DiscountCustomerElastic Customer { get; set; }
    [Object(Name = "Discount")] public DiscountDiscountElastic Discount { get; set; }
    [Date(Name = "@timestamp")] public DateTime TimeStamp { get; set; }
}