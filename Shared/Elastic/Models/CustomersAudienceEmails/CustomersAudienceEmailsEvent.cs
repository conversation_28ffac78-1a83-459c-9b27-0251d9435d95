using Integration.Models.Elastic.Behavior;
using Nest;
using Shared.Elastic.Models;

namespace Shared.Elastic.CustomersAudienceEmails;

public class CustomersAudienceEmailsEvent
{
    [Keyword(Name = "@timestamp")] public DateTime Event_received { get; set; }
    [Object(Name = "Contacts")] public CustomersAudienceEmailsEventContact Contacts { get; set; }
    [Object(Name = "Partner")] public ElasticPartner Partner { get; set; }
}