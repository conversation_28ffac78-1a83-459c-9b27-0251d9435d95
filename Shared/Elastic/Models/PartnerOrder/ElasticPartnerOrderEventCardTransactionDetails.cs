// ReSharper disable All

using Nest;

namespace Marlin_OS_Integration_API.Models.Order;

public class ElasticPartnerOrderEventCardTransactionDetails
{
    // TODO - Add Partner Id 
    
    [Keyword(Name = "Authorization_method")]
    public string? Authorization_method { get; set; }

    [Keyword(Name = "Merchant_name")] public string? Merchant_name { get; set; }
    [Keyword(Name = "Merchant_category")] public string? Merchant_category { get; set; }
}