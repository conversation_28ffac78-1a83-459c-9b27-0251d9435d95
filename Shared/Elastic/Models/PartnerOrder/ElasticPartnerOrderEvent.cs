// ReSharper disable All

using Integration.Models.Elastic.Order;
using Nest;
using Shared.Elastic.Models.PartnerOrder;
using Shared.Elastic.Order;

namespace Marlin_OS_Integration_API.Models.Order;

public class ElasticPartnerOrderEvent
{
    [Date(Name = "@timestamp")] public DateTime Order_date { get; set; }
    [Object(Name = "event")] public ElasticEvent Event { get; set; }
    [Object(Name = "Shop_order")] public ElasticPartnerOrderEventShopOrder? Shop_order { get; set; }
    [Object(Name = "Customer")] public ElasticPartnerOrderEventCustomer? Customer { get; set; }
    [Object(Name = "File")] public ElasticPartnerOrderEventFileOrder? File { get; set; }

    [Object(Name = "Card_transaction_details")]
    public ElasticPartnerOrderEventCardTransactionDetails? Card_transaction_details { get; set; }
    
    [Object(Name = "Partner")]
    public ElasticPartnerOrderEventPartner? Partner { get; set; }
}