// ReSharper disable All

using Integration.Models.Elastic.Order;
using Nest;
using Shared.Elastic.Order;

namespace Marlin_OS_Integration_API.Models.Order;

public class ElasticViabillOrderEventUpdate
{
    [Keyword(Name = "id")] public string? id { get; set; }
    [Object(Name = "Shop_order")] public ElasticViabillOrderEventShopOrderUpdate? Shop_order { get; set; }
}

public class ElasticViabillOrderEventShopOrderUpdate
{
    [Keyword(Name = "Webshop_id")] public string? Webshop_id { get; set; }
}