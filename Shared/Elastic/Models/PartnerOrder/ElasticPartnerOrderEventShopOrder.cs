// ReSharper disable All

using Integration.Models.Elastic.Order;
using Nest;
using Shared.Elastic.Order;

namespace Marlin_OS_Integration_API.Models.Order;

public class ElasticPartnerOrderEventShopOrder
{
    [Date(Name = "Last_modified")] public DateTime? Last_modified { get; set; }
    [Keyword(Name = "Webshop_id")] public string? Webshop_id { get; set; }

    [Keyword(Name = "Merchant_account_id")]
    public string? Merchant_account_id { get; set; }

    [Keyword(Name = "Webshop_store_id")] public string? Webshop_store_id { get; set; }
    [Keyword(Name = "Order_number")] public string? Order_number { get; set; }
    [Keyword(Name = "Currency")] public string? Currency { get; set; }
    [Keyword(Name = "Status")] public string? Status { get; set; }
    [Keyword(Name = "Transaction")] public string? Transaction { get; set; }
    [Boolean(Name = "IsCanceled")] public bool? IsCanceled { get; set; }

    [Number(Name = "Total_price_tax_included")]
    public decimal? Total_price_tax_included { get; set; }

    [Number(Name = "Total_price")] public decimal? Total_price { get; set; }
}