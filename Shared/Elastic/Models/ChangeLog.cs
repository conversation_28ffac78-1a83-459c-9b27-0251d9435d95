using Nest;

namespace Shared.Elastic;

public class ChangeLog
{
    [Date(Name = "@timestamp")] public DateTime Event_date { get; set; }
    [Object(Name = "user")] public User user { get; set; }
    [Object(Name = "event")] public Event Event { get; set; }
    [Object(Name = "data_stream")] public DataStream data_stream { get; set; }
}

public class User
{
    [Keyword(Name = "email")] public string email { get; set; }
}

public class Event
{
    [Keyword(Name = "action")] public string action { get; set; }
    [Keyword(Name = "Table")] public string Table { get; set; }
    [Keyword(Name = "Primary_key")] public string Primary_key { get; set; }
    [Object(Name = "Values")] public List<Values> Values { get; set; }
}

public class Values
{
    [Keyword(Name = "Name")] public string Name { get; set; }
    [MatchOnlyText(Name = "New")] public string New { get; set; }
    [MatchOnlyText(Name = "Old")] public string Old { get; set; }
}

public class DataStream
{
    [ConstantKeyword(Name = "type")] public string type { get; set; }
    [ConstantKeyword(Name = "dataset")] public string dataset { get; set; }
}