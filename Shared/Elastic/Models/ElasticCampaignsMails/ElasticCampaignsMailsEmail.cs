using Nest;

namespace Shared.Models.Elastic.EmailSent;

public class ElasticCampaignsMailsEmail
{
    [Keyword(Name = "Email_guid")] public string? Email_guid { get; set; }
    [Keyword(Name = "Webshop_id")] public string? Webshop_id { get; set; }
    [Keyword(Name = "Message_id")] public string? Message_id { get; set; }
    [Keyword(Name = "Action_date")] public DateTime? Action_date { get; set; }
    [Keyword(Name = "Campaign_id")] public string? Campaign_id { get; set; }
}