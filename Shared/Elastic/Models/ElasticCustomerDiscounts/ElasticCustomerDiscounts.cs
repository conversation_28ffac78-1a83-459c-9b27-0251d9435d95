using Nest;
using Shared.Elastic.Models;

namespace Shared.Models.Elastic.ElasticCustomerDiscounts;

public class ElasticCustomerDiscounts
{
    [Date(Name = "@timestamp")] public DateTime Event_received { get; set; }

    [Object(Name = "Discount")] public ElasticCustomerDiscountDiscount Discount { get; set; }

    [Object(Name = "Customer")] public ElasticCustomerDiscountCustomer Customer { get; set; }

    [Object(Name = "Shop_event")] public ElasticCustomerDiscountShopEvent Shop_event { get; set; }
    
    [Object(Name = "Parter")] public ElasticPartner Partner { get; set; }
}