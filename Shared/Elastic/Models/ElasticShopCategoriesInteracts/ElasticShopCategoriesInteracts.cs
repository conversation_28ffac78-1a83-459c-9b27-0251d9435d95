using Nest;

namespace Shared.Elastic.Models.ElasticShopCategoriesInteracts;

public class ElasticShopCategoriesInteracts
{
    [PropertyName("@timestamp")] public DateTime Action_date { get; set; }
    public DateTime Event_received { get; set; }
    [PropertyName("Category")] public ElasticShopCategoriesInteractsCategory Category { get; set; }
    [PropertyName("Marketing")] public ElasticMarketing Marketing { get; set; }
    [PropertyName("Customer")] public ElasticShopCategoriesInteractsCustomer Customer { get; set; }
    [PropertyName("search")] public ElasticSearch Search { get; set; }
}