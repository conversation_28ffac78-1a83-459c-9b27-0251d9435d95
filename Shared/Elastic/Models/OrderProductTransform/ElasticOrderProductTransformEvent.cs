// ReSharper disable All

using Integration.Models.Elastic.Order;
using Nest;
using Shared.Elastic.Order;
using Shared.Elastic.OrderTransformMerchants;

namespace Marlin_OS_Integration_API.Models.Order;

public class ElasticOrderProductTransformEvent
{
    [PropertyName("@timestamp")] public DateTime Action_date { get; set; }
    [Object(Name = "Merchant")] public ElasticOrderProductTransformMerchantEvent? Merchant { get; set; }
    [Object(Name = "Order")] public ElasticOrderProductTransformOrderEvent? Order { get; set; }
}