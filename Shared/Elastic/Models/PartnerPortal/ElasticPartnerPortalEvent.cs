// ReSharper disable All

using Integration.Models.Elastic.Order;
using Nest;
using Shared.Elastic.Order;
using Shared.Elastic.OrderDiscount;
using Shared.Elastic.PartnerPortal;

namespace Marlin_OS_Integration_API.Models.Order;

public class ElasticPartnerPortalEvent
{
    [Date(Name = "@timestamp")] public DateTime Created_date { get; set; }
    [Object(Name = "event")] public ElasticPartnerPortalEventEvent? Event { get; set; }
    [Object(Name = "user")] public ElasticPartnerPortalEventUser? user { get; set; }
}