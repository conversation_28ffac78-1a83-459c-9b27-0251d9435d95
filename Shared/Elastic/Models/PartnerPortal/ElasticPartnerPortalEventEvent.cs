// ReSharper disable All

using Nest;
using Shared.Elastic.Order;

namespace Integration.Models.Elastic.Order;

public class ElasticPartnerPortalEventEvent

{
    [Keyword(Name = "Webshop_id")] public string? Webshop_id { get; set; }
    [Keyword(Name = "Webshop_name")] public string? Webshop_name { get; set; }
    [Keyword(Name = "type")] public string? type { get; set; }
    [Keyword(Name = "action")] public string? action { get; set; }
}