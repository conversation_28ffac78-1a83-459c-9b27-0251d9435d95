using Integration.Models.Elastic.Behavior;
using Nest;
using Shared.Elastic.Behavior;

namespace Shared.Elastic.Models.Behavior;

public class ElasticBehaviorEvent
{
    [Keyword(Name = "Event_date")] public DateTime Event_date { get; set; }
    [Keyword(Name = "@timestamp")] public DateTime Event_received { get; set; }
    [Object(Name = "client")] public ElasticBehaviorEventClient? client { get; set; }
    [Object(Name = "url")] public ElasticBehaviorEventUrl? url { get; set; }
    [Object(Name = "user_agent")] public ElasticBehaviorEventUserAgent? user_agent { get; set; }
    [Object(Name = "Customer")] public ElasticBehaviorEventCustomer? Customer { get; set; }
    [Object(Name = "Shop_event")] public ElasticBehaviorEventShopEvent Shop_event { get; set; }
    [Object(Name = "Plugin")] public ElasticBehaviorEventPlugin? Plugin { get; set; }
}