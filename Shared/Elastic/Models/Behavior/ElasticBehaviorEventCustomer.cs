using Nest;

namespace Shared.Elastic.Behavior;

public class ElasticBehaviorEventCustomer
{
    [Keyword(Name = "Email")] public string? Email { get; set; }
    [Keyword(Name = "Email2")] public string? Email2 { get; set; }
    [Keyword(Name = "Session_id")] public string? Session_id { get; set; }
    [Keyword(Name = "ViaAds")] public string? ViaAds { get; set; }
    [Keyword(Name = "ViaAds2")] public string? ViaAds2 { get; set; }
}