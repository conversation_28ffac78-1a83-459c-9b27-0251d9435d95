using Nest;

namespace Shared.Elastic.Behavior;

public class ElasticBehaviorEventShopEvent
{
    [Keyword(Name = "Event_type")] public string? Event_type { get; set; }
    [Keyword(Name = "Webshop_id")] public string? Webshop_id { get; set; }
    [Keyword(Name = "Webshop_name")] public string? Webshop_name { get; set; }
    [Keyword(Name = "Product_sku")] public string? Product_sku { get; set; }
    [Keyword(Name = "Product_id")] public string? Product_id { get; set; }
    [Keyword(Name = "Product_variant_id")] public string? Product_variant_id { get; set; }

    [Keyword(Name = "Product_internal_id")]
    public string? Product_internal_id { get; set; }

    [Keyword(Name = "Variant_internal_id")]
    public string? Variant_internal_id { get; set; }

    [Keyword(Name = "Price")] public decimal? Price { get; set; }
    [Keyword(Name = "Price_range")] public string? Price_range { get; set; }
}