using System.Text.Json.Serialization;

namespace Shared.Elastic.Models.ElasticExposure
{
    public class ElasticExposureMarketingDisplayDto
    {
        [JsonPropertyName("Event")]
        public string Event { get; set; }

        [<PERSON>son<PERSON>ropertyName("Channel")]
        public string Channel { get; set; }

        [JsonPropertyName("List_id")]
        public string ListId { get; set; }

        [JsonPropertyName("List_type")]
        public string ListType { get; set; }

        [Json<PERSON>ropertyName("Page_number")]
        public int PageNumber { get; set; }

        [JsonPropertyName("Page_size")]
        public int PageSize { get; set; }
    }
}