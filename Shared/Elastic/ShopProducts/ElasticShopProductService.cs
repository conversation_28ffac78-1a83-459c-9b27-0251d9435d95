using Nest;
using Serilog;
using Shared.Elastic.ShopProductsRedirects;

namespace Shared.Elastic.ShopProducts;

public class ElasticShopProductService(ElasticClient elasticClient, ILogger logger) : IElasticShopProductService
{
    // TODO - Change this to the new Exposures Indexes and use PartnerId in the Search
    // Finds the products that the customer has interacted with in the last x days including the MerchantId
    public async Task<List<(string ProductId, string MerchantId)>> ShopProductsByEmail(int lookBackDays, int count, double boostRedirects, string email)
    {
        var productIds = new List<(string ProductId, string MerchantId)>();

        try
        {
            var searchResponseRecommend = await elasticClient.SearchAsync<object>(s => s
                .Index("customers-shop.products-redirects,customers-shop.products-interacts")
                .Size(0)
                .Query(q => q
                    .Bool(b => b
                        .Must(mu => mu
                            .Term(t => t
                                .Field("Customer.Email")
                                .Value(email)
                            ),
                            mu => mu
                            .DateRange(r => r
                                .Field("@timestamp")
                                .GreaterThanOrEquals(DateTime.Now.AddDays(-lookBackDays).Date)
                            )
                        )
                        .Should(sh => sh
                            .Term(t => t
                                .Field("data_stream.namespace")
                                .Value("redirect")
                                .Boost(boostRedirects)
                            )
                        )
                    )
                )
                .Aggregations(a => a
                    .Terms("group_by_id", t => t
                        .Field("Product.Internal_product_id")
                        .Order(o => o
                            .Descending("total_score")
                        )
                        .Aggregations(aa => aa
                            .Sum("total_score", sum => sum
                                .Script("_score")
                            )
                            .Terms("group_by_merchant", tm => tm
                                .Field("Merchant.Id")
                            )
                        )
                    )
                )
            );

            if (searchResponseRecommend.IsValid)
            {
                var aggregation = searchResponseRecommend.Aggregations.Terms("group_by_id");

                productIds = aggregation.Buckets.Select(x => 
                {
                    var merchantId = x.Terms("group_by_merchant").Buckets.FirstOrDefault()?.Key;
                    return (x.Key, merchantId);
                }).Take(count).ToList();
            }
            else
            {
                logger.ForContext("service_name", GetType().Name).Error("Getting products from {service} failed with error {error}" , "Elasticsearch", searchResponseRecommend.ServerError.Error.Reason);

            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error Redirects");
        }

        return productIds;
    }
}