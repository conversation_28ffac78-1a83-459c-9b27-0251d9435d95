using System.Globalization;
using General_Services.Models.Models.Scores;
using Marlin_OS_Integration_API.Models.Order;
using Nest;
using Shared.Dto.Campaign;
using Shared.Dto.Curated.Product;
using Shared.Dto.Dashboard;
using Shared.Dto.MerchantScore;
using Shared.Elastic.Behavior;
using Shared.Elastic.CustomersAudienceEmails;
using Shared.Elastic.Discounts;
using Shared.Elastic.Discounts.Redirect;
using Shared.Elastic.Invoice.InvoiceLine;
using Shared.Elastic.Models.Behavior;
using Shared.Elastic.Models.ElasticCampaignsMailsOpens;
using Shared.Elastic.Webshop;
using Shared.Models;
using Shared.Models.Elastic.ElasticCampaignsMails;
using Shared.Models.Elastic.ElasticCampaignsMailsOpens;
using Shared.Models.Elastic.ElasticCustomerDiscounts;
using Shared.Services.Cache;
using Shared.Services.Partner;
using ILogger = Serilog.ILogger;

namespace Shared.Elastic.Elastic;

public class ElasticService(ElasticClient elasticClient, ILogger logger, ICacheService cacheService, IPartnerContext partnerContext)
    : IElasticService
{
    private readonly ILogger _logger = logger;

    public async Task<Dictionary<DateTime, long>> ClicksWebshopDayCount(DateTime from, DateTime to)
    {
        Func<QueryContainerDescriptor<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>, QueryContainer>
            queryBlock = null!;
        queryBlock = q => q.DateRange(dt => dt
            .Field(field => field.Shop_event.Send_date)
            .GreaterThanOrEquals(from)
            .LessThanOrEquals(to)
        );

        var response =
            (await elasticClient.SearchAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(s => s
                .Index("campaigns-mails-clicks")
                .Query(queryBlock)
                .Aggregations(a => a
                    .DateHistogram("emails_by_day", dh => dh
                        .Field(field => field.Shop_event.Send_date)
                        .CalendarInterval(DateInterval.Day)
                    )
                )
            ));

        var emailsByDay = response.Aggregations.DateHistogram("emails_by_day").Buckets
            .ToDictionary(
                bucket => bucket.Date, // or bucket.Key.Date to get as DateTime
                bucket => Convert.ToInt64(bucket.DocCount.Value)
            );
        return emailsByDay;
    }

    public async Task<long> ClicksWebshopCount(DateTime from, DateTime to)
    {
        return (await elasticClient.CountAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(s => s
            .Index("campaigns-mails-clicks")
            .Query(q =>
                q.Bool(b => b
                    .Filter(f =>
                        f.DateRange(dt => dt
                            .Field(field => field.Shop_event.Send_date)
                            .GreaterThanOrEquals(from)
                            .LessThan(to)
                        )))
            )
        )).Count;
    }

    public async Task<List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>> ClicksUniqueWebshopDayCount(
        DateTime from, DateTime to)
    {
        var uniqueOpens = new List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>();
        var index = "campaigns-mails-clicks";
        CompositeKey afterKey = null;
        do
        {
            var searchResponse =
                await elasticClient.SearchAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(s => s
                    .Index(index)
                    .Query(q => q
                        .DateRange(dt => dt
                            .Field(field => field.Shop_event.Send_date)
                            .GreaterThanOrEquals(from)
                            .LessThanOrEquals(to)
                        )
                    )
                    .Aggregations(a => a
                        .Composite("group_by_emailGuid", c => c
                            .Sources(src => src
                                .Terms("emailGuid", t => t.Field(f => f.Customer.Email_guid))
                            )
                            .Size(10000) // Use this to paginate through your results
                            .After(afterKey) // Use the afterKey for pagination
                            .Aggregations(aa => aa
                                .TopHits("top_hit", th => th
                                    .Size(1)
                                )
                            )
                        )
                    )
                );

            // Handle results
            var compositeAgg = searchResponse.Aggregations.Composite("group_by_emailGuid");
            foreach (var bucket in compositeAgg.Buckets)
            {
                var topHit = bucket.TopHits("top_hit");
                var doc = topHit.Documents<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>().First();
                uniqueOpens.Add(doc);
            }

            afterKey = compositeAgg.AfterKey;
        } while (afterKey != null);

        return uniqueOpens;
    }

    public async Task<Dictionary<DateTime, long>> OpensClickWebshopDayCount(DateTime from, DateTime to)
    {
        var datas = new Dictionary<DateTime, long>();
        var uniqueOpens = new List<ElasticCampaignsMailsOpens>();
        var index = "campaigns-mails-opens";

        CompositeKey afterKey = null;
        do
        {
            var searchResponse = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
                .Index(index)
                .Query(q => q
                    .DateRange(dt => dt
                        .Field(field => field.Shop_event.Send_date)
                        .GreaterThanOrEquals(from)
                        .LessThanOrEquals(to)
                    )
                )
                .Aggregations(a => a
                    .Composite("group_by_emailGuid", c => c
                        .Sources(src => src
                            .Terms("emailGuid", t => t.Field(f => f.Customer.Email_guid))
                        )
                        .Size(10000) // Use this to paginate through your results
                        .After(afterKey) // Use the afterKey for pagination
                        .Aggregations(aa => aa
                            .TopHits("top_hit", th => th
                                .Size(1)
                            )
                        )
                    )
                )
            );

            // Handle results
            var compositeAgg = searchResponse.Aggregations.Composite("group_by_emailGuid");
            foreach (var bucket in compositeAgg.Buckets)
            {
                var topHit = bucket.TopHits("top_hit");
                var doc = topHit.Documents<ElasticCampaignsMailsOpens>().First();
                uniqueOpens.Add(doc);
            }

            afterKey = compositeAgg.AfterKey;
        } while (afterKey != null);

        var uniqueOpensDictionary = uniqueOpens.GroupBy(a => a.Shop_event.Send_date.Date).ToList();
        var clicks = await ClicksWebshopDayCount(from, to);
        foreach (var uniqueOpen in uniqueOpensDictionary)
        {
            clicks.TryGetValue(uniqueOpen.Key, out var clickData);
            decimal data = clickData / (decimal) uniqueOpen.Count();
            //datas.Add(uniqueOpen.Key, data);
        }

        return datas;
    }

    public async Task<long> MailSentCount(DateTime from, DateTime to, int? campaignId = null)
    {
        if (campaignId == null)
        {
            return (await elasticClient.CountAsync<ElasticCampaignsMails>(s => s
                .Index("campaigns-mails")
                .Query(q =>
                    q.Bool(b => b
                        .Filter(f =>
                            f.DateRange(dt => dt
                                .Field(field => field.Email.Action_date)
                                .GreaterThan(from)
                                .LessThan(to)
                            )))
                )
            )).Count;
        }

        return (await elasticClient.CountAsync<ElasticCampaignsMails>(s => s
            .Index("campaigns-mails")
            .Query(q =>
                q.Bool(b => b
                    .Filter(f =>
                            f.DateRange(dt => dt
                                .Field(field => field.Email.Action_date)
                                .GreaterThan(from)
                                .LessThan(to)
                            ),
                        f => f.Term(field => field.Email.Campaign_id, campaignId)
                    )
                )
            )
        )).Count;
    }

    /*public async Task<long> UniqueOpensWebshop(int year, int month, string webShopId, int? campaignId)
    {
        List<CompositeBucket> allBuckets = new List<CompositeBucket>();
        CompositeKey afterKey = null;
        Func<QueryContainerDescriptor<ElasticCampaignsMailsOpens>, QueryContainer> queryBlock = null!;
        if (year == 0 && month == 0)
        {
            if (campaignId == null)
            {
                queryBlock = q =>
                    q.Terms(t =>
                        t.Field(f => f.Shop_event.Webshop_id).Terms(webShopId));
            }
            else
            {
                queryBlock = q =>
                    q.Terms(t =>
                        t.Field(f => f.Customer.Campaign_Id).Terms(campaignId));
            }
        }
        else
        {
            if (webShopId == "all")
            {
                var date = new DateTime(year, 1, 1);
                queryBlock = q =>
                    q.DateRange(dt => dt
                        .Field(field => field.EventObject.Created)
                        .GreaterThanOrEquals(date)
                    );
            }
            else
            {
                var firstDayOfMonth = new DateTime(year, month, 1);
                //var lastDayOfMonth = new DateTime(year, month, DateTime.DaysInMonth(year, month));
                var lastDayOfMonth = new DateTime(year, month, 1).AddMonths(1);

                queryBlock = q =>
                    q.Terms(t =>
                        t.Field(f => f.Shop_event.Webshop_id).Terms(webShopId)) &&
                    q.DateRange(dt => dt
                        .Field(field => field.EventObject.Created)
                        .GreaterThanOrEquals(firstDayOfMonth)
                        .LessThanOrEquals(lastDayOfMonth)
                    );
            }
        }

        do
        {
            var searchResponse = await _elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
                .Index("campaigns-mails-opens")
                .Query(queryBlock
                )
                .Aggregations(a => a
                    .Composite("unique_emails", c => c
                            .Sources(so => so
                                .Terms("email", t => t
                                    .Field(f => f.Customer.Email)))
                            .After(afterKey)
                            .Size(10000) // Set the batch size
                    )
                )
            );
            var compositeAggregation = searchResponse.Aggregations.Composite("unique_emails");
            allBuckets.AddRange(compositeAggregation.Buckets);
            afterKey = compositeAggregation.AfterKey;
        } while (afterKey != null);

        return allBuckets.Count;
    }*/

    /*public async Task<long> ClicksWebshop(int year, int month, string webShopId, int? campaignId)
    {
        Func<QueryContainerDescriptor<ElasticCampaignsMailsClicks>, QueryContainer> queryBlock = null!;
        if (year == 0 && month == 0)
        {
            if (campaignId == null)
            {
                queryBlock = q =>
                    q.Terms(t =>
                        t.Field(f => f.Shop_event.Webshop_id).Terms(webShopId));
            }
            else
            {
                queryBlock = q =>
                    q.Terms(t =>
                        t.Field(f => f.Customer.Campaign_Id).Terms(campaignId));
            }
        }
        else
        {
            var firstDayOfMonth = new DateTime(year, month, 1);
            //var lastDayOfMonth = new DateTime(year, month, DateTime.DaysInMonth(year, month));
            var lastDayOfMonth = new DateTime(year, month, 1).AddMonths(1);
            queryBlock = q =>
                q.Terms(t =>
                    t.Field(f => f.Shop_event.Webshop_id).Terms(webShopId)) &&
                q.DateRange(dt => dt
                    .Field(field => field.EventObject.Created)
                    .GreaterThanOrEquals(firstDayOfMonth)
                    .LessThanOrEquals(lastDayOfMonth)
                );
        }

        return (await _elasticClient.CountAsync<ElasticCampaignsMailsClicks>(s => s
            .Index("campaigns-mails-clicks")
            .Query(queryBlock)
        )).Count;
    }*/

    public async Task<double> CustomerOrdersRevenueElastic(int partnerId, DateTime lookBack)
    {
        var elasticOrderEvent = await elasticClient.SearchAsync<ElasticOrderEvent>(s => s
            .Index("customers-orders-dashboard")
            .Query(q => q.DateRange(dt => dt
                .Field(field => field.Order_date)
                .GreaterThanOrEquals(lookBack)))
            .Aggregations(a => a
                .Sum("sum_value", sa => sa
                    .Field(f => f.Shop_order.Total_price)
                )
            )
        );

        var sum = elasticOrderEvent.Aggregations.Sum("sum_value")?.Value;
        return sum ?? 0;
    }

    public async Task<long> UniqueClicksWebshop(int year, int month, string webShopId, int? campaignId)
    {
        List<CompositeBucket> allBuckets = new List<CompositeBucket>();
        CompositeKey afterKey = null;
        Func<QueryContainerDescriptor<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>, QueryContainer>
            queryBlock = null!;
        if (year == 0 && month == 0)
        {
            if (campaignId == null)
            {
                queryBlock = q =>
                    q.Terms(t =>
                        t.Field(f => f.Shop_event.Webshop_id).Terms(webShopId));
            }
            else
            {
                queryBlock = q =>
                    q.Terms(t =>
                        t.Field(f => f.Customer.Campaign_Id).Terms(campaignId));
            }
        }
        else
        {
            var firstDayOfMonth = new DateTime(year, month, 1);
            //var lastDayOfMonth = new DateTime(year, month, DateTime.DaysInMonth(year, month));
            var lastDayOfMonth = new DateTime(year, month, 1).AddMonths(1);

            queryBlock = q =>
                q.Terms(t =>
                    t.Field(f => f.Shop_event.Webshop_id).Terms(webShopId)) &&
                q.DateRange(dt => dt
                    .Field(field => field.EventObject.Created)
                    .GreaterThanOrEquals(firstDayOfMonth)
                    .LessThanOrEquals(lastDayOfMonth)
                );
        }

        do
        {
            var searchResponse =
                await elasticClient.SearchAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(s => s
                    .Index("campaigns-mails-clicks")
                    .Query(queryBlock)
                    .Aggregations(a => a
                        .Composite("unique_emails", c => c
                                .Sources(so => so
                                    .Terms("email", t => t
                                        .Field(f => f.Customer.Email)))
                                .After(afterKey)
                                .Size(10000) // Set the batch size
                        )
                    )
                );
            var compositeAggregation = searchResponse.Aggregations.Composite("unique_emails");
            allBuckets.AddRange(compositeAggregation.Buckets);
            afterKey = compositeAggregation.AfterKey;
        } while (afterKey != null);

        return allBuckets.Count;
    }

    public async Task<ElasticBehaviorEvent?> NewestCustomerPageEvents(int merchantId, string type)
    {
        return (await elasticClient.SearchAsync<ElasticBehaviorEvent>(s => s
            .Index("customers-pages-events")
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Event_received,
                        f => f.Shop_event.Webshop_id,
                        f => f.Shop_event.Event_type
                    )
                )
            )
            .Query(q =>
                q.Bool(b => b
                    .Filter(f =>
                        f.DateRange(dt => dt
                            .Field(field => field.Event_received)
                            .GreaterThanOrEquals(DateTime.UtcNow.AddHours(-48))
                        ) &&
                        q.Term(t => t
                            .Field(f => f.Shop_event.Webshop_id)
                            .Value(merchantId)) &&
                        q.Term(t => t
                            .Field(f => f.Shop_event.Event_type)
                            .Value(type))
                    )
                ))
            .Sort(ss => ss
                .Descending(field => field.Event_received))
            .Take(1)
        )).Documents.FirstOrDefault();
    }

    public async Task<long> PageEvents(int webshopId, int days)
    {
        return (await elasticClient.CountAsync<ElasticBehaviorEvent>(s => s
            .Index("customers-pages-events")
            .Query(q =>
                q.Terms(t => t.Field(f => f.Customer.Email).Terms(new[] {"n/a"})) &&
                q.Terms(t => t.Field(f => f.Shop_event.Webshop_id).Terms(new[] {webshopId.ToString()}))
                && q.Bool(b => b
                    .Filter(f =>
                        f.DateRange(dt => dt
                            .Field(field => field.Event_received)
                            .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days))
                        )))
            )
        )).Count;
    }

    public async Task<List<CuratedProductEvents>> PageEventsTopProducts(int merchantId, int days, int productAmount = 10000)
    {
        var response = await elasticClient.SearchAsync<ElasticBehaviorEvent>(s => s
            .Index("customers-pages-events")
            .Size(0)
            .Query(q =>
                q.Terms(t => t.Field(f => f.Shop_event.Webshop_id).Terms(new[] {merchantId.ToString()}))
                && q.Bool(b => b
                    .Filter(f =>
                        f.DateRange(dt => dt
                            .Field(field => field.Event_received)
                            .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days))
                        )))
            )
            .Aggregations(a => a
                .Terms("top_internalId", st => st
                    .Field(f => f.Shop_event.Product_internal_id)
                    .Size(productAmount)
                    .Order(o => o
                        .CountDescending()
                    )
                )
            )
        );

        return response.Aggregations.Terms("top_internalId")?.Buckets
            .Select(b => new CuratedProductEvents {Id = b.Key, Count = b.DocCount ?? 0})
            .ToList() ?? [];
    }

    public async Task<List<CuratedProductEvents>> OrderTopProducts(int merchantId, int days, int productAmount)
    {
        var response = await elasticClient.SearchAsync<ElasticOrderProductTransformEvent>(s => s
            .Index("customers-orders-products.transform")
            .Size(0)
            .Query(q =>
                q.Terms(t => t.Field(f => f.Merchant.Id).Terms(new[] {merchantId.ToString()}))
                && q.Bool(b => b
                    .Filter(f =>
                        f.DateRange(dt => dt
                            .Field(field => field.Action_date)
                            .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days))
                        )))
            )
            .Aggregations(a => a
                .Terms("top_internalId", st => st
                    .Field(f => f.Order.Internal_product_id)
                    .Size(productAmount)
                    .Order(o => o
                        .CountDescending()
                    )
                )
            )
        );

        return response.Aggregations.Terms("top_internalId").Buckets
            .Select(b => new CuratedProductEvents {Id = b.Key, Count = b.DocCount ?? 0})
            .ToList();
    }

    public async Task<(long eventsEmail, long eventsNoEmail)> CustomerPagesAmount(int webshopId, int days)
    {
        long eventsNoEmail = 0;
        long eventsEmail = 0;

        var index = "customers-pages-events";
        //No email
        var searchResponse = await elasticClient.SearchAsync<ElasticBehaviorEvent>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Event_received,
                        f => f.Shop_event.Webshop_id,
                        f => f.Shop_event.Event_type,
                        f => f.Customer.Email
                    )
                )
            )
            .Index(index)
            .Query(q =>
                q.Terms(t => t.Field(f => f.Customer.Email).Terms(new[] {"n/a"})) &&
                q.Terms(t => t.Field(f => f.Shop_event.Webshop_id).Terms(new[] {webshopId.ToString()}))
                && q.Bool(b => b
                    .Filter(f =>
                        f.DateRange(dt => dt
                            .Field(field => field.Event_received)
                            .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days))
                        )))
            )
            .Aggregations(a => a.Terms("unique_campaign_ids", t =>
                t.Field(c => c.Customer.Email)))
            .Size(10000)
            .Scroll("2m")
        );

        var queryResult = searchResponse.Aggregations.Terms("unique_campaign_ids").Buckets;
        foreach (var keyStatus in queryResult)
        {
            eventsNoEmail = keyStatus.DocCount ?? 0;
        }

        //Found email
        searchResponse = await elasticClient.SearchAsync<ElasticBehaviorEvent>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Event_received,
                        f => f.Shop_event.Webshop_id,
                        f => f.Shop_event.Event_type,
                        f => f.Customer.Email
                    )
                )
            )
            .Index(index)
            .Query(q =>
                q.Bool(b =>
                    b.MustNot(m => m
                        .Term(t => t
                            .Field(f => f.Customer.Email)
                            .Value("n/a")
                        )
                    )) &&
                q.Terms(t => t.Field(f => f.Shop_event.Webshop_id).Terms(new[] {webshopId.ToString()}))
                && q.Bool(b => b
                    .Filter(f =>
                        f.DateRange(dt => dt
                            .Field(field => field.Event_received)
                            .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days))
                        )))
            )
            .Aggregations(a => a.Terms("unique_campaign_ids", t =>
                t.Field(c => c.Customer.Email)))
            .Size(10000)
            .Scroll("2m")
        );

        queryResult = searchResponse.Aggregations.Terms("unique_campaign_ids").Buckets;
        foreach (var keyStatus in queryResult)
        {
            eventsEmail += keyStatus.DocCount ?? 0;
        }

        await elasticClient.ClearScrollAsync(cs => cs
            .ScrollId(searchResponse.ScrollId)
        );
        return (eventsEmail, eventsNoEmail);
    }

    public async Task<List<ElasticOrderEvent>> CustomerOrdersElastic(string email, string phone, int lookBackYear)
    {
        var orders = new List<ElasticOrderEvent>();
        var searchResponse = elasticClient.Search<ElasticOrderEvent>((s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_order.Webshop_id,
                            f => f.Shop_order.Webshop_name,
                            f => f.Order_date,
                            f => f.Customer!.Email,
                            f => f.Shop_order.Billing_address!.Phone_number,
                            f => f.Shop_order.Shipping_address!.Phone_number
                        )
                    )
                )
                .Index("customers-orders")
                .Query(q =>
                    (q.Match(m => m
                         .Field(f => f.Customer.Email).Query(email.ToLowerInvariant()))
                     || q.Match(m => m
                         .Field(f => f.Shop_order.Billing_address.Phone_number).Query(phone.ToLowerInvariant()))
                     || q.Match(m => m
                         .Field(f => f.Shop_order.Shipping_address.Phone_number).Query(phone.ToLowerInvariant()))
                    )
                    && q.Bool(b => b
                        .Filter(f =>
                            f.DateRange(dt => dt
                                .Field(field => field.Order_date)
                                .GreaterThanOrEquals(DateTime.UtcNow.AddYears(-lookBackYear))
                            )))
                )
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.Order_date))
            ));
        orders.AddRange(searchResponse.Documents);
        await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
        return orders.ToList();
    }

    public async Task<List<ElasticPartnerOrderEvent>> ViaBillOrdersElastic(string email, int lookBackYear)
    {
        var orders = new List<ElasticPartnerOrderEvent>();
        var searchResponse = elasticClient.Search<ElasticPartnerOrderEvent>((s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_order.Webshop_id,
                            f => f.Order_date,
                            f => f.Customer!.Email
                        )
                    )
                )
                .Index("customers-orders-partners")
                .Query(q =>
                    (q.Match(m => m
                        .Field(f => f.Customer.Email).Query(email.ToLowerInvariant()))
                    )
                    && q.Bool(b => b
                        .Filter(f =>
                            f.DateRange(dt => dt
                                .Field(field => field.Order_date)
                                .GreaterThanOrEquals(DateTime.UtcNow.AddYears(-lookBackYear))
                            )))
                )
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.Order_date))
            ));

        orders.AddRange(searchResponse.Documents);
        await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
        return orders.Where(a => a.Shop_order.Webshop_id != "n/a").ToList();
    }

    public async Task<List<ElasticCampaignsMailsOpens>> CampaignOpenElastic(string email, int lookBackDays)
    {
        var orders = new List<ElasticCampaignsMailsOpens>();
        var index = "campaigns-mails-opens";
        var searchResponse = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Shop_event!.Webshop_id,
                        f => f.Customer.Email,
                        f => f.Customer.Campaign_Id,
                        f => f.EventObject.Created
                    )
                )
            )
            .Index(index)
            .Query(q =>
                (q.MatchPhrase(m => m
                    .Field(f => f.Customer.Email)
                    .Query(email.ToLowerInvariant())
                ))
                && q.Bool(b => b
                    .Filter(f =>
                        f.DateRange(dt => dt
                            .Field(field => field.EventObject.Created)
                            .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-lookBackDays))
                        )))
            )
            .Size(10_000)
            .Sort(srt => srt.Descending(f => f.EventObject.Created))
            .Scroll("2m")
        );

        orders.AddRange(searchResponse.Documents);
        while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
               searchResponse.Documents.Count != 0)
        {
            searchResponse =
                await elasticClient.ScrollAsync<ElasticCampaignsMailsOpens>("2m", searchResponse.ScrollId);
            orders.AddRange(searchResponse.Documents);
        }

        await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
        return orders.Where(a => !string.IsNullOrEmpty(a.Shop_event.Webshop_id.FirstOrDefault())).ToList();
    }

    public async Task<List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>> CampaignClickElastic(
        string email, int lookBackDays)
    {
        var orders = new List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>();
        var index = "campaigns-mails-clicks";
        var searchResponse =
            await elasticClient.SearchAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_event!.Webshop_id,
                            f => f.Customer.Email,
                            f => f.Customer.Campaign_Id,
                            f => f.EventObject.Created
                        )
                    )
                )
                .Index(index)
                .Query(q =>
                    (q.MatchPhrase(m => m
                        .Field(f => f.Customer.Email)
                        .Query(email.ToLowerInvariant())
                    ))
                    && q.Bool(b => b
                        .Filter(f =>
                            f.DateRange(dt => dt
                                .Field(field => field.EventObject.Created)
                                .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-lookBackDays))
                            )))
                )
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.EventObject.Created))
                .Scroll("2m")
            );

        orders.AddRange(searchResponse.Documents);
        while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
               searchResponse.Documents.Count != 0)
        {
            searchResponse =
                await elasticClient.ScrollAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>("2m",
                    searchResponse.ScrollId);
            orders.AddRange(searchResponse.Documents);
        }

        await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
        return orders.Where(a => !string.IsNullOrEmpty(a.Shop_event.Webshop_id)).ToList();
    }

    public async Task<Dictionary<string, decimal>> GetMerchantProductScore(string webshopId,
        Dictionary<string, string> scoreSettings, CancellationToken cancellationToken)
    {
        Dictionary<string, decimal> result = new();

        var now = TimeZoneInfo.ConvertTimeToUtc(DateTime.UtcNow);
        now = new DateTime(now.Year, now.Month, now.Day, now.Hour, now.Minute, now.Second);
        var scale = scoreSettings["PageviewDecayScaleDays"];
        var decay = double.Parse(scoreSettings["PageviewDecayFraction"], CultureInfo.InvariantCulture);
        var pageviewScore = int.Parse(scoreSettings["PageviewScore"]);
        var fromDate = now.AddDays(-int.Parse(scoreSettings["BuyLookbackDays"]));

        var filters = new List<Func<QueryContainerDescriptor<MerchantProductScoreEvent>, QueryContainer>>
        {
            f => f.DateRange(dt => dt
                     .Field(f => f.CreatedDate)
                     .GreaterThanOrEquals(fromDate)
                     .LessThanOrEquals(now))
                 && f.Term(t => t.Field(f => f.ShopEvent.WebshopId).Value(webshopId))
                 && f.Term(t => t.Field(f => f.ShopEvent.EventType).Value("ProductLook"))
        };

        var response = await elasticClient.SearchAsync<MerchantProductScoreEvent>(s => s
            .Size(0)
            .Source(s => s
                .Includes(i => i
                    .Field(f => f.ShopEvent.WebshopId)
                    .Field(f => f.ShopEvent.ProductInternalId)
                )
            )
            .Index("customers-pages-events")
            .Query(q => q
                .FunctionScore(fs => fs
                    .Name("MerchantProductScores")
                    .BoostMode(FunctionBoostMode.Replace)
                    .ScoreMode(FunctionScoreMode.Sum)
                    .Query(qu => qu
                        .Bool(b => b
                            .Filter(f => f.Bool(b => b.Should(filters)))
                        )
                    )
                    .Functions(fcs => fcs
                        .ExponentialDate(exp => exp
                            .Field(f => f.CreatedDate)
                            .Origin(now)
                            .Scale(scale + "d")
                            .Offset("0d")
                            .Decay(decay)
                            .Weight(pageviewScore)
                        )
                    )
                )
            )
            .Aggregations(a => a.Terms("shops", t => t.Field("Shop_event.Webshop_id")
                .Field("Shop_event.Product_internal_id")
                .Aggregations(a => a
                    .Sum("productscore", s => s.Script("_score"))))), cancellationToken);

        if (!cancellationToken.IsCancellationRequested)
        {
            if (response.IsValid)
            {
                var aggs = response.Aggregations.Terms("shops");
                foreach (var agg in aggs.Buckets)
                {
                    result.Add(agg.Key, (decimal) (agg.Sum("productscore").Value ?? 0.0));
                }
            }
        }

        return result;
    }

    public async Task<long> DiscountEventsElastic(string discountDetailsViewed, DateTime firstDayOfMonth)
    {
        var data = (await elasticClient.CountAsync<ElasticCustomerDiscounts>(s => s
            .Index("customers-discounts-events")
            .Query(q =>
                q.Terms(t =>
                    t.Field(f => f.Discount.Event_type).Terms(discountDetailsViewed)) &&
                q.Terms(t =>
                    t.Field(f => f.Discount.Page_number).Terms(1)) &&
                q.DateRange(dt => dt
                    .Field(field => field.Event_received)
                    .GreaterThanOrEquals(firstDayOfMonth)
                )
            )
        )).Count;
        return data;
    }

    public async Task<long> DiscountEventsElastic(long discountId, string eventType)
    {
        return (await elasticClient.CountAsync<ElasticCustomerDiscounts>(s => s
            .Index("customers-discounts-events")
            .Query(q =>
                q.Terms(t => t.Field(f => f.Discount.Discount_id).Terms(new[] {discountId})) &&
                q.Terms(t => t.Field(f => f.Discount.Event_type).Terms(eventType)))
        )).Count;
    }

    public async Task<DashboardProfileMQLGraph> CustomersAudienceCount(int year)
    {
        var data = new DashboardProfileMQLGraph();
        var firstDayOfYear = new DateTime(year, 1, 1);
        var firstDayOfNextYear = new DateTime(year + 1, 1, 1);
        var searchResponse = await elasticClient.SearchAsync<CustomersAudienceEmailsEvent>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Event_received,
                        f => f.Contacts.Is_total,
                        f => f.Contacts.Unique_email_count
                    )
                )
            )
            .Index("customers-audience-emailcounts")
            .Query(q =>
                q.Terms(t =>
                    t.Field(f => f.Contacts.Is_total).Terms("true")) &&
                q.DateRange(dt => dt
                    .Field(field => field.Event_received)
                    .GreaterThanOrEquals(firstDayOfYear)
                    .LessThan(firstDayOfNextYear)
                ))
            .Sort(srt => srt.Ascending(f => f.Event_received))
            .Size(10_000)
            .Scroll("2m")
        );

        // Create a dictionary to store data by month for quick lookup
        var dataByMonth = searchResponse.Documents.ToList()
            .GroupBy(e => e.Event_received.Month)
            .ToDictionary(g => g.Key, g => g.OrderByDescending(e => e.Event_received).First().Contacts.Unique_email_count);
        
        // Fill in all 12 months
        for (int month = 1; month <= 12; month++)
        {
            // Use actual data if available, otherwise use 0
            var emailCount = dataByMonth.TryGetValue(month, out var count) ? count : 0;
            
            data.Data.Add(emailCount);
            data.Month.Add(month);
        }

        return data;
    }

    public async Task<DashboardProfileMQLGraph> CustomersAudienceCount(int partnerId, DateTime from, DateTime to)
    {
        var searchResponse = await elasticClient.SearchAsync<CustomersAudienceEmailsEvent>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Event_received,
                        f => f.Contacts.Is_total,
                        f => f.Contacts.Unique_email_count
                        //f => f.Partner.Id
                    )
                )
            )
            .Index("customers-audience-emailcounts")
            .Query(q =>
            {
                var query = q.Terms(t => t.Field(f => f.Contacts.Is_total).Terms("true")) &&
                    q.DateRange(dt => dt
                        .Field(field => field.Event_received)
                        .GreaterThanOrEquals(from)
                        .LessThan(to)
                    );

                if (partnerId == 52876)
                {
                    // For partnerId 52876, include entries where PartnerId is not present
                    query &= q.Bool(b => b
                        .Should(
                            s => s.Terms(t => t.Field(f => f.Partner.Id).Terms(partnerId.ToString())),
                            s => s.Bool(nb => nb.MustNot(mn => mn.Exists(e => e.Field(f => f.Partner.Id))))
                        )
                        .MinimumShouldMatch(1)
                    );
                }
                else
                {
                    // For other partnerIds, only include entries with matching PartnerId
                    query &= q.Terms(t => t.Field(f => f.Partner.Id).Terms(partnerId.ToString()));
                }

                return query;
            })
            .Sort(srt => srt.Ascending(f => f.Event_received))
            .Size(10_000)
            .Scroll("2m")
        );

        var data = new DashboardProfileMQLGraph();
        
        // Create a dictionary to store data by day for quick lookup
        var dataByDay = searchResponse.Documents.ToList()
            .GroupBy(e => e.Event_received.Date)
            .ToDictionary(g => g.Key, g => g.OrderByDescending(e => e.Event_received).First().Contacts.Unique_email_count);
        
        // Fill in all days from 'from' to 'to' date range
        var currentDate = from.Date;
        while (currentDate < to.Date)
        {
            // Use actual data if available, otherwise use 0
            var emailCount = dataByDay.TryGetValue(currentDate, out var count) ? count : 0;
            
            data.Data.Add(emailCount);
            data.Month.Add(currentDate.Day);
            
            currentDate = currentDate.AddDays(1);
        }

        return data;
    }

    public async Task<List<ElasticCampaignsMailsOpens>> CampaignOpenElastic(string webshopId, string email,
        int lookBackDays)
    {
        try
        {
            var orders = new List<ElasticCampaignsMailsOpens>();
            var index = "campaigns-mails-opens";
            var searchResponse = await elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_event!.Webshop_id,
                            f => f.Customer.Email,
                            f => f.Customer.Campaign_Id,
                            f => f.EventObject.Created
                        )
                    )
                )
                .Index(index)
                .Query(q =>
                    q.Terms(t => t.Field(f => f.Shop_event.Webshop_id).Terms(new[] {webshopId})) &&
                    (q.MatchPhrase(m => m
                        .Field(f => f.Customer.Email)
                        .Query(email.ToLowerInvariant())
                    ))
                    && q.Bool(b => b
                        .Filter(f =>
                            f.DateRange(dt => dt
                                .Field(field => field.EventObject.Created)
                                .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-lookBackDays))
                            )))
                )
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.EventObject.Created))
                .Scroll("1m")
            );

            orders.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
                   searchResponse.Documents.Count != 0)
            {
                searchResponse =
                    await elasticClient.ScrollAsync<ElasticCampaignsMailsOpens>("1m", searchResponse.ScrollId);
                orders.AddRange(searchResponse.Documents);
            }

            if (!searchResponse.IsValid)
            {
                throw new Exception($"Error getting campaigns-mails-opens for invoice with email {email}");
            }

            await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
            return orders.Where(a => a.Shop_event.Webshop_id.Count > 0).ToList();
        }
        catch (Exception exception)
        {
            Console.WriteLine(exception);
            throw;
        }
    }

    public async Task<List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>> CampaignClickElastic(
        string webshopId, string email,
        int lookBackDays)
    {
        var orders = new List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>();
        var index = "campaigns-mails-clicks";
        var searchResponse =
            await elasticClient.SearchAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_event!.Webshop_id,
                            f => f.Customer.Email,
                            f => f.Customer.Campaign_Id,
                            f => f.EventObject.Created
                        )
                    )
                )
                .Index(index)
                .Query(q =>
                    q.Terms(t => t.Field(f => f.Shop_event.Webshop_id).Terms(new[] {webshopId})) &&
                    (q.MatchPhrase(m => m
                        .Field(f => f.Customer.Email)
                        .Query(email.ToLowerInvariant())
                    ))
                    && q.Bool(b => b
                        .Filter(f =>
                            f.DateRange(dt => dt
                                .Field(field => field.EventObject.Created)
                                .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-lookBackDays))
                            )))
                )
                .Size(10_000)
                .Sort(srt => srt.Descending(f => f.EventObject.Created))
                .Scroll("1m")
            );

        orders.AddRange(searchResponse.Documents);
        while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
               searchResponse.Documents.Count != 0)
        {
            searchResponse =
                await elasticClient.ScrollAsync<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>("1m",
                    searchResponse.ScrollId);
            orders.AddRange(searchResponse.Documents);
        }

        if (!searchResponse.IsValid)
        {
            throw new Exception($"Error getting campaigns-mails-opens for invoice with email {email}");
        }

        await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
        return orders.Where(a => !string.IsNullOrEmpty(a.Shop_event.Webshop_id)).ToList();
        //return orders.Where(a => a.Shop_event.Webshop_id.Count > 0).ToList();
    }

    public async Task<List<DiscountElastic>> CustomersDiscountsElastic(string webshopId, string email, string type,
        int lookBackDays)
    {
        var orders = new List<DiscountElastic>();
        var index = "customers-discounts-events";
        var searchResponse = await elasticClient.SearchAsync<DiscountElastic>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Shop_event!.Webshop_id,
                        f => f.Customer.Email,
                        f => f.Discount.Discount_id,
                        f => f.Discount.Event_type,
                        f => f.TimeStamp
                    )
                )
            )
            .Index(index)
            .Query(q =>
                q.Terms(t => t.Field(f => f.Shop_event.Webshop_id).Terms(new[] {webshopId})) &&
                q.MatchPhrase(m => m
                    .Field(f => f.Customer.Email)
                    .Query(email.ToLowerInvariant())
                ) &&
                q.MatchPhrase(m => m
                    .Field(f => f.Discount.Event_type)
                    .Query(type)
                )
                && q.Bool(b => b
                    .Filter(f =>
                        f.DateRange(dt => dt
                            .Field(field => field.TimeStamp)
                            .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-lookBackDays))
                        )))
            )
            .Size(10_000)
            .Sort(srt => srt.Descending(f => f.TimeStamp))
            .Scroll("1m")
        );

        orders.AddRange(searchResponse.Documents);
        while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
               searchResponse.Documents.Count != 0)
        {
            searchResponse = await elasticClient.ScrollAsync<DiscountElastic>("1m", searchResponse.ScrollId);
            orders.AddRange(searchResponse.Documents);
        }

        if (!searchResponse.IsValid)
        {
            throw new Exception($"Error getting customers-discounts-events for invoice with email {email}");
        }

        await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
        return orders.ToList();
    }

    public async Task<List<MerchantScore>> GetMerchantScore(string gender, int? age, int? ageInterval)
    {
        var cacheKey = $"ElasticService_GetMerchantScore_{gender}_{age}_{ageInterval}";
        List<MerchantScore>? lines = await cacheService.GetData<List<MerchantScore>>(cacheKey);
        if (lines == null || lines.Count == 0)
        {
            lines = await GetLines("invoices-lines", gender, age, ageInterval);
            var linesPotentials = await GetLines("invoices-potentiallines", gender, age, ageInterval);

            // Update list1 based on values in list2
            foreach (var potentialLine in linesPotentials)
            {
                var score = lines.FirstOrDefault(o => o.MerchantId == potentialLine.MerchantId);
                if (score != null)
                {
                    // If an object with the same Id exists in list1, update its Value
                    score.Sum += potentialLine.Sum;
                }
                else
                {
                    // If the object does not exist in list1, add it
                    lines.Add(potentialLine);
                }
            }

            cacheService.SetData(cacheKey, lines, TimeSpan.FromHours(16));
        }

        return lines;
    }

    public async Task<long> DiscountEventsRedirectElastic(long discountId)
    {
        return (await elasticClient.CountAsync<DiscountRedirectElastic>(s => s
            .Index("customers-discounts-redirects")
            .Query(q =>
                q.Terms(t => t.Field(f => f.Customer.Discount_id).Terms(new[] {discountId})))
        )).Count;
    }

    public async Task<List<MerchantScore>> GetLines(string index, string gender,
        int? age = null, int? ageInterval = null)
    {
        /*age = 30;
        ageInterval = 4;
        gender = "Female";*/

        if (gender == "Unknown")
        {
            age = null;
        }

        int startSearchAge = 0;
        int endSearchAge = 999;
        int start = 18;
        int end = 69;
        if (ageInterval != null && age != null && age <= end && age >= start)
        {
            // Calculate the interval start and end directly
            int intervalStart = ((int) age - start) / (int) ageInterval * (int) ageInterval + start;
            int intervalEnd = intervalStart + (int) ageInterval - 1;
            intervalEnd = intervalEnd > end ? end : intervalEnd;

            startSearchAge = intervalStart;
            endSearchAge = intervalEnd;
        }
        else
        {
            if (age >= end)
            {
                startSearchAge = 69;
                endSearchAge = 999;
            }
            else if (age == null)
            {
                startSearchAge = 0;
                endSearchAge = 999;
            }
            else
            {
                startSearchAge = 0;
                endSearchAge = start - 1;
            }
        }

        var now = DateTime.UtcNow;
        var lastDayOfPreviousMonth = new DateTime(now.Year, now.Month, 1).AddSeconds(-1);
        var firstDayOfPreviousMonth = new DateTime(lastDayOfPreviousMonth.Year, lastDayOfPreviousMonth.Month, 1);


        var merchantScores = new List<MerchantScore>();
        var searchResponse = await elasticClient.SearchAsync<ElasticInvoiceLineMasterEvent>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Invoice_line,
                        f => f.Invoice_line!.Webshop_id,
                        f => f.Invoice_line.Email,
                        f => f.Invoice_line.Gender,
                        f => f.Invoice_line.Order_date,
                        f => f.Invoice_line.Total_cut,
                        f => f.Invoice_line.Age
                    )
                )
            )
            .Index(index)
            .Query(q => q
                .Bool(b => b
                    .Filter(
                        f => f.DateRange(dt => dt
                            .Field(field => field.Invoice_line.Order_date)
                            .GreaterThanOrEquals(firstDayOfPreviousMonth)
                            .LessThanOrEquals(lastDayOfPreviousMonth)),
                        f => f.Range(r => r
                            .Field(field => field.Invoice_line.Age)
                            .GreaterThanOrEquals(startSearchAge)
                            .LessThanOrEquals(endSearchAge)
                        ),
                        f => f.Term(t => t
                            .Field(field => field.Invoice_line.Gender)
                            .Value(gender)
                        )
                    )
                )
            )
            .Aggregations(aggs => aggs
                .Terms("by_webshop", webshopAgg => webshopAgg
                    .Field(f => f.Invoice_line!.Webshop_id)
                    .Size(10000)
                    .Aggregations(webshopSubAggs => webshopSubAggs
                        .Sum("total_cut_sum", sumAgg => sumAgg
                            .Field(f => f.Invoice_line!.Total_cut)
                        )
                    )
                )
            )
        );

        var byWebshopAggregation = searchResponse.Aggregations.Terms("by_webshop");
        if (byWebshopAggregation != null)
        {
            foreach (var webshopBucket in byWebshopAggregation.Buckets)
            {
                var webshopId = webshopBucket.Key;

                // Get the sum of Total_cut for each webshop
                var totalCutSum = webshopBucket.Sum("total_cut_sum")?.Value ?? 0;

                merchantScores.Add(new Shared.Dto.MerchantScore.MerchantScore
                {
                    MerchantId = webshopId,
                    Sum = totalCutSum
                });
            }
        }

        return merchantScores.OrderByDescending(a => a.Sum).ToList();
    }

    // Simplified version of GetLinesExtended for debugging
    public async Task<List<MerchantScore>> GetLinesExtendedSimplified(string index, List<int> merchantIds, string gender,
        int? age = null, int? ageInterval = null)
    {
        Console.WriteLine($"=== SIMPLIFIED METHOD ===");
        Console.WriteLine($"GetLinesExtendedSimplified called with index: {index}, merchantIds: {string.Join(",", merchantIds)}, gender: {gender}, age: {age}, ageInterval: {ageInterval}");

        if (gender == "Unknown")
        {
            age = null;
        }

        int startSearchAge = 0;
        int endSearchAge = 999;
        int start = 18;
        int end = 69;
        if (ageInterval != null && age != null && age <= end && age >= start)
        {
            int intervalStart = ((int) age - start) / (int) ageInterval * (int) ageInterval + start;
            int intervalEnd = intervalStart + (int) ageInterval - 1;
            intervalEnd = intervalEnd > end ? end : intervalEnd;

            startSearchAge = intervalStart;
            endSearchAge = intervalEnd;
        }
        else
        {
            if (age >= end)
            {
                startSearchAge = 69;
                endSearchAge = 999;
            }
            else if (age == null)
            {
                startSearchAge = 0;
                endSearchAge = 999;
            }
            else
            {
                startSearchAge = 0;
                endSearchAge = start - 1;
            }
        }

        Console.WriteLine($"Age range calculated: {startSearchAge} to {endSearchAge}");

        // Convert merchant IDs to strings for Elasticsearch
        var merchantIdStrings = merchantIds.Select(id => id.ToString()).ToList();

        // Use a broader date range to ensure we catch data
        var now = DateTime.UtcNow;
        var fromDate = now.AddMonths(-6); // Look back 6 months instead of just previous month
        var toDate = now;

        Console.WriteLine($"Date range: {fromDate} to {toDate}");

        var merchantScores = new List<MerchantScore>();
        var searchResponse = await elasticClient.SearchAsync<ElasticInvoiceLineMasterEvent>(s => s
            .Index(index)
            .Size(0)
            .Query(q => q
                .Bool(b => b
                    .Filter(
                        f => f.Terms(t => t
                            .Field(field => field.Invoice_line.Webshop_id)
                            .Terms(merchantIdStrings)
                        ),
                        f => f.DateRange(dt => dt
                            .Field(field => field.Invoice_line.Order_date)
                            .GreaterThanOrEquals(fromDate)
                            .LessThanOrEquals(toDate)),
                        f => f.Range(r => r
                            .Field(field => field.Invoice_line.Age)
                            .GreaterThanOrEquals(startSearchAge)
                            .LessThanOrEquals(endSearchAge)
                        ),
                        f => f.Term(t => t
                            .Field(field => field.Invoice_line.Gender)
                            .Value(gender)
                        )
                    )
                )
            )
            .Aggregations(aggs => aggs
                .Terms("by_webshop", webshopAgg => webshopAgg
                    .Field(f => f.Invoice_line!.Webshop_id)
                    .Size(10000)
                    .Aggregations(webshopSubAggs => webshopSubAggs
                        .Sum("total_cut_sum", sumAgg => sumAgg
                            .Field(f => f.Invoice_line!.Total_cut)
                        )
                    )
                )
            )
        );

        Console.WriteLine($"Simplified query response - IsValid: {searchResponse.IsValid}, Total hits: {searchResponse.Total}");

        if (!searchResponse.IsValid)
        {
            Console.WriteLine($"Elasticsearch query failed: {searchResponse.OriginalException?.Message ?? searchResponse.ServerError?.ToString()}");
            Console.WriteLine($"Debug info: {searchResponse.DebugInformation}");
        }

        var byWebshopAggregation = searchResponse.Aggregations.Terms("by_webshop");
        if (byWebshopAggregation != null)
        {
            Console.WriteLine($"Found {byWebshopAggregation.Buckets.Count} webshop buckets");

            foreach (var webshopBucket in byWebshopAggregation.Buckets)
            {
                var webshopId = webshopBucket.Key;
                var totalCutSum = webshopBucket.Sum("total_cut_sum")?.Value ?? 0;

                Console.WriteLine($"Webshop {webshopId} has total cut sum: {totalCutSum}");

                merchantScores.Add(new Shared.Dto.MerchantScore.MerchantScore
                {
                    MerchantId = webshopId,
                    Sum = totalCutSum
                });
            }
        }
        else
        {
            Console.WriteLine("No aggregation results found");
        }

        // Ensure that all merchants are included, even those with zero sums
        var allMerchantScores = merchantIds.Select(id => new MerchantScore
        {
            MerchantId = id.ToString(),
            Sum = merchantScores.FirstOrDefault(ms => ms.MerchantId == id.ToString())?.Sum ?? 0
        }).ToList();

        Console.WriteLine($"Returning {allMerchantScores.Count} merchant scores");
        Console.WriteLine($"=== END SIMPLIFIED METHOD ===");

        return allMerchantScores.OrderByDescending(a => a.Sum).ToList();
    }

    // TODO - Replace GetLines with this method
    public async Task<List<MerchantScore>> GetLinesExtended(string index, List<int> merchantIds, string gender,
        int? age = null, int? ageInterval = null)
    {
        Console.WriteLine($"=== EXTENDED METHOD ===");
        Console.WriteLine($"GetLinesExtended called with index: {index}, merchantIds: {string.Join(",", merchantIds)}, gender: {gender}, age: {age}, ageInterval: {ageInterval}");

        if (gender == "Unknown")
        {
            age = null;
        }

        int startSearchAge = 0;
        int endSearchAge = 999;
        int start = 18;
        int end = 69;
        if (ageInterval != null && age != null && age <= end && age >= start)
        {
            int intervalStart = ((int) age - start) / (int) ageInterval * (int) ageInterval + start;
            int intervalEnd = intervalStart + (int) ageInterval - 1;
            intervalEnd = intervalEnd > end ? end : intervalEnd;

            startSearchAge = intervalStart;
            endSearchAge = intervalEnd;
        }
        else
        {
            if (age >= end)
            {
                startSearchAge = 69;
                endSearchAge = 999;
            }
            else if (age == null)
            {
                startSearchAge = 0;
                endSearchAge = 999;
            }
            else
            {
                startSearchAge = 0;
                endSearchAge = start - 1;
            }
        }

        Console.WriteLine($"Age range calculated: {startSearchAge} to {endSearchAge}");

        // Convert merchant IDs to strings for Elasticsearch
        var merchantIdStrings = merchantIds.Select(id => id.ToString()).ToList();

        // Step 1: Fetch the latest order date for each merchant using composite aggregation
        var latestOrderResponse = await elasticClient.SearchAsync<ElasticInvoiceLineMasterEvent>(s => s
            .Index(index)
            .Size(0)
            .Query(q => q
                .Bool(b => b
                    .Filter(
                        f => f.Terms(t => t
                            .Field(field => field.Invoice_line.Webshop_id)
                            .Terms(merchantIdStrings)
                        ),
                        f => f.Range(r => r
                            .Field(field => field.Invoice_line.Age)
                            .GreaterThanOrEquals(startSearchAge)
                            .LessThanOrEquals(endSearchAge)
                        ),
                        f => f.Term(t => t
                            .Field(field => field.Invoice_line.Gender)
                            .Value(gender)
                        )
                    )
                )
            )
            .Aggregations(aggs => aggs
                .Composite("by_merchant", c => c
                    .Size(500) // Increase size to ensure all merchants are retrieved
                    .Sources(cs => cs
                        .Terms("webshop_id", ts => ts
                            .Field(f => f.Invoice_line.Webshop_id)
                        )
                    )
                    .Aggregations(subAggs => subAggs
                        .Max("latest_order_date", max => max
                            .Field(f => f.Invoice_line.Order_date)
                        )
                    )
                )
            )
        );

        Console.WriteLine($"Latest order response - IsValid: {latestOrderResponse.IsValid}, Total hits: {latestOrderResponse.Total}");

        if (!latestOrderResponse.IsValid)
        {
            Console.WriteLine($"Elasticsearch query failed: {latestOrderResponse.OriginalException?.Message ?? latestOrderResponse.ServerError?.ToString()}");
        }

        var merchantScores = new List<MerchantScore>();
        var byMerchantBuckets = latestOrderResponse.Aggregations.Composite("by_merchant")?.Buckets;

        Console.WriteLine($"Found {byMerchantBuckets?.Count ?? 0} merchant buckets in aggregation");

        // Step 2: Calculate sums based on a lookback period from the latest order date for each merchant
        if (byMerchantBuckets != null)
        {
            foreach (var bucket in byMerchantBuckets)
            {
                var webshopId = bucket.Key["webshop_id"]?.ToString();
                var latestOrderDate = bucket.Max("latest_order_date")?.ValueAsString;

                Console.WriteLine($"Processing merchant {webshopId} with latest order date: {latestOrderDate}");

                if (!string.IsNullOrEmpty(webshopId) && !string.IsNullOrEmpty(latestOrderDate))
                {
                    // Convert latestOrderDate string to DateTime
                    if (DateTime.TryParse(latestOrderDate, out var latestOrderDateTime))
                    {
                        // Use a 30-day lookback period from the latest order date
                        var lookbackDays = 30;
                        var startDate = latestOrderDateTime.AddDays(-lookbackDays);
                        var endDate = latestOrderDateTime.AddDays(1); // Include the latest order date

                        Console.WriteLine($"Querying for merchant {webshopId} from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd} (30-day lookback)");

                        // Query to sum total_cut for the merchant based on its lookback period
                        var sumResponse = await elasticClient.SearchAsync<ElasticInvoiceLineMasterEvent>(s => s
                            .Index(index)
                            .Size(0)
                            .Query(q => q
                                .Bool(b => b
                                    .Filter(
                                        f => f.Term(t => t
                                            .Field(field => field.Invoice_line.Webshop_id)
                                            .Value(webshopId)
                                        ),
                                        f => f.DateRange(dt => dt
                                            .Field(field => field.Invoice_line.Order_date)
                                            .GreaterThanOrEquals(startDate)
                                            .LessThanOrEquals(endDate)
                                        ),
                                        f => f.Range(r => r
                                            .Field(field => field.Invoice_line.Age)
                                            .GreaterThanOrEquals(startSearchAge)
                                            .LessThanOrEquals(endSearchAge)
                                        ),
                                        f => f.Term(t => t
                                            .Field(field => field.Invoice_line.Gender)
                                            .Value(gender)
                                        )
                                    )
                                )
                            )
                            .Aggregations(aggs => aggs
                                .Sum("total_cut_sum", sumAgg => sumAgg
                                    .Field(f => f.Invoice_line.Total_cut)
                                )
                            )
                        );

                        var totalCutSum = sumResponse.Aggregations.Sum("total_cut_sum")?.Value ?? 0;

                        Console.WriteLine($"Merchant {webshopId} total cut sum: {totalCutSum}");

                        merchantScores.Add(new Shared.Dto.MerchantScore.MerchantScore
                        {
                            MerchantId = webshopId,
                            Sum = totalCutSum
                        });
                    }
                    else
                    {
                        Console.WriteLine($"Failed to parse latest order date: {latestOrderDate} for merchant {webshopId}");
                    }
                }
                else
                {
                    Console.WriteLine("Missing webshopId or latestOrderDate for bucket");
                }
            }
        }

        // Ensure that all merchants are included, even those with zero sums
        var allMerchantScores = merchantIds.Select(id => new MerchantScore
        {
            MerchantId = id.ToString(),
            Sum = merchantScores.FirstOrDefault(ms => ms.MerchantId == id.ToString())?.Sum ?? 0
        }).ToList();

        Console.WriteLine($"Returning {allMerchantScores.Count} merchant scores");
        Console.WriteLine($"=== END EXTENDED METHOD ===");

        return allMerchantScores.OrderByDescending(a => a.Sum).ToList();
    }

    public async Task<List<MerchantScore>> InvoiceStats(string index, DateTime from, DateTime to, List<int> merchantIds)
    {
        var merchantScores = new List<MerchantScore>();
        var searchResponse = await elasticClient.SearchAsync<ElasticInvoiceLineMasterEvent>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Invoice_line,
                        f => f.Invoice_line!.Webshop_id,
                        f => f.Invoice_line.Email,
                        f => f.Invoice_line.Gender,
                        f => f.Invoice_line.Order_date,
                        f => f.Invoice_line.Total_cut,
                        f => f.Invoice_line.Age
                    )
                )
            )
            .Index(index)
            .Query(q => q
                .Bool(b => b
                    .Filter(
                        f => f.Terms(t => t
                            .Field(field => field.Invoice_line.Webshop_id)
                            .Terms(merchantIds)
                        ),
                        f => f.DateRange(dt => dt
                            .Field(field => field.Invoice_line.Order_date)
                            .GreaterThanOrEquals(from)
                            .LessThanOrEquals(to))
                    )
                )
            )
            .Aggregations(aggs => aggs
                .Terms("by_webshop", webshopAgg => webshopAgg
                    .Field(f => f.Invoice_line!.Webshop_id)
                    .Size(10000)
                    .Aggregations(webshopSubAggs => webshopSubAggs
                        .Sum("total_cut_sum", sumAgg => sumAgg
                            .Field(f => f.Invoice_line!.Total_cut)
                        )
                    )
                )
            )
        );

        var byWebshopAggregation = searchResponse.Aggregations.Terms("by_webshop");
        if (byWebshopAggregation != null)
        {
            foreach (var webshopBucket in byWebshopAggregation.Buckets)
            {
                var webshopId = webshopBucket.Key;

                // Get the sum of Total_cut for each webshop
                var totalCutSum = webshopBucket.Sum("total_cut_sum")?.Value ?? 0;

                merchantScores.Add(new Shared.Dto.MerchantScore.MerchantScore
                {
                    MerchantId = webshopId,
                    Sum = totalCutSum
                });
            }
        }

        return merchantScores.OrderByDescending(a => a.Sum).ToList();
    }
    

    public async Task<List<PriceDropScoreProductDto>> GetPriceDropScores(int merchantId)
    {
        throw new NotImplementedException();
    }
    
    public async Task<List<DiscountElastic>> Test()
    {
        var discounts = new List<DiscountElastic>();
        var index = "customers-discounts-events";
        var searchResponse = await elasticClient.SearchAsync<DiscountElastic>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Discount.Event_type,
                        f => f.TimeStamp,
                        f => f.Shop_event.Webshop_id,
                        f => f.Customer.Email,
                        f => f.Customer.Viabill_debtor_account_id,
                        f => f.Discount.Discount_id,
                        f => f.Discount.Page_number,
                        f => f.Discount.Event_type
                    )
                )
            )
            .Index(index)
            .Query(q =>
                q.MatchPhrase(m => m
                    .Field(f => f.Discount.Event_type)
                    .Query("discount_overview_viewed")
                )
                && q.Bool(b => b
                    .Filter(f =>
                        f.DateRange(dt => dt
                            .Field(field => field.TimeStamp)
                            .GreaterThanOrEquals(new DateTime(2024, 1, 1))
                            .LessThanOrEquals(new DateTime(2024, 10, 15))
                        )))
            )
            .Size(10_000)
            .Sort(srt => srt.Descending(f => f.TimeStamp))
            .Scroll("1m")
        );

        discounts.AddRange(searchResponse.Documents);
        while (searchResponse.IsValid && searchResponse.Documents.Count % 10000 == 0 &&
               searchResponse.Documents.Count != 0)
        {
            searchResponse = await elasticClient.ScrollAsync<DiscountElastic>("1m", searchResponse.ScrollId);
            discounts.AddRange(searchResponse.Documents);
        }

        await elasticClient.ClearScrollAsync(new ClearScrollRequest(searchResponse.ScrollId));
        return discounts.ToList();
    }

    // Diagnostic method to test basic queries
    public async Task<object> DiagnosticQuery(string index, List<int> merchantIds, string gender, int? age = null)
    {
        var merchantIdStrings = merchantIds.Select(id => id.ToString()).ToList();
        
        Console.WriteLine($"=== DIAGNOSTIC QUERY ===");
        Console.WriteLine($"Index: {index}");
        Console.WriteLine($"MerchantIds: {string.Join(",", merchantIdStrings)}");
        Console.WriteLine($"Gender: {gender}");
        Console.WriteLine($"Age: {age}");
        Console.WriteLine($"Total Merchants: {merchantIds.Count}");

        // Calculate age range like the other methods
        int startSearchAge = 0;
        int endSearchAge = 999;
        int start = 18;
        int end = 69;
        if (age != null && age <= end && age >= start)
        {
            int intervalStart = ((int) age - start) / 4 * 4 + start;
            int intervalEnd = intervalStart + 4 - 1;
            intervalEnd = intervalEnd > end ? end : intervalEnd;
            startSearchAge = intervalStart;
            endSearchAge = intervalEnd;
        }
        else if (age >= end)
        {
            startSearchAge = 69;
            endSearchAge = 999;
        }
        else if (age == null)
        {
            startSearchAge = 0;
            endSearchAge = 999;
        }
        else
        {
            startSearchAge = 0;
            endSearchAge = start - 1;
        }

        Console.WriteLine($"Age range: {startSearchAge} to {endSearchAge}");

        // Test date ranges
        var now = DateTime.UtcNow;
        var simplifiedFromDate = now.AddMonths(-6);
        var simplifiedToDate = now;
        
        Console.WriteLine($"Simplified method date range: {simplifiedFromDate} to {simplifiedToDate}");

        // First, let's see if there are any documents at all for these merchants
        var basicCountResponse = await elasticClient.CountAsync<ElasticInvoiceLineMasterEvent>(s => s
            .Index(index)
            .Query(q => q
                .Terms(t => t
                    .Field(f => f.Invoice_line.Webshop_id)
                    .Terms(merchantIdStrings)
                )
            )
        );

        Console.WriteLine($"Basic count for merchants: {basicCountResponse.Count}");

        // Count with all filters like simplified method
        var simplifiedFilterCount = await elasticClient.CountAsync<ElasticInvoiceLineMasterEvent>(s => s
            .Index(index)
            .Query(q => q
                .Bool(b => b
                    .Filter(
                        f => f.Terms(t => t
                            .Field(f => f.Invoice_line.Webshop_id)
                            .Terms(merchantIdStrings)
                        ),
                        f => f.DateRange(dt => dt
                            .Field(field => field.Invoice_line.Order_date)
                            .GreaterThanOrEquals(simplifiedFromDate)
                            .LessThanOrEquals(simplifiedToDate)),
                        f => f.Range(r => r
                            .Field(field => field.Invoice_line.Age)
                            .GreaterThanOrEquals(startSearchAge)
                            .LessThanOrEquals(endSearchAge)
                        ),
                        f => f.Term(t => t
                            .Field(field => field.Invoice_line.Gender)
                            .Value(gender)
                        )
                    )
                )
            )
        );

        Console.WriteLine($"Count with simplified method filters: {simplifiedFilterCount.Count}");

        // Get latest order dates for each merchant (like extended method does)
        var latestOrderResponse = await elasticClient.SearchAsync<ElasticInvoiceLineMasterEvent>(s => s
            .Index(index)
            .Size(0)
            .Query(q => q
                .Bool(b => b
                    .Filter(
                        f => f.Terms(t => t
                            .Field(field => field.Invoice_line.Webshop_id)
                            .Terms(merchantIdStrings)
                        ),
                        f => f.Range(r => r
                            .Field(field => field.Invoice_line.Age)
                            .GreaterThanOrEquals(startSearchAge)
                            .LessThanOrEquals(endSearchAge)
                        ),
                        f => f.Term(t => t
                            .Field(field => field.Invoice_line.Gender)
                            .Value(gender)
                        )
                    )
                )
            )
            .Aggregations(aggs => aggs
                .Composite("by_merchant", c => c
                    .Size(500)
                    .Sources(cs => cs
                        .Terms("webshop_id", ts => ts
                            .Field(f => f.Invoice_line.Webshop_id)
                        )
                    )
                    .Aggregations(subAggs => subAggs
                        .Max("latest_order_date", max => max
                            .Field(f => f.Invoice_line.Order_date)
                        )
                    )
                )
            )
        );

        var latestDates = new Dictionary<string, DateTime>();
        var byMerchantBuckets = latestOrderResponse.Aggregations.Composite("by_merchant")?.Buckets;
        if (byMerchantBuckets != null)
        {
            Console.WriteLine($"Latest order dates per merchant:");
            foreach (var bucket in byMerchantBuckets)
            {
                var webshopId = bucket.Key["webshop_id"]?.ToString();
                var latestOrderDate = bucket.Max("latest_order_date")?.ValueAsString;
                
                if (!string.IsNullOrEmpty(webshopId) && !string.IsNullOrEmpty(latestOrderDate))
                {
                    if (DateTime.TryParse(latestOrderDate, out var latestOrderDateTime))
                    {
                        latestDates[webshopId] = latestOrderDateTime;
                        Console.WriteLine($"  Merchant {webshopId}: {latestOrderDateTime:yyyy-MM-dd}");
                    }
                }
            }
        }

        // Now let's see with gender filter using exact term
        var genderCountResponse = await elasticClient.CountAsync<ElasticInvoiceLineMasterEvent>(s => s
            .Index(index)
            .Query(q => q
                .Bool(b => b
                    .Filter(
                        f => f.Terms(t => t
                            .Field(f => f.Invoice_line.Webshop_id)
                            .Terms(merchantIdStrings)
                        ),
                        f => f.Term(t => t
                            .Field(f => f.Invoice_line.Gender)
                            .Value(gender)
                        )
                    )
                )
            )
        );

        Console.WriteLine($"Count with gender filter: {genderCountResponse.Count}");

        // Get a sample document to see the structure
        var sampleResponse = await elasticClient.SearchAsync<ElasticInvoiceLineMasterEvent>(s => s
            .Index(index)
            .Query(q => q
                .Terms(t => t
                    .Field(f => f.Invoice_line.Webshop_id)
                    .Terms(merchantIdStrings)
                )
            )
            .Size(1)
        );

        var sampleDoc = sampleResponse.Documents.FirstOrDefault();
        if (sampleDoc != null)
        {
            Console.WriteLine($"Sample document found:");
            Console.WriteLine($"  WebshopId: {sampleDoc.Invoice_line?.Webshop_id}");
            Console.WriteLine($"  Gender: {sampleDoc.Invoice_line?.Gender}");
            Console.WriteLine($"  Age: {sampleDoc.Invoice_line?.Age}");
            Console.WriteLine($"  TotalCut: {sampleDoc.Invoice_line?.Total_cut}");
            Console.WriteLine($"  OrderDate: {sampleDoc.Invoice_line?.Order_date}");
        }
        else
        {
            Console.WriteLine("No sample document found!");
        }

        // Test aggregation without any filters
        var basicAggResponse = await elasticClient.SearchAsync<ElasticInvoiceLineMasterEvent>(s => s
            .Index(index)
            .Size(0)
            .Query(q => q
                .Terms(t => t
                    .Field(f => f.Invoice_line.Webshop_id)
                    .Terms(merchantIdStrings)
                )
            )
            .Aggregations(aggs => aggs
                .Terms("by_webshop", webshopAgg => webshopAgg
                    .Field(f => f.Invoice_line!.Webshop_id)
                    .Size(10)
                    .Aggregations(webshopSubAggs => webshopSubAggs
                        .Sum("total_cut_sum", sumAgg => sumAgg
                            .Field(f => f.Invoice_line!.Total_cut)
                        )
                    )
                )
            )
        );

        var basicAggResults = basicAggResponse.Aggregations.Terms("by_webshop")?.Buckets
            .Select(b => new { WebshopId = b.Key, TotalCut = b.Sum("total_cut_sum")?.Value ?? 0 })
            .ToList();

        Console.WriteLine($"Basic aggregation results:");
        if (basicAggResults != null && basicAggResults.Any())
        {
            foreach (var result in basicAggResults)
            {
                Console.WriteLine($"  WebshopId: {result.WebshopId}, TotalCut: {result.TotalCut}");
            }
        }
        else
        {
            Console.WriteLine("  No aggregation results found!");
        }

        Console.WriteLine($"=== END DIAGNOSTIC ===");

        return new
        {
            BasicCount = basicCountResponse.Count,
            GenderCount = genderCountResponse.Count,
            SimplifiedFilterCount = simplifiedFilterCount.Count,
            SimplifiedDateRange = new { From = simplifiedFromDate, To = simplifiedToDate },
            LatestOrderDates = latestDates,
            SampleDocument = sampleDoc?.Invoice_line,
            BasicAggregation = basicAggResults
        };
    }

    // Enhanced method with manual age-based decay scoring
    public async Task<List<MerchantScore>> GetLinesExtendedWithAgeDecay(string index, List<int> merchantIds, string gender,
        int? age = null, int? ageInterval = null)
    {
        Console.WriteLine($"=== EXTENDED METHOD WITH AGE DECAY ===");
        Console.WriteLine($"GetLinesExtendedWithAgeDecay called with index: {index}, merchantIds: {string.Join(",", merchantIds)}, gender: {gender}, age: {age}, ageInterval: {ageInterval}");

        var merchantIdStrings = merchantIds.Select(id => id.ToString()).ToList();

        // Step 1: Get latest order dates (NO demographic filters)
        var latestOrderResponse = await elasticClient.SearchAsync<ElasticInvoiceLineMasterEvent>(s => s
            .Index(index)
            .Size(0)
            .Query(q => q
                .Terms(t => t
                    .Field(field => field.Invoice_line.Webshop_id)
                    .Terms(merchantIdStrings)
                )
            )
            .Aggregations(aggs => aggs
                .Composite("by_merchant", c => c
                    .Size(500)
                    .Sources(cs => cs
                        .Terms("webshop_id", ts => ts
                            .Field(f => f.Invoice_line.Webshop_id)
                        )
                    )
                    .Aggregations(subAggs => subAggs
                        .Max("latest_order_date", max => max
                            .Field(f => f.Invoice_line.Order_date)
                        )
                    )
                )
            )
        );

        Console.WriteLine($"Latest order query valid: {latestOrderResponse.IsValid}");

        var merchantScores = new List<MerchantScore>();
        var byMerchantBuckets = latestOrderResponse.Aggregations.Composite("by_merchant")?.Buckets;

        if (byMerchantBuckets != null)
        {
            foreach (var bucket in byMerchantBuckets)
            {
                var webshopId = bucket.Key["webshop_id"]?.ToString();
                var latestOrderDate = bucket.Max("latest_order_date")?.ValueAsString;

                if (!string.IsNullOrEmpty(webshopId) && !string.IsNullOrEmpty(latestOrderDate) && 
                    DateTime.TryParse(latestOrderDate, out var latestOrderDateTime))
                {
                    var startDate = latestOrderDateTime.AddDays(-30);
                    var endDate = latestOrderDateTime.AddDays(1);

                    Console.WriteLine($"Merchant {webshopId}: {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");

                    // Get documents and calculate manual weights
                    var documentsResponse = await elasticClient.SearchAsync<ElasticInvoiceLineMasterEvent>(s => s
                        .Index(index)
                        .Size(1000)
                        .Query(q => q
                            .Bool(b => b
                                .Filter(
                                    f => f.Term(t => t.Field(field => field.Invoice_line.Webshop_id).Value(webshopId)),
                                    f => f.DateRange(dt => dt.Field(field => field.Invoice_line.Order_date).GreaterThanOrEquals(startDate).LessThanOrEquals(endDate)),
                                    f => f.Term(t => t.Field(field => field.Invoice_line.Gender).Value(gender))
                                )
                            )
                        )
                    );

                    double totalWeightedSum = 0;
                    int docCount = 0;

                    foreach (var doc in documentsResponse.Documents)
                    {
                        if (doc.Invoice_line?.Age != null && doc.Invoice_line?.Total_cut != null)
                        {
                            var customerAge = doc.Invoice_line.Age;
                            var totalCut = doc.Invoice_line.Total_cut;
                            
                            double weight = 1.0; // Default weight
                            
                            if (age.HasValue)
                            {
                                // Calculate exponential decay: exp(-distance / scale)
                                var distance = Math.Abs((decimal)(customerAge - age.Value));
                                var scale = (ageInterval ?? 10) / 3.0; // Smaller scale for steeper decay
                                weight = Math.Exp(-(double)distance / scale);
                                weight = Math.Max(weight, 0.05); // Minimum 5% weight
                            }
                            
                            var weightedValue = totalCut * (decimal)weight;
                            totalWeightedSum += (double)weightedValue;
                            docCount++;
                            
                            if (docCount <= 5) // Log first few for debugging
                            {
                                Console.WriteLine($"  Age {customerAge}, Distance {Math.Abs((decimal)(customerAge - (age ?? 0)))}, Weight {weight:F3}, Cut {totalCut}, Weighted {weightedValue:F2}");
                            }
                        }
                    }

                    Console.WriteLine($"Merchant {webshopId}: {docCount} docs, weighted sum: {totalWeightedSum:F2}");

                    merchantScores.Add(new MerchantScore
                    {
                        MerchantId = webshopId,
                        Sum = totalWeightedSum
                    });
                }
            }
        }

        // Include all merchants
        var allMerchantScores = merchantIds.Select(id => new MerchantScore
        {
            MerchantId = id.ToString(),
            Sum = merchantScores.FirstOrDefault(ms => ms.MerchantId == id.ToString())?.Sum ?? 0
        }).ToList();

        Console.WriteLine($"=== END AGE DECAY METHOD ===");
        return allMerchantScores.OrderByDescending(a => a.Sum).ToList();
    }
}