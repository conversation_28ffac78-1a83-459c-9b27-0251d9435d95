using General_Services.Models.Models.Scores;
using Marlin_OS_Integration_API.Models.Order;
using Shared.Dto.Campaign;
using Shared.Dto.Curated.Product;
using Shared.Dto.Dashboard;
using Shared.Dto.MerchantScore;
using Shared.Elastic.Behavior;
using Shared.Elastic.Discounts;
using Shared.Elastic.Models.Behavior;
using Shared.Models;
using Shared.Models.Elastic.ElasticCampaignsMailsOpens;

namespace Shared.Elastic.Elastic;

public interface IElasticService
{
    Task<double> CustomerOrdersRevenueElastic(int partnerId, DateTime lookBack);
    Task<List<MerchantScore>> GetMerchantScore(string gender, int? age, int? ageInterval);

    Task<Dictionary<DateTime, long>> ClicksWebshopDayCount(DateTime from, DateTime to);
    Task<Dictionary<DateTime, long>> OpensClickWebshopDayCount(DateTime from, DateTime to);

    Task<List<Models.ElasticCampaignsMailsClicks.ElasticCampaignsMailsClicks>> ClicksUniqueWebshopDayCount(
        DateTime from, DateTime to);

    Task<List<CuratedProductEvents>> PageEventsTopProducts(int merchantId, int days, int productAmount = 10000);
    Task<List<CuratedProductEvents>> OrderTopProducts(int merchantId, int days, int productAmount);
    Task<(long eventsEmail, long eventsNoEmail)> CustomerPagesAmount(int merchantId, int days);
    Task<ElasticBehaviorEvent?> NewestCustomerPageEvents(int merchantId, string type);
    Task<List<ElasticOrderEvent>> CustomerOrdersElastic(string email, string phone, int lookBackYear);
    Task<List<ElasticPartnerOrderEvent>> ViaBillOrdersElastic(string email, int lookBackYear);
    Task<long> DiscountEventsElastic(string discountDetailsViewed, DateTime firstDayofMonth);
    Task<DashboardProfileMQLGraph> CustomersAudienceCount(int year);
    Task<DashboardProfileMQLGraph> CustomersAudienceCount(int partnerId, DateTime from, DateTime to);

    Task<List<DiscountElastic>>
        CustomersDiscountsElastic(string merchantId, string email, string type, int lookBackDays);

    Task<long> DiscountEventsElastic(long discountId, string eventType);
    Task<long> DiscountEventsRedirectElastic(long discountId);
    Task<List<MerchantScore>> InvoiceStats(string index, DateTime from, DateTime to, List<int> merchantIds);
    Task<List<MerchantScore>> GetLines(string index, string gender, int? age = null, int? ageInterval = null);

    Task<List<MerchantScore>> GetLinesExtended(string index, List<int> merchantIds, string gender, int? age = null,
        int? ageInterval = null);
    Task<List<MerchantScore>> GetLinesExtendedSimplified(string index, List<int> merchantIds, string gender, int? age = null,
        int? ageInterval = null);
    Task<List<MerchantScore>> GetLinesExtendedWithAgeDecay(string index, List<int> merchantIds, string gender, int? age = null,
        int? ageInterval = null);
    Task<object> DiagnosticQuery(string index, List<int> merchantIds, string gender, int? age = null);
    Task<List<PriceDropScoreProductDto>> GetPriceDropScores(int merchantId);
    
    
    // TEST!
    Task<List<DiscountElastic>> Test();
}