{"providerId": "Microsoft.Tools.ServiceModel.Svcutil", "version": "2.1.0", "ExtendedData": {"inputs": ["https://api.hostedshop.io/service.wsdl"], "namespaceMappings": ["*, Shared.WebServiceService"], "outputFile": "WebServiceService.cs", "references": ["<PERSON>Mapper, {AutoMapper, 12.0.1}", "AutoMapper.Extensions.Microsoft.DependencyInjection, {AutoMapper.Extensions.Microsoft.DependencyInjection, 12.0.1}", "Azure.Core, {Azure.Core, 1.25.0}", "Azure.Identity, {Azure.Identity, 1.7.0}", "Elasticsearch.Net, {Elasticsearch.Net, 7.17.5}", "Microsoft.Bcl.AsyncInterfaces, {Microsoft.Bcl.AsyncInterfaces, 1.1.1}", "Microsoft.Data.SqlClient, {Microsoft.Data.SqlClient, 5.1.1}", "Microsoft.EntityFrameworkCore, {Microsoft.EntityFrameworkCore, 7.0.11}", "Microsoft.EntityFrameworkCore.Abstractions, {Microsoft.EntityFrameworkCore.Abstractions, 7.0.11}", "Microsoft.EntityFrameworkCore.Relational, {Microsoft.EntityFrameworkCore.Relational, 7.0.11}", "Microsoft.EntityFrameworkCore.SqlServer, {Microsoft.EntityFrameworkCore.SqlServer, 7.0.11}", "Microsoft.Extensions.Caching.Abstractions, {Microsoft.Extensions.Caching.Abstractions, 7.0.0}", "Microsoft.Extensions.Caching.Memory, {Microsoft.Extensions.Caching.Memory, 7.0.0}", "Microsoft.Extensions.Configuration.Abstractions, {Microsoft.Extensions.Configuration.Abstractions, 7.0.0}", "Microsoft.Extensions.Configuration.Binder, {Microsoft.Extensions.Configuration.Binder, 7.0.0}", "Microsoft.Extensions.DependencyInjection, {Microsoft.Extensions.DependencyInjection, 7.0.0}", "Microsoft.Extensions.DependencyInjection.Abstractions, {Microsoft.Extensions.DependencyInjection.Abstractions, 7.0.0}", "Microsoft.Extensions.DependencyModel, {Microsoft.Extensions.DependencyModel, 7.0.0}", "Microsoft.Extensions.FileProviders.Abstractions, {Microsoft.Extensions.FileProviders.Abstractions, 7.0.0}", "Microsoft.Extensions.Hosting.Abstractions, {Microsoft.Extensions.Hosting.Abstractions, 7.0.0}", "Microsoft.Extensions.Http, {Microsoft.Extensions.Http, 2.1.0}", "Microsoft.Extensions.Logging, {Microsoft.Extensions.Logging, 7.0.0}", "Microsoft.Extensions.Logging.Abstractions, {Microsoft.Extensions.Logging.Abstractions, 7.0.0}", "Microsoft.Extensions.Options, {Microsoft.Extensions.Options, 7.0.0}", "Microsoft.Extensions.Primitives, {Microsoft.Extensions.Primitives, 7.0.0}", "Microsoft.Identity.Client, {Microsoft.Identity.Client, 4.47.2}", "Microsoft.Identity.Client.Extensions.Msal, {Microsoft.Identity.Client.Extensions.Msal, 2.19.3}", "Microsoft.IdentityModel.Abstractions, {Microsoft.IdentityModel.Abstractions, 6.24.0}", "Microsoft.IdentityModel.JsonWebTokens, {Microsoft.IdentityModel.JsonWebTokens, 6.24.0}", "Microsoft.IdentityModel.Logging, {Microsoft.IdentityModel.Logging, 6.24.0}", "Microsoft.IdentityModel.Protocols, {Microsoft.IdentityModel.Protocols, 6.24.0}", "Microsoft.IdentityModel.Protocols.OpenIdConnect, {Microsoft.IdentityModel.Protocols.OpenIdConnect, 6.24.0}", "Microsoft.IdentityModel.Tokens, {Microsoft.IdentityModel.Tokens, 6.24.0}", "Microsoft.SqlServer.Server, {Microsoft.SqlServer.Server, 1.0.0}", "Nest, {NEST, 7.17.5}", "Newtonsoft.J<PERSON>, {Newtonsoft<PERSON>J<PERSON>, 13.0.3}", "Pipelines.Sockets.Unofficial, {Pipelines.Sockets.Unofficial, 2.2.8}", "RabbitMQ.Client, {RabbitMQ.Client, 6.5.0}", "<PERSON><PERSON><PERSON>, {Serilog, 2.12.0}", "Serilog.AspNetCore, {Serilog.AspNetCore, 7.0.0}", "Serilog.Exceptions, {Serilog.Exceptions, 8.4.0}", "Serilog.Extensions.Hosting, {Serilog.Extensions.Hosting, 7.0.0}", "Serilog.Extensions.Logging, {Serilog.Extensions.Logging, 7.0.0}", "Serilog.Formatting.Compact, {Serilog.Formatting.Compact, 1.1.0}", "Serilog.Formatting.Elasticsearch, {Serilog.Formatting.Elasticsearch, 9.0.3}", "Serilog.Settings.Configuration, {Serilog.Settings.Configuration, 7.0.0}", "Serilog.<PERSON><PERSON>.<PERSON>, {Serilog.Sinks.Console, 4.1.0}", "Serilog.Sinks.Debug, {Serilog.Sinks.Debug, 2.0.0}", "Serilog.Sinks.Elasticsearch, {Serilog.Sinks.Elasticsearch, 9.0.3}", "Serilog.Sinks.File, {Serilog.Sinks.File, 5.0.0}", "Serilog.Sinks.PeriodicBatching, {Serilog.Sinks.PeriodicBatching, 3.1.0}", "SerilogTim<PERSON>, {SerilogTim<PERSON>, 3.0.1}", "ShopifySharp, {ShopifySharp, 6.4.0}", "StackExchange.Redis, {StackExchange.Redis, 2.6.122}", "System.Diagnostics.DiagnosticSource, {System.Diagnostics.DiagnosticSource, 6.0.0}", "System.IdentityModel.Tokens.Jwt, {System.IdentityModel.Tokens.Jwt, 6.24.0}", "System.IO.Pipelines, {System.IO.Pipelines, 5.0.1}", "System.Memory.Data, {System.Memory.Data, 1.0.2}", "System.Runtime, {System.Runtime, 4.3.0}", "System.Runtime.CompilerServices.Unsafe, {System.Runtime.CompilerServices.Unsafe, 6.0.0}", "System.Security.Cryptography.Cng, {System.Security.Cryptography.Cng, 5.0.0}", "System.Security.Cryptography.ProtectedData, {System.Security.Cryptography.ProtectedData, 6.0.0}", "System.Text.Encoding, {System.Text.Encoding, 4.3.0}", "System.Text.Encodings.Web, {System.Text.Encodings.Web, 7.0.0}", "System.<PERSON>.<PERSON>, {System.Text.<PERSON>, 7.0.0}", "System.Threading.Channels, {System.Threading.Channels, 7.0.0}"], "targetFramework": "net7.0", "typeReuseMode": "All"}}