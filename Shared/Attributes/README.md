# Partner Authentication Exemption

This document explains how to use the `PartnerAuthExempt` attribute to exempt controllers or actions from Partner authentication requirements.

## Overview

The `PartnerAuthExempt` attribute allows you to mark specific controllers or actions as exempt from Partner authentication requirements. When a request is made to an exempt endpoint, the middleware will:

1. Set the default Partner ID (52876)
2. Mark the request as exempt (`IsPartnerIdExempt = true`)
3. Mark the Partner ID as found by default (`IsFoundByDefault = true`)

This is useful for endpoints that:
- Need to be publicly accessible (webhooks, callbacks)
- Are used by third-party integrations
- Don't require Partner context
- Handle authentication in a different way

## Usage

### Exempting an Entire Controller

```csharp
using Shared.Attributes;

[ApiController]
[Route("api/public")]
[PartnerAuthExempt]  // Apply to the entire controller
public class PublicApiController : BasePartnerController
{
    // All actions in this controller will be exempt
}
```

### Exempting Specific Actions

```csharp
using Shared.Attributes;

[ApiController]
[Route("api/mixed")]
public class MixedAuthController : BasePartnerController
{
    [HttpGet("public")]
    [PartnerAuthExempt]  // Apply to just this action
    public IActionResult PublicEndpoint()
    {
        // This action is exempt
        return Ok();
    }
    
    [HttpGet("protected")]
    public IActionResult ProtectedEndpoint()
    {
        // This action requires Partner authentication
        return Ok();
    }
}
```

## Checking Exemption Status

You can check if the current request is exempt from Partner authentication:

```csharp
public class YourController : BasePartnerController
{
    public IActionResult YourAction()
    {
        if (IsPartnerIdExempt)
        {
            // This request is exempt from Partner authentication
        }
        else
        {
            // This request requires Partner authentication
            var partnerId = PartnerId;
        }
    }
}
```

## Common Use Cases

### Webhooks

```csharp
[ApiController]
[Route("webhooks")]
[PartnerAuthExempt]
public class WebhooksController : BasePartnerController
{
    [HttpPost("shopify")]
    public IActionResult ShopifyWebhook()
    {
        // Handle Shopify webhook
        return Ok();
    }
}
```

### Health Checks

```csharp
[ApiController]
[Route("health")]
[PartnerAuthExempt]
public class HealthController : ControllerBase
{
    [HttpGet]
    public IActionResult Check()
    {
        return Ok("Healthy");
    }
}
```

### Authentication Endpoints

```csharp
[ApiController]
[Route("auth")]
[PartnerAuthExempt]
public class AuthController : ControllerBase
{
    [HttpPost("login")]
    public IActionResult Login()
    {
        // Handle login
        return Ok();
    }
}
```

## Best Practices

1. Use the attribute at the controller level when all actions should be exempt
2. Use the attribute at the action level when only specific actions should be exempt
3. Document why an endpoint is exempt in a comment
4. Consider security implications when exempting endpoints
5. Remember that exempt endpoints still receive a default Partner ID 