namespace Shared.Models.Customer;

public class CustomerDto
{
    public long Id { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastModifiedDate { get; set; }
    public bool Active { get; set; }
    public string Email { get; set; } = null!;
    public string FirstName { get; set; } = null!;
    public string LastName { get; set; } = null!;
    public string? ZipCode { get; set; }
    public string Country { get; set; } = null!;
    public bool MarketingStatus { get; set; }
    public string PhoneNumber { get; set; } = null!;
    public decimal? CreditAvailability { get; set; }
    public string? PartnerGuid { get; set; }
    public string? PartnerId { get; set; }
    public string? PartnerProduct { get; set; }
    public byte? PartnerInstallments { get; set; }
    public int MissedOpenMails { get; set; }
    public string? Gender { get; set; } = null!;
    public byte? Age { get; set; }
    public List<CustomerExposureDto> CustomerExposures { get; set; } = [];
    public List<CustomerMetumDto> CustomerMeta { get; set; } = [];
}