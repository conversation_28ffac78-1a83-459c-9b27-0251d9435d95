namespace Shared.Models.Customer;

public class CustomerMetumDto
{
    public long Id { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastModifiedDate { get; set; }
    public bool Active { get; set; }
    public string Value { get; set; } = null!;
    public long FkCustomerId { get; set; }
    public int FkMerchantId { get; set; }
    public int FkCustomerMetaTypeId { get; set; }
}