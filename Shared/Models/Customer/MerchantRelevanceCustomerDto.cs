namespace Shared.Models.Customer;

public class MerchantRelevanceCustomerDto
{
    public long Id { get; set; }
    public string PartnerGuid { get; set; }
    public string Email { get; set; }
    public string Gender { get; set; }
    public int? Age { get; set; }
    
    public bool IsAnonymous { get; set; } = false;
    
    public List<CustomerExposureDto> CustomerExposures { get; set; } = [];
}