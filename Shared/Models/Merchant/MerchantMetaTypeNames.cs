namespace Shared.Models.Merchant;

public static class MerchantMetaTypeNames
{
    public const string AddCartEventTimeThreshold = "AddCartEventTimeThreshold";
    public const string ApiHookKey = "ApiHookKey";
    public const string ApiKey = "ApiKey";
    public const string ApiUserKey = "ApiUserKey";
    public const string ApiUserSecret = "ApiUserSecret";
    public const string ApiUrl = "ApiUrl";
    public const string IsAdultOnly = "IsAdultOnly";
    public const string DisablePartnerLogin = "DisablePartnerLogin";
    public const string LogoSrc = "LogoSrc";
    public const string Note = "Note";
    public const string ProductDataEventTimeThreshold = "ProductDataEventTimeThreshold";
    public const string OrderDataEventTimeThreshold = "OrderDataEventTimeThreshold";
    public const string PointOfContact = "PointOfContact";
    public const string ProductLookEventTimeThreshold = "ProductLookEventTimeThreshold";
    public const string RemoveCartEventTimeThreshold = "RemoveCartEventTimeThreshold";
    public const string SiteId = "SiteId";
    public const string TagLine = "TagLine";
    public const string ShopIdentifier = "ShopIdentifier";
    public const string MinimumOrderAmount = "MinimumOrderAmount";
    public const string Assets = "Assets";
    public const string AssetDefaultText = "AssetDefaultText";
    public const string AssetPromoText = "AssetPromoText";
    public const string AssetPromoTextExpiryDate = "AssetPromoTextExpiryDate";
    public const string MinExposureRenewalDays = "MinExposureRenewalDays";
    public const string MarketingStatus = "MarketingStatus";
    public const string AttributionType = "AttributionType";
    public const string CPM = "CPM";
    public const string CPMBudget = "CPMBudget";
    public const string BaseFee = "BaseFee";
    public const string CustomerSegmentFemale = "CustomerSegmentFemale";
    public const string CustomerSegmentMale = "CustomerSegmentMale";
    public const string CustomerSegmentUnknown = "CustomerSegmentUnknown";
    public const string IsCustomer = "IsCustomer";
    public const string CutOffDate = "CutOffDate";
    public const string AffiliateType = "AffiliateType";
    public const string AffiliateMarketingChannelApp = "AffiliateMarketingChannelApp";
    public const string AffiliateMarketingChannelAppUrl = "AffiliateMarketingChannelAppUrl";
    public const string AffiliateMarketingChannelCampaign = "AffiliateMarketingChannelCampaign";
    public const string AffiliateMarketingChannelCampaignUrl = "AffiliateMarketingChannelCampaignUrl";
    
    // Newly Added
    public const string KeepMarketingFeeOnReturns = "KeepMarketingFeeOnReturns";
    public const string CompanyRegistrationNumber = "CompanyRegistrationNumber";
}