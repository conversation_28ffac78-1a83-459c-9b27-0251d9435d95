namespace Shared.Models.Merchant;

public class ProductReturnEventDto
{
    //public string Name { get; set; }
    //public int WebShopId { get; set; }
    //public string? MerchantProductId { get; set; }
    public string Sku { get; set; }

    public string? ParentInternalProductId { get; set; }

    //public string Slug { get; set; }
    //public string PermaLink { get; set; }
    //public DateTime? DateCreated { get; set; }
    //public DateTime? DateUpdated { get; set; }
    //public string Status { get; set; }
    //public string Description { get; set; }
    //public string ShortDescrption { get; set; }
    public decimal? Price { get; set; }

    /*public decimal? RegularPrice { get; set; }
    public decimal? SalePrice { get; set; }
    public decimal? LowestPrice { get; set; }
    public decimal? HighestPrice { get; set; }
    public DateTime? DateOnSaleFrom { get; set; }
    public DateTime? DateOnSaleTo { get; set; }
    public bool? OnSale { get; set; }
    public int? StockQuantity { get; set; }
    public string StockStatus { get; set; }*/
    public string InternalProductId { get; set; }
}