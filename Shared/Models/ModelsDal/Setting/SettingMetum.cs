using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Shared.Models.ModelsDal.Setting;

[Table("SettingMeta", Schema = "setting")]
public partial class SettingMetum
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(255)]
    public string Name { get; set; } = null!;

    [StringLength(255)]
    public string Value { get; set; } = null!;

    [Column("FK_SettingsId")]
    public int? FkSettingsId { get; set; }

    [ForeignKey("FkSettingsId")]
    [InverseProperty("SettingMeta")]
    public virtual Setting? FkSettings { get; set; }
}
