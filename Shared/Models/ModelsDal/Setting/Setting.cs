using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Shared.Models.ModelsDal.Setting;

[Table("Settings", Schema = "setting")]
[Index("Key", "FkPartnerId", Name = "UC_Settings", IsUnique = true)]
public partial class Setting
{
    [StringLength(255)]
    public string Key { get; set; } = null!;

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public int SortOrder { get; set; }

    [StringLength(2000)]
    public string Value { get; set; } = null!;

    [StringLength(2000)]
    public string? Description { get; set; }

    [Column("FK_GroupId")]
    public int FkGroupId { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [Key]
    public int Id { get; set; }

    [ForeignKey("FkGroupId")]
    [InverseProperty("Settings")]
    public virtual Group FkGroup { get; set; } = null!;

    [InverseProperty("FkSettings")]
    public virtual ICollection<SettingMetum> SettingMeta { get; set; } = new List<SettingMetum>();
}
