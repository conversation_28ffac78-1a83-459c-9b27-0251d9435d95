using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace Shared.Models.ModelsDal.Setting;

public partial class SettingDbContext : DbContext
{
    public SettingDbContext(DbContextOptions<SettingDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Group> Groups { get; set; }

    public virtual DbSet<Setting> Settings { get; set; }

    public virtual DbSet<SettingMetum> SettingMeta { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Group>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Groups__3214EC0782E56E20");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<Setting>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Settings__3214EC079A697A08");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkGroup).WithMany(p => p.Settings)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Settings__FK_Gro__4959E263");
        });

        modelBuilder.Entity<SettingMetum>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__SettingM__3214EC0748282592");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkSettings).WithMany(p => p.SettingMeta).HasConstraintName("FK_SettingMeta_Settings");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
