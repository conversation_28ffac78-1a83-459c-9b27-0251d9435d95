using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Shared.Models.ModelsDal.Setting;

[Table("Groups", Schema = "setting")]
public partial class Group
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    [StringLength(255)]
    public string Name { get; set; } = null!;

    [InverseProperty("FkGroup")]
    public virtual ICollection<Setting> Settings { get; set; } = new List<Setting>();
}
