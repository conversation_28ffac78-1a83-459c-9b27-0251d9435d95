using Shared.Dto.Dashboard;

namespace Shared.Models.PartnerPortal;

public class MerchantMonthlyPerformance
{
    public decimal Revenue { get; set; }
    public decimal RevenueLastMonth { get; set; }
    public int OrdersGenerated { get; set; }
    public int OrdersGeneratedLastMonth { get; set; }
    public decimal AverageOrderValue { get; set; }
    public decimal AverageOrderValueLastMonth { get; set; }
    public long AudienceExposed { get; set; }
    public long TotalExposures { get; set; }
    public long TotalExposuresEmail { get; set; }
    public long TotalExposuresDiscount { get; set; }
    public int FullReturns { get; set; }
    public int PartialReturns { get; set; }

    public List<SaleOrderDto> SaleOrdersCurrentMonth { get; set; }
    public DashboardProfileRevenueGraph ExposuresGraph { get; set; }
}