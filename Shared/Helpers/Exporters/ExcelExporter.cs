using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace Shared.Helpers.Exporters;

public static class ExcelExporter
{
    public static void ExportToExcel(
        Dictionary<DateTime, int> uniqueEmailsPerDay,
        Dictionary<DateTime, int> uniqueEmailsPerMonth,
        Dictionary<DateTime, int> totalEventsPerDay,
        Dictionary<DateTime, int> totalEventsPerMonth,
        Dictionary<DateTime, int> exposuresPerDay,
        Dictionary<DateTime, int> exposuresPerMonth,
        string outputPath)
    {
        // Setup EPPlus license
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

        using (var package = new ExcelPackage())
        {
            // Export each dictionary to a separate sheet
            AddSheet(package, "Unique Customers Per Day", uniqueEmailsPerDay, "The Unique customer count (Only 1 entry for each email) accumulated per day.");
            AddSheet(package, "Unique Customers Per Month", uniqueEmailsPerMonth, "The Unique customer count (Only 1 entry for each email) accumulated per month.");
            AddSheet(package, "Total Events Per Day", totalEventsPerDay, "Total events (API Calls) logged each day.");
            AddSheet(package, "Total Events Per Month", totalEventsPerMonth, "Total events (API Calls) logged each month.");
            AddSheet(package, "Exposures Per Day", exposuresPerDay, "Quantity of Merchant Exposures per day. This depends on the Size of each API Fetch.");
            AddSheet(package, "Exposures Per Month", exposuresPerMonth, "Quantity of Merchant Exposures per month. This depends on the Size of each API Fetch.");

            // Save the package to the output path
            FileInfo fi = new FileInfo(outputPath);
            package.SaveAs(fi);
        }
    }

    private static void AddSheet(
        ExcelPackage package,
        string sheetName,
        Dictionary<DateTime, int> data,
        string description)
    {
        var worksheet = package.Workbook.Worksheets.Add(sheetName);

        // Add description field at the top
        worksheet.Cells["A1"].Value = "Description";
        worksheet.Cells["B1"].Value = description;

        // Add headers
        worksheet.Cells["A3"].Value = "Date/Month";
        worksheet.Cells["B3"].Value = "Value";

        // Style the headers
        using (var range = worksheet.Cells["A3:B3"])
        {
            range.Style.Font.Bold = true;
            range.Style.Fill.PatternType = ExcelFillStyle.Solid;
            range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
        }

        // Add data
        int rowIndex = 4; // Start after the headers
        foreach (var entry in data)
        {
            worksheet.Cells[rowIndex, 1].Value = entry.Key.ToString("yyyy-MM-dd");
            worksheet.Cells[rowIndex, 2].Value = entry.Value;
            rowIndex++;
        }

        // Auto-fit columns for readability
        worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
    }
}