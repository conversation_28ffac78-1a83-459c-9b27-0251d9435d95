using System.Text.Json;
using System.Text.Json.Serialization;
using Shared.Dto.Campaign;
using Shared.Dto.Campaign.Enums;
using JsonException = System.Text.Json.JsonException;

namespace Shared.Helpers.Converters;

public class BlockTypeDtoConverter : JsonConverter<BlockTypeDto>
{
    public override BlockTypeDto Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.String)
        {
            if (Enum.TryParse<BlockTypeDto>(reader.GetString(), true, out var value))
            {
                return value;
            }

            throw new JsonException();
        }
        else if (reader.TokenType == JsonTokenType.Number)
        {
            return (BlockTypeDto) reader.GetInt32();
        }
        else
        {
            throw new JsonException();
        }
    }

    public override void Write(Utf8JsonWriter writer, BlockTypeDto value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.ToString());
    }
}