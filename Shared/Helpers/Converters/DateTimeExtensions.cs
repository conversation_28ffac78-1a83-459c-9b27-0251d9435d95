using System.Text.Json;
using System.Text.Json.Serialization;
using Shared.Dto.Campaign;
using Shared.Dto.Campaign.Enums;
using JsonException = System.Text.Json.JsonException;

namespace Shared.Helpers.Converters;

public static class DateTimeExtensions
{
    public static DateTime SetTimeToMinimum(DateTime dateTime)
    {
        return new DateTime(dateTime.Year, dateTime.Month, dateTime.Day, 0, 0, 0);
    }

    public static DateTime SetTimeToMax(DateTime dateTime)
    {
        return new DateTime(dateTime.Year, dateTime.Month, dateTime.Day, 23, 59, 59);
    }

    public static DateTime ConvertToCopenhagenTime(DateTime date)
    {
        // Get the Copenhagen timezone
        TimeZoneInfo copenhagenTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Central European Standard Time");

        // Convert the date to Copenhagen time
        DateTime copenhagenTime = TimeZoneInfo.ConvertTimeFromUtc(date, copenhagenTimeZone);

        return copenhagenTime;
    }
}