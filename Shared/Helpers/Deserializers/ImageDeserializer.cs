using System.Text.Json;
using Shared.Dto.PrestaShop.product;

namespace Shared.Helpers.Deserializers;

public static class ImageDeserializer
{
    public static List<string> DeserializeProductImages(string? productImages)
    {
        if (string.IsNullOrEmpty(productImages)) return new List<string>();
        var imagesRaw = JsonSerializer.Deserialize<List<ProductImageDto>>(productImages);
        return imagesRaw?.Select(a => a.Src).ToList() ?? new List<string>();
    }
}