namespace Shared.Helpers.Checkers;

public static class AvailabilityChecker
{
    private static readonly HttpClient HttpClient = new();

    /// <summary>
    /// Checks if a product page is available by sending a HEAD request.
    /// </summary>
    /// <param name="url">The product URL.</param>
    /// <returns>True if the product page is available (HTTP 200-299), otherwise false.</returns>
    public static async Task<bool> IsProductAvailableAsync(string url)
    {
        if (string.IsNullOrWhiteSpace(url))
            return false;

        try
        {
            using var request = new HttpRequestMessage(HttpMethod.Head, url);
            using var response = await HttpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);
            return response.IsSuccessStatusCode; // 200-299 means the page exists
        }
        catch
        {
            return false; // Any exception (network issue, DNS failure, etc.) results in "not available"
        }
    }
}