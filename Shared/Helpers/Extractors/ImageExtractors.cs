namespace Shared.Helpers.Extractors;

public static class ImageExtractors
{
    public static string ExtractFileTypeFromBase64(string base64String)
    {
        if (string.IsNullOrEmpty(base64String))
        {
            throw new ArgumentException("Base64 string is null or empty.", nameof(base64String));
        }

        var startIndex = base64String.IndexOf('/') + 1;
        var endIndex = base64String.IndexOf(';');

        if (startIndex < 0 || endIndex < 0 || endIndex <= startIndex)
        {
            throw new FormatException("Invalid base64 string format.");
        }

        return base64String.Substring(startIndex, endIndex - startIndex);
    }
}