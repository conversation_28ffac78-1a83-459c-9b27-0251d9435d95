using System.Text.RegularExpressions;

namespace Shared.Helpers.Replacers;

public static class HtmlReplacer
{
    private static readonly List<string> WordsToRemove = new() {"størrelse", "størrelsesguide", "størrelser"};

    public static string RemoveHtmlTags(string input)
    {
        //Remove style data
        if (input.Contains("<style>"))
        {
            string pattern = @"<style\b[^>]*>(.*?)<\/style>";
            string replacement = string.Empty;
            input = Regex.Replace(input, pattern, replacement, RegexOptions.IgnoreCase | RegexOptions.Singleline);
        }

        //Remove js data
        if (input.Contains("<javascript>"))
        {
            var pattern = @"<javascript\b[^>]*>(.*?)<\/javascript>";
            var replacement = string.Empty;
            input = Regex.Replace(input, pattern, replacement, RegexOptions.IgnoreCase | RegexOptions.Singleline);
        }

        // Remove all remaining HTML tags
        string htmlTagPattern = "<.*?>";
        string noHtml = Regex.Replace(input, htmlTagPattern, string.Empty);

        // Replace multiple consecutive whitespace (including line breaks) with a single line break
        string singleLineBreak = Regex.Replace(noHtml, @"\s*[\r\n]+\s*", Environment.NewLine);

        // Trim any leading and trailing whitespace
        return singleLineBreak.Trim();
    }

    public static string RemoveHtmlTagsAndContent(string input)
    {
        // Adding "-" before each <li> tag and preserving the content
        input = Regex.Replace(input, @"<li[^>]*>", "- ", RegexOptions.IgnoreCase);
        input = Regex.Replace(input, @"<\/li>", string.Empty, RegexOptions.IgnoreCase);

        // Removing <ol> and <ul> tags but preserving their content
        input = Regex.Replace(input, @"<ol[^>]*>", string.Empty, RegexOptions.IgnoreCase);
        input = Regex.Replace(input, @"<\/ol>", Environment.NewLine + Environment.NewLine, RegexOptions.IgnoreCase);
        input = Regex.Replace(input, @"<ul[^>]*>", string.Empty, RegexOptions.IgnoreCase);
        input = Regex.Replace(input, @"<\/ul>", Environment.NewLine + Environment.NewLine, RegexOptions.IgnoreCase);

        // Patterns for tags that should have their content removed
        string[] tagsToRemoveContent = new string[]
        {
            "style", "script", "table", "thead", "tbody", "tfoot", "tr", "td", "th",
            "select", "option", "form", "input", "button", "textarea",
            "label", "a", "iframe", "video", "audio", "canvas", "javascript", "style"
        };

        foreach (string tag in tagsToRemoveContent)
        {
            string pattern = $@"<{tag}\b[^>]*>(.*?)<\/{tag}>";
            input = Regex.Replace(input, pattern, string.Empty, RegexOptions.IgnoreCase | RegexOptions.Singleline);
        }

        input = input.Replace("&nbsp;", " ");
        input = input.Replace("&amp;", "&");
        input = input.Replace("&aelig;", "æ");
        input = input.Replace("&aring;", "å");
        input = input.Replace("&oslash;", "ø");
        input = input.Replace("&lt;", "<");
        input = input.Replace("&gt;", ">");
        input = input.Replace("&quot;", "\"");
        input = input.Replace("&apos;", "'");
        input = input.Replace("&cent;", "¢");
        input = input.Replace("&pound;", "£");
        input = input.Replace("&yen;", "¥");
        input = input.Replace("&euro;", "€");
        input = input.Replace("&copy;", "©");
        input = input.Replace("&reg;", "®");

        // Remove all remaining HTML tags
        string htmlTagPattern = "<.*?>";
        input = Regex.Replace(input, htmlTagPattern, string.Empty);

        // Remove specific words from the input
        foreach (string word in WordsToRemove)
        {
            string wordPattern = $@"\b{word}\b";
            input = Regex.Replace(input, wordPattern, string.Empty, RegexOptions.IgnoreCase);
        }

        // Replace multiple consecutive whitespace (including line breaks) with a single line break
        // but preserve double newlines
        input = Regex.Replace(input, @"(\r?\n)\s*(\r?\n)+",
            Environment.NewLine + Environment.NewLine); // Reduce multiple newlines to two
        input = Regex.Replace(input, @"[ \t]*\r?\n[ \t]*", Environment.NewLine); // Remove spaces/tabs around newlines

        // Trim any leading and trailing whitespace
        return input.Trim();
    }
}