using System.Text.RegularExpressions;

namespace Shared.Helpers.Replacers;

public static class StringManipulators
{
    public static string NormalizePhoneNumber(string phoneNumber)
    {
        // Remove all non-digit characters except for the '+' sign
        var normalizedNumber = Regex.Replace(phoneNumber, @"[^\d+]", "");

        return normalizedNumber;
    }

    public static string AddCountryCodeToPhoneNumber(string phoneNumber, string countryCode)
    {
        if (phoneNumber.StartsWith($"+{countryCode}")) return phoneNumber;

        return $"+{countryCode}{phoneNumber}";
    }
}