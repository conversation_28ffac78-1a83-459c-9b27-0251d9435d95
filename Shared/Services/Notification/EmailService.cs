using System.Net;
using System.Net.Mail;
using ILogger = Serilog.ILogger;

namespace Shared.Services.Notification;

public class EmailService(ILogger logger) : IEmailService
{
    public async Task<bool> SendEmailAsync(List<string> emails, string subject, string htmlBody)
    {
        var fromAddress = new MailAddress("<EMAIL>", "App valyrion");
        const string appPassword = "qcqe mrcw kasn ozkj";

        var smtp = new SmtpClient
        {
            Host = "smtp.gmail.com",
            Port = 587,
            EnableSsl = true,
            DeliveryMethod = SmtpDeliveryMethod.Network,
            UseDefaultCredentials = false,
            Credentials = new NetworkCredential(fromAddress.Address, appPassword)
        };

        using (var message = new MailMessage())
        {
            message.From = fromAddress;
            message.Subject = subject;
            message.Body = htmlBody;
            message.IsBodyHtml = true;
            foreach (var email in emails)
            {
                message.To.Add(new MailAddress(email));
            }

            try
            {
                smtp.Send(message);
                return true;
            }
            catch (Exception ex)
            {
                logger.ForContext("service_name", GetType().Name)
                    .Error(ex, "Tryed to send email to: {Emails}", emails);
                return false;
            }
        }
    }
}