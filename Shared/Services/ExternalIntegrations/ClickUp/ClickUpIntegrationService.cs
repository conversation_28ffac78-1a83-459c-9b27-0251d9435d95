using System.Text;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Serilog;

namespace Shared.Services.ExternalIntegrations.ClickUp;

public class ClickUpIntegrationService : IExternalIntegrationService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger _logger;

    public ClickUpIntegrationService(IConfiguration configuration, ILogger logger)
    {
        _logger = logger;
        _httpClient = new HttpClient();
        _httpClient.BaseAddress = new Uri(configuration["ClickUp-ApiBaseEndpoint"] ?? string.Empty);

        // Set the base address for the ClickUp API - fetched from the configuration.
        _httpClient.DefaultRequestHeaders.Add("Authorization", configuration["ClickUp-ApiToken"]);
    }

    /// <summary>
    /// Asynchronous method to retrieve data from a specified endpoint.
    /// </summary>
    /// <param name="endpoint">The API endpoint to send the GET request to.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the response content as a string.</returns>
    public async Task<string> GetDataAsync(string endpoint)
    {
        var response = await _httpClient.GetAsync(endpoint);
        response.EnsureSuccessStatusCode();
        return await response.Content.ReadAsStringAsync();
    }

    /// <summary>
    /// Asynchronous method to post data to a specified endpoint.
    /// </summary>
    /// <param name="endpoint">The API endpoint to send the POST request to.</param>
    /// <param name="data"></param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the response content as a string.</returns>
    public async Task<bool> PostDataAsync(string endpoint, object data)
    {
        try
        {
            var jsonData = JsonConvert.SerializeObject(data);
            var content = new StringContent(jsonData, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(endpoint, content);
            var responseContent = await response.Content.ReadAsStringAsync();
            if (!response.IsSuccessStatusCode)
            {
                Console.WriteLine(responseContent);
            }

            return response.IsSuccessStatusCode;
        }
        catch (HttpRequestException httpRequestException)
        {
            _logger.Error(httpRequestException,
                "An http request error occurred while pushing data to ClickUp. {Message}. Data: {Data}",
                httpRequestException.Message, JsonConvert.SerializeObject(data));
            throw;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "An error occurred while pushing data to ClickUp.: {Message}. Data: {Data}", ex.Message,
                JsonConvert.SerializeObject(data));
            throw;
        }
    }
}