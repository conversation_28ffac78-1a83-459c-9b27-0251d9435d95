using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Configuration;

namespace Shared.Services.NewValyrion.Token;

public class ApiTokenProvider(HttpClient httpClient, IConfiguration configuration) : IApiTokenProvider
{
    private readonly ApiTokenSettings _settings = new()
    {
        RequestUrl = configuration["NewValyrion-RequestUrl"]
                     ?? throw new ArgumentException("ApiToken_RequestUrl is missing in configuration."),
        ClientId   = configuration["NewValyrion-ClientId"]
                     ?? throw new ArgumentException("ApiToken_ClientId is missing in configuration."),
        Secret     = configuration["NewValyrion-Secret"]
                     ?? throw new ArgumentException("ApiToken_Secret is missing in configuration.")
    };

    private string? _token;
    private DateTime _expiresAt;
    private readonly SemaphoreSlim _lock = new(1, 1);

    // Read each key directly

    public async Task<string> GetTokenAsync()
    {
        if (IsTokenValid()) return _token!;

        await _lock.WaitAsync();
        try
        {
            if (!IsTokenValid())
            {
                await FetchNewTokenAsync();
            }
        }
        finally
        {
            _lock.Release();
        }

        return _token!;
    }

    private bool IsTokenValid()
        => !string.IsNullOrWhiteSpace(_token) && DateTime.UtcNow < _expiresAt;

    private async Task FetchNewTokenAsync()
    {
        var payload = new
        {
            clientId = _settings.ClientId,
            secret = _settings.Secret
        };

        var requestBody = new StringContent(
            JsonSerializer.Serialize(payload),
            Encoding.UTF8,
            "application/json"
        );
        var BaseUrl = $"{_settings.RequestUrl}/auth/client/token";
        var response = await httpClient.PostAsync(BaseUrl, requestBody);
        if (!response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync();
            throw new Exception($"Failed to fetch token: {response.StatusCode} - {content}");
        }

        var json = await response.Content.ReadAsStringAsync();
        var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(json, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        if (tokenResponse == null || string.IsNullOrEmpty(tokenResponse.Token))
            throw new Exception("Invalid token response from server.");

        _token = tokenResponse.Token;
        _expiresAt = DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn - 30);
    }

    private class TokenResponse
    {
        public string Token { get; set; } = string.Empty;
        public string? TokenType { get; set; }
        public int ExpiresIn { get; set; }
    }
    
    private class ApiTokenSettings
    {
        public string RequestUrl { get; set; } = string.Empty;
        public string ClientId { get; set; } = string.Empty;
        public string Secret { get; set; } = string.Empty;
    }
}