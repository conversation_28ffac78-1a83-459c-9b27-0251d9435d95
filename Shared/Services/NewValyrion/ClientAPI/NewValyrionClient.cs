using System.Net.Http.Headers;
using System.Text.Json;
using System.Web;
using Microsoft.Extensions.Configuration;
using Shared.Services.NewValyrion.ResponseDto;
using Shared.Services.NewValyrion.Token;

namespace Shared.Services.NewValyrion.ClientAPI;

public class NewValyrionClient(HttpClient httpClient, IApiTokenProvider tokenProvider, IConfiguration configuration) : INewValyrionClient
{
    private readonly string? BaseUrl = configuration["NewValyrion-RequestUrl"];
    
    public async Task<GenericApiResponse<RecommendedProductPaginationResponse>> GetRecommendationsAsync(string email,
        string partnerId, int pageSize, int pageNumber)
    {
        var responseObj = new GenericApiResponse<RecommendedProductPaginationResponse>();

        try
        {
            var token = await tokenProvider.GetTokenAsync();
            var request = new HttpRequestMessage(HttpMethod.Get, BuildUrl(email, partnerId, pageSize, pageNumber));
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);

            var response = await httpClient.SendAsync(request);
            var content = await response.Content.ReadAsStringAsync();
            
            responseObj = JsonSerializer.Deserialize<GenericApiResponse<RecommendedProductPaginationResponse>>(
                content,
                new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
            ) ?? new GenericApiResponse<RecommendedProductPaginationResponse>();
            
        }
        catch (Exception ex)
        {
            responseObj.StatusCode = 500;
            responseObj.Message = $"Exception occurred: {ex.Message}";
        }

        return responseObj;
    }

    private string BuildUrl(string email, string partnerId, int pageSize, int pageNumber)
    {
        var builder = new UriBuilder($"{BaseUrl}/recommendations/products");

        var query = HttpUtility.ParseQueryString(string.Empty);
        query["legacyPartnerId"] = partnerId.ToString();
        query["customerEmail"] = email;
        query["pageSize"] = pageSize.ToString();
        query["pageNumber"] = pageNumber.ToString();

        builder.Query = query.ToString();
        return builder.ToString();
    }
}