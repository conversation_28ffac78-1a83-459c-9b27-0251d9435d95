using System.Text.RegularExpressions;

namespace Shared.Services;

public static class Validate
{
    public static bool Email(string email)
    {
        string pattern = @"^[\w!#$%&'*+\-/=?\^_`{|}~]+(\.[\w!#$%&'*+\-/=?\^_`{|}~]+)*" + "@" +
                         @"((([\-\w]+\.)+[a-zA-Z]{2,})|(([0-9]{1,3}\.){3}[0-9]{1,3}))$";

        if (Regex.IsMatch(email, pattern))
        {
            return true;
        }

        return false;
    }

    public static bool ValidateIPv4(string ipString)
    {
        if (String.IsNullOrWhiteSpace(ipString))
        {
            return false;
        }

        string[] splitValues = ipString.Split('.');
        if (splitValues.Length != 4)
        {
            return false;
        }

        return splitValues.All(r => byte.TryParse(r, out _));
    }

    public static bool ValidateInternalKey(string key)
    {
        return key == "GLmG6Gf6u2YtrPNs";
    }

    public static bool ValidateInternalExportKey(string key)
    {
        return key == "j8yaZ7KYDCVs4chUd6GtLu";
    }
}