using Shared.Models.Automation;

namespace Shared.Services.Setting;

public interface ISettingService
{
    Task<List<IGrouping<int, Models.ModelsDal.Setting.Setting>>> GetSettingsAsync(int partnerId);
    Task<Models.ModelsDal.Setting.Setting> GetSettingAsync(int partnerId, string key);
    Task<string> GetSettingValueAsync(int partnerId, string key);
    Task<List<Models.ModelsDal.Setting.Setting>> GetSettingAsync(List<string> settings);
    Task<List<Models.ModelsDal.Setting.Setting>> GetSettingByGroupAsync(int groupId);
    Task<List<SettingsProductScoreDecayDto>> GetSettingAutomation();
    Task<Models.ModelsDal.Setting.Setting> UpdateSettingsAsync(Models.ModelsDal.Setting.Setting setting);
}