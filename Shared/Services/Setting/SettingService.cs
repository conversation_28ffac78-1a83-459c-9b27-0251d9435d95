#region

using Microsoft.EntityFrameworkCore;
using Shared.Models.Automation;
using Shared.Models.ModelsDal.Setting;
using Shared.Services.Partner;

#endregion

namespace Shared.Services.Setting;

public class SettingService(SettingDbContext settingDbContext, IPartnerContext partnerContext) : ISettingService
{
    public async Task<List<IGrouping<int, Models.ModelsDal.Setting.Setting>>> GetSettingsAsync(int partnerId)
    {
        return await settingDbContext.Settings
            .Include(a => a.FkGroup)
            .Where(a => a.Active && a.FkPartnerId == partnerId)
            .OrderBy(a => a.FkGroup.Name)
            .GroupBy(a => a.FkGroupId)
            .ToListAsync()
            .ConfigureAwait(false);
    }
    public async Task<Models.ModelsDal.Setting.Setting> GetSettingAsync(string key)
    {
        return await settingDbContext.Settings.SingleAsync(a => a.Key == key);
    }

    public async Task<Models.ModelsDal.Setting.Setting> GetSettingAsync(int partnerId, string key)
    {
        return await settingDbContext.Settings.SingleAsync(a => a.Key == key && a.FkPartnerId == partnerId);
    }

    public async Task<string> GetSettingValueAsync(int partnerId, string key)
    {
        return (await settingDbContext.Settings.SingleAsync(a => a.Key == key && a.FkPartnerId == partnerId)).Value;
    }

    public async Task<List<Models.ModelsDal.Setting.Setting>> GetSettingAsync(List<string> settings)
    {
        var partnerId = partnerContext.PartnerId;
        return await settingDbContext.Settings
            .Where(a => settings.Contains(a.Key) && a.Active && a.FkPartnerId == partnerId)
            .OrderByDescending(a => a.SortOrder)
            .ToListAsync();
    }

    public async Task<List<Models.ModelsDal.Setting.Setting>> GetSettingByGroupAsync(int groupId)
    {
        var partnerId = partnerContext.PartnerId;
        return await settingDbContext.Settings
            .Where(a => a.FkGroupId == groupId && a.Active && a.FkPartnerId == partnerId)
            .ToListAsync();
    }

    public async Task<List<SettingsProductScoreDecayDto>> GetSettingAutomation()
    {
        return await settingDbContext.Settings
            .Include(a => a.SettingMeta)
            .Where(a => a.FkGroupId == 2)
            .SelectMany(a => a.SettingMeta
                .Select(meta => new SettingsProductScoreDecayDto
                {
                    Name = meta.Name,
                    Value = meta.Value,
                    SettingName = a.Key
                }))
            .ToListAsync();
    }

    public async Task<Models.ModelsDal.Setting.Setting> UpdateSettingsAsync(Models.ModelsDal.Setting.Setting setting)
    {
        settingDbContext.Update(setting);
        await settingDbContext.SaveChangesAsync();
        return setting;
    }
}