using Shared.Models.Customer;

namespace Shared.Services.MerchantRelevance
{
    public interface IMerchantRelevanceService
    {
        Task<List<Shared.Dto.MerchantScore.MerchantRelevance>> GetMerchantRelevance(
            MerchantRelevanceCustomerDto? customer,
            int? ageInterval,
            bool takeIntoConsiderationExposures = true);
    
        Task<Dictionary<string, List<Dto.MerchantScore.MerchantRelevance>>> GetMerchantRelevanceForBatch(
            List<MerchantRelevanceCustomerDto?> customers,
            int? ageInterval,
            bool takeIntoConsiderationExposures = true);
    }
}