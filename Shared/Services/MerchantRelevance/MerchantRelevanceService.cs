using System.Net.Http.Json;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Shared.Dto.Merchants;
using Shared.Dto.MerchantScore;
using Shared.Elastic.Elastic;
using Shared.Models.Customer;
using Shared.Models.Merchant;
using Shared.Services.Cache;
using Shared.Services.Partner;
using ILogger = Serilog.ILogger;

namespace Shared.Services.MerchantRelevance;

public class MerchantRelevanceService : IMerchantRelevanceService
{
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly ICacheService _cacheService;
    private readonly IElasticService _elasticService;
    private readonly IMemoryCache _memoryCache;
    private readonly IPartnerContext _partnerContext;

    private readonly HttpClient _httpClient = new();
    private const string Key = "c56rovWYj0EcKIh0kR0lbHBeiUgfswT4b9PeLdGTTih7i3SEJIkdRa1pTvduQ7Fw";

    public MerchantRelevanceService(
        ILogger logger, 
        IConfiguration configuration, 
        ICacheService cacheService,
        IElasticService elasticService, 
        IMemoryCache memoryCache,
        IPartnerContext partnerContext)
    {
        _logger = logger;
        _configuration = configuration;
        _cacheService = cacheService;
        _elasticService = elasticService;
        _memoryCache = memoryCache;
        _partnerContext = partnerContext;
        _httpClient.BaseAddress = new Uri(_configuration["ValyrionServices-BaseUrl"] ?? "");
    }

    public async Task<List<Shared.Dto.MerchantScore.MerchantRelevance>> GetMerchantRelevance(
        MerchantRelevanceCustomerDto? customer,
        int? ageInterval,
        bool takeIntoConsiderationExposures = true)
    {
        var partnerId = _partnerContext.PartnerId;
        var cacheKey = $"MerchantRelevanceService_GetMerchantRelevance_{customer?.Email}_{partnerId}";
        TimeSpan memoryCacheExpiry = TimeSpan.FromMinutes(7);
        TimeSpan redisCacheExpiry = TimeSpan.FromMinutes(12);

        return await _cacheService.GetDataWithCacheLockAsync(
            cacheKey,
            async () =>
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                var merchantRelevance = new List<Shared.Dto.MerchantScore.MerchantRelevance>();

                var now = DateTime.UtcNow;
                var firstDayOfCurrentMonth = new DateTime(now.Year, now.Month, 01);
                var firstDayOfLastMonth = new DateTime(now.Year, now.Month, 01).AddMonths(-1);
                var lastDayOfLastMonth = new DateTime(now.Year, now.Month, 01).AddMilliseconds(-1);
                
                var cacheKeyMerchant =
                    $"GeneralService_GetMerchantsAsync_{firstDayOfCurrentMonth:yyyy-MM-dd}_{now:yyyy-MM-dd}_{_configuration["Environment"]}_{partnerId}";
                var merchantPaginationDto =
                    await _cacheService.GetData<MerchantPaginationDto>(cacheKeyMerchant, true) ??
                    new MerchantPaginationDto();

                stopwatch.Stop();
                Console.WriteLine(
                    $"Section 1 (Cache and MerchantPaginationDto) took {stopwatch.ElapsedMilliseconds} ms");
                stopwatch.Restart();

                var cacheKeyMerchantLastMonth =
                    $"GeneralService_GetMerchantsAsync_{firstDayOfLastMonth:yyyy-MM-dd}_{lastDayOfLastMonth:yyyy-MM-dd}_{_configuration["Environment"]}_{partnerId}";
                var merchantPaginationLastMonthDto =
                    await _cacheService.GetData<MerchantPaginationDto>(cacheKeyMerchantLastMonth, true) ??
                    new MerchantPaginationDto();

                stopwatch.Stop();
                Console.WriteLine(
                    $"Section 2 (Cache and MerchantPaginationLastMonthDto) took {stopwatch.ElapsedMilliseconds} ms");
                stopwatch.Restart();

                var daysInMonth = DateTime.DaysInMonth(now.Year, now.Month);
                var currentDayInMonth = now.Day;
                var isAdultOnlyAllowed = customer?.Age >= 18;

                //CPA
                var lines = await _cacheService.GetDataWithCacheLockAsync(
                    key: $"CustomerService_GetMerchantRelevance_{customer?.Gender}_{customer?.Age}_{ageInterval}_{partnerId}",
                    async () =>
                    {
                        // Fallback logic to fetch from source
                        var gender = customer?.Gender ?? "Unknown";
                        return await GetMerchantRelevanceScoresAsync(customer?.Age, gender);
                    },
                    memoryCacheExpiry: TimeSpan.FromMinutes(10),
                    redisCacheExpiry: TimeSpan.FromMinutes(20)
                );

                stopwatch.Stop();
                Console.WriteLine(
                    $"Section 3 (Cache and GetMerchantRelevanceScoresAsync) took {stopwatch.ElapsedMilliseconds} ms");
                stopwatch.Restart();

                // Fallback If no data is returned from the endpoint above
                if (lines.Count == 0)
                {
                    var gender = customer?.Gender ?? "Unknown";
                    lines = await _elasticService.GetLines("invoices-lines", gender, customer?.Age, ageInterval);
                    var linesPotentials =
                        await _elasticService.GetLines("invoices-potentiallines", gender, customer?.Age, ageInterval);

                    // Update list1 based on values in list2
                    foreach (var potentialLine in linesPotentials)
                    {
                        var score = lines.FirstOrDefault(o => o.MerchantId == potentialLine.MerchantId);
                        if (score != null)
                        {
                            // If an object with the same Id exists in list1, update its Value
                            score.Sum += potentialLine.Sum;
                        }
                        else
                        {
                            // If the object does not exist in list1, add it
                            lines.Add(potentialLine);
                        }
                    }
                }

                stopwatch.Stop();
                Console.WriteLine(
                    $"Section 4 (Fallback and ElasticService.GetLines) took {stopwatch.ElapsedMilliseconds} ms");
                stopwatch.Restart();

                //Use data from last month to calculate totalExposuresTop
                var totalExposuresTop = merchantPaginationLastMonthDto.Merchants
                    .OrderByDescending(a => a.EmailDisplays + a.DiscountDisplays + a.ProductDisplays).Take(30)
                    .Sum(a => (a.EmailDisplays + a.DiscountDisplays + a.ProductDisplays) / 30);
                var merchantLinesDictionary = lines
                    .GroupBy(a => a.MerchantId.ToString())
                    .ToDictionary(
                        g => g.Key,
                        g => g.OrderByDescending(item => item.Sum).First()
                    );
                /*var mlMerchants = customer?.CustomerMeta.FirstOrDefault(a => a.FkCustomerMetaTypeId == 1)?.Value.Split(",")
                    .Take(5)
                    .ToList();*/

                var parallelOptions = new ParallelOptions {MaxDegreeOfParallelism = Environment.ProcessorCount};
                var relevance = merchantRelevance;
                Parallel.ForEach(merchantPaginationDto.Merchants.Where(a => a.IsMarketingAllowed), parallelOptions,
                    merchant =>
                    {
                        var attributionType = merchant.MerchantMeta
                            .FirstOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType)
                            ?.Value;
                        var isAdultOnly = Convert.ToBoolean(merchant.MerchantMeta
                            .FirstOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsAdultOnly)?.Value);

                        if (attributionType == "cpm")
                        {
                            var cpm = Convert.ToInt32(merchant.MerchantMeta
                                .FirstOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPM)?.Value);

                            if (customer != null)
                            {
                                string customerSegmentTypeName = customer.Gender switch
                                {
                                    "Female" => MerchantMetaTypeNames.CustomerSegmentFemale,
                                    "Male" => MerchantMetaTypeNames.CustomerSegmentMale,
                                    _ => MerchantMetaTypeNames.CustomerSegmentUnknown
                                };

                                var customerSegment = GetCustomerSegment(merchant, customerSegmentTypeName);
                                bool isCustomerInSegment =
                                    customerSegment == "all" || IsCustomerInSegment(customerSegment, customer.Age);
                                if (isCustomerInSegment && IsWithinDailyLimit(merchant, daysInMonth, currentDayInMonth))
                                {
                                    if (merchant.EmailDisplays != -1 && merchant.EmailDisplaysUnique != -1 &&
                                        merchant.EmailRedirects != -1)
                                    {
                                        lock (relevance)
                                        {
                                            AddMerchantToRelevance(merchant, relevance, cpm, attributionType, 0,
                                                isAdultOnly);
                                        }
                                    }
                                }
                            }
                        }
                        else if (attributionType == "cpa")
                        {
                            var merchantCpa =
                                merchantPaginationLastMonthDto.Merchants.FirstOrDefault(a =>
                                    a.MerchantId == merchant.MerchantId) ??
                                merchant;

                            decimal costPrImpression = 0;
                            var totalExposures = merchantCpa.EmailDisplays + merchantCpa.DiscountDisplays +
                                                 merchantCpa.ProductDisplays;
                            if (merchantLinesDictionary.TryGetValue(merchantCpa.MerchantId.ToString(),
                                    out var merchantScore))
                            {
                                if (merchantScore.Sum != 0 && totalExposures != 0)
                                {
                                    costPrImpression = (decimal) merchantScore.Sum / totalExposures;
                                }
                                else if (totalExposures == 0)
                                {
                                    if (merchantScore.Sum != 0 && totalExposuresTop != 0)
                                    {
                                        costPrImpression = (decimal) merchantScore.Sum / totalExposuresTop;
                                    }
                                }
                            }

                            var activeExposure = false;
                            if (customer != null && takeIntoConsiderationExposures)
                            {
                                var renew = merchant.MerchantMeta
                                    .FirstOrDefault(a =>
                                        a.FkMerchantMetaTypeName == MerchantMetaTypeNames.MinExposureRenewalDays)
                                    ?.Value;
                                renew = string.IsNullOrEmpty(renew) ? "3" : renew;
                                var renewDays = Convert.ToInt32(renew);
                                var expireWithInXDays = now.AddDays(-renewDays);

                                var customerExposure = customer.CustomerExposures
                                    .FirstOrDefault(a =>
                                        a.MerchantId == merchantCpa.MerchantId && a.ExposureEnd > expireWithInXDays);
                                if (customerExposure != null)
                                {
                                    activeExposure = true;
                                }
                            }

                            lock (relevance)
                            {
                                if (relevance.All(m => m.MerchantId != merchantCpa.MerchantId))
                                {
                                    relevance.Add(new Shared.Dto.MerchantScore.MerchantRelevance
                                    {
                                        MerchantId = merchantCpa.MerchantId,
                                        CostPrImpression = costPrImpression,
                                        ActiveExposures = activeExposure,
                                        AttributionType = attributionType,
                                        MLIndex = 0,
                                        IsAdultOnly = isAdultOnly
                                    });
                                }
                            }
                        }
                    });

                stopwatch.Stop();
                Console.WriteLine(
                    $"Section 5 (Parallel.ForEach and relevance calculation) took {stopwatch.ElapsedMilliseconds} ms");
                stopwatch.Restart();

                //Handle adult only shops
                if (!isAdultOnlyAllowed)
                {
                    merchantRelevance = merchantRelevance.Where(a => a.IsAdultOnly == false).ToList();
                }

                if (merchantRelevance.Count == 0)
                {
                    var topMerchantScores = lines.Take(50).ToList();
                    foreach (var merchantScore in topMerchantScores)
                    {
                        merchantRelevance.Add(new Shared.Dto.MerchantScore.MerchantRelevance
                        {
                            MerchantId = Convert.ToInt32(merchantScore.MerchantId),
                            CostPrImpression = 0,
                            ActiveExposures = false,
                            AttributionType = "randomly picked",
                            MLIndex = 0,
                            IsAdultOnly = false
                        });
                    }

                    stopwatch.Stop();
                    Console.WriteLine(
                        $"Section 6 (Fallback and topMerchantScores) took {stopwatch.ElapsedMilliseconds} ms");
                    stopwatch.Restart();

                    return merchantRelevance;
                }

                var sortedList = merchantRelevance.OrderBy(a => a.ActiveExposures)
                    /*.ThenBy(a => a.MLIndex == null ? 1 : 0)
                    .ThenBy(a => a.MLIndex)*/
                    .ThenByDescending(a => a.CostPrImpression).ToList();

                //Force cpm shops to be in the list
                var cpmShops = sortedList.Where(a => a.AttributionType == "cpm").Take(2).ToList();

                // Remove these two shops from the sorted list
                sortedList.RemoveAll(a => cpmShops.Contains(a));

                // Insert the two cpm shops at the beginning
                sortedList.InsertRange(0, cpmShops);

                stopwatch.Stop();
                Console.WriteLine(
                    $"Section 7 (Sorting and final list preparation) took {stopwatch.ElapsedMilliseconds} ms");

                return sortedList;
            },
            memoryCacheExpiry,
            redisCacheExpiry
        );
    }

    public async Task<Dictionary<string, List<Dto.MerchantScore.MerchantRelevance>>> GetMerchantRelevanceForBatch(
        List<MerchantRelevanceCustomerDto?> customers,
        int? ageInterval,
        bool takeIntoConsiderationExposures = true)
    {
        var partnerId = _partnerContext.PartnerId;
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var batchResults = new Dictionary<string, List<Shared.Dto.MerchantScore.MerchantRelevance>>();

        var now = DateTime.UtcNow;
        var firstDayOfCurrentMonth = new DateTime(now.Year, now.Month, 1);
        var firstDayOfLastMonth = firstDayOfCurrentMonth.AddMonths(-1);
        var lastDayOfLastMonth = firstDayOfCurrentMonth.AddMilliseconds(-1);

        // Fetch merchant data
        var cacheKeyMerchant =
            $"GeneralService_GetMerchantsAsync_{firstDayOfCurrentMonth:yyyy-MM-dd}_{now:yyyy-MM-dd}_{_configuration["Environment"]}_{partnerId}";
        var merchantPaginationDto =
            await _cacheService.GetDataWithCacheLockAsync<MerchantPaginationDto>(cacheKeyMerchant, null,
                memoryCacheExpiry: TimeSpan.FromHours(5), redisCacheExpiry: TimeSpan.FromHours(10)) ??
            new MerchantPaginationDto();

        var cacheKeyMerchantLastMonth =
            $"GeneralService_GetMerchantsAsync_{firstDayOfLastMonth:yyyy-MM-dd}_{lastDayOfLastMonth:yyyy-MM-dd}_{_configuration["Environment"]}_{partnerId}";
        var merchantPaginationLastMonthDto =
            await _cacheService.GetDataWithCacheLockAsync<MerchantPaginationDto>(cacheKeyMerchantLastMonth, null,
                memoryCacheExpiry: TimeSpan.FromHours(5), redisCacheExpiry: TimeSpan.FromHours(10)) ??
            new MerchantPaginationDto();

        stopwatch.Stop();
        Console.WriteLine($"Section 1 (MerchantPaginationDto) took {stopwatch.ElapsedMilliseconds} ms");

        stopwatch.Restart();
        // Pre-fetch merchant relevance scores for all customers in the batch
        var genderAgeKeys = customers
            .Select(c => $"CustomerService_GetMerchantRelevance_{c?.Gender}_{c?.Age}_{ageInterval}_{partnerId}").Distinct();
        var allLines = await _cacheService.GetBatchDataWithCacheLockAsync<List<MerchantScore>>(
            genderAgeKeys,
            async missingKeys =>
            {
                var enumerable = missingKeys.ToList();
                var fetchTasks = enumerable.Select(async key =>
                {
                    var gender = key.Split("_")[3];
                    var age = int.TryParse((string?) key.Split("_")[4], out var parsedAge) ? parsedAge : (int?) null;

                    // Fetch from primary source
                    var merchantScores = await GetMerchantRelevanceScoresAsync(age, gender);

                    // Fallback if primary source fails or returns no data
                    if (merchantScores.Count == 0)
                    {
                        merchantScores = await FallbackFetchMerchantScores(gender, age, ageInterval);
                    }

                    return merchantScores;
                });
                var fetchedResults = await Task.WhenAll(fetchTasks);
                var result = enumerable
                    .Zip(fetchedResults,
                        (key, result) => new KeyValuePair<string, List<MerchantScore>?>(key, result))
                    .ToDictionary(kv => kv.Key, kv => kv.Value);
                if (result.Count == 0)
                {
                    return null;
                }

                return result;
            },
            memoryCacheExpiry: TimeSpan.FromMinutes(10),
            redisCacheExpiry: TimeSpan.FromMinutes(20));

        stopwatch.Stop();
        Console.WriteLine($"Section 2 (MerchantRelevanceScores) took {stopwatch.ElapsedMilliseconds} ms");

        stopwatch.Restart();

        var daysInMonth = DateTime.DaysInMonth(now.Year, now.Month);
        var currentDayInMonth = now.Day;

        // Calculate total exposures for CPM merchants
        var totalExposuresTop = merchantPaginationLastMonthDto.Merchants
            .OrderByDescending(a => a.EmailDisplays + a.DiscountDisplays + a.ProductDisplays)
            .Take(30)
            .Sum(a => (a.EmailDisplays + a.DiscountDisplays + a.ProductDisplays) / 30);

        Parallel.ForEach(customers, new ParallelOptions {MaxDegreeOfParallelism = Environment.ProcessorCount},
            customer =>
            {
                if (customer == null)
                    return;

                var age = customer.Age ?? 0;
                var gender = customer.Gender ?? "Unknown";
                var isAdultOnlyAllowed = age >= 18;
                var lines = new List<MerchantScore>();

                var key = $"CustomerService_GetMerchantRelevance_{gender}_{age}_{ageInterval}_{partnerId}";

                // Safely attempt to retrieve from dictionary
                lock (allLines)
                {
                    if (allLines.TryGetValue(key, out var cachedLines))
                    {
                        lines = cachedLines;
                    }
                }

                if (lines == null || lines.Count == 0)
                {
                    try
                    {
                        // Fetch relevance scores asynchronously
                        lines = GetMerchantRelevanceScoresAsync(age, gender).GetAwaiter().GetResult();
                        lock (allLines)
                        {
                            allLines.TryAdd(key, lines);
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log and handle exceptions during fetching
                        _logger.Error(ex, $"Failed to fetch merchant relevance scores for key: {key}");
                        lines = []; // Default to empty list
                    }
                }

                var relevanceList = new List<Shared.Dto.MerchantScore.MerchantRelevance>();
                var merchantLinesDictionary = lines
                    .GroupBy(a => a.MerchantId.ToString())
                    .ToDictionary(
                        g => g.Key,
                        g => g.OrderByDescending(item => item.Sum).First()
                    );

                foreach (var merchant in merchantPaginationDto.Merchants.Where(a => a.IsMarketingAllowed))
                {
                    var customerSegmentTypeName = customer.Gender switch
                    {
                        "Female" => MerchantMetaTypeNames.CustomerSegmentFemale,
                        "Male" => MerchantMetaTypeNames.CustomerSegmentMale,
                        _ => MerchantMetaTypeNames.CustomerSegmentUnknown
                    };

                    var attributionType = merchant.MerchantMeta
                        .FirstOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType)?.Value;
                    var isAdultOnly = Convert.ToBoolean(merchant.MerchantMeta
                        .FirstOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsAdultOnly)?.Value);

                    switch (attributionType)
                    {
                        case "cpm":
                            var customerSegment = GetCustomerSegment(merchant, customerSegmentTypeName);
                            bool isCustomerInSegment =
                                customerSegment == "all" || IsCustomerInSegment(customerSegment, customer.Age);
                            if (isCustomerInSegment && IsWithinDailyLimit(merchant, daysInMonth, currentDayInMonth))
                            {
                                if (merchant.EmailDisplays != -1 && merchant.EmailDisplaysUnique != -1 &&
                                    merchant.EmailRedirects != -1)
                                {
                                    var cpm = Convert.ToDecimal(merchant.MerchantMeta
                                        .FirstOrDefault(a =>
                                            a.FkMerchantMetaTypeName == MerchantMetaTypeNames.CPM)
                                        ?.Value ?? "0");
                                    relevanceList.Add(new Shared.Dto.MerchantScore.MerchantRelevance
                                    {
                                        MerchantId = merchant.MerchantId,
                                        AttributionType = "cpm",
                                        CostPrImpression = cpm / 1000,
                                        ActiveExposures = false,
                                        IsAdultOnly = isAdultOnly
                                    });
                                }
                            }

                            break;

                        case "cpa":
                            var totalExposures = merchant.EmailDisplays + merchant.DiscountDisplays +
                                                 merchant.ProductDisplays;
                            if (merchantLinesDictionary.TryGetValue(merchant.MerchantId.ToString(),
                                    out var merchantScore))
                            {
                                var costPrImpression = (decimal) (totalExposures > 0
                                    ? merchantScore.Sum / totalExposures
                                    : totalExposuresTop > 0
                                        ? merchantScore.Sum / totalExposuresTop
                                        : 0);

                                var activeExposure = false;
                                if (takeIntoConsiderationExposures)
                                {
                                    var renew = merchant.MerchantMeta
                                        .FirstOrDefault(a =>
                                            a.FkMerchantMetaTypeName == MerchantMetaTypeNames.MinExposureRenewalDays)
                                        ?.Value;
                                    renew = string.IsNullOrEmpty(renew) ? "3" : renew;
                                    var renewDays = Convert.ToInt32(renew);
                                    var expireWithInXDays = now.AddDays(-renewDays);

                                    var customerExposure = customer.CustomerExposures
                                        .FirstOrDefault(a =>
                                            a.MerchantId == merchant.MerchantId && a.ExposureEnd > expireWithInXDays);
                                    if (customerExposure != null)
                                    {
                                        activeExposure = true;
                                    }
                                }

                                relevanceList.Add(new Shared.Dto.MerchantScore.MerchantRelevance
                                {
                                    MerchantId = merchant.MerchantId,
                                    AttributionType = "cpa",
                                    CostPrImpression = costPrImpression,
                                    ActiveExposures = activeExposure,
                                    IsAdultOnly = isAdultOnly
                                });
                            }

                            break;
                    }
                }

                if (!isAdultOnlyAllowed)
                {
                    relevanceList = relevanceList.Where(a => !a.IsAdultOnly).ToList();
                }

                if (relevanceList.Count == 0)
                {
                    var randomMerchants = lines.Take(50).Select(merchantScore =>
                        new Shared.Dto.MerchantScore.MerchantRelevance
                        {
                            MerchantId = Convert.ToInt32(merchantScore.MerchantId),
                            CostPrImpression = 0,
                            ActiveExposures = false,
                            AttributionType = "randomly picked",
                            MLIndex = 0,
                            IsAdultOnly = false
                        }).ToList();

                    relevanceList.AddRange(randomMerchants);
                }

                var sortedList = relevanceList
                    .OrderBy(r => r.ActiveExposures)
                    .ThenByDescending(r => r.CostPrImpression)
                    .ToList();

                var topCpmMerchants = sortedList.Where(r => r.AttributionType == "cpm").Take(2).ToList();
                sortedList.RemoveAll(r => topCpmMerchants.Contains(r));
                sortedList.InsertRange(0, topCpmMerchants);

                lock (batchResults)
                {
                    batchResults[customer.Email] = sortedList;
                }
            });

        stopwatch.Stop();
        Console.WriteLine(
            $"Total Batch Processing Time for {customers.Count} emails: {stopwatch.ElapsedMilliseconds} ms");

        return batchResults;
    }

    private string GetCustomerSegment(MerchantPaginationDataDto merchant, string customerSegmentTypeName)
    {
        var data = merchant.MerchantMeta
            .FirstOrDefault(a => a.FkMerchantMetaTypeName == customerSegmentTypeName)
            ?.Value;
        //If no restrictions on any meta all is allowed
        if (data == null)
        {
            var foundAny = merchant.MerchantMeta.FirstOrDefault(a =>
                a.FkMerchantMetaTypeName == MerchantMetaTypeNames.CustomerSegmentFemale ||
                a.FkMerchantMetaTypeName == MerchantMetaTypeNames.CustomerSegmentMale ||
                a.FkMerchantMetaTypeName == MerchantMetaTypeNames.CustomerSegmentUnknown)?.Value;
            if (foundAny == null)
            {
                return "all";
            }
        }

        return data ?? "00";
    }

    private bool IsCustomerInSegment(string customerSegment, int? customerAge)
    {
        return customerSegment?.Contains(customerAge?.ToString() ?? "") ?? false;
    }

    private bool IsWithinDailyLimit(MerchantPaginationDataDto merchant, int daysInMonth, int currentDayInMonth)
    {
        var currentLimit = (merchant.CPMBudget / daysInMonth) * currentDayInMonth;
        return merchant.Actual < currentLimit;
    }

    private void AddMerchantToRelevance(MerchantPaginationDataDto merchant,
        List<Shared.Dto.MerchantScore.MerchantRelevance> merchantRelevance, decimal cpm,
        string attributionType,
        int? mlIndex, bool isAdultOnly)
    {
        if (merchantRelevance.All(m => m.MerchantId != merchant.MerchantId))
        {
            merchantRelevance.Add(new Shared.Dto.MerchantScore.MerchantRelevance
            {
                MerchantId = merchant.MerchantId,
                CostPrImpression = cpm / (decimal) 1000,
                ActiveExposures = false,
                AttributionType = attributionType,
                MLIndex = mlIndex,
                IsAdultOnly = isAdultOnly
            });
        }
    }

    private async Task<List<MerchantScore>> FallbackFetchMerchantScores(string gender, int? age, int? ageInterval)
    {
        try
        {
            // Fetch fallback data from ElasticService or other sources
            var fallbackLines = await _elasticService.GetLines("invoices-lines", gender, age, ageInterval);
            var fallbackPotentialLines =
                await _elasticService.GetLines("invoices-potentiallines", gender, age, ageInterval);

            // Merge fallback lines with potential lines
            foreach (var potentialLine in fallbackPotentialLines)
            {
                var existingLine = fallbackLines.FirstOrDefault(l => l.MerchantId == potentialLine.MerchantId);
                if (existingLine != null)
                {
                    existingLine.Sum += potentialLine.Sum;
                }
                else
                {
                    fallbackLines.Add(potentialLine);
                }
            }

            return fallbackLines;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error in FallbackFetchMerchantScores");
            return [];
        }
    }

    private async Task<List<MerchantScore>> GetMerchantRelevanceScoresAsync(int? age, string gender)
    {
        age ??= 0;
        if (string.IsNullOrEmpty(gender))
        {
            gender = "Unknown";
        }

        try
        {
            var response =
                await _httpClient.GetAsync(
                    $"merchant/MerchantRelevanceScoresByAgeAndGender/{age}/{gender}/{_partnerContext.PartnerId}/{Key}");

            if (response.IsSuccessStatusCode)
            {
                // Read and deserialize the JSON response to List<MerchantScore>
                var result = await response.Content.ReadFromJsonAsync<List<MerchantScore>>();

                // Validate if the response contains valid data
                if (result != null && result.Count != 0) return result;

                // Log warning if no data was retrieved or data is null
                _logger.Warning("No merchant scores were returned from the endpoint.");
                return [];
            }

            // Log error if the response status code is not successful
            _logger.ForContext("service_name", GetType().Name)
                .Error(
                    $"Failed to retrieve merchant relevance scores: {response.StatusCode} - {response.ReasonPhrase} - {await response.Content.ReadAsStringAsync()} - Url: merchant/MerchantRelevanceScoresByAgeAndGender/{age}/{gender}/{Key}");
        }
        catch (HttpRequestException httpRequestException)
        {
            // Handle HTTP request exceptions
            _logger.Error(httpRequestException,
                "HTTP request error while calling MerchantRelevanceScoresByAgeAndGender endpoint.");
        }
        catch (Exception ex)
        {
            // Handle other exceptions
            _logger.Error(ex, "Unexpected error while calling MerchantRelevanceScoresByAgeAndGender endpoint.");
        }

        return [];
    }
}