using Microsoft.AspNetCore.Http;

namespace Shared.Services.Partner;

public interface IPartnerContext
{
    int PartnerId { get; }
    bool IsDevRequest { get; }
    bool IsExempt { get; }
    bool IsFoundByDefault { get; }
    
    void SetPartnerId(int partnerId);
}

public class PartnerContext(IHttpContextAccessor httpContextAccessor) : IPartnerContext
{
    private const int DEFAULT_PARTNER_ID = 52876;

    public int PartnerId
    {
        get
        {
            var context = httpContextAccessor.HttpContext;
            if (context?.Items.TryGetValue("PartnerId", out var partnerIdObject) == true && 
                partnerIdObject is int partnerId && 
                partnerId != 0)
            {
                return partnerId;
            }
            return DEFAULT_PARTNER_ID;
        }
    }

    public bool IsDevRequest
    {
        get
        {
            var context = httpContextAccessor.HttpContext;
            if (context?.Items.TryGetValue("IsDevPartnerRequest", out var isDevRequestObject) == true && isDevRequestObject is bool isDevRequest)
            {
                return isDevRequest;
            }
            return false;
        }
    }
    
    public bool IsExempt
    {
        get
        {
            var context = httpContextAccessor.HttpContext;
            if (context?.Items.TryGetValue("IsPartnerIdExempt", out var isExemptObject) == true && isExemptObject is bool isExempt)
            {
                return isExempt;
            }
            return false;
        }
    }
    
    public bool IsFoundByDefault
    {
        get
        {
            var context = httpContextAccessor.HttpContext;
            if (context?.Items.TryGetValue("IsFoundByDefault", out var isFoundByDefaultObject) == true && isFoundByDefaultObject is bool isFoundByDefault)
            {
                return isFoundByDefault;
            }
            return false;
        }
    }
    
    public void SetPartnerId(int partnerId)
    {
        if (httpContextAccessor.HttpContext != null)
        {
            httpContextAccessor.HttpContext.Items["PartnerId"] = partnerId;
            // When manually setting a Partner ID, it's not found by default
            httpContextAccessor.HttpContext.Items["IsFoundByDefault"] = false;
        }
    }
} 