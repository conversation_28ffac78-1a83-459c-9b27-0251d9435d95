using System.Text;
using Microsoft.Extensions.Configuration;
using Renci.SshNet;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using ILogger = Serilog.ILogger;

namespace Shared.Services.Image
{
    public class ImageService(ILogger logger, IConfiguration configuration, IHttpClientFactory clientFactory)
        : IImageService
    {
        public string CreateImages(string imageName, string data, string rootFolder)
        {
            var now = DateTime.UtcNow;
            var year = now.ToString("yyyy");
            var month = now.ToString("MM");

            var ext = imageName.Split('.').Last();
            var filename = $"{DateTime.UtcNow.Day}-{Guid.NewGuid().ToString()}.{ext}";

            string url = $"{rootFolder}/{year}/{month}/{filename}";
            string imageData = data.Split("base64,")[1];

            var keyString = configuration["ValyrionFtpKey"].Replace(" ", "\r\n");
            keyString = keyString.Replace("-----BEGIN\r\nRSA\r\nPRIVATE\r\nKEY-----",
                "-----BEGIN RSA PRIVATE KEY-----");
            keyString = keyString.Replace("-----END\r\nRSA\r\nPRIVATE\r\nKEY-----", "-----END RSA PRIVATE KEY-----");
            MemoryStream keyStream = new MemoryStream(Encoding.UTF8.GetBytes(keyString));
            var key = new PrivateKeyFile(keyStream);
            using (var client = new SftpClient(configuration["ValyrionFtpHost"],
                       configuration["ValyrionFtpUsername"], key))
            {
                client.Connect();

                //Year folder exists
                var folderSftpFiles = client.ListDirectory(rootFolder)
                    .Where(a => a.IsDirectory)
                    .ToList();
                var folderExists = folderSftpFiles.SingleOrDefault(a => a.Name == year);
                if (folderExists == null)
                {
                    client.CreateDirectory($"{rootFolder}/{year}");
                }

                //Month folder exists
                folderSftpFiles = client.ListDirectory($"{rootFolder}/{year}")
                    .Where(a => a.IsDirectory)
                    .ToList();
                folderExists = folderSftpFiles.SingleOrDefault(a => a.Name == month);
                if (folderExists == null)
                {
                    client.CreateDirectory($"{rootFolder}/{year}/{month}");
                }

                byte[] fileBytes = Convert.FromBase64String(imageData);

                client.UploadFile(new MemoryStream(fileBytes),
                    url, true);

                client.Disconnect();
            }

            return $"https://files.viaads.dk/{configuration["FilesPath"]}/{url}";
        }
        
        public string CreateImage(string imageName, string data, string folderPath, bool createDateFolders = false)
        {
            var now = DateTime.UtcNow;
            var year = now.ToString("yyyy");
            var month = now.ToString("MM");
            var containerName = "assets";
            var username = configuration["FileStorage-FTP-Username"]?.Replace("<CONTAINER_NAME>", containerName);

            var ext = imageName.Split('.').Last();
            var filename = $"{DateTime.UtcNow.Day}-{Guid.NewGuid().ToString()}.{ext}";
            
            if(createDateFolders)
            {
                folderPath = $"{folderPath}/{year}/{month}";
            }
            
            string url = $"{folderPath}/{filename}";
            string imageData = data.Split("base64,")[1];
            
            var password = configuration["FileStorage-FTP-Password"];
            using (var client = new SftpClient(configuration["FileStorage-FTP-Hostname"],
                       username, password))
            {
                client.Connect();

                if (createDateFolders)
                {
                    //Year folder exists
                    var folderSftpFiles = client.ListDirectory(folderPath)
                        .Where(a => a.IsDirectory)
                        .ToList();
                    var folderExists = folderSftpFiles.SingleOrDefault(a => a.Name == year);
                    if (folderExists == null)
                    {
                        client.CreateDirectory($"{folderPath}/{year}");
                    }

                    //Month folder exists
                    folderSftpFiles = client.ListDirectory($"{folderPath}/{year}")
                        .Where(a => a.IsDirectory)
                        .ToList();
                    folderExists = folderSftpFiles.SingleOrDefault(a => a.Name == month);
                    if (folderExists == null)
                    {
                        client.CreateDirectory($"{folderPath}/{year}/{month}");
                    }
                }

                byte[] fileBytes = Convert.FromBase64String(imageData);

                client.UploadFile(new MemoryStream(fileBytes),
                    url, true);

                client.Disconnect();
            }

            return $"https://files.valyrion.com/{containerName}/{url}";
        }

        public async Task<Image<Rgba64>> LoadImageAsync(string imageUrl)
        {
            var httpClient = clientFactory.CreateClient();

            try
            {
                await using Stream stream = await httpClient.GetStreamAsync(imageUrl);
                return SixLabors.ImageSharp.Image.Load<Rgba64>(stream);
            }
            catch (Exception ex)
            {
                logger.Error(ex, "An error occurred while loading the image.");
                throw;
            }
        }
    }
}