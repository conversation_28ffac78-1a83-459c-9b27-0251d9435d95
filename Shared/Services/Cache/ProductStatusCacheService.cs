using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Text.Json;

namespace Shared.Services.Cache;

public interface IProductStatusCacheService
{
    Task<Dictionary<string, bool>> GetProductStatusBatchAsync(IEnumerable<string> productIds);
    Task SetProductStatusBatchAsync(Dictionary<string, bool> productStatuses, TimeSpan? expiry = null);
    Task<bool?> GetProductStatusAsync(string productId);
    Task SetProductStatusAsync(string productId, bool isActive, TimeSpan? expiry = null);
    Task InvalidateProductStatusAsync(string productId);
}

public class ProductStatusCacheService : IProductStatusCacheService
{
    private readonly IConnectionMultiplexer _redis;
    private readonly ILogger<ProductStatusCacheService> _logger;
    private readonly TimeSpan _defaultExpiry = TimeSpan.FromHours(24);
    private const int BatchSize = 100; // Adjust based on your needs
    
    public ProductStatusCacheService(
        IConnectionMultiplexer redis,
        ILogger<ProductStatusCacheService> logger)
    {
        _redis = redis;
        _logger = logger;
    }
    
    // Get status for multiple products at once
    public async Task<Dictionary<string, bool>> GetProductStatusBatchAsync(IEnumerable<string> productIds)
    {
        var result = new Dictionary<string, bool>();
        var db = _redis.GetDatabase();
        var idList = productIds.Distinct().ToList();
        
        try
        {
            // Process in batches to avoid overwhelming Redis
            for (int i = 0; i < idList.Count; i += BatchSize)
            {
                var batch = idList.Skip(i).Take(BatchSize).ToList();
                var redisKeys = batch.Select(id => (RedisKey)GetCacheKey(id)).ToArray();
                
                // Use a batch operation to get multiple values at once
                RedisValue[] values = await db.StringGetAsync(redisKeys);
                
                for (int j = 0; j < batch.Count; j++)
                {
                    if (values[j].HasValue)
                    {
                        result[batch[j]] = bool.Parse(values[j]);
                    }
                }
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting batch product status from cache for {Count} products", idList.Count);
            return result;
        }
    }
    
    // Set status for multiple products at once
    public async Task SetProductStatusBatchAsync(Dictionary<string, bool> productStatuses, TimeSpan? expiry = null)
    {
        var db = _redis.GetDatabase();
        var expiryTime = expiry ?? _defaultExpiry;
        
        try
        {
            // Process in batches
            var batches = productStatuses.Keys
                .Select((key, index) => new { Key = key, Index = index })
                .GroupBy(x => x.Index / BatchSize)
                .Select(g => g.Select(x => x.Key).ToList())
                .ToList();
                
            foreach (var batch in batches)
            {
                var batchTasks = batch.Select(id => 
                    db.StringSetAsync(
                        GetCacheKey(id), 
                        productStatuses[id].ToString(), 
                        expiryTime
                    )
                ).ToArray();
                
                await Task.WhenAll(batchTasks);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting batch product status in cache for {Count} products", productStatuses.Count);
        }
    }
    
    // Get status for a single product
    public async Task<bool?> GetProductStatusAsync(string productId)
    {
        var db = _redis.GetDatabase();
        
        try
        {
            var value = await db.StringGetAsync(GetCacheKey(productId));
            if (value.HasValue)
            {
                return bool.Parse(value);
            }
            
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting product status from cache for product {ProductId}", productId);
            return null;
        }
    }
    
    // Set status for a single product
    public async Task SetProductStatusAsync(string productId, bool isActive, TimeSpan? expiry = null)
    {
        var db = _redis.GetDatabase();
        var expiryTime = expiry ?? _defaultExpiry;
        
        try
        {
            await db.StringSetAsync(GetCacheKey(productId), isActive.ToString(), expiryTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting product status in cache for product {ProductId}", productId);
        }
    }
    
    // Invalidate a product's cached status
    public async Task InvalidateProductStatusAsync(string productId)
    {
        var db = _redis.GetDatabase();
        
        try
        {
            await db.KeyDeleteAsync(GetCacheKey(productId));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating product status in cache for product {ProductId}", productId);
        }
    }
    
    // Helper to generate consistent cache keys
    private string GetCacheKey(string productId)
    {
        // Use a prefix to avoid key collisions with other data
        // Use a hash-based partitioning to distribute keys evenly
        int partition = Math.Abs(productId.GetHashCode() % 100);
        return $"product:status:{partition}:{productId}";
    }
} 