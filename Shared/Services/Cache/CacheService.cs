#region

using System.Collections.Concurrent;
using System.Net;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using StackExchange.Redis;
using ILogger = Serilog.ILogger;
using JsonSerializer = System.Text.Json.JsonSerializer;

#endregion

namespace Shared.Services.Cache;

public class CacheService(
    ILogger logger,
    IConfiguration configuration,
    ConnectionMultiplexer redisManager,
    IMemoryCache memoryCache,
    ICacheLockManager cacheLockManager)
    : ICacheService
{
    private readonly IConfiguration _configuration = configuration;
    private readonly IDatabase _cacheDb = redisManager.GetDatabase();

    //Only save to redis on production
    private readonly bool _prod = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") != "Development";
    //private readonly bool _prod = true;

    private readonly ConcurrentDictionary<string, TaskCompletionSource<object>> _cacheLocks = new();

    public async Task<T?> GetData<T>(string key, bool byPassProd = false)
    {
        if (!_prod && !byPassProd)
            return default;

        try
        {
            var value = await _cacheDb.StringGetAsync(key);
            if (string.IsNullOrEmpty(value))
                return default; // Avoid throwing exceptions to improve resiliency

            return JsonSerializer.Deserialize<T>(value);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting cache for {Key}", key);
            return default; // Avoid throwing exceptions to improve resiliency
        }
    }

    public async Task<IList<T?>> GetBatchData<T>(IEnumerable<string> keys, bool byPassProd = false)
    {
        if (!_prod && !byPassProd)
            return new List<T?>();

        var results = new List<T?>();
        try
        {
            var batch = _cacheDb.CreateBatch(); // Create a Redis pipeline batch
            var tasks = keys.Select(key => batch.StringGetAsync(key)).ToArray();

            batch.Execute(); // Execute the batch

            await Task.WhenAll(tasks);

            results = tasks.Select(task =>
            {
                var value = task.Result;
                if (string.IsNullOrEmpty(value)) return default;
                return JsonSerializer.Deserialize<T>(value);
            }).ToList();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error while fetching batch data from Redis");
        }

        return results;
    }


    public bool SetData<T>(string key, T? value, TimeSpan expirationTime, bool byPassProd = false)
    {
        if (_prod && value != null || byPassProd)
        {
            logger.ForContext("service_name", GetType().Name)
                .Information($"Cache: SetData {key}");

            try
            {
                var settings = new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore
                };
                var jsonValue = JsonConvert.SerializeObject(value, settings);
                if (jsonValue != "")
                {
                    return _cacheDb.StringSet(key, jsonValue, expirationTime);
                }
            }
            catch (Exception ex)
            {
                logger.ForContext("service_name", GetType().Name)
                    .Error(ex, "Error while updating cache for {Key}", key);
            }
        }

        return false;
    }

    public bool RemoveData(string key)
    {
        if (!_prod) return false;

        try
        {
            logger.ForContext("service_name", GetType().Name)
                .Information($"Cache: RemoveData {key}");

            if (_cacheDb.KeyExists(key))
            {
                return _cacheDb.KeyDelete(key);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while removing cache for {Key}", key);
        }

        return false;
    }

    public void RemoveDataWildcard(string wildcard)
    {
        if (!_prod) return;

        try
        {
            logger.ForContext("service_name", GetType().Name)
                .Information($"Cache: RemoveWildcard {wildcard}");

            EndPoint endPoint = redisManager.GetEndPoints().First();
            var server = redisManager.GetServer(endPoint);

            RedisKey[] rawKeys = server.Keys(pattern: $"*{wildcard}*").ToArray();
            var keys = rawKeys.Where(key => key.ToString().Contains(wildcard)).ToList();

            foreach (var key in keys)
            {
                _cacheDb.KeyDelete(key);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while removing wildcard cache for {Wildcard}", wildcard);
        }
    }

    /*------------ With Fallbacks --------------*/

    public async Task<T?> GetDataWithCacheLockAsync<T>(
        string key,
        Func<Task<T>>? fetchData,
        TimeSpan memoryCacheExpiry,
        TimeSpan redisCacheExpiry)
    {
        // Check memory cache first
        if (memoryCache.TryGetValue(key, out T cachedData))
        {
            return cachedData;
        }

        // Check Redis cache
        var redisData = await GetData<T>(key);
        if (redisData != null)
        {
            memoryCache.Set(key, redisData, memoryCacheExpiry);
            return redisData;
        }

        // Use TaskCompletionSource for lock coordination
        var tcs = _cacheLocks.GetOrAdd(key,
            _ => new TaskCompletionSource<object>(TaskCreationOptions.RunContinuationsAsynchronously));

        try
        {
            // If another thread is already fetching the data, wait for it
            if (!tcs.Task.IsCompleted)
            {
                if (fetchData == null)
                    return default;

                try
                {
                    // Fetch the data
                    var freshData = await fetchData();

                    if (freshData != null)
                    {
                        // Populate both memory and Redis caches if the data is valid
                        memoryCache.Set(key, freshData, memoryCacheExpiry);
                        SetData(key, freshData, redisCacheExpiry);
                    }
                    else
                    {
                        // Remove stale cache data if the result is null
                        memoryCache.Remove(key);
                        RemoveData(key);
                    }

                    // Mark the task as completed and return the data
                    tcs.SetResult(freshData ?? default(T)!);
                    return freshData;
                }
                catch (Exception ex)
                {
                    // Ensure TaskCompletionSource reflects failure
                    tcs.SetException(ex);
                    throw;
                }
            }
            else
            {
                // Wait for the data fetch to complete in another thread
                await tcs.Task;
            }
        }
        finally
        {
            // Ensure the lock is cleaned up for this key
            _cacheLocks.TryRemove(key, out _);
        }

        // Return the fetched data or default if an exception occurred
        return tcs.Task.IsCompletedSuccessfully ? (T?) tcs.Task.Result : default;
    }


    public async Task<Dictionary<string, T?>> GetBatchDataWithCacheLockAsync<T>(
        IEnumerable<string> keys,
        Func<IEnumerable<string>, Task<Dictionary<string, T?>>> fetchMissingKeysAsync,
        TimeSpan memoryCacheExpiry,
        TimeSpan redisCacheExpiry)
    {
        var results = new Dictionary<string, T?>();
        var missingKeys = new List<string>();

        foreach (var key in keys)
        {
            // Try fetching data from memory cache
            var memoryCachedData = memoryCache.Get<T>(key);
            if (memoryCachedData != null)
            {
                results[key] = memoryCachedData;
                continue;
            }

            // Try fetching data from Redis cache
            var redisCachedData = await GetData<T>(key);
            if (redisCachedData != null)
            {
                // Store the Redis cache value in memory for faster subsequent access
                memoryCache.Set(key, redisCachedData, memoryCacheExpiry);
                results[key] = redisCachedData;
                continue;
            }

            // Add missing key to list for fetching
            missingKeys.Add(key);
        }

        if (missingKeys.Count != 0)
        {
            // Lock fetching operation for missing keys to avoid concurrent fetches
            var missingKeyLocks = missingKeys
                .Select(key => cacheLockManager.AcquireLockAsync(key, TimeSpan.FromSeconds(30)))
                .ToList();

            await Task.WhenAll(missingKeyLocks);

            try
            {
                // Fetch missing keys using the provided fetch function
                var fetchedData = await fetchMissingKeysAsync(missingKeys);

                foreach (var key in missingKeys)
                {
                    if (fetchedData.TryGetValue(key, out var data) && data != null)
                    {
                        // Store fetched data in both Redis and memory cache
                        SetData(key, data, redisCacheExpiry, true);
                        memoryCache.Set(key, data, memoryCacheExpiry);
                        results[key] = data;
                    }
                    else
                    {
                        // Invalidate cache for missing or null data
                        await InvalidateCacheAsync(key);
                        results[key] = default;
                    }
                }
            }
            finally
            {
                // Release locks for the missing keys
                foreach (var lockKey in missingKeys)
                {
                    await cacheLockManager.ReleaseLockAsync(lockKey);
                }
            }
        }

        return results;
    }


    public Task InvalidateCacheAsync(string key)
    {
        // Remove from memory cache
        memoryCache.Remove(key);

        // Remove from Redis cache
        RemoveData(key);

        // Optional: Remove any lock related to this key
        _cacheLocks.TryRemove(key, out _);
        return Task.CompletedTask;
    }
}