namespace Shared.Services.Cache;

public interface ICacheService
{
    Task<T?> GetData<T>(string key, bool byPassProd = false);
    Task<IList<T?>> GetBatchData<T>(IEnumerable<string> keys, bool byPassProd = false);
    bool SetData<T>(string key, T value, TimeSpan expirationTime, bool byPassProd = false);
    bool RemoveData(string key);
    void RemoveDataWildcard(string wildcard);

    Task<T?> GetDataWithCacheLockAsync<T>(
        string key,
        Func<Task<T>> fetchData,
        TimeSpan memoryCacheExpiry,
        TimeSpan redisCacheExpiry);

    Task<Dictionary<string, T?>> GetBatchDataWithCacheLockAsync<T>(
        IEnumerable<string> keys,
        Func<IEnumerable<string>, Task<Dictionary<string, T?>>> fetchMissingKeysAsync,
        TimeSpan memoryCacheExpiry,
        TimeSpan redisCacheExpiry);
    
    Task InvalidateCacheAsync(string key);
}