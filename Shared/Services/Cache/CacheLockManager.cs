using System.Collections.Concurrent;

namespace Shared.Services.Cache;

public class CacheLockManager : ICacheLockManager
{
    private readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
    private readonly ConcurrentDictionary<string, SemaphoreSlim> _locks = new();

    public async Task AcquireLockAsync(string key, TimeSpan timeout)
    {
        var lockObject = _locks.GetOrAdd(key, _ => new SemaphoreSlim(1, 1));
        if (!await lockObject.WaitAsync(timeout))
        {
            throw new TimeoutException($"Failed to acquire lock for key {key}");
        }
    }

    public Task ReleaseLockAsync(string key)
    {
        if (_locks.TryGetValue(key, out var lockObject))
        {
            lockObject.Release();
        }

        return Task.CompletedTask;
    }
}
