<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <!-- Core Libraries -->
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.5" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.5" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.5">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.5" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />

        <!-- Serilog Logging -->
        <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
        <PackageReference Include="Serilog.Exceptions" Version="8.4.0" />
        <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
        <PackageReference Include="Serilog.Formatting.Elasticsearch" Version="9.0.3" />
        <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
        <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="9.0.3" />
        <PackageReference Include="SerilogTimings" Version="3.0.1" />

        <!-- Messaging, Auth, and Features -->
        <PackageReference Include="RabbitMQ.Client" Version="6.8.1" />
        <PackageReference Include="GoogleAuthenticator" Version="3.2.0" />
        <PackageReference Include="growthbook-c-sharp" Version="1.0.6" />
        <PackageReference Include="Unleash.Client" Version="4.1.9" />

        <!-- Image and PDF Generation -->
        <PackageReference Include="EPPlus" Version="7.4.1" />
        <PackageReference Include="SixLabors.Fonts" Version="2.0.2" />
        <PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.1.1" />

        <!-- E-Commerce and APIs -->
        <PackageReference Include="ShopifySharp" Version="6.21.0" />
        <PackageReference Include="ShopifyGraphQL.Net" Version="1.0.1" />
        <PackageReference Include="Stripe.net" Version="44.9.0" />

        <!-- Infrastructure -->
        <PackageReference Include="SSH.NET" Version="2024.0.0" />
        <PackageReference Include="StackExchange.Redis" Version="2.7.33" />
        <PackageReference Include="NEST" Version="7.17.5" />
        <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="System.ServiceModel.Duplex" Version="6.0.0" />
        <PackageReference Include="System.ServiceModel.Http" Version="8.0.0" />
        <PackageReference Include="System.ServiceModel.NetTcp" Version="8.0.0" />
        <PackageReference Include="System.ServiceModel.Security" Version="6.0.0" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Dto\Dandomain\" />
        <Folder Include="Elastic\Models\ElasticExposure\" />
    </ItemGroup>

</Project>