using AutoMapper;
using Integration.Models.Elastic.Behavior;
using Integration.Models.Elastic.Order;
using Marlin_OS_Integration_API.Models.Order;
using Shared.Dto.BehaviorDto;
using Shared.Dto.OrderDto;
using Shared.Dto.Setting;
using Shared.Elastic.Behavior;
using Shared.Elastic.Models.Behavior;
using Shared.Elastic.Order;
using Shared.Models.ModelsDal.Setting;

namespace Shared.Mappings
{
    public class Mapper : Profile
    {
        public Mapper()
        {
            //Default
            CreateMap<Setting, SettingDto>().ReverseMap();
            // Behavior
            CreateMap<ElasticBehaviorEvent, BehaviorEventDto>().ReverseMap()
                .ForMember(dest => dest.Event_received, opts =>
                    opts.MapFrom(src => DateTime.UtcNow))
                .ForPath(dest => dest.user_agent!.device.name,
                    opts => opts.MapFrom(src => src.user_agent!.device_name))
                .ForPath(dest => dest.user_agent!.name,
                    opts => opts.MapFrom(src => src.user_agent!.name))
                .ForPath(dest => dest.user_agent!.original,
                    opts => opts.MapFrom(src => src.user_agent!.original))
                .ForPath(dest => dest.user_agent!.version,
                    opts => opts.MapFrom(src => src.user_agent!.version));
            CreateMap<ElasticBehaviorEvent, BehaviorEventDto>().ReverseMap();
            CreateMap<ElasticBehaviorEventClient, BehaviorEventClientDto>().ReverseMap();
            CreateMap<ElasticBehaviorEventUrl, BehaviorEventUrlDto>().ReverseMap();
            CreateMap<ElasticBehaviorEventCustomer, BehaviorEventCustomerDto>().ReverseMap();
            CreateMap<ElasticBehaviorEventShopEvent, BehaviorEventShopEventDto>().ReverseMap();
            CreateMap<ElasticBehaviorEventPlugin, BehaviorEventPluginDto>().ReverseMap();
            CreateMap<ElasticBehaviorEventUserAgent, BehaviorEventUserAgentDto>().ReverseMap();

            // Order
            CreateMap<ElasticOrderEvent, OrderEventDto>().ReverseMap().ForMember(dest => dest.Event_received,
                opts => opts.MapFrom(src => DateTime.UtcNow));
            CreateMap<ElasticOrderEventShopOrder, OrderEventShopOrderDto>().ReverseMap();
            CreateMap<ElasticOrderEventClient, OrderEventClientDto>().ReverseMap();
            CreateMap<ElasticOrderEventUserAgent, OrderEventUserAgentDto>().ReverseMap();
            CreateMap<ElasticOrderEventCustomer, OrderEventCustomerDto>().ReverseMap();
            CreateMap<ElasticOrderEventPlugin, OrderEventPluginDto>().ReverseMap();
            CreateMap<ElasticOrderEventAddress, OrderEventAddressDto>().ReverseMap();
            CreateMap<ElasticOrderEventItem, OrderEventOrderItemDto>().ReverseMap();
            CreateMap<ElasticOrderEventAddressShipping, OrderEventAddressShippingDto>().ReverseMap();
        }
    }
}