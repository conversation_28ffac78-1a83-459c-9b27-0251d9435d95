using Shared.Dto.MerchantScore;

namespace Recommendation_Services.Models.Dto.Rules;

public class MerchantAgeAndGenderScoreDto
{
    public string MerchantId { get; set; } = string.Empty;
    public byte? Age { get; set; }
    public string Gender { get; set; } = string.Empty;
    public decimal Sum { get; set; }

    public MerchantAgeAndGenderScoreDto()
    {
    }

    public MerchantAgeAndGenderScoreDto(MerchantScore merchantScore, string gender, byte? age)
    {
        MerchantId = merchantScore.MerchantId.ToString();
        Gender = gender;
        Age = age;
        Sum = (decimal)merchantScore.Sum;
    }
}

public static class MerchantSegmentHelper
{
    public static string GetCustomerSegment(List<Merchant_Services.Models.ModelsDal.Merchant.MerchantMetum> merchantMeta, string customerSegmentTypeName)
    {
        var data = merchantMeta
            .FirstOrDefault(a => a.FkMerchantMetaTypeName == customerSegmentTypeName)
            ?.Value;
        
        // If no restrictions on any meta all is allowed
        if (data == null)
        {
            var foundAny = merchantMeta.FirstOrDefault(a =>
                a.FkMerchantMetaTypeName == Shared.Models.Merchant.MerchantMetaTypeNames.CustomerSegmentFemale ||
                a.FkMerchantMetaTypeName == Shared.Models.Merchant.MerchantMetaTypeNames.CustomerSegmentMale ||
                a.FkMerchantMetaTypeName == Shared.Models.Merchant.MerchantMetaTypeNames.CustomerSegmentUnknown)?.Value;
            if (foundAny == null)
            {
                return "all";
            }
        }

        return data ?? "00";
    }

    public static bool IsCustomerInSegment(string customerSegment, int? customerAge)
    {
        if (string.IsNullOrWhiteSpace(customerSegment))
            return false;
        return customerSegment?.Contains(customerAge?.ToString() ?? "") ?? false;
    }
} 