using System;
using System.Collections.Generic;

namespace Recommendation_Services.Models;

public class OfferRecommendationDto
{
    public int Id { get; set; }
    public DateTime CreatedDate { get; set; }
    public bool Active { get; set; }
    public string Gender { get; set; }
    public int? Age { get; set; }
    public string Strategy { get; set; }
    public List<OfferRecommendationItemDto> OfferItems { get; set; }
} 