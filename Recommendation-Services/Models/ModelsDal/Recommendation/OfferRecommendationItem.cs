using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Recommendation_Services.Models.ModelsDal.Recommendation;

[Table("OfferRecommendationItem", Schema = "recommendation")]
public partial class OfferRecommendationItem
{
    [Key]
    public int Id { get; set; }

    public int OfferRecommendationId { get; set; }

    [StringLength(30)]
    public string OfferType { get; set; } = null!;

    public long OfferId { get; set; }

    public long? MerchantId { get; set; }

    [Column(TypeName = "decimal(18, 4)")]
    public decimal? Score { get; set; }

    [ForeignKey("OfferRecommendationId")]
    [InverseProperty("OfferRecommendationItems")]
    public virtual OfferRecommendation OfferRecommendation { get; set; } = null!;
}
