using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace Recommendation_Services.Models.ModelsDal.Recommendation;

public partial class RecommendationDbContext : DbContext
{
    public RecommendationDbContext(DbContextOptions<RecommendationDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<OfferRecommendation> OfferRecommendations { get; set; }

    public virtual DbSet<OfferRecommendationItem> OfferRecommendationItems { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<OfferRecommendation>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__OfferRec__3214EC0769AD167C");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.PartnerId).IsRequired();
        });

        modelBuilder.Entity<OfferRecommendationItem>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__OfferRec__3214EC075D3586FF");

            entity.HasOne(d => d.OfferRecommendation).WithMany(p => p.OfferRecommendationItems)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__OfferReco__Offer__3C0AD43D");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
