using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Recommendation_Services.Models.ModelsDal.Recommendation;

[Table("OfferRecommendation", Schema = "recommendation")]
public partial class OfferRecommendation
{
    [Key]
    public int Id { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime CreatedDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(20)]
    public string Gender { get; set; } = null!;

    public int? Age { get; set; }

    public int? CustomerId { get; set; }

    public int? SegmentId { get; set; }

    [StringLength(50)]
    public string Strategy { get; set; } = null!;

    public int PartnerId { get; set; }

    [InverseProperty("OfferRecommendation")]
    public virtual ICollection<OfferRecommendationItem> OfferRecommendationItems { get; set; } = new List<OfferRecommendationItem>();
}
