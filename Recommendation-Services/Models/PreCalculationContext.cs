using System.Collections.Concurrent;
using Serilog;

namespace Recommendation_Services.Models;

public class PreCalculationContext
{
    public Dictionary<string, object> Data { get; } = new();
    public string StrategyName { get; set; } = string.Empty;
    
    // Performance caches shared across rules
    public ConcurrentDictionary<string, object> SharedCache { get; } = new();
    
    // Execution metadata
    public Dictionary<string, TimeSpan> RuleExecutionTimes { get; } = new();
    public DateTime ExecutionStartTime { get; set; } = DateTime.UtcNow;
    
    // Performance optimization flags
    public bool EnableCaching { get; set; } = true;
    public bool EnableParallelProcessing { get; set; } = true;
    public int BatchSize { get; set; } = 10;
    
    // Helper methods for common caching patterns
    public async Task<T> GetOrSetCacheAsync<T>(string key, Func<Task<T>> factory)
    {
        if (!EnableCaching)
            return await factory();
            
        if (SharedCache.TryGetValue(key, out var cached) && cached is T result)
            return result;
            
        var value = await factory();
        SharedCache.TryAdd(key, value);
        return value;
    }
    
    public T GetOrSetCache<T>(string key, Func<T> factory)
    {
        if (!EnableCaching)
            return factory();
            
        if (SharedCache.TryGetValue(key, out var cached) && cached is T result)
            return result;
            
        var value = factory();
        SharedCache.TryAdd(key, value);
        return value;
    }
    
    // Performance tracking
    public void TrackRuleExecution(string ruleName, TimeSpan duration)
    {
        RuleExecutionTimes[ruleName] = duration;
    }
    
    public void LogPerformanceMetrics(ILogger logger)
    {
        var totalTime = DateTime.UtcNow - ExecutionStartTime;
        logger.Information($"Strategy '{StrategyName}' completed in {totalTime.TotalSeconds:F2}s");
        
        foreach (var (rule, time) in RuleExecutionTimes.OrderByDescending(x => x.Value))
        {
            logger.Information($"  {rule}: {time.TotalSeconds:F2}s");
        }
    }
}