using System.Text.RegularExpressions;

namespace Recommendation_Services.Models.Configs;

public static class ProductKeywordFilterConfig
{
    /// <summary>
    /// Global list of keywords that should be filtered out from product names.
    /// These keywords apply to ALL merchants and ALL pipelines.
    /// Matching is case-insensitive and looks for complete word matches within product names.
    /// </summary>
    private static readonly HashSet<string> FilteredKeywords = new(StringComparer.OrdinalIgnoreCase)
    {
        // Add keywords here that should be filtered out
        "test",
        "sample",
        "demo",
        "placeholder",
        "temp",
        "disabled",
        "inactive",
        "null",
        "undefined",
        "n/a",
        "shipping"
    };

    /// <summary>
    /// Checks if a product name contains any filtered keywords as complete words.
    /// </summary>
    /// <param name="productName">The product name to check</param>
    /// <returns>True if the product should be filtered out, false otherwise</returns>
    public static bool ShouldFilterProduct(string? productName)
    {
        if (string.IsNullOrWhiteSpace(productName))
            return false; // Don't filter empty names

        // Check if any filtered keyword appears as a complete word in the product name
        return FilteredKeywords.Any(keyword => IsWholeWordMatch(productName, keyword));
    }

    /// <summary>
    /// Checks if a keyword appears as a complete word in the text using word boundaries.
    /// </summary>
    /// <param name="text">The text to search in</param>
    /// <param name="keyword">The keyword to search for</param>
    /// <returns>True if the keyword is found as a complete word</returns>
    private static bool IsWholeWordMatch(string text, string keyword)
    {
        // Use regex with word boundaries (\b) to ensure whole word matching
        // This prevents "demon" from matching "Demon" in "Ange Ou Demon Le Secret"
        var pattern = $@"\b{Regex.Escape(keyword)}\b";
        return Regex.IsMatch(text, pattern, RegexOptions.IgnoreCase);
    }

    /// <summary>
    /// Gets all configured filtered keywords for debugging/administration purposes.
    /// </summary>
    /// <returns>A read-only collection of filtered keywords</returns>
    public static IReadOnlySet<string> GetFilteredKeywords() => FilteredKeywords;

    /// <summary>
    /// Adds a new keyword to the filter list.
    /// Note: This is for runtime configuration. For permanent changes, modify the FilteredKeywords list directly.
    /// </summary>
    /// <param name="keyword">The keyword to add</param>
    /// <returns>True if the keyword was added, false if it already existed</returns>
    public static bool AddKeyword(string keyword)
    {
        if (string.IsNullOrWhiteSpace(keyword))
            return false;

        return FilteredKeywords.Add(keyword.Trim());
    }

    /// <summary>
    /// Removes a keyword from the filter list.
    /// Note: This is for runtime configuration. For permanent changes, modify the FilteredKeywords list directly.
    /// </summary>
    /// <param name="keyword">The keyword to remove</param>
    /// <returns>True if the keyword was removed, false if it didn't exist</returns>
    public static bool RemoveKeyword(string keyword)
    {
        if (string.IsNullOrWhiteSpace(keyword))
            return false;

        return FilteredKeywords.Remove(keyword.Trim());
    }

    /// <summary>
    /// Test method to verify keyword filtering logic
    /// </summary>
    public static void TestKeywordFiltering()
    {
        Console.WriteLine("=== Testing Product Keyword Filtering (Whole Word Matching) ===");
        
        var testProducts = new[]
        {
            "iPhone 13 Pro Max",
            "Test Product - Do Not Buy",
            "Sample T-Shirt",
            "Nike Air Max 90",
            "DEMO VERSION - Laptop",
            "Placeholder Item",
            "Regular Product Name",
            "DEBUG: Internal Use Only",
            "Lorem Ipsum Dolor Sit Amet",
            "Temporary Sale Item",
            "Draft Product Description",
            "Normal Product Without Issues",
            "Inspired By Givenchy Ange Ou Demon Le Secret", // Should NOT be filtered
            "This is a test", // Should be filtered
            "Testing Product", // Should NOT be filtered (testing != test)
            "Sample-Product", // Should be filtered (sample is a whole word)
            "Sampling Tool", // Should NOT be filtered (sampling != sample)
            "Product for demo purposes", // Should be filtered
            "Demonstration Video", // Should NOT be filtered (demonstration != demo)
            "temp-file.txt", // Should be filtered
            "temperature sensor", // Should NOT be filtered (temperature != temp)
            "Private Label Product", // Should be filtered
            "Privacy Policy", // Should NOT be filtered (privacy != private)
        };

        foreach (var product in testProducts)
        {
            var shouldFilter = ShouldFilterProduct(product);
            Console.WriteLine($"Product: '{product}' -> {(shouldFilter ? "FILTERED OUT" : "ALLOWED")}");
        }
        
        Console.WriteLine("\nWhole word keyword filtering test completed.");
        Console.WriteLine("Note: 'Inspired By Givenchy Ange Ou Demon Le Secret' should be ALLOWED");
        Console.WriteLine("because 'Demon' is part of a product name, not a standalone filtered word.");
    }
} 