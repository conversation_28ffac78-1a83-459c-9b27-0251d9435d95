namespace Recommendation_Services.Models.Configs;

public static class MerchantCategoryFilterConfig
{
    public enum FilterType
    {
        AllowOnly,    // Only include products with these categories
        Disallow      // Exclude products with these categories
    }

    public class CategoryFilter
    {
        public FilterType Type { get; set; }
        public List<string> Categories { get; set; } = new();
        public string? Gender { get; set; } // Optional: filter applies only to specific gender
        public int? Age { get; set; } // Optional: filter applies only to specific age
    }

    private static readonly Dictionary<int, List<CategoryFilter>> MerchantFilters = new()
    {
        // Merchant 5653: Women should only get products with 'Women' category
        [5653] = new()
        {
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "Women" },
                Gender = "Female"
            },
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "Men" },
                Gender = "Female"
            },
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "Men" },
                Gender = "Male"
            },
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "Women" },
                Gender = "Male"
            }
        },
        
        // Merchant 5645: Females should not get products with 'Men' category
        [5645] = new()
        {
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "Men" },
                Gender = "Female"
            },
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "Men" },
                Gender = "Male"
            },
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "Women" },
                Gender = "Male"
            }
        },
        [5650] = new()
        {
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "gender:men" },
                Gender = "Male"
            },
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "color_group:Pink", "color_group:Red" },
                Gender = "Male"
            },
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "gender:women" },
                Gender = "Female"
            },
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "gender:men" },
                Gender = "Female"
            }
        },
        [5636] = new()
        {
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "gender:mens" },
                Gender = "Male"
            },
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "gender:womens", "gender:kids", "gender:baby", "gender:toddler" },
                Gender = "Male"
            },
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "gender:mens", "gender:kids", "gender:baby", "gender:toddler" },
                Gender = "Female"
            },
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "gender:womens" },
                Gender = "Female"
            }
        },
        [5652] = new()
        {
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "Men ", "Scents", "Hefted Laces", "Sneaker Care", "Socks", "Bags", "Footwear", "Underwear", "Headwear", "Belts", "Bags" },
                Gender = "Male"
            },
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "Women" },
                Gender = "Male"
            },
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "Women" },
                Gender = "Female"
            },
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "Men", "Scents", "Hefted Laces", "Sneaker Care", "Socks", "Bags", "Footwear", "Underwear", "Headwear", "Belts", "Bags" },
                Gender = "Female"
            }
        },
        [5624] = new()
        {
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "Men" },
                Gender = "Male"
            },
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "Women" },
                Gender = "Male"
            },
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "Women" },
                Gender = "Female"
            },
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "Men" },
                Gender = "Female"
            }
        },
        [5641] = new()
        {
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "Mens", "Bedding" },
                Gender = "Male"
            },
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "Women" },
                Gender = "Male"
            },
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "women", "Bedding" },
                Gender = "Female"
            },
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "Mens" },
                Gender = "Female"
            }
        },
        [5625] = new()
        {
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "Male" },
                Gender = "Male"
            },
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "Female" },
                Gender = "Male"
            },
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "Women" },
                Gender = "Female"
            }
        }
    };

    /// <summary>
    /// Gets category filters for a specific merchant and demographic
    /// </summary>
    public static List<CategoryFilter> GetFiltersForMerchant(int merchantId, string? gender = null, int? age = null)
    {
        if (!MerchantFilters.TryGetValue(merchantId, out var allFilters))
            return new List<CategoryFilter>();

        // Filter rules based on demographic criteria
        return allFilters.Where(filter =>
            (filter.Gender == null || filter.Gender.Equals(gender, StringComparison.OrdinalIgnoreCase)) &&
            (filter.Age == null || filter.Age == age)
        ).ToList();
    }

    /// <summary>
    /// Checks if a product should be included based on category filters
    /// </summary>
    public static bool ShouldIncludeProduct(string? productCategories, List<CategoryFilter> filters)
    {
        if (!filters.Any())
            return true; // No filters = include all products

        if (string.IsNullOrWhiteSpace(productCategories))
            return true; // No categories = include (unless explicitly filtered)

        var categories = productCategories.Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(c => c.Trim())
            .ToHashSet(StringComparer.OrdinalIgnoreCase);

        // Group filters by type
        var allowOnlyFilters = filters.Where(f => f.Type == FilterType.AllowOnly).ToList();
        var disallowFilters = filters.Where(f => f.Type == FilterType.Disallow).ToList();

        // Check AllowOnly filters - if any exist, at least one must pass
        if (allowOnlyFilters.Any())
        {
            bool allowOnlyPassed = false;
            foreach (var filter in allowOnlyFilters)
            {
                // Product must have at least one of the allowed categories
                if (filter.Categories.Any(allowedCategory => categories.Contains(allowedCategory)))
                {
                    allowOnlyPassed = true;
                    break;
                }
            }
            
            if (!allowOnlyPassed)
                return false; // Failed AllowOnly requirement
        }

        // Check Disallow filters - if any exist, all must pass (no disallowed categories found)
        if (disallowFilters.Any())
        {
            foreach (var filter in disallowFilters)
            {
                // Product must not have any of the disallowed categories
                if (filter.Categories.Any(disallowedCategory => categories.Contains(disallowedCategory)))
                {
                    return false; // Failed Disallow requirement
                }
            }
        }

        return true; // Passed all filter requirements
    }

    /// <summary>
    /// Gets all configured merchant IDs for testing/debugging
    /// </summary>
    public static IEnumerable<int> GetConfiguredMerchants() => MerchantFilters.Keys;

    /// <summary>
    /// Test method to verify category filtering logic
    /// </summary>
    public static void TestCategoryFiltering()
    {
        // Test Merchant 5653 (Women only filter for females)
        var filters5653Female = GetFiltersForMerchant(5653, "Female");
        Console.WriteLine($"Merchant 5653 Female filters: {filters5653Female.Count}");
        
        // Test products for merchant 5653
        Console.WriteLine($"Product 'Women, Clothing' -> {ShouldIncludeProduct("Women, Clothing", filters5653Female)}"); // Should be true
        Console.WriteLine($"Product 'Men, Clothing' -> {ShouldIncludeProduct("Men, Clothing", filters5653Female)}"); // Should be false
        Console.WriteLine($"Product 'Women' -> {ShouldIncludeProduct("Women", filters5653Female)}"); // Should be true
        Console.WriteLine($"Product 'Accessories' -> {ShouldIncludeProduct("Accessories", filters5653Female)}"); // Should be false
        
        // Test Merchant 5645 (No Men for Females)
        var filters5645Female = GetFiltersForMerchant(5645, "Female");
        Console.WriteLine($"\nMerchant 5645 Female filters: {filters5645Female.Count}");
        
        Console.WriteLine($"Product 'Women, Clothing' -> {ShouldIncludeProduct("Women, Clothing", filters5645Female)}"); // Should be true
        Console.WriteLine($"Product 'Men, Clothing' -> {ShouldIncludeProduct("Men, Clothing", filters5645Female)}"); // Should be false
        Console.WriteLine($"Product 'Accessories' -> {ShouldIncludeProduct("Accessories", filters5645Female)}"); // Should be true
        Console.WriteLine($"Product 'Men' -> {ShouldIncludeProduct("Men", filters5645Female)}"); // Should be false
        
        // Test combined filtering scenario
        Console.WriteLine("\n=== Testing Combined AllowOnly + Disallow Filtering ===");
        
        // Create a test scenario with both AllowOnly and Disallow
        var combinedFilters = new List<CategoryFilter>
        {
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "gender:mens", "Clothing" }
            },
            new CategoryFilter
            {
                Type = FilterType.Disallow,
                Categories = new() { "gender:kids", "baby" }
            }
        };
        
        Console.WriteLine("Combined Filter Rules: Allow only 'gender:mens' OR 'Clothing', but disallow 'gender:kids' OR 'baby'");
        Console.WriteLine($"Product 'gender:mens, shoes' -> {ShouldIncludeProduct("gender:mens, shoes", combinedFilters)}"); // Should be true (has gender:mens, no kids/baby)
        Console.WriteLine($"Product 'Clothing, accessories' -> {ShouldIncludeProduct("Clothing, accessories", combinedFilters)}"); // Should be true (has Clothing, no kids/baby)
        Console.WriteLine($"Product 'gender:mens, gender:kids' -> {ShouldIncludeProduct("gender:mens, gender:kids", combinedFilters)}"); // Should be false (has gender:kids)
        Console.WriteLine($"Product 'gender:womens, shoes' -> {ShouldIncludeProduct("gender:womens, shoes", combinedFilters)}"); // Should be false (no allowed categories)
        Console.WriteLine($"Product 'Clothing, baby, toys' -> {ShouldIncludeProduct("Clothing, baby, toys", combinedFilters)}"); // Should be false (has baby)
        Console.WriteLine($"Product 'electronics' -> {ShouldIncludeProduct("electronics", combinedFilters)}"); // Should be false (no allowed categories)
        
        // Test multiple AllowOnly filters
        var multipleAllowFilters = new List<CategoryFilter>
        {
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "Men" }
            },
            new CategoryFilter
            {
                Type = FilterType.AllowOnly,
                Categories = new() { "Sports" }
            }
        };
        
        Console.WriteLine("\n=== Testing Multiple AllowOnly Filters ===");
        Console.WriteLine("Rules: Allow 'Men' OR 'Sports'");
        Console.WriteLine($"Product 'Men, Clothing' -> {ShouldIncludeProduct("Men, Clothing", multipleAllowFilters)}"); // Should be true
        Console.WriteLine($"Product 'Sports, Equipment' -> {ShouldIncludeProduct("Sports, Equipment", multipleAllowFilters)}"); // Should be true
        Console.WriteLine($"Product 'Women, Accessories' -> {ShouldIncludeProduct("Women, Accessories", multipleAllowFilters)}"); // Should be false
        
        Console.WriteLine("\nCategory filtering test completed.");
    }
} 