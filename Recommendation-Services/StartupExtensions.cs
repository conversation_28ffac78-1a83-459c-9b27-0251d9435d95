using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Recommendation_Services.Models.ModelsDal.Recommendation;
using Recommendation_Services.Repositories;
using Recommendation_Services.Services;
using Recommendation_Services.Services.Rules;

namespace Recommendation_Services;

public static class StartupExtensions
{
    public static IServiceCollection AddRecommendationServices(this IServiceCollection services, string connectionString)
    {
        services.AddDbContext<RecommendationDbContext>(options =>
            options.UseSqlServer(connectionString));
        services.AddScoped<IOfferRecommendationRepository, OfferRecommendationRepository>();
        services.AddScoped<IOfferRecommendationService, OfferRecommendationService>();

        // Rules
        services.AddScoped<IOfferPreCalculationRule, DiscountPreCalculationRule>();
        services.AddScoped<IOfferPreCalculationRule, CuratedProductsPriceDropRule>();
        services.AddScoped<IOfferPreCalculationRule, CuratedProductsRule>();
        services.AddScoped<IOfferPreCalculationRule, MerchantAgeAndGenderRule>();
        services.AddScoped<IOfferPreCalculationRule, RankedMerchantProductsByBehaviorRule>();
        services.AddScoped<IOfferPreCalculationRule, ProductKeywordFilterRule>();
        services.AddScoped<IOfferPreCalculationRule, CategoryFilterRule>();
        
        services.AddScoped<IOfferPreCalculationRuleRegistry, OfferPreCalculationRuleRegistry>();
        //services.AddHostedService<OfferPreCalculationBackgroundService>();
        return services;
    }
} 