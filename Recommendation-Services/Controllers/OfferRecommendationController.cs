using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Recommendation_Services.Models;
using Recommendation_Services.Models.ModelsDal.Recommendation;
using Recommendation_Services.Services;
using Shared.Elastic.Elastic;
using Webshop.Webshop;

namespace Recommendation_Services.Controllers;

[ApiController]
[Route("[controller]")]
public class OfferRecommendationController(IOfferRecommendationService service, IElasticService elasticService, IMerchantService merchantService) : ControllerBase
{
    [HttpGet]
    public async Task<ActionResult<List<OfferRecommendation>>> Get([FromQuery] string strategy, [FromQuery] int? numberOfRecommendationItems = 10, [FromQuery] string? gender = null, [FromQuery] int? age = null)
    {
        var recommendations = await service.GetRecommendationEntitiesAsync(strategy, numberOfRecommendationItems ?? 10, gender, age);
        return Ok(recommendations);
    }

    [HttpGet("RunRecommendations")]
    [AllowAnonymous]
    public async Task<ActionResult<List<OfferRecommendation>>> RunRecommendations()
    {
        await service.RunRecommendationsAsync(CancellationToken.None);
        return Ok();
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<OfferRecommendationDto>> GetById(int id)
    {
        var recommendation = await service.GetByIdAsync(id);
        if (recommendation == null) return NotFound();
        return Ok(recommendation);
    }

    [HttpGet("paginated")]
    public async Task<ActionResult<List<OfferRecommendationDto>>> GetPaginated(
        [FromQuery] string strategy,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? sortBy = null)
    {
        var recommendations = await service.GetPagedRecommendationsAsync(strategy, page, pageSize, sortBy);
        return Ok(recommendations);
    }

    [HttpGet("test-elastic")]
    public async Task<ActionResult> TestElastic(
        [FromQuery] string index = "invoices-potentiallines",
        [FromQuery] string gender = "Female",
        [FromQuery] int? age = 30)
    {
        try
        {
            var merchants = await merchantService.GetAllMerchantIdsWithAllowedMarketingByPartnerIdAsync(87317);

            // Test diagnostic query first
            var diagnosticResult = await elasticService.DiagnosticQuery(index, merchants, gender, age);
            
            // Test simplified method
            var simplifiedResult = await elasticService.GetLinesExtendedSimplified(index, merchants, gender, age, 4);
            
            // Test original extended method
            var extendedResult = await elasticService.GetLinesExtended(index, merchants, gender, age, 4);
            
            // Test new age decay method
            var ageDecayResult = await elasticService.GetLinesExtendedWithAgeDecay(index, merchants, gender, age, 4);
            
            return Ok(new
            {
                Diagnostic = diagnosticResult,
                SimplifiedResult = simplifiedResult,
                ExtendedResult = extendedResult,
                AgeDecayResult = ageDecayResult
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error: {ex.Message}");
        }
    }

    [HttpGet("test-age-decay")]
    public async Task<ActionResult> TestAgeDecay(
        [FromQuery] string index = "invoices-potentiallines",
        [FromQuery] string gender = "Female",
        [FromQuery] int? age = 30)
    {
        try
        {
            var merchants = await merchantService.GetAllMerchantIdsWithAllowedMarketingByPartnerIdAsync(87317);

            // Test the new age decay method
            var ageDecayResult = await elasticService.GetLinesExtendedWithAgeDecay(index, merchants, gender, age, 10);
            
            // Also test without age for comparison
            var noAgeResult = await elasticService.GetLinesExtendedWithAgeDecay(index, merchants, gender, null, 10);
            
            return Ok(new
            {
                WithAgeDecay = ageDecayResult.Where(r => r.Sum > 0).Take(10).ToList(),
                WithoutAge = noAgeResult.Where(r => r.Sum > 0).Take(10).ToList(),
                Summary = new
                {
                    WithAgeDecayCount = ageDecayResult.Count(r => r.Sum > 0),
                    WithoutAgeCount = noAgeResult.Count(r => r.Sum > 0),
                    TotalMerchants = merchants.Count
                }
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Error: {ex.Message}");
        }
    }
} 