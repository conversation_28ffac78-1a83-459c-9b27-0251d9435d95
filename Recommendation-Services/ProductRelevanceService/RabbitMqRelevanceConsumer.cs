using System.ComponentModel.DataAnnotations;
using System.Text;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using Recommendation_Services.ProductRelevanceService.Models;

namespace Recommendation_Services.ProductRelevanceService;

public class RabbitMqRelevanceBackgroundService(
    [FromKeyedServices("cloud")] IConnection connection,
    ILogger<RabbitMqRelevanceBackgroundService> logger,
    IMemoryCache memoryCache,
    IServiceProvider serviceProvider)
    : BackgroundService
{
    private const string merchantCacheKey = "RabbitMqRelevanceBackgroundService.Merchants";
    private IModel _channel;
    private const string QueueName = "product.relevance.reinforcement.queue";
    private const int ExpectedPartnerId = 87317;

    public override Task StartAsync(CancellationToken cancellationToken)
    {
        _channel = connection.CreateModel();
        _channel.BasicQos(prefetchSize: 0, prefetchCount: 1, global: false);

        logger.LogInformation("RabbitMQ channel initialized, queue: {QueueName}", QueueName);
        return base.StartAsync(cancellationToken);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var consumer = new EventingBasicConsumer(_channel);
        consumer.Received += async (sender, ea) =>
        {
            using var scope = serviceProvider.CreateScope();
            var relevanceService = scope.ServiceProvider.GetRequiredService<IProductRelevanceService>();
            var merchantDbContext = scope.ServiceProvider.GetRequiredService<MerchantDbContext>();
            var memoryCache = scope.ServiceProvider.GetRequiredService<IMemoryCache>();

            var body = ea.Body.ToArray();
            var message = Encoding.UTF8.GetString(body);
            try
            {
                var payload = ParsePayload(message);
                await ValidatePayload(memoryCache, merchantDbContext, payload);
                try
                {
                    await relevanceService.ReinforceRecommendationAsync(payload);
                    await Task.Delay(TimeSpan.FromMilliseconds(300), stoppingToken);
                    _channel.BasicAck(ea.DeliveryTag, multiple: false);
                    logger.LogInformation("Processed relevance for product {ProductId} with interaction {Interaction}",
                        payload.ProductId, payload.Action);
                }
                catch (Exception ex) // transient failure (retryable)
                {
                    logger.LogError(ex, "Failed to process payload, requeuing message: {Message}", message);
                    _channel.BasicNack(ea.DeliveryTag, multiple: false, requeue: true);
                }
            }
            catch (Exception ex) // bad payload (non-retryable)
            {
                logger.LogError(ex, "Failed to parse/validate message, dropping it: {Message}", message);
                _channel.BasicNack(ea.DeliveryTag, multiple: false, requeue: false);
            }
        };
        _channel.BasicQos(prefetchSize: 0, prefetchCount: 1, global: false);

        _channel.BasicConsume(queue: QueueName, autoAck: false, consumer: consumer);

        while (!stoppingToken.IsCancellationRequested)
        {
            await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
        }
    }
    private static async Task ValidatePayload(IMemoryCache memoryCache, MerchantDbContext merchantDbContext, RelevancePayload payload)
    {
        var merchants = await memoryCache.GetOrCreateAsync(merchantCacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(6);
            return await merchantDbContext.Merchants.ToListAsync()
                .ConfigureAwait(false);
        });
        
        var merchant = merchants?.Find(m => m.Id == payload.MerchantId);

        if (merchant == null)
            throw new InvalidOperationException($"Merchant with ID {payload.MerchantId} not found.");

        if (merchant.FkPartnerId != ExpectedPartnerId)
            throw new UnauthorizedAccessException($"Merchant with ID {payload.MerchantId} does not belong to the expected partner ID ${ExpectedPartnerId}.");
    }

    private static RelevancePayload ParsePayload(string message)
    {
        var payload = JsonConvert.DeserializeObject<RelevancePayload>(message);
        if (payload == null)
            throw new JsonSerializationException("Deserialized payload is null.");
        
        // Trigger validation
        var context = new ValidationContext(payload);
        var results = new List<ValidationResult>();
        if (Validator.TryValidateObject(payload, context, results, validateAllProperties: true)) return payload;
        var errorMessages = string.Join("; ", results.Select(r => r.ErrorMessage));
        throw new ValidationException($"Payload validation failed: {errorMessages}");
    }

    public override Task StopAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation($"Stopping RabbitMQ consumer for {QueueName} and closing channel");
        _channel?.Close();
        connection?.Close();
        return base.StopAsync(cancellationToken);
    }

    public override void Dispose()
    {
        _channel?.Dispose();
        connection?.Dispose();
        base.Dispose();
    }
}