using Customer_Services.Models.ModelsDal.Customer;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Nest;
using Recommendation_Services.ProductRelevanceService.Models;

namespace Recommendation_Services.ProductRelevanceService;

public class CustomerSegmentMapResult
{
    public const double HitFactor = 0.10;
    public const double HitInitialScore = 0.1;

    public const double SameGenderFactor = 0.01;
    public const double SameGenderInitialScore = 0.03;

    public const double OtherFactor = 0.0001;
    public const double OtherInitialScore = 0.0;

    public required CustomerSegment Segment { get; set; }

    /// <summary>
    /// The reinforcement factor (e.g., 0.10 for hit, 0.02 for others)
    /// </summary>
    public double Factor { get; set; }

    /// <summary>
    /// The initial score to use if this segment does not yet have a score.
    /// </summary>
    public double InitialScore { get; set; }
}

public class ProductRelevanceService(
    ProductRelevanceDbContext productRelevanceDbContext,
    IMemoryCache memoryCache,
    CustomerDbContext customerDbContext) : IProductRelevanceService
{
    private const string CacheSegmentsKey = "ProductRelevanceService.segments";
    private const string CacheCustomersKey = "ProductRelevanceService.customers_Happy_Pay";
    private const string HappyPayId = "87317";

    public async Task<IEnumerable<Guid>> GetProductsForCustomerSegmentAsync(string customerEmail, long merchantId)
    {
        return await productRelevanceDbContext.ProductRelevanceScores
            //.Where(score => score.CustomerSegmentId == segmentId)  use only one segment
            //.OrderByDescending(score => score.Score)
            //.LeftJoin(Product) // enrich with product data
            .Select(score => score.ProductId)
            .ToListAsync();
    }

   public async Task ReinforceRecommendationAsync(RelevancePayload payload)
    {
        var interaction = productRelevanceDbContext.ProductInteractions.Add(new ProductInteraction
        {
            Id = Guid.NewGuid(),
            CustomerEmail = payload.CustomerEmail,
            MerchantId = payload.MerchantId,
            ProductId = payload.ProductId,
            Action = payload.Action,
            Used = true
        });

        var allSegmentsMapResults = await this.MapEmailToSegment(payload.CustomerEmail);
        var segmentIds = allSegmentsMapResults.Select(s => s.Segment.Id).ToList();
        var now = DateTime.UtcNow;

        // Single DB query for all matching relevance scores
        var existingScores = await productRelevanceDbContext.ProductRelevanceScores
            .Where(r => r.ProductId == payload.ProductId && segmentIds.Contains(r.CustomerSegmentId))
            .ToListAsync();

        // Map for faster access
        var existingScoresMap = existingScores.ToDictionary(r => r.CustomerSegmentId, r => r);

        foreach (var entry in allSegmentsMapResults)
        {
            if (existingScoresMap.TryGetValue(entry.Segment.Id, out var relevanceScore))
            {
                relevanceScore.Score += (1 - relevanceScore.Score) * entry.Factor;
                relevanceScore.LastModifiedDate = now;
                relevanceScore.ReinforcementsIds ??= new List<Guid>();
                relevanceScore.ReinforcementsIds.Append(interaction.Entity.Id);
            }
            else
            {
                productRelevanceDbContext.ProductRelevanceScores.Add(new ProductRelevanceScore
                {
                    Id = Guid.NewGuid(),
                    ProductId = payload.ProductId,
                    MerchantId = payload.MerchantId,
                    CustomerSegmentId = entry.Segment.Id,
                    Score = entry.InitialScore,
                    ReinforcementsIds = new List<Guid> { interaction.Entity.Id },
                    CreatedDate = now,
                    LastModifiedDate = now
                });
            }
        }

        await productRelevanceDbContext.SaveChangesAsync();
}

    private async Task<List<CustomerSegmentMapResult>> MapEmailToSegment(string payloadCustomerEmail)
    {
        var segments = await memoryCache.GetOrCreateAsync(CacheSegmentsKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(24);
            return await productRelevanceDbContext.CustomerSegments.ToListAsync().ConfigureAwait(false);
        });

        var (customerAge, customerGender) 
            = await GetCustomerData(payloadCustomerEmail);

        Guid? hitSegmentId = null;
        var sameGenderSet = new HashSet<Guid>();
        
        if (customerAge.HasValue && customerGender.HasValue)
        {
            var closest = segments?
                .Where(s => s.Gender == customerGender)
                .OrderBy(s => Math.Abs(s.Age - customerAge.Value))
                .FirstOrDefault();

            if (closest != null)
                hitSegmentId = closest.Id;

            foreach (var customerSegment in segments?.Where(s => s.Gender == customerGender)!)
            {
                sameGenderSet.Add(customerSegment.Id);
            }
        }
        else if (customerGender.HasValue)
        {
            foreach (var segment in segments?.Where(s => s.Gender == customerGender)!)
                sameGenderSet.Add(segment.Id);
        }

        return segments?.Select(segment =>
        {
            var isHit = hitSegmentId.HasValue && segment.Id == hitSegmentId.Value;
            var isSameGender = sameGenderSet.Contains(segment.Id);

            return new CustomerSegmentMapResult
            {
                Segment = segment,
                Factor = isHit
                    ? CustomerSegmentMapResult.HitFactor
                    : isSameGender
                        ? CustomerSegmentMapResult.SameGenderFactor
                        : CustomerSegmentMapResult.OtherFactor,

                InitialScore = isHit
                    ? CustomerSegmentMapResult.HitInitialScore
                    : isSameGender
                        ? CustomerSegmentMapResult.SameGenderInitialScore
                        : CustomerSegmentMapResult.OtherInitialScore
            };
        }).ToList() ?? [];
    }

    private async Task<(int? Age, Gender? Gender)> GetCustomerData(string payloadCustomerEmail)
    {
        if (string.IsNullOrWhiteSpace(payloadCustomerEmail))
            return (null, null);

        var customers = await memoryCache.GetOrCreateAsync(CacheCustomersKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(12);
            return await customerDbContext.Customers
                .AsNoTracking()
                .Where(c => c.PartnerId == HappyPayId)
                .ToListAsync()
                .ConfigureAwait(false);
        });
        var customer = customers?
            .FirstOrDefault(c => c.Email != null &&
                                 c.Email.Equals(payloadCustomerEmail, StringComparison.OrdinalIgnoreCase));

        if (customer == null)
            return (null, null);

        Gender? customerGender = !string.IsNullOrWhiteSpace(customer.Gender)
            ? ParseGender(customer.Gender)
            : null;

        return (customer.Age, customerGender);
    }



    static Gender ParseGender(string? genderString)
    {
        return genderString?.Trim().ToLowerInvariant() switch
        {
            "male" => Gender.Male,
            "female" => Gender.Female,
            "unknown" or null => Gender.Other, // map "Unknown" and null to Other
            _ => Gender.Other // fallback for unexpected input
        };
    }

    public async Task<IEnumerable<CustomerSegment>> GetSegments()
    {
        return await productRelevanceDbContext.CustomerSegments.ToListAsync();
    }
}