using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Recommendation_Services.ProductRelevanceService.Models;

public class RelevancePayload
{
    // This is because DataSyncWorker doesn't capitalise this field like others
    [JsonProperty("customerEmail")]
    [Required(ErrorMessage = "CustomerEmail is required.")]
    [EmailAddress(ErrorMessage = "CustomerEmail must be a valid email address.")]
    public required string CustomerEmail { get; init; }

    [JsonProperty(nameof(MerchantId))]
    public required int MerchantId { get; init; }

    [JsonProperty(nameof(ProductId))] 
    public required Guid ProductId { get; init; }

    [JsonProperty(nameof(Action))]
    public required InteractionType Action { get; init; }
}