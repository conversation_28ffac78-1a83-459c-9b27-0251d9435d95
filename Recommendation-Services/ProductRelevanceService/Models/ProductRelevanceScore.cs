namespace Recommendation_Services.ProductRelevanceService.Models;

public class ProductRelevanceScore
{
    public Guid Id { get; set; }
    public Guid ProductId { get; set; }
    public int MerchantId { get; set; }
    public Guid CustomerSegmentId { get; set; }
    public double Score { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastModifiedDate { get; set; }

    public DateTime? LastProcessedDate { get; set; } 

    public IEnumerable<Guid>? ReinforcementsIds { get; set; }

    public CustomerSegment CustomerSegment { get; set; }
}
