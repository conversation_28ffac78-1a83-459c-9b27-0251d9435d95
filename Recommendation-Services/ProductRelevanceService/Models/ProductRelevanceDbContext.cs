using System.Text.Json;
using Microsoft.EntityFrameworkCore;

namespace Recommendation_Services.ProductRelevanceService.Models;

public class ProductRelevanceDbContext(DbContextOptions<ProductRelevanceDbContext> options) : DbContext(options)
{
    public virtual DbSet<CustomerSegment> CustomerSegments { get; set; }
    public virtual DbSet<ProductRelevanceScore> ProductRelevanceScores { get; set; }
    public virtual DbSet<ProductInteraction> ProductInteractions { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<CustomerSegment>(entity =>
        {
            entity.ToTable("CustomerSegment", "ProductRelevance");

            entity.HasKey(e => e.Id);
            entity.Property(e => e.Age).IsRequired();
            entity.Property(e => e.Gender)
                  .HasConversion<string>() // Store enum as string
                  .IsRequired();
        });

        modelBuilder.Entity<ProductRelevanceScore>(entity =>
        {
            entity.ToTable("ProductRelevanceScore", "ProductRelevance");

            entity.HasKey(e => e.Id);
            entity.Property(e => e.ProductId).IsRequired();
            entity.Property(e => e.Score).IsRequired();
            entity.Property(e => e.ReinforcementsIds)
                .HasConversion(
                    v => JsonSerializer.Serialize(v, JsonSerializerOptions.Default),
                    v => JsonSerializer.Deserialize<IEnumerable<Guid>>(v, JsonSerializerOptions.Default) ?? Array.Empty<Guid>())
                .HasColumnName("ReinforcementsIds");
            entity.Property(e => e.CreatedDate)
                  .HasColumnType("datetime2")
                  .IsRequired();
            entity.Property(e => e.LastModifiedDate)
                  .HasColumnType("datetime2")
                  .IsRequired();

            entity.HasOne(e => e.CustomerSegment)
                  .WithMany() // No back reference in CustomerSegment
                  .HasForeignKey(e => e.CustomerSegmentId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
        
        modelBuilder.Entity<ProductInteraction>(entity =>
        {
            entity.ToTable("ProductInteraction", "ProductRelevance");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id)
                .HasColumnName("Id")
                .IsRequired()
                .ValueGeneratedOnAdd(); // signals that the value is generated
            
            entity.Property(e => e.Used)
                .HasColumnName("Used")
                .IsRequired();

            entity.Property(e => e.CustomerEmail)
                .HasColumnName("CustomerEmail")
                .HasMaxLength(320)
                .IsRequired();

            entity.Property(e => e.MerchantId)
                .HasColumnName("MerchantId")
                .IsRequired();

            entity.Property(e => e.ProductId)
                .HasColumnName("ProductId")
                .HasMaxLength(255)
                .IsRequired();

            entity.Property(e => e.Action)
                .HasColumnName("InteractionType")
                .HasConversion(
                    v => v.ToString(),     
                    v => Enum.Parse<InteractionType>(v))
                .IsRequired();
        });

        base.OnModelCreating(modelBuilder);
    }
}