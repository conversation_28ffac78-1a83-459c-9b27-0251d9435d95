using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Recommendation_Services.ProductRelevanceService.Models;

namespace Recommendation_Services.ProductRelevanceService;

public class ProductRelevanceDecayBackgroundService(
    IServiceProvider serviceProvider,
    ILogger<ProductRelevanceDecayBackgroundService> _logger)
    : BackgroundService
{
    private readonly TimeSpan _interval = TimeSpan.FromDays(1);
    private readonly double DecreaseFactor =  0.9;
    private const int BatchSize = 100;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Product relevance decay service starting, waiting for 4AM...");

        var now = DateTime.UtcNow;
        var next4Am = now.Date.AddDays(now.Hour >= 4 ? 1 : 0).AddHours(4);
        var delayUntil4Am = next4Am - now;
        await Task.Delay(delayUntil4Am, stoppingToken);

        _logger.LogInformation("Start reached. Starting decay process every 24h.");
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = serviceProvider.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<ProductRelevanceDbContext>();

                var skip = 0;

                DateTime processingCutoff = DateTime.UtcNow.AddHours(-12);

                while (!stoppingToken.IsCancellationRequested)
                {
                    var batch = await dbContext.ProductRelevanceScores
                        .Where(p => p.LastProcessedDate == null || p.LastProcessedDate < processingCutoff)
                        .OrderBy(p => p.Id)
                        .Take(BatchSize)
                        .ToListAsync(stoppingToken);

                    if (batch.Count == 0) break;

                    foreach (var score in batch)
                    {
                        score.Score *= DecreaseFactor;
                        score.LastModifiedDate = DateTime.UtcNow;
                        score.LastProcessedDate = DateTime.UtcNow;
                    }

                    await dbContext.SaveChangesAsync(stoppingToken);
                    
                    _logger.LogInformation("Batch finished.");
                    
                    // Slow down
                    await Task.Delay(300);
                }


                _logger.LogInformation("Completed relevance decay job.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while decaying product relevance scores.");
            }

            await Task.Delay(_interval, stoppingToken);
        }
    }
}