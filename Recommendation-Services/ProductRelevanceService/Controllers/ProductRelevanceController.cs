using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Recommendation_Services.ProductRelevanceService.Models;

namespace Recommendation_Services.ProductRelevanceService.Controllers;

[ApiController]
[Route("product_relevance")]
public class ProductRelevanceController(
    IProductRelevanceService productRelevanceService,
    ILogger<ProductRelevanceController> logger)
    : ControllerBase
{
    [HttpGet("segments")]
    [AllowAnonymous]
    public async Task<ActionResult<IEnumerable<CustomerSegment>>> GetSegments()
    {
        logger.LogInformation("Received GET request for /product_relevance/segments");

        var segments = await productRelevanceService.GetSegments();

        logger.LogInformation("Returning {Count} customer segments", segments?.Count() ?? 0);

        return Ok(segments);
    }
}