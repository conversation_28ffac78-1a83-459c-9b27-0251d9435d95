using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Recommendation_Services.Models;
using Recommendation_Services.Models.ModelsDal.Recommendation;
using Recommendation_Services.Models.Dto.Rules;

namespace Recommendation_Services.Services.Finalizers;

public static class MerchantAgeAndGenderRevenueFinalizer
{
    public static async Task Run(PreCalculationContext context, IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        var loggerFactory = serviceProvider.GetService(typeof(ILoggerFactory)) as ILoggerFactory;
        var logger = loggerFactory?.CreateLogger("MerchantAgeAndGenderRevenueFinalizer");
        var dbContext = serviceProvider.GetRequiredService<RecommendationDbContext>();
        
        logger?.LogInformation("Running MerchantAgeAndGenderRevenueFinalizer");
        
        if (!context.Data.TryGetValue("PartnerId", out var partnerIdObj) || partnerIdObj is not int partnerId)
        {
            logger?.LogWarning("PartnerId not found in context. Skipping finalizer.");
            return;
        }

        if (!context.Data.TryGetValue("MerchantScoresByDemographics", out var scoresObj) || 
            scoresObj is not Dictionary<(int? Age, string Gender), List<MerchantAgeAndGenderScoreDto>> merchantScoresByDemographics)
        {
            logger?.LogWarning("MerchantScoresByDemographics not found in context. Skipping finalizer.");
            return;
        }

        var strategyName = context.StrategyName;
        var now = DateTime.UtcNow;

        // Remove existing merchant revenue recommendations for this partner and strategy
        var oldRecs = await dbContext.OfferRecommendations
            .Where(r => r.Strategy == strategyName && r.PartnerId == partnerId)
            .Include(r => r.OfferRecommendationItems)
            .ToListAsync(cancellationToken);
        dbContext.OfferRecommendationItems.RemoveRange(oldRecs.SelectMany(r => r.OfferRecommendationItems));
        dbContext.OfferRecommendations.RemoveRange(oldRecs);
        
        // Create new recommendations for each demographic group
        foreach (var ((age, gender), merchantScores) in merchantScoresByDemographics)
        {
            var newRec = new OfferRecommendation
            {
                Strategy = strategyName,
                Active = true,
                CreatedDate = now,
                LastModifiedDate = now,
                Age = age,
                Gender = gender,
                PartnerId = partnerId,
                OfferRecommendationItems = new List<OfferRecommendationItem>()
            };

            foreach (var merchantScore in merchantScores)
            {
                newRec.OfferRecommendationItems.Add(new OfferRecommendationItem
                {
                    OfferType = "Merchant",
                    OfferId = long.Parse(merchantScore.MerchantId), // Using MerchantId as OfferId for merchant recommendations
                    MerchantId = long.Parse(merchantScore.MerchantId),
                    Score = (decimal)merchantScore.Sum
                });
            }
            
            dbContext.OfferRecommendations.Add(newRec);
        }
        
        await dbContext.SaveChangesAsync(cancellationToken);
        logger?.LogInformation($"[Finalizer] Saved {merchantScoresByDemographics.Values.Sum(scores => scores.Count)} merchant revenue recommendations for strategy: {strategyName} (by demographics)");
    }
} 