using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Recommendation_Services.Models.ModelsDal.Recommendation;
using Merchant_Services.Models.ModelsDal.Merchant;
using Recommendation_Services.Models.Dto.Rules;
using Webshop.Webshop;
using System.Text.Json;
using Recommendation_Services.Models;

namespace Recommendation_Services.Services.Finalizers;

public static class CuratedProductsFinalizer
{
    public static async Task Run(PreCalculationContext context, IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        var startTime = DateTime.Now;
        Console.WriteLine($"[{startTime:HH:mm:ss}] ========== Starting CuratedProductsFinalizer ==========");
        
        var loggerFactory = serviceProvider.GetService(typeof(ILoggerFactory)) as ILoggerFactory;
        var logger = loggerFactory?.CreateLogger("CuratedProductsFinalizer");
        var dbContext = serviceProvider.GetRequiredService<RecommendationDbContext>();
        var merchantService = serviceProvider.GetRequiredService<IMerchantService>();

        if (!context.Data.TryGetValue("FilteredProducts", out var obj) || obj is not Dictionary<(int? Age, string Gender), Dictionary<string, List<RankedProductDto>>> rankedProducts)
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ⚠ No products found in context. Skipping finalizer.");
            logger?.LogWarning("No products found in context. Skipping finalizer.");
            return;
        }

        var strategyName = context.StrategyName;
        var now = DateTime.UtcNow;
        var partnerId = context.Data.TryGetValue("PartnerId", out var pidObj) && pidObj is int pid ? pid : 0;

        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Strategy: {strategyName}, Partner ID: {partnerId}");
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Found {rankedProducts.Count} demographic groups to process");

        // Remove old recommendations for this strategy
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Removing old recommendations for strategy: {strategyName}...");
        var oldRecs = await dbContext.OfferRecommendations
            .Where(r => r.Strategy == strategyName && r.PartnerId == partnerId)
            .Include(r => r.OfferRecommendationItems)
            .ToListAsync(cancellationToken);
        
        var oldItemsCount = oldRecs.SelectMany(r => r.OfferRecommendationItems).Count();
        dbContext.OfferRecommendationItems.RemoveRange(oldRecs.SelectMany(r => r.OfferRecommendationItems));
        dbContext.OfferRecommendations.RemoveRange(oldRecs);
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ✓ Removed {oldRecs.Count} old recommendations with {oldItemsCount} items");

        // Caches to avoid redundant product checks/validations per run
        var productCache = new Dictionary<long, Product>();
        var imageUrlCache = new Dictionary<long, string>();
        
        var totalProductsProcessed = 0;
        var totalProductsWithImages = 0;
        var totalProductsSkipped = 0;
        var demographicGroupIndex = 0;

        // Insert new recommendations for each demographic group
        foreach (var ((age, gender), topProductsByMerchant) in rankedProducts)
        {
            demographicGroupIndex++;
            var ageDisplay = age?.ToString() ?? "All";
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Processing demographic group {demographicGroupIndex}/{rankedProducts.Count}: Age={ageDisplay}, Gender={gender}");
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Found {rankedProducts.Count} merchants in this demographic group");
            
            var newRec = new OfferRecommendation
            {
                Strategy = strategyName,
                Active = true,
                CreatedDate = now,
                LastModifiedDate = now,
                Age = age,
                Gender = gender,
                PartnerId = partnerId,
                OfferRecommendationItems = []
            };
            
            var merchantIndex = 0;
            foreach (var (merchantIdStr, products) in topProductsByMerchant)
            {
                merchantIndex++;
                if (!long.TryParse(merchantIdStr, out var merchantId)) 
                {
                    Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ⚠ Invalid merchant ID: {merchantIdStr}");
                    continue;
                }
                
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Processing merchant {merchantIndex}/{topProductsByMerchant.Count}: {merchantId} ({products.Count} products)");
                
                var productIndex = 0;
                foreach (var product in products)
                {
                    productIndex++;
                    totalProductsProcessed++;
                    
                    if (productIndex % 10 == 0 || productIndex == products.Count)
                    {
                        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Progress: Product {productIndex}/{products.Count} for merchant {merchantId}");
                    }
                    
                    // Fetch the product entity from the DB (with cache)
                    if (!productCache.TryGetValue(product.ProductDbId, out var productEntity))
                    {
                        productEntity = await merchantService.GetProductByInternalProductIdWithoutMerchantReference(product.ProductInternalId);
                        productCache[product.ProductDbId] = productEntity;
                        
                        if (productEntity == null)
                        {
                            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ⚠ Product not found: {product.ProductInternalId}");
                        }
                    }

                    if (productEntity != null)
                    {
                        // Validate/generate the image (with cache)
                        if (!imageUrlCache.TryGetValue(product.ProductDbId, out var imageUrl))
                        {
                            imageUrl = await merchantService.CuratedProductsImageValidationAndCreation(productEntity);
                            if (string.IsNullOrEmpty(imageUrl))
                            {
                                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ⚠ No curated image generated for {product.ProductInternalId}, using fallback image");
                                // If no image is found, use the first image from the product
                                imageUrl = GetFirstImageUrl(productEntity.ProductImages);

                                // At some point, we should make sure the Curated Image is valid and if not, continue and not just use the first image
                                //continue;
                            }
                            else
                            {
                                totalProductsWithImages++;
                            }
                            imageUrlCache[product.ProductDbId] = imageUrl;
                        }
                    }
                    else
                    {
                        totalProductsSkipped++;
                    }
                    
                    newRec.OfferRecommendationItems.Add(new OfferRecommendationItem
                    {
                        OfferType = "Product",
                        OfferId = product.ProductDbId,
                        MerchantId = merchantId,
                        Score = product.ProductScore
                    });
                }
                
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ✓ Completed merchant {merchantId}: {products.Count} products processed");
            }
            
            dbContext.OfferRecommendations.Add(newRec);
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ✓ Completed demographic group {demographicGroupIndex}: Age={ageDisplay}, Gender={gender} ({newRec.OfferRecommendationItems.Count} items)");
        }
        
        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Saving recommendations to database...");
        await dbContext.SaveChangesAsync(cancellationToken);
        
        var endTime = DateTime.Now;
        var duration = endTime - startTime;
        
        Console.WriteLine($"[{endTime:HH:mm:ss}] ========== CuratedProductsFinalizer Completed ==========");
        Console.WriteLine($"[{endTime:HH:mm:ss}] Duration: {duration.TotalMinutes:F1} minutes");
        Console.WriteLine($"[{endTime:HH:mm:ss}] Total products processed: {totalProductsProcessed:N0}");
        Console.WriteLine($"[{endTime:HH:mm:ss}] Products with curated images: {totalProductsWithImages:N0}");
        Console.WriteLine($"[{endTime:HH:mm:ss}] Products skipped: {totalProductsSkipped:N0}");
        Console.WriteLine($"[{endTime:HH:mm:ss}] Cache hits - Products: {productCache.Count:N0}, Images: {imageUrlCache.Count:N0}");
        
        logger?.LogInformation($"[Finalizer] Saved curated product recommendations for strategy: {strategyName} (by demographics)");
    }
    
    private static string GetFirstImageUrl(string productImages)
    {
        if (string.IsNullOrEmpty(productImages))
        {
            return string.Empty;
        }

        try
        {
            // First try to deserialize as List<string>
            var stringImages = JsonSerializer.Deserialize<List<string>>(productImages);
            return stringImages?.FirstOrDefault() ?? string.Empty;
        }
        catch (JsonException)
        {
            try
            {
                // If that fails, try to deserialize as array of objects and extract URL
                using var document = JsonDocument.Parse(productImages);
                var root = document.RootElement;
                
                if (root.ValueKind == JsonValueKind.Array && root.GetArrayLength() > 0)
                {
                    var firstElement = root[0];
                    
                    // If it's a string, return it directly
                    if (firstElement.ValueKind == JsonValueKind.String)
                    {
                        return firstElement.GetString() ?? string.Empty;
                    }
                    
                    // If it's an object, try to find URL property
                    if (firstElement.ValueKind == JsonValueKind.Object)
                    {
                        // Try common property names for image URLs
                        var urlProperties = new[] { "Src", "src", "url", "Url", "imageUrl", "ImageUrl", "href" };
                        
                        foreach (var propName in urlProperties)
                        {
                            if (firstElement.TryGetProperty(propName, out var urlProperty) && 
                                urlProperty.ValueKind == JsonValueKind.String)
                            {
                                return urlProperty.GetString() ?? string.Empty;
                            }
                        }
                    }
                }
            }
            catch (JsonException)
            {
                // If all parsing attempts fail, return empty string
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] ⚠ Failed to parse ProductImages JSON: {productImages?.Substring(0, Math.Min(100, productImages.Length))}...");
            }
        }
        
        return string.Empty;
    }
} 