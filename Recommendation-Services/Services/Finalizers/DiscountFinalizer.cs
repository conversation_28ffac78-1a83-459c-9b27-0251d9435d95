using Microsoft.Extensions.Logging;
using Recommendation_Services.Models;

namespace Recommendation_Services.Services.Finalizers;

public static class DiscountFinalizer
{
    public static async Task Run(PreCalculationContext context, IServiceProvider serviceProvider, CancellationToken cancellationToken)
    {
        var loggerFactory = serviceProvider.GetService(typeof(ILoggerFactory)) as ILoggerFactory;
        var logger = loggerFactory?.CreateLogger("DiscountFinalizer");
        logger?.LogInformation($"[Finalizer] Saving Discount recommendations for strategy: {context.StrategyName}");
        await Task.CompletedTask;
    }
} 