using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using Partner_Services.Services.General;
using Recommendation_Services.Models;
using Recommendation_Services.Services.Rules;

namespace Recommendation_Services.Services;

public class OfferPreCalculationBackgroundService(
    IServiceProvider serviceProvider,
    ILogger logger)
    : BackgroundService
{
    private readonly TimeSpan _interval = TimeSpan.FromDays(1); // Configurable

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            using (var scope = serviceProvider.CreateScope())
            {
                var registry = scope.ServiceProvider.GetRequiredService<IOfferPreCalculationRuleRegistry>();
                var partnerService = scope.ServiceProvider.GetRequiredService<IPartnerService>();
                var partners = await partnerService.GetAllAsync();
                
                // TODO: Remove the hardcoded partner ID
                foreach (var partner in partners.Where(p => p.Id == 87317))
                {
                    foreach (var strategy in registry.GetAllStrategies())
                    {
                        await ExecuteStrategy(strategy, partner.Id, registry, scope.ServiceProvider, stoppingToken);
                    }
                }                
            }
            await Task.Delay(_interval, stoppingToken);
        }
    }

    private async Task ExecuteStrategy(
        string strategy, 
        int partnerId, 
        IOfferPreCalculationRuleRegistry registry, 
        IServiceProvider serviceProvider, 
        CancellationToken stoppingToken)
    {
        var strategyStartTime = DateTime.UtcNow;
        logger.Information($"Executing pre-calculation for strategy: {strategy}, Partner: {partnerId}");
        Console.WriteLine($"Executing pre-calculation for strategy: {strategy}, Partner: {partnerId}");

        // Use enhanced context for performance optimizations
        var context = new PreCalculationContext 
        { 
            StrategyName = strategy,
            ExecutionStartTime = strategyStartTime,
            EnableCaching = true,
            EnableParallelProcessing = true,
            BatchSize = 10
        };
        context.Data.Add("PartnerId", partnerId);
        
        var rulesForStrategy = registry.GetRulesByStrategy(strategy).ToList();
        logger.Information($"Strategy '{strategy}' has {rulesForStrategy.Count} rules");
        Console.WriteLine($"Strategy '{strategy}' has {rulesForStrategy.Count} rules");

        // Execute rules in priority order
        foreach (var (rule, priority) in rulesForStrategy)
        {
            var ruleStartTime = DateTime.UtcNow;
            var ruleName = rule.GetType().Name;
            
            logger.Information($"Executing rule: {ruleName} (Priority {priority})");
            
            try
            {
                await rule.PreCalculateAsync(stoppingToken, context);
                
                var ruleDuration = DateTime.UtcNow - ruleStartTime;
                context.TrackRuleExecution(ruleName, ruleDuration);
                
                logger.Information($"Successfully completed rule '{ruleName}' in {ruleDuration.TotalSeconds:F2}s");
                Console.WriteLine($"Successfully completed rule '{ruleName}' in {ruleDuration.TotalSeconds:F2}s");
            }
            catch (Exception ex)
            {
                logger.Error(ex, $"Error executing rule '{ruleName}' for strategy '{strategy}'");
                Console.WriteLine($"Error executing rule '{ruleName}' for strategy '{strategy}'");
                // Continue with other rules even if one fails
            }
        }
        
        // Execute finalizer if present
        var finalizer = registry.GetFinalizerByStrategy(strategy);
        if (finalizer != null)
        {
            var finalizerStartTime = DateTime.UtcNow;
            try
            {
                await finalizer(context, serviceProvider, stoppingToken);
                
                var finalizerDuration = DateTime.UtcNow - finalizerStartTime;
                context.TrackRuleExecution("Finalizer", finalizerDuration);
                
                logger.Information($"Successfully completed finalizer in {finalizerDuration.TotalSeconds:F2}s");
                Console.WriteLine($"Successfully completed finalizer in {finalizerDuration.TotalSeconds:F2}s");
            }
            catch (Exception ex)
            {
                logger.Error(ex, $"Error executing finalizer for strategy '{strategy}'");
                Console.WriteLine($"Error executing finalizer for strategy '{strategy}'");
            }
        }
        
        // Log performance metrics
        context.LogPerformanceMetrics(logger);
        
        var totalDuration = DateTime.UtcNow - strategyStartTime;
        logger.Information($"Completed strategy '{strategy}' for partner {partnerId} in {totalDuration.TotalSeconds:F2}s");
        Console.WriteLine($"Completed strategy '{strategy}' for partner {partnerId} in {totalDuration.TotalSeconds:F2}s");

        // Log cache efficiency
        LogCacheEfficiency(context);
    }
    
    private void LogCacheEfficiency(PreCalculationContext context)
    {
        var cacheHits = context.SharedCache.Count;
        if (cacheHits > 0)
        {
            logger.Information($"Cache efficiency: {cacheHits} cached items used across rules");
            Console.WriteLine($"Cache efficiency: {cacheHits} cached items used across rules");
        }
    }
} 