using System.Linq.Expressions;
using Recommendation_Services.Models;
using Recommendation_Services.Repositories;
using Recommendation_Services.Models.ModelsDal.Recommendation;
using Shared.Constants;
using Recommendation_Services.Services.Rules;
using Shared.Services.Partner;
using Microsoft.Extensions.DependencyInjection;
using Partner_Services.Services.General;
using Serilog;

namespace Recommendation_Services.Services;

public class OfferRecommendationService(IOfferRecommendationRepository repository, IServiceProvider serviceProvider, ILogger logger) : IOfferRecommendationService
{
    public async Task<List<OfferRecommendationDto>> GetRecommendationsAsync(string strategy, string gender, int? age)
    {
        var entities = await repository.GetAsync(r => r.Active && r.Strategy == strategy && r.Gender == gender && r.Age == age);
        return entities.Select(MapToDto).ToList();
    }

    public async Task<OfferRecommendationDto?> GetByIdAsync(int id)
    {
        var entity = await repository.GetByIdAsync(id);
        return entity == null ? null : MapToDto(entity);
    }

    public async Task<List<OfferRecommendationDto>> GetRecommendationsByStrategyAsync(string strategy, string? gender, int? age)
    {
        var entities = await repository.GetAsync(r => r.Active && r.Strategy == strategy 
            && (gender == null || r.Gender == gender) 
            && (age == null || r.Age == age));
        return entities.Select(MapToDto).ToList();
    }

    public async Task<List<OfferRecommendationDto>> GetRecommendationEntitiesAsync(string strategy, int numberOfRecommendations, string? gender, int? age)
    {
        var entities = await repository.GetAsync(
            r => r.Active 
                && r.Strategy == strategy 
                && (gender == null || r.Gender == gender) 
                && (age == null || r.Age == age),
            numberOfRecommendations);

        // If the Strategy is Curated Products or Curated Products With Price Drop, we need to shuffle the items
        if (strategy == OfferRecommendationTypes.CuratedProducts || strategy == OfferRecommendationTypes.CuratedProductsPriceDrop) 
        {
            foreach (var entity in entities)
            {
                entity.OfferRecommendationItems = entity.OfferRecommendationItems
                    .GroupBy(item => item.MerchantId)
                    .SelectMany(group => group.OrderBy(_ => Guid.NewGuid()).Take(numberOfRecommendations))
                    .ToList();
            }
        }

        return entities.Select(MapToDto).ToList();
    }

    public async Task<List<OfferRecommendationDto>> GetPagedRecommendationsAsync(string strategy, int page, int pageSize, string? sortBy = null)
    {
        var query = repository.GetQueryable()
            .Where(r => r.Active && r.Strategy == strategy);

        query = sortBy?.ToLower() switch
        {
            "createdate" => query.OrderBy(r => r.CreatedDate),
            "createdatedesc" => query.OrderByDescending(r => r.CreatedDate),
            "age" => query.OrderBy(r => r.Age),
            _ => query.OrderByDescending(r => r.CreatedDate)
        };

        query = query.Skip((page - 1) * pageSize).Take(pageSize);
        var entities = await repository.GetAsync(query);
        return entities.Select(MapToDto).ToList();
    }

    public async Task<bool> HasActiveRecommendationsAsync(string strategy, string gender)
    {
        return await repository.ExistsAsync(r => r.Active && r.Strategy == strategy && r.Gender == gender);
    }

    public async Task<int> GetRecommendationCountAsync(string strategy)
    {
        return await repository.CountAsync(r => r.Active && r.Strategy == strategy);
    }

    private static OfferRecommendationDto MapToDto(OfferRecommendation entity)
    {
        return new OfferRecommendationDto
        {
            Id = entity.Id,
            CreatedDate = entity.CreatedDate,
            Active = entity.Active,
            Gender = entity.Gender,
            Age = entity.Age,
            Strategy = entity.Strategy,
            OfferItems = entity.OfferRecommendationItems?.Select(i => new OfferRecommendationItemDto
            {
                OfferType = i.OfferType,
                OfferId = i.OfferId,
                MerchantId = i.MerchantId ?? 0,
                Score = i.Score.HasValue ? (double)i.Score.Value : 0.0
            }).ToList() ?? []
        };
    }

    public async Task RunRecommendationsAsync(CancellationToken stoppingToken)
    {
        using (var scope = serviceProvider.CreateScope())
            {
                var registry = scope.ServiceProvider.GetRequiredService<IOfferPreCalculationRuleRegistry>();
                var partnerService = scope.ServiceProvider.GetRequiredService<IPartnerService>();
                var partners = await partnerService.GetAllAsync();
                
                // TODO: Remove the hardcoded partner ID
                foreach (var partner in partners.Where(p => p.Id == 87317))
                {
                    foreach (var strategy in registry.GetAllStrategies())
                    {
                        await ExecuteStrategy(strategy, partner.Id, registry, scope.ServiceProvider, stoppingToken);
                    }
                }                
            }
    }

    private async Task ExecuteStrategy(
        string strategy, 
        int partnerId, 
        IOfferPreCalculationRuleRegistry registry, 
        IServiceProvider serviceProvider, 
        CancellationToken stoppingToken)
    {
        var strategyStartTime = DateTime.UtcNow;
        logger.Information($"Executing pre-calculation for strategy: {strategy}, Partner: {partnerId}");
        Console.WriteLine($"Executing pre-calculation for strategy: {strategy}, Partner: {partnerId}");

        // Use enhanced context for performance optimizations
        var context = new PreCalculationContext 
        { 
            StrategyName = strategy,
            ExecutionStartTime = strategyStartTime,
            EnableCaching = true,
            EnableParallelProcessing = true,
            BatchSize = 10
        };
        context.Data.Add("PartnerId", partnerId);
        
        var rulesForStrategy = registry.GetRulesByStrategy(strategy).ToList();
        logger.Information($"Strategy '{strategy}' has {rulesForStrategy.Count} rules");
        Console.WriteLine($"Strategy '{strategy}' has {rulesForStrategy.Count} rules");

        // Execute rules in priority order
        foreach (var (rule, priority) in rulesForStrategy)
        {
            var ruleStartTime = DateTime.UtcNow;
            var ruleName = rule.GetType().Name;
            
            logger.Information($"Executing rule: {ruleName} (Priority {priority})");
            
            try
            {
                await rule.PreCalculateAsync(stoppingToken, context);
                
                var ruleDuration = DateTime.UtcNow - ruleStartTime;
                context.TrackRuleExecution(ruleName, ruleDuration);
                
                logger.Information($"Successfully completed rule '{ruleName}' in {ruleDuration.TotalSeconds:F2}s");
                Console.WriteLine($"Successfully completed rule '{ruleName}' in {ruleDuration.TotalSeconds:F2}s");
            }
            catch (Exception ex)
            {
                logger.Error(ex, $"Error executing rule '{ruleName}' for strategy '{strategy}'");
                Console.WriteLine($"Error executing rule '{ruleName}' for strategy '{strategy}'");
                // Continue with other rules even if one fails
            }
        }
        
        // Execute finalizer if present
        var finalizer = registry.GetFinalizerByStrategy(strategy);
        if (finalizer != null)
        {
            var finalizerStartTime = DateTime.UtcNow;
            try
            {
                await finalizer(context, serviceProvider, stoppingToken);
                
                var finalizerDuration = DateTime.UtcNow - finalizerStartTime;
                context.TrackRuleExecution("Finalizer", finalizerDuration);
                
                logger.Information($"Successfully completed finalizer in {finalizerDuration.TotalSeconds:F2}s");
                Console.WriteLine($"Successfully completed finalizer in {finalizerDuration.TotalSeconds:F2}s");
            }
            catch (Exception ex)
            {
                logger.Error(ex, $"Error executing finalizer for strategy '{strategy}'");
                Console.WriteLine($"Error executing finalizer for strategy '{strategy}'");
            }
        }
        
        // Log performance metrics
        context.LogPerformanceMetrics(logger);
        
        var totalDuration = DateTime.UtcNow - strategyStartTime;
        logger.Information($"Completed strategy '{strategy}' for partner {partnerId} in {totalDuration.TotalSeconds:F2}s");
        Console.WriteLine($"Completed strategy '{strategy}' for partner {partnerId} in {totalDuration.TotalSeconds:F2}s");

        // Log cache efficiency
        LogCacheEfficiency(context);
    }
    
    private void LogCacheEfficiency(PreCalculationContext context)
    {
        var cacheHits = context.SharedCache.Count;
        if (cacheHits > 0)
        {
            logger.Information($"Cache efficiency: {cacheHits} cached items used across rules");
            Console.WriteLine($"Cache efficiency: {cacheHits} cached items used across rules");
        }
    }
} 