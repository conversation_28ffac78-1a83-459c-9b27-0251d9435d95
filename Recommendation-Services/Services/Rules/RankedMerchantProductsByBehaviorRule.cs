using Serilog;
using Recommendation_Services.Models.Dto.Rules;
using Shared.Dto.Curated.Product;
using Shared.Elastic.Elastic;
using Webshop.Webshop;
using Recommendation_Services.Models;

namespace Recommendation_Services.Services.Rules;

public class RankedMerchantProductsByBehaviorRule(
    ILogger logger,
    IElasticService elasticService,
    IMerchantService merchantService)
    : IOfferPreCalculationRule
{
    public async Task PreCalculateAsync(CancellationToken cancellationToken, PreCalculationContext context)
    {
        var startTime = DateTime.UtcNow;
        logger.Information("Running RankedMerchantProductsByBehaviorRule");
        Console.WriteLine("Running RankedMerchantProductsByBehaviorRule");
        
        // Try to get merchant scores from optimized format first, fallback to legacy
        Dictionary<(int? Age, string Gender), List<MerchantAgeAndGenderScoreDto>> merchantScoresByDemographics;
        
        if (context.Data.TryGetValue("MerchantScoresByDemographics", out var optimizedScores) && 
            optimizedScores is Dictionary<(int? Age, string Gender), List<MerchantAgeAndGenderScoreDto>> scores)
        {
            merchantScoresByDemographics = scores;
        }
        else if (context.Data.TryGetValue("MerchantScores", out var legacyScores) && 
                 legacyScores is IEnumerable<dynamic> merchantScores)
        {
            // Convert legacy format
            merchantScoresByDemographics = merchantScores
                .GroupBy(ms => new { Age = (int?)ms.Age, Gender = (string)ms.Gender })
                .ToDictionary(g => (g.Key.Age, g.Key.Gender), 
                             g => g.Select(ms => new MerchantAgeAndGenderScoreDto 
                             { 
                                 MerchantId = ms.MerchantId.ToString(), 
                                 Age = (byte?)ms.Age, 
                                 Gender = ms.Gender,
                                 Sum = ms.Sum 
                             }).ToList());
        }
        else
        {
            logger.Warning("No merchant scores found in context. Skipping product ranking.");
            Console.WriteLine("No merchant scores found in context. Skipping product ranking.");
            return;
        }

        // Get all unique merchant IDs for batch processing
        var allMerchantIds = merchantScoresByDemographics.Values
            .SelectMany(scores => scores.Select(s => int.Parse(s.MerchantId)))
            .Distinct()
            .ToList();

        // Check if we have products for each merchant in the Database and if not, remove the merchant from the list before caching
        var merchantIdsWithProducts = await merchantService.GetProductCountByMerchantIdsAsync(allMerchantIds);
        allMerchantIds = allMerchantIds.Where(id => merchantIdsWithProducts.ContainsKey(id)).ToList();

        // Cache merchant products across all demographics
        var merchantProductsCache = await GetMerchantProductsCache(allMerchantIds, context);
        
        // Build results by demographics
        var allDemographicResults = new Dictionary<(int? Age, string Gender), List<MerchantWithRankedProductsDto>>();

        foreach (var ((age, gender), merchantScoresForGroup) in merchantScoresByDemographics)
        {
            var rankedMerchantsWithProducts = new List<MerchantWithRankedProductsDto>();

            foreach (var merchantScore in merchantScoresForGroup)
            {
                var merchantId = int.Parse(merchantScore.MerchantId);
                if (merchantProductsCache.TryGetValue(merchantId, out var topProducts))
                {
                    var rankedProducts = topProducts
                        .OrderByDescending(p => p.Count)
                        .Select(p => new RankedProductDto 
                        { 
                            ProductInternalId = p.Id, 
                            ProductScore = (int)p.Count 
                        })
                        .ToList();

                    rankedMerchantsWithProducts.Add(new MerchantWithRankedProductsDto
                    {
                        MerchantId = merchantId,
                        RankedProducts = rankedProducts
                    });
                }
            }
            
            allDemographicResults[(age, gender)] = rankedMerchantsWithProducts;
        }
        
        context.Data["RankedMerchantsWithProductsByDemographics"] = allDemographicResults;
        
        var duration = DateTime.UtcNow - startTime;
        context.TrackRuleExecution(nameof(RankedMerchantProductsByBehaviorRule), duration);
            
        logger.Information($"Completed RankedMerchantProductsByBehaviorRule in {duration.TotalSeconds:F2}s");
        Console.WriteLine($"Completed RankedMerchantProductsByBehaviorRule in {duration.TotalSeconds:F2}s");
    }

    private async Task<Dictionary<int, List<CuratedProductEvents>>> GetMerchantProductsCache(
        List<int> merchantIds, 
        PreCalculationContext context)
    {
        var cacheKey = $"MerchantProducts_{string.Join(",", merchantIds.OrderBy(x => x))}_{merchantIds.Count}";
        
        return await context.GetOrSetCacheAsync(cacheKey, async () =>
        {
            logger.Information($"Fetching products for {merchantIds.Count} merchants (not cached)");
            Console.WriteLine($"Fetching products for {merchantIds.Count} merchants (not cached)");
            
            var merchantProductsCache = new Dictionary<int, List<CuratedProductEvents>>();
            
            if (context.EnableParallelProcessing)
            {
                // Process in batches to avoid overwhelming the elastic service
                const int batchSize = 50;
                for (int i = 0; i < merchantIds.Count; i += batchSize)
                {
                    var batch = merchantIds.Skip(i).Take(batchSize).ToList();
                    var tasks = batch.Select(async merchantId =>
                    {
                        var topProducts = await GetTopProductsWithFallback(merchantId);
                        return new { MerchantId = merchantId, Products = topProducts };
                    });
                    
                    var results = await Task.WhenAll(tasks);
                    foreach (var result in results)
                    {
                        merchantProductsCache[result.MerchantId] = result.Products;
                    }
                }
            }
            else
            {
                // Sequential processing
                foreach (var merchantId in merchantIds)
                {
                    var topProducts = await GetTopProductsWithFallback(merchantId);
                    merchantProductsCache[merchantId] = topProducts;
                }
            }
            
            return merchantProductsCache;
        });
    }

    private async Task<List<CuratedProductEvents>> GetTopProductsWithFallback(int merchantId)
    {
        try
        {
            // First attempt: Get products by order data (preferred method)
            var topProducts = await elasticService.OrderTopProducts(merchantId, 20, 10000);
            
            // If we got results, return them with full weight (orders are worth 10x page views)
            if (topProducts?.Any() == true)
            {
                logger.Information($"Found {topProducts.Count} products for merchant {merchantId} using OrderTopProducts (full weight)");
                return topProducts; // Orders keep their original count (full weight)
            }
            
            // Fallback: Get products by page events if no order data available
            logger.Information($"No order data found for merchant {merchantId}, falling back to PageEventsTopProducts");
            var fallbackProducts = await elasticService.PageEventsTopProducts(merchantId, 20);
            
            if (fallbackProducts?.Any() == true)
            {
                // Apply 0.1 weight to page view data (page views are worth 1/10th of orders)
                foreach (var product in fallbackProducts)
                {
                    product.Count = (long)(product.Count * 0.1);
                }
                
                logger.Information($"Found {fallbackProducts.Count} products for merchant {merchantId} using PageEventsTopProducts fallback (0.1x weight applied)");
                return fallbackProducts;
            }
            
            logger.Warning($"No products found for merchant {merchantId} using either method");
            return new List<CuratedProductEvents>();
        }
        catch (Exception ex)
        {
            logger.Error(ex, $"Error fetching products for merchant {merchantId}, trying fallback method");
            
            try
            {
                // Emergency fallback in case of exceptions
                var fallbackProducts = await elasticService.PageEventsTopProducts(merchantId, 20);
                
                // Apply 0.1 weight to emergency fallback data as well
                if (fallbackProducts?.Any() == true)
                {
                    foreach (var product in fallbackProducts)
                    {
                        product.Count = (long)(product.Count * 0.1);
                    }
                }
                
                logger.Information($"Successfully retrieved {fallbackProducts?.Count ?? 0} products for merchant {merchantId} using emergency fallback (0.1x weight applied)");
                return fallbackProducts ?? new List<CuratedProductEvents>();
            }
            catch (Exception fallbackEx)
            {
                logger.Error(fallbackEx, $"Both primary and fallback methods failed for merchant {merchantId}");
                return new List<CuratedProductEvents>();
            }
        }
    }
}