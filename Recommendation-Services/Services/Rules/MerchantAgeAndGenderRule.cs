using Microsoft.Extensions.Logging;
using Recommendation_Services.Models.Dto.Rules;
using Recommendation_Services.Repositories;
using Shared.Dto.MerchantScore;
using Shared.Elastic.Elastic;
using Shared.Models.Merchant;
using Webshop.Webshop;
using Recommendation_Services.Models;

namespace Recommendation_Services.Services.Rules;

public class MerchantAgeAndGenderRule(
    IOfferRecommendationRepository repository,
    ILogger<MerchantAgeAndGenderRule> logger,
    IElasticService elasticService,
    IMerchantService merchantService)
    : IOfferPreCalculationRule
{
    private const byte AgeInterval = 10;
    private const byte StartAge = 18;
    private const byte EndAge = 69;
    private static readonly string[] Genders = ["Unknown", "Male", "Female"];

    public async Task PreCalculateAsync(CancellationToken cancellationToken, PreCalculationContext context)
    {
        var startTime = DateTime.UtcNow;
        logger.LogInformation("Running MerchantAgeAndGenderRule");
        
        var partnerId = context.Data.TryGetValue("PartnerId", out var pidObj) && pidObj is int pid ? pid : 0;
        
        // Cache key for merchant scores
        var cacheKey = $"Recommendation_Engine_MerchantScores_Partner_{partnerId}";
        
        var merchantScoresByDemographics = await context.GetOrSetCacheAsync(cacheKey, async () =>
        {
            logger.LogInformation("Computing merchant scores (not cached)");
            return await ComputeMerchantScoresByDemographics(partnerId, cancellationToken);
        });
        
        // Store in both formats for backward compatibility and new optimized access
        context.Data["MerchantScores"] = merchantScoresByDemographics.Values.SelectMany(x => x).ToList();
        context.Data["MerchantScoresByDemographics"] = merchantScoresByDemographics;
        
        var duration = DateTime.UtcNow - startTime;
        context.TrackRuleExecution(nameof(MerchantAgeAndGenderRule), duration);
            
        logger.LogInformation($"Completed MerchantAgeAndGenderRule in {duration.TotalSeconds:F2}s");
    }

    private async Task<Dictionary<(int? Age, string Gender), List<MerchantAgeAndGenderScoreDto>>> ComputeMerchantScoresByDemographics(
        int partnerId, 
        CancellationToken cancellationToken)
    {
        var merchantAgeAndGenderScores = new List<MerchantAgeAndGenderScoreDto>();
        var allMerchantIds = await merchantService.GetAllMerchantIdsWithAllowedMarketingByPartnerIdAsync(partnerId);

        // Fetch merchant metadata for CustomerSegment filtering
        var merchantsWithMetadata = await merchantService.GetMerchantsWithMetadataAsync(allMerchantIds);

        logger.LogInformation($"Computing scores for {allMerchantIds.Count} merchants across {Genders.Length} genders with CustomerSegment filtering");

        foreach (var gender in Genders)
        {
            logger.LogInformation($"Processing gender: {gender}");
            
            if (gender != "Unknown")
            {
                // For Male/Female, calculate age decay scores for different age targets
                var ageTasks = new List<Task<(int age, List<MerchantScore> actual, List<MerchantScore> potential)>>();
                
                // Create age targets across the range 
                for (int age = StartAge; age <= EndAge; age++)
                {
                    var currentAge = age;
                    ageTasks.Add(Task.Run(async () =>
                    {
                        logger.LogInformation($"Computing scores for {gender}, age {currentAge}");
                        
                        // Filter merchants based on CustomerSegment restrictions for this age/gender
                        var allowedMerchantIds = FilterMerchantsByCustomerSegment(merchantsWithMetadata, gender, currentAge);
                        
                        if (!allowedMerchantIds.Any())
                        {
                            logger.LogInformation($"No merchants allowed for {gender}, age {currentAge} based on CustomerSegment restrictions");
                            return (currentAge, new List<MerchantScore>(), new List<MerchantScore>());
                        }
                        
                        var actualTask = elasticService.GetLinesExtendedSimplified("invoices-lines", allowedMerchantIds, gender, currentAge, AgeInterval);
                        var potentialTask = elasticService.GetLinesExtendedSimplified("invoices-potentiallines", allowedMerchantIds, gender, currentAge, AgeInterval);
                        
                        var actual = await actualTask;
                        var potential = await potentialTask;
                        
                        logger.LogInformation($"Completed scores for {gender}, age {currentAge}: {actual.Count(s => s.Sum > 0)} actual, {potential.Count(s => s.Sum > 0)} potential merchants with scores (filtered from {allMerchantIds.Count} to {allowedMerchantIds.Count} allowed merchants)");
                        
                        return (currentAge, actual, potential);
                    }));
                }
                
                var ageResults = await Task.WhenAll(ageTasks);
                
                foreach (var (age, actual, potential) in ageResults)
                {
                    // Combine actual and potential scores
                    var actualDict = actual.ToDictionary(x => x.MerchantId);
                    
                    foreach (var potentialScore in potential)
                    {
                        if (actualDict.TryGetValue(potentialScore.MerchantId, out var existingScore))
                        {
                            existingScore.Sum += potentialScore.Sum;
                        }
                        else
                        {
                            actual.Add(potentialScore);
                        }
                    }
                    
                    // Convert to DTOs with age information
                    var scoresForAge = actual.Select(x => new MerchantAgeAndGenderScoreDto(x, gender, (byte?)age)).ToList();
                    merchantAgeAndGenderScores.AddRange(scoresForAge);
                    
                    logger.LogInformation($"Added {scoresForAge.Where(s => s.Sum > 0).Count()} scores for {gender}, age {age}");
                }
            }
            else
            {
                // For Unknown gender, use regular method without age decay
                logger.LogInformation("Computing scores for Unknown gender (no age decay)");
                
                // Filter merchants for Unknown gender - they should allow marketing to Unknown customers
                var allowedMerchantIds = FilterMerchantsByCustomerSegment(merchantsWithMetadata, gender, null);
                
                if (!allowedMerchantIds.Any())
                {
                    logger.LogInformation("No merchants allowed for Unknown gender based on CustomerSegment restrictions");
                    continue;
                }
                
                var actualTask = elasticService.GetLinesExtended("invoices-lines", allowedMerchantIds, gender);
                var potentialTask = elasticService.GetLinesExtended("invoices-potentiallines", allowedMerchantIds, gender);
                
                var actual = await actualTask;
                var potential = await potentialTask;
                
                // Combine actual and potential scores
                var actualDict = actual.ToDictionary(x => x.MerchantId);
                
                foreach (var potentialScore in potential)
                {
                    if (actualDict.TryGetValue(potentialScore.MerchantId, out var existingScore))
                    {
                        existingScore.Sum += potentialScore.Sum;
                    }
                    else
                    {
                        actual.Add(potentialScore);
                    }
                }
                
                var scoresForUnknown = actual.Select(x => new MerchantAgeAndGenderScoreDto(x, gender, null)).ToList();
                merchantAgeAndGenderScores.AddRange(scoresForUnknown);
                
                logger.LogInformation($"Added {scoresForUnknown.Where(s => s.Sum > 0).Count()} scores for Unknown gender (filtered from {allMerchantIds.Count} to {allowedMerchantIds.Count} allowed merchants)");
            }
        }

        logger.LogInformation($"Total merchant scores computed: {merchantAgeAndGenderScores.Count}");
        logger.LogInformation($"Scores with non-zero values: {merchantAgeAndGenderScores.Where(s => s.Sum > 0).Count()}");

        return merchantAgeAndGenderScores
            .GroupBy(ms => new { ms.Age, ms.Gender })
            .ToDictionary(g => ((int?)g.Key.Age, g.Key.Gender), g => g.ToList());
    }

    private List<int> FilterMerchantsByCustomerSegment(
        Dictionary<int, List<Merchant_Services.Models.ModelsDal.Merchant.MerchantMetum>> merchantsWithMetadata,
        string gender,
        int? age)
    {
        var allowedMerchants = new List<int>();
        
        foreach (var (merchantId, merchantMeta) in merchantsWithMetadata)
        {
            var customerSegmentTypeName = gender switch
            {
                "Female" => MerchantMetaTypeNames.CustomerSegmentFemale,
                "Male" => MerchantMetaTypeNames.CustomerSegmentMale,
                _ => MerchantMetaTypeNames.CustomerSegmentUnknown
            };

            var customerSegment = MerchantSegmentHelper.GetCustomerSegment(merchantMeta, customerSegmentTypeName);
            bool isCustomerInSegment = customerSegment == "all" || MerchantSegmentHelper.IsCustomerInSegment(customerSegment, age);
            
            if (isCustomerInSegment)
            {
                allowedMerchants.Add(merchantId);
            }
        }
        
        return allowedMerchants;
    }
} 