using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Recommendation_Services.Services.Finalizers;

namespace Recommendation_Services.Services.Rules;

public interface IOfferPreCalculationRuleRegistry
{
    IEnumerable<(IOfferPreCalculationRule Rule, int Priority)> GetRulesByStrategy(string contextType);
    PreCalculationFinalizer? GetFinalizerByStrategy(string contextType);
    IEnumerable<string> GetAllStrategies();
}

public class OfferPreCalculationRuleRegistry(IEnumerable<IOfferPreCalculationRule> rules)
    : IOfferPreCalculationRuleRegistry
{
    private readonly Dictionary<string, (List<(IOfferPreCalculationRule Rule, int Priority)> Rules, PreCalculationFinalizer? Finalizer)> _strategyMap = new()
    {
        /*["Discount"] = (new()
        {
            //(rules.OfType<MerchantAgeAndGenderRule>().First(), 1),
            //(rules.OfType<DiscountPreCalculationRule>().First(), 2)
        }, DiscountFinalizer.Run),*/
        ["CuratedProductsPriceDrop"] = (new()
        {
            (rules.OfType<MerchantAgeAndGenderRule>().First(), 1),
            (rules.OfType<RankedMerchantProductsByBehaviorRule>().First(), 2),
            (rules.OfType<ProductKeywordFilterRule>().First(), 3),
            (rules.OfType<CategoryFilterRule>().First(), 4),
            (rules.OfType<CuratedProductsPriceDropRule>().First(), 5)
        }, CuratedProductsFinalizer.Run),
        ["CuratedProducts"] = (new()
        {
            (rules.OfType<MerchantAgeAndGenderRule>().First(), 1),
            (rules.OfType<RankedMerchantProductsByBehaviorRule>().First(), 2),
            (rules.OfType<ProductKeywordFilterRule>().First(), 3),
            (rules.OfType<CategoryFilterRule>().First(), 4),
            (rules.OfType<CuratedProductsRule>().First(), 5)
        }, CuratedProductsFinalizer.Run),
        /*["MerchantAgeAndGenderRevenue"] = (new()
        {
            (rules.OfType<MerchantAgeAndGenderRule>().First(), 1)
        }, MerchantAgeAndGenderRevenueFinalizer.Run)*/
    };

    // In-code mapping: context name -> (list of (rule, priority), finalizer)

    public IEnumerable<(IOfferPreCalculationRule Rule, int Priority)> GetRulesByStrategy(string contextType) =>
        _strategyMap.TryGetValue(contextType, out var entry)
            ? entry.Rules.OrderBy(r => r.Priority)
            : Enumerable.Empty<(IOfferPreCalculationRule, int)>();

    public PreCalculationFinalizer? GetFinalizerByStrategy(string strategyName) =>
        _strategyMap.TryGetValue(strategyName, out var entry) ? entry.Finalizer : null;

    public IEnumerable<string> GetAllStrategies() => _strategyMap.Keys;
} 