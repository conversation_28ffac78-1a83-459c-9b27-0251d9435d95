using Serilog;
using Recommendation_Services.Repositories;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.EntityFrameworkCore;
using Recommendation_Services.Models.Dto.Rules;
using Microsoft.Extensions.DependencyInjection;
using Recommendation_Services.Models;

namespace Recommendation_Services.Services.Rules;

public class CuratedProductsPriceDropRule(
    IOfferRecommendationRepository repository,
    IServiceProvider serviceProvider,
    ILogger logger)
    : IOfferPreCalculationRule
{
    private static readonly HttpClient httpClient = new();

    public async Task PreCalculateAsync(CancellationToken cancellationToken, PreCalculationContext context)
    {
        var startTime = DateTime.UtcNow;
        logger.Information("Running CuratedProductsPriceDropRule");
        
        if (!context.Data.TryGetValue("RankedMerchantsWithProductsByDemographics", out var rankedObj) || 
            rankedObj is not Dictionary<(int? Age, string Gender), List<MerchantWithRankedProductsDto>> rankedByDemographics)
        {
            logger.Information("No ranked merchants with products by demographics found in context. Skipping.");
            return;
        }

        // Build final result by demographic - process each demographic separately to respect filtering
        var topOnSaleProductsByDemographics = new Dictionary<(int? Age, string Gender), Dictionary<string, List<RankedProductDto>>>();
        var permalinkValidationCache = context.GetOrSetCache("PermalinkValidationCache", 
            () => new Dictionary<string, bool>());
        
        foreach (var ((age, gender), merchantsForGroup) in rankedByDemographics)
        {
            var topOnSaleProductsByMerchant = new Dictionary<string, List<RankedProductDto>>();
            
            // Process each merchant for this specific demographic group
            if (context.EnableParallelProcessing)
            {
                // Process merchants in batches for this demographic
                var batchSize = context.BatchSize;
                for (int i = 0; i < merchantsForGroup.Count; i += batchSize)
                {
                    var batch = merchantsForGroup.Skip(i).Take(batchSize).ToList();
                    var batchTasks = batch.Select(merchant => 
                        ProcessMerchantOnSaleProducts(merchant, permalinkValidationCache, serviceProvider, cancellationToken));
                    var batchResults = await Task.WhenAll(batchTasks);
                    
                    foreach (var (merchantId, products) in batchResults)
                    {
                        if (products.Any())
                        {
                            topOnSaleProductsByMerchant[merchantId.ToString()] = products;
                        }
                    }
                }
            }
            else
            {
                // Sequential processing for this demographic
                foreach (var merchant in merchantsForGroup)
                {
                    var (merchantId, products) = await ProcessMerchantOnSaleProducts(merchant, permalinkValidationCache, serviceProvider, cancellationToken);
                    if (products.Any())
                    {
                        topOnSaleProductsByMerchant[merchantId.ToString()] = products;
                    }
                }
            }
            
            topOnSaleProductsByDemographics[(age, gender)] = topOnSaleProductsByMerchant;
        }
        
        context.Data["FilteredProducts"] = topOnSaleProductsByDemographics;
        
        var duration = DateTime.UtcNow - startTime;
        context.TrackRuleExecution(nameof(CuratedProductsPriceDropRule), duration);
            
        logger.Information($"Completed CuratedProductsPriceDropRule in {duration.TotalSeconds:F2}s");
    }

    private async Task<(int MerchantId, List<RankedProductDto> Products)> ProcessMerchantOnSaleProducts(
        MerchantWithRankedProductsDto merchant,
        Dictionary<string, bool> permalinkValidationCache,
        IServiceProvider serviceProvider,
        CancellationToken cancellationToken)
    {
        if (!merchant.RankedProducts.Any())
        {
            return (merchant.MerchantId, new List<RankedProductDto>());
        }

        var productIds = merchant.RankedProducts.Select(p => p.ProductInternalId).ToList();
        
        // Create a scoped DbContext for this task to avoid concurrency issues
        using var scope = serviceProvider.CreateScope();
        var merchantDbContext = scope.ServiceProvider.GetRequiredService<MerchantDbContext>();
        
        // Get on-sale products for this merchant
        var onSaleProducts = await merchantDbContext.Products
            .Where(p => productIds.Contains(p.InternalProductId)
                        && p.Price != null
                        && p.RegularPrice != null
                        && p.Price < p.RegularPrice
                        && p.RegularPrice > 100 // To Filter out Shipment and Package Labels
                        && p.Active
                        && !string.IsNullOrEmpty(p.Permalink)
                        && (p.Status != "private" || p.Status != "draft")
                        && p.IsInStock == true)
            .Select(p => new { p.InternalProductId, p.Permalink, p.Id })
            .ToListAsync(cancellationToken);

        if (!onSaleProducts.Any())
        {
            return (merchant.MerchantId, new List<RankedProductDto>());
        }

        var onSaleSet = new HashSet<string>(onSaleProducts.Select(p => p.InternalProductId));
        var dbIdDict = onSaleProducts.ToDictionary(p => p.InternalProductId, p => p.Id);
        var permalinkDict = onSaleProducts.ToDictionary(p => p.InternalProductId, p => p.Permalink);
        
        var filteredProducts = merchant.RankedProducts
            .Where(p => onSaleSet.Contains(p.ProductInternalId))
            .OrderByDescending(p => p.ProductScore)
            .ToList();

        var validProducts = new List<RankedProductDto>();
        
        foreach (var product in filteredProducts)
        {
            if (!permalinkDict.TryGetValue(product.ProductInternalId, out var permalink) || string.IsNullOrWhiteSpace(permalink))
                continue;

            // Check permalink validation cache first
            if (!permalinkValidationCache.TryGetValue(permalink, out var isValid))
            {
                try
                {
                    using var request = new HttpRequestMessage(HttpMethod.Get, permalink);
                    using var response = await httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
                    isValid = response.StatusCode != System.Net.HttpStatusCode.NotFound;
                }
                catch
                {
                    isValid = false;
                }
                permalinkValidationCache[permalink] = isValid;
            }

            if (!isValid) continue;

            if (dbIdDict.TryGetValue(product.ProductInternalId, out var dbId))
            {
                validProducts.Add(new RankedProductDto
                {
                    ProductInternalId = product.ProductInternalId,
                    ProductDbId = dbId,
                    ProductScore = product.ProductScore
                });
                
                if (validProducts.Count == 10) // Limit to 10 products per merchant
                    break;
            }
        }

        return (merchant.MerchantId, validProducts);
    }
} 