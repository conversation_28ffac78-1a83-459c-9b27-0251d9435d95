using Discount_Services.Services.Discounts;
using Microsoft.Extensions.Logging;
using Recommendation_Services.Models.Dto.Rules;
using Recommendation_Services.Models;

namespace Recommendation_Services.Services.Rules;

public class DiscountPreCalculationRule(ILogger<DiscountPreCalculationRule> logger, IDiscountService discountService) : IOfferPreCalculationRule
{
    // Placeholder for discount fetching service/repository
    // private readonly IDiscountRepository discountRepository;

    //, IDiscountRepository discountRepository
    // this.discountRepository = discountRepository;

    public async Task PreCalculateAsync(CancellationToken cancellationToken, PreCalculationContext context)
    {
        logger.LogInformation("Running DiscountPreCalculationRule for age/gender discount scoring (parent discount id)");
        if (!context.Data.TryGetValue("MerchantScores", out var merchantScoresObj) || merchantScoresObj is not List<MerchantAgeAndGenderScoreDto> merchantScores)
        {
            logger.LogWarning("No merchant scores found in context. Skipping discount calculation.");
            return;
        }

        // Group merchant scores by (age, gender)
        var scoresByAgeGender = merchantScores
            .GroupBy(ms => (ms.Age, ms.Gender))
            .ToDictionary(g => (g.Key.Age, g.Key.Gender), g => g.ToList());

        // Fetch all active discounts (with children)
        var allActiveDiscounts = await discountService.GetAllActiveDiscountsWithChildrenAsync();

        // Prepare result: (age, gender) -> List<(parentDiscountId, score)>
        var discountScoresByAgeGender = new Dictionary<(byte? Age, string Gender), List<(int ParentDiscountId, double Score)>>();

        foreach (var (ageGenderGroup, groupScores) in scoresByAgeGender)
        {
            var age = ageGenderGroup.Age;
            var gender = ageGenderGroup.Gender;
            var discountScoreList = new List<(int, double)>();

            // Allowed merchants for this age/gender group
            var allowedMerchantIds = new HashSet<string>(groupScores.Select(ms => ms.MerchantId));

            // Filter discounts to only those for allowed merchants
            var filteredDiscounts = allActiveDiscounts
                .Where(d => allowedMerchantIds.Contains(d.FkMerchantId.ToString()))
                .ToList();

            foreach (var merchantScore in groupScores)
            {
                // Find all parent discounts for this merchant
                var merchantParentDiscounts = filteredDiscounts
                    .Where(d => d.FkMerchantId.ToString() == merchantScore.MerchantId)
                    .ToList();

                discountScoreList.AddRange((IEnumerable<(int, double)>)merchantParentDiscounts.Select(parentDiscount => (parentDiscount.Id, merchantScore.Sum)));
            }
            // Optionally, sort by score descending
            discountScoresByAgeGender[(age, gender)] = discountScoreList.OrderByDescending(x => x.Item2).ToList();
        }

        // Store the result in the context for the finalizer
        context.Data["DiscountScoresByAgeGender"] = discountScoresByAgeGender;
    }
}