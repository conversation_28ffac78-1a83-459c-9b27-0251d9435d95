using Serilog;
using Recommendation_Services.Models.Dto.Rules;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Recommendation_Services.Models;
using Recommendation_Services.Models.Configs;

namespace Recommendation_Services.Services.Rules;

public class CategoryFilterRule(
    ILogger logger,
    IServiceProvider serviceProvider)
    : IOfferPreCalculationRule
{
    public async Task PreCalculateAsync(CancellationToken cancellationToken, PreCalculationContext context)
    {
        var startTime = DateTime.UtcNow;
        logger.Information("Running CategoryFilterRule");
        Console.WriteLine("Running CategoryFilterRule");
        
        if (!context.Data.TryGetValue("RankedMerchantsWithProductsByDemographics", out var rankedObj) || 
            rankedObj is not Dictionary<(int? Age, string Gender), List<MerchantWithRankedProductsDto>> rankedByDemographics)
        {
            logger.Information("No ranked merchants with products by demographics found in context. Skipping category filtering.");
            Console.WriteLine("No ranked merchants with products by demographics found in context. Skipping category filtering.");
            return;
        }

        var filteredResults = new Dictionary<(int? Age, string Gender), List<MerchantWithRankedProductsDto>>();
        
        // Get all unique merchant IDs that have category filters configured
        var configuredMerchants = MerchantCategoryFilterConfig.GetConfiguredMerchants().ToHashSet();
        
        // Get merchants that have filters and need product category data
        var merchantsWithFilters = rankedByDemographics.Values
            .SelectMany(merchants => merchants)
            .Where(m => configuredMerchants.Contains(m.MerchantId))
            .Select(m => m.MerchantId)
            .Distinct()
            .ToList();

        // Cache product categories only for merchants that have category filters
        Dictionary<string, string?> productCategoriesCache = new();
        if (merchantsWithFilters.Any())
        {
            productCategoriesCache = await GetProductCategoriesCache(merchantsWithFilters, rankedByDemographics, context, cancellationToken);
        }

        foreach (var ((age, gender), merchantsForGroup) in rankedByDemographics)
        {
            var filteredMerchants = new List<MerchantWithRankedProductsDto>();

            foreach (var merchant in merchantsForGroup)
            {
                // Get category filters for this merchant and demographic
                var filters = MerchantCategoryFilterConfig.GetFiltersForMerchant(merchant.MerchantId, gender, age);
                
                if (!filters.Any())
                {
                    // No filters = include all products unchanged
                    filteredMerchants.Add(merchant);
                    continue;
                }

                // Apply category filtering to products
                var filteredProducts = new List<RankedProductDto>();
                
                foreach (var product in merchant.RankedProducts)
                {
                    var productCategories = productCategoriesCache.GetValueOrDefault(product.ProductInternalId);
                    
                    if (MerchantCategoryFilterConfig.ShouldIncludeProduct(productCategories, filters))
                    {
                        filteredProducts.Add(product);
                    }
                    else
                    {
                        logger.Debug($"Filtered out product {product.ProductInternalId} for merchant {merchant.MerchantId} due to category rules");
                    }
                }

                // Only include merchant if they have remaining products after filtering
                if (filteredProducts.Any())
                {
                    filteredMerchants.Add(new MerchantWithRankedProductsDto
                    {
                        MerchantId = merchant.MerchantId,
                        RankedProducts = filteredProducts
                    });
                    
                    logger.Information($"Applied category filters to merchant {merchant.MerchantId} for {gender} {age}: {merchant.RankedProducts.Count} -> {filteredProducts.Count} products");
                }
                else
                {
                    logger.Information($"Merchant {merchant.MerchantId} removed for {gender} {age} - no products remaining after category filtering");
                }
            }
            
            filteredResults[(age, gender)] = filteredMerchants;
        }

        foreach (var result in filteredResults)
        {
            foreach (var merchant in result.Value)
            {
                Console.WriteLine($"Age: {result.Key.Age}, Gender: {result.Key.Gender}, MerchantId: {merchant.MerchantId}, ProductCount: {merchant.RankedProducts.Count}");
            }
        }
        
        // Update context with filtered results
        context.Data["RankedMerchantsWithProductsByDemographics"] = filteredResults;
        
        var duration = DateTime.UtcNow - startTime;
        context.TrackRuleExecution(nameof(CategoryFilterRule), duration);
            
        logger.Information($"Completed CategoryFilterRule in {duration.TotalSeconds:F2}s");
        Console.WriteLine($"Completed CategoryFilterRule in {duration.TotalSeconds:F2}s");
    }

    private async Task<Dictionary<string, string?>> GetProductCategoriesCache(
        List<int> merchantsWithFilters,
        Dictionary<(int? Age, string Gender), List<MerchantWithRankedProductsDto>> rankedByDemographics,
        PreCalculationContext context,
        CancellationToken cancellationToken)
    {
        // Get all unique product internal IDs for merchants that have category filters
        var allProductIds = rankedByDemographics.Values
            .SelectMany(merchants => merchants
                .Where(m => merchantsWithFilters.Contains(m.MerchantId))
                .SelectMany(m => m.RankedProducts.Select(p => p.ProductInternalId)))
            .Distinct()
            .ToList();

        if (!allProductIds.Any())
        {
            return new Dictionary<string, string?>();
        }

        var cacheKey = $"ProductCategories_{string.Join(",", merchantsWithFilters.OrderBy(x => x))}_{allProductIds.Count}";
        
        return await context.GetOrSetCacheAsync(cacheKey, async () =>
        {
            logger.Information($"Fetching categories for {allProductIds.Count} products from {merchantsWithFilters.Count} merchants with filters (not cached)");
            Console.WriteLine($"Fetching categories for {allProductIds.Count} products from {merchantsWithFilters.Count} merchants with filters (not cached)");
            
            // Create a scoped DbContext for this operation
            using var scope = serviceProvider.CreateScope();
            var merchantDbContext = scope.ServiceProvider.GetRequiredService<MerchantDbContext>();
            
            // Batch fetch product categories
            const int batchSize = 1000; // Process in batches to avoid large queries
            var categoriesDict = new Dictionary<string, string?>();
            
            for (int i = 0; i < allProductIds.Count; i += batchSize)
            {
                var batch = allProductIds.Skip(i).Take(batchSize).ToList();
                
                var batchCategories = await merchantDbContext.Products
                    .Where(p => batch.Contains(p.InternalProductId))
                    .Select(p => new { p.InternalProductId, p.Categories })
                    .ToListAsync(cancellationToken);
                
                foreach (var product in batchCategories)
                {
                    categoriesDict[product.InternalProductId] = product.Categories;
                }
            }
            
            logger.Information($"Cached categories for {categoriesDict.Count} products");
            Console.WriteLine($"Cached categories for {categoriesDict.Count} products");
            
            return categoriesDict;
        });
    }
} 