using Serilog;
using Recommendation_Services.Models.Dto.Rules;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Recommendation_Services.Models;
using Recommendation_Services.Models.Configs;

namespace Recommendation_Services.Services.Rules;

public class ProductKeywordFilterRule(
    ILogger logger,
    IServiceProvider serviceProvider)
    : IOfferPreCalculationRule
{
    public async Task PreCalculateAsync(CancellationToken cancellationToken, PreCalculationContext context)
    {
        var startTime = DateTime.UtcNow;
        logger.Information("Running ProductKeywordFilterRule");
        Console.WriteLine("Running ProductKeywordFilterRule");
        
        if (!context.Data.TryGetValue("RankedMerchantsWithProductsByDemographics", out var rankedObj) || 
            rankedObj is not Dictionary<(int? Age, string Gender), List<MerchantWithRankedProductsDto>> rankedByDemographics)
        {
            logger.Information("No ranked merchants with products by demographics found in context. Skipping keyword filtering.");
            Console.WriteLine("No ranked merchants with products by demographics found in context. Skipping keyword filtering.");
            return;
        }

        var filteredResults = new Dictionary<(int? Age, string Gender), List<MerchantWithRankedProductsDto>>();
        
        // Get all unique product internal IDs for batch processing
        var allProductIds = rankedByDemographics.Values
            .SelectMany(merchants => merchants
                .SelectMany(m => m.RankedProducts.Select(p => p.ProductInternalId)))
            .Distinct()
            .ToList();

        if (!allProductIds.Any())
        {
            logger.Information("No products found to filter. Skipping keyword filtering.");
            Console.WriteLine("No products found to filter. Skipping keyword filtering.");
            context.Data["RankedMerchantsWithProductsByDemographics"] = rankedByDemographics; // Pass through unchanged
            return;
        }

        // Cache product names for keyword filtering
        var productNamesCache = await GetProductNamesCache(allProductIds, context, cancellationToken);

        var totalProductsBeforeFiltering = 0;
        var totalProductsAfterFiltering = 0;
        var totalFilteredProducts = 0;
        var filteredProductsList = new List<(string ProductName, string ProductId, int MerchantId, string Demographic)>();

        foreach (var ((age, gender), merchantsForGroup) in rankedByDemographics)
        {
            var filteredMerchants = new List<MerchantWithRankedProductsDto>();

            foreach (var merchant in merchantsForGroup)
            {
                var originalProductCount = merchant.RankedProducts.Count;
                totalProductsBeforeFiltering += originalProductCount;

                // Apply keyword filtering to products
                var filteredProducts = new List<RankedProductDto>();
                
                foreach (var product in merchant.RankedProducts)
                {
                    var productName = productNamesCache.GetValueOrDefault(product.ProductInternalId);
                    
                    if (!ProductKeywordFilterConfig.ShouldFilterProduct(productName))
                    {
                        filteredProducts.Add(product);
                    }
                    else
                    {
                        totalFilteredProducts++;
                        var demographicInfo = $"{gender} {age}";
                        filteredProductsList.Add((productName ?? "Unknown", product.ProductInternalId, merchant.MerchantId, demographicInfo));
                        
                        logger.Debug($"Filtered out product '{productName}' (ID: {product.ProductInternalId}) for merchant {merchant.MerchantId} due to keyword filtering");
                    }
                }

                totalProductsAfterFiltering += filteredProducts.Count;

                // Only include merchant if they have remaining products after filtering
                if (filteredProducts.Any())
                {
                    filteredMerchants.Add(new MerchantWithRankedProductsDto
                    {
                        MerchantId = merchant.MerchantId,
                        RankedProducts = filteredProducts
                    });
                    
                    if (originalProductCount != filteredProducts.Count)
                    {
                        logger.Information($"Applied keyword filters to merchant {merchant.MerchantId} for {gender} {age}: {originalProductCount} -> {filteredProducts.Count} products");
                    }
                }
                else
                {
                    logger.Information($"Merchant {merchant.MerchantId} removed for {gender} {age} - no products remaining after keyword filtering");
                }
            }
            
            filteredResults[(age, gender)] = filteredMerchants;
        }
        
        // Print filtered products to console
        if (filteredProductsList.Any())
        {
            Console.WriteLine($"\n=== FILTERED PRODUCTS ({filteredProductsList.Count} total) ===");
            foreach (var (productName, productId, merchantId, demographic) in filteredProductsList)
            {
                Console.WriteLine($"🚫 Merchant {merchantId} | {demographic} | '{productName}' (ID: {productId})");
            }
            Console.WriteLine("=== END FILTERED PRODUCTS ===\n");
        }
        else
        {
            Console.WriteLine("✅ No products were filtered out by keyword filtering.");
        }
        
        // Update context with filtered results
        context.Data["RankedMerchantsWithProductsByDemographics"] = filteredResults;
        
        var duration = DateTime.UtcNow - startTime;
        context.TrackRuleExecution(nameof(ProductKeywordFilterRule), duration);
            
        logger.Information($"Completed ProductKeywordFilterRule in {duration.TotalSeconds:F2}s");
        logger.Information($"Keyword filtering summary: {totalProductsBeforeFiltering} -> {totalProductsAfterFiltering} products ({totalFilteredProducts} filtered out)");
        Console.WriteLine($"Completed ProductKeywordFilterRule in {duration.TotalSeconds:F2}s");
        Console.WriteLine($"Keyword filtering summary: {totalProductsBeforeFiltering} -> {totalProductsAfterFiltering} products ({totalFilteredProducts} filtered out)");
    }

    private async Task<Dictionary<string, string?>> GetProductNamesCache(
        List<string> allProductIds,
        PreCalculationContext context,
        CancellationToken cancellationToken)
    {
        var cacheKey = $"ProductNames_{allProductIds.Count}_{string.Join(",", allProductIds.Take(5).OrderBy(x => x))}";
        
        return await context.GetOrSetCacheAsync(cacheKey, async () =>
        {
            logger.Information($"Fetching product names for {allProductIds.Count} products for keyword filtering (not cached)");
            Console.WriteLine($"Fetching product names for {allProductIds.Count} products for keyword filtering (not cached)");
            
            // Create a scoped DbContext for this operation
            using var scope = serviceProvider.CreateScope();
            var merchantDbContext = scope.ServiceProvider.GetRequiredService<MerchantDbContext>();
            
            // Batch fetch product names
            const int batchSize = 1000; // Process in batches to avoid large queries
            var productNamesDict = new Dictionary<string, string?>();
            
            for (int i = 0; i < allProductIds.Count; i += batchSize)
            {
                var batch = allProductIds.Skip(i).Take(batchSize).ToList();
                
                var batchNames = await merchantDbContext.Products
                    .Where(p => batch.Contains(p.InternalProductId))
                    .Select(p => new { p.InternalProductId, p.Name })
                    .ToListAsync(cancellationToken);
                
                foreach (var product in batchNames)
                {
                    productNamesDict[product.InternalProductId] = product.Name;
                }
            }
            
            logger.Information($"Cached names for {productNamesDict.Count} products");
            Console.WriteLine($"Cached names for {productNamesDict.Count} products");
            
            return productNamesDict;
        });
    }
} 