using System.Linq.Expressions;
using Recommendation_Services.Models;
using Recommendation_Services.Models.ModelsDal.Recommendation;

namespace Recommendation_Services.Services;

/// <summary>
/// Service for managing offer recommendations with both DTO and entity-based operations
/// </summary>
public interface IOfferRecommendationService
{
    /// <summary>
    /// Gets recommendations as DTOs for API responses
    /// </summary>
    Task<List<OfferRecommendationDto>> GetRecommendationsAsync(string strategy, string gender, int? age);
    
    /// <summary>
    /// Gets a single recommendation by ID as DTO
    /// </summary>
    Task<OfferRecommendationDto?> GetByIdAsync(int id);
    
    /// <summary>
    /// Gets recommendations by strategy with optional filters as DTOs
    /// </summary>
    Task<List<OfferRecommendationDto>> GetRecommendationsByStrategyAsync(string strategy, string? gender, int? age);
    
    /// <summary>
    /// Gets recommendation entities with limit for internal processing
    /// </summary>
    Task<List<OfferRecommendationDto>> GetRecommendationEntitiesAsync(string strategy, int numberOfRecommendations, string? gender, int? age);
    
    /// <summary>
    /// Gets paginated recommendations with optional sorting
    /// </summary>
    Task<List<OfferRecommendationDto>> GetPagedRecommendationsAsync(string strategy, int page, int pageSize, string? sortBy = null);
    
    /// <summary>
    /// Checks if active recommendations exist for strategy and gender
    /// </summary>
    Task<bool> HasActiveRecommendationsAsync(string strategy, string gender);
    
    /// <summary>
    /// Gets count of active recommendations for a strategy
    /// </summary>
    Task<int> GetRecommendationCountAsync(string strategy);

    /// <summary>
    /// Runs all recommendations
    /// </summary>
    Task RunRecommendationsAsync(CancellationToken stoppingToken);

} 