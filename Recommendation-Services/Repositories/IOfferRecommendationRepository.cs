using System.Linq.Expressions;
using Recommendation_Services.Models.ModelsDal.Recommendation;

namespace Recommendation_Services.Repositories;

/// <summary>
/// Generic repository for OfferRecommendation entities with partner-scoped operations
/// </summary>
public interface IOfferRecommendationRepository
{
    /// <summary>
    /// Gets a recommendation by its ID
    /// </summary>
    Task<OfferRecommendation?> GetByIdAsync(int id);
    
    /// <summary>
    /// Adds a new recommendation to the context
    /// </summary>
    Task AddAsync(OfferRecommendation recommendation);
    
    /// <summary>
    /// Saves all pending changes to the database
    /// </summary>
    Task SaveChangesAsync();
    
    /// <summary>
    /// Gets recommendations based on a predicate with optional limit
    /// </summary>
    Task<List<OfferRecommendation>> GetAsync(Expression<Func<OfferRecommendation, bool>> predicate, int? take = null);
    
    /// <summary>
    /// Gets a single recommendation based on a predicate
    /// </summary>
    Task<OfferRecommendation?> GetSingleAsync(Expression<Func<OfferRecommendation, bool>> predicate);
    
    /// <summary>
    /// Checks if any recommendation exists matching the predicate
    /// </summary>
    Task<bool> ExistsAsync(Expression<Func<OfferRecommendation, bool>> predicate);
    
    /// <summary>
    /// Counts recommendations matching the predicate
    /// </summary>
    Task<int> CountAsync(Expression<Func<OfferRecommendation, bool>> predicate);
    
    /// <summary>
    /// Executes a pre-built queryable and returns results
    /// </summary>
    Task<List<OfferRecommendation>> GetAsync(IQueryable<OfferRecommendation> queryable);
    
    /// <summary>
    /// Gets a queryable for building complex queries in the service layer
    /// </summary>
    IQueryable<OfferRecommendation> GetQueryable();
}   