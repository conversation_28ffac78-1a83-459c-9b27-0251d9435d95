using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using Partner_Services.Services.General;
using Recommendation_Services.Models.ModelsDal.Recommendation;
using Shared.Services.Partner;

namespace Recommendation_Services.Repositories;

public class OfferRecommendationRepository(RecommendationDbContext dbContext, IPartnerService partnerService) : IOfferRecommendationRepository
{
    public async Task<OfferRecommendation?> GetByIdAsync(int id)
    {
        return await dbContext.OfferRecommendations
            .Include(r => r.OfferRecommendationItems)
            .FirstOrDefaultAsync(r => r.Id == id);
    }

    public async Task AddAsync(OfferRecommendation recommendation)
    {
        await dbContext.OfferRecommendations.AddAsync(recommendation);
    }

    public async Task SaveChangesAsync()
    {
        await dbContext.SaveChangesAsync();
    }

    public async Task<List<OfferRecommendation>> GetAsync(Expression<Func<OfferRecommendation, bool>> predicate, int? take = null)
    {
        var partnerId = partnerService.GetCurrentPartnerId();
        var query = dbContext.OfferRecommendations
            .Include(r => r.OfferRecommendationItems)
            .Where(r => r.PartnerId == partnerId)
            .Where(predicate);

        if (take.HasValue)
            query = query.Take(take.Value);

        return await query.ToListAsync();
    }

    public async Task<OfferRecommendation?> GetSingleAsync(Expression<Func<OfferRecommendation, bool>> predicate)
    {
        var partnerId = partnerService.GetCurrentPartnerId();
        return await dbContext.OfferRecommendations
            .Include(r => r.OfferRecommendationItems)
            .Where(r => r.PartnerId == partnerId)
            .Where(predicate)
            .FirstOrDefaultAsync();
    }

    public async Task<bool> ExistsAsync(Expression<Func<OfferRecommendation, bool>> predicate)
    {
        var partnerId = partnerService.GetCurrentPartnerId();
        return await dbContext.OfferRecommendations
            .Where(r => r.PartnerId == partnerId)
            .AnyAsync(predicate);
    }

    public async Task<int> CountAsync(Expression<Func<OfferRecommendation, bool>> predicate)
    {
        var partnerId = partnerService.GetCurrentPartnerId();
        return await dbContext.OfferRecommendations
            .Where(r => r.PartnerId == partnerId)
            .CountAsync(predicate);
    }

    public async Task<List<OfferRecommendation>> GetAsync(IQueryable<OfferRecommendation> queryable)
    {
        return await queryable
            .Include(r => r.OfferRecommendationItems)
            .ToListAsync();
    }

    public IQueryable<OfferRecommendation> GetQueryable()
    {
        var partnerId = partnerService.GetCurrentPartnerId();
        return dbContext.OfferRecommendations
            .Include(r => r.OfferRecommendationItems)
            .Where(r => r.PartnerId == partnerId);
    }
} 