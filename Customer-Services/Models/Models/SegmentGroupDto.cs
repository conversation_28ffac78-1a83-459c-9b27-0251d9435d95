using System;
using System.Collections.Generic;

namespace Customer_Services.Models.ModelsDal.Customer;

public class SegmentGroupDto
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public string Type { get; set; } = null!;

    public int SortOrder { get; set; }

    public int FkSegmentId { get; set; }

    public int? FkSegmentGroupId { get; set; }

    public virtual Segment? FkSegment { get; set; } = null!;

    public virtual SegmentGroup? FkSegmentGroup { get; set; }

    public virtual ICollection<SegmentGroupDto> InverseFkSegmentGroup { get; set; } = new List<SegmentGroupDto>();

    public virtual ICollection<SegmentValueDto> SegmentValues { get; set; } = new List<SegmentValueDto>();
}