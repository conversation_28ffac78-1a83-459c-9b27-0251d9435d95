using System;
using System.Collections.Generic;

namespace Customer_Services.Models.ModelsDal.Customer;

public class SegmentValueDto
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public string? Type { get; set; }

    public string? Condition { get; set; }

    public string? Value { get; set; }
    public string? Value1 { get; set; }

    public int SortOrder { get; set; }

    public int FkSegmentGroupId { get; set; }

    public string? ValueType { get; set; }

    //public virtual SegmentGroup FkSegmentGroup { get; set; } = null!;
}