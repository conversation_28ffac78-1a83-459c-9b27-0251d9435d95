using System;
using System.Collections.Generic;

namespace Customer_Services.Models.ModelsDal.Customer;

public class SegmentDto
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public string Name { get; set; } = null!;

    public int SortOrder { get; set; }

    public ICollection<SegmentGroupDto> SegmentGroups { get; set; } = new List<SegmentGroupDto>();
}