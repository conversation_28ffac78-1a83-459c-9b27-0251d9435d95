using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Customer_Services.Models.ModelsDal.Customer;

[Table("CustomerExposureTypes", Schema = "customer")]
public partial class CustomerExposureType
{
    [Key]
    public int Id { get; set; }

    [StringLength(2000)]
    public string Name { get; set; } = null!;

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [InverseProperty("FkCustomerExposuresType")]
    public virtual ICollection<CustomerExposure> CustomerExposures { get; set; } = new List<CustomerExposure>();
}
