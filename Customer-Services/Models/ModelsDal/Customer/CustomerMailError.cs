using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Customer_Services.Models.ModelsDal.Customer;

[Table("CustomerMailError", Schema = "customer")]
public partial class CustomerMailError
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    [StringLength(255)]
    public string Error { get; set; } = null!;

    [StringLength(2000)]
    public string Reason { get; set; } = null!;

    [Column("FK_CustomerId")]
    public long FkCustomerId { get; set; }

    [ForeignKey("FkCustomerId")]
    [InverseProperty("CustomerMailErrors")]
    public virtual Customer FkCustomer { get; set; } = null!;
}
