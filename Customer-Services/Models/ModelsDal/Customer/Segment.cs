using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Customer_Services.Models.ModelsDal.Customer;

[Table("Segments", Schema = "customer")]
public partial class Segment
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(200)]
    public string Name { get; set; } = null!;

    public int SortOrder { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [InverseProperty("FkSegment")]
    public virtual ICollection<SegmentGroup> SegmentGroups { get; set; } = new List<SegmentGroup>();
}
