using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Customer_Services.Models.ModelsDal.Customer;

[Table("SegmentGroups", Schema = "customer")]
public partial class SegmentGroup
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(50)]
    public string Type { get; set; } = null!;

    public int SortOrder { get; set; }

    [Column("FK_SegmentId")]
    public int FkSegmentId { get; set; }

    [Column("FK_SegmentGroupId")]
    public int? FkSegmentGroupId { get; set; }

    [ForeignKey("FkSegmentId")]
    [InverseProperty("SegmentGroups")]
    public virtual Segment FkSegment { get; set; } = null!;

    [ForeignKey("FkSegmentGroupId")]
    [InverseProperty("InverseFkSegmentGroup")]
    public virtual SegmentGroup? FkSegmentGroup { get; set; }

    [InverseProperty("FkSegmentGroup")]
    public virtual ICollection<SegmentGroup> InverseFkSegmentGroup { get; set; } = new List<SegmentGroup>();

    [InverseProperty("FkSegmentGroup")]
    public virtual ICollection<SegmentValue> SegmentValues { get; set; } = new List<SegmentValue>();
}
