using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Customer_Services.Models.ModelsDal.Customer;

[Table("CustomerExposures", Schema = "customer")]
public partial class CustomerExposure
{
    [Key]
    public long Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [Precision(0)]
    public DateTime ExposureStart { get; set; }

    [Precision(0)]
    public DateTime ExposureEnd { get; set; }

    [Column("FK_CustomerId")]
    public long FkCustomerId { get; set; }

    [Column("FK_MerchantId")]
    public int FkMerchantId { get; set; }

    [Column("FK_CustomerExposuresTypeId")]
    public int FkCustomerExposuresTypeId { get; set; }

    [ForeignKey("FkCustomerId")]
    [InverseProperty("CustomerExposures")]
    public virtual Customer FkCustomer { get; set; } = null!;

    [ForeignKey("FkCustomerExposuresTypeId")]
    [InverseProperty("CustomerExposures")]
    public virtual CustomerExposureType FkCustomerExposuresType { get; set; } = null!;
}
