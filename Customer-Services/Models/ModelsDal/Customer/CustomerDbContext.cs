using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace Customer_Services.Models.ModelsDal.Customer;

public partial class CustomerDbContext : DbContext
{
    public CustomerDbContext(DbContextOptions<CustomerDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Customer> Customers { get; set; }

    public virtual DbSet<CustomerExposure> CustomerExposures { get; set; }

    public virtual DbSet<CustomerExposureType> CustomerExposureTypes { get; set; }

    public virtual DbSet<CustomerMailError> CustomerMailErrors { get; set; }

    public virtual DbSet<CustomerMetaType> CustomerMetaTypes { get; set; }

    public virtual DbSet<CustomerMetum> CustomerMeta { get; set; }

    public virtual DbSet<Segment> Segments { get; set; }

    public virtual DbSet<SegmentGroup> SegmentGroups { get; set; }

    public virtual DbSet<SegmentValue> SegmentValues { get; set; }

    public virtual DbSet<Unsubscribe> Unsubscribes { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Customer>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Customer__3214EC07C3A98592");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<CustomerExposure>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Customer__3214EC076376BC2F");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkCustomerExposuresType).WithMany(p => p.CustomerExposures)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__CustomerE__FK_Cu__62AFA012");

            entity.HasOne(d => d.FkCustomer).WithMany(p => p.CustomerExposures)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__CustomerE__FK_Cu__60C757A0");
        });

        modelBuilder.Entity<CustomerExposureType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Customer__3214EC073C3EF66B");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<CustomerMailError>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Customer__3214EC077974B581");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkCustomer).WithMany(p => p.CustomerMailErrors)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__CustomerM__FK_Cu__52E34C9D");
        });

        modelBuilder.Entity<CustomerMetaType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Customer__3214EC07476FC47D");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<CustomerMetum>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Customer__3214EC075E6CA21E");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkCustomer).WithMany(p => p.CustomerMeta)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__CustomerM__FK_Cu__695C9DA1");

            entity.HasOne(d => d.FkCustomerMetaType).WithMany(p => p.CustomerMeta)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__CustomerM__FK_Cu__6B44E613");
        });

        modelBuilder.Entity<Segment>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Segments__3214EC07DE187C52");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<SegmentGroup>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__SegmentG__3214EC07AFFAAD8D");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkSegmentGroup).WithMany(p => p.InverseFkSegmentGroup).HasConstraintName("FK__SegmentGr__FK_Se__00FF1D08");

            entity.HasOne(d => d.FkSegment).WithMany(p => p.SegmentGroups)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__SegmentGr__FK_Se__000AF8CF");
        });

        modelBuilder.Entity<SegmentValue>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__SegmentV__3214EC07E87631C2");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkSegmentGroup).WithMany(p => p.SegmentValues)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__SegmentVa__FK_Se__06B7F65E");
        });

        modelBuilder.Entity<Unsubscribe>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Unsubscr__3214EC07EB060A6F");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
