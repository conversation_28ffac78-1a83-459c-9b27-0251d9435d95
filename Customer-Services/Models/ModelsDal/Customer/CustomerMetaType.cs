using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Customer_Services.Models.ModelsDal.Customer;

[Table("CustomerMetaTypes", Schema = "customer")]
public partial class CustomerMetaType
{
    [Key]
    public int Id { get; set; }

    [StringLength(2000)]
    public string Name { get; set; } = null!;

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [InverseProperty("FkCustomerMetaType")]
    public virtual ICollection<CustomerMetum> CustomerMeta { get; set; } = new List<CustomerMetum>();
}
