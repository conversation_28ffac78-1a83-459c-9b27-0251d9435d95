using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Customer_Services.Models.ModelsDal.Customer;

[Table("Customers", Schema = "customer")]
[Index("Active", "Country", "MarketingStatus", Name = "nci_msft_1_Customers_250990A6363750F94A21302D136F4FBA")]
[Index("Active", "Country", "MarketingStatus", "FkPartnerId", Name = "nci_msft_1_Customers_7BBC87FE7051ABBA4013D223D6A6D4B3")]
public partial class Customer
{
    [Key]
    public long Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(255)]
    public string Email { get; set; } = null!;

    [StringLength(500)]
    public string FirstName { get; set; } = null!;

    [StringLength(500)]
    public string LastName { get; set; } = null!;

    [StringLength(50)]
    public string? ZipCode { get; set; }

    [StringLength(500)]
    public string Country { get; set; } = null!;

    public bool MarketingStatus { get; set; }

    [StringLength(200)]
    public string PhoneNumber { get; set; } = null!;

    [Column(TypeName = "decimal(16, 2)")]
    public decimal? CreditAvailability { get; set; }

    [StringLength(500)]
    public string? PartnerGuid { get; set; }

    [StringLength(500)]
    public string? PartnerId { get; set; }

    [StringLength(500)]
    public string? PartnerProduct { get; set; }

    public byte? PartnerInstallments { get; set; }

    public int MissedOpenMails { get; set; }

    [StringLength(50)]
    public string Gender { get; set; } = null!;

    public byte? Age { get; set; }

    public byte OpenRate { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [InverseProperty("FkCustomer")]
    public virtual ICollection<CustomerExposure> CustomerExposures { get; set; } = new List<CustomerExposure>();

    [InverseProperty("FkCustomer")]
    public virtual ICollection<CustomerMailError> CustomerMailErrors { get; set; } = new List<CustomerMailError>();

    [InverseProperty("FkCustomer")]
    public virtual ICollection<CustomerMetum> CustomerMeta { get; set; } = new List<CustomerMetum>();
}
