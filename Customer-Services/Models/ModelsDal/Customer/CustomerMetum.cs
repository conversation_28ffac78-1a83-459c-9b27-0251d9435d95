using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Customer_Services.Models.ModelsDal.Customer;

[Table("CustomerMeta", Schema = "customer")]
public partial class CustomerMetum
{
    [Key]
    public long Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(2000)]
    public string Value { get; set; } = null!;

    [Column("FK_CustomerId")]
    public long FkCustomerId { get; set; }

    [Column("FK_MerchantId")]
    public int? FkMerchantId { get; set; }

    [Column("FK_CustomerMetaTypeId")]
    public int FkCustomerMetaTypeId { get; set; }

    [ForeignKey("FkCustomerId")]
    [InverseProperty("CustomerMeta")]
    public virtual Customer FkCustomer { get; set; } = null!;

    [ForeignKey("FkCustomerMetaTypeId")]
    [InverseProperty("CustomerMeta")]
    public virtual CustomerMetaType FkCustomerMetaType { get; set; } = null!;
}
