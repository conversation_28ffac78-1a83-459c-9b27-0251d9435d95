using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Customer_Services.Models.ModelsDal.Customer;

[Table("SegmentValue", Schema = "customer")]
public partial class SegmentValue
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(100)]
    public string? Type { get; set; }

    [StringLength(100)]
    public string? Condition { get; set; }

    [StringLength(100)]
    public string? Value { get; set; }

    public int SortOrder { get; set; }

    [Column("FK_SegmentGroupId")]
    public int FkSegmentGroupId { get; set; }

    [StringLength(50)]
    public string? ValueType { get; set; }

    [StringLength(100)]
    public string? Value1 { get; set; }

    [ForeignKey("FkSegmentGroupId")]
    [InverseProperty("SegmentValues")]
    public virtual SegmentGroup FkSegmentGroup { get; set; } = null!;
}
