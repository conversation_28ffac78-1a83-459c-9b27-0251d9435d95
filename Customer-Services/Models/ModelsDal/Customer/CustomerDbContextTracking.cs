using System.Threading.Tasks;
using Customer_Services.Models.ModelsDal.Customer;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Shared;
using IConnection = RabbitMQ.Client.IConnection;

namespace Discount_Services.Models.ModelsDal.Discount;

public class CustomerDbContextTracking(
    DbContextOptions<CustomerDbContext> options,
    IHttpContextAccessor httpContextAccessor,
    [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud)
    //IConnection rabbitConnection)
    : CustomerDbContext(options)
{
    public virtual async Task<int> SaveChangesAsync()
    {
        OnBeforeSaveChanges();
        var result = await base.SaveChangesAsync();
        return result;
    }

    public virtual int SaveChanges()
    {
        OnBeforeSaveChanges();
        var result = base.SaveChanges();
        return result;
    }

    private void OnBeforeSaveChanges()
    {
        AuditSaveDb.OnBeforeSaveChanges(ChangeTracker, httpContextAccessor, rabbitConnectionCloud);
    }
}