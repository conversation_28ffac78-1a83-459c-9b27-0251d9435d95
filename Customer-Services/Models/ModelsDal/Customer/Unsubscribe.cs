using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Customer_Services.Models.ModelsDal.Customer;

[Table("Unsubscribes", Schema = "customer")]
public partial class Unsubscribe
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [StringLength(255)]
    public string Email { get; set; } = null!;

    [Precision(0)]
    public DateTime UnsubscribedAt { get; set; }

    [Column("FK_CampaignId")]
    public int? FkCampaignId { get; set; }
}
