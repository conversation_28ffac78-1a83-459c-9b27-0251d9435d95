using System;
using System.Collections.Generic;
using System.Runtime.InteropServices.JavaScript;
using System.Threading.Tasks;
using Audience_Service.Models.ModelsDto;
using Audience.Models;
using Customer_Services.Models.ModelsDal.Customer;
using Shared.Dto;
using Shared.Dto.Customer.Pagination;
using Shared.Dto.MerchantDashboard.Order;
using Shared.Dto.MerchantScore;
using Shared.Models;
using Shared.Models.Customer;
using Shared.Models.Message;
using CustomerServices.Models.Models;

namespace Audience.Services.Audience;

public interface ICustomerService
{
    Task<List<Customer>> GetAllAsync();
    Task<List<Customer>> GetAllActiveAsync();
    Task<List<Customer>> GetAllConsentAsync();
    Task<List<CustomerForCampaignsDto>> GetAllWithConsentForCampaignsAsync();
    Task<List<string>> GetAllConsentEmailListAsync();
    Task<List<string>> GetAllCustomerEmailsAsync();
    Task<Customer?> GetByEmailAsync(string email);
    Task<string?> GetEmailByPhoneAsync(string? phone);
    Task<Customer?> GetByEmailFullAsync(string email);
    Task<CustomerDto?> GetByEmailFullDtoAsync(string email);
    Task<List<CustomerEmailPhoneDto>> GetEmailPhoneAsync();
    Task BulkUpdateUnengagedCustomers(List<string> emails, bool clear);
    Task<Customer?> MailSentErrorAsync(SendGridEmailStatus sendGridEmailStatus);
    Task<List<Customer>> UnsubscribeByEmailAsync(string email, int? campaignId, DateTime? timeOfUnsubscribe = null);
    Task<List<Customer>> GetAllRawAsync(bool checkOnlyDenmark = false, bool skippAllCheck = false);
    Task<List<Customer>> GetAllWithoutCheckAsync(int partnerId);
    Task<int> UnsubscribedCustomers(int campaignId);
    Task<int> UnsubscribedCustomersByCampaignIds(List<int> campaignIds);
    Task<int> UnsubscribedCustomers(DateTime lookBackDay);
    Task<List<Unsubscribe>> GetUnsubscribedCustomersPeriod(DateTime from);
    Task<Dictionary<DateTime, long>> UnsubscribedCustomers(DateTime from, DateTime to, List<int> campaignIds);
    Task RemoveDuplicatesAudience(int partnerId);
    Task<Customer?> GetByPartnerGuid(string partnerGuid);
    Task AddContactPartner(Customer customer);
    Task UpdateContactPartner(Customer customer);
    Task MerchantRecommendationsM(List<MerchantRecommendationsMl> merchantRecommendationsMls);
    Task SaveCustomerDb();
    Task UpdateOpenRate(List<MessageDto> messages);
    Task<CustomerPaginationDto> GetPaginationAsync(PaginationSearchDto paginationSearchDto, bool forceUpdate = false);
    Task<List<string[]>> GetImport0Async(FileDto fileDto);
    Task<int> GetImport1Async(CustomerImportDto customerImportDto);

    // Exposures
    Task<List<CustomerExposureMetric>> GetCustomerExposureMetrics(string? customerEmail, DateTime? from, DateTime? to,
        int? merchantId);

    Task<MerchantRelevanceCustomerDto> GetAnonymousContact(string email);
    Task<MerchantRelevanceCustomerDto?> GetCustomerForMerchantRelevance(string email);
    Task<List<MerchantRelevanceCustomerDto>> GetCustomersForMerchantRelevance(List<string> emails);
    
    
    Task<List<CustomerForInvoiceWorkerDto>> GetCustomersForInvoiceWorkerAsync(int partnerId);
    Task<Segment?> GetSegmentByIdAsync(int segmentId);
    Task<List<CustomersForJsonDto>> GetCustomersForJsonFile();
    Task<int> GetPartnerIdByEmailAsync(string email);
}