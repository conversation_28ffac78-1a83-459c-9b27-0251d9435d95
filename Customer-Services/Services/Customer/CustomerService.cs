using System.Data;
using System.Text;
using Audience_Service.Models.ModelsDto;
using Audience.Models;
using AutoMapper;
using Customer_Services.Models.ModelsDal.Customer;
using Discount_Services.Models.ModelsDal.Discount;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Shared.Dto;
using Shared.Dto.Customer.Pagination;
using Shared.Elastic.CampaignMailClick;
using Shared.Elastic.CampaignMailOpen;
using Shared.Elastic.DiscountOpen;
using Shared.Elastic.ShopProductsDisplays;
using Shared.Elastic.ShopProductsInteracts;
using Shared.Elastic.ShopProductsRedirects;
using Shared.Models;
using Shared.Models.Customer;
using Shared.Models.Message;
using Shared.Services.Cache;
using Shared.Services.Partner;
using CustomerServices.Models.Models;

namespace Audience.Services.Audience;

public class CustomerService(
    CustomerDbContextTracking customerDbContext,
    IMemoryCache memoryCache,
    IElasticCampaignMailOpenService elasticCampaignMailOpenService,
    IElasticCampaignMailClickService elasticCampaignMailClickService,
    IElasticDiscountOpenService elasticDiscountOpenService,
    IElasticShopProductDisplaysService elasticShopProductDisplaysService,
    IElasticShopProductInteractsService elasticShopProductInteractsService,
    IElasticShopProductRedirectsService elasticShopProductRedirectsService,
    IPartnerContext partnerContext,
    ICacheService cacheService)
    : ICustomerService
{
    public async Task<List<Customer>> GetAllAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var cacheKey = $"AudienceService_GetAllAsync_{partnerId}";
        if (partnerId == 52876)
        {
            await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(12);
                return await customerDbContext.Customers
                    .Where(a => a.Country == "Denmark" && a.FkPartnerId == partnerId)
                    .OrderByDescending(a => a.PartnerProduct == "PAID_TIER_DK_20000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_15000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_10000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_8000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_6000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_4000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_2000")
                    .ThenByDescending(a => a.PartnerProduct == "LIGHT")
                    .ThenByDescending(a => a.PartnerProduct == "FREE_TIER_DK")
                    .ThenByDescending(a => a.CreatedDate)
                    .ToListAsync()
                    .ConfigureAwait(false);
            });
        }

        return await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(12);
            return await customerDbContext.Customers
                .Where(a => a.FkPartnerId == partnerId)
                .OrderByDescending(a => a.CreatedDate)
                .ToListAsync()
                .ConfigureAwait(false);
        }) ?? [];
    }

    public async Task<List<Customer>> GetAllActiveAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var cacheKey = $"AudienceService_GetAllActiveAsync_{partnerId}";
        if (partnerId == 52876)
        {
            return await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(12);
                return await customerDbContext.Customers
                    .Where(a => a.Country == "Denmark" && a.Active && a.FkPartnerId == partnerId)
                    .OrderByDescending(a => a.PartnerProduct == "PAID_TIER_DK_20000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_15000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_10000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_8000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_6000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_4000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_2000")
                    .ThenByDescending(a => a.PartnerProduct == "LIGHT")
                    .ThenByDescending(a => a.PartnerProduct == "FREE_TIER_DK")
                    .ThenByDescending(a => a.CreatedDate)
                    .ToListAsync()
                    .ConfigureAwait(false);
            }) ?? [];
        }
        
        return await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(12);
            return await customerDbContext.Customers
                .Where(a => a.Active && a.FkPartnerId == partnerId)
                .OrderByDescending(a => a.CreatedDate)
                .ToListAsync()
                .ConfigureAwait(false);
        }) ?? [];
    }

    public async Task<List<Customer>> GetAllConsentAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var cacheKey = $"AudienceService_GetAllConsentAsync_{partnerId}";
        if (partnerId == 52876)
        {
            // TODO - Fix this by "Deleting" all Non Danish customers from the database from ViaBill
            return (await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(12);
                return await customerDbContext.Customers
                    .Include(a => a.CustomerMailErrors)
                    .Where(a => a.MarketingStatus && a.Active == true && a.CustomerMailErrors.Count < 3 &&
                                a.Country == "Denmark" && a.FkPartnerId == partnerId)
                    .OrderByDescending(a => a.PartnerProduct == "PAID_TIER_DK_20000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_15000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_10000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_8000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_6000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_4000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_2000")
                    .ThenByDescending(a => a.PartnerProduct == "LIGHT")
                    .ThenByDescending(a => a.PartnerProduct == "FREE_TIER_DK")
                    .ThenByDescending(a => a.CreatedDate)
                    .ToListAsync()
                    .ConfigureAwait(false);
            }))!;
        }

        return (await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(12);
            return await customerDbContext.Customers
                .Include(a => a.CustomerMailErrors)
                .Where(a => a.MarketingStatus && a.Active == true && a.CustomerMailErrors.Count < 3 &&
                            a.FkPartnerId == partnerId && 
                            (a.OpenRate > 0 || // Include customers who have opened emails
                             (!a.Email.ToLower().EndsWith("@gmail.com") &&
                              !a.Email.ToLower().EndsWith("@outlook.com") &&
                              !a.Email.ToLower().EndsWith("@hotmail.com") &&
                              !a.Email.ToLower().EndsWith("@live.com") &&
                              !a.Email.ToLower().EndsWith("@msn.com"))))
                .OrderByDescending(a => a.CreatedDate)
                .ToListAsync()
                .ConfigureAwait(false);
        }))!;
    }

    public async Task<List<CustomerForCampaignsDto>> GetAllWithConsentForCampaignsAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var cacheKey = $"AudienceService_GetAllWithConsentForCampaignsAsync_{partnerId}";
        if (partnerId == 52876)
        {
            return await memoryCache.GetOrCreateAsync(cacheKey,
                async entry =>
                {
                    entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(12);
                    return await customerDbContext.Customers
                        .Where(a => a.MarketingStatus && a.Active == true && a.Country == "Denmark" && a.FkPartnerId == partnerId)
                        .Select(a => new CustomerForCampaignsDto
                        {
                            Email = a.Email,
                            FirstName = a.FirstName,
                            LastName = a.LastName,
                            ZipCode = a.ZipCode,
                            MarketingStatus = a.MarketingStatus,
                            PhoneNumber = a.PhoneNumber
                        })
                        .ToListAsync()
                        .ConfigureAwait(false);
                }) ?? [];
        }
        
        return await memoryCache.GetOrCreateAsync(cacheKey,
            async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(12);
                
                // Get customers with filtering applied in the query
                var customers = await customerDbContext.Customers
                    .Where(a => a.MarketingStatus && a.Active == true && a.FkPartnerId == partnerId)
                    .Select(a => new CustomerForCampaignsDto
                    {
                        Email = a.Email,
                        FirstName = a.FirstName,
                        LastName = a.LastName,
                        ZipCode = a.ZipCode,
                        MarketingStatus = a.MarketingStatus,
                        PhoneNumber = a.PhoneNumber
                    })
                    .ToListAsync()
                    .ConfigureAwait(false);
                
                return customers;
            }) ?? [];
    }



    public async Task<List<string>> GetAllConsentEmailListAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var cacheKey = $"AudienceService_GetAllConsentListAsync_{partnerId}";
        if (partnerId == 52876)
        {
            return await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(12);
                return await customerDbContext.Customers
                    .Include(a => a.CustomerMailErrors)
                    .Where(a => a.MarketingStatus && a.Active == true && a.CustomerMailErrors.Count < 3 &&
                                a.Country == "Denmark" && a.FkPartnerId == partnerId)
                    .OrderByDescending(a => a.PartnerProduct == "PAID_TIER_DK_20000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_15000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_10000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_8000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_6000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_4000")
                    .ThenByDescending(a => a.PartnerProduct == "PAID_TIER_DK_2000")
                    .ThenByDescending(a => a.PartnerProduct == "LIGHT")
                    .ThenByDescending(a => a.PartnerProduct == "FREE_TIER_DK")
                    .ThenByDescending(a => a.CreatedDate)
                    .Select(a => a.Email)
                    .ToListAsync()
                    .ConfigureAwait(false);
            }) ?? [];
        }

        return await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(12);
            return await customerDbContext.Customers
                .Include(a => a.CustomerMailErrors)
                .Where(a => a.MarketingStatus && a.Active == true && a.CustomerMailErrors.Count < 3 && a.FkPartnerId == partnerId)
                .OrderByDescending(a => a.CreatedDate)
                .Select(a => a.Email)
                .ToListAsync()
                .ConfigureAwait(false);
        }) ?? [];
    }


    public async Task<List<string>> GetAllCustomerEmailsAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var customers = await memoryCache.GetOrCreateAsync($"CustomerService_GetAllCustomerEmailsAsync_{partnerId}", async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(12);
            return await customerDbContext.Customers
                .Where(a => a.Active == true && a.FkPartnerId == partnerId && (partnerId != 52876 || a.Country == "Denmark"))
                .Select(a => a.Email)
                .ToListAsync()
                .ConfigureAwait(false);
        });
        return customers!;
    }


    public async Task<Customer?> GetByEmailAsync(string email)
    {
        var partnerId = partnerContext.PartnerId;
        var cacheKey = $"CustomerService_GetByEmailAsync_{email}";
        email = email.Trim().ToLowerInvariant();
        return await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(4);
            return await customerDbContext.Customers
                .FirstOrDefaultAsync(a => a.Email == email && a.FkPartnerId == partnerId)
                .ConfigureAwait(false);
        });
    }

    public async Task<string?> GetEmailByPhoneAsync(string? phone)
    {
        if (phone == null)
        {
            return null;
        }

        var customer = await customerDbContext.Customers
            .AsNoTracking()
            .FirstOrDefaultAsync(a => a.PhoneNumber == phone)
            .ConfigureAwait(false);

        return customer?.Email;
    }

    public async Task<Customer?> GetByEmailFullAsync(string email)
    {
        var partnerId = partnerContext.PartnerId;
        email = email.Trim().ToLowerInvariant();
        var cacheKey = $"CustomerService_GetByEmailFullAsync_{email}_{partnerId}";;
        return await cacheService.GetDataWithCacheLockAsync(
            cacheKey,
            async () =>
            {
                return await customerDbContext.Customers
                    .Include(a => a.CustomerExposures)
                    .Include(a => a.CustomerMeta)
                    .FirstOrDefaultAsync(a => a.Email == email && a.FkPartnerId == partnerId)
                    .ConfigureAwait(false);
            },
            TimeSpan.FromHours(5),
            TimeSpan.FromHours(8));
    }

    public async Task<CustomerDto?> GetByEmailFullDtoAsync(string email)
    {
        var customer = await GetByEmailFullAsync(email);
        var config = new MapperConfiguration(cfg =>
        {
            cfg.CreateMap<Customer, CustomerDto>().ReverseMap();
            cfg.CreateMap<CustomerExposure, CustomerExposureDto>().ReverseMap();
            cfg.CreateMap<CustomerMetum, CustomerMetumDto>().ReverseMap();
        });
        var mapper = config.CreateMapper();
        return mapper.Map<Customer?, CustomerDto?>(customer);
    }

    public async Task<List<CustomerEmailPhoneDto>> GetEmailPhoneAsync()
    {
        var partnerId = partnerContext.PartnerId;
        var cacheKey = $"AudienceService_GetEmailPhoneAsync_{partnerId}";
        var customers = await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(8);
            return await customerDbContext.Customers
                .Include(a => a.CustomerMailErrors)
                .Where(a => partnerId != 52876 || a.Country == "Denmark")
                .Select(
                    myEntity => new CustomerEmailPhoneDto()
                    {
                        Email = myEntity.Email,
                        PhoneNumber = myEntity.PhoneNumber
                    }).ToListAsync();
        });
        return customers!;
    }

    public async Task BulkUpdateUnengagedCustomers(List<string> emails, bool clear)
    {
        if (emails.Count == 0)
            return;

        var partnerId = partnerContext.PartnerId;
        var customersToUpdate = await customerDbContext.Customers
            .Where(c => emails.Contains(c.Email) && c.Active && c.FkPartnerId == partnerId)
            .ToListAsync();

        if (clear)
        {
            customersToUpdate.ForEach(c => c.MissedOpenMails = 0);
        }
        else
        {
            customersToUpdate.ForEach(c => c.MissedOpenMails += 1);
        }

        customerDbContext.Customers.UpdateRange(customersToUpdate);
        await customerDbContext.SaveChangesAsync();
    }


    public async Task<Customer?> MailSentErrorAsync(SendGridEmailStatus sendGridEmailStatus)
    {
        var partnerId = partnerContext.PartnerId;
        var customers = await customerDbContext.Customers
            .Include(a => a.CustomerMailErrors)
            .Where(a => a.Email == sendGridEmailStatus.Email && a.FkPartnerId == partnerId).ToListAsync();
        foreach (var customer in customers)
        {
            var newer = DateTime.UtcNow.AddDays(-3);
            if (!customer.CustomerMailErrors.Any(a => a.CreatedDate > newer))
            {
                var contactMailError = new CustomerMailError
                {
                    FkCustomerId = customer.Id,
                    CreatedDate = DateTime.UtcNow,
                    Error = sendGridEmailStatus.Status,
                    Reason = sendGridEmailStatus.Reason
                };
                customerDbContext.Add(contactMailError);
            }

            await customerDbContext.SaveChangesAsync().ConfigureAwait(false);
        }

        return customers.FirstOrDefault();
    }

    public async Task<List<Customer>> UnsubscribeByEmailAsync(string email, int? campaignId,
        DateTime? timeOfUnsubscribe = null)
    {
        var partnerId = partnerContext.PartnerId;
        var customers = await customerDbContext.Customers.Where(a => a.Email == email && a.FkPartnerId == partnerId).ToListAsync();
        if (customers.Count == 0)
        {
            customerDbContext.Unsubscribes.Add(new Unsubscribe()
            {
                Email = email,
                FkCampaignId = campaignId,
                UnsubscribedAt = timeOfUnsubscribe ?? DateTime.UtcNow
            });
            await customerDbContext.SaveChangesAsync();
        }

        foreach (var customer in customers)
        {
            customer.MarketingStatus = false;
            customerDbContext.Customers.Update(customer);
            customerDbContext.Unsubscribes.Add(new Unsubscribe()
            {
                Email = email,
                FkCampaignId = campaignId,
                UnsubscribedAt = timeOfUnsubscribe ?? DateTime.Now
            });
            await customerDbContext.SaveChangesAsync();
        }

        return customers;
    }

    public async Task<List<Customer>> GetAllRawAsync(bool checkOnlyDenmark = false, bool skippAllCheck = false)
    {
        var partnerId = partnerContext.PartnerId;
        if (skippAllCheck)
        {
            if(checkOnlyDenmark)
                return await customerDbContext.Customers.Where(a => a.Country == "Denmark" && a.FkPartnerId == partnerId).ToListAsync();
            
            return await customerDbContext.Customers.Where(a => a.FkPartnerId == partnerId).ToListAsync();
        }
        

        if(checkOnlyDenmark)
            return await customerDbContext.Customers
                .Include(a => a.CustomerMailErrors)
                .Where(a => a.CustomerMailErrors.Count < 3 && a.Country == "Denmark" && a.FkPartnerId == partnerId)
                .ToListAsync();
        
        return await customerDbContext.Customers
            .Include(a => a.CustomerMailErrors)
            .Where(a => a.CustomerMailErrors.Count < 3 && a.FkPartnerId == partnerId)
            .ToListAsync();
        
    }

    public async Task<List<Customer>> GetAllWithoutCheckAsync(int partnerId)
    {
        return await customerDbContext.Customers
                        .Where(a => a.FkPartnerId == partnerId)
                        .ToListAsync();
    }

    public async Task<int> UnsubscribedCustomers(int campaignId)
    {
        return await customerDbContext.Unsubscribes.CountAsync(a => a.FkCampaignId == campaignId);
    }

    public async Task<int> UnsubscribedCustomersByCampaignIds(List<int> campaignIds)
    {
        return await customerDbContext.Unsubscribes.CountAsync(a => campaignIds.Contains(a.FkCampaignId ?? 0));
    }

    public async Task<int> UnsubscribedCustomers(DateTime lookBackDay)
    {
        return await customerDbContext.Unsubscribes.CountAsync(a => a.UnsubscribedAt > lookBackDay);
    }

    public async Task<List<Unsubscribe>> GetUnsubscribedCustomersPeriod(DateTime lookBackDay)
    {
        return await customerDbContext.Unsubscribes.Where(a => a.UnsubscribedAt > lookBackDay).ToListAsync();
    }

    public async Task<Dictionary<DateTime, long>> UnsubscribedCustomers(DateTime from, DateTime to, List<int> campaignIds)
    {
        var originalDict = await customerDbContext.Unsubscribes
            .Where(a => a.UnsubscribedAt >= from && a.UnsubscribedAt < to && campaignIds.Contains(a.FkCampaignId ?? 0))
            .Select(a => new {a.Email, UnsubscribedDate = a.UnsubscribedAt})
            .GroupBy(a => a.UnsubscribedDate)
            .Select(g => new {Date = g.Key, Count = g.Select(x => x.Email).Distinct().LongCount()})
            .ToDictionaryAsync(k => k.Date, v => v.Count);
        return originalDict.ToDictionary(
            kvp => kvp.Key,
            kvp => kvp.Value
        );
    }

    public async Task RemoveDuplicatesAudience(int partnerId)
    {
        var customers = customerDbContext.Customers
            .AsNoTracking()
            .AsEnumerable()
            .Where(a => a.FkPartnerId == partnerId)
            .GroupBy(s => new {s.PartnerGuid})
            .SelectMany(g => g.Skip(1))
            .ToList();

        if(customers.Count == 0)
            return;

        var customerIds = customers.Select(a => a.Id).ToList();
        var contactsErrors = new List<CustomerMailError>();

        int batchSize = 5000;
        int numberOfBatches = (customerIds.Count + batchSize - 1) / batchSize;

        for (int batchNumber = 0; batchNumber < numberOfBatches; batchNumber++)
        {
            var batchIds = customerIds.Skip(batchNumber * batchSize).Take(batchSize).ToList();
            var batchErrors = await customerDbContext.CustomerMailErrors
                .Where(a => batchIds.Contains(a.FkCustomerId))
                .ToListAsync();
            contactsErrors.AddRange(batchErrors);
        }

        if(contactsErrors.Count > 0) {
            customerDbContext.CustomerMailErrors.RemoveRange(contactsErrors);
            await customerDbContext.SaveChangesAsync();
        }
            
        customerDbContext.ChangeTracker.Clear();
        customerDbContext.Customers.RemoveRange(customers);
        await customerDbContext.SaveChangesAsync();
    }

    public async Task<Customer?> GetByPartnerGuid(string partnerGuid)
    {
        return await customerDbContext.Customers.FirstOrDefaultAsync(
            a => a.PartnerGuid == partnerGuid);
    }

    public async Task AddContactPartner(Customer contact)
    {
        await customerDbContext.AddAsync(contact);
    }

    public async Task UpdateContactPartner(Customer contact)
    {
        customerDbContext.Update(contact);
    }

    public async Task MerchantRecommendationsM(List<MerchantRecommendationsMl> merchantRecommendationsMlsAll)
    {
        var customers = await GetAllActiveAsync();
        var customerDict = customers
            .GroupBy(c => c.Email)
            .Select(g => g.First())
            .ToDictionary(c => c.Email, c => c);

        var now = DateTime.UtcNow;
        const int batchSizeProduct = 25000;
        int totalBatches = (int) Math.Ceiling((double) merchantRecommendationsMlsAll.Count / batchSizeProduct);

        DataTable customerMetaTable = new DataTable();
        customerMetaTable.Columns.Add("CustomerId", typeof(long));
        customerMetaTable.Columns.Add("MerchantId", typeof(long));
        customerMetaTable.Columns.Add("CustomerMetaTypeId", typeof(int));
        customerMetaTable.Columns.Add("Active", typeof(int));
        customerMetaTable.Columns.Add("Value", typeof(string));
        customerMetaTable.Columns.Add("LastModifiedDate", typeof(DateTime));

        for (int i = 0, batchNumber = 1; i <= merchantRecommendationsMlsAll.Count; i += batchSizeProduct, batchNumber++)
        {
            Console.WriteLine($"\rProcessing batch {batchNumber} of {totalBatches}...");
            customerMetaTable.Rows.Clear();
            //Get batch to update
            var merchantRecommendationsMls = merchantRecommendationsMlsAll.Skip(i).Take(batchSizeProduct).ToList();
            foreach (var merchantRecommendationsMl in merchantRecommendationsMls)
            {
                if (customerDict.TryGetValue(merchantRecommendationsMl.email, out var customer))
                {
                    var recommendations = string.Join(",", merchantRecommendationsMl.recommendations.Take(50));
                    customerMetaTable.Rows.Add(customer.Id, null, 1, 1, recommendations, now);
                }
            }

            if (customerMetaTable.Rows.Count > 0)
            {
                using (var command = customerDbContext.Database.GetDbConnection().CreateCommand())
                {
                    command.CommandTimeout = 1200;
                    command.CommandText = "[customer].[MetaNonMerchant_Import]";
                    command.CommandType = CommandType.StoredProcedure;

                    var parameter = command.CreateParameter();
                    parameter.ParameterName = "@meta";
                    parameter.Value = customerMetaTable;
                    command.Parameters.Add(parameter);
                    
                    await customerDbContext.Database.OpenConnectionAsync();
                    try
                    {
                        await command.ExecuteNonQueryAsync();
                    }
                    finally
                    {
                        await customerDbContext.Database.CloseConnectionAsync();
                    }
                }
            }
        }
    }

    public async Task SaveCustomerDb()
    {
        await customerDbContext.SaveChangesAsync();
    }

    public async Task UpdateOpenRate(List<MessageDto> messagesDtos)
    {
        //https://app.clickup.com/t/86byx3bja
        var customers = await GetAllActiveAsync();

        // Faster look up
        var messageLookup = messagesDtos
            .GroupBy(m => m.Email)
            .ToDictionary(g => g.Key, g => g.ToList());

        foreach (var customer in customers)
        {
            if (!messageLookup.TryGetValue(customer.Email, out var messages))
            {
                // If no messages found, default to 100% openRate
                if (customer.OpenRate != 100)
                {
                    customer.OpenRate = 100;
                }
            }
            else
            {
                // Calculate openRate
                var openRate = ((double) messages.Count(m => m.FirstOpen != null) / messages.Count) * 100;
                var rate = (byte) Math.Ceiling(openRate);
                if (customer.OpenRate != rate)
                {
                    customer.OpenRate = rate;
                }
            }
        }

        await customerDbContext.SaveChangesAsync();
    }

    public async Task<CustomerPaginationDto> GetPaginationAsync(PaginationSearchDto paginationSearchDto,
        bool forceUpdate = false)
    {
        var response = new CustomerPaginationDto
        {
            Customers = new List<CustomerPaginationDataDto>()
        };
        var audiences = await GetAllAsync();
        var size = paginationSearchDto.Size * paginationSearchDto.Page + paginationSearchDto.Size;

        //Search
        if (paginationSearchDto.Search != "")
        {
            audiences = audiences.Where(a =>
                a.FirstName.Contains(paginationSearchDto.Search) || a.LastName.Contains(paginationSearchDto.Search) ||
                a.Email.Contains(paginationSearchDto.Search) ||
                (a.PartnerProduct != null && a.PartnerProduct.Contains(paginationSearchDto.Search))).ToList();
        }

        //Filter
        foreach (var filter in paginationSearchDto.Filters)
        {
            if (filter.Value != null)
            {
                switch (filter.Field)
                {
                    case "marketingStatus":
                        audiences = audiences.Where(a => a.MarketingStatus == Convert.ToBoolean(filter.Value)).ToList();
                        break;
                    case "gender":
                        audiences = audiences.Where(a => a.Gender.ToLowerInvariant() == filter.Value.ToLowerInvariant())
                            .ToList();
                        break;
                }
            }
        }

        //Sort
        switch (paginationSearchDto.SortName)
        {
            case "firstName":
                audiences = paginationSearchDto.SortOrder == "asc"
                    ? audiences.OrderBy(a => a.FirstName).ToList()
                    : audiences.OrderByDescending(a => a.FirstName).ToList();
                break;
            case "lastName":
                audiences = paginationSearchDto.SortOrder == "asc"
                    ? audiences.OrderBy(a => a.LastName).ToList()
                    : audiences.OrderByDescending(a => a.LastName).ToList();
                break;
            case "marketingStatus":
                audiences = paginationSearchDto.SortOrder == "asc"
                    ? audiences.OrderBy(a => a.MarketingStatus).ToList()
                    : audiences.OrderByDescending(a => a.MarketingStatus).ToList();
                break;
            case "gender":
                audiences = paginationSearchDto.SortOrder == "asc"
                    ? audiences.OrderBy(a => a.Gender).ToList()
                    : audiences.OrderByDescending(a => a.Gender).ToList();
                break;
            case "age":
                audiences = paginationSearchDto.SortOrder == "asc"
                    ? audiences.OrderBy(a => a.Age).ToList()
                    : audiences.OrderByDescending(a => a.Age).ToList();
                break;
            case "email":
                audiences = paginationSearchDto.SortOrder == "asc"
                    ? audiences.OrderBy(a => a.Email).ToList()
                    : audiences.OrderByDescending(a => a.Email).ToList();
                break;
            case "product":
                audiences = paginationSearchDto.SortOrder == "asc"
                    ? audiences.OrderBy(a => a.PartnerProduct).ToList()
                    : audiences.OrderByDescending(a => a.PartnerProduct).ToList();
                break;
            case "openRate":
                audiences = paginationSearchDto.SortOrder == "asc"
                    ? audiences.OrderBy(a => a.OpenRate).ToList()
                    : audiences.OrderByDescending(a => a.OpenRate).ToList();
                break;
        }

        var audienceToReturn = audiences.Skip(paginationSearchDto.Size * paginationSearchDto.Page)
            .Take(paginationSearchDto.Size).ToList();
        foreach (var audience in audienceToReturn)
        {
            response.Customers.Add(new CustomerPaginationDataDto
            {
                FirstName = audience.FirstName,
                LastName = audience.LastName,
                MarketingStatus = audience.MarketingStatus,
                Gender = audience.Gender,
                Age = audience.Age,
                Email = audience.Email,
                Product = audience.PartnerProduct ?? "Unknown",
                OpenRate = audience.OpenRate
            });
        }

        response.HasPreviousPage = paginationSearchDto.Page != 0;
        response.HasNextPage = size < audiences.Count;
        response.Size = audiences.Count;
        return response;
    }

    public async Task<List<string[]>> GetImport0Async(FileDto fileDto)
    {
        byte[] csvBytes = Convert.FromBase64String(fileDto.Data);
        string csvData = Encoding.UTF8.GetString(csvBytes);

        var records = ParseCsvData(csvData);
        return records;
    }

    public async Task<int> GetImport1Async(CustomerImportDto customerImportDto)
    {
        byte[] csvBytes = Convert.FromBase64String(customerImportDto.Data);
        string csvData = Encoding.UTF8.GetString(csvBytes);
        var records = ParseCsvData(csvData);
        //Get fresh from db so cache don't fuck with duplicate emails
        var customers = await customerDbContext.Customers.ToListAsync();
        records = records.Skip(1).ToList();
        foreach (var record in records)
        {
            var values = new Dictionary<string, string>();
            for (int i = 0; i < record.Length; i++)
            {
                var value = record[i];
                var field = customerImportDto.Values[i];
                values.Add(field.value, value);
            }

            var email = values["email"];
            var customer = customers.FirstOrDefault(a => a.Email == email);
            if (customer == null)
            {
                customer = new Customer
                {
                    Active = true,
                    CreatedDate = DateTime.UtcNow,
                    LastModifiedDate = DateTime.UtcNow,
                    MissedOpenMails = 0,
                    Email = email,
                    PhoneNumber = "",
                    FirstName = "",
                    LastName = "",
                    MarketingStatus = false,
                    Gender = "",
                    Country = "",
                    OpenRate = 100,
                };
                customerDbContext.Add(customer);
            }

            foreach (var value in values)
            {
                switch (value.Key)
                {
                    case "firstName":
                        customer.FirstName = value.Value;
                        break;
                    case "lastName":
                        customer.LastName = value.Value;
                        break;
                    case "gender":
                        customer.Gender = value.Value;
                        break;
                    case "country":
                        customer.Country = value.Value;
                        break;
                    case "age":
                        Byte.TryParse(value.Value, out var age);
                        customer.Age = age;
                        break;
                    case "marketingStatus":
                        customer.MarketingStatus = Convert.ToBoolean(value.Value);
                        break;
                    case "product":
                        customer.FirstName = value.Value;
                        break;
                }
            }
        }

        await customerDbContext.SaveChangesAsync();
        return records.Count;
    }

    public async Task<List<CustomerExposureMetric>> GetCustomerExposureMetrics(string? customerEmail, DateTime? from,
        DateTime? to, int? merchantId)
    {
        var metrics = await memoryCache.GetOrCreateAsync(
            $"CustomerService_GetCustomerExposureMetrics_{merchantId}_{from}_{to}_{customerEmail}", async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(12);
                // Elastic query
                var campaignDisplayTask =
                    elasticCampaignMailOpenService.OpensData(from, to, customerEmail, merchantId);
                var campaignClicksTask =
                    elasticCampaignMailClickService.ClicksData(from, to, customerEmail, merchantId);
                var discountDisplayTask = elasticDiscountOpenService.OpensData(from, to, customerEmail, merchantId);
                var discountInteractTask =
                    elasticDiscountOpenService.OpensData(from, to, customerEmail, merchantId,
                        type: "discount_details_viewed");
                var discountRedirectTask =
                    elasticDiscountOpenService.OpensData(from, to, customerEmail, merchantId,
                        type: "discount_link_opened");
                var productDisplayTask =
                    elasticShopProductDisplaysService.DisplaysData(from, to, customerEmail, merchantId);
                var productInteractTask =
                    elasticShopProductInteractsService.InteractsData(from, to, customerEmail, merchantId);
                var productRedirectTask =
                    elasticShopProductRedirectsService.RedirectsData(from, to, customerEmail, merchantId);

                await Task.WhenAll(campaignDisplayTask, campaignClicksTask, discountDisplayTask,
                    discountInteractTask,
                    discountRedirectTask, productDisplayTask, productInteractTask, productRedirectTask);

                var campaignDisplay = await campaignDisplayTask;
                var campaignClicks = await campaignClicksTask;
                var discountDisplay = await discountDisplayTask;
                var discountInteract = await discountInteractTask;
                var discountRedirect = await discountRedirectTask;
                var productDisplay = await productDisplayTask;
                var productInteract = await productInteractTask;
                var productRedirect = await productRedirectTask;

                var exposureMetrics = new List<CustomerExposureMetric>();

                // Process the data and populate the metrics list
                // Campaigns
                exposureMetrics.AddRange(campaignDisplay.Select(d => new CustomerExposureMetric
                {
                    Channel = "Email", Date = d.EventObject.Created, ExposureMethod = "Display",
                    CustomerEmail = d.Customer.Email
                }));
                exposureMetrics.AddRange(campaignClicks.Select(d => new CustomerExposureMetric
                {
                    Channel = "Email", Date = d.EventObject.Created, ExposureMethod = "Redirect",
                    CustomerEmail = d.Customer.Email
                }));

                // Discounts
                exposureMetrics.AddRange(discountDisplay.Select(d => new CustomerExposureMetric
                {
                    Channel = "Discounts", Date = d.Event_received, ExposureMethod = "Display",
                    CustomerEmail = d.Customer.Email
                }));
                exposureMetrics.AddRange(discountInteract.Select(d => new CustomerExposureMetric
                {
                    Channel = "Discounts", Date = d.Event_received, ExposureMethod = "Interact",
                    CustomerEmail = d.Customer.Email
                }));
                exposureMetrics.AddRange(discountRedirect.Select(d => new CustomerExposureMetric
                {
                    Channel = "Discounts", Date = d.Event_received, ExposureMethod = "Redirect",
                    CustomerEmail = d.Customer.Email
                }));

                // Products
                exposureMetrics.AddRange(productDisplay.Select(d => new CustomerExposureMetric
                {
                    Channel = "Products", Date = d.Action_date, ExposureMethod = "Display",
                    CustomerEmail = d.Customer.Email
                }));
                exposureMetrics.AddRange(productInteract.Select(d => new CustomerExposureMetric
                {
                    Channel = "Products", Date = d.Action_date, ExposureMethod = "Interact",
                    CustomerEmail = d.Customer.Email
                }));
                exposureMetrics.AddRange(productRedirect.Select(d => new CustomerExposureMetric
                {
                    Channel = "Products", Date = d.Action_date, ExposureMethod = "Redirect",
                    CustomerEmail = d.Customer.Email
                }));
                return exposureMetrics;
            });

        return metrics!;
    }

    List<string[]> ParseCsvData(string csvData)
    {
        var records = new List<string[]>();

        using (var reader = new StringReader(csvData))
        {
            string line;
            bool firstLine = true;
            int expectedFieldCount = 0;

            while ((line = reader.ReadLine()) != null)
            {
                // Split the line by comma and handle quoted fields
                var fields = SplitCsvLine(line);

                // Check if it's the first line
                if (firstLine)
                {
                    expectedFieldCount = fields.Length;
                    firstLine = false;
                }

                // Validate the length of fields
                if (fields.Length == expectedFieldCount)
                {
                    records.Add(fields);
                }
            }
        }

        return records;
    }

    string[] SplitCsvLine(string line)
    {
        var fields = new List<string>();
        var field = new StringBuilder();
        bool inQuotes = false;

        for (int i = 0; i < line.Length; i++)
        {
            char currentChar = line[i];

            if (inQuotes)
            {
                if (currentChar == '"')
                {
                    // Check for escaped quote
                    if (i + 1 < line.Length && line[i + 1] == '"')
                    {
                        field.Append('"');
                        i++; // Skip the next quote
                    }
                    else
                    {
                        inQuotes = false;
                    }
                }
                else
                {
                    field.Append(currentChar);
                }
            }
            else
            {
                if (currentChar == '"')
                {
                    inQuotes = true;
                }
                else if (currentChar == ',' || currentChar == ';')
                {
                    fields.Add(field.ToString());
                    field.Clear();
                }
                else
                {
                    field.Append(currentChar);
                }
            }
        }

        fields.Add(field.ToString());
        return fields.ToArray();
    }

    public async Task<MerchantRelevanceCustomerDto> GetAnonymousContact(string email)
    {
        return new MerchantRelevanceCustomerDto()
        {
            Email = email,
            Age = 0,
            CustomerExposures = [],
            IsAnonymous = true
        };
    }

    public async Task<MerchantRelevanceCustomerDto?> GetCustomerForMerchantRelevance(string email)
    {
        var now = DateTime.UtcNow;

        var cacheKey = $"CustomerService_GetCustomerForMerchantRelevance_{email}_{partnerContext.PartnerId}";
        var cachedCustomer = await memoryCache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(12);
            return await customerDbContext.Customers
                .AsNoTracking()
                .Where(a => a.Email == email && a.FkPartnerId == partnerContext.PartnerId)
                .Select(a => new MerchantRelevanceCustomerDto
                {
                    Id = a.Id,
                    PartnerGuid = a.PartnerGuid ?? string.Empty,
                    Email = a.Email,
                    Gender = a.Gender,
                    Age = a.Age,
                    CustomerExposures = a.CustomerExposures
                        .Where(exp => exp.ExposureEnd > now && exp.Active)
                        .Select(exp => new CustomerExposureDto
                        {
                            ExposureEnd = exp.ExposureEnd,
                            MerchantId = exp.FkMerchantId,
                        })
                        .ToList()
                })
                .FirstOrDefaultAsync();
        });
            

        return cachedCustomer;
    }

    
    
    public async Task<List<MerchantRelevanceCustomerDto>> GetCustomersForMerchantRelevance(List<string> emails)
    {
        var now = DateTime.UtcNow;

        return await customerDbContext.Customers
            .AsNoTracking()
            .Where(a => emails.Contains(a.Email))
            .Select(a => new MerchantRelevanceCustomerDto
            {
                Id = a.Id,
                PartnerGuid = a.PartnerGuid ?? string.Empty,
                Email = a.Email,
                Gender = a.Gender,
                Age = a.Age,
                CustomerExposures = a.CustomerExposures
                    .Where(exp => exp.ExposureEnd > now && exp.Active)
                    .Select(exp => new CustomerExposureDto
                    {
                        ExposureEnd = exp.ExposureEnd,
                        MerchantId = exp.FkMerchantId,
                    })
                    .ToList()
            })
            .ToListAsync()
            .ConfigureAwait(false);
    }


    public async Task<List<CustomerForInvoiceWorkerDto>> GetCustomersForInvoiceWorkerAsync(int partnerId)
    {
        //var partnerId = partnerContext.PartnerId;
        return await customerDbContext.Customers
            .AsNoTracking()
            .Where(a => a.FkPartnerId == partnerId)
            .Select(a => new CustomerForInvoiceWorkerDto
            {
                Email = a.Email,
                PhoneNumber = a.PhoneNumber,
                MissedOpenMails = a.MissedOpenMails,
                MarketingStatus = a.MarketingStatus,
                Age = a.Age ?? 0,
                Gender = a.Gender
            })
            .ToListAsync()
            .ConfigureAwait(false);
    }
    
    public async Task<Segment?> GetSegmentByIdAsync(int segmentId)
    {
        return await customerDbContext.Segments
            .AsNoTracking()
            .FirstOrDefaultAsync(a => a.Id == segmentId)
            .ConfigureAwait(false);
    }

    public async Task<List<CustomersForJsonDto>> GetCustomersForJsonFile()
    {
        return await customerDbContext.Customers
            .AsNoTracking()
            .Where(a => a.FkPartnerId == partnerContext.PartnerId && a.Active && a.Country == "Denmark")
            .Select(a => new CustomersForJsonDto
            {
                Id = a.Id,
                Email = a.Email,
                FirstName = a.FirstName,
                LastName = a.LastName,
                ZipCode = a.ZipCode,
                Country = a.Country,
                MarketingStatus = a.MarketingStatus,
                PhoneNumber = a.PhoneNumber,
                Gender = a.Gender,
                Age = a.Age,
                PartnerId = a.FkPartnerId.ToString(),
            })
            .ToListAsync()
            .ConfigureAwait(false);
    }

    public async Task<int> GetPartnerIdByEmailAsync(string email)
    {
        return await customerDbContext.Customers
            .AsNoTracking()
            .Where(a => a.Email == email)
            .Select(a => a.FkPartnerId)
            .FirstOrDefaultAsync();
    }
}