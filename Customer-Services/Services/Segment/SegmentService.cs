using Customer_Services.Models.ModelsDal.Customer;
using Discount_Services.Models.ModelsDal.Discount;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Shared.Dto;
using Shared.Dto.Customer.Pagination;
using Shared.Dto.Segment.Pagination;
using Shared.Elastic.CampaignMailClick;
using Shared.Elastic.CampaignMailOpen;
using Shared.Elastic.DiscountOpen;
using Shared.Elastic.Elastic;
using Shared.Models;
using Shared.Services.Cache;
using Shared.Services.Partner;

namespace Audience.Services.Audience;

public class SegmentService(
    CustomerDbContextTracking customerDbContext,
    IMemoryCache memoryCache,
    ICacheService cacheService,
    IElasticCampaignMailOpenService elasticCampaignMailOpenService,
    IElasticCampaignMailClickService elasticCampaignMailClickService,
    IElasticDiscountOpenService elasticDiscountOpenService,
    IElasticService elasticService,
    ICustomerService customerService,
    IPartnerContext partnerContext)
    : ISegmentService
{
    private readonly ICacheService _cacheService = cacheService;
    private readonly IElasticService _elasticService = elasticService;
    private readonly IElasticCampaignMailOpenService _elasticCampaignMailOpenService = elasticCampaignMailOpenService;
    private readonly IElasticCampaignMailClickService _elasticCampaignMailClickService = elasticCampaignMailClickService;
    private readonly IElasticDiscountOpenService _elasticDiscountOpenService = elasticDiscountOpenService;

    public async Task<Segment> GetAsync(int id)
    {
        var segment = await customerDbContext.Segments
            .Include(a => a.SegmentGroups.Where(b => b.Active))
            .ThenInclude(a => a.SegmentValues.Where(b => b.Active))
            .FirstAsync(a => a.Id == id);

        segment.SegmentGroups = segment.SegmentGroups.Where(a => a.FkSegmentGroupId == null).ToList();
        return segment;
    }

    public Task<List<Segment>> GetAllAsync()
    {
        var partnerId = partnerContext.PartnerId;
        return customerDbContext.Segments.Where(a => a.Active && a.FkPartnerId == partnerId).ToListAsync();
    }

    private async Task<List<Segment>> GetAllWithGroupsAsync()
    {
        var partnerId = partnerContext.PartnerId;
        /*var cacheKey = $"AllSegmentsWithGroups_{partnerId}";
        if (memoryCache.TryGetValue(cacheKey, out List<Segment> cachedResult))
        {
            return cachedResult;
        }*/

        var result = await customerDbContext.Segments
            .Include(a => a.SegmentGroups.Where(b => b.Active))
            .ThenInclude(a => a.SegmentValues.Where(b => b.Active))
            .Where(a => a.Active && a.FkPartnerId == partnerId).ToListAsync();

        //memoryCache.Set(cacheKey, result, TimeSpan.FromMinutes(30));
        return result;
    }

    public async Task<Segment> CreateAsync(Segment segment)
    {
        var partnerId = partnerContext.PartnerId;
        segment.FkPartnerId = partnerId;

        using var transaction = await customerDbContext.Database.BeginTransactionAsync();
        try
        {
            // Set up all the navigation properties correctly
            SetupSegmentNavigationProperties(segment);

            // Add the segment with all its related entities
            customerDbContext.Segments.Add(segment);
            
            // Save all changes at once - EF will handle the order and foreign keys
            await customerDbContext.SaveChangesAsync();
            await transaction.CommitAsync();

            return segment;
        }
        catch (Exception)
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    private void SetupSegmentNavigationProperties(Segment segment)
    {
        // Set up top-level segment groups
        foreach (var segmentGroup in segment.SegmentGroups)
        {
            // Set navigation property - EF will handle the foreign key
            segmentGroup.FkSegment = segment;
            segmentGroup.FkSegmentGroupId = null; // Top-level group
            
            // Set up segment values for this group
            foreach (var segmentValue in segmentGroup.SegmentValues)
            {
                segmentValue.FkSegmentGroup = segmentGroup;
            }

            // Set up nested segment groups
            if (segmentGroup.InverseFkSegmentGroup?.Any() == true)
            {
                foreach (var nestedGroup in segmentGroup.InverseFkSegmentGroup)
                {
                    // Set navigation properties - EF will handle the foreign keys
                    nestedGroup.FkSegment = segment;
                    nestedGroup.FkSegmentGroup = segmentGroup;
                    
                    // Set up segment values for nested group
                    foreach (var segmentValue in nestedGroup.SegmentValues)
                    {
                        segmentValue.FkSegmentGroup = nestedGroup;
                    }
                }
            }
        }
    }

    public async Task<Segment> UpdateAsync(Segment segment)
    {
        var partnerId = partnerContext.PartnerId;
        segment.FkPartnerId = partnerId;

        // Clear change tracker to avoid tracking conflicts
        customerDbContext.ChangeTracker.Clear();

        // Get existing segment with all related data using AsNoTracking to avoid conflicts
        var existingSegment = await customerDbContext.Segments
            .AsNoTracking()
            .Include(s => s.SegmentGroups)
            .ThenInclude(sg => sg.SegmentValues)
            .Include(s => s.SegmentGroups)
            .ThenInclude(sg => sg.InverseFkSegmentGroup)
            .ThenInclude(isg => isg.SegmentValues)
            .FirstOrDefaultAsync(s => s.Id == segment.Id);

        if (existingSegment == null && segment.Id != 0)
        {
            throw new InvalidOperationException($"Segment with ID {segment.Id} not found.");
        }

        using var transaction = await customerDbContext.Database.BeginTransactionAsync();
        try
        {
            // Update main segment
            if (segment.Id == 0)
            {
                customerDbContext.Segments.Add(segment);
            }
            else
            {
                customerDbContext.Segments.Update(segment);
            }

            await customerDbContext.SaveChangesAsync();

            // Handle segment groups
            await UpdateSegmentGroupsAsync(segment, existingSegment);

            // Handle inverse segment groups (nested groups) for all parent groups
            await UpdateAllInverseSegmentGroupsAsync(segment, existingSegment);

            await customerDbContext.SaveChangesAsync();
            await transaction.CommitAsync();

            return segment;
        }
        catch (Exception)
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    private async Task UpdateSegmentGroupsAsync(Segment segment, Segment? existingSegment)
    {
        var existingGroupIds = existingSegment?.SegmentGroups?
            .Where(sg => sg.FkSegmentGroupId == null) // Only top-level groups
            .Select(sg => sg.Id)
            .ToHashSet() ?? [];

        foreach (var segmentGroup in segment.SegmentGroups)
        {
            segmentGroup.FkSegmentId = segment.Id;
            segmentGroup.FkSegmentGroupId = null; // Ensure this is a top-level group

            if (segmentGroup.Id == 0)
            {
                // New segment group
                customerDbContext.SegmentGroups.Add(segmentGroup);
                await customerDbContext.SaveChangesAsync(); // Save to get the ID
            }
            else
            {
                // Update existing segment group
                customerDbContext.SegmentGroups.Update(segmentGroup);
                existingGroupIds.Remove(segmentGroup.Id);
            }

            // Handle segment values
            await UpdateSegmentValuesAsync(segmentGroup, existingSegment?.SegmentGroups?.FirstOrDefault(sg => sg.Id == segmentGroup.Id));
        }

        // Remove deleted segment groups (this will cascade delete segment values and nested groups)
        if (existingGroupIds.Any())
        {
            var groupsToDelete = await customerDbContext.SegmentGroups
                .Where(sg => existingGroupIds.Contains(sg.Id))
                .ToListAsync();
            
            customerDbContext.SegmentGroups.RemoveRange(groupsToDelete);
        }
    }

    private async Task UpdateAllInverseSegmentGroupsAsync(Segment segment, Segment? existingSegment)
    {
        // Get all existing nested groups to track what should be deleted
        var allExistingNestedGroups = existingSegment?.SegmentGroups?
            .Where(sg => sg.FkSegmentGroupId != null)
            .ToList() ?? [];

        var processedNestedGroupIds = new HashSet<int>();

        foreach (var parentGroup in segment.SegmentGroups)
        {
            var existingParentGroup = existingSegment?.SegmentGroups?.FirstOrDefault(sg => sg.Id == parentGroup.Id);
            
            if (parentGroup.InverseFkSegmentGroup?.Any() == true)
            {
                var processedIds = await UpdateInverseSegmentGroupsAsync(parentGroup, segment.Id, existingParentGroup);
                foreach (var id in processedIds)
                {
                    processedNestedGroupIds.Add(id);
                }
            }
            else if (existingParentGroup?.InverseFkSegmentGroup?.Any() == true)
            {
                // If the parent group had nested groups before but not anymore, delete them
                var nestedGroupsToDelete = await customerDbContext.SegmentGroups
                    .Where(sg => sg.FkSegmentGroupId == parentGroup.Id)
                    .ToListAsync();
                
                customerDbContext.SegmentGroups.RemoveRange(nestedGroupsToDelete);
            }
        }

        // Clean up any nested groups that were not processed (orphaned)
        var orphanedNestedGroups = allExistingNestedGroups
            .Where(ng => !processedNestedGroupIds.Contains(ng.Id))
            .ToList();

        if (orphanedNestedGroups.Any())
        {
            customerDbContext.SegmentGroups.RemoveRange(orphanedNestedGroups);
        }
    }

    private async Task<HashSet<int>> UpdateInverseSegmentGroupsAsync(SegmentGroup parentGroup, int segmentId, SegmentGroup? existingParentGroup)
    {
        var processedNestedGroupIds = new HashSet<int>();
        var existingNestedGroupIds = existingParentGroup?.InverseFkSegmentGroup?
            .Select(isg => isg.Id)
            .ToHashSet() ?? [];

        foreach (var nestedGroup in parentGroup.InverseFkSegmentGroup)
        {
            nestedGroup.FkSegmentId = segmentId;
            nestedGroup.FkSegmentGroupId = parentGroup.Id;

            if (nestedGroup.Id == 0)
            {
                // New nested group
                customerDbContext.SegmentGroups.Add(nestedGroup);
                await customerDbContext.SaveChangesAsync(); // Save to get ID for new groups
                processedNestedGroupIds.Add(nestedGroup.Id);
            }
            else
            {
                // Update existing nested group
                customerDbContext.SegmentGroups.Update(nestedGroup);
                existingNestedGroupIds.Remove(nestedGroup.Id);
                processedNestedGroupIds.Add(nestedGroup.Id);
            }

            // Handle segment values for nested groups
            var existingNestedGroup = existingParentGroup?.InverseFkSegmentGroup?.FirstOrDefault(isg => isg.Id == nestedGroup.Id);
            await UpdateSegmentValuesAsync(nestedGroup, existingNestedGroup);
        }

        // Remove deleted nested groups for this parent
        if (existingNestedGroupIds.Any())
        {
            var nestedGroupsToDelete = await customerDbContext.SegmentGroups
                .Where(sg => existingNestedGroupIds.Contains(sg.Id))
                .ToListAsync();
            
            customerDbContext.SegmentGroups.RemoveRange(nestedGroupsToDelete);
        }

        return processedNestedGroupIds;
    }

    private async Task UpdateSegmentValuesAsync(SegmentGroup segmentGroup, SegmentGroup? existingGroup)
    {
        var existingValueIds = existingGroup?.SegmentValues?.Select(sv => sv.Id).ToHashSet() ?? new HashSet<int>();

        foreach (var segmentValue in segmentGroup.SegmentValues)
        {
            segmentValue.FkSegmentGroupId = segmentGroup.Id;

            if (segmentValue.Id == 0)
            {
                // New segment value
                customerDbContext.SegmentValues.Add(segmentValue);
            }
            else
            {
                // Verify the segment value exists before updating
                var exists = await customerDbContext.SegmentValues
                    .AsNoTracking()
                    .AnyAsync(sv => sv.Id == segmentValue.Id);

                if (exists)
                {
                    customerDbContext.SegmentValues.Update(segmentValue);
                    existingValueIds.Remove(segmentValue.Id);
                }
                else
                {
                    // If not found, treat as new
                    segmentValue.Id = 0;
                    customerDbContext.SegmentValues.Add(segmentValue);
                }
            }
        }

        // Remove deleted segment values
        if (existingValueIds.Any())
        {
            var valuesToDelete = await customerDbContext.SegmentValues
                .Where(sv => existingValueIds.Contains(sv.Id))
                .ToListAsync();
            
            customerDbContext.SegmentValues.RemoveRange(valuesToDelete);
        }
    }

    public async Task<List<string>> CalculateAsync(Segment segment)
    {
        if (segment.SegmentGroups.Count == 0)
        {
            segment.SegmentGroups = await customerDbContext.SegmentGroups
                .Include(a => a.SegmentValues.Where(b => b.Active))
                .Where(a => a.FkSegmentId == segment.Id && a.Active).ToListAsync();
        }
        else
        {
            segment.SegmentGroups = segment.SegmentGroups.Where(a => a.FkSegmentGroupId == null && a.Active).ToList();
            foreach (var segmentGroup in segment.SegmentGroups)
            {
                segmentGroup.SegmentValues = segmentGroup.SegmentValues.Where(sv => sv.Active).ToList();
            }
        }

        var customers = await customerService.GetAllConsentAsync();
        customers = customers.DistinctBy(a => a.Email).ToList();

        var customersToReturn = new List<Customer>();
        foreach (var segmentGroupMain in segment.SegmentGroups)
        {
            var splitCustomersMain = new List<List<Customer>>();

            //Find all audience
            var conditionFunctions = new Dictionary<string, Func<Customer, SegmentValue, bool>>
            {
                //Gender
                {"gender_is", (customer, segmentValue) => customer.Gender == segmentValue.Value},
                {"gender_isNot", (customer, segmentValue) => customer.Gender == segmentValue.Value},

                //Age
                {
                    "age_isBetween",
                    (customer, segmentValue) => int.TryParse(segmentValue.Value, out var from) &&
                                                int.TryParse(segmentValue.Value1, out var to) && customer.Age >= from &&
                                                customer.Age <= to
                },
                {
                    "age_isExactly",
                    (customer, segmentValue) => int.TryParse(segmentValue.Value, out var from) && customer.Age == from
                },
                {
                    "age_isNotExactly",
                    (customer, segmentValue) => int.TryParse(segmentValue.Value, out var from) && customer.Age != from
                },
                {
                    "age_isGreaterThan",
                    (customer, segmentValue) => int.TryParse(segmentValue.Value, out var from) && customer.Age > from
                },
                {
                    "age_isGreaterThanOrEqual",
                    (customer, segmentValue) => int.TryParse(segmentValue.Value, out var from) && customer.Age >= from
                },
                {
                    "age_isLessThan",
                    (customer, segmentValue) => int.TryParse(segmentValue.Value, out var from) && customer.Age < from
                },
                {
                    "age_isLessThanOrEqual",
                    (customer, segmentValue) => int.TryParse(segmentValue.Value, out var from) && customer.Age <= from
                },
                //OpenRate
                {
                    "openRate_isBetween",
                    (customer, segmentValue) => int.TryParse(segmentValue.Value, out var from) &&
                                                int.TryParse(segmentValue.Value1, out var to) &&
                                                customer.OpenRate >= from && customer.OpenRate <= to
                },
                {
                    "openRate_isExactly",
                    (customer, segmentValue) =>
                        int.TryParse(segmentValue.Value, out var from) && customer.OpenRate == from
                },
                {
                    "openRate_isNotExactly",
                    (customer, segmentValue) =>
                        int.TryParse(segmentValue.Value, out var from) && customer.OpenRate != from
                },
                {
                    "openRate_isGreaterThan",
                    (customer, segmentValue) =>
                        int.TryParse(segmentValue.Value, out var from) && customer.OpenRate > from
                },
                {
                    "openRate_isGreaterThanOrEqual",
                    (customer, segmentValue) =>
                        int.TryParse(segmentValue.Value, out var from) && customer.OpenRate >= from
                },
                {
                    "openRate_isLessThan",
                    (customer, segmentValue) =>
                        int.TryParse(segmentValue.Value, out var from) && customer.OpenRate < from
                },
                {
                    "openRate_isLessThanOrEqual",
                    (customer, segmentValue) =>
                        int.TryParse(segmentValue.Value, out var from) && customer.OpenRate <= from
                }
            };

            //Main values
            foreach (var segmentValue in segmentGroupMain.SegmentValues)
            {
                var key = $"{segmentValue.Type}_{segmentValue.Condition}";
                if (conditionFunctions.TryGetValue(key, out var conditionFunc))
                {
                    var filteredCustomers = customers.Where(customer => conditionFunc(customer, segmentValue)).ToList();
                    splitCustomersMain.Add(filteredCustomers);
                }
            }

            //Group handling
            foreach (var segmentGroup in segmentGroupMain.InverseFkSegmentGroup)
            {
                var splittedeCustomers = new List<List<Customer>>();
                var customerToReturnGroup = new List<Customer>();
                foreach (var segmentValue in segmentGroup.SegmentValues)
                {
                    var key = $"{segmentValue.Type}_{segmentValue.Condition}";
                    if (conditionFunctions.TryGetValue(key, out var conditionFunc))
                    {
                        var filteredCustomers =
                            customers.Where(customer => conditionFunc(customer, segmentValue)).ToList();
                        splittedeCustomers.Add(filteredCustomers);
                    }
                }

                //Handle values in group
                if (segmentGroup.Type == "and")
                {
                    if (customerToReturnGroup.Count == 0)
                    {
                        customerToReturnGroup = customers;
                    }

                    foreach (var currentCustomers in splittedeCustomers)
                    {
                        customerToReturnGroup = customerToReturnGroup.Intersect(currentCustomers).ToList();
                    }
                }
                else if (segmentGroup.Type == "or")
                {
                    foreach (var currentCustomers in splittedeCustomers)
                    {
                        customerToReturnGroup.AddRange(currentCustomers);
                    }
                }
                else if (segmentGroup.Type == "none")
                {
                    if (customerToReturnGroup.Count == 0)
                    {
                        customerToReturnGroup = customers;
                    }

                    foreach (var currentCustomers in splittedeCustomers)
                    {
                        customerToReturnGroup = customerToReturnGroup.Except(currentCustomers).ToList();
                    }
                }

                splitCustomersMain.Add(customerToReturnGroup);
            }


            //validate all together to find the correct data
            if (segmentGroupMain.Type == "and")
            {
                if (customersToReturn.Count == 0)
                {
                    customersToReturn = customers;
                }

                foreach (var currentCustomers in splitCustomersMain)
                {
                    customersToReturn = customersToReturn.Intersect(currentCustomers).ToList();
                }
            }
            else if (segmentGroupMain.Type == "or")
            {
                foreach (var currentCustomers in splitCustomersMain)
                {
                    customersToReturn.AddRange(currentCustomers);
                }
            }
            else if (segmentGroupMain.Type == "none")
            {
                if (customersToReturn.Count == 0)
                {
                    customersToReturn = customers;
                }

                foreach (var currentCustomers in splitCustomersMain)
                {
                    customersToReturn = customersToReturn.Except(currentCustomers).ToList();
                }
            }
        }

        var result = customersToReturn.DistinctBy(a => a.Email).Select(a => a.Email).ToList();
        
        return result;
    }

    public async Task<object?> DeleteAsync(int segmentId)
    {
        var segment = await customerDbContext.Segments.FindAsync(segmentId);
        if (segment == null)
        {
            return new ErrorDto("Segment not found");
        }

        segment.Active = false;
        await customerDbContext.SaveChangesAsync();
        return segment;
    }

    public async Task<List<string>> CalculateAsync(int segmentId)
    {
        var segment = await GetAsync(segmentId);
        return await CalculateAsync(segment);
    }

    public async Task<SegmentPaginationDto> GetPaginationAsync(PaginationSearchDto paginationSearchDto,
        bool forceUpdate = false)
    {
        var response = new SegmentPaginationDto
        {
            Segments = []
        };
        
        //var audiences = await GetAllAsync();
        var segments = await GetAllWithGroupsAsync();
        var size = paginationSearchDto.Size * paginationSearchDto.Page + paginationSearchDto.Size;

        //Search
        if (paginationSearchDto.Search != "")
        {
            segments = segments.Where(a =>
                a.Name.Contains(paginationSearchDto.Search)).ToList();
        }

        //Sort
        switch (paginationSearchDto.SortName)
        {
            case "name":
                segments = paginationSearchDto.SortOrder == "asc"
                    ? segments.OrderBy(a => a.Name).ToList()
                    : segments.OrderByDescending(a => a.Name).ToList();
                break;
        }

        var segmentsToReturn = segments.Skip(paginationSearchDto.Size * paginationSearchDto.Page)
            .Take(paginationSearchDto.Size).ToList();
        foreach (var segment in segmentsToReturn)
        {
            response.Segments.Add(new SegmentPaginationDataDto
            {
                Id = segment.Id,
                Name = segment.Name,
                AudienceSize = CalculateAsync(segment).Result.Count
            });
        }

        response.HasPreviousPage = paginationSearchDto.Page != 0;
        response.HasNextPage = size < segments.Count;
        response.Size = segments.Count;
        return response;
    }
}