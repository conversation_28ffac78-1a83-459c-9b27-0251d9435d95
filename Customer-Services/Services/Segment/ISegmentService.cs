using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Audience_Service.Models.ModelsDto;
using Audience.Models;
using Customer_Services.Models.ModelsDal.Customer;
using Shared.Dto;
using Shared.Dto.Customer.Pagination;
using Shared.Dto.MerchantScore;
using Shared.Models;
using Shared.Models.Customer;
using Shared.Models.Message;

namespace Audience.Services.Audience;

public interface ISegmentService
{
    Task<Segment> GetAsync(int id);
    Task<List<Segment>> GetAllAsync();
    Task<Segment> UpdateAsync(Segment segment);
    Task<List<string>> CalculateAsync(Segment segment);
    Task<List<string>> CalculateAsync(int segmentId);


    Task<SegmentPaginationDto> GetPaginationAsync(PaginationSearchDto paginationSearchDto, bool forceUpdate = false);
    Task<object?> DeleteAsync(int segmentId);
    Task<Segment> CreateAsync(Segment segment);


    /*Task<List<Customer>> GetAllAsync();
    Task<List<Customer>> GetAllActiveAsync();
    Task<List<Customer>> GetAllConsentAsync();
    Task<List<string>> GetAllConsentListAsync();
    Task<Customer?> GetByEmailAsync(string email);
    Task<Customer?> GetByEmailFullAsync(string email);
    Task<CustomerDto?> GetByEmailFullDtoAsync(string email);
    Task<List<string>> GetAllConsentedEngagedListAsync();
    Task<List<CustomerEmailPhoneDto>> GetEmailPhoneAsync();
    Task<List<Customer>> UpdateUnengagedCustomers(string email, bool clear);
    Task<Customer?> MailSentErrorAsync(SendGridEmailStatus sendGridEmailStatus);
    Task<List<Customer>> UnsubscribeByEmailAsync(string email, int? campaignId, DateTime? timeOfUnsubscribe = null);
    Task<List<Customer>> GetAllRawAsync(bool skippAllCheck = false);
    Task<List<Customer>> GetAllFromAllCountryNoCheckAsync();
    Task<int> UnsubscribedCustomers(int campaignId);
    Task<int> UnsubscribedCustomers(DateTime lookBackDay);
    Task<List<Unsubscribe>> GetUnsubscribedCustomersPeriod(DateTime from);
    Task<Dictionary<DateTime,long>> UnsubscribedCustomers(DateTime from, DateTime to, List<int> campaignIds);
    Task RemoveDuplicatesAudience();
    Task<Customer?> GetByPartnerGuid(string partnerGuid);
    Task AddContactViaBill(Customer customer);
    Task UpdateContactViaBill(Customer customer);
    Task MerchantRecommendationsM(List<MerchantRecommendationsMl> merchantRecommendationsMls);
    Task SaveCustomerDb();
    Task UpdateOpenRate(List<MessageDto> messages);
    Task<CustomerPaginationDto> GetPaginationAsync(PaginationSearchDto paginationSearchDto, bool forceUpdate = false);
    Task<List<string[]>> GetImport0Async(FileDto fileDto);
    Task<int> GetImport1Async(CustomerImportDto customerImportDto);*/
}