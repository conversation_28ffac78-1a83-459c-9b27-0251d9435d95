using Audience.Services.Audience;
using AutoMapper;
using Customer_Services.Models.ModelsDal.Customer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Dto;
using Shared.Dto.Webshop;
using Shared.Models;
using Shared.Services;
using ILogger = Serilog.ILogger;

namespace Endpoints.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class SegmentController : ControllerBase
{
    private readonly ILogger _logger;
    private readonly ISegmentService _segmentService;
    private readonly IMapper _mapper;

    public SegmentController(ILogger logger, ISegmentService segmentService)
    {
        _logger = logger;
        _segmentService = segmentService;
        var config = new MapperConfiguration(cfg =>
        {
            //Webshop
            cfg.CreateMap<Segment, SegmentDto>().ReverseMap();
            cfg.CreateMap<SegmentGroup, SegmentGroupDto>().ReverseMap();
            cfg.CreateMap<SegmentValue, SegmentValueDto>().ReverseMap();
        });
        _mapper = config.CreateMapper();
    }

    [HttpGet]
    [Route("{segmentId}")]
    public async Task<IActionResult> Get(int segmentId)
    {
        try
        {
            return Ok(await _segmentService.GetAsync(segmentId));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting get");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetAll()
    {
        try
        {
            return Ok(await _segmentService.GetAllAsync());
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting get");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [Route("pagination")]
    public async Task<IActionResult> SegmentationPagination(PaginationSearchDto paginationSearchDto)
    {
        try
        {
            return Ok(await _segmentService.GetPaginationAsync(paginationSearchDto));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting pagination");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    public async Task<IActionResult> CreateSegment(SegmentDto segmentDto)
    {
        try
        {
            var segment = _mapper.Map<SegmentDto, Segment>(segmentDto);
            return Ok(await _segmentService.CreateAsync(segment));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while creating segment");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPut]
    public async Task<IActionResult> UpdateSegment(SegmentDto segmentDto)
    {
        try
        {
            var segment = _mapper.Map<SegmentDto, Segment>(segmentDto);
            return Ok(await _segmentService.UpdateAsync(segment));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while updating segment");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpDelete]
    [Route("{segmentId}")]
    public async Task<IActionResult> DeleteSegment(int segmentId)
    {
        try
        {
            return Ok(await _segmentService.DeleteAsync(segmentId));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while deleting segment");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPut]
    [Route("calculate")]
    public async Task<IActionResult> CalculateSizeSegment(SegmentDto segmentDto)
    {
        try
        {
            var segment = _mapper.Map<SegmentDto, Segment>(segmentDto);
            var size = await _segmentService.CalculateAsync(segment);
            return Ok(size);
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting pagination");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPut]
    [Route("calculate/number")]
    public async Task<IActionResult> CalculateSizeIntSegment(SegmentDto segmentDto)
    {
        try
        {
            var segment = _mapper.Map<SegmentDto, Segment>(segmentDto);
            var size = await _segmentService.CalculateAsync(segment);
            return Ok(size.Count);
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting pagination");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("calculate/{segmentId}")]
    public async Task<IActionResult> CalculateSizeSegment(int segmentId)
    {
        try
        {
            var size = await _segmentService.CalculateAsync(segmentId);
            return Ok(size);
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting pagination");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("calculate/{segmentId}/number")]
    public async Task<IActionResult> CalculateSizeNumberSegment(int segmentId)
    {
        try
        {
            var size = await _segmentService.CalculateAsync(segmentId);
            return Ok(size.Count);
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting pagination");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }
}