using System.Text.Json;
using Audience.Services.Audience;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ILogger = Serilog.ILogger;

namespace Endpoints.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class CustomersController(ILogger logger, ICustomerService customerService) : ControllerBase
{
    private readonly ILogger _logger = logger;

    [HttpGet]
    [Route("export/json")]
    [AllowAnonymous]
    public async Task<IActionResult> ExportCustomersAsJson()
    {
        var customers = await customerService.GetCustomersForJsonFile();
        var json = JsonSerializer.Serialize(customers);
        var fileName = $"customers_{DateTime.Now.ToString("yyyyMMddHHmmss")}.json";
        string docPath =
            Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
        var filePath = Path.Combine(docPath, fileName);
        System.IO.File.WriteAllText(filePath, json);
        return Ok();
    }
}