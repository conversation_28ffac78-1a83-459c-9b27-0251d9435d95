#region

using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using Discount_Services.Services.Discounts;
using Integration.Models.Elastic.Order;
using Marlin_OS_Integration_API.Models.Order;
using Merchant_Services.Models.ModelsDal.Merchant;
using Message_Services.Services.Message;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using PartnerPortal_Services.Models;
using RabbitMQ.Client;
using Shared.Dto.Message;
using Shared.Dto.PartnerPortal;
using Shared.Dto.Webshop;
using Shared.Elastic.PartnerPortal;
using Shared.Models;
using Shared.Models.Merchant;
using Shared.Models.PartnerPortal;
using Shared.Services.Cache;
using Shared.Services.Setting;
using Statistics_Services.Services.Statistics;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

#endregion

namespace PartnerPortal_Services.Services.PartnerPortal;

public class PartnerPortalService(
    IMerchantService merchantService,
    IStatisticsService statisticsService,
    //IConnection rabbitConnection,
    [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud,
    ILogger logger,
    IConfiguration configuration,
    IMessageService messageService,
    ICacheService cacheService,
    ISettingService settingService)
    : IPartnerPortalService
{
    private string validIssuer = "ApiGateway";
    private string audience = "ApiGatewayAudience";
    private const string PartnerPortal = "PartnerPortal";

    /// <summary>
    /// Handles login requests
    /// </summary>
    /// <param name="loginDto"></param>
    /// <returns>Response with Result, User and Token if any</returns>
    public async Task<AuthenticationResponseDto?> LoginAsync(LoginDto loginDto)
    {
        var user = await merchantService.PartnerLogin(loginDto);
        if (user != null)
        {
            user.MerchantUsersRels = user.MerchantUsersRels.Where(a => a.FkMerchant.MerchantMeta.SingleOrDefault(meta =>
                meta.FkMerchantMetaTypeName == MerchantMetaTypeNames.DisablePartnerLogin)?.Value != "true").ToList();

            var authenticationModel = new AuthenticationResponseDto
            {
                IsAuthenticated = true,
                MaintenanceMode = user.MerchantUsersRels.Count == 0
            };
            try
            {
                var jwtSecurityToken = CreateJwtToken(user);
                authenticationModel.Token = new JwtSecurityTokenHandler().WriteToken(jwtSecurityToken);
            }
            catch (Exception ex)
            {
                logger.ForContext("service_name", GetType().Name).Warning(ex, "Error creating jwt login");
            }

            authenticationModel.Email = loginDto.Email;
            return authenticationModel;
        }

        return new AuthenticationResponseDto
        {
            IsAuthenticated = false
        };
    }

    public async Task<AuthenticationResponseDto> LoginAdminAsync(int merchantId, string email)
    {
        var merchant = await merchantService.GetByIdFullAsync(merchantId);
        var authenticationModel = new AuthenticationResponseDto
        {
            IsAuthenticated = true
        };
        try
        {
            var user = new User
            {
                Id = 1,
                Email = email,
                FirstName = "Valyrion",
                LastName = "Admin",
                MerchantUsersRels = new List<MerchantUsersRel>
                {
                    new()
                    {
                        FkMerchantId = merchant.Id,
                        FkUserGroupId = 2,
                        FkMerchant = new Merchant()
                        {
                            DisplayName = merchant.DisplayName,
                            Name = merchant.Name,
                            FkPartnerId = merchant.FkPartnerId, 
                            MerchantMeta = new List<MerchantMetum>
                            {
                                new()
                                {
                                    FkMerchantMetaTypeName = MerchantMetaTypeNames.PointOfContact,
                                    Value = "1"
                                },
                                new()
                                {
                                    FkMerchantMetaTypeName = MerchantMetaTypeNames.DisablePartnerLogin,
                                    Value = "false"
                                }
                            }
                        }
                    }
                }
            };
            var jwtSecurityToken = CreateJwtToken(user);
            authenticationModel.Token = new JwtSecurityTokenHandler().WriteToken(jwtSecurityToken);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Warning(ex, "Error creating jwt admin");
        }

        authenticationModel.Email = email;
        return authenticationModel;
    }
    
    public async Task<string> LoginShopifyAsync(string email)
    {
        var user = await merchantService.GetShopifyByEmailAsync(email);
        if (user != null)
        {
            return user.Url;
        }

        return "";
    }

    private JwtSecurityToken CreateJwtToken(User user)
    {
        var claims = new List<Claim>
        {
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(JwtRegisteredClaimNames.Email, user.Email),
            new Claim("firstName", user.FirstName ?? ""),
            new Claim("lastName", user.LastName ?? ""),
            new Claim("uid", user.Id.ToString()),

            new Claim(ClaimTypes.Role, "partner"),
        };
        foreach (var merchantUser in user.MerchantUsersRels)
        {
            var disablePartnerLogin = Convert.ToBoolean(merchantUser.FkMerchant.MerchantMeta
                .Single(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.DisablePartnerLogin).Value);
            claims.Add(new Claim(ClaimTypes.Role,
                $"partner||{merchantUser.FkMerchantId}||{merchantUser.FkUserGroupId}||{JsonSerializer.Serialize(new PartnerDataDto
                {
                    MerchantId = merchantUser.FkMerchantId,
                    PartnerId = merchantUser.FkMerchant.FkPartnerId,
                    UserGroupId = merchantUser.FkUserGroupId,
                    Name = merchantUser.FkMerchant.DisplayName,
                    ShopIsDisabled = disablePartnerLogin,
                })}"
            ));
        }

        var symmetricSecurityKey =
            new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["JwtSecretLogin"] ?? ""));
        var signingCredentials = new SigningCredentials(symmetricSecurityKey, SecurityAlgorithms.HmacSha256);
        var jwtSecurityToken = new JwtSecurityToken(
            validIssuer,
            audience,
            claims,
            expires: DateTime.UtcNow.AddMinutes(360),
            signingCredentials: signingCredentials);
        return jwtSecurityToken;
    }


    public JwtSecurityToken ValidateToken(string authToken)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var validationParameters = GetValidationParameters();

        SecurityToken validatedToken;
        tokenHandler.ValidateToken(authToken.Split(" ")[1], validationParameters, out validatedToken);
        return (JwtSecurityToken) validatedToken;
    }

    public async Task<DataResponseDto> GetMerchantShopIdentifier(int merchantId)
    {
        var merchant = await merchantService.GetByIdFullAsync(merchantId);
        if (merchant == null)
            return new DataResponseDto()
            {
                Success = false,
                Message = $"Merchant with Id: {merchantId} Not Found"
            };
        var shopIdentifier = await merchantService.FetchOrAddMerchantMeta(merchant,
            MerchantMetaTypeNames.ShopIdentifier, () => Guid.NewGuid().ToString());
        return new DataResponseDto()
        {
            Success = true,
            Data = new Dictionary<string, string> {{MerchantMetaTypeNames.ShopIdentifier, shopIdentifier}}
        };
    }

    public async Task<MerchantPartnerPortalDto> GetMerchantProfileForDashboardAsync(int merchantId)
    {
        var merchant = await merchantService.GetByIdFullAsync(merchantId);
        var pointOfContactString = merchant?.MerchantMeta
            .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.PointOfContact)?.Value ?? "1";
        var pointOfContact = string.IsNullOrEmpty(pointOfContactString) ? 1 : int.Parse(pointOfContactString);
        var isKeepMarketingFeeOnReturnsMerchant = merchant?.MerchantMeta
            .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.KeepMarketingFeeOnReturns)?.Value == "true";

        /*var validTypes = new HashSet<string>
        {
            MerchantTypes.Shopify,
            MerchantTypes.WooCommerce,
            MerchantTypes.DanDomain,
            MerchantTypes.DanDomainClassic,
            MerchantTypes.Magento
        };*/

        var isCustomer = merchant != null && merchant.MerchantMeta
            .Any(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer && a.EndDate == null);
        var toBeOnboarded = merchant is {Sync: null}
                            /*&& validTypes.Contains(merchant.Type)*/ && isCustomer;

        return new MerchantPartnerPortalDto
        {
            PointOfContact = pointOfContact,
            MerchantId = merchant?.Id ?? 0,
            PartnerId = merchant?.FkPartnerId ?? 0,
            CmsType = merchant?.Type ?? "",
            ApiKey = merchant?.MerchantMeta
                .FirstOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ApiKey)
                ?.Value,
            Name = merchant?.DisplayName ?? (merchant?.Name ?? ""),
            Url = merchant?.Url ?? "",
            IsKeepMarketingFeeOnReturnsMerchant = isKeepMarketingFeeOnReturnsMerchant,
            ToBeOnboarded = toBeOnboarded
        };
    }

    public async Task<MerchantProfile> GetMerchantProfileAsync(int merchantId)
    {
        var cacheKey = $"PartnerPortalService_GetMerchantProfileAsync_{merchantId}";
        var merchantProfile = await cacheService.GetData<MerchantProfile>(cacheKey);
        if (merchantProfile == null)
        {
            merchantProfile = await statisticsService.GetMerchantProfileAsync(merchantId)
                .ConfigureAwait(false);
            cacheService.SetData(cacheKey, merchantProfile, TimeSpan.FromHours(2));
        }

        return merchantProfile;
    }

    public async Task<MerchantMonthlyPerformance> GetMerchantMonthlyPerformanceAsync(int merchantId, int month,
        int year)
    {
        var cacheKey = $"PartnerPortalService_GetMerchantProfileAsync_{merchantId}_{month}_{year}";
        var merchantMonthlyPerformance = await cacheService.GetData<MerchantMonthlyPerformance>(cacheKey);
        if (merchantMonthlyPerformance == null)
        {
            merchantMonthlyPerformance = await statisticsService.GetMerchantProfileMonthAsync(merchantId, month, year)
                .ConfigureAwait(false);
            cacheService.SetData(cacheKey, merchantMonthlyPerformance, TimeSpan.FromHours(2));
        }

        return merchantMonthlyPerformance;
    }

    public async Task<MerchantUserPartnerPortalDto> CrudUsers(MerchantUserPartnerPortalDto userDto, int merchantId)
    {
        var user = await merchantService.GetMerchantUserByEmailAsync(userDto.Email) ?? new User();
        var merchant = await merchantService.GetByIdAsync(merchantId);
        var newUser = user.Id == 0;
        var addedNewMerchant = false;
        user.Email = userDto.Email;
        user.FirstName = userDto.FirstName;
        user.LastName = userDto.LastName;
        var merchantUser = user.MerchantUsersRels.SingleOrDefault(a => a.FkMerchantId == merchantId);
        if (merchantUser == null)
        {
            user.MerchantUsersRels.Add(new MerchantUsersRel
            {
                FkMerchantId = merchantId,
                Active = true,
                FkUserGroupId = userDto.FkUserGroupId,
            });
            addedNewMerchant = true;
        }
        else
        {
            if (merchantUser.Active == false && userDto.Active)
            {
                addedNewMerchant = true;
            }

            merchantUser.Active = userDto.Active;
            merchantUser.FkUserGroupId = userDto.FkUserGroupId;
        }

        if (newUser)
        {
            user.Password = Guid.NewGuid().ToString();
            user.Active = true;
        }

        await merchantService.CrudMerchantUserAsync(user);

        if (!newUser && !addedNewMerchant) return userDto;

        var serviceEmailDto = new ServiceEmailDto
        {
            // TODO - Change to Dynamic PartnerId instead of Hardcoded
            //SenderName = await _messageService.GetSettingValueByName("SenderName", PartnerPortal) ?? "Partner",
            SenderName = await settingService.GetSettingValueAsync(52876, "SenderName"),
            SenderEmail = await settingService.GetSettingValueAsync(52876, "SenderEmail"),
            Recipient = new EmailRecipientDto
            {
                Email = user.Email ?? "",
                FirstName = user.FirstName ?? "",
                LastName = user.LastName ?? ""
            },
            Parameters = new Dictionary<string, string>()
            {
                {"WebshopName", merchant!.DisplayName}
            }
        };

        if (addedNewMerchant && !newUser)
        {
            // TODO - Change to Dynamic CampaignId instead of Hardcoded
            serviceEmailDto.CampaignId = Convert.ToInt32(
                await settingService.GetSettingValueAsync(52876, "AddedToDashboardCampaignId"));
            serviceEmailDto.Parameters.Add("PartnerPortalUrl",
                await settingService.GetSettingValueAsync(52876, "BaseUrl"));

            await messageService.SendServiceEmailAsync(serviceEmailDto);
        }

        if (newUser)
        {
            var token = await merchantService.CreateMerchantUsersTokenAsync(user);

            // TODO - Change to Dynamic CampaignId instead of Hardcoded
            serviceEmailDto.CampaignId = Convert.ToInt32(
                await settingService.GetSettingValueAsync(52876, "ActivateAccountCampaignId"));
            serviceEmailDto.Parameters.Add("ActivateAccountUrl",
                await settingService.GetSettingValueAsync(52876, "ActivateAccountUrl"));
            serviceEmailDto.Parameters.Add("ActivateAccountToken", token.Token);

            await messageService.SendServiceEmailAsync(serviceEmailDto);
        }

        return userDto;
    }

    public async Task CreateEvent(PartnerPortalEventDto partnerPortalEvent, string email, string userId)
    {
        var user = await merchantService.GetByIdAsync(Convert.ToInt32(partnerPortalEvent.MerchantId))
            .ConfigureAwait(false);
        var elasticPartnerPortalEvent = new ElasticPartnerPortalEvent
        {
            Created_date = DateTime.UtcNow,
            user = new ElasticPartnerPortalEventUser()
            {
                email = email,
                id = userId
            },
            Event = new ElasticPartnerPortalEventEvent
            {
                type = partnerPortalEvent.EventType,
                action = partnerPortalEvent.Action,
                Webshop_id = partnerPortalEvent.MerchantId.ToString(),
                Webshop_name = user.Name
            }
        };

        var json = JsonSerializer.Serialize(elasticPartnerPortalEvent);
        var actionBody = Encoding.UTF8.GetBytes(json);
        try
        {
            using (var publishChannel = rabbitConnectionCloud.CreateModel())
            {
                publishChannel.ConfirmSelect();

                publishChannel.BasicPublish(exchange: "log",
                    routingKey: "audit_merchantdashboard_event",
                    basicProperties: null,
                    body: actionBody);

                publishChannel.WaitForConfirmsOrDie(new TimeSpan(0, 0, 10));
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error creating event for partner portal with RabbitMQ {Json}", json);
        }
    }

    public async Task UpdatePassword(MerchantUserPartnerPortalPasswordDto webshopsUserPartnerPortalPasswordDto)
    {
        await merchantService.UpdateMerchantUserPasswordAsync(webshopsUserPartnerPortalPasswordDto);
    }

    public async Task<User?> GetUsersByToken(string token)
    {
        return await merchantService.GetMerchantUsersByTokenAsync(token);
    }

    public async Task ResetPasswordEmail(string email)
    {
        var user = await merchantService.GetMerchantUserByEmailAsync(email);
        if (user != null && !string.IsNullOrEmpty(user.Email))
        {
            var partnerId = user.MerchantUsersRels.First(a => a.Active).FkMerchant.FkPartnerId;
            var token = await merchantService.CreateMerchantUsersTokenAsync(user);
            var serviceEmailDto = new ServiceEmailDto
            {
                // TODO - Change to Dynamic CampaignId instead of Hardcoded
                CampaignId =
                    Convert.ToInt32(
                        await settingService.GetSettingValueAsync(partnerId, "ResetPasswordCampaignId")),
                SenderName = await settingService.GetSettingValueAsync(partnerId, "SenderName"),
                SenderEmail = await settingService.GetSettingValueAsync(partnerId, "SenderEmail"),
                Recipient = new EmailRecipientDto
                {
                    Email = user.Email ?? "",
                    LastName = "",
                    FirstName = ""
                },
                Parameters = new Dictionary<string, string>()
                {
                    {
                        "ResetPasswordUrl",
                        await settingService.GetSettingValueAsync(partnerId, "ResetPasswordUrl")
                    },
                    {"ResetPasswordToken", token.Token},
                }
            };

            await messageService.SendServiceEmailAsync(serviceEmailDto);
        }
    }

    /*public async Task ResetPassword(WebshopsUserPartnerPortalDto userDto, int merchantId)
    {
        var user = await _merchantService.GetWebShopUserByIdAsync(userDto.Id) ??
                   new User { FkWebshopId = merchantId };
        if (user.FkWebshopId == merchantId && !string.IsNullOrEmpty(user.Email))
        {
            var token = await _merchantService.CreateWebShopUsersTokenAsync(user);
            var serviceEmailDto = new ServiceEmailDto
            {
                CampaignId = Convert.ToInt32(await _messageService.GetSettingValueByName("ResetPasswordCampaignId", "PartnerPortal")),
                SenderName = await _messageService.GetSettingValueByName("SenderName", "PartnerPortal") ?? "Partner",
                SenderEmail = await _messageService.GetSettingValueByName("SenderEmail", "PartnerPortal") ?? "",
                Recipient = new EmailRecipientDto
                {
                    Email = user.Email ?? "",
                    //Email = "<EMAIL>",
                    FirstName = user.FirstName ?? "",
                    LastName = user.LastName ?? ""
                },
                Parameters = new Dictionary<string, string>()
                {
                    {"ResetPasswordUrl", await _messageService.GetSettingValueByName("ResetPasswordUrl", "PartnerPortal") ?? ""},
                    {"ResetPasswordToken", token.Token}
                }
            };

            await _messageService.SendServiceEmailAsync(serviceEmailDto);
        }
    }*/

    public async Task<List<User>> GetUsers(int websshopId)
    {
        return await merchantService.GetMerchantUserAsync(websshopId);
    }

    public async Task<List<UserGroup>> GetUsersGroup()
    {
        return await merchantService.GetMerchantUserGroupAsync();
    }

    private TokenValidationParameters GetValidationParameters()
    {
        return new TokenValidationParameters()
        {
            ValidateLifetime = false,
            ValidateAudience = false,
            ValidateIssuer = false,
            ValidIssuer = validIssuer,
            ValidAudience = audience,
            ValidAlgorithms = new List<string> {SecurityAlgorithms.HmacSha256},
            IssuerSigningKey =
                new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["JwtSecretLogin"] ?? ""))
        };
    }
}