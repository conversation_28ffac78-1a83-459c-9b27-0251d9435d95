#region

using System.IdentityModel.Tokens.Jwt;
using Merchant_Services.Models.ModelsDal.Merchant;
using PartnerPortal_Services.Models;
using Shared.Dto.PartnerPortal;
using Shared.Dto.Webshop;
using Shared.Models;
using Shared.Models.Merchant;
using Shared.Models.PartnerPortal;

#endregion

namespace PartnerPortal_Services.Services.PartnerPortal;

public interface IPartnerPortalService
{
    Task<AuthenticationResponseDto?> LoginAsync(LoginDto loginDto);
    Task<AuthenticationResponseDto> LoginAdminAsync(int merchantId, string email);
    Task<string> LoginShopifyAsync(string email);
    JwtSecurityToken ValidateToken(string authToken);
    Task<DataResponseDto> GetMerchantShopIdentifier(int merchantId);
    Task<MerchantPartnerPortalDto> GetMerchantProfileForDashboardAsync(int merchantId);
    Task<MerchantProfile> GetMerchantProfileAsync(int merchantId);
    Task<MerchantMonthlyPerformance> GetMerchantMonthlyPerformanceAsync(int merchantId, int month, int year);
    Task<MerchantUserPartnerPortalDto> CrudUsers(MerchantUserPartnerPortalDto user, int merchantId);
    Task CreateEvent(PartnerPortalEventDto partnerPortalEvent, string email, string userId);
    Task UpdatePassword(MerchantUserPartnerPortalPasswordDto webshopsUserPartnerPortalPasswordDto);
    Task<User?> GetUsersByToken(string token);
    Task ResetPasswordEmail(string email);

    Task<List<User>> GetUsers(int merchantId);

    //Task<List<WebShopsUsersGroup>> GetUsersGroup();
    Task<List<UserGroup>> GetUsersGroup();
}