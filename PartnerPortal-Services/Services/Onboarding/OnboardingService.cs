using Gateway_Services.Services.Onboarding;
using Gateway_Services.Services.Onboarding.Cms;
using Shared.Dto.Webshop;
using Shared.Models;
using Shared.Services.Notification;
using Shared.Services.Partner;
using Webshop.Webshop;

namespace PartnerPortal_Services.Services.Onboarding;

public class OnboardingService : IOnboardingService
{
    private readonly IMerchantService _merchantService;
    private readonly Dictionary<string, ICmsOnboardingService> _cmsServices;
    private readonly IEmailService _emailService;
    private readonly IPartnerContext _partnerContext;

    public OnboardingService(IEnumerable<ICmsOnboardingService> cmsServices, IMerchantService merchantService,
        IEmailService emailService, IPartnerContext partnerContext)
    {
        _merchantService = merchantService;
        _emailService = emailService;
        _cmsServices = cmsServices.ToDictionary(
            service => service.GetType().Name.Replace("OnboardingService", "").ToLower(),
            service => service
        );
        _partnerContext = partnerContext;
    }

    public async Task<DataResponseDto> ValidateOnboardingStep(MerchantPartnerPortalDto merchantProfile)
    {
        var cmsType = merchantProfile.CmsType.ToLower();
        if (_cmsServices.TryGetValue(cmsType, out var cmsService))
        {
            var validationResponse = await cmsService.ValidateStep(merchantProfile);
            if (merchantProfile.Step == merchantProfile.FinalStep && validationResponse.Success)
            {
                var onboardingCompletionResponse = await MarkOnboardingAsCompleted(merchantProfile);
                if (!onboardingCompletionResponse.Success)
                {
                    return onboardingCompletionResponse;
                }

                await SendSuccessfulOnboardingNotification(merchantProfile);
            }

            return validationResponse;
        }

        throw new NotSupportedException("Unsupported CMS type.");
    }

    private async Task SendSuccessfulOnboardingNotification(MerchantPartnerPortalDto merchantProfile)
    {
        string emailBody = $"""
                            <!DOCTYPE html>
                            <html lang='en'>
                            <head>
                                <meta charset='UTF-8'>
                                <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                                <title>Merchant Onboarding Successful</title>
                            </head>
                            <body>
                                <div class='container'>
                                    <div class='header'>
                                        <h2>Successful Merchant Onboarding!</h2>
                                    </div>
                                    <div class='content'>
                                        <p>Merchant: <strong>{merchantProfile.Name}</strong> with ID: <strong>{merchantProfile.MerchantId}</strong> has onboarded themselves!</p>
                                        <p>The Initial Synchronization will begin shortly, and you will be able to see their Potentials soon!</p>
                                    </div>
                                    <div class='footer'>
                                        <p>Best Regards,<br>Valyrion</p>
                                    </div>
                                </div>
                            </body>
                            </html>
                            """;

        var emails = new List<string> {"<EMAIL>", "<EMAIL>"};

        switch (merchantProfile.PartnerId)
        {
            case 52876:
                emails.Add("<EMAIL>");
                break;
            case 87317:
                emails.Add("<EMAIL>");
                emails.Add("<EMAIL>");
                emails.Add("<EMAIL>");
                break;
        }

        if (emailBody != "")
        {
            await _emailService.SendEmailAsync(emails, "Merchant Onboarding completed", emailBody);
        }
    }

    private async Task<DataResponseDto> MarkOnboardingAsCompleted(MerchantPartnerPortalDto merchantProfile)
    {
        var result = await _merchantService.ActivateSync(merchantProfile.MerchantId);
        return new DataResponseDto()
        {
            Success = result,
            Message = $"{(result ? "Successfully" : "Unsuccessfully")} Activated Synchronization For Merchant"
        };
    }
}