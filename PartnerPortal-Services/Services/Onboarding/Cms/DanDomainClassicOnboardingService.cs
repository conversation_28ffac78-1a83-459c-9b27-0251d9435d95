using Gateway_Services.Services.Onboarding.Cms;
using Shared.Dto.Webshop;
using Shared.Models;
using Shared.Models.Merchant;
using Webshop.Webshop;

namespace PartnerPortal_Services.Services.Onboarding.Cms;

public class DanDomainClassicOnboardingService(IMerchantService merchantService) : ICmsOnboardingService
{
    public async Task<DataResponseDto> ValidateStep(MerchantPartnerPortalDto merchantProfile)
    {
        switch (merchantProfile.Step)
        {
            case 1:
                var shopIdentifier = await ExtractShopIdentifier(merchantProfile.Url);
                if (!string.IsNullOrEmpty(shopIdentifier))
                {
                    return await ValidateShopIdentifier(merchantProfile, shopIdentifier);
                }

                return new DataResponseDto()
                {
                    Success = false,
                    Message = "The Shop Identifier was not found",
                    Data = new Dictionary<string, string> {{"shopIdentifierFound", "false"}}
                };
            case 2:
                if (merchantProfile.FinalStep == 2)
                {
                    return await ValidateShopScriptAsync(merchantProfile);
                }

                if (merchantProfile.CmsProperties.TryGetValue("danDomainClassicShopIdentifier",
                        out string danDomainClassicShopIdentifier))
                {
                    return await ValidateShopIdentifier(merchantProfile, danDomainClassicShopIdentifier);
                }

                return new DataResponseDto()
                {
                    Success = false,
                    Message = "Shop Identifier Missing"
                };
            case 3:
                /*return new DataResponseDto()
                {
                    Success = true,
                    Message = "No validation Needed"
                };*/
                return await ValidateShopScriptAsync(merchantProfile);
            default:
                return new DataResponseDto()
                {
                    Success = true,
                    Message = "No validation Needed"
                };
        }
    }

    private async Task<string> ExtractShopIdentifier(string merchantProfileUrl)
    {
        var shopIdentifier = string.Empty;
        var latestDanDomainClassicShops = await merchantService.GetLatestDanDomainClassicShops();

        //merchantProfileUrl = "https://luksusbaby.dk/shop/frontpage.html";
        if (latestDanDomainClassicShops == null) return shopIdentifier;
        var merchantHost = RemoveWww(new Uri(merchantProfileUrl).Host);

        using (HttpClient client = new HttpClient())
        {
            client.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

            foreach (var danDomainClassicShop in latestDanDomainClassicShops)
            {
                HttpResponseMessage response;
                try
                {
                    response = await client.GetAsync(danDomainClassicShop.Url);
                }
                catch
                {
                    continue;
                }

                var redirectHost = RemoveWww(response.RequestMessage.RequestUri.Host);
                if (merchantHost.Equals(redirectHost, StringComparison.OrdinalIgnoreCase))
                {
                    shopIdentifier = danDomainClassicShop.ShopIdentifier.ToString();
                    break;
                }

                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    string htmlContent = await response.Content.ReadAsStringAsync();
                    if (htmlContent.Contains(new Uri(merchantProfileUrl).Host))
                    {
                        shopIdentifier = danDomainClassicShop.ShopIdentifier.ToString();
                        break;
                    }
                }
            }
        }

        return shopIdentifier;
    }

    private string RemoveWww(string host)
    {
        return host.StartsWith("www.", StringComparison.OrdinalIgnoreCase) ? host.Substring(4) : host;
    }


    private async Task<DataResponseDto> ValidateShopIdentifier(MerchantPartnerPortalDto merchantProfile,
        string danDomainClassicShopIdentifier)
    {
        var shopId = Convert.ToInt64(danDomainClassicShopIdentifier);
        var webshop = await merchantService.GetByIdFullAsync(merchantProfile.MerchantId);
        if (webshop == null)
        {
            return new DataResponseDto()
            {
                Success = false,
                Message =
                    $"Webshop with ID: {merchantProfile.MerchantId} was not found - If this error persists, please contact ViaAds"
            };
        }

        var danDomainClassic = await merchantService.GetDanDomainClassicByIdentifierAsync(shopId);

        if (danDomainClassic == null)
        {
            return new DataResponseDto()
            {
                Success = false,
                Message =
                    $"DanDomain Classic Shop with Shop Identifier: {danDomainClassicShopIdentifier} was not found - check to see if app was installed correctly or the Shop Identifier was incorrect"
            };
        }

        var danDomainClassicIsClaimed =
            await merchantService.CheckIfDanDomainClassicIsClaimed(shopId, merchantProfile.MerchantId);

        if (danDomainClassicIsClaimed)
        {
            return new DataResponseDto()
            {
                Success = false,
                Message =
                    "DanDomain Classic Shop Identifier Already Claimed by someone else - make sure the inserted Shop Identifier is correct"
            };
        }

        await merchantService.AddOrUpdateMerchantMeta(webshop, MerchantMetaTypeNames.ApiKey, danDomainClassic.ApiKey);
        await merchantService.AddOrUpdateMerchantMeta(webshop, MerchantMetaTypeNames.ApiUrl, danDomainClassic.Url);

        var shopIdentifierValue = await merchantService.FetchOrAddMerchantMeta(webshop,
            MerchantMetaTypeNames.ShopIdentifier, () => Guid.NewGuid().ToString());

        await merchantService.UpdateMerchantAsync(webshop).ConfigureAwait(false);

        return new DataResponseDto()
        {
            Success = true,
            Message = "Onboarding Successful",
            Data = new Dictionary<string, string> {{MerchantMetaTypeNames.ShopIdentifier, shopIdentifierValue}}
        };
    }

    private async Task<DataResponseDto> ValidateShopScriptAsync(MerchantPartnerPortalDto merchantProfile)
    {
        var webshop = await merchantService.GetByIdFullAsync(merchantProfile.MerchantId);
        if (webshop == null)
        {
            return new DataResponseDto()
            {
                Success = false,
                Message =
                    $"Webshop with ID: {merchantProfile.MerchantId} was not found - If this error persists, please contact ViaAds"
            };
        }

        using (var httpClient = new HttpClient())
        {
            //webshop.Url = "https://8447651.shop4.webshop8.dk/shop/frontpage.html?BypassShopClosed=1";
            var response = await httpClient.GetStringAsync(webshop.Url);

            var htmlDocument = new HtmlAgilityPack.HtmlDocument();
            htmlDocument.LoadHtml(response);

            // Check if the script tag with the source 'danDomainClassic.min.js' exists
            var scriptNode = htmlDocument.DocumentNode.SelectSingleNode(
                "//script[contains(text(), 'https://files.viaads.dk/plugins/min/danDomainClassic.min.js')]");

            if (scriptNode == null)
            {
                return new DataResponseDto()
                {
                    Success = false,
                    Message = $"The Script was not found on the Webshop with url: {webshop.Url}"
                };
            }

            // Check if the ShopIdentifier has been set correctly within the script
            var shopConfig =
                htmlDocument.DocumentNode.SelectSingleNode("//script[contains(text(), 'window.ShopConfig =')]");

            if (shopConfig == null || !shopConfig.InnerText.Contains(
                    $"ShopIdentifier: '{webshop.MerchantMeta.SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ShopIdentifier)?.Value}'"))
            {
                return new DataResponseDto()
                {
                    Success = false,
                    Message = $"The Shop Identifier was incorrect - make sure this is set and is correct"
                };
            }
        }

        webshop.Sync = true;
        await merchantService.UpdateMerchantAsync(webshop).ConfigureAwait(false);

        return new DataResponseDto()
        {
            Success = true,
            Message = $"Script Successfully Inserted on Webshop"
        };
    }
}