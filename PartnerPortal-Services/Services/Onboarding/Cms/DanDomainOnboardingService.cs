using Gateway_Services.Services.Onboarding.Cms;
using Shared.Dto.Webshop;
using Shared.Models;
using Shared.Models.Merchant;
using Shared.WebServiceService;
using Webshop.Webshop;

namespace PartnerPortal_Services.Services.Onboarding.Cms;

public class DanDomainOnboardingService(IMerchantService merchantService) : ICmsOnboardingService
{
    public async Task<DataResponseDto> ValidateStep(MerchantPartnerPortalDto merchantProfile)
    {
        return merchantProfile.Step switch
        {
            1 => await ValidateApiUser(merchantProfile),
            2 => await ValidateHookTokenAsync(merchantProfile),
            3 => await ValidateShopScriptAsync(merchantProfile),
            _ => new DataResponseDto() {Success = true, Message = "No validation Needed"}
        };
    }

    private async Task<DataResponseDto> ValidateApiUser(MerchantPartnerPortalDto merchantProfile)
    {
        var username = merchantProfile.CmsProperties.SingleOrDefault(a => a.Key == "username").Value;
        var password = merchantProfile.CmsProperties.SingleOrDefault(a => a.Key == "password").Value;
        try
        {
            var webServicePortClient = new WebServicePortClient();
            webServicePortClient.InnerChannel.OperationTimeout = new TimeSpan(0, 30, 0);
            await webServicePortClient.Solution_ConnectAsync(username, password);
        }
        catch (Exception ex)
        {
            return new DataResponseDto()
            {
                Success = false,
                Message = "Api user credentials are incorrect"
            };
        }

        var merchant = await merchantService.GetByIdFullAsync(merchantProfile.MerchantId).ConfigureAwait(false);

        await merchantService.AddOrUpdateMerchantMeta(merchant, MerchantMetaTypeNames.ApiUserKey, username);
        await merchantService.AddOrUpdateMerchantMeta(merchant, MerchantMetaTypeNames.ApiUserSecret, password);

        merchant.Sync = true;
        await merchantService.UpdateMerchantAsync(merchant);

        return new DataResponseDto()
        {
            Success = true,
            Message = "Successfully validated API User"
        };
    }

    private async Task<DataResponseDto> ValidateHookTokenAsync(MerchantPartnerPortalDto merchantProfile)
    {
        // Validate the Hook Token if possible
        // Insert the Hook into the Webshop Meta
        var hookToken = merchantProfile.CmsProperties.SingleOrDefault(a => a.Key == "hookToken").Value;
        if (!Guid.TryParse(hookToken, out Guid parsedGuid))
        {
            {
                return new DataResponseDto()
                {
                    Success = false,
                    Message = "The token provided is in an incorrect format"
                };
            }
        }

        var webshop = await merchantService.GetByIdFullAsync(merchantProfile.MerchantId).ConfigureAwait(false);

        await merchantService.AddOrUpdateMerchantMeta(webshop, MerchantMetaTypeNames.ApiHookKey, hookToken);
        var shopIdentifierValue = await merchantService.FetchOrAddMerchantMeta(webshop,
            MerchantMetaTypeNames.ShopIdentifier, () => Guid.NewGuid().ToString());

        await merchantService.UpdateMerchantAsync(webshop).ConfigureAwait(false);

        return new DataResponseDto()
        {
            Success = true,
            Message = "Successfully validated Hook Token",
            Data = new Dictionary<string, string> {{MerchantMetaTypeNames.ShopIdentifier, shopIdentifierValue}}
        };
    }

    private async Task<DataResponseDto> ValidateShopScriptAsync(MerchantPartnerPortalDto merchantProfile)
    {
        var webshop = await merchantService.GetByIdFullAsync(merchantProfile.MerchantId);
        if (webshop == null)
        {
            return new DataResponseDto()
            {
                Success = false,
                Message =
                    $"Webshop with ID: {merchantProfile.MerchantId} was not found - If this error persists, please contact ViaAds"
            };
        }

        using (var httpClient = new HttpClient())
        {
            httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");
            
            var response = await httpClient.GetStringAsync(webshop.Url);

            var htmlDocument = new HtmlAgilityPack.HtmlDocument();
            htmlDocument.LoadHtml(response);

            // Check if the script tag with the source 'danDomain.min.js' exists
            var scriptNode =
                htmlDocument.DocumentNode.SelectSingleNode(
                    "//script[contains(text(), 'https://files.viaads.dk/plugins/min/danDomain.min.js')]");

            if (scriptNode == null)
            {
                return new DataResponseDto()
                {
                    Success = false,
                    Message = $"The Script was not found on the Webshop with url: {webshop.Url}"
                };
            }

            // Check if the ShopIdentifier has been set correctly within the script
            var shopConfig =
                htmlDocument.DocumentNode.SelectSingleNode("//script[contains(text(), 'window.ShopConfig =')]");

            if (shopConfig == null || !shopConfig.InnerText.Contains(
                    $"ShopIdentifier: '{webshop.MerchantMeta.SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.ShopIdentifier)?.Value}'"))
            {
                return new DataResponseDto()
                {
                    Success = false,
                    Message = $"The Shop Identifier was incorrect - make sure this is set and is correct"
                };
            }

            return new DataResponseDto()
            {
                Success = true,
                Message = $"Script Successfully Inserted on Webshop"
            };
        }
    }
}