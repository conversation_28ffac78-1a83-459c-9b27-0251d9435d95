using System.Net.Http.Headers;
using System.Text.Json;
using Gateway_Services.Services.Onboarding.Cms;
using Shared.Dto.Webshop;
using Shared.Models;
using Shared.Models.Merchant;
using Webshop.Webshop;
using Webshop_Service.Services.Webshop;

namespace PartnerPortal_Services.Services.Onboarding.Cms;

public class WooCommerceOnboardingService (IMerchantService merchantService) : ICmsOnboardingService
{
    public async Task<DataResponseDto> ValidateStep(MerchantPartnerPortalDto merchantProfile)
    {
        switch (merchantProfile.Step)
        {
            case 1:
                return await ValidatePluginInstallation(merchantProfile);
            case 2:
                var response = await ValidateApiKey(merchantProfile);
                if (!response.Success) return response;

                if(merchantProfile.PartnerId == 52876)
                {
                    return await SetupPlugin(merchantProfile);
                }
                else
                {
                    return new DataResponseDto()
                    {
                        Success = true,
                        Message = "Successfully validated API Key"
                    };
                }
            case 3:
                return await ValidateWooCommerceUserKeyAndSecret(merchantProfile);
            default:
                return new DataResponseDto()
                {
                    Success = true,
                    Message = "No validation Needed"
                };
        }
    }

    private async Task<DataResponseDto> ValidateWooCommerceUserKeyAndSecret(MerchantPartnerPortalDto merchantProfile)
    {
        try
        {
            // Fetch WooCommerce User Key and User Secret from ComProperties
            var userKey = merchantProfile.CmsProperties["woocommerce_user_key"];
            var userSecret = merchantProfile.CmsProperties["woocommerce_user_secret"];

            if (string.IsNullOrEmpty(userKey) || string.IsNullOrEmpty(userSecret))
            {
                return new DataResponseDto()
                {
                    Success = false,
                    Message = "WooCommerce User Key and User Secret are required"
                };
            }

            // Validate WooCommerce User Key and User Secret
            var url = merchantProfile.Url.EndsWith("/") ? merchantProfile.Url.TrimEnd('/') : merchantProfile.Url;
            url = $"{url}/wp-json/wc/v3/orders?per_page=1";

            if (!url.StartsWith("https://"))
                url = $"https://{url}";     

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");
                
                // Add Basic Authentication
                var authString = $"{userKey}:{userSecret}";
                var authHeader = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(authString));
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authHeader);

                var response = await client.GetAsync(url);

                switch (response.StatusCode)
                {
                    case System.Net.HttpStatusCode.OK:
                        // Save the users Credentials
                        await merchantService.UpdateMerchantMeta(merchantProfile.MerchantId, MerchantMetaTypeNames.ApiUserKey, userKey);
                        await merchantService.UpdateMerchantMeta(merchantProfile.MerchantId, MerchantMetaTypeNames.ApiUserSecret, userSecret);

                        return new DataResponseDto()
                        {
                            Success = true,
                            Message = "Successfully validated WooCommerce credentials"
                        };
                    
                    case System.Net.HttpStatusCode.Unauthorized:
                        return new DataResponseDto()
                        {
                            Success = false,
                            Message = "Invalid WooCommerce credentials. Please check your User Key and User Secret."
                        };
                    
                    case System.Net.HttpStatusCode.Forbidden:
                        return new DataResponseDto()
                        {
                            Success = false,
                            Message = "Insufficient permissions for the provided WooCommerce credentials."
                        };
                    
                    default:
                        return new DataResponseDto()
                        {
                            Success = false,
                            Message = $"Failed to validate WooCommerce credentials. Status code: {response.StatusCode}"
                        };
                }
            }
        }
        catch (Exception ex)
        {
            return new DataResponseDto()
            {
                Success = false,
                Message = $"Exception occurred while validating WooCommerce credentials: {ex.Message}"
            };
        }
    }

    private async Task<DataResponseDto> SetupPlugin(MerchantPartnerPortalDto merchantProfile)
    {
        try
        {
            if (string.IsNullOrEmpty(merchantProfile.Url))
            {
                return new DataResponseDto()
                {
                    Success = false,
                    Message = $"The URL cannot be null or empty: {nameof(merchantProfile.Url)}"
                };
            }
            
            var partnerPath = merchantProfile.PartnerId == 52876 ? "viaads" : "happyads";
            var partnerParam = merchantProfile.PartnerId == 52876 ? "apiKeyViabillMarketing" : "apiKeyValyrionMarketing";

            var url = merchantProfile.Url.EndsWith("/") ? merchantProfile.Url.TrimEnd('/') : merchantProfile.Url;
            url = $"{url}/wp-json/{partnerPath}/v1/setup?{partnerParam}={merchantProfile.ApiKey}";

            if (!url.StartsWith("https://"))
                url = $"https://{url}";

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

                var response = await client.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                {
                    return new DataResponseDto()
                    {
                        Success = false,
                        Message = "Failed to Setup the Plugin, please contact us for more information"
                    };
                }

                return new DataResponseDto()
                {
                    Success = true,
                    Message = "Successfully Setup the Plugin"
                };
            }
        }
        catch (Exception ex)
        {
            return new DataResponseDto()
            {
                Success = false,
                Message = $"Exception occurred while creating API User: {ex.Message}"
            };
        }
    }

    private async Task<DataResponseDto> ValidatePluginInstallation(MerchantPartnerPortalDto merchantProfile)
    {
        try
        {
            if (string.IsNullOrEmpty(merchantProfile.Url))
            {
                return new DataResponseDto()
                {
                    Success = false,
                    Message = $"The URL cannot be null or empty: {nameof(merchantProfile.Url)}"
                };
            }
            var partnerPath = merchantProfile.PartnerId == 52876 ? "viaads/v1/validate/plugin" : "happy-ads/v1/installation";

            var url = merchantProfile.Url.EndsWith("/") ? merchantProfile.Url.TrimEnd('/') : merchantProfile.Url;
            url = $"{url}/wp-json/{partnerPath}";

            if (!url.StartsWith("https://"))
                url = $"https://{url}";

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");
                client.Timeout = TimeSpan.FromMinutes(2);

                var response = await client.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                {
                    return new DataResponseDto()
                    {
                        Success = false,
                        Message =
                            "Failed to validate plugin installation. Make sure the plugin is installed and activated"
                    };
                }

                var responseBody = await response.Content.ReadAsStringAsync();
                //var valid = responseBody == "\"Successfully Installed\"";
                return new DataResponseDto()
                {
                    Success = true,
                    Message = true ? "Successfully Installed Plugin" : "Unsuccessfully installed plugin"
                };
            }
        }
        catch (Exception ex)
        {
            return new DataResponseDto()
            {
                Success = false,
                Message = $"Exception occurred while validating plugin installation: {ex.Message}"
            };
        }
    }

    private async Task<DataResponseDto> ValidateApiKey(MerchantPartnerPortalDto merchantProfile)
    {
        try
        {
            if (string.IsNullOrEmpty(merchantProfile.Url))
            {
                return new DataResponseDto()
                {
                    Success = false,
                    Message = $"The URL cannot be null or empty: {nameof(merchantProfile.Url)}"
                };
            }
            
            var partnerPath = merchantProfile.PartnerId == 52876 ? $"viaads/v1/validate/apikey?apiKeyViabillMarketing={merchantProfile.ApiKey}" : "happy-ads/v1/verify";

            var url = merchantProfile.Url.EndsWith("/") ? merchantProfile.Url.TrimEnd('/') : merchantProfile.Url;
            url = $"{url}/wp-json/{partnerPath}";

            if (!url.StartsWith("https://"))
                url = $"https://{url}";

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

                if (merchantProfile.PartnerId != 52876)
                {
                    // Use Bearer token as per new WordPress endpoint requirements
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", merchantProfile.ApiKey);
                }

                var response = await client.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                {
                    return new DataResponseDto()
                    {
                        Success = false,
                        Message = "Failed to validate API Key. Make sure the API Key is correctly inserted."
                    };
                }

                var responseBody = await response.Content.ReadAsStringAsync();
                // Parse the response to check for status: verified
                bool isVerified = false;
                if (responseBody.Contains("Valid API Key"))
                {
                    isVerified = true;
                }
                else
                {
                    try
                    {
                        var json = JsonDocument.Parse(responseBody);
                        isVerified = (json.RootElement.TryGetProperty("status", out var statusProp) &&
                                      statusProp.GetString() == "verified");
                    }
                    catch
                    {
                        // Ignore parse errors, treat as not verified
                    }
                }

                return new DataResponseDto()
                {
                    Success = isVerified,
                    Message = isVerified ? "Successfully inserted API Key" : "Unsuccessfully inserted API Key"
                };
            }
        }
        catch (Exception ex)
        {
            return new DataResponseDto()
            {
                Success = false,
                Message = $"Exception occurred while validating API Key: {ex.Message}"
            };
        }
    }
}