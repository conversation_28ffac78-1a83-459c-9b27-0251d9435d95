using System.Net;
using System.Text;
using System.Text.Json;
using Gateway_Services.Services.Onboarding.Cms;
using Serilog;
using Shared.Dto.Webshop;
using Shared.Models;
using Shared.Models.Merchant;
using Webshop.Webshop;

namespace PartnerPortal_Services.Services.Onboarding.Cms;

public class MagentoOnboardingService(ILogger logger, IMerchantService merchantService) : ICmsOnboardingService
{
    public async Task<DataResponseDto> ValidateStep(MerchantPartnerPortalDto merchantProfile)
    {
        switch (merchantProfile.Step)
        {
            case 2:
                return await ValidateApiUser(merchantProfile);
            case 3:
                return await ValidatePluginInstallation(merchantProfile);
            case 4:
                var validationResponse = await ValidateApiKey(merchantProfile);
                if (!validationResponse.Success) return validationResponse;
                return await CreateApiUser(merchantProfile);

            default:
                return new DataResponseDto()
                {
                    Success = true,
                    Message = "No validation Needed"
                };
        }
    }

    private async Task<DataResponseDto> ValidateApiUser(MerchantPartnerPortalDto merchantProfile)
    {
        try
        {
            if (string.IsNullOrEmpty(merchantProfile.Url))
            {
                return new DataResponseDto()
                {
                    Success = false,
                    Message = $"The URL cannot be null or empty: {nameof(merchantProfile.Url)}"
                };
            }

            using (var httpClient = new HttpClient())
            {
                string url = $"{merchantProfile.Url}/index.php/rest/V1/";
                var username = merchantProfile.CmsProperties
                    .SingleOrDefault(a => a.Key == "username").Value ?? "";
                var password = merchantProfile.CmsProperties
                    .SingleOrDefault(a => a.Key == "password").Value ?? "";
                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    return new DataResponseDto()
                    {
                        Success = false,
                        Message = "Either the Username or Password was not inserted"
                    };
                }

                var token = await GetAdminTokenAsync(httpClient, url, username, password);

                if (string.IsNullOrEmpty(token))
                {
                    return new DataResponseDto()
                    {
                        Success = false,
                        Message = "The API User Credentials was incorrect - a connection could not be established"
                    };
                }

                if (token.Equals("forbidden"))
                {
                    return new DataResponseDto()
                    {
                        Success = false,
                        Message =
                            "The API User does not have correct permissions - please check if the Users Role has the correct Permissions"
                    };
                }

                if (!await VerifyConnection(token, url, "products"))
                {
                    return new DataResponseDto()
                    {
                        Success = false,
                        Message =
                            "A Connection to the Products Module was not possible - make sure the API User has the Catalog::Products permissions set"
                    };
                }

                if (!await VerifyConnection(token, url, "orders"))
                {
                    return new DataResponseDto()
                    {
                        Success = false,
                        Message =
                            "A Connection to the Orders Module was not possible - make sure the API User has the Sales::View permissions set"
                    };
                }

                if (!await VerifyConnection(token, url, "categories"))
                {
                    return new DataResponseDto()
                    {
                        Success = false,
                        Message =
                            "A Connection to the Category Module was not possible - make sure the API User has the Catalog::Categories permissions set"
                    };
                }

                var merchant = await merchantService.GetByIdFullAsync(merchantProfile.MerchantId);
                if (merchant == null)
                {
                    logger.ForContext("service_name", GetType().Name)
                        .Error("Magento Merchant not found - Id: {MerchantID}, Name: {MerchantName}",
                            merchantProfile.MerchantId, merchantProfile.Name);
                    return new DataResponseDto()
                    {
                        Success = false,
                        Message = "Merchant was not found in the system - please contact ViaAds"
                    };
                }

                await merchantService.AddOrUpdateMerchantMeta(merchant, MerchantMetaTypeNames.ApiUserKey, username);
                await merchantService.AddOrUpdateMerchantMeta(merchant, MerchantMetaTypeNames.ApiUserSecret, password);
                await merchantService.UpdateMerchantAsync(merchant);

                return new DataResponseDto()
                {
                    Success = true,
                    Message = "All Connections were successfully established"
                };
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error connecting to Magento Merchant");
            return new DataResponseDto()
            {
                Success = false,
                Message = $"Exception occurred while establishing a Connection to the Magento Shop: {ex.Message}"
            };
        }
    }

    private async Task<bool> VerifyConnection(string token, string url, string endpoint)
    {
        using (var httpClient = new HttpClient())
        {
            //Fix bot detection
            httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

            try
            {
                var searchCriteria = $"searchCriteria[pageSize]=1&searchCriteria[currentPage]=1";

                var request = new HttpRequestMessage(HttpMethod.Get, $"{url}{endpoint}?{searchCriteria}");
                request.Headers.Add("Authorization", "Bearer " + token);
                var response = await httpClient.SendAsync(request);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                logger.ForContext("service_name", GetType().Name)
                    .Error(ex, "Error connecting to Magento Merchant");
            }
        }

        return false;
    }

    async Task<string> GetAdminTokenAsync(HttpClient httpClient, string magentoUrl, string username, string password)
    {
        var request = new HttpRequestMessage(HttpMethod.Post, magentoUrl + "integration/admin/token");
        var credentials = new {username, password};
        var json = JsonSerializer.Serialize(credentials);
        request.Content = new StringContent(json, Encoding.UTF8, "application/json");

        var response = await httpClient.SendAsync(request);
        var responseString = await response.Content.ReadAsStringAsync();

        if (!response.IsSuccessStatusCode)
        {
            if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                return "forbidden";
            }

            return string.Empty;
        }

        return responseString.Trim('"'); // Remove quotes around token
    }

    private async Task<DataResponseDto> ValidatePluginInstallation(MerchantPartnerPortalDto merchantProfile)
    {
        try
        {
            if (string.IsNullOrEmpty(merchantProfile.Url))
            {
                return new DataResponseDto()
                {
                    Success = false,
                    Message = $"The URL cannot be null or empty: {nameof(merchantProfile.Url)}"
                };
            }

            var url = merchantProfile.Url.EndsWith("/") ? merchantProfile.Url.TrimEnd('/') : merchantProfile.Url;
            url = $"{url}/rest/V1/viaads/validate/plugin";

            if (!url.StartsWith("https://"))
                url = $"https://{url}";

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

                var response = await client.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                {
                    return new DataResponseDto()
                    {
                        Success = false,
                        Message =
                            $"Failed to validate plugin installation. Make sure the plugin is installed and activated"
                    };
                }

                var responseBody = await response.Content.ReadAsStringAsync();
                var valid = responseBody == "\"Successfully Installed\"";
                return new DataResponseDto()
                {
                    Success = valid,
                    Message = valid ? "Successfully Installed Plugin" : "Unsuccessfully installed plugin"
                };
            }
        }
        catch (Exception ex)
        {
            return new DataResponseDto()
            {
                Success = false,
                Message = $"Exception occurred while validating plugin installation: {ex.Message}"
            };
        }
    }

    private async Task<DataResponseDto> ValidateApiKey(MerchantPartnerPortalDto merchantProfile)
    {
        try
        {
            if (string.IsNullOrEmpty(merchantProfile.Url))
            {
                return new DataResponseDto()
                {
                    Success = false,
                    Message = $"The URL cannot be null or empty: {nameof(merchantProfile.Url)}"
                };
            }

            var url = merchantProfile.Url.EndsWith("/") ? merchantProfile.Url.TrimEnd('/') : merchantProfile.Url;
            url = $"{url}/rest/V1/viaads/validate/apikey/{merchantProfile.ApiKey}";

            if (!url.StartsWith("https://"))
                url = $"https://{url}";

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

                var response = await client.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                {
                    return new DataResponseDto()
                    {
                        Success = false,
                        Message = $"Failed to validate API Key. Make sure the API Key is correctly inserted."
                    };
                }

                var responseBody = await response.Content.ReadAsStringAsync();
                var valid = responseBody == "\"Valid API Key\"";
                return new DataResponseDto()
                {
                    Success = valid,
                    Message = valid ? "Successfully inserted API Key" : "Unsuccessfully inserted API Key"
                };
            }
        }
        catch (Exception ex)
        {
            return new DataResponseDto()
            {
                Success = false,
                Message = $"Exception occurred while validating API Key: {ex.Message}"
            };
        }
    }


    private async Task<DataResponseDto> CreateApiUser(MerchantPartnerPortalDto merchantProfile)
    {
        try
        {
            if (string.IsNullOrEmpty(merchantProfile.Url))
            {
                return new DataResponseDto()
                {
                    Success = false,
                    Message = $"The URL cannot be null or empty: {nameof(merchantProfile.Url)}"
                };
            }

            var url = merchantProfile.Url.EndsWith("/") ? merchantProfile.Url.TrimEnd('/') : merchantProfile.Url;
            url = $"{url}/rest/V1/viaads/create/user";

            if (!url.StartsWith("https://"))
                url = $"https://{url}";

            //url = "https://magento-viaads.com/rest/V1/viaads/createuser";

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

                var response = await client.GetAsync(url);

                if (!response.IsSuccessStatusCode)
                {
                    return new DataResponseDto()
                    {
                        Success = false,
                        Message = $"Failed to create API User. Make sure the API Key is correctly inserted."
                    };
                }

                var responseBody = await response.Content.ReadAsStringAsync();
                var valid = responseBody == "\"API user and role created successfully!\"";
                return new DataResponseDto()
                {
                    Success = valid,
                    Message = valid ? "Successfully created API User" : "Unsuccessfully created API User"
                };
            }
        }
        catch (Exception ex)
        {
            return new DataResponseDto()
            {
                Success = false,
                Message = $"Exception occurred while creating API User: {ex.Message}"
            };
        }
    }
}