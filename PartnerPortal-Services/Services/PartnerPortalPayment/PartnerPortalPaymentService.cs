#region

using Merchant_Services.Models.ModelsDal.Merchant;
using PartnerPortal_Services.Models;
using PartnerPortal_Services.Models.Payment;
using Stripe;
using Webshop.Webshop;

#endregion

namespace PartnerPortal_Services.Services.PartnerPortalPayment;

public class PartnerPortalPaymentService : IPartnerPortalPaymentService
{
    private readonly IMerchantService _merchantService;

    public PartnerPortalPaymentService(IMerchantService merchantService)
    {
        _merchantService = merchantService;
        StripeConfiguration.ApiKey =
            "sk_test_51OgPqmEa8YRYImpjFkFMAFtgwwfG2TEFmcJfVkuGX2GlKSz8ohP4yGJRylnsodwOZxf1ugq7rh57C5Z3igeLumI000aa0oY2Bc";
    }

    public async Task<PaymentCustomerDto> GetCustomer(int merchantId)
    {
        var paymentCustomerDto = new PaymentCustomerDto();
        var merchant = await _merchantService.GetByIdFullAsync(merchantId);
        if (merchant != null && merchant.MerchantPayers.Any())
        {
            var customerService = new CustomerService();
            var payer = merchant.MerchantPayers.First().FkPayer;
            //Check if payer exists
            if (merchant.MerchantPayers.Count != 0)
            {
                Customer? customer = null;
                try
                {
                    customer = await customerService.GetAsync(payer.ProviderCustomerId);
                }
                catch (Exception)
                {
                }

                List<PaymentMethod> paymentMethods = new List<PaymentMethod>();
                var paymentMethodService = new PaymentMethodService();
                try
                {
                    paymentMethods = (await paymentMethodService.ListAsync(new PaymentMethodListOptions
                    {
                        Customer = payer.ProviderCustomerId,
                    })).ToList();
                }
                catch (Exception)
                {
                }

                if (customer != null)
                {
                    paymentCustomerDto.Email = customer.Email;
                    paymentCustomerDto.Description = customer.Description;
                    foreach (var paymentMethod in paymentMethods)
                    {
                        paymentCustomerDto.Cards.Add(new PaymentCustomerCardsDto
                        {
                            Id = paymentMethod.Id,
                            CustomerId = paymentMethod.CustomerId,
                            Last4 = paymentMethod.Card.Last4,
                            ExpYear = paymentMethod.Card.ExpYear,
                            ExpMonth = paymentMethod.Card.ExpMonth,
                            Brand = paymentMethod.Card.DisplayBrand
                        });
                    }
                }
            }
        }


        return paymentCustomerDto;
    }

    public async Task<PaymentDto> SetUpCard(PaymentDto paymentDto)
    {
        var merchant = await _merchantService.GetByIdFullAsync(paymentDto.MerchantId);
        if (merchant != null)
        {
            var customerService = new CustomerService();

            //Check if payer exists
            if (merchant.MerchantPayers.Count == 0)
            {
                var options = new CustomerCreateOptions
                {
                    Name = merchant.Name,
                    Description = $"MerchantId: {merchant.Id}",
                };
                var providerCustomerId = (await customerService.CreateAsync(options)).Id;

                merchant.MerchantPayers.Add(new MerchantPayer
                {
                    Active = true,
                    FkPayer = new Payer
                    {
                        InvoiceType = "Credit",
                        ProviderCustomerId = providerCustomerId,
                        PaymentActivated = false
                    }
                });
                await _merchantService.UpdateSimpleAsync(merchant);
            }

            var payer = merchant.MerchantPayers.First().FkPayer;
            Customer? customer = null;

            try
            {
                customer = await customerService.GetAsync(payer.ProviderCustomerId);
            }
            catch (Exception)
            {
            }

            if (customer != null)
            {
                var checkOutSessionService = new Stripe.Checkout.SessionService();
                var optionsPaymentMethod = new Stripe.Checkout.SessionCreateOptions
                {
                    Mode = "setup",
                    Currency = "dkk",
                    Customer = customer.Id,
                    SuccessUrl = paymentDto.Path + "?success=true&session_id={CHECKOUT_SESSION_ID}",
                    CancelUrl = $"{paymentDto.Path}?success=false",
                };
                var session = (await checkOutSessionService.CreateAsync(optionsPaymentMethod));

                return new PaymentDto
                {
                    MerchantId = merchant.Id,
                    Path = session.Url
                };
            }
        }

        return new PaymentDto();
    }

    public async Task UpdateCard(UpdateCardDto updateCardDto)
    {
        var merchant = await _merchantService.GetByIdFullAsync(updateCardDto.MerchantId);
        if (merchant != null)
        {
            var payer = merchant.MerchantPayers.First().FkPayer;
            List<PaymentMethod> paymentMethods = new List<PaymentMethod>();
            var paymentMethodService = new PaymentMethodService();
            try
            {
                paymentMethods = (await paymentMethodService.ListAsync(new PaymentMethodListOptions
                {
                    Customer = payer.ProviderCustomerId,
                })).ToList();
            }
            catch (Exception)
            {
            }

            //Remove old card if exists
            foreach (var paymentMethod in paymentMethods.OrderByDescending(a => a.Created).Skip(1).ToList())
            {
                await paymentMethodService.DetachAsync(paymentMethod.Id);
            }

            var checkOutSessionService = new Stripe.Checkout.SessionService();
            var setupIntentId = (await checkOutSessionService.GetAsync(updateCardDto.SessionId)).SetupIntentId;
            var service = new SetupIntentService();
            await service.GetAsync(setupIntentId);
            merchant.MerchantPayers.First().FkPayer.PaymentActivated = true;
            await _merchantService.UpdateSimpleAsync(merchant);
        }
    }

    public async Task RemoveCard(PaymentDto paymentDto)
    {
        var merchant = await _merchantService.GetByIdFullAsync(paymentDto.MerchantId);
        if (merchant != null)
        {
            var payer = merchant.MerchantPayers.First().FkPayer;
            List<PaymentMethod> paymentMethods = new List<PaymentMethod>();
            var paymentMethodService = new PaymentMethodService();
            try
            {
                paymentMethods = (await paymentMethodService.ListAsync(new PaymentMethodListOptions
                {
                    Customer = payer.ProviderCustomerId,
                })).ToList();
            }
            catch (Exception)
            {
            }

            foreach (var paymentMethod in paymentMethods)
            {
                await paymentMethodService.DetachAsync(paymentMethod.Id);
            }

            merchant.MerchantPayers.First().FkPayer.PaymentActivated = false;
            await _merchantService.UpdateSimpleAsync(merchant);
        }
    }
}