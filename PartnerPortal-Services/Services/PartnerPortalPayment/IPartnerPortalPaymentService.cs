#region

#endregion

using PartnerPortal_Services.Models;
using PartnerPortal_Services.Models.Payment;

namespace PartnerPortal_Services.Services.PartnerPortalPayment;

public interface IPartnerPortalPaymentService
{
    Task<PaymentCustomerDto> GetCustomer(int merchantId);
    Task<PaymentDto> SetUpCard(PaymentDto paymentDto);
    Task UpdateCard(UpdateCardDto updateCardDto);
    Task RemoveCard(PaymentDto paymentDto);
}