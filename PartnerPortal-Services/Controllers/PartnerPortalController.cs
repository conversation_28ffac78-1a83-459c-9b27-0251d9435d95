#region

using Discount_Services.Services.DiscountPartners;
using Integration.Models.Shopify;
using Integration.Services.Plugins.Integration;
using Integration.Services.Plugins.ShopifyIntegration;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using PartnerPortal_Services.Services.Onboarding;
using PartnerPortal_Services.Services.PartnerPortal;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Dto.Discount;
using Shared.Dto.PartnerPortal;
using Shared.Dto.Webshop;
using Shared.Models;
using Shared.Models.Merchant;
using Shared.Services.Setting;
using Webshop_Service.Services.Shopify;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

#endregion

namespace PartnerPortal_Services.Controllers;

[Authorize(Roles = "partner")]
[Route("[controller]")]
[ApiController]
public class PartnerPortalController(
    IPartnerPortalService partnerPortalService,
    ILogger logger,
    ISettingService settingService,
    IOnboardingService onboardingService,
    IIntegrationService integrationService,
    IMerchantService merchantService,
    IShopifyService shopifyService,
    IDiscountPartnerService discountPartnerService,
    IMemoryCache memoryCache)
    : ControllerBase
{
    private readonly ShopifyPluginService _shopifyPluginService = new(logger, integrationService, merchantService, shopifyService, memoryCache);

    [HttpGet]
    [AllowAnonymous]
    [Route("maintenanceMode")]
    public async Task<IActionResult> MaintenanceMode()
    {
        try
        {
            // TODO - Remove hardcoded partnerId and replace with actual partnerId
            return Ok((await settingService.GetSettingAsync(52876, "PartnerMaintenanceMode")).Value);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting MaintenanceMode");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("{merchantId:int}")]
    [Authorize(Roles = "partner")]
    public async Task<IActionResult> MerchantProfile(int merchantId)
    {
        try
        {
            if (!ValidateTokenMerchant(merchantId))
            {
                return Forbid();
            }

            return Ok(await partnerPortalService.GetMerchantProfileAsync(merchantId));
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting MerchantProfile");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Authorize(Roles = "partner")]
    [Route("{merchantId:int}/{year:int}/{month:int}")]
    public async Task<IActionResult> MerchantMonthlyPerformance(int merchantId, int year, int month)
    {
        try
        {
            if (!ValidateTokenMerchant(merchantId))
            {
                return Forbid();
            }

            return Ok(await partnerPortalService.GetMerchantMonthlyPerformanceAsync(merchantId, month,
                year));
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "MerchantMonthlyPerformance");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("webshop/{merchantId:int}")]
    [Authorize(Roles = "partner")]
    [PartnerAuthExempt]
    public async Task<IActionResult> GetMerchantProfile(int merchantId)
    {
        try
        {
            if (!ValidateTokenMerchant(merchantId))
            {
                return Forbid();
            }

            return Ok(await partnerPortalService.GetMerchantProfileForDashboardAsync(merchantId));
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting MerchantProfile");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Route("shopIdentifier/{merchantId:int}")]
    [Authorize(Roles = "partner")]
    public async Task<IActionResult> MerchantShopIdentifier(int merchantId)
    {
        try
        {
            if (!ValidateTokenMerchant(merchantId))
            {
                return Forbid();
            }

            var shopIdentifier = await partnerPortalService.GetMerchantShopIdentifier(merchantId);
            return Ok(shopIdentifier);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting MerchantShopIdentifier");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [AllowAnonymous]
    [HttpGet]
    [Route("users/token/{token}")]
    public async Task<IActionResult> GetUsersByToken(string token)
    {
        try
        {
            var webshopsUser = await partnerPortalService.GetUsersByToken(token);
            return Ok(webshopsUser);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting GetUsersByToken");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("resetPasswordEmail/{email}")]
    public async Task<IActionResult> ResetPasswordEmail(string email)
    {
        try
        {
            await partnerPortalService.ResetPasswordEmail(email);
            return Ok();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting ResetPasswordEmail");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [AllowAnonymous]
    [Route("updatePassword")]
    public async Task<IActionResult> UpdatePassword(
        MerchantUserPartnerPortalPasswordDto merchantUserPartnerPortalPasswordDto)
    {
        try
        {
            await partnerPortalService.UpdatePassword(merchantUserPartnerPortalPasswordDto);
            return Ok();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting UpdatePassword");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [Authorize(Roles = "partner")]
    [Route("users")]
    public async Task<IActionResult> CrudUsers(MerchantUserPartnerPortalDto merchantUser)
    {
        try
        {
            if (!ValidateTokenMerchant(merchantUser.FkMerchantId, true))
            {
                return Forbid();
            }

            return Ok(await partnerPortalService.CrudUsers(merchantUser, merchantUser.FkMerchantId));
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting CrudUsers");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [Authorize(Roles = "partner")]
    [Route("event")]
    public async Task<IActionResult> CreateEvent(PartnerPortalEventDto partnerPortalEvent)
    {
        try
        {
            if (!ValidateTokenMerchant(Convert.ToInt32(partnerPortalEvent.MerchantId), false))
            {
                return Forbid();
            }

            var token = Request.Headers.SingleOrDefault(a => a.Key == "Authorization");
            var jwtToken = partnerPortalService.ValidateToken(token.Value);
            var userId = jwtToken.Claims.Single(a => a.Type == "uid").Value;
            var email = jwtToken.Claims.Single(a => a.Type == "email").Value;
            await partnerPortalService.CreateEvent(partnerPortalEvent, email, userId).ConfigureAwait(false);
            return Ok();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while CreateEvent");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    /*[HttpPost]
    [Authorize(Roles = "partner-2")]
    [Route("users/resetPassword")]
    public async Task<IActionResult> ResetPasswordUsers(WebshopsUserPartnerPortalDto webshopsUser)
    {
        try
        {
            await _partnerPortalService.ResetPassword(webshopsUser, Convert.ToInt32(websshopId));
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting merchantProfile");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }*/

    [HttpGet]
    [Authorize(Roles = "partner")]
    [Route("users/{merchantId}")]
    public async Task<IActionResult> GetUsers(int merchantId)
    {
        try
        {
            if (!ValidateTokenMerchant(merchantId, true))
            {
                return Forbid();
            }

            var users = await partnerPortalService.GetUsers(merchantId);
            var webshopsUserPartnerPortal = new List<MerchantUserPartnerPortalDto>();
            foreach (var user in users)
            {
                var merchantUser = user.MerchantUsersRels.Single(a => a.Active && a.FkMerchantId == merchantId);
                webshopsUserPartnerPortal.Add(new MerchantUserPartnerPortalDto
                {
                    FkMerchantId = merchantUser.FkMerchantId,
                    FkUserGroupId = merchantUser.FkUserGroupId,
                    Active = user.Active,
                    Email = user.Email ?? "",
                    FirstName = user.FirstName ?? "",
                    LastName = user.LastName ?? "",
                    Id = user.Id
                });
            }

            return Ok(webshopsUserPartnerPortal);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting GetUsers");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet]
    [Authorize(Roles = "partner")]
    [Route("usersGroups")]
    public async Task<IActionResult> GetUsersGroups()
    {
        try
        {
            return Ok(await partnerPortalService.GetUsersGroup());
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting GetUsersGroups");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    //Login
    [AllowAnonymous]
    [HttpPost("login")]
    [PartnerAuthExempt]
    public async Task<IActionResult> LoginAsync(LoginDto login)
    {
        try
        {
            var result = await partnerPortalService.LoginAsync(login)
                
                .ConfigureAwait(false);
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Warning(ex, "Error while login");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [AllowAnonymous]
    [HttpGet("adminLogin/{merchantId:int}")]
    public async Task<IActionResult> LoginAsync(int merchantId)
    {
        try
        {
            var email = User.Claims.FirstOrDefault(a => a.Value.Contains("@"))!.Value;
            var result = await partnerPortalService.LoginAdminAsync(merchantId, email);
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Warning(ex, "Error while login admin");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    [AllowAnonymous]
    [HttpGet("shopifyLogin/{email}")]
    public async Task<IActionResult> ShopifyLoginAsync(string email)
    {
        try
        {
            var result = await partnerPortalService.LoginShopifyAsync(email);
            object response = new { myshopify = result };
            return Ok(response);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Warning(ex, "Error while login admin");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }

    
    
    /* Shopify Partner Section - Only for Shopify Review Team! */
    
    [AllowAnonymous]
    [HttpPost]
    [Route("products/count")]
    public async Task<IActionResult> PartnerPortalProductsCount(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalProductsCount"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalProductsCount(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting PartnerPortalProductsCount from shopify - Partner Portal");
            return Ok();
        }
    }
    
    [AllowAnonymous]
    [HttpPost]
    [Route("orders/count")]
    public async Task<IActionResult> PartnerPortalOrdersCount(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalOrdersCount"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalOrdersCount(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting PartnerPortalOrdersCount from shopify - Partner Portal");
            return Ok();
        }
    }
    
    [AllowAnonymous]
    [HttpPost]
    [Route("orders/aov")]
    public async Task<IActionResult> PartnerPortalOrdersAov(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalOrdersAov"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalOrdersAov(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting PartnerPortalOrdersAov from shopify - Partner Portal");
            return Ok();
        }
    }
    
    [AllowAnonymous]
    [HttpPost]
    [Route("orders/monthlyRevenue")]
    public async Task<IActionResult> PartnerPortalOrdersMonthlyRevenue(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalOrdersMonthlyRevenue"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalMonthlyRevenue(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting PartnerPortalOrdersMonthlyRevenue from shopify - Partner Portal");
            return Ok();
        }
    }
    
    [AllowAnonymous]
    [HttpPost]
    [Route("orders/table")]
    public async Task<IActionResult> PartnerPortalOrdersTable(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalOrdersTable"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalOrdersTable(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting PartnerPortalOrdersTable from shopify - Partner Portal");
            return Ok();
        }
    }
    
    [AllowAnonymous]
    [HttpPost]
    [Route("customers/count")]
    public async Task<IActionResult> PartnerPortalCustomersCount(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalCustomersCount"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalCustomersCount(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting PartnerPortalCustomersCount from shopify - Partner Portal");
            return Ok();
        }
    }
    
    [AllowAnonymous]
    [HttpPost]
    [Route("customers/uniqueEmail")]
    public async Task<IActionResult> PartnerPortalCustomersUniqueEmail(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalCustomersUniqueEmail"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalCustomersUniqueEmails(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting PartnerPortalCustomersUniqueEmail from shopify - Partner Portal");
            return Ok();
        }
    }
    
    [AllowAnonymous]
    [HttpPost]
    [Route("customers/uniquePhone")]
    public async Task<IActionResult> PartnerPortalCustomersUniquePhone(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalCustomersUniquePhone"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalCustomersUniquePhones(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting PartnerPortalCustomersUniquePhone from shopify - Partner Portal");
            return Ok();
        }
    }
    
    [AllowAnonymous]
    [HttpPost]
    [Route("customers/activeState")]
    public async Task<IActionResult> PartnerPortalCustomersActiveState(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalCustomersActiveState"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalCustomersActiveState(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting PartnerPortalCustomersActiveState from shopify - Partner Portal");
            return Ok();
        }
    }
    
    [AllowAnonymous]
    [HttpPost]
    [Route("customers/marketingConsents")]
    public async Task<IActionResult> PartnerPortalCustomersMarketingConsents(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalCustomersMarketingConsents"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalCustomersMarketingConsents(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting PartnerPortalCustomersMarketingConsents from shopify - Partner Portal");
            return Ok();
        }
    }
    
    [AllowAnonymous]
    [HttpPost]
    [Route("customers/topZipCodes")]
    public async Task<IActionResult> PartnerPortalTopZipCodes(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalTopZipCodes"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalTopZipCodes(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting PartnerPortalTopZipCodes from shopify - Partner Portal");
            return Ok();
        }
    }
    
    [AllowAnonymous]
    [HttpPost]
    [Route("customers/topTags")]
    public async Task<IActionResult> PartnerPortalTopTags(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalTopTags"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalTopTags(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting PartnerPortalTopTags from shopify - Partner Portal");
            return Ok();
        }
    }
    
    [AllowAnonymous]
    [HttpPost]
    [Route("customers/topCustomerEmail")]
    public async Task<IActionResult> PartnerPortalTopCustomerEmail(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalTopCustomerEmail"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalTopCustomerEmail(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting PartnerPortalTopCustomerEmail from shopify - Partner Portal");
            return Ok();
        }
    }
    
    [AllowAnonymous]
    [HttpPost]
    [Route("customers/table")]
    public async Task<IActionResult> PartnerPortalCustomersTable(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalCustomersTable"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalCustomersTable(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting PartnerPortalCustomersTable from shopify - Partner Portal");
            return Ok();
        }
    }

    [AllowAnonymous]
    [HttpPost]
    [Route("orders")]
    public async Task<IActionResult> PartnerPortalOrders(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalOrders"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalOrders(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting all products from shopify - Partner Portal");
            return Ok();
        }
    }


    [AllowAnonymous]
    [HttpPost]
    [Route("orders/stats")]
    public async Task<IActionResult> PartnerPortalOrderStats(ShopifyLoginDto shopifyLoginDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PartnerPortalOrders"))
            {
                var success = await _shopifyPluginService
                    .PartnerPortalOrderStats(shopifyLoginDto).ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting all products from shopify - Partner Portal");
            return Ok();
        }
    }

    /***************** Onboarding Section ******************/

    /// <summary>
    /// Validation of the different CMS Onboarding Steps
    /// </summary>
    /// <param name="merchantProfile"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize(Roles = "partner")]
    [Route("onboarding/validate")]
    public async Task<IActionResult> ValidateOnboardingSteps(MerchantPartnerPortalDto merchantProfile)
    {
        try
        {
            if (!ValidateTokenMerchant(merchantProfile.MerchantId))
            {
                return Forbid();
            }

            var response = await onboardingService.ValidateOnboardingStep(merchantProfile);
            return Ok(response);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while validating merchantProfile");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    private bool ValidateTokenMerchant(int merchantId, bool groupAdmin = false)
    {
        var token = Request.Headers.SingleOrDefault(a => a.Key == "Authorization");

        try
        {
            var jwtToken = partnerPortalService.ValidateToken(token.Value);
            string? websshopId = null;
            if (groupAdmin)
            {
                websshopId = jwtToken.Claims.FirstOrDefault(a => a.Value.Contains("partner||" + merchantId + "||2"))
                    ?.Value;
            }
            else
            {
                websshopId = jwtToken.Claims.FirstOrDefault(a => a.Value.Contains("partner||" + merchantId))?.Value;
            }

            if (websshopId != null)
            {
                return true;
            }
        }
        catch (Exception)
        {
            // ignored
        }

        return false;
    }

    /***************** Onboarding Section END ******************/

    /***************** Discount Section ******************/

    [HttpGet]
    [Route("discounts/{merchantId}")]
    [Authorize(Roles = "partner")]
    public async Task<IActionResult> GetDiscountPartnersByMerchantId(int merchantId)
    {
        try
        {
            if (!ValidateTokenMerchant(merchantId))
            {
                return Forbid();
            }

            return Ok(await discountPartnerService.GetDiscountPartnersByMerchantId(merchantId));
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while getting GetDiscountPartnersByMerchantId");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [Authorize(Roles = "partner")]
    [Route("discounts")]
    public async Task<IActionResult> CrudDiscount(DiscountPartnerDto discount)
    {
        try
        {
            if (!ValidateTokenMerchant(discount.FkMerchantId, true))
            {
                return Forbid();
            }

            var token = Request.Headers.SingleOrDefault(a => a.Key == "Authorization");
            var jwtToken = partnerPortalService.ValidateToken(token.Value);
            var email = jwtToken.Claims.Single(a => a.Type == "email").Value;

            return Ok(await discountPartnerService.CrudDiscounts(discount, email));
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting CrudDiscount");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    /***************** Discount Section END ******************/
}