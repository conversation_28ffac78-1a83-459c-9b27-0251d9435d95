#region

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PartnerPortal_Services.Models.Payment;
using PartnerPortal_Services.Services.PartnerPortal;
using PartnerPortal_Services.Services.PartnerPortalPayment;
using Shared.Models;
using Stripe;
using ILogger = Serilog.ILogger;

#endregion

namespace PartnerPortal_Services.Controllers;

[Authorize(Roles = "partner")]
[Route("[controller]")]
[ApiController]
public class PartnerPortalPaymentController : ControllerBase
{
    private readonly IPartnerPortalService _partnerPortalService;
    private readonly IPartnerPortalPaymentService _partnerPortalPaymentService;
    private readonly ILogger _logger;

    public PartnerPortalPaymentController(IPartnerPortalService partnerPortalService, ILogger logger,
        IPartnerPortalPaymentService partnerPortalPaymentService)
    {
        _partnerPortalService = partnerPortalService;
        _logger = logger;
        _partnerPortalPaymentService = partnerPortalPaymentService;
    }

    [HttpGet]
    [Route("{merchantId}")]
    [Authorize(Roles = "partner")]
    public async Task<IActionResult> StripeCustomer(int merchantId)
    {
        try
        {
            if (!ValidateTokenMerchant(merchantId, true))
            {
                return Forbid();
            }

            return Ok(await _partnerPortalPaymentService.GetCustomer(merchantId));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting stripeCustomer");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [Authorize(Roles = "partner")]
    public async Task<IActionResult> SetUpCard(PaymentDto paymentDto)
    {
        try
        {
            if (!ValidateTokenMerchant(paymentDto.MerchantId, true))
            {
                return Forbid();
            }

            return Ok(await _partnerPortalPaymentService.SetUpCard(paymentDto));
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting SetUpCard");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [Route("updateCard")]
    [Authorize(Roles = "partner")]
    public async Task<IActionResult> UpdateCard(UpdateCardDto updateCardDto)
    {
        try
        {
            if (!ValidateTokenMerchant(updateCardDto.MerchantId, true))
            {
                return Forbid();
            }

            await _partnerPortalPaymentService.UpdateCard(updateCardDto);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting updateCard");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    [Route("removeCard")]
    [Authorize(Roles = "partner")]
    public async Task<IActionResult> RemoveCard(PaymentDto paymentDto)
    {
        try
        {
            if (!ValidateTokenMerchant(paymentDto.MerchantId, true))
            {
                return Forbid();
            }

            await _partnerPortalPaymentService.RemoveCard(paymentDto);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting removeCard");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    // This is your Stripe CLI webhook secret for testing your endpoint locally.
    [HttpPost]
    [Route("webhooks")]
    public async Task<IActionResult> Index()
    {
        var json = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
        try
        {
            var stripeEvent = EventUtility.ConstructEvent(json,
                Request.Headers["Stripe-Signature"],
                "whsec_92f9b6491aab9515b16ea16bd64ac1610e0d400245f9b7ab6ab76c3e32e28aa3");

            // Handle the event
            if (stripeEvent.Type == Events.PaymentMethodAttached)
            {
                _logger.ForContext("service_name", GetType().Name).Error(json);
            }
            else
            {
                Console.WriteLine("Unhandled event type: {0}", stripeEvent.Type);
            }

            return Ok();
        }
        catch (StripeException e)
        {
            return BadRequest();
        }
    }

    private bool ValidateTokenMerchant(int merchantId, bool groupAdmin = false)
    {
        var token = Request.Headers.SingleOrDefault(a => a.Key == "Authorization");

        try
        {
            var jwtToken = _partnerPortalService.ValidateToken(token.Value);
            string? websshopId = null;
            if (groupAdmin)
            {
                websshopId = jwtToken.Claims.FirstOrDefault(a => a.Value.Contains("partner||" + merchantId + "||2"))
                    ?.Value;
            }
            else
            {
                websshopId = jwtToken.Claims.FirstOrDefault(a => a.Value.Contains("partner||" + merchantId))?.Value;
            }

            if (websshopId != null)
            {
                return true;
            }
        }
        catch (Exception)
        {
            // ignored
        }

        return false;
    }
}