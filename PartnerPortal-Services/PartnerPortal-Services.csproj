<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <RootNamespace>PartnerPortal_Services</RootNamespace>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Invoice-Services\Invoice-Services.csproj" />
        <ProjectReference Include="..\Integration-Services\Integration-Services.csproj" />
        <ProjectReference Include="..\Shared\Shared.csproj" />
        <ProjectReference Include="..\Statistics-Services\Statistics-Services.csproj" />
        <ProjectReference Include="..\Merchant-Services\Merchant-Services.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="HtmlAgilityPack" Version="1.11.59" />
    </ItemGroup>

</Project>