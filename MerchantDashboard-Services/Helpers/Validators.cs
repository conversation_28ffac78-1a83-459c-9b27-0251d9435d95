using System.IdentityModel.Tokens.Jwt;
using System.Text;
using Microsoft.AspNetCore.Http;
using Microsoft.IdentityModel.Tokens;

namespace MerchantDashboard_Services.Helpers
{
    public static class Validators
    {
        public static bool ValidateTokenMerchant(HttpRequest request, int merchantId, string jwtSecretLogin,
            bool groupAdmin = false)
        {
            var token = request.Headers.Authorization.FirstOrDefault();

            if (string.IsNullOrEmpty(token))
            {
                return false;
            }

            try
            {
                var jwtToken = ValidateAndFetchToken(token, jwtSecretLogin);
                var claimValue = groupAdmin
                    ? $"partner||{merchantId}||2"
                    : $"partner||{merchantId}";

                return jwtToken.Claims.Any(c => c.Value.Contains(claimValue));
            }
            catch (Exception)
            {
                // Log exception if necessary
                return false;
            }
        }

        private static TokenValidationParameters GetValidationParameters(string jwtSecretLogin)
        {
            return new TokenValidationParameters
            {
                ValidateLifetime = false,
                ValidateAudience = false,
                ValidateIssuer = false,
                ValidIssuer = "Valyrion",
                ValidAudience = "Valyrion",
                ValidAlgorithms = new List<string> {SecurityAlgorithms.HmacSha256},
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSecretLogin))
            };
        }

        public static JwtSecurityToken ValidateAndFetchToken(string authToken, string jwtSecretLogin)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var validationParameters = GetValidationParameters(jwtSecretLogin);

            tokenHandler.ValidateToken(authToken.Split(" ")[1], validationParameters, out var validatedToken);
            return (JwtSecurityToken) validatedToken;
        }
    }
}