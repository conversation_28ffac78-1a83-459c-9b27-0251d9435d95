using System.Text;
using System.Text.Json;
using Integration.Models.Elastic.Order;
using Marlin_OS_Integration_API.Models.Order;
using Microsoft.Extensions.DependencyInjection;
using RabbitMQ.Client;
using Shared.Dto.MerchantDashboard.Event;
using Shared.Elastic.PartnerPortal;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace MerchantDashboard_Services.Services.Event;

public class MerchantDashboardEventService(IMerchantService merchantService, [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud, ILogger logger) : IMerchantDashboardEventService
{
    public async Task CreateEvent(MerchantDashboardEventDto merchantDashboardEventDto, string email, string userId)
    {
        var user = await merchantService.GetByIdAsync(Convert.ToInt32(merchantDashboardEventDto.MerchantId))
            .ConfigureAwait(false);
        var elasticPartnerPortalEvent = new ElasticPartnerPortalEvent
        {
            Created_date = DateTime.UtcNow,
            user = new ElasticPartnerPortalEventUser()
            {
                email = email,
                id = userId
            },
            Event = new ElasticPartnerPortalEventEvent
            {
                type = merchantDashboardEventDto.EventType,
                action = merchantDashboardEventDto.Action,
                Webshop_id = merchantDashboardEventDto.MerchantId.ToString(),
                Webshop_name = user.Name
            }
        };

        var json = JsonSerializer.Serialize(elasticPartnerPortalEvent);
        var actionBody = Encoding.UTF8.GetBytes(json);
        try
        {
            using (var publishChannel = rabbitConnectionCloud.CreateModel())
            {
                publishChannel.ConfirmSelect();
                
                publishChannel.BasicPublish(exchange: "log",
                    routingKey: "audit_merchantdashboard_event",
                    basicProperties: null,
                    body: actionBody);

                publishChannel.WaitForConfirmsOrDie(new TimeSpan(0, 0, 10));
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error creating event for Merchant Dashboard with RabbitMQ {Json}", json);
        }
    }

    public async Task CreateTermsAndConditionsAcceptEvent(int merchantId, string email, string? userId)
    {
        int? userIdInt = Convert.ToInt32(userId);
        if (userIdInt == 0)
        {
            userIdInt = null;
        }
        
        await merchantService.CreateTermsAndConditionsAcceptEvent(merchantId, email, userIdInt)
            .ConfigureAwait(false);
    }
}