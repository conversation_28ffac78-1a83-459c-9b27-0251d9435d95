using General_Services.Models.ModelsDal.Valyrion;
using MerchantDashboard_Services.Models.Support;
using Microsoft.EntityFrameworkCore;
using Partner_Services.Models.ModelsDal.Partner;

namespace MerchantDashboard_Services.Services.Support;

public class SupportCenterService(PartnerDbContext partnerDbContext, ValyrionDbContext valyrionDbContext)
    : ISupportCenterService
{
    public async Task<IEnumerable<FAQSectionDto>> GetFAQsAsync(int partnerId, string? lang)
    {
        // Step 1: Determine the languageId for the given language or default
        var languageId = await DetermineLanguageIdAsync(partnerId, lang);

        // Step 2: Fetch FAQ sections for the determined languageId
        var faqSections = await partnerDbContext.Faqsections
            .AsNoTracking()
            .Include(f => f.Faqs)
            .Where(f => f.FkPartnerId == partnerId && f.FkLanguageId == languageId && f.IsActive)
            .OrderBy(f => f.DisplayOrder)
            .Select(f => new FAQSectionDto()
            {
                Name = f.Name,
                DisplayOrder = f.DisplayOrder,
                FAQs = f.Faqs
                    .Where(faq => faq.IsActive)
                    .OrderBy(faq => faq.DisplayOrder)
                    .Select(faq => new FAQDto()
                    {
                        Id = faq.Id,
                        Question = faq.Question,
                        Answer = faq.Answer,
                        DisplayOrder = faq.DisplayOrder
                    }).ToList()
            }).ToListAsync();

        // Step 3: Check if no sections found and fallback to default language if applicable
        if (faqSections.Count != 0)
            return faqSections;


        // Fetch the default languageId for the partner
        var defaultLanguageId = await GetDefaultLanguageIdForPartnerAsync(partnerId);
        if (languageId != defaultLanguageId) // Ensure we don't re-query if already using default
        {
            faqSections = await partnerDbContext.Faqsections
                .AsNoTracking()
                .Include(f => f.Faqs)
                .Where(f => f.FkPartnerId == partnerId && f.FkLanguageId == defaultLanguageId && f.IsActive)
                .OrderBy(f => f.DisplayOrder)
                .Select(f => new FAQSectionDto()
                {
                    Name = f.Name,
                    DisplayOrder = f.DisplayOrder,
                    FAQs = f.Faqs
                        .Where(faq => faq.IsActive)
                        .OrderBy(faq => faq.DisplayOrder)
                        .Select(faq => new FAQDto()
                        {
                            Id = faq.Id,
                            Question = faq.Question,
                            Answer = faq.Answer,
                            DisplayOrder = faq.DisplayOrder
                        }).ToList()
                }).ToListAsync();
        }

        return faqSections;
    }

    private async Task<int> DetermineLanguageIdAsync(int partnerId, string? lang)
    {
        // Fetch partner languages and default language in a single query
        var partnerLanguageQuery = partnerDbContext.PartnerLanguages
            .AsNoTracking()
            .Where(p => p.FkPartnerId == partnerId && p.IsActive);

        // Try to determine the requested language if provided
        if (string.IsNullOrEmpty(lang))
            return await partnerLanguageQuery
                .Where(p => p.IsDefault)
                .Select(p => p.FkLanguageId)
                .FirstOrDefaultAsync();

        var tempLanguageId = await valyrionDbContext.Languages
            .AsNoTracking()
            .Where(l => l.LanguageCode == lang && l.IsActive)
            .Select(l => l.Id)
            .FirstOrDefaultAsync();

        if (tempLanguageId == 0)
            return await partnerLanguageQuery
                .Where(p => p.IsDefault)
                .Select(p => p.FkLanguageId)
                .FirstOrDefaultAsync();

        var foundLanguage = await partnerLanguageQuery
            .AnyAsync(p => p.FkLanguageId == tempLanguageId);

        if (foundLanguage)
        {
            return tempLanguageId;
        }

        // Fallback to default language if no match found
        return await partnerLanguageQuery
            .Where(p => p.IsDefault)
            .Select(p => p.FkLanguageId)
            .FirstOrDefaultAsync();
    }

    private async Task<int> GetDefaultLanguageIdForPartnerAsync(int partnerId)
    {
        return await partnerDbContext.PartnerLanguages
            .AsNoTracking()
            .Where(p => p.FkPartnerId == partnerId && p.IsDefault && p.IsActive)
            .Select(p => p.FkLanguageId)
            .FirstOrDefaultAsync();
    }


    public async Task<SupportContactDto?> GetSupportContactByIdAsync(int contactId, int partnerId)
    {
        // Try to fetch the requested contact first
        var contact = await partnerDbContext.SupportContacts
            .AsNoTracking() // Optimize for read-only query
            .Where(s => s.Id == contactId && s.IsActive)
            .Select(s => new SupportContactDto
            {
                Id = s.Id,
                ContactName = s.ContactName,
                Email = s.Email,
                PhoneNumber = s.PhoneNumber,
                Role = s.Role,
                ImageUrl = s.ImageUrl
            }).FirstOrDefaultAsync();

        // If not found, fallback to the primary contact
        if (contact == null)
        {
            contact = await partnerDbContext.SupportContacts
                .AsNoTracking() // Optimize for read-only query
                .Where(s => s.IsPrimaryContact && s.IsActive && s.FkPartnerId == partnerId)
                .OrderByDescending(s => s.Id) // Optional: you can order by Id or other criteria
                .Select(s => new SupportContactDto
                {
                    Id = s.Id,
                    ContactName = s.ContactName,
                    Email = s.Email,
                    PhoneNumber = s.PhoneNumber,
                    Role = s.Role,
                    ImageUrl = s.ImageUrl
                }).FirstOrDefaultAsync();
        }

        return contact;
    }
}