#region

using Merchant_Services.Models.ModelsDal.Merchant;
using Shared.Dto.MerchantDashboard.Event;
using Shared.Dto.MerchantDashboard.Form;
using Shared.Dto.MerchantDashboard.Order;
using Shared.Dto.MerchantDashboard.Overview;
using Shared.Dto.MerchantDashboard.User;

#endregion

namespace MerchantDashboard_Services.Services.MerchantDashboard;

public interface IMerchantDashboardService
{
    Task<MerchantDashboardOverviewMetricsDto> OverviewAsync(MerchantDashboardSearchDto search);
    Task<MerchantDashboardOverviewMetricsDto> OverviewYearToDateAsync(MerchantDashboardSearchDto search);
    Task<MerchantDashboardOrderMetricsDto> GetOrderMetricsAsync(MerchantDashboardSearchOrderDto search);
    Task<List<MerchantDashboardOrderDetailMetrics>> GetOrdersAsync(MerchantDashboardSearchOrderDto search);

    //Team
    Task<MerchantDashboardUserDto> CreateUser(MerchantDashboardUserDto user);
    Task<MerchantDashboardUserDto> UpdateUser(MerchantDashboardUserDto user);
    Task DeleteUser(int userId, int merchantId);
    Task<List<User>> GetUsers(int merchantId);

    //Event
    

    // Feedback
    Task<bool> CreateFeedback(MerchantDashboardFeedbackDto feedback);

    // Validation
    Task<bool> ValidateMerchantDashboardSettings(ValidationRequestDto request);
}