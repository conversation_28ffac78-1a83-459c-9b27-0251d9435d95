#region

using Admin_Services.Services.Admin;
using Audience.Services.Audience;
using Invoice_Services.Models.ModelsDal.Invoice;
using Merchant_Services.Models.ModelsDal.Merchant;
using Message_Services.Services.Message;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Shared.Dto.MerchantDashboard.Form;
using Shared.Dto.MerchantDashboard.Order;
using Shared.Dto.MerchantDashboard.Overview;
using Shared.Dto.MerchantDashboard.User;
using Shared.Dto.Message;
using Shared.Elastic.CampaignMailClick;
using Shared.Elastic.CampaignMailOpen;
using Shared.Elastic.DiscountOpen;
using Shared.Elastic.ShopProductsDisplays;
using Shared.Helpers.Converters;
using Shared.Helpers.Replacers;
using Shared.Models.ExternalIntegrations.ClickUp;
using Shared.Models.Merchant;
using Shared.Services.ExternalIntegrations;
using Shared.Services.Setting;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

#endregion

namespace MerchantDashboard_Services.Services.MerchantDashboard;

public class MerchantDashboardService(
    IConfiguration configuration,
    IMerchantService merchantService,
    IElasticCampaignMailOpenService elasticCampaignMailOpenService,
    IElasticCampaignMailClickService elasticCampaignMailClickService,
    IElasticDiscountOpenService elasticDiscountOpenService,
    IElasticShopProductDisplaysService elasticShopProductDisplaysService,
    IAdminService adminService,
    ISettingService settingService,
    IMessageService messageService,
    ICustomerService customerService,
    IExternalIntegrationService externalIntegrationService,
    ILogger logger)
    : IMerchantDashboardService
{
    private const int MinimumDaysAgo = 3;
    private const int OrderExposureDaysAgo = 90;
    private const int ExposureMethodCooldownPeriodInMinutes = 5;

    #region Overview Section

    public async Task<MerchantDashboardOverviewMetricsDto> OverviewAsync(MerchantDashboardSearchDto search)
    {
        var metrics = new MerchantDashboardOverviewMetricsDto
        {
            AudienceExposed = 0,
            TotalExposures = 0,
            TotalOrders = 0,
            AverageOrderValue = 0,
            TotalRevenue = 0,
            ChartData = new Dictionary<string, List<MerchantDashboardOverviewMetricsChartDataPointDto>>(),
            ComparisonMetrics = new MerchantDashboardOverviewComparisionMetrics
            {
                AudienceExposed = 0,
                TotalExposures = 0,
                TotalOrders = 0,
                AverageOrderValue = 0,
                TotalRevenue = 0
            }
        };

        var merchant = await merchantService.GetPaymentByIdAsync(search.MerchantId);

        //Normal period
        /*var customerPeriods = merchant.MerchantMeta
            .Where(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer)
            .Where(a => (a.EndDate == null || a.EndDate > search.From) && a.StartDate <= search.To)
            .OrderBy(a => a.StartDate)
            .ToList();*/

        //Last period
        var betweenDate = (search.To - search.From).Value.Days;
        var lastFrom = search.From;
        var lastPeriodFrom = lastFrom.Value.AddDays(-(betweenDate + 1));
        var lastPeriodTo = lastFrom.Value.AddDays(-1);
        var lastPeriodSearch = new MerchantDashboardSearchDto
        {
            From = lastPeriodFrom,
            To = lastPeriodTo,
            App = search.App,
            Email = search.Email,
            MerchantId = search.MerchantId
        };

        /*var customerPeriodsLastMonth = merchant.MerchantMeta
            .Where(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer)
            .Where(a => (a.EndDate == null || a.EndDate > lastPeriodFrom) && a.StartDate <= lastPeriodTo)
            .OrderBy(a => a.StartDate)
            .ToList();*/

        var start = DateTime.UtcNow;
        /*var metricsTask = MerchantDashboardCustomerPeriods(search, merchant, metrics, customerPeriods);
        var metricsLastPeriodTask = MerchantDashboardCustomerPeriods(lastPeriodSearch, merchant,
            new MerchantDashboardOverviewMetricsDto(), customerPeriodsLastMonth);*/

        var metricsTask = MerchantDashboardOverviewMetrics(search, merchant, metrics);
        var metricsLastPeriodTask = MerchantDashboardOverviewMetrics(lastPeriodSearch, merchant,
            new MerchantDashboardOverviewMetricsDto());

        var results = await Task.WhenAll(metricsTask, metricsLastPeriodTask);
        metrics = results[0];
        var metricsLastPeriod = results[1];

        metrics.ComparisonMetrics!.AudienceExposed = metricsLastPeriod.AudienceExposed;
        metrics.ComparisonMetrics!.TotalExposures = metricsLastPeriod.TotalExposures;
        metrics.ComparisonMetrics!.TotalOrders = metricsLastPeriod.TotalOrders;
        metrics.ComparisonMetrics!.AverageOrderValue = metricsLastPeriod.AverageOrderValue;
        metrics.ComparisonMetrics!.TotalRevenue = metricsLastPeriod.TotalRevenue;

        var exposuresGraphDatas = new Dictionary<DateTime, long>();
        var exposuresAppGraphDatas = new Dictionary<DateTime, long>();
        var revenuesAppGraphDatas = new Dictionary<DateTime, decimal>();
        var revenuesEmailGraphDatas = new Dictionary<DateTime, decimal>();

        ///////////////////////////////////////////////////////////
        //////////////// GRAPH DATA START /////////////////////////
        ///////////////////////////////////////////////////////////
        await CalculateGraphDataAsync(search, merchant, metrics, exposuresGraphDatas, exposuresAppGraphDatas,
            revenuesAppGraphDatas, revenuesEmailGraphDatas);

        ///////////////////////////////////////////////////////////
        //////////////// GRAPH DATA END /////////////////////////
        ///////////////////////////////////////////////////////////
        
        return metrics;
    }

    public async Task<MerchantDashboardOverviewMetricsDto> OverviewYearToDateAsync(MerchantDashboardSearchDto search)
    {
        var metrics = new MerchantDashboardOverviewMetricsDto
        {
            AudienceExposed = 0,
            TotalExposures = 0,
            TotalOrders = 0,
            AverageOrderValue = 0,
            TotalRevenue = 0,
            ChartData = new Dictionary<string, List<MerchantDashboardOverviewMetricsChartDataPointDto>>(),
        };

        var merchant = await merchantService.GetPaymentByIdAsync(search.MerchantId);
        var now = DateTime.UtcNow;
        var firstDayOfMonthTwelveMonthsAgo = new DateTime(now.Year - 1, now.Month, 1);
        search.From = new DateTime(now.Year, 1, 1);
        search.To = now;

        if(search.SelectedYear != null)
        {
            if(search.SelectedYear.Value != now.Year)
            {
                search.From = new DateTime(search.SelectedYear.Value, 1, 1);
                search.To = new DateTime(search.SelectedYear.Value, 12, 31);
            }
        }
        
        /*var customerPeriods = merchant.MerchantMeta
            .Where(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer)
            .Where(a => (a.EndDate == null || a.EndDate > search.From) && a.StartDate <= search.To)
            .OrderBy(a => a.StartDate)
            .ToList();*/

        //metrics = await MerchantDashboardCustomerPeriods(search, merchant, metrics, customerPeriods);
        metrics = await MerchantDashboardOverviewMetrics(search, merchant, metrics);

        var revenuesAppGraphDatasMonth = new Dictionary<DateTime, decimal>();
        var revenuesEmailGraphDatasMonth = new Dictionary<DateTime, decimal>();

        var revenuesAppGraphDatasYear = new Dictionary<DateTime, decimal>();
        var revenuesEmailGraphDatasYear = new Dictionary<DateTime, decimal>();
        
        if(search.SelectedYear != null)
        {
            if(search.SelectedYear.Value == now.Year)
            {
                search.From = firstDayOfMonthTwelveMonthsAgo;
            }
        }
        
        ///////////////////////////////////////////////////////////
        //////////////// GRAPH DATA START /////////////////////////
        ///////////////////////////////////////////////////////////

        await CalculateGraphDataYearlyAsync(search, merchant, metrics, revenuesAppGraphDatasMonth,
            revenuesEmailGraphDatasMonth, revenuesAppGraphDatasYear, revenuesEmailGraphDatasYear);

        ///////////////////////////////////////////////////////////
        //////////////// GRAPH DATA END /////////////////////////
        ///////////////////////////////////////////////////////////

        return metrics;
    }

    #endregion

    #region Orders Section

    public async Task<MerchantDashboardOrderMetricsDto> GetOrderMetricsAsync(MerchantDashboardSearchOrderDto search)
    {
        var metrics = new MerchantDashboardOrderMetricsDto
        {
            TotalOrders = 0,
            AverageOrderValue = 0,
            Returns = new MerchantDashboardOrderReturnsMetrics
            {
                FullReturns = 0,
                PartialReturns = 0,
                ReturnRate = 0,
                TotalReturns = 0
            },
            OrdersTable = [],
            DaysToOrder = 0,
            ExposuresPerOrder = 0
        };
        var merchant = await merchantService.GetPaymentByIdAsync(search.MerchantId);
        
        var isKeepMarketingFeeOnReturnMerchant = IsKeepMarketingFeeOnReturnMerchant(merchant);

        var customerPeriods = merchant.MerchantMeta
            .Where(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer)
            .Where(a => (a.EndDate == null || a.EndDate > search.From) && a.StartDate <= search.To)
            .OrderBy(a => a.StartDate)
            .ToList();
        var orderLines = new List<OrderLine>();

        foreach (var customerPeriod in customerPeriods)
        {
            var from = customerPeriod.StartDate;
            var to = customerPeriod.EndDate;

            if (from < search.From)
            {
                from = search.From;
            }

            if (to != null)
            {
                to = to.Value.AddDays(-1);
            }

            if (to == null || to > search.To)
            {
                to = search.To;
            }

            if (from == null || to == null)
            {
                return metrics;
            }

            orderLines.AddRange(await adminService.InvoiceLinesAsync(merchant.Id, from.Value, to.Value));
        }

        if(!isKeepMarketingFeeOnReturnMerchant)
        {
            orderLines = orderLines.Where(a => a.ValyrionStatus == "paid").ToList();
        }
        
        var totalRevenue = orderLines.Sum(a => a.TotalPrice);
        var partialRefunds = orderLines.Count(a => a.ValyrionStatus == "partialRefund");
        var fullRefund = orderLines.Count(a => a.ValyrionStatus == "fullRefund");
        metrics.Returns.FullReturns = fullRefund;
        metrics.Returns.PartialReturns = partialRefunds;
        metrics.Returns.TotalReturns = fullRefund + partialRefunds;
        metrics.TotalOrders += orderLines.Select(o => o.OrderId).Distinct().Count();
        metrics.AverageOrderValue = Number.RoundToTwoDecimalPlaces(metrics.TotalOrders != 0 && totalRevenue != 0
            ? totalRevenue / metrics.TotalOrders
            : 0);

        decimal percentageDifference = 0;
        if (metrics.TotalOrders != 0 && metrics.Returns.TotalReturns != 0)
        {
            percentageDifference = ((decimal) metrics.Returns.TotalReturns / metrics.TotalOrders) * 100;
        }

        metrics.Returns.ReturnRate = Number.RoundToTwoDecimalPlaces(percentageDifference);

        return metrics;
    }

    public async Task<List<MerchantDashboardOrderDetailMetrics>> GetOrdersAsync(MerchantDashboardSearchOrderDto search)
    {
        var minimumDaysAgo = DateTime.UtcNow.AddDays(-MinimumDaysAgo);
        var merchant = await merchantService.GetByIdAsync(search.MerchantId);

        // Get the periods where the merchant is or has been an active customer
        var customerPeriods = merchant.MerchantMeta
            .Where(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.IsCustomer)
            .Where(a => (a.EndDate == null || a.EndDate > search.From) && a.StartDate <= search.To)
            .OrderBy(a => a.StartDate)
            .ToList();
        var orderLines = new List<OrderLine>();

        var customers = await customerService.GetAllAsync();
        var customerDictionary = new Dictionary<string, List<string>>();

        foreach (var customer in customers)
        {
            if (customerDictionary.TryGetValue(customer.Email, out var value))
            {
                value.Add(customer.PhoneNumber);
            }
            else
            {
                customerDictionary[customer.Email] = [customer.PhoneNumber];
            }
        }

        // Get order lines for the period and add to the list for each customer period
        foreach (var customerPeriod in customerPeriods)
        {
            var from = customerPeriod.StartDate;
            var to = customerPeriod.EndDate;

            if (from < search.From)
            {
                from = search.From;
            }

            if (to != null)
            {
                to = to.Value.AddDays(-1);
            }

            if (to == null || to > search.To)
            {
                to = search.To;
            }

            if (from == null || to == null)
            {
                return [];
            }

            to = DateTimeExtensions.SetTimeToMax(to.Value);

            // Ensure 'to' is at least x days ago
            /*if (to > minimumDaysAgo)
            {
                to = minimumDaysAgo;
            }*/

            // Get paginated order lines for the period and add to the list
            orderLines.AddRange(await adminService.PaginatedInvoiceLinesAsync(merchant.Id, from.Value, to.Value,
                search.PageNumber, search.PageSize, search.SearchQuery));
        }

        // Group order lines by order id to make sure we only have one order per order id
        var latestOrdersList = orderLines
            .GroupBy(o => o.OrderId)
            .ToList();

        var orderMetrics = new List<MerchantDashboardOrderDetailMetrics>();
        var start = DateTime.UtcNow;

        Console.WriteLine("Calculate Metrics Start: " + start.ToString("HH:mm:ss.fff"));
        
        // Calculate metrics for each order in the paginated list to get the order details including the purchase journey
        var tasks = latestOrdersList.Select(async latestOrders =>
        {
            decimal value = 0;
            var viaAdsStatus = "";
            var skipLine = false;
            bool pending = latestOrders.Last().OrderDate > minimumDaysAgo;

            foreach (var latestOrder in latestOrders)
            {
                value += latestOrder.TotalPrice;
                viaAdsStatus = latestOrder.ValyrionStatus;
            }

            if (pending)
            {
                viaAdsStatus = "pending";
            }

            if (!skipLine)
            {
                var orderLine = latestOrders.Last();
                if (orderLine.OrderId == "653197")
                {
                    Console.WriteLine("Found");
                }
                var specifiedDaysAgoFromOrderDate = orderLine.OrderDate.AddDays(-OrderExposureDaysAgo);

                var latestCustomerExposuresMetrics =
                    await customerService.GetCustomerExposureMetrics(orderLine.Email,
                        specifiedDaysAgoFromOrderDate,
                        orderLine.OrderDate, merchant.Id);

                //Get email from customer via phone number
                var customerEmail = string.Empty;
                if (!string.IsNullOrEmpty(orderLine.Phone))
                {
                    var normalizedPhoneNumber =
                        StringManipulators.AddCountryCodeToPhoneNumber(orderLine.Phone, "45");
                    customerEmail = customerDictionary.FirstOrDefault(c => c.Value.Contains(normalizedPhoneNumber))
                        .Key;
                }

                //Check if email from phone is not null and not the same from the email so it does not just duplicate the data
                if (!string.IsNullOrEmpty(customerEmail) && customerEmail != orderLine.Email &&
                    latestCustomerExposuresMetrics.All(a => a.CustomerEmail != customerEmail))
                {
                    latestCustomerExposuresMetrics.AddRange(
                        await customerService.GetCustomerExposureMetrics(customerEmail,
                            specifiedDaysAgoFromOrderDate,
                            orderLine.OrderDate, merchant.Id));
                }

                
                var orderDetailMetrics = new MerchantDashboardOrderDetailMetrics
                {
                    CustomerInfo = new MerchantDashboardOrderCustomerInfoMetrics
                    {
                        Email = orderLine.Email,
                        Name = orderLine.CustomerName ?? "Unknown",
                        City = orderLine.CustomerCity ?? "Unknown",
                    },
                    PurchaseJourney = [],
                    OrderId = orderLine.OrderId,
                    OrderDate = orderLine.OrderDate,
                    Status = viaAdsStatus,
                    OriginalAmount = latestOrders.First().TotalPrice,
                    RefundedAmount = latestOrders.First().TotalPrice - value,
                    TotalAmount = value,
                    LastExposureToOrder = 0
                };

                var exposureEmails = new List<string>();

                if (!string.IsNullOrEmpty(customerEmail))
                {
                    exposureEmails.Add(customerEmail);
                }

                exposureEmails.Add(orderLine.Email);

                foreach (var exposureEmail in exposureEmails)
                {
                    var exposuresByEmail = latestCustomerExposuresMetrics
                        .Where(e => e.Date <= orderLine.OrderDate)
                        .GroupBy(e => e.CustomerEmail)
                        .ToDictionary(g => g.Key, g =>
                        {
                            var exposureMetrics = g.OrderBy(e => e.Date).ToList();
                            return exposureMetrics.Where((e, i) =>
                                i == 0 || (e.ExposureMethod == "Display" &&
                                           (e.Date - exposureMetrics[i - 1].Date).TotalMinutes >=
                                           ExposureMethodCooldownPeriodInMinutes) ||
                                e.ExposureMethod != exposureMetrics[i - 1].ExposureMethod ||
                                e.ExposureMethod == "Redirect" ||
                                e.ExposureMethod == "Interact").ToList();
                        });

                    if (exposuresByEmail.TryGetValue(exposureEmail, out var exposures)) 
                    {
                        var orderedExposures = exposures.Where(e => e.Date <= orderLine.OrderDate).ToList();

                        foreach (var exposureMetric in orderedExposures)
                        {
                            orderDetailMetrics.PurchaseJourney.Add(new MerchantDashboardOrderPurchaseJourneyStepMetrics 
                            {
                                Channel = exposureMetric.Channel,
                                Date = exposureMetric.Date,
                                Step = "Exposure"
                            });
                        }   
                    }
                }
                
                orderDetailMetrics.PurchaseJourney = orderDetailMetrics.PurchaseJourney.OrderBy(a => a.Date).ToList();

                orderDetailMetrics.PurchaseJourney.Add(new MerchantDashboardOrderPurchaseJourneyStepMetrics
                {
                    Channel = "",
                    Date = orderLine.OrderDate,
                    Step = "Order placed"
                });

                // Get all ordered exposures across all emails
                var allOrderedExposures = latestCustomerExposuresMetrics
                    .Where(e => e.Date <= orderLine.OrderDate && exposureEmails.Contains(e.CustomerEmail))
                    .OrderBy(e => e.Date)
                    .ToList();

                if (allOrderedExposures.Count != 0)
                {
                    var lastExposureDate = allOrderedExposures.Last().Date;
                    orderDetailMetrics.LastExposureToOrder = (orderLine.OrderDate - lastExposureDate).Days;
                }

                lock (orderMetrics)
                {
                    orderMetrics.Add(orderDetailMetrics);
                }
            }
        }).ToList();

        await Task.WhenAll(tasks);
        

        Console.WriteLine("Calculate Metrics Elapsed: " + (DateTime.UtcNow - start).TotalMilliseconds);

        return orderMetrics.OrderByDescending(a => a.OrderDate).ToList();
    }
    
    private bool IsKeepMarketingFeeOnReturnMerchant(Merchant merchant)
    {
        var model = merchant.MerchantMeta.FirstOrDefault(a =>
            a.FkMerchantMetaTypeName == MerchantMetaTypeNames.KeepMarketingFeeOnReturns);
        return model?.Value == "true";
    }

    #endregion

    #region Team Section

    public async Task<MerchantDashboardUserDto> CreateUser(MerchantDashboardUserDto userDto)
    {
        var user = await merchantService.GetMerchantUserByEmailAsync(userDto.Email) ?? new User();
        var merchant = await merchantService.GetByIdAsync(userDto.FkMerchantId);
        var newUser = user.Id == 0;
        var addedNewMerchant = false;
        user.Email = userDto.Email;
        user.FirstName = userDto.FirstName;
        user.LastName = userDto.LastName;
        var merchantUser = user.MerchantUsersRels.SingleOrDefault(a => a.FkMerchantId == userDto.FkMerchantId);
        if (merchantUser == null)
        {
            user.MerchantUsersRels.Add(new MerchantUsersRel
            {
                FkMerchantId = userDto.FkMerchantId,
                Active = true,
                FkUserGroupId = userDto.FkUserGroupId,
            });
            addedNewMerchant = true;
        }
        else
        {
            if (merchantUser.Active == false && userDto.Active)
            {
                addedNewMerchant = true;
            }

            merchantUser.Active = userDto.Active;
            merchantUser.FkUserGroupId = userDto.FkUserGroupId;
        }

        if (newUser)
        {
            user.Password = Guid.NewGuid().ToString();
            user.Active = true;
        }

        await merchantService.CrudMerchantUserAsync(user);

        if (!newUser && !addedNewMerchant) return userDto;

        var serviceEmailDto = new ServiceEmailDto
        {
            SenderName = await settingService.GetSettingValueAsync(merchant.FkPartnerId, "SenderName"),
            SenderEmail = await settingService.GetSettingValueAsync(merchant.FkPartnerId, "SenderEmail"),
            Recipient = new EmailRecipientDto
            {
                Email = user.Email ?? "",
                FirstName = user.FirstName ?? "",
                LastName = user.LastName ?? ""
            },
            Parameters = new Dictionary<string, string>()
            {
                {"WebshopName", merchant!.DisplayName}
            }
        };

        if (addedNewMerchant && !newUser)
        {
            serviceEmailDto.CampaignId = Convert.ToInt32(
                await settingService.GetSettingValueAsync(merchant.FkPartnerId, "AddedToDashboardCampaignId"));
            serviceEmailDto.Parameters.Add("PartnerPortalUrl",
                await settingService.GetSettingValueAsync(merchant.FkPartnerId, "BaseUrl"));

            await messageService.SendServiceEmailAsync(serviceEmailDto);
        }

        if (newUser)
        {
            var token = await merchantService.CreateMerchantUsersTokenAsync(user);

            serviceEmailDto.CampaignId = Convert.ToInt32(
                await settingService.GetSettingValueAsync(merchant.FkPartnerId, "ActivateAccountCampaignId"));
            serviceEmailDto.Parameters.Add("ActivateAccountUrl",
                await settingService.GetSettingValueAsync(merchant.FkPartnerId, "ActivateAccountUrl"));
            serviceEmailDto.Parameters.Add("ActivateAccountToken", token.Token);

            await messageService.SendServiceEmailAsync(serviceEmailDto);
        }

        return userDto;
    }

    public async Task<MerchantDashboardUserDto> UpdateUser(MerchantDashboardUserDto userDto)
    {
        var user = await merchantService.GetMerchantUserByEmailAsync(userDto.Email);
        if (user != null)
        {
            user.FirstName = userDto.FirstName;
            user.LastName = userDto.LastName;
            var merchantUser = user.MerchantUsersRels.SingleOrDefault(a => a.FkMerchantId == userDto.FkMerchantId);
            if (merchantUser != null)
            {
                merchantUser.Active = userDto.Active;
                merchantUser.FkUserGroupId = userDto.FkUserGroupId;
            }

            await merchantService.CrudMerchantUserAsync(user);
        }

        return userDto;
    }

    public async Task DeleteUser(int userId, int merchantId)
    {
        var users = await merchantService.GetMerchantUserAsync(merchantId);
        var user = users.SingleOrDefault(a => a.Id == userId);
        if (user != null)
        {
            var merchantUser = user.MerchantUsersRels.SingleOrDefault(a => a.FkMerchantId == merchantId);
            if (merchantUser != null)
            {
                merchantUser.Active = false;
            }

            await merchantService.CrudMerchantUserAsync(user);
        }
    }

    public async Task<List<User>> GetUsers(int merchantId)
    {
        return await merchantService.GetMerchantUserAsync(merchantId);
    }

    #endregion

    #region Private Methods

    private async Task CalculateGraphDataAsync(MerchantDashboardSearchDto search, Merchant merchant,
        MerchantDashboardOverviewMetricsDto metrics, Dictionary<DateTime, long> exposuresGraphDatas,
        Dictionary<DateTime, long> exposuresAppGraphDatas, Dictionary<DateTime, decimal> revenuesAppGraphDatas,
        Dictionary<DateTime, decimal> revenuesEmailGraphDatas)
    {
        var from = search.From;
        var to = search.To;

        if (from == null || to == null)
        {
            return;
        }

        to = DateTimeExtensions.SetTimeToMax(to.Value);

        //var includeRefunds = merchant.MerchantMeta.SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.KeepMarketingFeeOnReturns)?.Value == "false";
        var includeRefunds = false;

        if (merchant.MerchantPayments.First().DisplayInvoice || merchant.MerchantMeta
                .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType)?.Value
                .ToLowerInvariant() == MerchantAttributionTypes.CPM)
        {
            //Display Merchant
            if (search.Email)
            {
                foreach (var value in await elasticCampaignMailOpenService.OpensDayCount(from.Value, to.Value,
                             merchant.Id))
                {
                    if (!exposuresGraphDatas.ContainsKey(value.Key))
                    {
                        exposuresGraphDatas[value.Key] = value.Value;
                    }
                }
            }
        }
        else
        {
            //Click merchant
            if (search.Email)
            {
                foreach (var value in await elasticCampaignMailClickService.ClicksDayCount(from.Value, to.Value,
                             merchant.Id))
                {
                    if (!exposuresGraphDatas.ContainsKey(value.Key))
                    {
                        exposuresGraphDatas[value.Key] = value.Value;
                    }
                }
            }
        }

        if (search.Email)
        {
            foreach (var value in await adminService.InvoiceLinesDayCountAsync(merchant.Id, "Campaign", from.Value,
                         to.Value, includeRefunds))
            {
                if (!revenuesEmailGraphDatas.ContainsKey(value.Key))
                {
                    revenuesEmailGraphDatas[value.Key] = value.Value;
                }
            }
        }

        if (search.App)
        {
            foreach (var value in await elasticDiscountOpenService.OpensDayCount(from.Value, to.Value, merchant.Id))
            {
                if (!exposuresAppGraphDatas.ContainsKey(value.Key))
                {
                    exposuresAppGraphDatas[value.Key] = value.Value;
                }
                else
                {
                    exposuresAppGraphDatas[value.Key] += value.Value;
                }
            }

            foreach (var value in await elasticShopProductDisplaysService.DisplaysDayCount(from.Value, to.Value,
                         merchant.Id))
            {
                if (!exposuresAppGraphDatas.ContainsKey(value.Key))
                {
                    exposuresAppGraphDatas[value.Key] = value.Value;
                }
                else
                {
                    exposuresAppGraphDatas[value.Key] += value.Value;
                }
            }

            foreach (var value in await adminService.InvoiceLinesDayCountAsync(merchant.Id, "Discount", from.Value,
                         to.Value, includeRefunds))
            {
                if (!revenuesAppGraphDatas.ContainsKey(value.Key))
                {
                    revenuesAppGraphDatas[value.Key] = value.Value;
                }
                else
                {
                    revenuesAppGraphDatas[value.Key] += value.Value;
                }
            }

            foreach (var value in await adminService.InvoiceLinesDayCountAsync(merchant.Id, "Product", from.Value,
                         to.Value, includeRefunds))
            {
                if (!revenuesAppGraphDatas.ContainsKey(value.Key))
                {
                    revenuesAppGraphDatas[value.Key] = value.Value;
                }
                else
                {
                    revenuesAppGraphDatas[value.Key] += value.Value;
                }
            }
        }

        decimal accumulatedCurrentMonthExposures = 0;
        decimal accumulatedCurrentMonthRevenue = 0;
        metrics.ChartData.Add("exposures", new List<MerchantDashboardOverviewMetricsChartDataPointDto>());
        metrics.ChartData.Add("revenue", new List<MerchantDashboardOverviewMetricsChartDataPointDto>());
        var currentMonth = from.Value;

        while (currentMonth <= to.Value)
        {
            var currentDayExposure = exposuresGraphDatas.SingleOrDefault(a => a.Key.Date == currentMonth.Date);
            var currentDayEmailRevenue = revenuesEmailGraphDatas.SingleOrDefault(a => a.Key.Date == currentMonth.Date);
            var currentDayAppRevenue = revenuesAppGraphDatas.SingleOrDefault(a => a.Key.Date == currentMonth.Date);
            var currentDayExposureDiscount =
                exposuresAppGraphDatas.SingleOrDefault(a => a.Key.Date == currentMonth.Date);

            accumulatedCurrentMonthExposures += currentDayExposure.Value;
            accumulatedCurrentMonthRevenue += currentDayEmailRevenue.Value;
            accumulatedCurrentMonthExposures += currentDayExposureDiscount.Value;
            accumulatedCurrentMonthRevenue += currentDayAppRevenue.Value;

            metrics.ChartData["exposures"].Add(new MerchantDashboardOverviewMetricsChartDataPointDto
            {
                Value = accumulatedCurrentMonthExposures,
                Label = $"{currentMonth:MMM dd}"
            });
            metrics.ChartData["revenue"].Add(new MerchantDashboardOverviewMetricsChartDataPointDto
            {
                Value = accumulatedCurrentMonthRevenue,
                Label = $"{currentMonth:MMM dd}"
            });

            currentMonth = currentMonth.AddDays(1);
        }
    }

    private async Task CalculateGraphDataYearlyAsync(MerchantDashboardSearchDto search, Merchant merchant,
        MerchantDashboardOverviewMetricsDto metrics, Dictionary<DateTime, decimal> revenuesAppGraphDatasMonth,
        Dictionary<DateTime, decimal> revenuesEmailGraphDatasMonth,
        Dictionary<DateTime, decimal> revenuesAppGraphDatasYear,
        Dictionary<DateTime, decimal> revenuesEmailGraphDatasYear)
    {
        var from = search.From;
        var to = search.To;

        if (from == null || to == null)
        {
            return;
        }

        to = DateTimeExtensions.SetTimeToMax(to.Value);

        var fromYear = new DateTime(to.Value.Year - 3, 1, 1);
        if(search.SelectedYear != null && search.SelectedYear.Value != DateTime.Now.Year)
        {
            fromYear = new DateTime(search.SelectedYear.Value, 1, 1);
        }

        //var includeRefunds = merchant.MerchantMeta.SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.KeepMarketingFeeOnReturns)?.Value == "false";
        var includeRefunds = false;

        if (search.Email)
        {
            foreach (var value in await adminService.InvoiceLinesMonthCountAsync(merchant.Id, "Campaign", from.Value,
                         to.Value, includeRefunds))
            {
                if (!revenuesEmailGraphDatasMonth.ContainsKey(value.Key))
                {
                    revenuesEmailGraphDatasMonth[value.Key] = value.Value;
                }
            }

            foreach (var value in await adminService.InvoiceLinesYearCountAsync(merchant.Id, "Campaign", fromYear,
                         to.Value, includeRefunds))
            {
                if (!revenuesEmailGraphDatasYear.ContainsKey(value.Key))
                {
                    revenuesEmailGraphDatasYear[value.Key] = value.Value;
                }
            }
        }

        if (search.App)
        {
            foreach (var value in await adminService.InvoiceLinesMonthCountAsync(merchant.Id, "Discount", from.Value,
                         to.Value, includeRefunds))
            {
                if (!revenuesAppGraphDatasMonth.ContainsKey(value.Key))
                {
                    revenuesAppGraphDatasMonth[value.Key] = value.Value;
                }
            }

            foreach (var value in await adminService.InvoiceLinesYearCountAsync(merchant.Id, "Discount", fromYear,
                         to.Value, includeRefunds))
            {
                if (!revenuesAppGraphDatasYear.ContainsKey(value.Key))
                {
                    revenuesAppGraphDatasYear[value.Key] = value.Value;
                }
            }

            foreach (var value in await adminService.InvoiceLinesMonthCountAsync(merchant.Id, "Product", from.Value,
                         to.Value, includeRefunds))
            {
                if (!revenuesAppGraphDatasMonth.ContainsKey(value.Key))
                {
                    revenuesAppGraphDatasMonth[value.Key] = value.Value;
                }
                else
                {
                    revenuesAppGraphDatasMonth[value.Key] += value.Value;
                }
            }

            foreach (var value in await adminService.InvoiceLinesYearCountAsync(merchant.Id, "Product", fromYear,
                         to.Value, includeRefunds))
            {
                if (!revenuesAppGraphDatasYear.ContainsKey(value.Key))
                {
                    revenuesAppGraphDatasYear[value.Key] = value.Value;
                }
                else
                {
                    revenuesAppGraphDatasYear[value.Key] += value.Value;
                }
            }
        }

        metrics.ChartData.Add("revenueYear", []);
        while (fromYear <= to.Value)
        {
            var currentYearApp = revenuesAppGraphDatasYear.SingleOrDefault(a => a.Key.Year == fromYear.Year);
            var currentYearEmail = revenuesEmailGraphDatasYear.SingleOrDefault(a => a.Key.Year == fromYear.Year);
            decimal amount = 0;
            amount += currentYearApp.Value;
            amount += currentYearEmail.Value;

            if (amount > 0)
            {
                metrics.ChartData["revenueYear"].Add(new MerchantDashboardOverviewMetricsChartDataPointDto
                {
                    Value = amount,
                    Label = fromYear.Year.ToString()
                });
            }

            fromYear = fromYear.AddYears(1);
        }

        var fromMonth = from;
        metrics.ChartData.Add("revenueMonth", []);
        while (fromMonth <= to.Value)
        {
            var currentMonthApp = revenuesAppGraphDatasMonth.SingleOrDefault(a => a.Key.Date == fromMonth.Value.Date);
            var currentMonthEmail =
                revenuesEmailGraphDatasMonth.SingleOrDefault(a => a.Key.Date == fromMonth.Value.Date);
            decimal amount = 0;
            amount += currentMonthApp.Value;
            amount += currentMonthEmail.Value;

            if (amount > 0)
            {
                metrics.ChartData["revenueMonth"].Add(new MerchantDashboardOverviewMetricsChartDataPointDto
                {
                    Value = amount,
                    Label = fromMonth.Value.ToString("MMMM y")
                });
            }

            fromMonth = fromMonth.Value.AddMonths(1);
        }
    }

    private async Task<MerchantDashboardOverviewMetricsDto> MerchantDashboardOverviewMetrics(
        MerchantDashboardSearchDto search, Merchant merchant, MerchantDashboardOverviewMetricsDto metrics)
    {
        var tasksTotalExposures = new List<Task<long>>();
        var tasksAudienceExposures = new List<Task<long>>();

        var from = search.From;
        var to = search.To;

        if (from == null || to == null)
        {
            return metrics;
        }

        to = DateTimeExtensions.SetTimeToMax(to.Value);

        //TotalExposures and AudienceExposed (DisplayInvoice and CPM)
        if ((merchant.MerchantPayments?.FirstOrDefault()?.DisplayInvoice ?? false) ||
            (merchant.MerchantMeta?.SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType)?.Value
                ?.ToLowerInvariant() == MerchantAttributionTypes.CPM))
        {
            //Display Merchant
            if (search.Email)
            {
                var totalExposuresTask = elasticCampaignMailOpenService.Opens(from, to, merchant.Id);
                var audienceExposedTask = elasticCampaignMailOpenService.UniqueOpens(from, to, merchant.Id);
                tasksTotalExposures.Add(totalExposuresTask);
                tasksAudienceExposures.Add(audienceExposedTask);
            }
        }
        else
        {
            //Click merchant
            if (search.Email)
            {
                var totalExposuresTask = elasticCampaignMailClickService.Clicks(from, to, merchant.Id);
                var audienceExposedTask = elasticCampaignMailClickService.UniqueClicks(from, to, merchant.Id);
                tasksTotalExposures.Add(totalExposuresTask);
                tasksAudienceExposures.Add(audienceExposedTask);
            }
        }

        if (search.App)
        {
            var totalExposuresTaskDiscounts = elasticDiscountOpenService.Opens(from, to, merchant.Id);
            var totalExposuresTaskProducts = elasticShopProductDisplaysService.Displays(from, to, merchant.Id);
            var audienceExposedTaskDiscounts = elasticDiscountOpenService.UniqueOpens(from, to, merchant.Id);
            var audienceExposedTaskProducts = elasticShopProductDisplaysService.UniqueDisplays(from, to, merchant.Id);

            tasksTotalExposures.Add(totalExposuresTaskDiscounts);
            tasksTotalExposures.Add(totalExposuresTaskProducts);
            tasksAudienceExposures.Add(audienceExposedTaskDiscounts);
            tasksAudienceExposures.Add(audienceExposedTaskProducts);
        }

        // Wait for all tasks to complete
        var allTasks = tasksTotalExposures.Concat(tasksAudienceExposures).ToList();
        var allResults = await Task.WhenAll(allTasks);
        int totalExposuresCount = tasksTotalExposures.Count;

        for (int i = 0; i < totalExposuresCount; i++)
        {
            metrics.TotalExposures += allResults[i];
        }

        for (int i = totalExposuresCount; i < allResults.Length; i++)
        {
            metrics.AudienceExposed += allResults[i];
        }

        var options = new DbContextOptionsBuilder<InvoiceDbContext>()
            .UseSqlServer(configuration["Connection-string"])
            .Options;
        var invoiceDbContext = new InvoiceDbContext(options);

        //TotalOrders, TotalRevenue
        var orderLines = new List<OrderLine>();
        if (search.Email)
        {
            orderLines.AddRange(await adminService.InvoiceLinesAsync(merchant.Id, "Campaign", from.Value, to.Value,
                invoiceDbContext));
        }

        if (search.App)
        {
            orderLines.AddRange(await adminService.InvoiceLinesAsync(merchant.Id, "Discount", from.Value, to.Value,
                invoiceDbContext));
            orderLines.AddRange(await adminService.InvoiceLinesAsync(merchant.Id, "Product", from.Value, to.Value,
                invoiceDbContext));
        }

        //Remove orders there is within 3 days
        //orderLines = orderLines.Where(a => a.OrderDate <= newer3Days).ToList();
        
        // Only take the first order line for each order
        orderLines = orderLines.GroupBy(a => a.OrderId).Select(g => g.First()).ToList();

        metrics.TotalRevenue += orderLines.Sum(a => a.TotalPrice);
        metrics.TotalOrders += orderLines.Select(o => o.OrderId).Distinct().Count();

        //AverageOrder value
        metrics.AverageOrderValue = metrics.TotalOrders != 0 && metrics.TotalRevenue != 0
            ? metrics.TotalRevenue / metrics.TotalOrders
            : 0;
        return metrics;
    }


    private async Task<MerchantDashboardOverviewMetricsDto> MerchantDashboardCustomerPeriods(
        MerchantDashboardSearchDto search, Merchant merchant, MerchantDashboardOverviewMetricsDto metrics,
        List<MerchantMetum> customerPeriods)
    {
        //var newer3Days = DateTime.UtcNow.AddDays(-3);
        var tasksTotalExposures = new List<Task<long>>();
        var tasksAudienceExposures = new List<Task<long>>();

        foreach (var customerPeriod in customerPeriods)
        {
            var from = customerPeriod.StartDate;
            var to = customerPeriod.EndDate;

            if (from < search.From)
            {
                from = search.From;
            }

            if (to != null)
            {
                to = to.Value.AddDays(-1);
            }

            if (to == null || to > search.To)
            {
                to = search.To;
            }

            if (from == null || to == null)
            {
                return metrics;
            }

            to = DateTimeExtensions.SetTimeToMax(to.Value);

            //TotalExposures and AudienceExposed (DisplayInvoice and CPM)
            if (merchant.MerchantPayments.First().DisplayInvoice || merchant.MerchantMeta
                    .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType)?.Value
                    .ToLowerInvariant() == MerchantAttributionTypes.CPM)
            {
                //Display Merchant
                if (search.Email)
                {
                    var totalExposuresTask = elasticCampaignMailOpenService.Opens(from, to, merchant.Id);
                    var audienceExposedTask = elasticCampaignMailOpenService.UniqueOpens(from, to, merchant.Id);
                    tasksTotalExposures.Add(totalExposuresTask);
                    tasksAudienceExposures.Add(audienceExposedTask);
                }
            }
            else
            {
                //Click merchant
                if (search.Email)
                {
                    var totalExposuresTask = elasticCampaignMailClickService.Clicks(from, to, merchant.Id);
                    var audienceExposedTask = elasticCampaignMailClickService.UniqueClicks(from, to, merchant.Id);
                    tasksTotalExposures.Add(totalExposuresTask);
                    tasksAudienceExposures.Add(audienceExposedTask);
                }
            }

            if (search.App)
            {
                var totalExposuresTask = elasticDiscountOpenService.Opens(from, to, merchant.Id);
                var audienceExposedTask = elasticDiscountOpenService.UniqueOpens(from, to, merchant.Id);

                tasksTotalExposures.Add(totalExposuresTask);
                tasksAudienceExposures.Add(audienceExposedTask);
            }

            // Wait for all tasks to complete
            var allTasks = tasksTotalExposures.Concat(tasksAudienceExposures).ToList();
            var allResults = await Task.WhenAll(allTasks);
            int totalExposuresCount = tasksTotalExposures.Count;

            for (int i = 0; i < totalExposuresCount; i++)
            {
                metrics.TotalExposures += allResults[i];
            }

            for (int i = totalExposuresCount; i < allResults.Length; i++)
            {
                metrics.AudienceExposed += allResults[i];
            }

            var options = new DbContextOptionsBuilder<InvoiceDbContext>()
                .UseSqlServer(configuration["Connection-string"])
                .Options;
            var invoiceDbContext = new InvoiceDbContext(options);

            //TotalOrders, TotalRevenue
            var orderLines = new List<OrderLine>();
            if (search.Email)
            {
                orderLines.AddRange(await adminService.InvoiceLinesAsync(merchant.Id, "Campaign", from.Value, to.Value,
                    invoiceDbContext));
            }

            if (search.App)
            {
                orderLines.AddRange(await adminService.InvoiceLinesAsync(merchant.Id, "Discount", from.Value, to.Value,
                    invoiceDbContext));
                orderLines.AddRange(await adminService.InvoiceLinesAsync(merchant.Id, "Product", from.Value, to.Value,
                    invoiceDbContext));
            }

            //Remove orders there is within 3 days
            //orderLines = orderLines.Where(a => a.OrderDate <= newer3Days).ToList();
            metrics.TotalRevenue += orderLines.Sum(a => a.TotalPrice);
            metrics.TotalOrders += orderLines.Select(o => o.OrderId).Distinct().Count();
        }

        //AverageOrder value
        metrics.AverageOrderValue = metrics.TotalOrders != 0 && metrics.TotalRevenue != 0
            ? metrics.TotalRevenue / metrics.TotalOrders
            : 0;
        return metrics;
    }

    #endregion


    #region Feedback

    public async Task<bool> CreateFeedback(MerchantDashboardFeedbackDto feedback)
    {
        var merchant = await merchantService.GetByIdAsync(feedback.MerchantId);

        var feedbackTask = new ClickUpTask
        {
            name = feedback.Title,
            description = feedback.Description,
            custom_fields = new object[]
            {
                new
                {
                    id = "3ae67839-726b-4956-8ee7-667f309e6a85",
                    value = merchant?.Name ?? ""
                },
                new
                {
                    id = "5a1b7539-3a05-4b5d-b0aa-264d1f787873",
                    value = feedback.MerchantId.ToString()
                },
                new
                {
                    id = "abf7ba29-a30e-4de9-987b-3fc4592fb945",
                    value = feedback.MerchantEmail
                }
            }
        };

        // TODO - Change to dynamic PartnerId instead og Hardcoded
        var listId = await settingService.GetSettingValueAsync(52876, "Integrations_ClickUp_MerchantFeedback_ListId");

        return await externalIntegrationService.PostDataAsync($"list/{listId}/task", feedbackTask);
    }

    #endregion

    #region Validation

    public async Task<bool> ValidateMerchantDashboardSettings(ValidationRequestDto request)
    {
        var isValid = false;

        switch (request.CmsType.ToLowerInvariant())
        {
            case "woocommerce":
                isValid = await ValidateWooCommerceSettings(request);
                break;
            default:
                isValid = false;
                break;
        }

        return isValid;
    }

    private async Task<bool> ValidateWooCommerceSettings(ValidationRequestDto request)
    {
        var isValid = false;

        var merchant = await merchantService.GetByIdAsync(request.MerchantId);

        if (merchant == null)
        {
            return false;
        }

        try
        {
            // Try to Fetch WooCommerce User Key and User Secret from ComProperties
            var userKey = request.ValidationProperties["ApiUserKey"];
            var userSecret = request.ValidationProperties["ApiUserSecret"];

            if (string.IsNullOrEmpty(userKey) || string.IsNullOrEmpty(userSecret))
            {
                return false;
            }

            // Validate WooCommerce User Key and User Secret
            var url = merchant.Url.EndsWith("/") ? merchant.Url.TrimEnd('/') : merchant.Url;
            url = $"{url}/wp-json/wc/v3/orders?per_page=1";

            if (!url.StartsWith("https://"))
                url = $"https://{url}";     

            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");
                
                // Add Basic Authentication
                var authString = $"{userKey}:{userSecret}";
                var authHeader = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(authString));
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authHeader);

                var response = await client.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    isValid = true;
                }
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while validating WooCommerce credentials");
            return false;
        }

        return isValid;
    }
    

    #endregion
}