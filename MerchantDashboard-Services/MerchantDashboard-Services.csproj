<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <RootNamespace>MerchantDashboard_Services</RootNamespace>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\General-Services\General-Services.csproj" />
        <ProjectReference Include="..\Invoice-Services\Invoice-Services.csproj" />
        <ProjectReference Include="..\Merchant-Services\Merchant-Services.csproj" />
        <ProjectReference Include="..\Partner-Services\Partner-Services.csproj" />
        <ProjectReference Include="..\PartnerPortal-Services\PartnerPortal-Services.csproj" />
        <ProjectReference Include="..\Shared\Shared.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Services\General\" />
    </ItemGroup>

</Project>