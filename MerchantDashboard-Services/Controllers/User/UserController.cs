using MerchantDashboard_Services.Helpers;
using MerchantDashboard_Services.Services.MerchantDashboard;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Shared.Dto.MerchantDashboard.User;
using Shared.Dto.Webshop;
using Shared.Models;
using ILogger = Serilog.ILogger;

namespace MerchantDashboard_Services.Controllers.User;

[Authorize(Roles = "partner")]
[Route("MerchantDashboard/User")]
[ApiController]
public class MerchantDashboardUserController(
    ILogger logger,
    IMerchantDashboardService merchantDashboardService,
    IConfiguration configuration)
    : ControllerBase
{
    [HttpGet]
    [Authorize(Roles = "partner")]
    [Route("{merchantId:int}")]
    public async Task<IActionResult> GetUsers(int merchantId)
    {
        try
        {
            if (!Validators.ValidateTokenMerchant(Request, merchantId, configuration["JwtSecretLogin"] ?? "", true))
            {
                return Forbid();
            }

            var users = await merchantDashboardService.GetUsers(merchantId);

            // Mapping to MerchantUserPartnerPortalDto to return only necessary fields
            var webshopsUserPartnerPortal = (from user in users
                let webshopUser = user.MerchantUsersRels.Single(a => a.Active && a.FkMerchantId == merchantId)
                select new MerchantUserPartnerPortalDto
                {
                    FkMerchantId = webshopUser.FkMerchantId,
                    FkUserGroupId = webshopUser.FkUserGroupId,
                    Active = user.Active,
                    Email = user.Email ?? "",
                    FirstName = user.FirstName ?? "",
                    LastName = user.LastName ?? "",
                    Id = user.Id
                }).ToList();

            return Ok(webshopsUserPartnerPortal);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting GetUsers");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPost]
    public async Task<IActionResult> CreateUser(MerchantDashboardUserDto merchantUser)
    {
        try
        {
            if (!Validators.ValidateTokenMerchant(Request, merchantUser.FkMerchantId,
                    configuration["JwtSecretLogin"] ?? "", true))
            {
                return Forbid();
            }

            return Ok(await merchantDashboardService.CreateUser(merchantUser));
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting CrudUsers");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpPut]
    public async Task<IActionResult> UpdateUser(MerchantDashboardUserDto merchantUser)
    {
        try
        {
            if (!Validators.ValidateTokenMerchant(Request, merchantUser.FkMerchantId,
                    configuration["JwtSecretLogin"] ?? "", true))
            {
                return Forbid();
            }

            return Ok(await merchantDashboardService.UpdateUser(merchantUser));
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting CrudUsers");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpDelete]
    [Route("{userId:int}/{merchantId:int}")]
    public async Task<IActionResult> DeleteUser(int userId, int merchantId)
    {
        try
        {
            if (!Validators.ValidateTokenMerchant(Request, merchantId, configuration["JwtSecretLogin"] ?? "", true))
            {
                return Forbid();
            }

            await merchantDashboardService.DeleteUser(userId, merchantId);
            return Ok();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while getting CrudUsers");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }
}