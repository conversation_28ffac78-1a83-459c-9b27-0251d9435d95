using MerchantDashboard_Services.Helpers;
using MerchantDashboard_Services.Services.Event;
using MerchantDashboard_Services.Services.MerchantDashboard;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Dto.MerchantDashboard.Event;
using Shared.Models;
using ILogger = Serilog.ILogger;

namespace MerchantDashboard_Services.Controllers.Event;

[Authorize(Roles = "partner")]
[Route("MerchantDashboard/Event")]
[ApiController]
public class MerchantDashboardEventController(
    ILogger logger,
    IMerchantDashboardEventService merchantDashboardEventService,
    IConfiguration configuration)
    : ControllerBase
{
    [HttpPost]
    [PartnerAuthExempt]
    public async Task<IActionResult> CreateEvent(MerchantDashboardEventDto merchantDashboardEventDto)
    {
        try
        {
            if (!Validators.ValidateTokenMerchant(Request, Convert.ToInt32(merchantDashboardEventDto.MerchantId),
                    configuration["JwtSecretLogin"] ?? ""))
            {
                return Forbid();
            }

            var token = Request.Headers.SingleOrDefault(a => a.Key == "Authorization");
            var jwtToken = Validators.ValidateAndFetchToken(token.Value, configuration["JwtSecretLogin"] ?? "");
            var userId = jwtToken.Claims.Single(a => a.Type == "uid").Value;
            var email = jwtToken.Claims.Single(a => a.Type == "email").Value;
            await merchantDashboardEventService.CreateEvent(merchantDashboardEventDto, email, userId).ConfigureAwait(false);

            return Ok();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while creating Merchant Event");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }
    
    [HttpGet]
    [Route("TermsAndConditions/{merchantId}")]
    public async Task<IActionResult> CreateTermsAndConditionsAcceptEvent(string merchantId)
    {
        try
        {
            
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CreateTermsAndConditionsAcceptEvent"))
            {
                var merchantIdIntValue = Convert.ToInt32(merchantId);
                if (!Validators.ValidateTokenMerchant(Request, merchantIdIntValue, configuration["JwtSecretLogin"] ?? ""))
                {
                    return Forbid();
                }

                var token = Request.Headers.SingleOrDefault(a => a.Key == "Authorization");
                var jwtToken = Validators.ValidateAndFetchToken(token.Value, configuration["JwtSecretLogin"] ?? "");
                var userId = jwtToken.Claims.Single(a => a.Type == "uid")?.Value;
                var email = jwtToken.Claims.Single(a => a.Type == "email").Value;
                await merchantDashboardEventService.CreateTermsAndConditionsAcceptEvent(merchantIdIntValue, email, userId).ConfigureAwait(false);

                return Ok();   
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while creating Merchant TermsAndConditionsAccept Event");
            return BadRequest(new ErrorDto(ex.ToString()));
        }
    }
}