using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Partner_Services.Services.General;
using Serilog;
using Shared.Attributes;
using Shared.Constants;
using Shared.Models;

namespace MerchantDashboard_Services.Controllers.General;

[Route("merchantDashboard/general")]
public class MerchantDashboardGeneralController(IPartnerService partnerService, ILogger logger) : ControllerBase
{
    [HttpGet("partner-logo/{partnerId:int}")]
    [Authorize(Roles = "partner")]
    [AllowAnonymous]
    [PartnerAuthExempt]
    public async Task<IActionResult> GetPartnerAsset(int partnerId)
    {
        try
        {
            var asset = await partnerService.GetPartnerAssetAsync(partnerId, PartnerAssetTypes.Logo);
            return Ok(asset);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while fetching partner logo");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet("color-scheme/{partnerId:int}")]
    [Authorize(Roles = "partner")]
    [AllowAnonymous]
    public async Task<IActionResult> GetColorScheme(int partnerId)
    {
        try
        {
            var colorScheme = await partnerService.GetColorSchemeAsync(partnerId);
            return Ok(colorScheme);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while fetching color scheme");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet("partner-config/{partnerId:int}")]
    [Authorize(Roles = "partner")]
    [AllowAnonymous]
    public async Task<IActionResult> GetPartnerConfig(int partnerId)
    {
        try
        {
            var partnerConfig = await partnerService.GetPartnerConfigAsync(partnerId);
            return Ok(partnerConfig);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while fetching partner config");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }
}