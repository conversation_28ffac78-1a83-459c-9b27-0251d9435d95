using MerchantDashboard_Services.Services.Support;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shared.Models;
using ILogger = Serilog.ILogger;

namespace MerchantDashboard_Services.Controllers.Support;

[Authorize(Roles = "partner")]
[Route("MerchantDashboard/support")]
[ApiController]
public class MerchantDashboardSupportCenterController(ISupportCenterService supportCenterService, ILogger logger) : ControllerBase
{
    [HttpGet("faq")]
    [Authorize(Roles = "partner")]
    [AllowAnonymous]
    public async Task<IActionResult> GetFAQs([FromQuery] int partnerId, [FromQuery] string? lang)
    {
        try
        {
            var faqs = await supportCenterService.GetFAQsAsync(partnerId, lang);
            return Ok(faqs);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while fetching FAQs");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }

    [HttpGet("contact/{contactId:int}")]
    [Authorize(Roles = "partner")]
    [AllowAnonymous]
    public async Task<IActionResult> GetSupportContact(int contactId, [FromQuery] int partnerId)
    {
        try
        {
            var contacts = await supportCenterService.GetSupportContactByIdAsync(contactId, partnerId);
            return Ok(contacts);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex, "Error while fetching support contacts");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }
}