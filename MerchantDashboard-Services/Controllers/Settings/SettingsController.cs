using MerchantDashboard_Services.Helpers;
using MerchantDashboard_Services.Services.MerchantDashboard;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Dto.MerchantDashboard.Settings;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace MerchantDashboard_Services.Controllers.Settings;

[Authorize(Roles = "partner")]
[Route("merchantDashboard/[controller]")]
[ApiController]
[PartnerAuthExempt]
public class SettingsController(
    ILogger logger,
    IMerchantService merchantService,
    IMerchantDashboardService merchantDashboardService,
    IConfiguration configuration)
    : ControllerBase
{
    [HttpGet]
    [Route("{merchantId:int}")]
    public async Task<IActionResult> GetSettingsAsync(int merchantId)
    {
        try
        {
            if (!Validators.ValidateTokenMerchant(Request, merchantId, configuration["JwtSecretLogin"] ?? ""))
            {
                return Forbid();
            }

            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetSettingsAsync"))
            {
                var settings = await merchantService.GetMerchantDashboardSettings(merchantId);
                return Ok(settings);    
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving Merchant Settings");
            return BadRequest();
        }
    }

    [HttpPut]
    public async Task<IActionResult> UpdateSettingsAsync(MerchantDashboardSettingsDto settings)
    {
        try
        {
            var updatedSettings = await merchantService.UpdateMerchantDashboardSettings(settings);
            return Ok(updatedSettings);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while updating Merchant Settings");
            return BadRequest();
        }
    }

    [HttpPost]
    [Route("validate")]
    public async Task<IActionResult> ValidateAsync(ValidationRequestDto request)
    {
        try
        {
            var isValid = await merchantDashboardService.ValidateMerchantDashboardSettings(request);
            return Ok(new { isValid = isValid, message = isValid ? "Settings are valid" : "Settings are invalid" } );
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while validating Merchant Settings");
            return BadRequest();
        }
    }
    
}