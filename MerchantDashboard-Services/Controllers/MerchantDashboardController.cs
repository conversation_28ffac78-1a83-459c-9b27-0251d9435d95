using System.Text.Json;
using MerchantDashboard_Services.Helpers;
using MerchantDashboard_Services.Services.MerchantDashboard;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using PartnerPortal_Services.Services.PartnerPortal;
using SerilogTimings.Extensions;
using Shared.Attributes;
using Shared.Dto.MerchantDashboard.Order;
using Shared.Dto.MerchantDashboard.Overview;
using Shared.Models;
using Shared.Models.Merchant;
using ILogger = Serilog.ILogger;

namespace MerchantDashboard_Services.Controllers;

[Authorize(Roles = "partner")]
[Route("[controller]")]
[ApiController]
[PartnerAuthExempt]
public class MerchantDashboardController(
    ILogger logger,
    IMerchantDashboardService merchantDashboardService,
    IPartnerPortalService partnerPortalService,
    IMemoryCache memoryCache,
    IConfiguration configuration)
    : ControllerBase
{
    [HttpPost]
    [Route("overview")]
    public async Task<IActionResult> OverviewAsync(MerchantDashboardSearchDto merchantDashboardSearchDto)
    {
        try
        {
            if (!Validators.ValidateTokenMerchant(Request, merchantDashboardSearchDto.MerchantId,
                    configuration["JwtSecretLogin"] ?? ""))
            {
                return Forbid();
            }

            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "OverviewAsync"))
            {
                if (merchantDashboardSearchDto.From == null || merchantDashboardSearchDto.To == null)
                {
                    return BadRequest(new {message = "From and To are required"});
                }

                var success = await memoryCache.GetOrCreateAsync(
                    $"MerchantDashboard_OverviewAsync_{merchantDashboardSearchDto.MerchantId}_{merchantDashboardSearchDto.App}_{merchantDashboardSearchDto.Email}_{merchantDashboardSearchDto.From}_{merchantDashboardSearchDto.To}",
                    async entry =>
                    {
                        entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1);
                        //entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(1);
                        return await merchantDashboardService.OverviewAsync(merchantDashboardSearchDto)
                            .ConfigureAwait(false);
                    });

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving merchantDashboard overview with query {Query}",
                JsonSerializer.Serialize(merchantDashboardSearchDto));
            return BadRequest();
        }
    }

    [HttpPost]
    [Route("overviewYearToDate")]
    public async Task<IActionResult> OverviewYearToDateAsync(MerchantDashboardSearchDto merchantDashboardSearchDto)
    {
        try
        {
            if (!Validators.ValidateTokenMerchant(Request, merchantDashboardSearchDto.MerchantId,
                    configuration["JwtSecretLogin"] ?? ""))
            {
                return Forbid();
            }

            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "OverviewYearToDateAsync"))
            {
                var success = await memoryCache.GetOrCreateAsync(
                    $"MerchantDashboard_OverviewYearToDateAsync_{merchantDashboardSearchDto.MerchantId}_{merchantDashboardSearchDto.App}_{merchantDashboardSearchDto.Email}_{merchantDashboardSearchDto.From}_{merchantDashboardSearchDto.To}_{merchantDashboardSearchDto.SelectedYear}",
                    async entry =>
                    {
                        entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1);
                        //entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(1); // For Testing Purposes
                        return await merchantDashboardService.OverviewYearToDateAsync(merchantDashboardSearchDto)
                            .ConfigureAwait(false);
                    });
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving merchantDashboard overviewYearToDate with query {Query}",
                JsonSerializer.Serialize(merchantDashboardSearchDto));
            return BadRequest();
        }
    }

    [HttpPost]
    [Route("orderMetrics")]
    [AllowAnonymous]
    public async Task<IActionResult> OrderMetricsAsync(MerchantDashboardSearchOrderDto merchantDashboardSearchOrderDto)
    {
        try
        {
            if (!Validators.ValidateTokenMerchant(Request, merchantDashboardSearchOrderDto.MerchantId,
                    configuration["JwtSecretLogin"] ?? ""))
            {
                return Forbid();
            }

            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "OrderMetricsAsync"))
            {
                var success = await memoryCache.GetOrCreateAsync(
                    $"MerchantDashboard_OrderMetricsAsync_{merchantDashboardSearchOrderDto.MerchantId}_{merchantDashboardSearchOrderDto.From}_{merchantDashboardSearchOrderDto.To}",
                    async entry =>
                    {
                        entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1);
                        //entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(1); //TEST FUNCTION
                        return await merchantDashboardService.GetOrderMetricsAsync(merchantDashboardSearchOrderDto)
                            .ConfigureAwait(false);
                    });
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving merchantDashboard order metrics with query {Query}",
                JsonSerializer.Serialize(merchantDashboardSearchOrderDto));
            return BadRequest();
        }
    }

    [HttpPost]
    [Route("orders")]
    [AllowAnonymous]
    public async Task<IActionResult> OrdersDateAsync(MerchantDashboardSearchOrderDto merchantDashboardSearchOrderDto)
    {
        try
        {
            if (!Validators.ValidateTokenMerchant(Request, merchantDashboardSearchOrderDto.MerchantId,
                    configuration["JwtSecretLogin"] ?? ""))
            {
                return Forbid();
            }

            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "OrdersDateAsync"))
            {
                var success = await memoryCache.GetOrCreateAsync(
                    $"MerchantDashboard_OrdersDateAsync_{merchantDashboardSearchOrderDto.MerchantId}_{merchantDashboardSearchOrderDto.From}_{merchantDashboardSearchOrderDto.To}_{merchantDashboardSearchOrderDto.PageNumber}_{merchantDashboardSearchOrderDto.PageSize}_{merchantDashboardSearchOrderDto.SearchQuery}",
                    async entry =>
                    {
                        entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1);
                        //entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(1); //TEST FUNCTION
                        return await merchantDashboardService.GetOrdersAsync(merchantDashboardSearchOrderDto)
                            .ConfigureAwait(false);
                    });
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving merchantDashboard orders with query {Query}",
                JsonSerializer.Serialize(merchantDashboardSearchOrderDto));
            return BadRequest();
        }
    }
    
    //Login
    [AllowAnonymous]
    [HttpPost("login")]
    public async Task<IActionResult> LoginAsync(LoginDto login)
    {
        try
        {
            var result = await partnerPortalService.LoginAsync(login)
                .ConfigureAwait(false);
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Warning(ex, "Error while login");
            return StatusCode(500, new ErrorDto(ex.ToString()));
        }
    }
}