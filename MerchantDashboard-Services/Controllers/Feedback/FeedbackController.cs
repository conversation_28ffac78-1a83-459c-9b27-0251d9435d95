using MerchantDashboard_Services.Helpers;
using MerchantDashboard_Services.Services.MerchantDashboard;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Shared.Dto.MerchantDashboard.Form;
using Shared.Models;
using ILogger = Serilog.ILogger;

namespace MerchantDashboard_Services.Controllers.Feedback;

[Authorize(Roles = "partner")]
[Route("MerchantDashboard/feedback")]
[ApiController]
public class MerchantDashboardFeedbackController : ControllerBase
{
    private readonly ILogger _logger;
    private readonly IConfiguration _configuration;
    private readonly IMerchantDashboardService _merchantDashboardService;

    public MerchantDashboardFeedbackController(ILogger logger,
        IConfiguration configuration, IMerchantDashboardService merchantDashboardService)
    {
        _logger = logger;
        _configuration = configuration;
        _merchantDashboardService = merchantDashboardService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateFeedbackEvent(MerchantDashboardFeedbackDto merchantDashboardFormDto)
    {
        try
        {
            if (!Validators.ValidateTokenMerchant(Request, merchantDashboardFormDto.MerchantId,
                    _configuration["JwtSecretLogin"] ?? ""))
            {
                return Forbid();
            }

            var success = await _merchantDashboardService.CreateFeedback(merchantDashboardFormDto)
                .ConfigureAwait(false);

            return Ok(success);
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex, "Error while creating feedback event");
            return Ok(new ErrorDto(ex.ToString()));
        }
    }
}