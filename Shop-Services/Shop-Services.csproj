<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <RootNamespace>Shop_Services</RootNamespace>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <!-- Project References -->
        <ProjectReference Include="..\Customer-Services\Customer-Services.csproj" />
        <ProjectReference Include="..\Integration-Services\Integration-Services.csproj" />
        <ProjectReference Include="..\Recommendation-Services\Recommendation-Services.csproj" />
        <ProjectReference Include="..\Shared\Shared.csproj" />
        <ProjectReference Include="..\Merchant-Services\Merchant-Services.csproj" />
    </ItemGroup>

    <ItemGroup>
        <!-- Elasticsearch Client -->
        <PackageReference Include="Elastic.Clients.Elasticsearch" Version="8.14.8" />
    </ItemGroup>

    <ItemGroup>
        <!-- Folder Structure -->
        <Folder Include="Models\Exposure\" />
    </ItemGroup>

    <ItemGroup>
        <!-- Static Mock Data -->
        <None Update="MockData\MockProducts.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="MockData\MockBanners.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="MockData\MockCategories.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="MockData\MockSections.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

</Project>