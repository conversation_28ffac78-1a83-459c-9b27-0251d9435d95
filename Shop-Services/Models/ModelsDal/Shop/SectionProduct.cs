using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Shop_Services.Models.ModelsDal.Shop;

[Table("SectionProducts", Schema = "shop")]
public partial class SectionProduct
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [Column("FK_SectionId")]
    public int FkSectionId { get; set; }

    [Column("FK_ProductId")]
    public long FkProductId { get; set; }

    public int SortOrder { get; set; }

    public bool IsInvalidated { get; set; }

    [StringLength(200)]
    public string? InvalidationReason { get; set; }

    [ForeignKey("FkSectionId")]
    [InverseProperty("SectionProducts")]
    public virtual Section FkSection { get; set; } = null!;
}
