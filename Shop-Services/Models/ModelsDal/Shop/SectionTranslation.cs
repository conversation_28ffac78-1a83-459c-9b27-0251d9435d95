using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Shop_Services.Models.ModelsDal.Shop;

[Table("SectionTranslations", Schema = "shop")]
public partial class SectionTranslation
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [Column("languageCode")]
    [StringLength(2)]
    [Unicode(false)]
    public string LanguageCode { get; set; } = null!;

    [StringLength(300)]
    public string Title { get; set; } = null!;

    [StringLength(1000)]
    public string Subtitle { get; set; } = null!;

    [Column("FK_SectionId")]
    public int FkSectionId { get; set; }

    [ForeignKey("FkSectionId")]
    [InverseProperty("SectionTranslations")]
    public virtual Section FkSection { get; set; } = null!;
}
