using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Shop_Services.Models.ModelsDal.Shop;

[Table("Sections", Schema = "shop")]
public partial class Section
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(200)]
    public string Type { get; set; } = null!;

    public int SortOrder { get; set; }

    [StringLength(200)]
    public string Name { get; set; } = null!;

    public bool Deleted { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [InverseProperty("FkSection")]
    public virtual ICollection<SectionProduct> SectionProducts { get; set; } = new List<SectionProduct>();

    [InverseProperty("FkSection")]
    public virtual ICollection<SectionTranslation> SectionTranslations { get; set; } = new List<SectionTranslation>();
}
