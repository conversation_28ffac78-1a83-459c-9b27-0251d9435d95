using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Shop_Services.Models.ModelsDal.Shop;

[Table("Categories", Schema = "shop")]
public partial class Category
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [Column("NameDA")]
    [StringLength(100)]
    public string NameDa { get; set; } = null!;

    [StringLength(500)]
    public string Src { get; set; } = null!;

    public int SortOrder { get; set; }

    [Column("FK_parentId")]
    public int? FkParentId { get; set; }

    [StringLength(500)]
    public string? PositiveTags { get; set; }

    [StringLength(500)]
    public string? NegativeTags { get; set; }

    [Column("NameEN")]
    [StringLength(100)]
    public string NameEn { get; set; } = null!;

    [Column("NameES")]
    [StringLength(100)]
    public string NameEs { get; set; } = null!;

    public bool Deleted { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [InverseProperty("FkCategory")]
    public virtual ICollection<CategoryMerchantRel> CategoryMerchantRels { get; set; } = new List<CategoryMerchantRel>();

    [ForeignKey("FkParentId")]
    [InverseProperty("InverseFkParent")]
    public virtual Category? FkParent { get; set; }

    [InverseProperty("FkParent")]
    public virtual ICollection<Category> InverseFkParent { get; set; } = new List<Category>();
}
