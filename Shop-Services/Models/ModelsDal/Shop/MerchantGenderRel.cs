using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Shop_Services.Models.ModelsDal.Shop;

[Table("MerchantGenderRel", Schema = "shop")]
public partial class MerchantGenderRel
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [Column("FK_MerchantGenderId")]
    public int FkMerchantGenderId { get; set; }

    [Column("FK_MerchantId")]
    public int FkMerchantId { get; set; }

    [ForeignKey("FkMerchantGenderId")]
    [InverseProperty("MerchantGenderRels")]
    public virtual MerchantGender FkMerchantGender { get; set; } = null!;
}
