using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Shop_Services.Models.ModelsDal.Shop;

[Table("MerchantGenders", Schema = "shop")]
public partial class MerchantGender
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(255)]
    public string Name { get; set; } = null!;

    [StringLength(200)]
    public string Type { get; set; } = null!;

    [InverseProperty("FkMerchantGender")]
    public virtual ICollection<MerchantGenderRel> MerchantGenderRels { get; set; } = new List<MerchantGenderRel>();
}
