using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Shop_Services.Models.ModelsDal.Shop;

[Table("Banners", Schema = "shop")]
public partial class Banner
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(1000)]
    public string ImageSrc { get; set; } = null!;

    [StringLength(200)]
    public string Redirect { get; set; } = null!;

    [StringLength(2)]
    [Unicode(false)]
    public string LanguageCode { get; set; } = null!;

    [Column("FK_MerchantId")]
    public int? FkMerchantId { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }
}
