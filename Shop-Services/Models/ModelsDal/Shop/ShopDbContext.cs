using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace Shop_Services.Models.ModelsDal.Shop;

public partial class ShopDbContext : DbContext
{
    public ShopDbContext(DbContextOptions<ShopDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Banner> Banners { get; set; }

    public virtual DbSet<Category> Categories { get; set; }

    public virtual DbSet<CategoryMerchantRel> CategoryMerchantRels { get; set; }

    public virtual DbSet<MerchantGender> MerchantGenders { get; set; }

    public virtual DbSet<MerchantGenderRel> MerchantGenderRels { get; set; }

    public virtual DbSet<Section> Sections { get; set; }

    public virtual DbSet<SectionProduct> SectionProducts { get; set; }

    public virtual DbSet<SectionTranslation> SectionTranslations { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Banner>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Banners__3214EC07AAD29A9D");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LanguageCode).IsFixedLength();
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<Category>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Categori__3214EC074663C544");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.NameEn).HasDefaultValue("");
            entity.Property(e => e.NameEs).HasDefaultValue("");

            entity.HasOne(d => d.FkParent).WithMany(p => p.InverseFkParent).HasConstraintName("FK__Categorie__FK_pa__75C27486");
        });

        modelBuilder.Entity<CategoryMerchantRel>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Category__3214EC07ABB39522");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkCategory).WithMany(p => p.CategoryMerchantRels)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__CategoryM__FK_Ca__162F4418");
        });

        modelBuilder.Entity<MerchantGender>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Merchant__3214EC0725E543A4");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<MerchantGenderRel>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Merchant__3214EC0756479A13");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkMerchantGender).WithMany(p => p.MerchantGenderRels)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__MerchantG__FK_Me__3F3159AB");
        });

        modelBuilder.Entity<Section>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Sections__3214EC07B6C6722D");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<SectionProduct>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__SectionP__3214EC073ECA42DC");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkSection).WithMany(p => p.SectionProducts)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__SectionPr__FK_Se__15FA39EE");
        });

        modelBuilder.Entity<SectionTranslation>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__SectionT__3214EC07A64B52CC");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LanguageCode).IsFixedLength();
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkSection).WithMany(p => p.SectionTranslations)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__SectionTr__FK_Se__1CA7377D");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
