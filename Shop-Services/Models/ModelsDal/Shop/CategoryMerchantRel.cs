using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Shop_Services.Models.ModelsDal.Shop;

[Table("CategoryMerchantRel", Schema = "shop")]
public partial class CategoryMerchantRel
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [Column("FK_CategoryId")]
    public int FkCategoryId { get; set; }

    [Column("FK_MerchantId")]
    public int FkMerchantId { get; set; }

    [ForeignKey("FkCategoryId")]
    [InverseProperty("CategoryMerchantRels")]
    public virtual Category FkCategory { get; set; } = null!;
}
