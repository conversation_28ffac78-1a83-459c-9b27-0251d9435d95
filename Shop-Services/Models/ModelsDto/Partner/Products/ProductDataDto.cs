namespace Shop_Services.Models.ModelsDto.Partner.Products;

/// <summary>
/// Internal DTO for transferring product data from database queries to business logic.
/// Used to optimize database projections and reduce data transfer.
/// </summary>
public class ProductDataDto
{
    public string InternalProductId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? ShortDescription { get; set; }
    public string? ProductImages { get; set; }
    public string? Permalink { get; set; }
    public decimal? RegularPrice { get; set; }
    public decimal? Price { get; set; }
    public int FkMerchantId { get; set; }
    public string MerchantName { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string MerchantLogoSrc { get; set; } = string.Empty;
    public bool IsFavorited { get; set; }
    public int VariantCount { get; set; }
} 