namespace Shop_Services.Models.ModelsDto.Partner.Products;

public class ProductPartnerDto
{
    public string Id { get; set; }
    public List<string> ProductImages { get; set; } = new List<string>();
    public string Name { get; set; }
    public string Description { get; set; }
    public decimal NormalPrice { get; set; }
    public decimal? SalePrice { get; set; }
    public string MerchantLogoSrc { get; set; }
    public string MerchantName { get; set; }
    public string RedirectLink { get; set; }
    public bool OnSale { get; set; }
    public bool Favored { get; set; }
    public bool IncludesVariants { get; set; }
}