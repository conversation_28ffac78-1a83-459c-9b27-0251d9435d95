namespace Shop_Services.Models.ModelsDto.Partner.Categories;

using System.Collections.Generic;
using System.Text.Json.Serialization;

// Internal DTO mirroring MockCategories.json structure
public class CategoryMockDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Src { get; set; } = string.Empty;
    public int? FkParentId { get; set; }
    public List<string>? ProductIds { get; set; }
    public List<CategoryMockDto>? Subcategories { get; set; }
} 