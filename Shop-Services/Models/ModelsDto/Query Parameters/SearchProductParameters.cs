using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Mvc;

namespace Shop_Services.Models.ModelsDto.Query_Parameters;

public class SearchProductParameters
{
    public string Email { get; set; }
    public int Size { get; set; }
    public int Page { get; set; }
    
    public string Search { get; set; }
    
    [FromQuery(Name = "search_identifier")]
    public string? SearchIdentifier { get; set; }

    [FromQuery(Name = "use_mock")]
    public bool UseMock { get; set; } = false;
}