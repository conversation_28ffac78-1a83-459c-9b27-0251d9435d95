using System.Text.Json.Serialization;

namespace Shop_Services.Models.ElasticSearch;

public class ElasticSearchSearchEvent
{
    [JsonPropertyName("Merchant")] public ElasticSearchMerchant MerchantInfo { get; set; }

    [JsonPropertyName("@timestamp")] public DateTime EventDate { get; set; }

    [JsonPropertyName("Product")] public ElasticSearchProduct ProductInfo { get; set; }

    [JsonPropertyName("text_embedding")] public ElasticSearchEmbedding EmbeddingInfo { get; set; }
}

public class ElasticSearchMerchant
{
    [JsonPropertyName("Id")] public long Id { get; set; }

    [JsonPropertyName("Name")] public string? Name { get; set; }
}

public class ElasticSearchProduct
{
    [JsonPropertyName("Id")] public int? Id { get; set; }

    [JsonPropertyName("Name")] public string? Name { get; set; }

    [JsonPropertyName("Created_date")] public DateTime CreatedDate { get; set; }

    [JsonPropertyName("Description")] public string? Description { get; set; }
    [JsonPropertyName("Categories")] public string? Categories { get; set; }

    [JsonPropertyName("Images")] public string[] Images { get; set; }

    [JsonPropertyName("Internal_product_id")]
    public string? InternalProductId { get; set; }

    [JsonPropertyName("Last_modified_date")]
    public DateTime LastModifiedDate { get; set; }

    [JsonPropertyName("Permalink")] public string? Permalink { get; set; }

    [JsonPropertyName("Regular_price")] public decimal RegularPrice { get; set; }
    [JsonPropertyName("Price")] public decimal? Price { get; set; }

    [JsonPropertyName("Short_description")]
    public string? ShortDescription { get; set; }

    [JsonPropertyName("Sku")] public string? Sku { get; set; }
}

public class ElasticSearchEmbedding
{
    [JsonPropertyName("predicted_value")] public double[] Value { get; set; }
}