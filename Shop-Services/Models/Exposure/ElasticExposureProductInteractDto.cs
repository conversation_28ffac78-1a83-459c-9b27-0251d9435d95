using System.Text.Json.Serialization;
using Shared.Elastic.Models.ElasticExposure;

namespace Shop_Services.Models.Exposure
{
    public class ElasticExposureProductInteractDto
    {
        [JsonPropertyName("Action_date")]
        public DateTime ActionDate { get; set; }

        [JsonPropertyName("Event_received")]
        public DateTime EventReceived { get; set; }

        [JsonPropertyName("Partner")]
        public ElasticExposurePartnerDto? Partner { get; set; }

        [JsonPropertyName("Merchant")]
        public ElasticExposureMerchantDto? Merchant { get; set; }

        [JsonPropertyName("Customer")]
        public ElasticExposureCustomerDto? Customer { get; set; }

        [Json<PERSON>ropertyName("Marketing")]
        public ElasticExposureMarketingDto? Marketing { get; set; }

        [JsonPropertyName("Product")]
        public ElasticExposureProductDto? Product { get; set; }

        [JsonPropertyName("Search")]
        public ElasticExposureSearchDto? Search { get; set; }
    }
}