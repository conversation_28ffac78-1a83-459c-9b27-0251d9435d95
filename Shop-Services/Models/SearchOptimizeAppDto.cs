namespace Shop_Services.Models;

public class SearchOptimizeAppDto
{
    public string Search { get; set; }
    public string PositiveTags { get; set; }
    public string NegativeTags { get; set; }
    public int Page { get; set; }
    public int Size { get; set; }
    public double ProductNameBoost { get; set; }
    public double ProductDescriptionBoost { get; set; }
    public double CategoryBoost { get; set; }
    public double MinimumMatchPercentage { get; set; }
    public float Bm25TextSearchBoost { get; set; }
    public float TextVectorSearchBoost { get; set; }
    public float Similarity { get; set; }
    public string MerchantsToFilter { get; set; }
    public string? Gender { get; set; }
    public byte? Age { get; set; }
    public bool BestSeller { get; set; }
    public bool Recommended { get; set; }
    public string? ProductsToFilter { get; set; }
    public bool Randomize { get; set; } = true;
    
    public long RandomizeSeed { get; set; } = 0;
    public double? MinimumScore { get; set; } = 2;
}