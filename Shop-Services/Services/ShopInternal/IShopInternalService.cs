using Shared.Dto.Shop.Categories;
using Shared.Dto.Shop.MerchantGenders;
using Shared.Dto.Shop.Product.Internal;
using Shared.Dto.Shop.Sections.Internal;
using Shop_Services.Models;
using Shop_Services.Models.ElasticSearch;
using Shop_Services.Models.ModelsDal.Shop;
using Shop_Services.Models.ModelsDto;
using Shared.Models.Customer;

namespace Shop_Services.Services.ShopInternal
{
    public interface IShopInternalService
    {
        //Internal
        Task<ProductPaginationResponseInternal> SearchProductAsync(SearchOptimizeAppDto searchOptimizeAppDto);
        // Sections
        Task<List<SectionInternalDto>> GetSectionsAsync(bool all = false);
        Task<SectionInternalDto> GetSectionAsync(int sectionId);
        Task<SectionInternalDto> UpdateSectionAsync(SectionInternalDto section);
        Task<SectionInternalDto> CreateSectionsAsync(SectionInternalDto section);
        Task<bool> DeleteSectionAsync(int sectionId);

        //Banners
        Task<List<Banner>> GetBannersAsync();
        Task<Banner> UpdateBannerAsync(BannerDto banner);
        Task<Banner> CreateBannerAsync(BannerDto banner);
        Task<bool> DeleteBannerAsync(int bannerId);


        //Categories
        Task<List<InternalCategoryDto>> GetCategoriesInternalAsync();
        Task<InternalCategoryDto> CreateCategoryAsync(InternalCategoryDto category);
        Task<InternalCategoryDto> UpdateCategoryAsync(InternalCategoryDto category);
        Task<bool> DeleteCategoryAsync(int categoryId);
        Task<CategoryMerchantRelationDto> GetCategoryMerchantRelationsByMerchantIdAsync(int merchantId);

        Task<CategoryMerchantRelationDto> UpdateCategoryMerchantRelationsAsync(
            CategoryMerchantRelationDto categoryMerchantRelationDto);

        //MerchantGenders
        Task<List<MerchantGenderDto>> GetMerchantGendersAsync();
        Task UpdateMerchantGendersAsync(MerchantGenderDto merchantGenderDto);
        Task ValidateSectionProductsValidity();
        
        
        // Temp!
        Task CheckAllImagesExist();
    }
}