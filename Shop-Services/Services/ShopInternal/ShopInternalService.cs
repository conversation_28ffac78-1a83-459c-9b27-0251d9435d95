using System.Collections.Concurrent;
using System.Diagnostics;
using System.Net;
using System.Net.Http.Headers;
using System.Text;
using Audience.Services.Audience;
using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Core.Search;
using Elastic.Clients.Elasticsearch.QueryDsl;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Shared.Dto.Curated.Product;
using Shared.Dto.MerchantScore;
using Shared.Dto.Shop.Categories;
using Shared.Dto.Shop.MerchantGenders;
using Shared.Dto.Shop.Product;
using Shared.Dto.Shop.Product.Internal;
using Shared.Dto.Shop.Sections.Internal;
using Shared.Dto.Webshop;
using Shared.Services;
using Shared.Services.Cache;
using Shared.Services.Image;
using Shared.Services.MerchantRelevance;
using Shared.Services.Partner;
using Shop_Services.Models;
using Shop_Services.Models.ElasticSearch;
using Shop_Services.Models.ModelsDal.Shop;
using Shop_Services.Models.ModelsDto;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Webp;
using SixLabors.ImageSharp.PixelFormats;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;
using JsonSerializer = System.Text.Json.JsonSerializer;
using ApiKey = Elastic.Transport.ApiKey;
using Field = Elastic.Clients.Elasticsearch.Field;
using MinimumShouldMatch = Elastic.Clients.Elasticsearch.MinimumShouldMatch;
using Shared.Models.Customer;
using Shop_Services.Services.ElasticSearchService;


namespace Shop_Services.Services.ShopInternal
{
    public class ShopInternalService(
        ILogger logger,
        IMerchantService merchantService,
        ICacheService cacheService,
        ICustomerService customerService,
        ShopDbContext shopDbContext,
        IImageService imageService,
        IMerchantRelevanceService merchantRelevanceService,
        IPartnerContext partnerContext,
        IMemoryCache memoryCache,
        IElasticSearchService elasticSearchService)
        : IShopInternalService
    {
        private readonly IImageService _imageService = imageService;

        private const int AgeInterval = 10;
        
        private readonly HttpClient _httpClient = new()
        {
            Timeout = TimeSpan.FromHours(10)
        };


        public async Task<ProductPaginationResponseInternal> SearchProductAsync(SearchOptimizeAppDto searchOptimizeAppDto)
        {
            var productResponse = new ProductPaginationResponseInternal
            {
                Data = new ProductPaginationInternalDto
                {
                    Products = [],
                    LastPage = 1
                }
            };

            if (searchOptimizeAppDto.BestSeller)
            {
                var gender = searchOptimizeAppDto.Gender ?? "Unknown";
                string email = "<EMAIL>";

                var contact = await customerService.GetCustomerForMerchantRelevance(email);
                if (contact != null)
                {
                    contact.Gender = searchOptimizeAppDto.Gender ?? contact.Gender;
                    contact.Age = searchOptimizeAppDto?.Age ?? 30;
                    email += $"_{contact.Gender}_{contact.Age}";
                    var topMerchants = (await merchantRelevanceService.GetMerchantRelevance(contact, AgeInterval, false))
                        .ToList();

                    //Filter out merchant who are not on the filter list from db
                    List<int> allowedMerchants;

                    if (searchOptimizeAppDto?.MerchantsToFilter != "")
                    {
                        allowedMerchants = searchOptimizeAppDto!.MerchantsToFilter.Split(",").Select(int.Parse)
                            .ToList();
                    }
                    else
                    {
                        allowedMerchants = (await shopDbContext.MerchantGenderRels
                                .Include(a => a.FkMerchantGender)
                                .Where(a => a.Active && a.FkMerchantGender.Active &&
                                            a.FkMerchantGender.Type == "Bestseller" &&
                                            a.FkMerchantGender.Name == gender).ToListAsync())
                            .Select(a => a.FkMerchantId)
                            .ToList();
                    }

                    topMerchants = topMerchants.Where(a => allowedMerchants.Contains(a.MerchantId)).ToList();
                    topMerchants = topMerchants.Take(10).ToList();

                    var tempProductResponse = await ProductHandling(searchOptimizeAppDto.Page,
                        searchOptimizeAppDto.Size,
                        email, topMerchants,
                        $"ShopService_GetProductsByEmailAndFiltersAsync_1_{email}_{searchOptimizeAppDto.Page}_{searchOptimizeAppDto.Size}_{topMerchants}_{partnerContext.PartnerId}",
                        "GeneralService_TopProductsForMerchants_Order", "1", 3);

                    productResponse.Data.LastPage = tempProductResponse.Data.LastPage;
                    foreach (var product in tempProductResponse.Data.Products)
                    {
                        productResponse.Data.Products.Add(new ProductRefInternalDto
                        {
                            Id = product.Id,
                            Name = product.Name,
                            MerchantName = product.MerchantName,
                            MerchantId = product.MerchantId,
                            ImageSrc = product.ImageSrc,
                            BestSeller = product.BestSeller,
                            Favored = product.Favored,
                            IncludesVariants = product.IncludesVariants,
                            NormalPrice = product.NormalPrice,
                            SalePrice = product.SalePrice,
                            OnSale = product.OnSale,
                            Description = product.Description,
                            ProductUrl = ""
                        });
                    }
                }
            }
            else if (searchOptimizeAppDto.Recommended)
            {
                var gender = searchOptimizeAppDto.Gender ?? "Unknown";
                string email = "<EMAIL>";

                var contact = await customerService.GetCustomerForMerchantRelevance(email);
                if (contact != null)
                {
                    contact.Gender = searchOptimizeAppDto.Gender ?? contact.Gender;
                    contact.Age = searchOptimizeAppDto?.Age ?? 30;
                    email += $"_{contact.Gender}_{contact.Age}";
                    var topMerchants = (await merchantRelevanceService.GetMerchantRelevance(contact, AgeInterval, false))
                        .ToList();

                    //Filter out merchant who are not on the filter list from db
                    List<int> allowedMerchants;

                    if (searchOptimizeAppDto?.MerchantsToFilter != "")
                    {
                        allowedMerchants = searchOptimizeAppDto!.MerchantsToFilter.Split(",").Select(int.Parse)
                            .ToList();
                    }
                    else
                    {
                        allowedMerchants = (await shopDbContext.MerchantGenderRels
                            .Include(a => a.FkMerchantGender)
                            .Where(a => a.Active && a.FkMerchantGender.Active &&
                                        a.FkMerchantGender.Type == "Recommended" && a.FkMerchantGender.Name == gender)
                            .ToListAsync()).Select(a => a.FkMerchantId).ToList();
                    }

                    topMerchants = topMerchants.Where(a => allowedMerchants.Contains(a.MerchantId)).ToList();
                    topMerchants = topMerchants.Take(20).ToList();
                    /*var tempProductResponse = await ProductHandling(searchOptimizeAppDto?.Page ?? 0,
                        searchOptimizeAppDto?.Size ?? 25, email, topMerchants,
                        $"ShopService_GetProductsByEmailAndFiltersAsync_3_{email}_{searchOptimizeAppDto?.Page ?? 0}_{searchOptimizeAppDto?.Size ?? 25}",
                        "GeneralService_TopProductsForMerchants_PageEvent", "3", 10);*/

                    var tempProductResponse = await ProductHandling(searchOptimizeAppDto.Page,
                        searchOptimizeAppDto.Size,
                        email, topMerchants,
                        $"ShopService_GetProductsByEmailAndFiltersAsync_3_{email}_{searchOptimizeAppDto.Page}_{searchOptimizeAppDto.Size}_{topMerchants}_{partnerContext.PartnerId}",
                        "GeneralService_TopProductsForMerchants_PageEvent", "3", 10);

                    productResponse.Data.LastPage = tempProductResponse.Data.LastPage;
                    foreach (var product in tempProductResponse.Data.Products)
                    {
                        productResponse.Data.Products.Add(new ProductRefInternalDto
                        {
                            Id = product.Id,
                            Name = product.Name,
                            MerchantName = product.MerchantName,
                            MerchantId = product.MerchantId,
                            ImageSrc = product.ImageSrc,
                            BestSeller = product.BestSeller,
                            Favored = product.Favored,
                            IncludesVariants = product.IncludesVariants,
                            NormalPrice = product.NormalPrice,
                            SalePrice = product.SalePrice,
                            OnSale = product.OnSale,
                            Description = product.Description,
                            ProductUrl = ""
                        });
                    }
                }
            }
            // Product Free Search & Internal Category Search
            else  //if (!string.IsNullOrEmpty(searchOptimizeAppDto.Search) || !string.IsNullOrEmpty(searchOptimizeAppDto.PositiveTags))
            {
                // Use the new PartnerElasticSearchService for search
                var contact = await customerService.GetCustomerForMerchantRelevance("<EMAIL>");
                searchOptimizeAppDto.RandomizeSeed = contact.Id;
                
                if(!string.IsNullOrEmpty(searchOptimizeAppDto.PositiveTags))
                    searchOptimizeAppDto.Search = searchOptimizeAppDto.PositiveTags;
                
                var elasticResponse = await elasticSearchService.PerformElasticSearch(searchOptimizeAppDto);
                // Map elasticResponse to ProductPaginationResponseInternal as needed
                productResponse = new ProductPaginationResponseInternal
                {
                    Data = new ProductPaginationInternalDto
                    {
                        Products = elasticResponse.Products.Select(p => new ProductRefInternalDto
                        {
                            Id = p.Id,
                            Name = p.Name,
                            MerchantName = p.MerchantName,
                            MerchantId = p.MerchantId,
                            ImageSrc = p.ImageUrl,
                            BestSeller = false,
                            Favored = false,
                            IncludesVariants = false,
                            NormalPrice = p.RegularPrice,
                            SalePrice = p.SalePrice > 0 ? p.SalePrice : p.RegularPrice,
                            OnSale = p.RegularPrice != p.SalePrice && p.SalePrice is > 0,
                            Description = p.Description,
                            ProductUrl = "",
                            Categories = null // Map if available
                        }).ToList(),
                        LastPage = searchOptimizeAppDto.Size > 0
                            ? (long)Math.Ceiling((double)elasticResponse.Total / searchOptimizeAppDto.Size)
                            : 1
                    }
                };
            }
            return productResponse;
        }

        public async Task<List<InternalCategoryDto>> GetCategoriesInternalAsync()
        {
            var categoriesDb = await shopDbContext.Categories
                .Include(a => a.CategoryMerchantRels.Where(b => b.Active))
                .Where(a => !a.Deleted && a.FkPartnerId == partnerContext.PartnerId)
                .ToListAsync();

            return categoriesDb.Select(categoryDb => new InternalCategoryDto
                {
                    Id = categoryDb.Id,
                    Active = categoryDb.Active,
                    Name = categoryDb.NameDa,
                    NameDA = categoryDb.NameDa,
                    NameEN = categoryDb.NameEn,
                    NameES = categoryDb.NameEs,
                    ImageSrc = categoryDb.Src,
                    Order = categoryDb.SortOrder,
                    ParentId = categoryDb.FkParentId,
                    PositiveTags = categoryDb.PositiveTags,
                    NegativeTags = categoryDb.NegativeTags,
                    MerchantIds = categoryDb.CategoryMerchantRels.Select(a => a.FkMerchantId).ToList()
                })
                .ToList();
        }

        public async Task<InternalCategoryDto> CreateCategoryAsync(InternalCategoryDto category)
        {
            var image = string.Empty;
            if (category.ImageSrc.Contains("base64"))
            {
                var rawData = category.ImageSrc.Split("base64,");
                category.ImageSrc = rawData[1];
                byte[] imageBytes = Convert.FromBase64String(category.ImageSrc);
                using (MemoryStream ms = new MemoryStream(imageBytes))
                {
                    using Image<Rgba32> background = Image.Load<Rgba32>(ms);
                    using (MemoryStream ms1 = new MemoryStream())
                    {
                        var webpEncoder = new WebpEncoder
                        {
                            Quality = 100
                        };
                        await background.SaveAsync(ms1, webpEncoder);
                        image = imageService.CreateImages($"{category.Name}.webp",
                            "data:image/webp;base64," + Convert.ToBase64String(ms.ToArray()), $"shop-categories");
                    }
                }
            }

            var newCategory = new Category
            {
                Active = false,
                NameDa = category.NameDA,
                NameEn = category.NameEN,
                NameEs = category.NameES,
                Src = image,
                SortOrder = category.Order,
                PositiveTags = category.PositiveTags,
                NegativeTags = category.NegativeTags,
                FkParentId = category.ParentId == 0 ? null : category.ParentId,
                Deleted = false,
                FkPartnerId = partnerContext.PartnerId,
            };

            await shopDbContext.Categories.AddAsync(newCategory);

            // Get new Category ID
            await shopDbContext.SaveChangesAsync();

            // Add Merchant Category Relations
            foreach (var merchantId in category.MerchantIds)
            {
                shopDbContext.CategoryMerchantRels.Add(new CategoryMerchantRel
                {
                    Active = true,
                    FkCategoryId = newCategory.Id,
                    FkMerchantId = merchantId
                });
            }

            await shopDbContext.SaveChangesAsync();

            category.Id = newCategory.Id;
            category.ImageSrc = image;

            return category;
        }

        public async Task<InternalCategoryDto> UpdateCategoryAsync(InternalCategoryDto category)
        {
            var existing = await shopDbContext.Categories
                .Where(a => a.Id == category.Id)
                .FirstOrDefaultAsync().ConfigureAwait(false);

            if (existing != null)
            {
                if (category.ImageSrc.Contains("base64"))
                {
                    var rawData = category.ImageSrc.Split("base64,");
                    category.ImageSrc = rawData[1];
                    byte[] imageBytes = Convert.FromBase64String(category.ImageSrc);
                    using (MemoryStream ms = new MemoryStream(imageBytes))
                    {
                        using Image<Rgba32> background = Image.Load<Rgba32>(ms);
                        using (MemoryStream ms1 = new MemoryStream())
                        {
                            var webpEncoder = new WebpEncoder
                            {
                                Quality = 100
                            };
                            await background.SaveAsync(ms1, webpEncoder);
                            var image = imageService.CreateImages($"{category.Name}.webp",
                                "data:image/webp;base64," + Convert.ToBase64String(ms.ToArray()), $"shop-categories");

                            existing.Src = image;
                            category.ImageSrc = image;
                        }
                    }
                }

                existing.Active = category.Active;
                existing.NameDa = category.NameDA;
                existing.NameEn = category.NameEN;
                existing.NameEs = category.NameES;
                existing.SortOrder = category.Order;
                existing.PositiveTags = category.PositiveTags;
                existing.NegativeTags = category.NegativeTags;
                existing.FkParentId = category.ParentId == 0 ? null : category.ParentId;
                shopDbContext.Update(existing);

                // Update Merchant Category Rels
                var existingMerchantCategoryRels = await shopDbContext.CategoryMerchantRels
                    .Where(a => a.FkCategoryId == category.Id)
                    .ToListAsync().ConfigureAwait(false);

                await using (var transaction = await shopDbContext.Database.BeginTransactionAsync())
                {
                    try
                    {
                        foreach (var merchantId in category.MerchantIds)
                        {
                            if (existingMerchantCategoryRels.All(a => a.FkMerchantId != merchantId))
                            {
                                shopDbContext.CategoryMerchantRels.Add(new CategoryMerchantRel
                                {
                                    Active = true,
                                    FkCategoryId = category.Id,
                                    FkMerchantId = merchantId
                                });
                            }
                            else if (existingMerchantCategoryRels.Any(a => a.FkMerchantId == merchantId))
                            {
                                var existingMerchantCategoryRel =
                                    existingMerchantCategoryRels.First(a => a.FkMerchantId == merchantId);
                                existingMerchantCategoryRel.Active = true;
                                shopDbContext.Update(existingMerchantCategoryRel);
                            }
                        }

                        foreach (var merchantCategoryRel in existingMerchantCategoryRels)
                        {
                            if (category.MerchantIds.All(a => a != merchantCategoryRel.FkMerchantId))
                            {
                                merchantCategoryRel.Active = false;
                                shopDbContext.Update(merchantCategoryRel);
                            }
                        }

                        await shopDbContext.SaveChangesAsync();
                        await transaction.CommitAsync();
                    }
                    catch (Exception ex)
                    {
                        await transaction.RollbackAsync();
                        logger.Error(ex, "Error updating merchant category relations for category ID: {CategoryId}",
                            category.Id);
                        throw;
                    }
                }

                await shopDbContext.SaveChangesAsync().ConfigureAwait(false);
                cacheService.RemoveData($"ShopService_GetCategoriesAsync_{partnerContext.PartnerId}");
            }

            return category;
        }

        public async Task<bool> DeleteCategoryAsync(int categoryId)
        {
            var existing = await shopDbContext.Categories
                .Where(a => a.Id == categoryId)
                .FirstOrDefaultAsync().ConfigureAwait(false);

            if (existing != null)
            {
                existing.Active = false;
                existing.Deleted = true;
                shopDbContext.Update(existing);

                var existingMerchantCategoryRels = await shopDbContext.CategoryMerchantRels
                    .Where(a => a.FkCategoryId == categoryId)
                    .ToListAsync().ConfigureAwait(false);

                foreach (var merchantCategoryRel in existingMerchantCategoryRels)
                {
                    merchantCategoryRel.Active = false;
                    shopDbContext.Update(merchantCategoryRel);
                }

                await shopDbContext.SaveChangesAsync().ConfigureAwait(false);
                cacheService.RemoveData($"ShopService_GetCategoriesAsync_{partnerContext.PartnerId}");
                return true;
            }

            return false;
        }

        public async Task<List<SectionInternalDto>> GetSectionsAsync(bool all = false)
        {
            var partnerId = partnerContext.PartnerId;
            var sections = await memoryCache.GetOrCreateAsync($"GetSections_Internal_{partnerId}", async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(10);
                var sections = await shopDbContext.Sections
                    .Include(s => s.SectionProducts.Where(p => p.Active && !p.IsInvalidated))
                    .Include(s => s.SectionTranslations.Where(t => t.Active))
                    .Where(a => !a.Deleted && (all || a.FkPartnerId == partnerId))
                    .ToListAsync();

                var sectionsToReturn = new List<SectionInternalDto>();

                foreach (var section in sections)
                {
                    var sectionToAdd = new SectionInternalDto
                    {
                        Id = section.Id,
                        Active = section.Active,
                        Type = section.Type,
                        SortOrder = section.SortOrder,
                        Name = section.Name,
                        SectionProducts = [],
                        SectionTranslations = section.SectionTranslations.Select(t => new SectionTranslationDto
                        {
                            Id = t.Id,
                            Active = t.Active,
                            LanguageCode = t.LanguageCode,
                            Title = t.Title,
                            Subtitle = t.Subtitle
                        }).ToList()
                    };

                    foreach (var sectionProduct in section.SectionProducts.OrderBy(a => a.SortOrder))
                    {
                        var product = await merchantService.GetProductByProductId(sectionProduct.FkProductId);
                        if (product != null)
                        {
                            var merchantName = await merchantService.GetMerchantName(product.FkMerchantId);
                            var image = string.Empty;
                            if (!string.IsNullOrEmpty(product.ProductImages))
                            {
                                var images = JsonSerializer.Deserialize<List<ProductImageDto>>(product.ProductImages);
                                image = images?.FirstOrDefault()?.Src ?? string.Empty;
                            }

                            sectionToAdd.SectionProducts.Add(new SectionProductInternalDto
                            {
                                Id = product.Id,
                                Active = sectionProduct.Active,
                                InternalProductId = product.InternalProductId,
                                Name = product.Name,
                                ImgSrc = image,
                                SortOrder = sectionProduct.SortOrder,
                                ProductUrl = product.Permalink,
                                IsInvalidated = sectionProduct.IsInvalidated,
                                InvalidationReason = sectionProduct.InvalidationReason,
                                MerchantName = merchantName
                            });
                        }
                    }

                    sectionsToReturn.Add(sectionToAdd);
                }
                return sectionsToReturn;
            });
            return sections ?? [];
        }

        public async Task<SectionInternalDto> GetSectionAsync(int sectionId)
        {
            var section = await shopDbContext.Sections
                .Include(s => s.SectionProducts.Where(p => p.Active && !p.IsInvalidated))
                .Include(s => s.SectionTranslations.Where(t => t.Active))
                .SingleOrDefaultAsync(a => a.Id == sectionId);

            if (section == null) return null;

            var sectionToReturn = new SectionInternalDto
            {
                Id = section.Id,
                Active = section.Active,
                Type = section.Type,
                SortOrder = section.SortOrder,
                Name = section.Name,
                SectionProducts = [],
                SectionTranslations = section.SectionTranslations.Select(t => new SectionTranslationDto
                {
                    Id = t.Id,
                    Active = t.Active,
                    LanguageCode = t.LanguageCode,
                    Title = t.Title,
                    Subtitle = t.Subtitle
                }).ToList()
            };

            foreach (var sectionProduct in section.SectionProducts.OrderBy(a => a.SortOrder).Where(a => a.Active))
            {
                var product = await merchantService.GetProductByProductId(sectionProduct.FkProductId);
                if (product != null)
                {
                    var merchantName = await merchantService.GetMerchantName(product.FkMerchantId);
                    var image = string.Empty;
                    if (!string.IsNullOrEmpty(product.ProductImages))
                    {
                        var images = JsonSerializer.Deserialize<List<ProductImageDto>>(product.ProductImages);
                        image = images?.FirstOrDefault()?.Src ?? string.Empty;
                    }

                    sectionToReturn.SectionProducts.Add(new SectionProductInternalDto
                    {
                        Id = product.Id,
                        Active = sectionProduct.Active,
                        InternalProductId = product.InternalProductId,
                        Name = product.Name,
                        ImgSrc = image,
                        SortOrder = sectionProduct.SortOrder,
                        ProductUrl = product.Permalink,
                        IsInvalidated = sectionProduct.IsInvalidated,
                        InvalidationReason = sectionProduct.InvalidationReason,
                        MerchantName = merchantName
                    });
                }
            }

            return sectionToReturn;
        }

        public async Task<SectionInternalDto> UpdateSectionAsync(SectionInternalDto section)
        {
            var existing = await shopDbContext.Sections
                .Include(s => s.SectionProducts)
                .Include(s => s.SectionTranslations)
                .SingleOrDefaultAsync(a => a.Id == section.Id);

            if (existing == null) return section;

            existing.Name = section.Name;
            existing.Type = section.Type;
            existing.SortOrder = section.SortOrder;
            existing.LastModifiedDate = DateTime.UtcNow;
            existing.Active = section.Active;

            // Update Products
            var existingProductIds =
                existing.SectionProducts.Where(sp => sp.Active).Select(sp => sp.FkProductId).ToList();
            var newProducts = section.SectionProducts.ToList();
            var newProductIds = section.SectionProducts.Select(p => p.Id).ToList();

            foreach (var productId in existingProductIds.Except(newProductIds).ToList())
            {
                var productToDeactivate = existing.SectionProducts.First(sp => sp.FkProductId == productId);
                productToDeactivate.Active = false;
            }

            foreach (var product in newProducts)
            {
                var existingProduct = existing.SectionProducts.FirstOrDefault(sp => sp.FkProductId == product.Id);
                if (existingProduct != null)
                {
                    existingProduct.Active = true;
                    existingProduct.SortOrder = product.SortOrder;
                }
                else
                {
                    existing.SectionProducts.Add(new SectionProduct
                    {
                        FkSectionId = existing.Id,
                        FkProductId = product.Id,
                        Active = true,
                        IsInvalidated = false,
                        SortOrder = product.SortOrder
                    });
                }
            }

            // Update Translations
            var existingTranslations = existing.SectionTranslations.ToList();
            foreach (var translation in section.SectionTranslations)
            {
                var existingTranslation = existingTranslations.SingleOrDefault(t => t.Id == translation.Id);

                if (existingTranslation == null)
                {
                    existing.SectionTranslations.Add(new SectionTranslation
                    {
                        Active = translation.Active,
                        LanguageCode = translation.LanguageCode,
                        Title = translation.Title,
                        Subtitle = translation.Subtitle,
                        FkSectionId = existing.Id
                    });
                }
                else
                {
                    existingTranslation.Active = translation.Active;
                    existingTranslation.LanguageCode = translation.LanguageCode;
                    existingTranslation.Title = translation.Title;
                    existingTranslation.Subtitle = translation.Subtitle;
                }
            }

            foreach (var translation in existingTranslations
                         .Where(et => section.SectionTranslations.All(st => st.Id != et.Id)).ToList())
            {
                translation.Active = false;
            }

            shopDbContext.Sections.Update(existing);
            await shopDbContext.SaveChangesAsync();

            cacheService.RemoveData($"Section_{section.Id}");
            cacheService.RemoveData($"Sections_{partnerContext.PartnerId}");
            memoryCache.Remove($"GetSections_Internal_{existing.FkPartnerId}");

            return section;
        }


        public async Task<SectionInternalDto> CreateSectionsAsync(SectionInternalDto section)
        {
            var newSection = new Section
            {
                FkPartnerId = partnerContext.PartnerId,
                CreatedDate = DateTime.UtcNow,
                LastModifiedDate = DateTime.UtcNow,
                Active = section.Active,
                Deleted = false,
                Type = section.Type,
                SortOrder = section.SortOrder,
                Name = section.Name,
                SectionProducts = section.SectionProducts.Select(product => new SectionProduct
                {
                    FkProductId = product.Id,
                    Active = true,
                    SortOrder = product.SortOrder
                }).ToList(),
                SectionTranslations = section.SectionTranslations.Select(translation => new SectionTranslation
                {
                    CreatedDate = DateTime.UtcNow,
                    LastModifiedDate = DateTime.UtcNow,
                    Active = translation.Active,
                    LanguageCode = translation.LanguageCode,
                    Title = translation.Title,
                    Subtitle = translation.Subtitle
                }).ToList()
            };

            shopDbContext.Sections.Add(newSection);
            await shopDbContext.SaveChangesAsync();
            cacheService.RemoveData($"Sections_{partnerContext.PartnerId}");
            memoryCache.Remove($"GetSections_Internal_{partnerContext.PartnerId}");

            section.Id = newSection.Id;
            return section;
        }


        public async Task<bool> DeleteSectionAsync(int sectionId)
        {
            var existing = await shopDbContext.Sections
                .Include(s => s.SectionProducts)
                .Include(s => s.SectionTranslations)
                .SingleOrDefaultAsync(a => a.Id == sectionId);

            if (existing == null) return false;

            // Deactivate the section
            existing.Active = false;
            existing.Deleted = true;

            // Deactivate related products
            foreach (var product in existing.SectionProducts)
            {
                product.Active = false;
            }

            // Deactivate related translations
            foreach (var translation in existing.SectionTranslations)
            {
                translation.Active = false;
            }

            shopDbContext.Sections.Update(existing);
            await shopDbContext.SaveChangesAsync();

            cacheService.RemoveData($"Section_{sectionId}");
            cacheService.RemoveData($"Sections_{partnerContext.PartnerId}");
            memoryCache.Remove($"GetSections_Internal_{partnerContext.PartnerId}");
            return true;
        }

        public async Task<List<Banner>> GetBannersAsync()
        {
            return await shopDbContext.Banners.AsNoTracking()
                .Where(a => a.Active == true && a.FkPartnerId == partnerContext.PartnerId).ToListAsync();
        }

        public async Task<Banner> UpdateBannerAsync(BannerDto bannerDto)
        {
            if (bannerDto.ImageSrc.Contains("base64,"))
            {
                string imageFormat = StaticVariables.GetImageFormatFromBase64(bannerDto.ImageSrc);
                bannerDto.ImageSrc = _imageService.CreateImages(
                    $"{StaticVariables.GenerateRandomString(10)}.{imageFormat}",
                    bannerDto.ImageSrc, "banners");
            }

            var banner = new Banner
            {
                Active = bannerDto.Active,
                CreatedDate = bannerDto.CreatedDate,
                FkMerchantId = bannerDto.FkMerchantId,
                Id = bannerDto.Id,
                ImageSrc = bannerDto.ImageSrc,
                LanguageCode = bannerDto.LanguageCode,
                LastModifiedDate = bannerDto.LastModifiedDate,
                Redirect = bannerDto.Redirect,
            };
            
            shopDbContext.Banners.Update(banner);
            await shopDbContext.SaveChangesAsync();
            // TODO - Add PartnerId to this cache key
            cacheService.RemoveData("Shop_Banners");
            return banner;
        }

        public async Task<Banner> CreateBannerAsync(BannerDto bannerDto)
        {
            if (bannerDto.ImageSrc.Contains("base64,"))
            {
                string imageFormat = StaticVariables.GetImageFormatFromBase64(bannerDto.ImageSrc);
                bannerDto.ImageSrc = _imageService.CreateImages(
                    $"{StaticVariables.GenerateRandomString(10)}.{imageFormat}",
                    bannerDto.ImageSrc, "banners");
            }
            
            var banner = new Banner
            {
                FkPartnerId = partnerContext.PartnerId,
                Active = bannerDto.Active,
                CreatedDate = bannerDto.CreatedDate,
                FkMerchantId = bannerDto.FkMerchantId,
                ImageSrc = bannerDto.ImageSrc,
                LanguageCode = bannerDto.LanguageCode,
                LastModifiedDate = bannerDto.LastModifiedDate,
                Redirect = bannerDto.Redirect,
            };

            await shopDbContext.Banners.AddAsync(banner);
            await shopDbContext.SaveChangesAsync();
            // TODO - Add PartnerId to this cache key
            cacheService.RemoveData("Shop_Banners");
            return banner;
        }

        public async Task<bool> DeleteBannerAsync(int bannerId)
        {
            var banner = await shopDbContext.Banners.SingleOrDefaultAsync(a => a.Id == bannerId).ConfigureAwait(false);
            if (banner == null) return false;

            banner.Active = false;
            banner.LastModifiedDate = DateTime.UtcNow;
            await shopDbContext.SaveChangesAsync();

            // TODO - Add PartnerId to this cache key
            cacheService.RemoveData("Shop_Banners");
            return true;
        }


        public async Task<CategoryMerchantRelationDto> GetCategoryMerchantRelationsByMerchantIdAsync(int merchantId)
        {
            var categoryMerchantRelation = new CategoryMerchantRelationDto {MerchantId = merchantId, CategoryIds = []};
            var categoriesDb = await shopDbContext.CategoryMerchantRels
                .Where(a => a.Active && a.FkMerchantId == merchantId).ToListAsync();
            foreach (var categoryDb in categoriesDb)
            {
                categoryMerchantRelation.CategoryIds.Add(categoryDb.FkCategoryId);
            }

            return categoryMerchantRelation;
        }

        public async Task<CategoryMerchantRelationDto> UpdateCategoryMerchantRelationsAsync(
            CategoryMerchantRelationDto categoryMerchantRelation)
        {
            var allRelations = await shopDbContext.CategoryMerchantRels
                .Where(a => a.FkMerchantId == categoryMerchantRelation.MerchantId)
                .ToListAsync().ConfigureAwait(false);

            foreach (var activeRelation in allRelations.Where(a => a.Active))
            {
                if (!categoryMerchantRelation.CategoryIds.Contains(activeRelation.FkCategoryId))
                {
                    activeRelation.Active = false;
                    shopDbContext.Update(activeRelation);
                }
            }

            foreach (var categoryId in categoryMerchantRelation.CategoryIds)
            {
                var existing = allRelations.FirstOrDefault(a => a.FkCategoryId == categoryId);
                if (existing != null)
                {
                    existing.Active = true;
                    shopDbContext.Update(existing);

                    cacheService.RemoveData($"Category_{categoryId}");
                }
                else
                {
                    shopDbContext.CategoryMerchantRels.Add(new CategoryMerchantRel()
                    {
                        FkCategoryId = categoryId,
                        FkMerchantId = categoryMerchantRelation.MerchantId,
                        Active = true
                    });
                }
            }

            await shopDbContext.SaveChangesAsync().ConfigureAwait(false);

            return categoryMerchantRelation;
        }


        public async Task<List<MerchantGenderDto>> GetMerchantGendersAsync()
        {
            var merchantGenders = await shopDbContext.MerchantGenders
                .Include(a => a.MerchantGenderRels.Where(b => b.Active))
                .Where(a => a.Active).ToListAsync();

            var merchantGenderDtos = new List<MerchantGenderDto>();
            foreach (var merchantGender in merchantGenders)
            {
                var ids = merchantGender.MerchantGenderRels.Select(a => a.FkMerchantId).ToList();
                merchantGenderDtos.Add(new MerchantGenderDto
                {
                    Active = merchantGender.Active,
                    CreatedDate = merchantGender.CreatedDate,
                    LastModifiedDate = merchantGender.LastModifiedDate,
                    Name = merchantGender.Name,
                    Id = merchantGender.Id,
                    Type = merchantGender.Type,
                    MerchantGenderRels = ids
                });
            }


            return merchantGenderDtos;
        }

        public async Task UpdateMerchantGendersAsync(MerchantGenderDto merchantGenderDto)
        {
            var merchantGendersRel = shopDbContext.MerchantGenderRels
                .Where(a => a.FkMerchantGenderId == merchantGenderDto.Id).ToList();
            foreach (var merchantRel in merchantGendersRel)
            {
                if (merchantGenderDto.MerchantGenderRels.Contains(merchantRel.FkMerchantId)) continue;

                merchantGenderDto.MerchantGenderRels.Remove(merchantRel.FkMerchantId);
                merchantRel.Active = false;
                merchantRel.LastModifiedDate = DateTime.UtcNow;
            }

            foreach (var merchantId in merchantGenderDto.MerchantGenderRels)
            {
                var existingMerchantRel = merchantGendersRel.FirstOrDefault(a => a.FkMerchantId == merchantId);
                if (existingMerchantRel == null)
                {
                    shopDbContext.MerchantGenderRels.Add(new MerchantGenderRel
                    {
                        Active = true,
                        CreatedDate = DateTime.UtcNow,
                        LastModifiedDate = DateTime.UtcNow,
                        FkMerchantId = merchantId,
                        FkMerchantGenderId = merchantGenderDto.Id,
                    });
                }
                else
                {
                    if (existingMerchantRel.Active) continue;

                    existingMerchantRel.Active = true;
                    existingMerchantRel.LastModifiedDate = DateTime.UtcNow;
                    shopDbContext.Update(existingMerchantRel);
                }
            }


            await shopDbContext.SaveChangesAsync();

            cacheService.RemoveDataWildcard("AllowedMerchants_");
        }

        private async Task<ProductPaginationResponse> ProductHandling(int page, int size, string email,
            List<MerchantRelevance> topMerchants, string cacheKeyExists, string cacheKeyNewValue, string type,
            int maxProductsPrMerchant)
        {
            //Check if redis already have page cached
            var productResponse = await cacheService.GetData<ProductPaginationResponse>(cacheKeyExists, true) ??
                                  new ProductPaginationResponse
                                  {
                                      Data = new ProductPaginationDto
                                      {
                                          Products = [],
                                          LastPage = 1
                                      }
                                  };

            if (productResponse.Data.Products.Count > 0)
            {
                //Check for favorites
                foreach (var productRef in productResponse.Data.Products)
                {
                    productRef.Favored = await merchantService.CheckFavorite(productRef.Id, email);
                }
            }
            else
            {
                //Check what products have already been loaded
                var productsAlreadyLoaded = new List<ProductRefDto>();
                if (page > 1)
                {
                    for (int i = 1; i <= page; i++)
                    {
                        var productPagination =
                            await cacheService.GetData<ProductPaginationResponse>(
                                $"ShopService_GetProductsByEmailAndFiltersAsync_{type}_{email}_{i}_{size}", true);
                        if (productPagination != null)
                        {
                            productsAlreadyLoaded.AddRange(productPagination.Data.Products);
                        }
                    }
                }

                var merchantPaginationDto = await cacheService.GetData<List<CuratedProduct>>(cacheKeyNewValue, true) ??
                                            [];
                List<CuratedProductEvents> products = [];
                foreach (var merchantScore in topMerchants)
                {
                    var curatedProduct =
                        merchantPaginationDto.SingleOrDefault(a => a.MerchantId == merchantScore.MerchantId);
                    if (curatedProduct != null)
                    {
                        products.AddRange(curatedProduct.Products.Take(maxProductsPrMerchant));
                    }
                }

                Console.WriteLine($"Found {products.Count} products");
                var totalProducts = products.Count;
                products.RemoveAll(a => productsAlreadyLoaded.Select(b => b.Id).Contains(a.ProductRefDto?.Id));
                Shuffle(products);
                Console.WriteLine($"{products.Count} products left");

                foreach (var curatedProductEvent in products.Where(_ => productResponse.Data.Products.Count < size))
                {
                    curatedProductEvent.ProductRefDto!.Favored =
                        await merchantService.CheckFavorite(curatedProductEvent.ProductRefDto.Id, email);
                    productResponse.Data.Products.Add(curatedProductEvent.ProductRefDto);
                }

                productResponse.Data.LastPage = (int) Math.Ceiling((decimal) totalProducts / size);
                cacheService.SetData(cacheKeyExists, productResponse, TimeSpan.FromHours(1), true);
            }

            return productResponse;
        }

        private void Shuffle<T>(List<T> list)
        {
            Random rng = new Random();
            int n = list.Count;
            while (n > 1)
            {
                n--;
                int k = rng.Next(n + 1);
                (list[k], list[n]) = (list[n], list[k]);
            }
        }


        public async Task ValidateSectionProductsValidity()
        {
            var activeSections = (await GetSectionsAsync(true)).Where(a => a.Active).ToList();
            var sectionsToValidate = activeSections.Where(a => a.Type == SectionTypes.Product).ToList();

            using var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");

            var anyProductsToBeUpdated = false;

            foreach (var section in sectionsToValidate)
            {
                foreach (var product in section.SectionProducts)
                {
                    var existingProduct = await shopDbContext.SectionProducts.FirstOrDefaultAsync(a => a.FkProductId == product.Id);
                    if (existingProduct == null) continue;

                    bool shouldInvalidate = false;
                    string invalidationReason = string.Empty;

                    // Check if deactivated by merchant
                    if (await merchantService.IsProductDeactivatedByMerchant(product.Id))
                    {
                        shouldInvalidate = true;
                        invalidationReason = "Product is deactivated by merchant";
                    }
                    else
                    {
                        // Retry logic for HTTP check
                        const int maxRetries = 3;
                        int attempt = 0;
                        bool exists = false;
                        Exception? lastException = null;

                        while (attempt < maxRetries && !exists)
                        {
                            try
                            {
                                var response = await httpClient.GetAsync(product.ProductUrl);
                                if (response.IsSuccessStatusCode ||
                                    response.StatusCode == HttpStatusCode.Found ||
                                    response.StatusCode == HttpStatusCode.Redirect ||
                                    response.StatusCode == HttpStatusCode.MovedPermanently)
                                {
                                    exists = true;
                                }
                                else if ((int)response.StatusCode >= 500 || response.StatusCode == (HttpStatusCode)429)
                                {
                                    // Retry on server errors or rate limiting
                                    await Task.Delay(500 * (attempt + 1));
                                }
                                else
                                {
                                    // Client error, treat as invalid
                                    invalidationReason = $"Product URL returned status {response.StatusCode}";
                                    shouldInvalidate = true;
                                    break;
                                }
                            }
                            catch (Exception ex)
                            {
                                lastException = ex;
                                await Task.Delay(500 * (attempt + 1));
                            }
                            attempt++;
                        }

                        if (!exists && !shouldInvalidate)
                        {
                            shouldInvalidate = true;
                            invalidationReason = lastException != null
                                ? $"Exception: {lastException.Message}"
                                : "Product URL did not respond successfully after retries";
                        }
                    }

                    // Update invalidation status
                    if (shouldInvalidate)
                    {
                        if (!existingProduct.IsInvalidated || existingProduct.InvalidationReason != invalidationReason)
                        {
                            existingProduct.IsInvalidated = true;
                            existingProduct.InvalidationReason = invalidationReason;
                            shopDbContext.SectionProducts.Update(existingProduct);
                            anyProductsToBeUpdated = true;
                        }
                    }
                    else
                    {
                        if (existingProduct.IsInvalidated)
                        {
                            existingProduct.IsInvalidated = false;
                            existingProduct.InvalidationReason = string.Empty;
                            shopDbContext.SectionProducts.Update(existingProduct);
                            anyProductsToBeUpdated = true;
                        }
                    }
                }
            }

            if (anyProductsToBeUpdated)
            {
                await shopDbContext.SaveChangesAsync();
            }
        }
        
        public async Task CheckAllImagesExist()
        {
            int skip = 0;
            const int batchSize = 1000;
            Dictionary<long, string> products;
        
            Console.WriteLine("Starting image check...");
            
            do
            {
                products = await merchantService.GetAllActiveProducts(skip, batchSize);
                Console.WriteLine($"Checking {products.Count} images in batch starting at {skip}...");
        
                var tasks = new List<Task>();
                foreach (var product in products)
                {
                    //Console.WriteLine($"Checking image {tasks.Count + 1} of {products.Count}...");
        
                    var image = string.Empty;
                    if (!string.IsNullOrEmpty(product.Value))
                    {
                        var images = JsonSerializer.Deserialize<List<ProductImageDto>>(product.Value);
                        image = images?.FirstOrDefault()?.Src ?? string.Empty;
                    }
        
                    tasks.Add(CheckImageExistsAsync((int)product.Key, image));
                }
        
                await Task.WhenAll(tasks);
                skip += batchSize;
            } while (products.Count == batchSize);
        }
        
        private async Task<string> CheckImageExistsAsync(int productId, string imageUrl)
        {
            var cacheKey = $"CheckImageExists_{productId}";
            //Console.WriteLine($"Cache key: {cacheKey}");

            return await cacheService.GetDataWithCacheLockAsync(
                cacheKey,
                async () =>
                {
                    try
                    {
                        using var request = new HttpRequestMessage(HttpMethod.Head, imageUrl);
                        var imageResponse = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead);

                        // Check if the response is successful and the content type is an image
                        bool exists = imageResponse.IsSuccessStatusCode && imageResponse.Content.Headers.ContentType.MediaType.StartsWith("image");
                        //Console.WriteLine($"Image exists: {exists}");
                        return exists ? imageUrl : string.Empty;
                    }
                    catch (Exception ex)
                    {
                        // If an exception occurs (e.g., network error), assume the image does not exist
                        //Console.WriteLine($"Exception occurred: {ex.Message}");
                        return string.Empty;
                    }
                },
                memoryCacheExpiry: TimeSpan.FromDays(1),
                redisCacheExpiry: TimeSpan.FromDays(2)
            );
        }
    }
}