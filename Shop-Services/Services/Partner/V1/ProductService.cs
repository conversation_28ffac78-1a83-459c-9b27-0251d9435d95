using System.Globalization;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Audience.Services.Audience;
using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Core.Search;
using Elastic.Clients.Elasticsearch.QueryDsl;
using Integration.Services.Static;
using Merchant_Services.Models.ModelsDal.Merchant;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using RabbitMQ.Client;
using Shared.Constants;
using Shared.Dto.Curated.Product;
using Shared.Dto.MerchantScore;
using Shared.Dto.Shop;
using Shared.Dto.Shop.Categories;
using Shared.Dto.Shop.Product;
using Shared.Dto.Shop.Sections;
using Shared.Dto.Webshop;
using Shared.Elastic.Models;
using Shared.Elastic.Models.ElasticShopCategoriesInteracts;
using Shared.Elastic.Models.ElasticShopLog;
using Shared.Elastic.Models.ElasticShopProductsDisplays;
using Shared.Elastic.Models.ElasticShopProductsInteracts;
using Shared.Elastic.Models.ElasticShopProductsRedirects;
using Shared.Elastic.ShopProducts;
using Shared.Helpers.Deserializers;
using Shared.Models;
using Shared.Models.Customer;
using Shared.Services;
using Shared.Services.Cache;
using Shared.Services.MerchantRelevance;
using Shared.Services.Partner;
using Shared.Services.Setting;
using Shop_Services.Models;
using Shop_Services.Models.ElasticSearch;
using Shop_Services.Models.ModelsDal.Shop;
using Shop_Services.Models.ModelsDto.Partner.Products;
using Shop_Services.Models.ModelsDto.Query_Parameters;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;
using JsonSerializer = System.Text.Json.JsonSerializer;
using ApiKey = Elastic.Transport.ApiKey;
using Field = Elastic.Clients.Elasticsearch.Field;
using MinimumShouldMatch = Elastic.Clients.Elasticsearch.MinimumShouldMatch;
using GrowthBook;
using Partner_Services.Services.General;
using Shop_Services.Models.Exposure;
using Shared.Elastic.Models.ElasticExposure;
using System.Text.Json;
using Customer_Services.Models.ModelsDal.Customer;
using Recommendation_Services.Services;
using Recommendation_Services.Models;
using Shop_Services.Services.ElasticSearchService;

namespace Shop_Services.Services.Partner.V1;

public class ProductService(
    ILogger logger,
    //IConnection rabbitConnection,
    [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud,
    IMerchantService merchantService,
    ICacheService cacheService,
    ICustomerService customerService,
    ISettingService settingService,
    IElasticShopProductService elasticShopProductService,
    ShopDbContext shopDbContext,
    Microsoft.Extensions.Configuration.IConfiguration configuration,
    IMerchantRelevanceService merchantRelevanceService,
    IMemoryCache memoryCache,
    MerchantDbContext merchantDbContext,
    VaultSettings vaultSettings,
    IPartnerContext partnerContext,
    IGrowthBook growthBook,
    IPartnerService partnerService,
    IOfferRecommendationService offerRecommendationService,
    IElasticSearchService elasticSearchService)
    : IProductService
{
    private const string Channel = "app";


    /// <summary>
    /// This function needs to be migrated to ShopService
    /// The implementation should use the new Elasticsearch-based search functionality
    /// and follow the pattern of GetProductsByEmailAndFiltersAsync in ShopService
    /// </summary>
    public async Task<ProductPartnerPaginationResponse> SearchProductsAsync(SearchProductParameters searchProductParameters)
    {
        if (searchProductParameters.UseMock)
        {
            var mockProducts = LoadMockProducts();
            var page = searchProductParameters.Page > 0 ? searchProductParameters.Page : 1;
            var size = searchProductParameters.Size > 0 ? searchProductParameters.Size : 10;
            var paged = mockProducts.Skip((page - 1) * size).Take(size).ToList();
            var lastPage = (long)Math.Ceiling((double)mockProducts.Count / size);
            return CreatePaginationSuccessResponse(paged, lastPage, "Mock products returned");
        }

        // Get customer data for relevance-based sorting and personalization
        var contact = await customerService.GetCustomerForMerchantRelevance(searchProductParameters.Email);
        var gender = contact?.Gender ?? "Unknown";

        // Prepare search parameters with boosts and filters
        var searchOptimizeAppDto = new SearchOptimizeAppDto
        {
            Search = searchProductParameters.Search ?? "",
            NegativeTags = string.Empty,
            ProductNameBoost = 4.0,
            ProductDescriptionBoost = 1.0,
            Bm25TextSearchBoost = 1.0f,
            CategoryBoost = 2.5f,
            MinimumMatchPercentage = 100,
            Page = searchProductParameters.Page - 1,
            Size = searchProductParameters.Size,
            MerchantsToFilter = "",
            Randomize = false,
            MinimumScore = 2
        };

        // Perform the search using the new decoupled Elasticsearch service
        var response = await elasticSearchService.PerformElasticSearch(searchOptimizeAppDto);

        // Process the search response and convert to the required format
        var productResponse = await elasticSearchService.ProcessElasticSearchResponse(
            response, searchOptimizeAppDto,
            new ProductFilterDto
            {
                Search = searchProductParameters.Search,
                SearchGuid = searchProductParameters.SearchIdentifier
            },
            searchProductParameters.Size,
            searchProductParameters.Page - 1,
            searchProductParameters.Email,
            DateTime.UtcNow);

        // Optimize transformation with batch operations and lookup optimization
        // Create merchant assets lookup for O(1) access instead of FirstOrDefault
        var merchantAssets = await merchantService.GetMerchantLogosAsync();
        var merchantAssetsLookup = merchantAssets.ToDictionary(a => a.FkMerchantId, a => a.Src);
        
        // Batch fetch all product details to avoid N+1 queries and threading issues
        var internalProductIds = productResponse.Data.Products.Select(p => p.Id).ToList();
        var productDetails = await merchantService.GetProductsByInternalProductIds(internalProductIds);
        
        // Convert the processed response to the partner-specific format with batch optimization
        var partnerProducts = productResponse.Data.Products
            .Where(p =>
            {
                var productDetail = productDetails.GetValueOrDefault(p.Id);
                var salePrice = p.SalePrice > 0 ? p.SalePrice : p.NormalPrice;
                var productImages = ImageDeserializer.DeserializeProductImages(productDetail?.ProductImages);
                
                // Filter out products with invalid data
                return salePrice > 0 && productDetail != null && productImages.Count > 0;
            })
            .Select(p =>
            {
                var productDetail = productDetails.GetValueOrDefault(p.Id);
                var redirectLink = productDetail != null 
                    ? BuildRedirectUrl(searchProductParameters.Email, contact?.PartnerGuid ?? "", productDetail.Permalink ?? "", p.Id, productDetail.FkMerchantId.ToString(), p.MerchantName)
                    : string.Empty;
                var salePrice = p.SalePrice > 0 ? p.SalePrice : p.NormalPrice;
                var productImages = ImageDeserializer.DeserializeProductImages(productDetail?.ProductImages);
                
                return new ProductPartnerDto
                {
                    Id = p.Id,
                    Name = p.Name,
                    Description = productDetail?.FormattedDescription ?? productDetail?.Description ?? p.Description ?? "",
                    Favored = p.Favored,
                    IncludesVariants = p.IncludesVariants,
                    ProductImages = productImages,
                    NormalPrice = p.NormalPrice,
                    SalePrice = salePrice,
                    OnSale = p.OnSale,
                    RedirectLink = redirectLink,
                    MerchantLogoSrc = merchantAssetsLookup.TryGetValue((int)p.MerchantId, out var logoSrc) ? logoSrc : "",
                    MerchantName = p.MerchantName
                };
            }).ToList();

        return CreatePaginationSuccessResponse(partnerProducts, productResponse.Data.LastPage, "Products found");
    }

    public async Task<ProductPartnerResponse> GetProductByProductIdAndEmailAsync(string productId, string email, bool useMock)
    {
        // Input validation
        if (string.IsNullOrWhiteSpace(productId))
        {
            return CreateErrorResponse(StatusCode.BadRequest, "ProductId cannot be null or empty");
        }

        if (string.IsNullOrWhiteSpace(email))
        {
            return CreateErrorResponse(StatusCode.BadRequest, "Email cannot be null or empty");
        }

        if (useMock)
        {
            return await GetMockProductAsync(productId);
        }

        return await GetRealProductAsync(productId, email);
    }

    private async Task<ProductPartnerResponse> GetMockProductAsync(string productId)
    {
        var mockProducts = LoadMockProducts();
        var mockProduct = mockProducts.FirstOrDefault(p => p.Id == productId);
        
        if (mockProduct == null)
        {
            return CreateErrorResponse(StatusCode.NotFound, "Mock product not found");
        }

        return CreateSuccessResponse(mockProduct, "Mock product returned");
    }

    private async Task<ProductPartnerResponse> GetRealProductAsync(string productId, string email)
    {
        // Check cache first
        var cacheKey = $"ProductPartner_{productId}_{email}";
        var cachedProduct = await cacheService.GetData<ProductPartnerDto>(cacheKey);
        
        if (cachedProduct != null)
        {
            return CreateSuccessResponse(cachedProduct, "Product found");
        }

        // Fetch data in parallel
        var contactTask = GetCustomerContactAsync(email);
        var productDataTask = GetProductDataAsync(productId, email);

        await Task.WhenAll(contactTask, productDataTask);

        var contact = await contactTask;
        var productData = await productDataTask;

        if (productData == null)
        {
            return CreateErrorResponse(StatusCode.NotFound, "Provided ProductId was not associated with an existing product");
        }

        // Build the response DTO
        var productDto = await BuildProductPartnerDtoAsync(productData, contact, email);

        // Cache the result
        cacheService.SetData(cacheKey, productDto, TimeSpan.FromMinutes(30));

        return CreateSuccessResponse(productDto, "Product found");
    }

    private async Task<Customer_Services.Models.ModelsDal.Customer.Customer?> GetCustomerContactAsync(string email)
    {
        var cacheKey = $"Customer_{email}";
        var cachedContact = await cacheService.GetData<Customer_Services.Models.ModelsDal.Customer.Customer>(cacheKey);
        
        if (cachedContact != null)
        {
            return cachedContact;
        }

        var contact = await customerService.GetByEmailAsync(email);
        if (contact != null)
        {
            cacheService.SetData(cacheKey, contact, TimeSpan.FromMinutes(15));
        }

        return contact;
    }

    private async Task<ProductDataDto?> GetProductDataAsync(string productId, string email)
    {
        var product = await merchantDbContext.Products
            .AsNoTracking()
            .Where(p => p.InternalProductId == productId)
            .Select(p => new ProductDataDto
            {
                InternalProductId = p.InternalProductId,
                Name = p.Name,
                Description = p.Description,
                ShortDescription = p.ShortDescription,
                ProductImages = p.ProductImages,
                Permalink = p.Permalink,
                RegularPrice = p.RegularPrice,
                Price = p.Price,
                FkMerchantId = p.FkMerchantId,
                MerchantName = p.FkMerchant.Name,
                DisplayName = p.FkMerchant.DisplayName ?? p.FkMerchant.Name,
                MerchantLogoSrc = p.FkMerchant.MerchantAssets
                    .Where(a => a.FkMerchantAssetTypeId == 2)
                    .Select(a => a.Src)
                    .FirstOrDefault() ?? string.Empty,
                IsFavorited = p.ProductFavorites.Any(f => f.Email == email && f.Active),
                VariantCount = p.Variants.Count
            })
            .FirstOrDefaultAsync();

        return product;
    }

    private async Task<ProductPartnerDto> BuildProductPartnerDtoAsync(ProductDataDto productData, Customer? contact, string email)
    {
        var partnerGuid = contact?.PartnerGuid ?? string.Empty;
        var images = ImageDeserializer.DeserializeProductImages(productData.ProductImages);
        var redirectUrl = BuildRedirectUrl(email, partnerGuid, productData.Permalink, 
            productData.InternalProductId, productData.FkMerchantId.ToString(), productData.MerchantName);

        return new ProductPartnerDto
        {
            Id = productData.InternalProductId,
            Name = productData.Name,
            Description = productData.Description ?? productData.ShortDescription ?? string.Empty,
            Favored = productData.IsFavorited,
            IncludesVariants = productData.VariantCount > 0,
            ProductImages = images,
            NormalPrice = productData.RegularPrice ?? 0,
            SalePrice = productData.Price ?? 0,
            OnSale = productData.Price != null && productData.Price != 0 && (productData.RegularPrice != productData.Price),
            RedirectLink = redirectUrl,
            MerchantLogoSrc = productData.MerchantLogoSrc,
            MerchantName = productData.DisplayName
        };
    }

    public async Task<ProductPartnerPaginationResponse> GetFavoritesProductsAsync(string email, bool useMock, int page, int size)
    {
        if (useMock)
        {
            // Takes some random products from the mock products
            var mockProducts = LoadMockProducts();
            var randomProducts = mockProducts.OrderBy(p => Guid.NewGuid()).Take(size).ToList();
            var paged = randomProducts.Skip((page - 1) * size).Take(size).ToList();
            var lastPage = (long)Math.Ceiling((double)mockProducts.Count / size);

            // Sets the Favorites to true for the products
            foreach (var product in paged)
            {
                product.Favored = true;
            }
            return CreatePaginationSuccessResponse(paged, lastPage, "Mock products returned");
        }
        
        // Define a cache key based on the user's email
        var cacheKey = $"FavoriteProducts_{email}_{partnerContext.PartnerId}";

        // Attempt to retrieve the favorite products from the cache
        var products = await cacheService.GetData<List<Product>>(cacheKey, true);

        if (products == null)
        {
            // If not found in cache, retrieve from the database
            products = await merchantService.GetFavorites(email);

            // Store the retrieved favorite products in the cache
            cacheService.SetData(cacheKey, products, TimeSpan.FromHours(1), true);
        }

        if (products.Count == 0)
        {
            return CreatePaginationErrorResponse(StatusCode.NotFound, "No products found");
        }

        // Build the product list with optimized transformation
        var contact = await customerService.GetCustomerForMerchantRelevance(email);
        var productDtos = products.Select(product =>
        {
            var image = "";
            if (!string.IsNullOrEmpty(product.ProductImages))
            {
                var images = JsonSerializer.Deserialize<List<ProductImageDto>>(product.ProductImages);
                image = images?.FirstOrDefault()?.Src ?? "";
            }

            var redirectLink = !string.IsNullOrEmpty(product.Permalink) 
                ? BuildRedirectUrl(email, contact?.PartnerGuid ?? "", product.Permalink, product.InternalProductId, product.FkMerchantId.ToString(), product.FkMerchant?.Name ?? "")
                : string.Empty;

            return new ProductPartnerDto
            {
                Id = product.InternalProductId,
                Name = product.Name,
                Description = product.FormattedDescription ?? product.Description ?? "",
                NormalPrice = product.RegularPrice ?? 0,
                SalePrice = product.Price ?? 0,
                OnSale = product.RegularPrice != product.Price,
                Favored = true,
                IncludesVariants = product.Variants.Count > 0,
                ProductImages = [image],
                MerchantName = product.FkMerchant?.Name ?? "n/a",
                MerchantLogoSrc = product.FkMerchant?.MerchantAssets.FirstOrDefault()?.Src ?? string.Empty,
                RedirectLink = redirectLink
            };
        }).ToList();

        // Calculate pagination
        var start = size * (page - 1);
        var totalProducts = productDtos.Count;
        var totalPages = (int)Math.Ceiling((double)totalProducts / size);
        var paginatedProducts = productDtos.Skip(start).Take(size).ToList();

        return CreatePaginationSuccessResponse(paginatedProducts, totalPages, "Favorite products found");
    }

    public async Task<ProductPartnerPaginationResponse> GetProductRecommendationsAsync(string email, string strategy, int size, int page, bool useMock)
    {
        // Balsa Here
        // Input validation
        if (string.IsNullOrWhiteSpace(email))
        {
            return CreatePaginationErrorResponse(StatusCode.BadRequest, "Email cannot be null or empty");
        }

        // If Strategy is not one of the valid strategies, return an error
        if (!OfferRecommendationTypes.IsValidStrategy(strategy))
        {
            return CreatePaginationErrorResponse(StatusCode.BadRequest, $"Invalid strategy: {strategy}");
        }

        if (size <= 0)
        {
            return CreatePaginationErrorResponse(StatusCode.BadRequest, "Size must be greater than 0");
        }

        if (page <= 0)
        {
            return CreatePaginationErrorResponse(StatusCode.BadRequest, "Page must be greater than 0");
        }

        if (useMock)
        {
            return await GetMockRecommendationsAsync(email, strategy, size, page);
        }

        try
        {
            // Get customer data for personalized recommendations
            var contact = await customerService.GetCustomerForMerchantRelevance(email);
            
            // Create a cache key for the complete set of recommendations for this user/strategy
            var cacheKey = $"ProductRecommendations_{email}_{strategy}_{contact?.Gender}_{contact?.Age}";
            
            // Try to get the complete set of processed products from cache
            var allDistributedProducts = await cacheService.GetData<List<ProductPartnerDto>>(cacheKey);
            
            if (allDistributedProducts == null)
            {
                // Request a large, consistent amount of recommendations (not dependent on page number)
                // This ensures we get the same set of products regardless of which page is requested
                var requestSize = 500; // Fixed size - enough to cover multiple pages
                
                // Get recommendations from the offer recommendation service
                var recommendations = await offerRecommendationService.GetRecommendationEntitiesAsync(
                    strategy, requestSize, contact?.Gender, contact?.Age);

                if (recommendations == null || recommendations.Count == 0)
                {
                    return CreatePaginationErrorResponse(StatusCode.NotFound, "No recommendations found for the given strategy");
                }

                // Extract product IDs from recommendation items and deduplicate immediately
                // Use a dictionary to track the highest score for each unique product
                var uniqueProductScores = new Dictionary<string, double>();
                
                foreach (var recommendation in recommendations)
                {
                    if (recommendation.OfferItems == null) continue;
                    
                    foreach (var item in recommendation.OfferItems.Where(item => item.OfferType == "Product"))
                    {
                        var productId = item.OfferId.ToString();
                        
                        // Keep only the highest score for each unique product
                        if (!uniqueProductScores.ContainsKey(productId) || uniqueProductScores[productId] < item.Score)
                        {
                            uniqueProductScores[productId] = item.Score;
                        }
                    }
                }

                var uniqueProductIds = uniqueProductScores.Keys.ToList();

                if (uniqueProductIds.Count == 0)
                {
                    return CreatePaginationErrorResponse(StatusCode.NotFound, "No product recommendations found for the given strategy");
                }

                // Randomize the product ids for the same customer - Temporary Solution to avoid the same products being shown to the same customer
                //var randomizedProductIds = await HandleRandomizationOfProductIds(uniqueProductIds, contact);

                // Batch fetch all products and merchant assets
                var productDetails = await merchantService.GetProductsByProductIds(uniqueProductIds.Select(id => long.Parse(id)).ToList());
                var merchantAssets = await merchantService.GetMerchantLogosAsync();
                var merchantAssetsLookup = merchantAssets.ToDictionary(a => a.FkMerchantId, a => a.Src);
                var favoriteProducts = await merchantService.GetFavorites(email);
                var favoriteProductsLookup = favoriteProducts.ToDictionary(p => p.InternalProductId, p => p);

                // Create temporary objects with merchant info for round-robin distribution
                // Apply all filtering criteria here to ensure we only work with valid, unique products
                var productsWithMerchantInfo = productDetails.Values
                    .Where(product => product.Price.HasValue || product.RegularPrice.HasValue)
                    .Where(product => !string.IsNullOrEmpty(product.ProductImages)) // Only include products with images
                    .Where(product => 
                    {
                        var salePrice = product.Price ?? product.RegularPrice ?? 0;
                        var productImages = ImageDeserializer.DeserializeProductImages(product.ProductImages);
                        
                        // Filter out products with no sale price or no images
                        if (salePrice == 0 || productImages.Count == 0)
                            return false;

                        // If the strategy is CuratedProductsPriceDrop, filter out products that are not on sale
                        if (strategy == OfferRecommendationTypes.CuratedProductsPriceDrop)
                        {
                            if (product.Price.HasValue && product.RegularPrice.HasValue && product.Price.Value >= product.RegularPrice.Value)
                                return false;
                        }

                        return true;
                    })
                    .Select(product =>
                    {
                        var redirectLink = !string.IsNullOrEmpty(product.Permalink) 
                            ? BuildRedirectUrl(email, contact?.PartnerGuid ?? "", product.Permalink, product.InternalProductId, product.FkMerchantId.ToString(), product.FkMerchant?.Name ?? "")
                            : string.Empty;
                        var salePrice = product.Price ?? product.RegularPrice ?? 0;
                        var productImages = ImageDeserializer.DeserializeProductImages(product.ProductImages);

                        // Get product images
                        var imageUrl = "";
                        if (!string.IsNullOrEmpty(product.ProductImages))
                        {
                            try
                            {
                                var images = JsonSerializer.Deserialize<List<ProductImageDto>>(product.ProductImages);
                                imageUrl = images?.FirstOrDefault()?.Src ?? "";
                            }
                            catch
                            {
                                // Fallback if JSON parsing fails
                                imageUrl = "";
                            }
                        }

                        return new ProductWithMerchantInfo
                        {
                            ProductDto = new ProductPartnerDto
                            {
                                Id = product.InternalProductId,
                                Name = product.Name,
                                Description = product.FormattedDescription ?? product.Description ?? "",
                                Favored = favoriteProductsLookup.ContainsKey(product.InternalProductId),
                                IncludesVariants = product.Variants?.Count > 0,
                                ProductImages = productImages,
                                NormalPrice = product.RegularPrice ?? 0,
                                SalePrice = salePrice,
                                OnSale = product.Price.HasValue && product.RegularPrice.HasValue && product.Price.Value < product.RegularPrice.Value,
                                RedirectLink = redirectLink,
                                MerchantLogoSrc = merchantAssetsLookup.TryGetValue(product.FkMerchantId, out var logoSrc) ? logoSrc : "",
                                MerchantName = product.FkMerchant?.DisplayName ?? product.FkMerchant?.Name ?? ""
                            },
                            MerchantId = product.FkMerchantId,
                            Score = uniqueProductScores.GetValueOrDefault(product.InternalProductId, 0)
                        };
                    })
                    .ToList();

                // Apply round-robin distribution by merchant for better diversity
                allDistributedProducts = DistributeRandomizedProductsRoundRobin(productsWithMerchantInfo);
                
                // Cache the complete processed set for consistency across page requests
                cacheService.SetData(cacheKey, allDistributedProducts, TimeSpan.FromMinutes(30));
            }

            // Calculate pagination from the complete unique set
            var totalUniqueProducts = allDistributedProducts.Count;
            var totalPages = (long)Math.Ceiling((double)totalUniqueProducts / size);
            
            // Apply pagination to the unique, distributed products
            var paginatedProducts = allDistributedProducts.Skip((page - 1) * size).Take(size).ToList();

            return CreatePaginationSuccessResponse(paginatedProducts, totalPages, $"Recommendations found using strategy: {strategy}");
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error getting product recommendations for email: {Email}, strategy: {Strategy}", email, strategy);
            return CreatePaginationErrorResponse(StatusCode.ServerError, "An error occurred while getting recommendations");
        }
    }

    private async Task<ProductPartnerPaginationResponse> GetMockRecommendationsAsync(string email, string strategy, int size, int page)
    {
        // Load mock products and simulate recommendations
        var mockProducts = LoadMockProducts();
        var randomProducts = mockProducts.OrderBy(p => Guid.NewGuid()).Take(size * 3).ToList(); // Get more for pagination
        
        // Apply pagination
        var totalProducts = randomProducts.Count;
        var totalPages = (long)Math.Ceiling((double)totalProducts / size);
        var paginatedProducts = randomProducts.Skip((page - 1) * size).Take(size).ToList();

        return CreatePaginationSuccessResponse(paginatedProducts, totalPages, $"Mock recommendations returned for strategy: {strategy}");
    }

    public async Task<HttpResponseDto> ToggleFavoriteAsync(ShopFavoriteRequestDto favoriteRequest)
    {
        // Temporary Commented out, as Happy Pay gets so many customers each day,
        // who would not be able to toggle favorites before the next day
        
        //var contact = await customerService.GetCustomerForMerchantRelevance(favoriteRequest.Email);
        /*if (contact == null)
        {
            return CreateHttpErrorResponse(StatusCode.NotFound, "Customer not found");
        }*/
        
        await merchantService.UpdateFavoriteProduct(favoriteRequest);
        cacheService.RemoveData($"FavoriteProducts_{favoriteRequest.Email}_{partnerContext.PartnerId}");
        return CreateHttpSuccessResponse("Favorite toggled");
    }

    public async Task<HttpResponseDto> AddBehaviorEventAsync(ShopEventDto shopEventDto)
    {
        try
        {
            if (!ShopEventTypes.IsValidEventType(shopEventDto.EventType))
            {
                logger.ForContext("service_name", GetType().Name).Warning(
                    "Received an unknown shop event: {event} from Customer Email: {Email}",
                    shopEventDto.EventType, shopEventDto.CustomerEmail);

                return CreateHttpErrorResponse(StatusCode.BadRequest, 
                    $"The EventType: {shopEventDto.EventType} is unknown - known EventTypes are: {string.Join(", ", ShopEventTypes.GetKnownEventTypes())}");
            }

            await ProcessShopEventAsync(shopEventDto);
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(
                ex,
                "{event} sending to {component} exchange '{exchange}' with routing key '{routingKey}' Shop Event object: {ShopEvent}",
                "Failed", "RabbitMQ", "customer", "customer_shop_event",
                JsonSerializer.Serialize(shopEventDto));
            return CreateHttpErrorResponse(StatusCode.ServerError, "Adding the Event was unsuccessful");
        }

        logger.ForContext("service_name", GetType().Name).Information(
            "Successfully added a new Event: {event} from Customer Email: {Email}",
            shopEventDto.EventType, shopEventDto.CustomerEmail);

        return CreateHttpSuccessResponse("Behavior event added");
    }

    private async Task ProcessShopEventAsync(ShopEventDto shopEventDto)
    {
        if (!string.IsNullOrEmpty(shopEventDto.ProductId))
        {
            await ProcessSingleProductEventAsync(shopEventDto);
        }
        else if (shopEventDto.ProductIds?.Count > 0)
        {
            await ProcessMultipleProductsEventAsync(shopEventDto);
        }
    }

    private async Task ProcessSingleProductEventAsync(ShopEventDto shopEventDto)
    {
        var product = await merchantService.GetProductByInternalProductId(shopEventDto.ProductId);
        if (product == null) return;

        if (ShopEventTypes.IsInteractEvent(shopEventDto.EventType))
        {
            await PublishEventAsync(shopEventDto, product, "interact");
        }
        else if (ShopEventTypes.IsRedirectEvent(shopEventDto.EventType))
        {
            await PublishEventAsync(shopEventDto, product, "redirect");
        }
    }

    private async Task ProcessMultipleProductsEventAsync(ShopEventDto shopEventDto)
    {
        var products = await GetProductsAsync(shopEventDto.ProductIds);
        if (products.Count == 0) return;

        if (ShopEventTypes.IsDisplayEvent(shopEventDto.EventType))
        {
            await PublishEventAsync(shopEventDto, products, "display");
        }
    }

    private async Task<List<Product>> GetProductsAsync(List<string> productIds)
    {
        // Use batch method to avoid N+1 queries
        var productDict = await merchantService.GetProductsByInternalProductIds(productIds);
        return productDict.Values.ToList();
    }
    
    private static ProductPartnerResponse CreateErrorResponse(StatusCode statusCode, string message)
    {
        return new ProductPartnerResponse
        {
            Data = null,
            Response = new HttpResponseDto
            {
                StatusCode = statusCode,
                Success = false,
                Message = message
            }
        };
    }

    private static ProductPartnerResponse CreateSuccessResponse(ProductPartnerDto product, string message)
    {
        return new ProductPartnerResponse
        {
            Data = product,
            Response = new HttpResponseDto
            {
                StatusCode = StatusCode.Ok,
                Success = true,
                Message = message
            }
        };
    }

    // Helper methods for HttpResponseDto creation
    private static HttpResponseDto CreateHttpErrorResponse(StatusCode statusCode, string message)
    {
        return new HttpResponseDto
        {
            StatusCode = statusCode,
            Success = false,
            Message = message
        };
    }

    private static HttpResponseDto CreateHttpSuccessResponse(string message)
    {
        return new HttpResponseDto
        {
            StatusCode = StatusCode.Ok,
            Success = true,
            Message = message
        };
    }

    // Helper methods for ProductPartnerPaginationResponse creation
    private static ProductPartnerPaginationResponse CreatePaginationSuccessResponse(List<ProductPartnerDto> products, long lastPage, string message)
    {
        return new ProductPartnerPaginationResponse
        {
            Data = new ProductPartnerPaginationDto
            {
                LastPage = lastPage,
                Products = products
            },
            Response = new HttpResponseDto
            {
                StatusCode = StatusCode.Ok,
                Success = true,
                Message = message
            }
        };
    }

    private static ProductPartnerPaginationResponse CreatePaginationErrorResponse(StatusCode statusCode, string message)
    {
        return new ProductPartnerPaginationResponse
        {
            Data = new ProductPartnerPaginationDto
            {
                Products = [],
                LastPage = 1
            },
            Response = new HttpResponseDto
            {
                StatusCode = statusCode,
                Success = false,
                Message = message
            }
        };
    }

    private async Task PublishEventAsync(ShopEventDto shopEventDto, Product product, string eventCategory)
    {
        var (elasticData, routingKey) = await CreateElasticDataAsync(shopEventDto, product, eventCategory);
        var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticData));
        AddRabbitEvent(actionBody, "customer", routingKey);
    }

    private async Task PublishEventAsync(ShopEventDto shopEventDto, List<Product> products, string eventCategory)
    {
        var (elasticData, routingKey) = await CreateElasticDataAsync(shopEventDto, products, eventCategory);
        var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticData));
        AddRabbitEvent(actionBody, "customer", routingKey);
    }

    private async Task<(object elasticData, string routingKey)> CreateElasticDataAsync(ShopEventDto shopEventDto, Product product, string eventCategory)
    {
        var useExposureIndices = growthBook.IsOn("valyrionservice_exposureindices");
        
        return eventCategory switch
        {
            "interact" when !useExposureIndices => (CreateElasticShopProductsInteracts(shopEventDto, product), "shop_product_interact"),
            "interact" => (await CreateElasticExposureProductsInteractsAsync(shopEventDto, product), "exposure_product_interact"),
            "redirect" when !useExposureIndices => (CreateElasticShopProductsRedirects(shopEventDto, product), "shop_product_redirect"),
            "redirect" => (await CreateElasticExposureProductsRedirectsAsync(shopEventDto, product), "exposure_product_redirect"),
            _ => throw new ArgumentException($"Unknown event category: {eventCategory}")
        };
    }

    private async Task<(object elasticData, string routingKey)> CreateElasticDataAsync(ShopEventDto shopEventDto, List<Product> products, string eventCategory)
    {
        var useExposureIndices = growthBook.IsOn("valyrionservice_exposureindices");
        
        return eventCategory switch
        {
            "display" when !useExposureIndices => (CreateElasticShopProductsDisplays(shopEventDto, products), "shop_product_display"),
            "display" => (await CreateElasticExposureProductsDisplaysAsync(shopEventDto, products), "exposure_product_display"),
            _ => throw new ArgumentException($"Unknown event category: {eventCategory}")
        };
    }

    private string BuildRedirectUrl(string email, string partnerGuid, string permalink, string productId,
        string webshopId = "", string webshopName = "")
    {
        var partnerId = partnerContext.PartnerId;   
        if(partnerId == 87317) {
            return $"https://redirect.happyads.io/redirectShop/{JwtTokenEncode(email, partnerGuid, permalink, productId, webshopId, webshopName)}";
        }

        return $"{configuration["RedirectService-Url"]}redirectShop/{JwtTokenEncode(email, partnerGuid, permalink, productId, webshopId, webshopName)}";
    }
    
    private string JwtTokenEncode(string email, string partnerGuid, string url, string productId,
        string webshopId = "", string webshopName = "")
    {
        var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(vaultSettings.JwtTokenKey));

        var tokenHandler = new JwtSecurityTokenHandler();

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity([
                new Claim("email", email),
                new Claim("url", url),
                new Claim("pid", productId),
                //new Claim("did", discountId),
                new Claim("wid", webshopId),
                new Claim("wname", webshopName),
                //new Claim("type", type),
                //new Claim("dcode", discountCode ?? ""),
                //new Claim("dcodeid", discountCodeId ?? ""),
                //new Claim("vbdai", debtorAccountId)
                new Claim("pguid", partnerGuid),
                new Claim("parid", partnerContext.PartnerId.ToString())
            ]),
            Issuer = vaultSettings.JwtIssuer,
            SigningCredentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha512Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);

        return tokenHandler.WriteToken(token);
    }

    private ElasticShopProductsInteracts CreateElasticShopProductsInteracts(ShopEventDto shopEvent, Product product)
    {
        return new ElasticShopProductsInteracts
        {
            Action_date = DateTime.UtcNow,
            Event_received = DateTime.UtcNow,
            Marketing = new ElasticMarketing
            {
                Channel = Channel,
                Event = shopEvent.EventType
            },
            Customer = new ElasticCustomer
            {
                Email = shopEvent.CustomerEmail
            },
            Merchant = new ElasticMerchant
            {
                Id = [product.FkMerchantId.ToString()],
                Name = [product.FkMerchant.Name]
            },
            Product = new ElasticProduct
            {
                Internal_product_id = [product.InternalProductId],
                Price = [product.Price ?? 0],
                Price_range = [PriceConverter.ConvertPrice(product.Price)],
            },
            Search = new ElasticSearch
            {
                Id = shopEvent.SearchGuid ?? "n/a"
            }
        };
    }

    private ElasticShopProductsRedirects CreateElasticShopProductsRedirects(ShopEventDto shopEvent, Product product)
    {
        return new ElasticShopProductsRedirects
        {
            Action_date = DateTime.UtcNow,
            Event_received = DateTime.UtcNow,
            Marketing = new ElasticMarketing
            {
                Channel = Channel,
                Event = shopEvent.EventType
            },
            Customer = new ElasticCustomer
            {
                Email = shopEvent.CustomerEmail
            },
            Merchant = new ElasticMerchant
            {
                Id = [product.FkMerchantId.ToString()],
                Name = [product.FkMerchant.Name]
            },
            Product = new ElasticProduct
            {
                Internal_product_id = [product.InternalProductId],
                Price = [product.Price ?? 0],
                Price_range = [PriceConverter.ConvertPrice(product.Price)],
            },
            Search = new ElasticSearch
            {
                Id = shopEvent.SearchGuid ?? "n/a"
            }
        };
    }

    private ElasticShopProductsDisplays CreateElasticShopProductsDisplays(ShopEventDto shopEvent,
        List<Product> products)
    {
        return new ElasticShopProductsDisplays
        {
            Action_date = DateTime.UtcNow,
            Event_received = DateTime.UtcNow,
            Marketing = new ElasticShopProductsDisplaysMarketing
            {
                Channel = Channel,
                Event = shopEvent.EventType,
                List_type = shopEvent.ListType ?? "Not Specified",
                List_id = shopEvent.ListId ?? 0,
            },
            Customer = new ElasticCustomer
            {
                Email = shopEvent.CustomerEmail
            },
            Merchant = new ElasticMerchant
            {
                Id = products.Select(product => product.FkMerchantId.ToString()).ToList(),
                Name = products.Select(product => product.FkMerchant.Name).ToList()
            },
            Product = new ElasticProduct
            {
                Internal_product_id = products.Select(product => product.InternalProductId).ToList(),
                Price = products.Select(product => product.Price ?? 0).ToList(),
                Price_range = products.Select(product => PriceConverter.ConvertPrice(product.Price)).ToList(),
            },
            Search = new ElasticSearch
            {
                Id = shopEvent.SearchGuid ?? "n/a"
            }
        };
    }


    private void AddRabbitEvent(Byte[] actionBody, string exchange, string routingKey)
    {
        using (var publishChannel = rabbitConnectionCloud.CreateModel())
        {
            publishChannel.BasicPublish(exchange: exchange,
                routingKey: routingKey,
                basicProperties: null,
                body: actionBody);
        }
    }

    private void Shuffle<T>(List<T> list)
    {
        Random rng = new Random();
        int n = list.Count;
        while (n > 1)
        {
            n--;
            int k = rng.Next(n + 1);
            (list[k], list[n]) = (list[n], list[k]);
        }
    }

    private async Task<ProductPaginationResponse> ProductHandling(int page, int size, string email,
        List<MerchantRelevance> topMerchants, string cacheKeyExists, string cacheKeyNewValue, string type,
        int maxProductsPrMerchant)
    {

        //Check if redis already have page cached
        var productResponse = await cacheService.GetData<ProductPaginationResponse>(cacheKeyExists, true) ??
                              new ProductPaginationResponse
                              {
                                  Data = new ProductPaginationDto
                                  {
                                      Products = [],
                                      LastPage = 1
                                  }
                              };


        if (productResponse.Data.Products.Count > 0)
        {
            //Check for favorites
            foreach (var productRef in productResponse.Data.Products)
            {
                productRef.Favored = await merchantService.CheckFavorite(productRef.Id, email);
            }
        }
        else
        {
            var interactedProductsCount = 0;

            //Check what products have already been loaded
            var productsAlreadyLoaded = new List<ProductRefDto>();
            if (page > 1)
            {
                for (int i = 1; i <= page; i++)
                {
                    var productPagination =
                        await cacheService.GetData<ProductPaginationResponse>(
                            $"ShopService_GetProductsByEmailAndFiltersAsync_{type}_{email}_{i}_{size}", true);
                    if (productPagination != null)
                    {
                        productsAlreadyLoaded.AddRange(productPagination.Data.Products);
                    }
                }
            }

            if (type == "3")
            {
                List<ProductRefDto> interactedProducts = new();

                var cachedInteractedProducts =
                    await cacheService.GetData<List<ProductRefDto>>($"ShopService_InteractedProducts_{email}", true);

                if (cachedInteractedProducts != null)
                {
                    interactedProducts = cachedInteractedProducts;
                }
                else
                {
                    // TODO - Replace Hardcoded Partner Id with Actual Partner Id 
                    var InteractProductsToShow =
                        Convert.ToInt32(
                            (await settingService.GetSettingAsync(52876, "ProductInteractShowNumber")).Value);
                    var InteractProductsRedirectBoost = Convert.ToDouble(
                        (await settingService.GetSettingAsync(52876, "ProductInteractRedirectBoost")).Value,
                        CultureInfo.InvariantCulture);
                    var InteractProductsLookBack =
                        Convert.ToInt32((await settingService.GetSettingAsync(52876, "ProductInteractLookBackDays"))
                            .Value);

                    var interactedProductIds = await elasticShopProductService.ShopProductsByEmail(
                        InteractProductsLookBack, InteractProductsToShow, InteractProductsRedirectBoost, email);

                    if (interactedProductIds.Count > 0)
                    {
                        interactedProducts =
                            await merchantService.GetInAppProductsFromIdList(interactedProductIds
                                .Select(id => id.ProductId).ToList());

                        if (interactedProducts.Count > 0)
                        {
                            interactedProducts.ForEach(product => product!.Interacted = true);

                            cacheService.SetData($"ShopService_InteractedProducts_{email}", interactedProducts,
                                TimeSpan.FromMinutes(15), true);
                        }
                    }
                }

                interactedProductsCount = interactedProducts.Count;

                // Only take the interacted products that fits the current page and add them to the front of the list to show
                productResponse.Data.Products = interactedProducts.Skip((page - 1) * size).Take(size).ToList();
            }

            var merchantPaginationDto = await cacheService.GetData<List<CuratedProduct>>(cacheKeyNewValue, true) ?? [];
            List<CuratedProductEvents> products = [];
            foreach (var merchantScore in topMerchants)
            {
                var curatedProduct =
                    merchantPaginationDto.SingleOrDefault(a => a.MerchantId == merchantScore.MerchantId);
                if (curatedProduct != null)
                {
                    products.AddRange(curatedProduct.Products.Take(maxProductsPrMerchant));
                }
            }

            Console.WriteLine($"Found {products.Count} products");
            var totalProducts = products.Count + interactedProductsCount;
            products.RemoveAll(a => productsAlreadyLoaded.Select(b => b.Id).Contains(a.ProductRefDto?.Id));
            Shuffle(products);
            Console.WriteLine($"{products.Count} products left");

            foreach (var curatedProductEvent in products.Where(_ => productResponse.Data.Products.Count < size))
            {
                curatedProductEvent.ProductRefDto!.Favored =
                    await merchantService.CheckFavorite(curatedProductEvent.ProductRefDto.Id, email);
                productResponse.Data.Products.Add(curatedProductEvent.ProductRefDto);
            }

            productResponse.Data.LastPage = (int)Math.Ceiling((decimal)totalProducts / size);
            cacheService.SetData(cacheKeyExists, productResponse, TimeSpan.FromHours(1), true);
        }

        return productResponse;
    }

    private async Task<ElasticExposureProductInteractDto> CreateElasticExposureProductsInteractsAsync(ShopEventDto shopEvent, Product product)
    {
        var partnerInfo = await partnerService.GetIdAndNameAsync(partnerContext.PartnerId);

        return new ElasticExposureProductInteractDto
        {
            ActionDate = DateTime.UtcNow,
            EventReceived = DateTime.UtcNow,
            Marketing = new ElasticExposureMarketingDto
            {
                Channel = Channel,
                Event = shopEvent.EventType
            },
            Customer = new ElasticExposureCustomerDto
            {
                Email = shopEvent.CustomerEmail
            },
            Product = new ElasticExposureProductDto
            {
                InternalId = product.InternalProductId,
                Price = product.Price ?? 0,
                PriceRange = PriceConverter.ConvertPrice(product.Price)
            },
            Search = new ElasticExposureSearchDto
            {
                Id = shopEvent.SearchGuid ?? "n/a"
            },
            Merchant = new ElasticExposureMerchantDto
            {
                Id = product.FkMerchantId.ToString(),
                Name = product.FkMerchant.Name
            },
            Partner = new ElasticExposurePartnerDto
            {
                Id = partnerInfo.Id.ToString(),
                Name = partnerInfo.Name
            }
        };
    }

    private async Task<ElasticExposureProductRedirectDto> CreateElasticExposureProductsRedirectsAsync(ShopEventDto shopEvent, Product product)
    {
        var partnerInfo = await partnerService.GetIdAndNameAsync(partnerContext.PartnerId);

        return new ElasticExposureProductRedirectDto
        {
            ActionDate = DateTime.UtcNow,
            EventReceived = DateTime.UtcNow,
            Marketing = new ElasticExposureMarketingDto
            {
                Channel = Channel,
                Event = shopEvent.EventType
            },
            Customer = new ElasticExposureCustomerDto
            {
                Email = shopEvent.CustomerEmail
            },
            Product = new ElasticExposureProductDto
            {
                InternalId = product.InternalProductId,
                Price = product.Price ?? 0,
                PriceRange = PriceConverter.ConvertPrice(product.Price)
            },
            Search = new ElasticExposureSearchDto
            {
                Id = shopEvent.SearchGuid ?? "n/a"
            },
            Url = new ElasticExposureUrlDto
            {
                Full = product.Permalink ?? "n/a"
            },
            Merchant = new ElasticExposureMerchantDto
            {
                Id = product.FkMerchantId.ToString(),
                Name = product.FkMerchant.Name
            },
            Partner = new ElasticExposurePartnerDto
            {
                Id = partnerInfo.Id.ToString(),
                Name = partnerInfo.Name
            }
        };
    }

    private async Task<ElasticExposureProductDisplayDto> CreateElasticExposureProductsDisplaysAsync(ShopEventDto shopEvent,
        List<Product> products)
    {
        var partnerInfo = await partnerService.GetIdAndNameAsync(partnerContext.PartnerId);

        return new ElasticExposureProductDisplayDto
        {
            ActionDate = DateTime.UtcNow,
            EventReceived = DateTime.UtcNow,
            Marketing = new ElasticExposureMarketingDisplayDto
            {
                Channel = Channel,
                Event = shopEvent.EventType,
                ListType = shopEvent.ListType ?? "Not Specified",
                ListId = shopEvent.ListId.ToString() ?? "0"
            },
            Customer = new ElasticExposureCustomerDto
            {
                Email = shopEvent.CustomerEmail
            },
            Merchant = new ElasticExposureMerchantListDto
            {
                Id = products.Select(product => product.FkMerchantId.ToString()).ToList(),
                Name = products.Select(product => product.FkMerchant.Name).ToList()
            },
            Product = new ElasticExposureProductListDto
            {
                InternalId = products.Select(product => product.InternalProductId).ToList(),
                Price = products.Select(product => product.Price ?? 0).ToList(),
                PriceRange = products.Select(product => PriceConverter.ConvertPrice(product.Price)).ToList(),
            },
            Search = new ElasticExposureSearchDto
            {
                Id = shopEvent.SearchGuid ?? "n/a"
            },
            Partner = new ElasticExposurePartnerDto
            {
                Id = partnerInfo.Id.ToString(),
                Name = partnerInfo.Name
            }
        };
    }

    // Helper class for round-robin distribution without modifying ProductPartnerDto
    private class ProductWithMerchantInfo
    {
        public ProductPartnerDto ProductDto { get; set; } = null!;
        public int MerchantId { get; set; }
        public double Score { get; set; }
    }


    private List<ProductPartnerDto> DistributeRandomizedProductsRoundRobin(List<ProductWithMerchantInfo> productsWithMerchantInfo)
    {
        // Group products by merchant, then randomize both the merchant order and products within each merchant
        var merchantGroups = productsWithMerchantInfo
            .GroupBy(p => p.MerchantId)
            .OrderBy(g => Guid.NewGuid()) // Randomize merchant order
            .Select(g => new
            {
                MerchantId = g.Key,
                MerchantScore = g.Max(p => p.Score),
                Products = g.OrderBy(p => Guid.NewGuid()).ToList() // Randomize products within each merchant
            })
            .ToList();

        var result = new List<ProductPartnerDto>();
        var maxProductsPerMerchant = merchantGroups.Max(g => g.Products.Count);

        // Round-robin distribution: take one product from each merchant in randomized order, then repeat
        for (int round = 0; round < maxProductsPerMerchant; round++)
        {
            foreach (var merchantGroup in merchantGroups)
            {
                if (round < merchantGroup.Products.Count)
                {
                    result.Add(merchantGroup.Products[round].ProductDto);
                }
            }
        }

        return result;
    }

    private List<ProductPartnerDto> DistributeProductsRoundRobinInternal(List<ProductWithMerchantInfo> productsWithMerchantInfo)
    {
        // Group products by merchant and order merchants by their highest score
        var merchantGroups = productsWithMerchantInfo
            .GroupBy(p => p.MerchantId)
            .OrderByDescending(g => g.Max(p => p.Score))
            .Select(g => new
            {
                MerchantId = g.Key,
                MerchantScore = g.Max(p => p.Score),
                Products = g.OrderByDescending(p => p.Score).ToList()
            })
            .ToList();

        var result = new List<ProductPartnerDto>();
        var maxProductsPerMerchant = merchantGroups.Max(g => g.Products.Count);

        // Round-robin distribution: take one product from each merchant in order, then repeat
        for (int round = 0; round < maxProductsPerMerchant; round++)
        {
            foreach (var merchantGroup in merchantGroups)
            {
                if (round < merchantGroup.Products.Count)
                {
                    result.Add(merchantGroup.Products[round].ProductDto);
                }
            }
        }

        return result;
    }

    private List<ProductPartnerDto> LoadMockProducts()
    {
        var filePath = Path.Combine(AppContext.BaseDirectory, "MockData", "MockProducts.json");
        if (!File.Exists(filePath))
            return new List<ProductPartnerDto>();
        var json = File.ReadAllText(filePath);
        return JsonSerializer.Deserialize<List<ProductPartnerDto>>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new List<ProductPartnerDto>();
    }
}