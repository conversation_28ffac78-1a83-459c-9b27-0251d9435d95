using System.Text.Json;
using Microsoft.Extensions.Logging;
using Shop_Services.Models.ModelsDto.Partner.Products;
using Shop_Services.Models.ModelsDto.Partner.Sections;
using Shared.Models;

namespace Shop_Services.Services.Partner.V1;

public class SectionService : ISectionService
{
    private readonly ILogger<SectionService> _logger;
    private readonly string _mockSectionsPath = Path.Combine(AppContext.BaseDirectory, "MockData", "MockSections.json");
    private readonly string _mockProductsPath = Path.Combine(AppContext.BaseDirectory, "MockData", "MockProducts.json");

    public SectionService(ILogger<SectionService> logger)
    {
        _logger = logger;
    }

    public async Task<List<SectionDto>> GetSectionsAsync(int? itemsPerSection, string? lang, bool useMock)
    {
        if (!useMock)
        {
            throw new NotImplementedException("Real implementation not yet available");
        }

        var sections = await LoadMockSectionsByLanguage(lang ?? "en");
        return sections;
    }

    public async Task<List<SectionWithProductsDto>> GetSectionsWithProductsAsync(int itemsPerSection, string? lang, bool useMock)
    {
        if (!useMock)
        {
            throw new NotImplementedException("Real implementation not yet available");
        }

        var sections = await LoadMockSectionsInternal();
        var products = await LoadMockProducts();
        var sectionsWithProducts = new List<SectionWithProductsDto>();

        foreach (var section in sections)
        {
            var sectionProducts = products
                .Where(p => section.ProductIds?.Contains(p.Id) == true)
                .Take(itemsPerSection)
                .ToList();

            sectionsWithProducts.Add(new SectionWithProductsDto
            {
                Id = section.Id,
                Name = section.Name,
                Src = section.Src,
                Products = sectionProducts
            });
        }

        return sectionsWithProducts;
    }

    public async Task<ProductPartnerPaginationResponse> GetSectionItemsAsync(int sectionId, string email, int size, int page, string? searchIdentifier, bool useMock)
    {
        if (!useMock)
        {
            throw new NotImplementedException("Real implementation not yet available");
        }

        var sections = await LoadMockSectionsInternal();
        var section = sections.FirstOrDefault(s => s.Id == sectionId);
        if (section == null)
        {
            return new ProductPartnerPaginationResponse
            {
                Data = new ProductPartnerPaginationDto { LastPage = 0, Products = new List<ProductPartnerDto>() },
                Response = new HttpResponseDto { Success = false, Message = "Section not found", StatusCode = StatusCode.NotFound }
            };
        }

        var products = await LoadMockProducts();
        var sectionProducts = products.Where(p => section.ProductIds?.Contains(p.Id) == true).ToList();
        var totalProducts = sectionProducts.Count;
        var paged = sectionProducts.Skip((page - 1) * size).Take(size).ToList();

        return new ProductPartnerPaginationResponse
        {
            Data = new ProductPartnerPaginationDto
            {
                LastPage = (long)Math.Ceiling((double)totalProducts / size),
                Products = paged
            },
            Response = new HttpResponseDto { Success = true, Message = "Mock products returned", StatusCode = StatusCode.Ok }
        };
    }

    private async Task<List<SectionDto>> LoadMockSectionsByLanguage(string lang)
    {
        try
        {
            var json = await File.ReadAllTextAsync(_mockSectionsPath);
            var sectionsByLang = JsonSerializer.Deserialize<Dictionary<string, List<SectionDto>>>(json);
            return sectionsByLang?.GetValueOrDefault(lang, new List<SectionDto>()) ?? new List<SectionDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading mock sections for language {Lang}", lang);
            return new List<SectionDto>();
        }
    }

    private async Task<List<SectionMockDto>> LoadMockSectionsInternal()
    {
        try
        {
            var json = await File.ReadAllTextAsync(_mockSectionsPath);
            var sectionsByLang = JsonSerializer.Deserialize<Dictionary<string, List<SectionMockDto>>>(json);
            return sectionsByLang?.GetValueOrDefault("en", new List<SectionMockDto>()) ?? new List<SectionMockDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading mock sections");
            return new List<SectionMockDto>();
        }
    }

    private async Task<List<ProductPartnerDto>> LoadMockProducts()
    {
        try
        {
            var json = await File.ReadAllTextAsync(_mockProductsPath);
            return JsonSerializer.Deserialize<List<ProductPartnerDto>>(json) ?? new List<ProductPartnerDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading mock products");
            return new List<ProductPartnerDto>();
        }
    }
} 