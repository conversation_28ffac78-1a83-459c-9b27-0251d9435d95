using Shop_Services.Models.ModelsDto.Partner.Categories;
using Shop_Services.Models.ModelsDto.Partner.Products;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Shop_Services.Models.ModelsDal.Shop;
using Shared.Services.Partner;
using Shop_Services.Services.Shop;
using Shared.Dto.Shop;
using Audience.Services.Audience;
using Webshop.Webshop;
using System.Text;
using System.Security.Claims;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Extensions.Configuration;
using Shared.Dto.Shop.Product;
using System.IdentityModel.Tokens.Jwt;
using Shared.Models;
using Shared.Helpers.Deserializers;

namespace Shop_Services.Services.Partner.V1
{
    public class CategoryService(ShopDbContext shopDbContext, IPartnerContext partnerContext, IShopService shopService, ICustomerService customerService, IMerchantService merchantService, IConfiguration configuration, VaultSettings vaultSettings)
        : ICategoryService
    {
        public async Task<List<CategoryDto>> GetCategoriesAsync(int? parentId, string? lang, bool useMock)
        {
            if (!useMock) 
            {
                var partnerId = partnerContext.PartnerId;
                
                var categories = await shopDbContext.Categories.Where(c => c.FkPartnerId == partnerId && c.Active).ToListAsync();
                var categoriesResult = categories.Where(c => c.FkParentId == parentId)
                    .Select(c => new CategoryDto 
                    {
                        Id = c.Id,
                        Name = c.NameEn,
                        Src = c.Src,
                        FkParentId = c.FkParentId,
                        Subcategories = categories.Where(sub => sub.FkParentId == c.Id)
                            .Select(s => new CategoryDto 
                            {
                                Id = s.Id,
                                Name = s.NameEn,
                                Src = s.Src,
                                FkParentId = s.FkParentId
                            }).ToList()
                    }).ToList();
                
                return categoriesResult;
            }
                

            var categoriesByLang = LoadMockCategoriesByLanguage();
            var language = (lang ?? "en").ToLower();
            if (!categoriesByLang.TryGetValue(language, out var mockCategories))
                mockCategories = categoriesByLang["en"];
            var result = new List<CategoryDto>();
            foreach (var cat in mockCategories)
            {
                if (parentId == null && cat.FkParentId == null)
                {
                    result.Add(cat);
                }
                else if (cat.Subcategories != null)
                {
                    foreach (var sub in cat.Subcategories)
                    {
                        if (parentId != null && sub.FkParentId == parentId)
                        {
                            result.Add(sub);
                        }
                    }
                }
            }
            return result;
        }

        public async Task<ProductPartnerPaginationResponse> GetCategoryItemsAsync(int categoryId, string email, int size, int page, string? searchIdentifier, bool useMock)
        {
            if (!useMock) 
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                var partnerId = partnerContext.PartnerId;
                
                var partnerIdSetTime = stopwatch.ElapsedMilliseconds;
                partnerContext.SetPartnerId(87317);
                
                var contactStartTime = stopwatch.ElapsedMilliseconds;
                var contact = await customerService.GetCustomerForMerchantRelevance(email);
                var contactEndTime = stopwatch.ElapsedMilliseconds;
                
                var merchantAssetsStartTime = stopwatch.ElapsedMilliseconds;
                var merchantAssets = await merchantService.GetMerchantLogosAsync();
                var merchantAssetsEndTime = stopwatch.ElapsedMilliseconds;
                
                var searchStartTime = stopwatch.ElapsedMilliseconds;
                var result = await shopService.HandleCategorySearch(page, size, email, new ProductFilterDto { CategoryId = categoryId }, contact);
                var searchEndTime = stopwatch.ElapsedMilliseconds;
                
                // Optimize transformation with batch operations and lookup optimization
                var transformStartTime = stopwatch.ElapsedMilliseconds;
                
                // Create merchant assets lookup for O(1) access instead of FirstOrDefault
                var merchantAssetsLookup = merchantAssets.ToDictionary(a => a.FkMerchantId, a => a.Src);
                
                // Batch fetch all product details to avoid N+1 queries and threading issues
                var internalProductIds = result.Data.Products.Select(p => p.Id).ToList();
                var productDetails = await merchantService.GetProductsByInternalProductIds(internalProductIds);
                
                // Transform products using batch data and lookup optimization
                var transformedProducts = result.Data.Products
                    .Where(p =>
                    {
                        var productDetail = productDetails.GetValueOrDefault(p.Id);
                        if (productDetail == null)
                            return false;
                            
                        var salePrice = p.SalePrice > 0 ? p.SalePrice : p.NormalPrice;
                        var productImages = ImageDeserializer.DeserializeProductImages(productDetail.ProductImages);
                        
                        // Filter out products with invalid data
                        return salePrice > 0 && productImages.Count > 0;
                    })
                    .Select(p =>
                    {
                        var productDetail = productDetails.GetValueOrDefault(p.Id);
                        var redirectLink = productDetail != null 
                            ? GetRedirectLink(email, contact?.PartnerGuid ?? "", productDetail.Permalink ?? "", p.Id, productDetail.FkMerchantId.ToString(), p.MerchantName)
                            : string.Empty;
                        var salePrice = p.SalePrice > 0 ? p.SalePrice : p.NormalPrice;
                        var productImages = ImageDeserializer.DeserializeProductImages(productDetail.ProductImages);
                        
                        return new ProductPartnerDto
                        {
                            Id = p.Id,
                            Name = p.Name,
                            NormalPrice = p.NormalPrice,
                            SalePrice = salePrice,
                            MerchantName = p.MerchantName,
                            OnSale = p.OnSale,
                            Favored = p.Favored,
                            IncludesVariants = p.IncludesVariants,
                            Description = productDetail?.FormattedDescription ?? productDetail?.Description ?? p.Description,
                            ProductImages = productImages,
                            MerchantLogoSrc = merchantAssetsLookup.TryGetValue((int)p.MerchantId, out var logoSrc) ? logoSrc : "",
                            RedirectLink = redirectLink
                        };
                    }).ToList();
                
                var transformEndTime = stopwatch.ElapsedMilliseconds;
                
                stopwatch.Stop();
                
                Console.WriteLine($"PartnerIdSetTime: {partnerIdSetTime}ms");
                Console.WriteLine($"ContactTime: {contactEndTime - contactStartTime}ms");
                Console.WriteLine($"MerchantAssetsTime: {merchantAssetsEndTime - merchantAssetsStartTime}ms");
                Console.WriteLine($"SearchTime: {searchEndTime - searchStartTime}ms");
                Console.WriteLine($"TransformTime: {transformEndTime - transformStartTime}ms");
                Console.WriteLine($"TotalTime: {stopwatch.ElapsedMilliseconds}ms");

                return new ProductPartnerPaginationResponse {
                    Data = new ProductPartnerPaginationDto { 
                        LastPage = result.Data.LastPage, 
                        Products = transformedProducts 
                    },
                    Response = new Shared.Models.HttpResponseDto { Success = true, Message = "Products returned", StatusCode = Shared.Models.StatusCode.Ok }
                };
            }
                

            var categoriesByLang = LoadMockCategoriesInternal();
            if (!categoriesByLang.TryGetValue("en", out var categories))
                return new ProductPartnerPaginationResponse { Response = new Shared.Models.HttpResponseDto { Success = false, Message = "Mock data not found", StatusCode = Shared.Models.StatusCode.NotFound } };

            var allCategories = new List<CategoryMockDto>();
            void AddWithSubs(CategoryMockDto cat)
            {
                allCategories.Add(cat);
                if (cat.Subcategories != null)
                    foreach (var sub in cat.Subcategories)
                        AddWithSubs(sub);
            }
            foreach (var cat in categories)
                AddWithSubs(cat);

            var category = allCategories.FirstOrDefault(c => c.Id == categoryId);
            if (category == null)
                return new ProductPartnerPaginationResponse
                {
                    Data = new ProductPartnerPaginationDto { LastPage = 0, Products = new List<ProductPartnerDto>() },
                    Response = new Shared.Models.HttpResponseDto { Success = false, Message = "Category not found", StatusCode = Shared.Models.StatusCode.NotFound }
                };

            var productIds = category.ProductIds ?? new List<string>();
            var products = LoadMockProducts().Where(p => productIds.Contains(p.Id)).ToList();
            var totalProducts = products.Count;
            var paged = products.Skip((page - 1) * size).Take(size).ToList();

            return new ProductPartnerPaginationResponse
            {
                Data = new ProductPartnerPaginationDto
                {
                    LastPage = (long)Math.Ceiling((double)totalProducts / size),
                    Products = paged
                },
                Response = new Shared.Models.HttpResponseDto { Success = true, Message = "Mock products returned", StatusCode = Shared.Models.StatusCode.Ok }
            };
        }

        private Dictionary<string, List<CategoryDto>> LoadMockCategoriesByLanguage()
        {
            var filePath = Path.Combine(AppContext.BaseDirectory, "MockData", "MockCategories.json");
            var json = File.ReadAllText(filePath);
            return JsonSerializer.Deserialize<Dictionary<string, List<CategoryDto>>>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new Dictionary<string, List<CategoryDto>>();
        }

        private Dictionary<string, List<CategoryMockDto>> LoadMockCategoriesInternal()
        {
            var filePath = Path.Combine(AppContext.BaseDirectory, "MockData", "MockCategories.json");
            var json = File.ReadAllText(filePath);
            return JsonSerializer.Deserialize<Dictionary<string, List<CategoryMockDto>>>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new Dictionary<string, List<CategoryMockDto>>();
        }

        private List<ProductPartnerDto> LoadMockProducts()
        {
            var filePath = Path.Combine(AppContext.BaseDirectory, "MockData", "MockProducts.json");
            var json = File.ReadAllText(filePath);
            return JsonSerializer.Deserialize<List<ProductPartnerDto>>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new List<ProductPartnerDto>();
        }

        private async Task<string> GetRedirectLinkAsync(string productId, string email, string? partnerGuid, string merchantName)
        {
            var product = await merchantService.GetProductByInternalProductId(productId);
            if (product == null)
                return string.Empty;

            var redirectBaseUrl = configuration["RedirectService-Url"];
            var partnerId = partnerContext.PartnerId;
            if(partnerId == 87317) {
                redirectBaseUrl = "https://redirect.happyads.io/";
            }

            var redirectUrl = redirectBaseUrl + "redirectShop/" + JwtTokenEncode(email, partnerGuid ?? "",
                product.Permalink ?? "", productId, product.FkMerchantId.ToString(), merchantName);

            return redirectUrl;
        }

        private string GetRedirectLink(string email, string partnerGuid, string permalink, string productId, string merchantId, string merchantName)
        {
            if (string.IsNullOrEmpty(permalink))
                return string.Empty;

            var redirectBaseUrl = configuration["RedirectService-Url"];
            var partnerId = partnerContext.PartnerId;
            if(partnerId == 87317) {
                redirectBaseUrl = "https://redirect.happyads.io/";
            }

            var redirectUrl = redirectBaseUrl + "redirectShop/" + JwtTokenEncode(email, partnerGuid,
                permalink, productId, merchantId, merchantName);

            return redirectUrl;
        }

        private string JwtTokenEncode(string email, string partnerGuid, string url, string productId,
        string webshopId = "", string webshopName = "")
    {
        var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(vaultSettings.JwtTokenKey));

        var tokenHandler = new JwtSecurityTokenHandler();

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity([
                new Claim("email", email),
                new Claim("url", url),
                new Claim("pid", productId),
                //new Claim("did", discountId),
                new Claim("wid", webshopId),
                new Claim("wname", webshopName),
                //new Claim("type", type),
                //new Claim("dcode", discountCode ?? ""),
                //new Claim("dcodeid", discountCodeId ?? ""),
                //new Claim("vbdai", debtorAccountId)
                new Claim("pguid", partnerGuid),
                new Claim("parid", partnerContext.PartnerId.ToString())
            ]),
            Issuer = vaultSettings.JwtIssuer,
            SigningCredentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha512Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);

        return tokenHandler.WriteToken(token);
    }
    }
} 