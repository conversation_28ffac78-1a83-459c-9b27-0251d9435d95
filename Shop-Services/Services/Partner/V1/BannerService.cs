using Shop_Services.Models.ModelsDal.Shop;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace Shop_Services.Services.Partner.V1
{
    public class BannerService : IBannerService
    {
        private readonly ShopDbContext shopDbContext;
        public BannerService(ShopDbContext shopDbContext)
        {
            this.shopDbContext = shopDbContext;
        }

        public async Task<List<Banner>> GetBannersAsync(string? languageCode, bool useMock)
        {
            if (useMock)
            {
                var mockBanners = LoadMockBanners();
                if (!string.IsNullOrEmpty(languageCode))
                    return mockBanners.Where(b => b.LanguageCode == languageCode).ToList();
                return mockBanners;
            }
            return await shopDbContext.Banners.Where(b => b.LanguageCode == languageCode).ToListAsync();
        }

        public async Task<Banner?> GetBannerByIdAsync(int bannerId, bool useMock)
        {
            if (useMock)
            {
                var mockBanners = LoadMockBanners();
                return mockBanners.FirstOrDefault(b => b.Id == bannerId);
            }
            return await shopDbContext.Banners.FirstOrDefaultAsync(b => b.Id == bannerId);
        }

        private List<Banner> LoadMockBanners()
        {
            var filePath = Path.Combine(AppContext.BaseDirectory, "MockData", "MockBanners.json");
            if (!File.Exists(filePath))
                return new List<Banner>();
            var json = File.ReadAllText(filePath);
            return JsonSerializer.Deserialize<List<Banner>>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true }) ?? new List<Banner>();
        }
    }
} 