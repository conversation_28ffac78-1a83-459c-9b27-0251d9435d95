using Shop_Services.Models.ModelsDto.Partner.Categories;
using Shop_Services.Models.ModelsDto.Partner.Products;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Shop_Services.Services.Partner.V1
{
    public interface ICategoryService
    {
        Task<List<CategoryDto>> GetCategoriesAsync(int? parentId, string? lang, bool useMock);
        Task<ProductPartnerPaginationResponse> GetCategoryItemsAsync(int categoryId, string email, int size, int page, string? searchIdentifier, bool useMock);
    }
} 