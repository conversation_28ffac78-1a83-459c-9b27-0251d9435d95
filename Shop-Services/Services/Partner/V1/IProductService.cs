using Shared.Dto.Shop;
using Shared.Models;
using Shop_Services.Models.ModelsDto.Partner.Products;
using Shop_Services.Models.ModelsDto.Query_Parameters;

namespace Shop_Services.Services.Partner.V1
{
    public interface IProductService
    {
        Task<ProductPartnerPaginationResponse> SearchProductsAsync(SearchProductParameters searchProductParameters);
        
        Task<ProductPartnerResponse> GetProductByProductIdAndEmailAsync(string productId, string email, bool useMock);
        Task<ProductPartnerPaginationResponse> GetFavoritesProductsAsync(string email, bool useMock, int page, int size);
        Task<ProductPartnerPaginationResponse> GetProductRecommendationsAsync(string email, string strategy, int size, int page, bool useMock);
        Task<HttpResponseDto> ToggleFavoriteAsync(ShopFavoriteRequestDto favoriteRequest);
        Task<HttpResponseDto> AddBehaviorEventAsync(ShopEventDto shopEventDto);

        /********************  OLD  ***********************/
        //IN APP
        /*Task<ProductPaginationResponse> GetProductsByEmailAndFiltersAsync(string email, int size, int page,
            ProductFilterDto? productFilterDto);

        Task<ProductSingleRefResponse> GetProductByProductIdAndEmailAsync(string productId, string email);
        Task<List<CategoriesDto>> GetCategoriesAsync();
        Task<CategoriesSubResponse> GetCategoriesSubCategoriesAsync(string email, int parentId);
        Task<List<SectionDto>> GetSectionsAsync();
        Task<ResponseDto> ToggleFavoriteAsync(ShopFavoriteRequestDto favoriteRequest);
        Task<ResponseDto> AddShopEventAsync(ShopEventDto shopEvent);
        Task<List<Banner>> GetBannersAsync(string? languageCode);
        Task<List<LogoDto>> GetLogosAsync();*/

        //Internal
        /*Task<ProductPaginationResponseInternal> SearchProductAsync(SearchOptimizeAppDto searchOptimizeAppDto);
        Task<List<InternalCategoryDto>> GetCategoriesInternalAsync();
        Task<InternalCategoryDto> UpdateAsync(InternalCategoryDto category);

        //Categories
        Task<CategoryMerchantRelationDto> GetCategoryMerchantRelationsByMerchantIdAsync(int merchantId);
        Task<CategoryMerchantRelationDto> UpdateCategoryMerchantRelationsAsync(CategoryMerchantRelationDto categoryMerchantRelationDto);

        //MerchantGenders
        Task<List<MerchantGenderDto>> GetMerchantGendersAsync();
        Task UpdateMerchantGendersAsync(MerchantGenderDto merchantGenderDto);*/
    }
}