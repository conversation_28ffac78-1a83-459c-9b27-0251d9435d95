using Shared.Dto.Shop;
using Shared.Dto.Shop.Categories;
using Shared.Dto.Shop.Product;
using Shared.Dto.Shop.Sections;
using Shared.Models;
using Shop_Services.Models;
using Shop_Services.Models.ModelsDal.Shop;
using Shop_Services.Models.ElasticSearch;
using Shop_Services.Models.ModelsDto.Query_Parameters;
using Shared.Models.Customer;

namespace Shop_Services.Services.Shop
{
    public interface IShopService
    {
        //IN APP
        Task<ProductPaginationResponse> GetProductsByEmailAndFiltersAsync(string email, int size, int page,
            ProductFilterDto? productFilterDto);

        Task<ProductSingleRefResponse> GetProductByProductIdAndEmailAsync(string productId, string email);
        Task<List<CategoriesDto>> GetCategoriesAsync();
        Task<CategoriesSubResponse> GetCategoriesSubCategoriesAsync(string email, int parentId);
        Task<List<SectionDto>> GetSectionsAsync();
        Task<ResponseDto> ToggleFavoriteAsync(ShopFavoriteRequestDto favoriteRequest);
        Task<ResponseDto> AddShopEventAsync(ShopEventDto shopEvent);
        Task<List<LogoDto>> GetLogosAsync();
        Task<List<Banner>> GetBannersAsync(string languageCode);

        Task<ProductPaginationResponse> HandleCategorySearch(int page, int size, string email,
        ProductFilterDto productFilterDto, MerchantRelevanceCustomerDto contact);

        //Internal
        /*Task<ProductPaginationResponseInternal> SearchProductAsync(SearchOptimizeAppDto searchOptimizeAppDto);
        Task<List<InternalCategoryDto>> GetCategoriesInternalAsync();
        Task<InternalCategoryDto> UpdateAsync(InternalCategoryDto category);

        //Categories
        Task<CategoryMerchantRelationDto> GetCategoryMerchantRelationsByMerchantIdAsync(int merchantId);
        Task<CategoryMerchantRelationDto> UpdateCategoryMerchantRelationsAsync(CategoryMerchantRelationDto categoryMerchantRelationDto);

        //MerchantGenders
        Task<List<MerchantGenderDto>> GetMerchantGendersAsync();
        Task UpdateMerchantGendersAsync(MerchantGenderDto merchantGenderDto);*/
    }
}