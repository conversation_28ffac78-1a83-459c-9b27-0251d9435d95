using System.Diagnostics;
using System.Globalization;
using System.Text;
using Audience.Services.Audience;
using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Core.Search;
using Elastic.Clients.Elasticsearch.QueryDsl;
using Integration.Services.Static;
using Merchant_Services.Models.ModelsDal.Merchant;
using Merchant_Services.Services.ProductStatus;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using RabbitMQ.Client;
using Shared.Constants;
using Shared.Dto.Curated.Product;
using Shared.Dto.MerchantScore;
using Shared.Dto.Shop;
using Shared.Dto.Shop.Categories;
using Shared.Dto.Shop.Product;
using Shared.Dto.Shop.Sections;
using Shared.Dto.Webshop;
using Shared.Elastic.Models;
using Shared.Elastic.Models.ElasticShopCategoriesInteracts;
using Shared.Elastic.Models.ElasticShopLog;
using Shared.Elastic.Models.ElasticShopProductsDisplays;
using Shared.Elastic.Models.ElasticShopProductsInteracts;
using Shared.Elastic.Models.ElasticShopProductsRedirects;
using Shared.Elastic.ShopProducts;
using Shared.Models;
using Shared.Models.Customer;
using Shared.Services;
using Shared.Services.Cache;
using Shared.Services.MerchantRelevance;
using Shop_Services.Models;
using Shop_Services.Models.ModelsDal.Shop;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;
using JsonSerializer = System.Text.Json.JsonSerializer;
using Shop_Services.Models.ElasticSearch;
using ApiKey = Elastic.Transport.ApiKey;
using Field = Elastic.Clients.Elasticsearch.Field;
using MinimumShouldMatch = Elastic.Clients.Elasticsearch.MinimumShouldMatch;
using Shared.Services.Setting;
using Shared.Services.Partner;
using GrowthBook;
using Shop_Services.Models.Exposure;
using Shared.Elastic.Models.ElasticExposure;
using Partner_Services.Services.General;
using System.Text.Json;

namespace Shop_Services.Services.Shop;

public class ShopService(
    ILogger logger,
    //IConnection rabbitConnection,
    [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud,
    IMerchantService merchantService,
    ICacheService cacheService,
    ICustomerService customerService,
    ISettingService settingService,
    IElasticShopProductService elasticShopProductService,
    ShopDbContext shopDbContext,
    Microsoft.Extensions.Configuration.IConfiguration configuration,
    IMerchantRelevanceService merchantRelevanceService,
    IMemoryCache memoryCache,
    IPartnerContext partnerContext,
    IProductStatusService productStatusService,
    IGrowthBook growthBook,
    IPartnerService partnerService)
    : IShopService
{
    private const string Channel = "app";
    private const int AgeInterval = 10;

    /// <summary>
    /// Retrieves products based on the provided email and filter criteria.
    /// </summary>
    /// <param name="email">The email of the user requesting the products.</param>
    /// <param name="size">The number of products per page.</param>
    /// <param name="page">The page number to retrieve.</param>
    /// <param name="productFilterDto">The filter criteria for the products.</param>
    /// <returns>A <see cref="ProductPaginationResponse"/> containing the paginated products.</returns>
    public async Task<ProductPaginationResponse> GetProductsByEmailAndFiltersAsync(string email,
        int size, int page,
        ProductFilterDto? productFilterDto)
    {
        var partnerId = partnerContext.PartnerId;
        Console.WriteLine("Start");
        var stopwatch = new Stopwatch();
        stopwatch.Start();
        var contact = await customerService.GetCustomerForMerchantRelevance(email);
        var gender = contact?.Gender ?? "Unknown";
        stopwatch.Stop();
        Console.WriteLine("GetCustomerForMerchantRelevance: " + stopwatch.ElapsedMilliseconds);

        /*var topMerchantsCacheKey = $"TopMerchants_Customer_{contact?.Id}_Gender_{gender}";

        // Attempt to retrieve the category from the cache
        var topMerchants = await cacheService.GetData<List<MerchantRelevance>>(topMerchantsCacheKey, true);

        if(topMerchants == null || topMerchants.Count == 0)
        {
            topMerchants = await merchantRelevanceService.GetMerchantRelevance(contact, 4, true);
            cacheService.SetData(topMerchantsCacheKey, topMerchants, TimeSpan.FromMinutes(30), true);
        }*/

        stopwatch.Restart();
        var topMerchantsCacheKey = $"TopMerchantsForEmail_{email}_{partnerId}";

        var topMerchants = await cacheService.GetDataWithCacheLockAsync(
            topMerchantsCacheKey,
            async () =>
            {
                List<MerchantRelevance> topMerchants = [];
                if (contact != null)
                {
                    var customerList = new List<MerchantRelevanceCustomerDto> {contact};
                    try
                    {
                        var topMerchantsResult =
                            await merchantRelevanceService.GetMerchantRelevanceForBatch(customerList, AgeInterval,
                                true);
                        topMerchants = topMerchantsResult.FirstOrDefault().Value;
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                        topMerchants =
                            await merchantRelevanceService.GetMerchantRelevance(contact, AgeInterval, true);
                    }


                    if (topMerchants == null || topMerchants.Count < 20)
                    {
                        topMerchants =
                            await merchantRelevanceService.GetMerchantRelevance(contact, AgeInterval, true);
                    }
                }

                return topMerchants;
            },
            memoryCacheExpiry: TimeSpan.FromMinutes(15),
            redisCacheExpiry: TimeSpan.FromMinutes(30)
        ) ?? [];

        if (topMerchants.Count < 20)
        {
            topMerchants = await merchantRelevanceService.GetMerchantRelevance(contact, AgeInterval, true);
        }

        stopwatch.Stop();
        Console.WriteLine("GetMerchantRelevance: " + stopwatch.ElapsedMilliseconds);

        ProductPaginationResponse productResponse;


        stopwatch.Restart();
        // Section products
        if (productFilterDto?.SectionId != null)
        {
            productResponse = await HandleSectionProducts(page, size, email, productFilterDto);
        }
        // Category Search products
        else if (productFilterDto?.CategoryId != null || (productFilterDto?.Category != null &&
                                                          productFilterDto.Category?.ToLower() != "favorites" &&
                                                          productFilterDto.Category?.ToLower() != "anbefalet" &&
                                                          productFilterDto.Category != "50" &&
                                                          productFilterDto.Category != "48"))
        {
            productResponse = await HandleCategorySearch(page, size, email, productFilterDto, contact);
        }
        // Free search products
        else if (!string.IsNullOrEmpty(productFilterDto?.Search))
        {
            productResponse = await HandleFreeSearch(page, size, email, productFilterDto, contact);
        }
        // Favorites products
        else if (productFilterDto?.Favorites != null || productFilterDto?.Category?.ToLower() == "favorites")
        {
            productResponse = await HandleFavorites(page, size, email);
        }
        // Bestseller products
        else if (productFilterDto?.BestSellers == true)
        {
            /*var cacheKey = $"Bestsellers_{email}_{page}_{size}";
            var cachedResponse = await cacheService.GetData<ProductPaginationResponse>(cacheKey, true);
            if (cachedResponse != null)
            {
                // Check if any values for tracking and send event
                // TODO - Does not current work as expected
                _ = TrackProducts(cachedResponse, page, size, email);
                return cachedResponse;
            }*/

            var filteredTopMerchants = await GetTopMerchants(gender, topMerchants, "Bestseller", 10);
            productResponse = await ProductHandling(page, size, email, filteredTopMerchants,
                "GeneralService_TopProductsForMerchants_Order", "1", 5);

            //cacheService.SetData(cacheKey, productResponse, TimeSpan.FromMinutes(1), true);
        }
        // Recommended products
        else if (productFilterDto?.Recommended == true)
        {
            /*var cacheKey = $"Recommended_{email}_{page}_{size}";
            var cachedResponse = await cacheService.GetData<ProductPaginationResponse>(cacheKey, true);
            if (cachedResponse != null || cachedResponse?.Data.Products.Count > 0)
            {
                // Check if any values for tracking and send event
                // TODO - Does not current work as expected
                _ = TrackProducts(cachedResponse, page, size, email);
                return cachedResponse;
            }*/

            var filteredTopMerchants = await GetTopMerchants(gender, topMerchants, "Recommended", 20);
            productResponse = await ProductHandling(page, size, email, filteredTopMerchants,
                "GeneralService_TopProductsForMerchants_PageEvent", "3", 10);

            //cacheService.SetData(cacheKey, productResponse, TimeSpan.FromMinutes(1), true);
        }
        // Default to recommended products
        else
        {
            /*var cacheKey = $"Recommended_{email}_{page}_{size}";
            var cachedResponse = await cacheService.GetData<ProductPaginationResponse>(cacheKey, true);
            if (cachedResponse != null || cachedResponse?.Data.Products.Count > 0)
            {
                // Check if any values for tracking and send event
                // TODO - Does not current work as expected
                _ = TrackProducts(cachedResponse, page, size, email);
                return cachedResponse;
            }*/

            var filteredTopMerchants = await GetTopMerchants(gender, topMerchants, "Recommended", 20);
            productResponse = await ProductHandling(page, size, email, filteredTopMerchants,
                "GeneralService_TopProductsForMerchants_PageEvent", "3", 10);

            //cacheService.SetData(cacheKey, productResponse, TimeSpan.FromMinutes(1), true);
        }
        
        
        stopwatch.Stop();
        Console.WriteLine("ProductHandling: " + stopwatch.ElapsedMilliseconds);

        stopwatch.Restart();
        // Get product IDs and check which ones are active
        var productIds = productResponse.Data.Products.Select(p => p.Id).ToList();  
        var productStatuses = await productStatusService.GetProductStatusBatchAsync(productIds);
        
        // Filter out inactive products directly without modifying the product objects
        productResponse.Data.Products = [.. productResponse.Data.Products.Where(p => productStatuses.TryGetValue(p.Id, out bool isActive) && isActive)];
    
        if(productResponse.Data.Products.Any(p => p.MerchantId == 5552))
        {
            Console.WriteLine("Found product 5552");
        }

        // Chekck if Product is acutally on Sale
        foreach (var product in productResponse.Data.Products)
        {
            if (product.OnSale)
            {
                if(product.SalePrice == 0)
                {
                    product.OnSale = false;
                    product.SalePrice = product.NormalPrice;
                }
            }
        }
        
        stopwatch.Stop();
        Console.WriteLine("GetProductStatusBatchAsync: " + stopwatch.ElapsedMilliseconds);

        // Check if any values for tracking and send event
        // TODO - Does not currently work as expected
        _ = TrackProducts(productResponse, page, size, email);
        
        return productResponse;
    }


    /// <summary>
    /// Retrieves the top merchants based on the specified gender, type, and limit.
    /// </summary>
    /// <param name="gender">The gender of the user to filter merchants.</param>
    /// <param name="topMerchants">The initial list of top merchants.</param>
    /// <param name="type">The type of merchants to filter (e.g., Bestseller, Recommended).</param>
    /// <param name="topMerchantsLimit">The maximum number of top merchants to return.</param>
    /// <returns>A list of top merchants filtered by the specified criteria.</returns>
    private async Task<List<MerchantRelevance>> GetTopMerchants(string gender, List<MerchantRelevance> topMerchants,
        string type, int topMerchantsLimit)
    {
        var partnerId = partnerContext.PartnerId;
        // Generate a cache key based on gender and type
        var allowedMerchantsCacheKey = $"AllowedMerchants_{gender}_{type}_{partnerId}";

        // Attempt to retrieve the allowed merchants from the cache
        var allowedMerchants = await cacheService.GetData<List<int>>(allowedMerchantsCacheKey, true);

        if (allowedMerchants == null)
        {
            // If not found in cache, retrieve from the database
            // TODO - Add partnerId to the query and in the Database
            allowedMerchants = (await shopDbContext.MerchantGenderRels
                .Include(a => a.FkMerchantGender)
                .Where(a => a.Active && a.FkMerchantGender.Active && a.FkMerchantGender.Type == type &&
                            a.FkMerchantGender.Name == gender)
                .Select(a => a.FkMerchantId)
                .ToListAsync());

            // Store the retrieved allowed merchants in the cache
            cacheService.SetData(allowedMerchantsCacheKey, allowedMerchants, TimeSpan.FromHours(2), true);
        }

        // Filter the top merchants based on the allowed merchants list
        topMerchants = topMerchants.Where(a => allowedMerchants.Contains(a.MerchantId)).ToList();

        // Limit the number of top merchants to the specified limit
        topMerchants = topMerchants.Take(topMerchantsLimit).ToList();

        return topMerchants;
    }

    /// <summary>
    /// Handles the retrieval of products for a specific category based on the provided filter criteria.
    /// </summary>
    /// <param name="page">The page number to retrieve.</param>
    /// <param name="size">The number of products per page.</param>
    /// <param name="email">The email of the user requesting the products.</param>
    /// <param name="productFilterDto">The filter criteria for the products.</param>
    /// <param name="contact">The contact information of the user.</param>
    /// <returns>A <see cref="ProductPaginationResponse"/> containing the paginated products for the category.</returns>
    public async Task<ProductPaginationResponse> HandleCategorySearch(int page, int size, string email,
        ProductFilterDto productFilterDto, MerchantRelevanceCustomerDto contact)
    {
        // Record the start time for performance tracking
        var startTime = DateTime.UtcNow;

        // Initialize the Elasticsearch client
        var elasticClient = InitializeElasticClient();

        // Decrement the page number as the first page is considered 0 in Elasticsearch
        page--;

        // Retrieve the category ID from the filter DTO or convert the category name to an ID
        var categoryId = productFilterDto.CategoryId ?? Convert.ToInt32(productFilterDto.Category);

        // Fetch the category details from the database, including active merchant relationships
        // Define a cache key based on the category ID
        var categoryCacheKey = $"Category_{categoryId}";

        // Attempt to retrieve the category from the cache
        var category = await cacheService.GetData<Category>(categoryCacheKey, true);

        if (category == null)
        {
            // If not found in cache, retrieve from the database
            category = await shopDbContext.Categories
                .Include(a => a.CategoryMerchantRels.Where(b => b.Active))
                .FirstOrDefaultAsync(a => a.Id == categoryId);

            // Store the retrieved category in the cache
            if (category != null)
            {
                cacheService.SetData(categoryCacheKey, category, TimeSpan.FromHours(8), true);
            }
        }

        // Prepare the search optimization DTO with the category's positive and negative tags
        var searchOptimizeAppDto = new SearchOptimizeAppDto
        {
            Search = category?.PositiveTags?.Replace(",", " ") ?? "",
            NegativeTags = category?.NegativeTags?.Replace(",", " ") ?? "",
            ProductNameBoost = 2.0,
            ProductDescriptionBoost = 1,
            Bm25TextSearchBoost = 1.0f,
            TextVectorSearchBoost = 0.7f,
            MinimumMatchPercentage = 0,
            Page = page,
            Size = size,
            Similarity = 0,
            CategoryBoost = 1.5f,
            MerchantsToFilter = string.Join(",",
                category?.CategoryMerchantRels.Select(a => a.FkMerchantId.ToString()) ?? []),
            ProductsToFilter = string.Join(",", await productStatusService.GetAllInactiveProductIdsAsync()) 
        };

        // Define a cache key based on the search parameters
        var searchCacheKey =
            $"ElasticSearchResponse_category_{categoryId}_{searchOptimizeAppDto.Page}_{searchOptimizeAppDto.Size}_{partnerContext.PartnerId}";

        // Check if the response is already cached
        /*var cachedResponse = await cacheService.GetData<ElasticSearchResponseDto>(searchCacheKey, true);
        if (cachedResponse != null)
        {
            cachedResponse.Took = 0;
            // Return the cached response
            return await ProcessElasticSearchResponse(cachedResponse, searchOptimizeAppDto, productFilterDto, size,
                page, email,
                startTime);
        }*/

        // Perform the Elasticsearch query with the prepared DTO
        var response = await PerformElasticSearch(elasticClient, searchOptimizeAppDto, contact, startTime);
        //var response = await activeProductSearchService.SearchActiveProductsAsync(searchOptimizeAppDto, size, page, contact);

        // Cache the response
        //cacheService.SetData(searchCacheKey, response, TimeSpan.FromMinutes(1), true);

        // Process the Elasticsearch response and return the paginated product response
        return await ProcessElasticSearchResponse(response, searchOptimizeAppDto, productFilterDto, size, page, email,
            startTime);

        /*// Perform the Elasticsearch query with the prepared DTO
        var response = await PerformElasticSearch(elasticClient, searchOptimizeAppDto, contact, startTime);

        // Process the Elasticsearch response and return the paginated product response
        return await ProcessElasticSearchResponse(response, searchOptimizeAppDto, size, page, email, contact,
            startTime);*/
    }

    /// <summary>
    /// Handles the retrieval of products for a specific section.
    /// </summary>
    /// <param name="page">The page number to retrieve.</param>
    /// <param name="size">The number of products per page.</param>
    /// <param name="email">The email of the user requesting the products.</param>
    /// <param name="productFilterDto">The filter criteria for the products.</param>
    /// <returns>A <see cref="ProductPaginationResponse"/> containing the paginated products for the section.</returns>
    private async Task<ProductPaginationResponse> HandleSectionProducts(int page, int size, string email,
        ProductFilterDto productFilterDto)
    {
        // Retrieve all products for the specified section
        var allProducts = await GetSectionProductsById(productFilterDto?.SectionId);
        var totalProducts = allProducts.Count;

        // Initialize the response object with pagination details
        var productResponse = new ProductPaginationResponse
        {
            Data = new ProductPaginationDto
            {
                Products = [],
                LastPage = (totalProducts / size) + 1
            }
        };

        // Paginate the products
        var paginatedProducts = allProducts.Skip((page - 1) * size).Take(size).ToList();
        var internalProductIds = paginatedProducts.Select(p => p.InternalProductId ?? "").ToList();

        // Check favorite status for the products
        var favoriteStatuses = await merchantService.CheckFavoriteMultiple(internalProductIds, email);
        var favoriteSet = new HashSet<string>(favoriteStatuses);

        // Populate the response with product details
        foreach (var product in paginatedProducts)
        {
            var image = string.Empty;
            if (!string.IsNullOrEmpty(product.ProductImages))
            {
                var imagesRaw = JsonSerializer.Deserialize<List<ProductImageDto>>(product.ProductImages);
                if (imagesRaw != null)
                {
                    image = imagesRaw.Select(a => a.Src).FirstOrDefault();
                }
            }

            productResponse.Data.Products.Add(new ProductRefDto
            {
                Id = product.InternalProductId ?? "",
                Name = product.Name ?? "",
                MerchantName = product.FkMerchant?.Name ?? "n/a",
                MerchantId = product.FkMerchant?.Id ?? 0,
                ImageSrc = image ?? "",
                BestSeller = false,
                Favored = favoriteSet.Contains(product.InternalProductId ?? ""),
                IncludesVariants = false,
                NormalPrice = product.RegularPrice ?? 0,
                SalePrice = product.Price ?? 0,
                OnSale = product.RegularPrice != product.Price && product.Price != null,
            });
        }

        return productResponse;
    }

    /// <summary>
    /// Handles the retrieval of products based on a free text search.
    /// </summary>
    /// <param name="page">The page number to retrieve.</param>
    /// <param name="size">The number of products per page.</param>
    /// <param name="email">The email of the user requesting the products.</param>
    /// <param name="productFilterDto">The filter criteria for the products.</param>
    /// <param name="contact">The contact information of the user.</param>
    /// <returns>A <see cref="ProductPaginationResponse"/> containing the paginated products.</returns>
    private async Task<ProductPaginationResponse> HandleFreeSearch(int page, int size, string email,
        ProductFilterDto productFilterDto, MerchantRelevanceCustomerDto contact)
    {
        // Record the start time for performance tracking
        var startTime = DateTime.UtcNow;

        // Initialize the Elasticsearch client
        var elasticClient = InitializeElasticClient();

        // Decrement the page number as the first page is considered 0 in Elasticsearch
        page--;

        // Prepare the search optimization DTO with the provided search criteria
        var searchOptimizeAppDto = new SearchOptimizeAppDto
        {
            Search = productFilterDto.Search,
            NegativeTags = string.Empty,
            ProductNameBoost = 2.0,
            ProductDescriptionBoost = 1,
            Bm25TextSearchBoost = 1.0f,
            TextVectorSearchBoost = 0.7f,
            MinimumMatchPercentage = 0,
            Page = page,
            Size = size,
            Similarity = 0,
            CategoryBoost = 1.5f,
            MerchantsToFilter = ""
        };

        // Perform the Elasticsearch query with the prepared DTO
        var response = await PerformElasticSearch(elasticClient, searchOptimizeAppDto, contact, startTime);

        // Process the Elasticsearch response and return the paginated product response
        return await ProcessElasticSearchResponse(response, searchOptimizeAppDto, productFilterDto, size, page, email,
            startTime);
    }

    /// <summary>
    /// Handles the retrieval of favorite products for a user based on their email, utilizing caching for performance optimization.
    /// </summary>
    /// <param name="page">The page number to retrieve.</param>
    /// <param name="size">The number of products per page.</param>
    /// <param name="email">The email of the user requesting the favorite products.</param>
    /// <returns>A <see cref="ProductPaginationResponse"/> containing the paginated favorite products.</returns>
    private async Task<ProductPaginationResponse> HandleFavorites(int page, int size, string email)
    {
        // Define a cache key based on the user's email
        var cacheKey = $"FavoriteProducts_{email}_{partnerContext.PartnerId}";

        // Attempt to retrieve the favorite products from the cache
        var products = await cacheService.GetData<List<Product>>(cacheKey, true);

        if (products == null)
        {
            // If not found in cache, retrieve from the database
            products = await merchantService.GetFavorites(email);

            // Store the retrieved favorite products in the cache
            cacheService.SetData(cacheKey, products, TimeSpan.FromHours(8), true);
        }

        // Initialize the response object with pagination details
        var productResponse = new ProductPaginationResponse
        {
            Data = new ProductPaginationDto
            {
                Products = [],
                LastPage = 1
            }
        };

        if (products.Count == 0)
        {
            return productResponse;
        }

        // Populate the response with product details
        foreach (var product in products)
        {
            var image = "";
            if (!string.IsNullOrEmpty(product.ProductImages))
            {
                var images = JsonSerializer.Deserialize<List<ProductImageDto>>(product.ProductImages);
                image = images?.FirstOrDefault()?.Src ?? "";
            }

            productResponse.Data.Products.Add(new ProductRefDto
            {
                Id = product.InternalProductId,
                Name = product.Name,
                NormalPrice = product.RegularPrice ?? 0,
                SalePrice = product.Price ?? 0,
                OnSale = product.RegularPrice != product.Price,
                Favored = true,
                IncludesVariants = product.Variants.Count > 0,
                ImageSrc = image,
                MerchantName = product.FkMerchant?.Name ?? "n/a",
                MerchantId = product.FkMerchantId,
                BestSeller = false
            });
        }

        // Calculate the pagination details
        var start = size * (page - 1);
        var totalProducts = productResponse.Data.Products.Count;
        productResponse.Data.LastPage = (int) Math.Ceiling((double) totalProducts / size);
        productResponse.Data.Products = productResponse.Data.Products.Skip(start).Take(size).ToList();

        return productResponse;
    }

    /// <summary>
    /// Tracks the products displayed to the user and sends the tracking data to RabbitMQ.
    /// </summary>
    /// <param name="productResponse">The response containing the paginated products.</param>
    /// <param name="page">The page number of the products displayed.</param>
    /// <param name="size">The number of products per page.</param>
    /// <param name="email">The email of the user to whom the products are displayed.</param>
    private async Task TrackProducts(ProductPaginationResponse productResponse, int page, int size, string email)
    {
        if (productResponse.Data.Products.FirstOrDefault()?.MerchantId != 0)
        {
            var merchantIds = productResponse.Data.Products.Select(a => a.MerchantId.ToString()).ToList();
            var merchantNames = productResponse.Data.Products.Select(a => a.MerchantName.ToString()).ToList();
            var productIds = productResponse.Data.Products.Select(a => a.Id.ToString()).ToList();
            var price = productResponse.Data.Products.Select(a => a.SalePrice).ToList();
            var priceRange = productResponse.Data.Products.Select(a => PriceConverter.ConvertPrice(a.SalePrice)).ToList();

            if (growthBook.IsOff("valyrionservice_exposureindices"))
            {
                var elasticShopProductsDisplays = new ElasticShopProductsDisplays()
                {
                    Action_date = DateTime.UtcNow,
                    Event_received = DateTime.UtcNow,
                    Marketing = new ElasticShopProductsDisplaysMarketing()
                    {
                        Page_number = page,
                        Page_size = size,
                        Channel = Channel,
                        Event = "internal_display"
                    },
                    Product = new ElasticProduct
                    {
                        Internal_product_id = productIds,
                        Price = price,
                        Price_range = priceRange
                    },
                    Customer = new ElasticCustomer
                    {
                        Email = email
                    },
                    Merchant = new ElasticMerchant
                    {
                        Id = merchantIds,
                        Name = merchantNames
                    }
                };
                var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticShopProductsDisplays));
                AddRabbitEvent(actionBody, "customer", "shop_product_display");
            }
            else
            {
                var partnerInfo = await partnerService.GetIdAndNameAsync(partnerContext.PartnerId);

                var elasticExposureProductDisplayDto = new ElasticExposureProductDisplayDto
                {
                    ActionDate = DateTime.UtcNow,
                    EventReceived = DateTime.UtcNow,
                    Marketing = new ElasticExposureMarketingDisplayDto
                    {
                        PageNumber = page,
                        PageSize = size,
                        Channel = Channel,
                        Event = "internal_display"
                    },
                    Product = new ElasticExposureProductListDto
                    {
                        InternalId = productIds,
                        Price = price,
                        PriceRange = priceRange
                    },
                    Customer = new ElasticExposureCustomerDto
                    {
                        Email = email
                    },
                    Merchant = new ElasticExposureMerchantListDto
                    {
                        Id = merchantIds,
                        Name = merchantNames
                    },
                    Partner = new ElasticExposurePartnerDto
                    {
                        Id = partnerInfo.Id.ToString(),
                        Name = partnerInfo.Name
                    }
                };

                var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticExposureProductDisplayDto));
                AddRabbitEvent(actionBody, "customer", "exposure_product_display");
            }
        }
    }

    /// <summary>
    /// Initializes and configures the Elasticsearch client.
    /// </summary>
    /// <returns>An instance of <see cref="ElasticsearchClient"/> configured with the specified settings.</returns>
    private ElasticsearchClient InitializeElasticClient()
    {
        // TODO Remove hardcoded values and use configuration after Elastic Index Refinement
        var prodElasticApiKey = "T0l3N09ZOEI3d0NHQkIycEp1T3U6WFl5ZEhOaDhSLUtucVhZUGdpdXQ1dw==";
        var settings = new ElasticsearchClientSettings(new Uri(configuration["ElasticHost"]))
            .Authentication(new ApiKey(prodElasticApiKey))
            .RequestTimeout(TimeSpan.FromMinutes(2));
        return new ElasticsearchClient(settings);
    }

    /// <summary>
    /// Performs an Elasticsearch query based on the provided search optimization criteria and user contact information.
    /// </summary>
    /// <param name="elasticClient">The Elasticsearch client used to perform the search.</param>
    /// <param name="searchOptimizeAppDto">The search optimization DTO containing filter criteria and search parameters.</param>
    /// <param name="contact">The contact information of the user.</param>
    /// <param name="startTime">The start time for performance tracking.</param>
    /// <returns>A <see cref="SearchResponse{ElasticSearchSearchEvent}"/> containing the search results.</returns>
    public async Task<ElasticSearchResponseDto> PerformElasticSearch(ElasticsearchClient elasticClient,
        SearchOptimizeAppDto searchOptimizeAppDto, MerchantRelevanceCustomerDto contact, DateTime startTime)
    {
        // Seed for randomization based on the contact ID
        var randomizationSeed = contact?.Id ?? 0;

        // Get merchant IDs for filtering
        List<string> merchantIds;
        if(searchOptimizeAppDto.MerchantsToFilter == "''") 
        {
            merchantIds = [];
        }
        else if(string.IsNullOrEmpty(searchOptimizeAppDto.MerchantsToFilter))
        {
            merchantIds = [];
        }
        else
        {
            merchantIds = searchOptimizeAppDto.MerchantsToFilter.Split(',').ToList();
        }
        var merchantIdValues = merchantIds.Select(id => (FieldValue)long.Parse(id)).ToArray();

        List<string> productsToFilter;
        if(string.IsNullOrEmpty(searchOptimizeAppDto.ProductsToFilter))
        {
            productsToFilter = [];
        }
        else
        {
            productsToFilter = searchOptimizeAppDto.ProductsToFilter.Split(',').ToList();
        }
		
		var multiMatchMust = new MultiMatchQuery()
        {
            Fields = new Field[]
                    {
                        new("Product.Name", searchOptimizeAppDto.ProductNameBoost),
                        new("Product.Description", searchOptimizeAppDto.ProductDescriptionBoost),
                        new("Product.Categories", searchOptimizeAppDto.CategoryBoost)
                    },
            MinimumShouldMatch = MinimumShouldMatch.Percentage(searchOptimizeAppDto.MinimumMatchPercentage),
            Query = searchOptimizeAppDto.Search,
            Fuzziness = new Fuzziness("AUTO"),
            Boost = searchOptimizeAppDto.Bm25TextSearchBoost
        };

        var multiMatchMustNot = new MultiMatchQuery()
        {
            Fields = new Field[]
                    {
                        new("Product.Name", searchOptimizeAppDto.ProductNameBoost),
                        new("Product.Description", searchOptimizeAppDto.ProductDescriptionBoost),
                        new("Product.Categories", searchOptimizeAppDto.CategoryBoost)
                    },
            Type = TextQueryType.CrossFields,
            Query = searchOptimizeAppDto.NegativeTags
        };

        var filters = new List<Query>()
        {
            new NumberRangeQuery("Product.Regular_price")
                {
                    Gt = StaticVariables.MinimumPriceForShowingProduct
                },
            new TermQuery("Partner.Id")
                {
                    Value = partnerContext.PartnerId.ToString()
                }

        };

        if (merchantIdValues.Count() > 0)
        {
            filters.Add(
                    new TermsQuery()
                        {
                            Field = "Merchant.Id",
                            Term = new TermsQueryField(merchantIdValues)
                        }
            );
        }

        if (productsToFilter.Count() > 0)
        {
            filters.Add(
                    new BoolQuery
                    {
                        MustNot = new Query[]
                            {
                                new TermsQuery()
                                {
                                    Field = "Product.Internal_product_id",
                                    Term = new TermsQueryField(productsToFilter.Select(id => (FieldValue)id).ToArray())
                                }
                            }
                    });
        }

        var combinedQuery = new BoolQuery
        {
            Must = new Query[] { multiMatchMust },
            MustNot = new Query[] { multiMatchMustNot },
            Filter = filters
        };

        // Build the base query with merchant filters
        //query = new QueryDescriptor<ElasticSearchSearchEvent>().Bool(b => b
        //    .Must(m => m
        //        .MultiMatch(mm => mm
        //            .Fields(new Field[]
        //            {
        //                new("Product.Name", searchOptimizeAppDto.ProductNameBoost),
        //                new("Product.Description", searchOptimizeAppDto.ProductDescriptionBoost),
        //                new("Product.Categories", searchOptimizeAppDto.CategoryBoost)
        //            })
        //            .MinimumShouldMatch(MinimumShouldMatch.Percentage(searchOptimizeAppDto.MinimumMatchPercentage))
        //            .Query(searchOptimizeAppDto.Search)
        //            .Fuzziness(new Fuzziness("AUTO"))
        //            .Boost(searchOptimizeAppDto.Bm25TextSearchBoost))
        //    )
        //    .MustNot(m => m
        //        .MultiMatch(mm => mm
        //            .Type(TextQueryType.CrossFields)
        //            .Fields(new Field[]
        //                {new("Product.Name"), new("Product.Description"), new("Product.Categories")})
        //            .Query(searchOptimizeAppDto.NegativeTags)))
        //    .Filter(f => f
        //        .Range(dt => dt
        //            .NumberRange(nr => nr
        //                .Field("Product.Regular_price").Gt(StaticVariables.MinimumPriceForShowingProduct)))
        //            , f => f
        //                .Terms(t => t
        //                    .Field("Merchant.Id")
        //                    .Term(new TermsQueryField(merchantIdValues)))
        //            , f => f.Bool(b => b
        //                .MustNot(mn => mn
        //                    .Terms(t => t
        //                        .Field("Product.Internal_product_id")
        //                        .Term(new TermsQueryField(productsToFilter.Select(id => FieldValue.String(id)).ToArray())))))
        //            , f => f.Term(t => t
        //                .Field("Partner.Id")
        //                .Value(partnerContext.PartnerId.ToString()))));

        // Track total hits for the search
        var track = new TrackHits(true);

        // Calculate pagination parameters
        var from = searchOptimizeAppDto.Page * searchOptimizeAppDto.Size;
        var size = searchOptimizeAppDto.Size * 2; // Request more products to ensure we have enough after filtering

        // Perform the search query with consistent sorting
        var response = await elasticClient.SearchAsync<ElasticSearchSearchEvent>(s => s
            .Index("merchants-products-search")
            .From(from)
            .Size(size)
            .TrackTotalHits(track)
            .Query(q => q
                .FunctionScore(fs => fs
                    .Query(combinedQuery)
                    .Functions(f => f.RandomScore(r => r.Seed(randomizationSeed).Field(f => f.ProductInfo.Id)))))
            .Sort(s => s
                .Field(f => f.ProductInfo.InternalProductId)));

        List<ElasticSearchResponseProductDto> products = [];
        if(merchantIds.Count != 0)
        {
            // Process and filter results
            products = response.Documents
                .Where(doc => merchantIds.Contains(doc.MerchantInfo.Id.ToString()))
            .Select(doc => new ElasticSearchResponseProductDto
            {
                Id = doc.ProductInfo.InternalProductId ?? "",
                Name = doc.ProductInfo.Name ?? "",
                MerchantName = doc.MerchantInfo.Name ?? "n/a",
                MerchantId = doc.MerchantInfo.Id,
                Description = doc.ProductInfo.Description ?? "",
                RegularPrice = doc.ProductInfo.RegularPrice,
                SalePrice = doc.ProductInfo.Price ?? 0,
                ImageUrl = doc.ProductInfo.Images.FirstOrDefault() ?? "",
            })
                .DistinctBy(p => p.Id) // Ensure no duplicates
                .Take(searchOptimizeAppDto.Size) // Take only the requested number of products
                .ToList();
        }
        else
        {
            products = response.Documents
                .Select(doc => new ElasticSearchResponseProductDto
                {
                    Id = doc.ProductInfo.InternalProductId ?? "",
                    Name = doc.ProductInfo.Name ?? "",
                    MerchantName = doc.MerchantInfo.Name ?? "n/a",
                    MerchantId = doc.MerchantInfo.Id,
                    Description = doc.ProductInfo.Description ?? "",
                    RegularPrice = doc.ProductInfo.RegularPrice,
                    SalePrice = doc.ProductInfo.Price ?? 0,
                    ImageUrl = doc.ProductInfo.Images.FirstOrDefault() ?? ""
                })
                .DistinctBy(p => p.Id) // Ensure no duplicates
                .Take(searchOptimizeAppDto.Size) // Take only the requested number of products
                .ToList();
        }

        return new ElasticSearchResponseDto
        {
            Products = products,
            Total = response.Total,
            Took = response.Took
        };
    }

    /// <summary>
    /// Processes the Elasticsearch response and returns a paginated product response.
    /// </summary>
    /// <param name="response">The Elasticsearch response containing the search results.</param>
    /// <param name="searchOptimizeAppDto">The search optimization DTO with filter criteria.</param>
    /// <param name="size">The number of products per page.</param>
    /// <param name="page">The page number to retrieve.</param>
    /// <param name="email">The email of the user requesting the products.</param>
    /// <param name="startTime">The start time for performance tracking.</param>
    /// <returns>A <see cref="ProductPaginationResponse"/> containing the paginated products.</returns>
    private async Task<ProductPaginationResponse> ProcessElasticSearchResponse(
        ElasticSearchResponseDto response, SearchOptimizeAppDto searchOptimizeAppDto, ProductFilterDto productFilter,
        int size,
        int page, string email, DateTime startTime)
    {
        // Initialize the response object with pagination details
        var productResponse = new ProductPaginationResponse
        {
            Data = new ProductPaginationDto
            {
                Products = [],
                LastPage = response.Total / size
            }
        };

        // Log the search event asynchronously
        _ = AddLogSearchEventAsync(new ElasticShopLog
        {
            Search = new ElasticShopLogSearch
            {
                Id = productFilter.SearchGuid ?? "n/a",
                Hits = response.Products.Count,
                Page = page + 1,
                Query = searchOptimizeAppDto.Search,
                Query_negative = searchOptimizeAppDto.NegativeTags,
                Merchant_filter_ids = searchOptimizeAppDto.MerchantsToFilter.Split(',').ToList(),
                Total_hits = response.Total
            },
            Product = new ElasticShopLogProduct
            {
                Internal_product_ids = response.Products.Select(a => a.Id).ToList()
            },
            Event = new ElasticShopLogEvent
            {
                Created = startTime,
                Duration = (long) (DateTime.UtcNow - startTime).TotalMilliseconds,
                Duration_raw = response.Took
            }
        });

        // Extract product information from the Elasticsearch response
        var products = response.Products;
        var internalProductIds = products.Select(p => p.Id).ToList();
        var favoriteStatuses = await merchantService.CheckFavoriteMultiple(internalProductIds, email);
        var favoriteSet = new HashSet<string>(favoriteStatuses);

        // Populate the response with product details
        foreach (var product in products)
        {
            productResponse.Data.Products.Add(new ProductRefDto
            {
                Id = product.Id,
                Name = product.Name,
                MerchantName = product.MerchantName,
                MerchantId = product.MerchantId,
                ImageSrc = product.ImageUrl,
                BestSeller = false,
                Favored = favoriteSet.Contains(product.Id),
                IncludesVariants = false,
                NormalPrice = product.RegularPrice,
                SalePrice = product.SalePrice ?? 0,
                OnSale = product.RegularPrice != product.SalePrice &&
                         product.SalePrice != null
            });
        }

        return productResponse;
    }


    /// <summary>
    /// Retrieves the products for a specific section by its ID, utilizing caching for performance optimization.
    /// </summary>
    /// <param name="sectionId">The ID of the section to retrieve products for.</param>
    /// <returns>A list of products for the specified section.</returns>
    private async Task<List<Product>> GetSectionProductsById(int? sectionId)
    {
        // Define a cache key based on the section ID
        var cacheKey = $"SectionProducts_{sectionId}";

        // Attempt to retrieve the products from the cache
        var products = await cacheService.GetData<List<Product>>(cacheKey, true);

        if (products == null)
        {
            // If not found in cache, retrieve the product IDs from the database
            var productIds = await shopDbContext.SectionProducts
                .Where(a => a.FkSectionId == sectionId)
                .Select(a => a.FkProductId)
                .ToListAsync();

            // Retrieve the products using the product IDs
            products = await merchantService.GetProductByProductIdMultiple(productIds);

            // Store the retrieved products in the cache
            cacheService.SetData(cacheKey, products, TimeSpan.FromHours(8), true);
        }

        return products;
    }

    public async Task<ProductSingleRefResponse> GetProductByProductIdAndEmailAsync(string productId, string email)
    {
        var contact = await customerService.GetByEmailAsync(email);
        var partnerGuid = string.Empty;
        if (contact != null && contact.PartnerGuid != null)
            partnerGuid = contact.PartnerGuid;
        return await merchantService.GetInAppProduct(productId, email, partnerGuid);
    }

    public async Task<List<CategoriesDto>> GetCategoriesAsync()
    {
        var allCategories = await memoryCache.GetOrCreateAsync($"ShopService_GetCategoriesAsync_{partnerContext.PartnerId}", async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1);
            var categories = new List<CategoriesDto>();
            var categoriesDb = await shopDbContext.Categories
                .Where(a => a.FkParentId == null && a.Active && !a.Deleted && a.NameDa != "Favoritter" && a.FkPartnerId == partnerContext.PartnerId)
                .OrderBy(a => a.SortOrder)
                .ToListAsync();
            foreach (var categoryDb in categoriesDb)
            {
                var category = new CategoriesDto
                {
                    Id = categoryDb.Id,
                    //Name = = categoryDb.NameDa,
                    Name = new CategoriesTranslationDto
                    {
                        Da = categoryDb.NameDa,
                        En = categoryDb.NameEn,
                        Es = categoryDb.NameEs
                    },
                    NameDA = categoryDb.NameDa,
                    NameEN = categoryDb.NameEn,
                    NameES = categoryDb.NameEs,
                    ImageSrc = categoryDb.Src
                };
                if (category.ImageSrc == "")
                {
                    category.ImageSrc = $"https://placehold.co/1200x800/FFFFFF/000000/png?text={categoryDb.NameDa}";
                }

                categories.Add(category);
            }

            return categories;
        });

        return allCategories!;
    }

    public async Task<CategoriesSubResponse> GetCategoriesSubCategoriesAsync(string email, int parentId)
    {
        if (parentId == 6)
        {
            return new CategoriesSubResponse()
            {
                Error = new ShopErrorDto()
                {
                    Status = 404,
                    Error = "Not Found",
                    Message = "Invalid parentId provided."
                }
            };
        }

        var categories = new List<CategoriesDto>();
        var categoriesDb = await shopDbContext.Categories.Where(a => a.FkParentId == parentId && a.Active)
            .OrderBy(a => a.SortOrder).ToListAsync();
        foreach (var categoryDb in categoriesDb)
        {
            var sub = new CategoriesDto
            {
                Id = categoryDb.Id,
                //Name = categoryDb.NameDa,
                Name = new CategoriesTranslationDto
                {
                    Da = categoryDb.NameDa,
                    En = categoryDb.NameEn,
                    Es = categoryDb.NameEs
                },
                NameDA = categoryDb.NameDa,
                NameEN = categoryDb.NameEn,
                NameES = categoryDb.NameEs,
                ParentId = parentId,
                ImageSrc = categoryDb.Src
            };
            if (sub.ImageSrc == "")
            {
                sub.ImageSrc = $"https://placehold.co/1200x800/FFFFFF/000000/png?text={categoryDb.NameDa}";
            }

            categories.Add(sub);
        }

        var categoryParent = await shopDbContext.Categories.FirstAsync(a => a.Id == parentId);
        var elasticShopCategoriesInteracts = new ElasticShopCategoriesInteracts
        {
            Action_date = DateTime.UtcNow,
            Event_received = DateTime.UtcNow,
            Category = new ElasticShopCategoriesInteractsCategory
            {
                Id = categoryParent.Id.ToString(),
                Name = categoryParent.NameDa,
                Parent_id = categoryParent.FkParentId.ToString() ?? "n/a"
            },
            Marketing = new ElasticMarketing
            {
                Channel = Channel,
                Event = "category_interacts_internal"
            },
            Customer = new ElasticShopCategoriesInteractsCustomer
            {
                Email = email,
                Action = "n/a"
            }
        };

        if (elasticShopCategoriesInteracts.Category.Parent_id == "")
        {
            elasticShopCategoriesInteracts.Category.Parent_id = "n/a";
        }

        var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticShopCategoriesInteracts));
        AddRabbitEvent(actionBody, "customer", "shop_category_interact");
        return new CategoriesSubResponse()
        {
            Data = categories
        };
    }

    public async Task<List<SectionDto>> GetSectionsAsync()
    {
        // Define a cache key for sections
        var cacheKey = $"Sections_{partnerContext.PartnerId}";

        // Attempt to retrieve the sections from the cache
        var sectionsDto = await cacheService.GetData<List<SectionDto>>(cacheKey, true);

        if (sectionsDto == null)
        {
            // If not found in cache, retrieve from the database
            sectionsDto = [];
            var sections = await shopDbContext.Sections
                .Include(a => a.SectionProducts.Where(b => b.Active && !b.IsInvalidated))
                .Include(a => a.SectionTranslations.Where(b => b.Active))
                .Where(a => a.Active && !a.Deleted && a.FkPartnerId == partnerContext.PartnerId)
                .OrderBy(a => a.SortOrder)
                .ToListAsync();

            foreach (var section in sections)
            {
                var sectionDto = new SectionDto
                {
                    Id = section.Id,
                    Title = new Dictionary<string, string>(),
                    Subtitle = new Dictionary<string, string>(),
                    Type = section.Type,
                    Products = []
                };

                foreach (var sectionTranslation in section.SectionTranslations)
                {
                    sectionDto.Title.Add(sectionTranslation.LanguageCode, sectionTranslation.Title);
                    sectionDto.Subtitle.Add(sectionTranslation.LanguageCode, sectionTranslation.Subtitle);
                }

                var products =
                    await merchantService.GetProductByProductIdMultiple(section.SectionProducts
                        .Select(a => a.FkProductId).ToList());
                foreach (var sectionProduct in section.SectionProducts.OrderBy(a => a.SortOrder))
                {
                    if (sectionDto.Products.Count >= 6) break;
                    var product = products.SingleOrDefault(a => a.Id == sectionProduct.FkProductId);
                    if (product is not {Active: true}) continue;

                    var image = "";
                    if (!string.IsNullOrEmpty(product.ProductImages))
                    {
                        var images = JsonSerializer.Deserialize<List<ProductImageDto>>(product.ProductImages);
                        image = images?.FirstOrDefault()?.Src ?? "";
                    }

                    sectionDto.Products.Add(new ProductRefDto
                    {
                        Id = product.InternalProductId,
                        Name = product.Name,
                        MerchantName = product.FkMerchant.Name,
                        MerchantId = product.FkMerchantId,
                        ImageSrc = image,
                        BestSeller = false,
                        Favored = false,
                        IncludesVariants = false,
                        NormalPrice = product.RegularPrice ?? 0,
                        SalePrice = product.Price ?? 0,
                        OnSale = product.RegularPrice != product.Price && product.Price != null,
                    });
                }

                sectionDto.ProductCount = sectionDto.Products.Count;
                sectionsDto.Add(sectionDto);
            }

            // Store the retrieved sections in the cache
            cacheService.SetData(cacheKey, sectionsDto, TimeSpan.FromHours(8), true);
        }

        logger.ForContext("service_name", GetType().Name).Information(
            "Returned  {sectionCount} sections, with a total of {productCount} to Customer Email: {Email}",
            sectionsDto.Count, sectionsDto.Sum(a => a.Products.Count), "Unknown");

        return sectionsDto;
    }

    public async Task<ResponseDto> ToggleFavoriteAsync(ShopFavoriteRequestDto favoriteRequest)
    {
        // Call the merchant service to update the favorite
        var result = await merchantService.UpdateFavoriteProduct(favoriteRequest);
    
        if (result.Success)
        {
            // Clear favorite-related caches
            cacheService.RemoveData($"FavoriteProducts_{favoriteRequest.Email}_{partnerContext.PartnerId}");
            cacheService.RemoveData($"FavoriteProductIds_{favoriteRequest.Email}_{partnerContext.PartnerId}");
        
            // Also invalidate the product status cache to ensure we get the latest status
            await productStatusService.InvalidateProductStatusAsync(favoriteRequest.ProductId);
        }

        return result;
    }

    public async Task<ResponseDto> AddShopEventAsync(ShopEventDto shopEvent)
    {
        try
        {
            if (!ShopEventTypes.IsValidEventType(shopEvent.EventType))
            {
                logger.ForContext("service_name", GetType().Name).Warning(
                    "Received an unknown shop event: {event} from Customer Email: {Email}",
                    shopEvent.EventType, shopEvent.CustomerEmail);

                return new ResponseDto
                {
                    Success = false,
                    Message =
                        $"The EventType: {shopEvent.EventType} is unknown - known EventTypes are: {string.Join(", ", ShopEventTypes.GetKnownEventTypes())}"
                };
            }

            if (!string.IsNullOrEmpty(shopEvent.ProductId))
            {
                var product = await merchantService.GetProductByInternalProductId(shopEvent.ProductId);
                if (product != null)
                {
                    if (ShopEventTypes.IsInteractEvent(shopEvent.EventType))
                    {
                        if (growthBook.IsOff("valyrionservice_exposureindices"))
                        {
                            var elasticShopProductsInteracts = CreateElasticShopProductsInteracts(shopEvent, product);
                            var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticShopProductsInteracts));
                            AddRabbitEvent(actionBody, "customer", "shop_product_interact");
                        }
                        else
                        {
                            if (growthBook.IsOff("valyrionservice_exposureindices"))
                            {
                                var elasticShopProductsInteracts = await CreateElasticExposureProductsInteractsAsync(shopEvent, product);
                                var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticShopProductsInteracts));
                                AddRabbitEvent(actionBody, "customer", "exposure_product_interact");
                            }
                            else
                            {
                                var elasticShopProductsRedirects = await CreateElasticExposureProductsRedirectsAsync(shopEvent, product);
                                var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticShopProductsRedirects));
                                AddRabbitEvent(actionBody, "customer", "exposure_product_redirect");
                            }
                        }
                    }
                    else if (ShopEventTypes.IsRedirectEvent(shopEvent.EventType))
                    {
                        var elasticShopProductsRedirects = CreateElasticShopProductsRedirects(shopEvent, product);
                        var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticShopProductsRedirects));
                        AddRabbitEvent(actionBody, "customer", "shop_product_redirect");
                    }
                }
            }
            else if (shopEvent.ProductIds is {Count: > 0})
            {
                var products = new List<Product>();
                foreach (var productId in shopEvent.ProductIds)
                {
                    // Use separate DbContext instances for each product retrieval
                    var product = await merchantService.GetProductByInternalProductId(productId);
                    if (product != null)
                    {
                        products.Add(product);
                    }
                }

                if (ShopEventTypes.IsDisplayEvent(shopEvent.EventType))
                {
                    if (growthBook.IsOff("valyrionservice_exposureindices"))
                    {
                        var elasticShopProductsDisplays = CreateElasticShopProductsDisplays(shopEvent, products);
                        var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticShopProductsDisplays));
                        AddRabbitEvent(actionBody, "customer", "shop_product_display");
                    }
                    else
                    {
                        var elasticShopProductsDisplays = await CreateElasticExposureProductsDisplaysAsync(shopEvent, products);
                        var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticShopProductsDisplays));
                        AddRabbitEvent(actionBody, "customer", "exposure_product_display");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(
                ex,
                "{event} sending to {component} exchange '{exchange}' with routing key '{routingKey}' Shop Event object: {ShopEvent}",
                "Failed", "RabbitMQ", "customer", "customer_shop_event",
                JsonSerializer.Serialize(shopEvent));
            return new ResponseDto {Success = false, Message = "Adding the Event was unsuccessful"};
        }

        logger.ForContext("service_name", GetType().Name).Information(
            "Successfully added a new Event: {event} from Customer Email: {Email}",
            shopEvent.EventType, shopEvent.CustomerEmail);

        return new ResponseDto {Success = true, Message = "Successfully added the Event"};
    }

    public async Task<List<LogoDto>> GetLogosAsync()
    {
        var logos = new List<LogoDto>();
        // TODO - Replace with a more efficient way to get the merchant logos
        var merchants = await merchantService.GetFullAsync();
        foreach (var merchant in merchants)
        {
            var logo = merchant.MerchantAssets.FirstOrDefault(a => a.FkMerchantAssetTypeId == 2);
            if (logo != null)
            {
                logos.Add(new LogoDto
                {
                    Redirect = merchant.Url,
                    Src = logo.Src
                });
            }
        }

        return logos;
    }

    public async Task<List<Banner>> GetBannersAsync(string languageCode)
    {
        var partnerId = partnerContext.PartnerId;
        var banners = await shopDbContext.Banners.Where(b => b.LanguageCode == languageCode && b.FkPartnerId == partnerId).ToListAsync();
        return banners;
    }

    private ElasticShopProductsInteracts CreateElasticShopProductsInteracts(ShopEventDto shopEvent, Product product)
    {
        return new ElasticShopProductsInteracts
        {
            Action_date = DateTime.UtcNow,
            Event_received = DateTime.UtcNow,
            Marketing = new ElasticMarketing
            {
                Channel = Channel,
                Event = shopEvent.EventType
            },
            Customer = new ElasticCustomer
            {
                Email = shopEvent.CustomerEmail
            },
            Merchant = new ElasticMerchant
            {
                Id = [product.FkMerchantId.ToString()],
                Name = [product.FkMerchant.Name]
            },
            Product = new ElasticProduct
            {
                Internal_product_id = [product.InternalProductId],
                Price = [product.Price ?? 0],
                Price_range = [PriceConverter.ConvertPrice(product.Price)],
            },
            Search = new ElasticSearch
            {
                Id = shopEvent.SearchGuid ?? "n/a"
            }
        };
    }

    private ElasticShopProductsRedirects CreateElasticShopProductsRedirects(ShopEventDto shopEvent, Product product)
    {
        return new ElasticShopProductsRedirects
        {
            Action_date = DateTime.UtcNow,
            Event_received = DateTime.UtcNow,
            Marketing = new ElasticMarketing
            {
                Channel = Channel,
                Event = shopEvent.EventType
            },
            Customer = new ElasticCustomer
            {
                Email = shopEvent.CustomerEmail
            },
            Merchant = new ElasticMerchant
            {
                Id = [product.FkMerchantId.ToString()],
                Name = [product.FkMerchant.Name]
            },
            Product = new ElasticProduct
            {
                Internal_product_id = [product.InternalProductId],
                Price = [product.Price ?? 0],
                Price_range = [PriceConverter.ConvertPrice(product.Price)],
            },
            Search = new ElasticSearch
            {
                Id = shopEvent.SearchGuid ?? "n/a"
            }
        };
    }

    private ElasticShopProductsDisplays CreateElasticShopProductsDisplays(ShopEventDto shopEvent,
        List<Product> products)
    {
        return new ElasticShopProductsDisplays
        {
            Action_date = DateTime.UtcNow,
            Event_received = DateTime.UtcNow,
            Marketing = new ElasticShopProductsDisplaysMarketing
            {
                Channel = Channel,
                Event = shopEvent.EventType,
                List_type = shopEvent.ListType ?? "Not Specified",
                List_id = shopEvent.ListId ?? 0,
            },
            Customer = new ElasticCustomer
            {
                Email = shopEvent.CustomerEmail
            },
            Merchant = new ElasticMerchant
            {
                Id = products.Select(product => product.FkMerchantId.ToString()).ToList(),
                Name = products.Select(product => product.FkMerchant.Name).ToList()
            },
            Product = new ElasticProduct
            {
                Internal_product_id = products.Select(product => product.InternalProductId).ToList(),
                Price = products.Select(product => product.Price ?? 0).ToList(),
                Price_range = products.Select(product => PriceConverter.ConvertPrice(product.Price)).ToList(),
            },
            Search = new ElasticSearch
            {
                Id = shopEvent.SearchGuid ?? "n/a"
            }
        };
    }

    private async Task<ElasticExposureProductInteractDto> CreateElasticExposureProductsInteractsAsync(ShopEventDto shopEvent, Product product)
    {
        var partnerInfo = await partnerService.GetIdAndNameAsync(partnerContext.PartnerId);

        return new ElasticExposureProductInteractDto
        {
            ActionDate = DateTime.UtcNow,
            EventReceived = DateTime.UtcNow,
            Marketing = new ElasticExposureMarketingDto
            {
                Channel = Channel,
                Event = shopEvent.EventType
            },
            Customer = new ElasticExposureCustomerDto
            {
                Email = shopEvent.CustomerEmail
            },
            Product = new ElasticExposureProductDto
            {
                InternalId = product.InternalProductId,
                Price = product.Price ?? 0,
                PriceRange = PriceConverter.ConvertPrice(product.Price)
            },
            Search = new ElasticExposureSearchDto
            {
                Id = shopEvent.SearchGuid ?? "n/a"
            },
            Merchant = new ElasticExposureMerchantDto
            {
                Id = product.FkMerchantId.ToString(),
                Name = product.FkMerchant.Name
            },
            Partner = new ElasticExposurePartnerDto
            {
                Id = partnerInfo.Id.ToString(),
                Name = partnerInfo.Name
            }
        };
    }

    private async Task<ElasticExposureProductRedirectDto> CreateElasticExposureProductsRedirectsAsync(ShopEventDto shopEvent, Product product)
    {
        var partnerInfo = await partnerService.GetIdAndNameAsync(partnerContext.PartnerId);

        return new ElasticExposureProductRedirectDto
        {
            ActionDate = DateTime.UtcNow,
            EventReceived = DateTime.UtcNow,
            Marketing = new ElasticExposureMarketingDto
            {
                Channel = Channel,
                Event = shopEvent.EventType
            },
            Customer = new ElasticExposureCustomerDto
            {
                Email = shopEvent.CustomerEmail
            },
            Product = new ElasticExposureProductDto
            {
                InternalId = product.InternalProductId,
                Price = product.Price ?? 0,
                PriceRange = PriceConverter.ConvertPrice(product.Price)
            },
            Search = new ElasticExposureSearchDto
            {
                Id = shopEvent.SearchGuid ?? "n/a"
            },
            Url = new ElasticExposureUrlDto
            {
                Full = product.Permalink ?? "n/a"
            },
            Merchant = new ElasticExposureMerchantDto
            {
                Id = product.FkMerchantId.ToString(),
                Name = product.FkMerchant.Name
            },
            Partner = new ElasticExposurePartnerDto
            {
                Id = partnerInfo.Id.ToString(),
                Name = partnerInfo.Name
            }
        };
    }

    private async Task<ElasticExposureProductDisplayDto> CreateElasticExposureProductsDisplaysAsync(ShopEventDto shopEvent,
        List<Product> products)
    {
        var partnerInfo = await partnerService.GetIdAndNameAsync(partnerContext.PartnerId);

        return new ElasticExposureProductDisplayDto
        {
            ActionDate = DateTime.UtcNow,
            EventReceived = DateTime.UtcNow,
            Marketing = new ElasticExposureMarketingDisplayDto
            {
                Channel = Channel,
                Event = shopEvent.EventType,
                ListType = shopEvent.ListType ?? "Not Specified",
                ListId = shopEvent.ListId.ToString() ?? "0"
            },
            Customer = new ElasticExposureCustomerDto
            {
                Email = shopEvent.CustomerEmail
            },
            Merchant = new ElasticExposureMerchantListDto
            {
                Id = products.Select(product => product.FkMerchantId.ToString()).ToList(),
                Name = products.Select(product => product.FkMerchant.Name).ToList()
            },
            Product = new ElasticExposureProductListDto
            {
                InternalId = products.Select(product => product.InternalProductId).ToList(),
                Price = products.Select(product => product.Price ?? 0).ToList(),
                PriceRange = products.Select(product => PriceConverter.ConvertPrice(product.Price)).ToList(),
            },
            Search = new ElasticExposureSearchDto
            {
                Id = shopEvent.SearchGuid ?? "n/a"
            },
            Partner = new ElasticExposurePartnerDto
            {
                Id = partnerInfo.Id.ToString(),
                Name = partnerInfo.Name
            }
        };
    }


    private void AddRabbitEvent(Byte[] actionBody, string exchange, string routingKey)
    {
        using (var publishChannel = rabbitConnectionCloud.CreateModel())
        {
            publishChannel.BasicPublish(exchange: exchange,
                routingKey: routingKey,
                basicProperties: null,
                body: actionBody);
        }
    }

    private void Shuffle<T>(List<T> list)
    {
        Random rng = new Random();
        int n = list.Count;
        while (n > 1)
        {
            n--;
            int k = rng.Next(n + 1);
            (list[k], list[n]) = (list[n], list[k]);
        }
    }

    private async Task<List<int>> GetAllowedMerchantIds(string gender, string type)
    {
        var partnerId = partnerContext.PartnerId;
        var allowedMerchantsCacheKey = $"AllowedMerchants_{gender}_{type}_{partnerId}";

        var allowedMerchants = await cacheService.GetData<List<int>>(allowedMerchantsCacheKey, true);

        if (allowedMerchants == null)
        {
            allowedMerchants = (await shopDbContext.MerchantGenderRels
                .Include(a => a.FkMerchantGender)
                .Where(a => a.Active && a.FkMerchantGender.Active && a.FkMerchantGender.Type == type &&
                            a.FkMerchantGender.Name == gender)
                .Select(a => a.FkMerchantId)
                .ToListAsync());

            cacheService.SetData(allowedMerchantsCacheKey, allowedMerchants, TimeSpan.FromHours(2), true);
        }

        return allowedMerchants;
    }

    private async Task<ProductPaginationResponse> ProductHandling(int page, int size, string email,
        List<MerchantRelevance> topMerchants, string cacheKeyNewValue, string type,
        int maxProductsPrMerchant, bool updateInteractedProducts = false)
    {
        var partnerId = partnerContext.PartnerId;
        var allProductsCacheKey = $"AllCachedProductsByType_{type}_{email}_{partnerId}";

        var stopWatch = new Stopwatch();
        stopWatch.Start();

        var allCachedProducts = await cacheService.GetDataWithCacheLockAsync(
            allProductsCacheKey,
            async () =>
            {
                var returnableProducts = new List<ProductRefDto>();

                if (type == "3")
                {
                    List<ProductRefDto> interactedProducts = [];

                    var interactedProductsCacheKey = $"ShopService_InteractedProducts_{email}";
                    var cachedInteractedProducts =
                        await cacheService.GetData<List<ProductRefDto>>(interactedProductsCacheKey, true);

                    if (cachedInteractedProducts != null)
                    {
                        interactedProducts = cachedInteractedProducts;
                    }
                    else
                    {
                        if (updateInteractedProducts)
                        {
                            var interactProductsToShow =
                                Convert.ToInt32(
                                    (await settingService.GetSettingAsync(partnerId, "ProductInteractShowNumber"))
                                    .Value);
                            var interactProductsRedirectBoost = Convert.ToDouble(
                                (await settingService.GetSettingAsync(partnerId, "ProductInteractRedirectBoost")).Value,
                                CultureInfo.InvariantCulture);
                            var interactProductsLookBack =
                                Convert.ToInt32(
                                    (await settingService.GetSettingAsync(partnerId, "ProductInteractLookBackDays"))
                                    .Value);

                            var interactedProductIds = await elasticShopProductService.ShopProductsByEmail(
                                interactProductsLookBack, interactProductsToShow, interactProductsRedirectBoost, email);

                            if (interactedProductIds.Count > 0)
                            {
                                interactedProducts =
                                    await merchantService.GetInAppProductsFromIdList(interactedProductIds.Select(id => id.ProductId).ToList());

                                if (interactedProducts.Count > 0)
                                {
                                    interactedProducts.ForEach(product => product!.Interacted = true);

                                    cacheService.SetData(interactedProductsCacheKey, interactedProducts,
                                        TimeSpan.FromMinutes(15), true);
                                }
                            }
                        }
                    }

                    // Only take the interacted products that fits the current page and add them to the front of the list to show
                    returnableProducts.AddRange(interactedProducts.Take(size));
                }

                stopWatch.Stop();
                Console.WriteLine($"Interacted products took: {stopWatch.ElapsedMilliseconds}ms");

                stopWatch.Restart();
                var curatedProducts = await cacheService.GetData<List<CuratedProduct>>(cacheKeyNewValue, true) ?? [];
                List<CuratedProductEvents> products = [];

                // Use top merchants if available
                if (topMerchants.Any())
                {
                    foreach (var merchant in topMerchants)
                    {
                        var curatedProduct = curatedProducts.FirstOrDefault(a => a.MerchantId == merchant.MerchantId);
                        if (curatedProduct != null)
                        {
                            products.AddRange(curatedProduct.Products.Take(maxProductsPrMerchant));
                        }
                    }
                }
                else
                {
                    // Fallback to gender-based merchant filtering
                    var contact = await customerService.GetCustomerForMerchantRelevance(email);
                    var gender = contact?.Gender ?? "Unknown";
                    var allowedMerchantIds = await GetAllowedMerchantIds(gender, type);

                    logger.ForContext("service_name", GetType().Name).Information(
                        "No top merchants available. Using gender-based filtering. Gender: {gender}, Type: {type}, Email: {email}, PartnerId: {partnerId}",
                        gender, type, email, partnerId);

                    var filteredMerchants = curatedProducts
                        .Where(cp => allowedMerchantIds.Contains(cp.MerchantId))
                        .Select(cp => new MerchantRelevance { MerchantId = cp.MerchantId })
                        .ToList();

                    if (!filteredMerchants.Any())
                    {
                        logger.ForContext("service_name", GetType().Name).Warning(
                            "No allowed merchants available for gender {gender}. Type: {type}, Email: {email}, PartnerId: {partnerId}",
                            gender, type, email, partnerId);
                    }

                    foreach (var merchant in filteredMerchants)
                    {
                        var curatedProduct = curatedProducts.FirstOrDefault(a => a.MerchantId == merchant.MerchantId);
                        if (curatedProduct != null)
                        {
                            products.AddRange(curatedProduct.Products.Take(maxProductsPrMerchant));
                        }
                    }
                }

                stopWatch.Stop();
                Console.WriteLine($"Curated products took: {stopWatch.ElapsedMilliseconds}ms");

                stopWatch.Restart();
                if (products.Count < 20)
                {
                    // While products are less than 20, add more products random from the curated products
                    var randomProducts = curatedProducts
                        .SelectMany(a => a.Products)
                        .Where(product => 
                            product.ProductRefDto != null && 
                            !products.Any(p => p.ProductRefDto!.Id == product.ProductRefDto!.Id))
                        .ToList();

                    Shuffle(randomProducts);
                    products.AddRange(randomProducts.Take(20 - products.Count));

                    // Fallback logic to ensure we have at least 20 products
                    if (products.Count < 20)
                    {
                        logger.ForContext("service_name", GetType().Name).Warning(
                            "Product count ({count}) is less than minimum required (20). Attempting fallback logic. Type: {type}, Email: {email}, PartnerId: {partnerId}",
                            products.Count, type, email, partnerId);

                        // Try to get more products by using all products from curated products
                        var additionalProducts = curatedProducts
                            .SelectMany(a => a.Products)
                            .Where(product => 
                                product.ProductRefDto != null && 
                                !products.Any(p => p.ProductRefDto!.Id == product.ProductRefDto!.Id))
                            .ToList();

                        if (additionalProducts.Count > 0)
                        {
                            Shuffle(additionalProducts);
                            products.AddRange(additionalProducts.Take(20 - products.Count));
                            
                            logger.ForContext("service_name", GetType().Name).Information(
                                "Fallback logic added {count} additional products. New total: {newTotal}",
                                additionalProducts.Count, products.Count);
                        }
                        else
                        {
                            logger.ForContext("service_name", GetType().Name).Error(
                                "Fallback logic failed to add more products. Still below minimum required (20). Current count: {count}",
                                products.Count);
                        }
                    }
                }

                if(products.Any(p => p.ProductRefDto!.MerchantId == 5552))
                {
                    Console.WriteLine("Found product 5552");
                }

                Shuffle(products);
                stopWatch.Stop();
                Console.WriteLine($"Random products took: {stopWatch.ElapsedMilliseconds}ms");

                returnableProducts.AddRange(products.Select(curatedProductEvent => curatedProductEvent.ProductRefDto)!);
                return returnableProducts;
            },
            memoryCacheExpiry: TimeSpan.FromMinutes(10),
            redisCacheExpiry: TimeSpan.FromMinutes(10)
        ) ?? [];

        // Paginate the products and return the response
        var paginatedProducts = allCachedProducts.Skip((page - 1) * size).Take(size).ToList();
        var totalProducts = allCachedProducts.Count;
        var totalPages = (int)Math.Ceiling(totalProducts / (decimal)size);
        var isLastPage = page >= totalPages;

        // Log if we're returning fewer products than requested and it's not the last page
        if (paginatedProducts.Count < size && !isLastPage)
        {
            logger.ForContext("service_name", GetType().Name).Warning(
                "Returning fewer products than requested. Page: {page}, Requested: {requested}, Actual: {actual}, Total: {total}, IsLastPage: {isLastPage}, Type: {type}, Email: {email}, PartnerId: {partnerId}",
                page, size, paginatedProducts.Count, totalProducts, isLastPage, type, email, partnerId);
        }

        stopWatch.Restart();

        var favorites =
            await merchantService.CheckFavoriteMultiple(paginatedProducts.Select(p => p.Id).ToList(), email);
        var favoriteSet = new HashSet<string>(favorites);
        foreach (var product in paginatedProducts)
        {
            product.Favored = favoriteSet.Contains(product.Id);
        }

        stopWatch.Stop();
        Console.WriteLine($"Check favorites took: {stopWatch.ElapsedMilliseconds}ms");

        return new ProductPaginationResponse
        {
            Data = new ProductPaginationDto
            {
                Products = paginatedProducts,
                LastPage = totalPages
            }
        };
    }

    private async Task AddLogSearchEventAsync(ElasticShopLog elasticShopLog)
    {
        try
        {
            using (var publishChannel = rabbitConnectionCloud.CreateModel())
            {
                var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticShopLog));
                publishChannel.BasicPublish(exchange: "customer",
                    routingKey: "customer_search",
                    basicProperties: null,
                    body: actionBody);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(
                ex,
                "{event} sending to {component} exchange '{exchange}' with routing key '{routingKey}' Discount Event object: {discountEvent}",
                "Failed", "RabbitMQ", "customer", "customer_discount_event",
                JsonSerializer.Serialize(elasticShopLog));
        }
    }
}