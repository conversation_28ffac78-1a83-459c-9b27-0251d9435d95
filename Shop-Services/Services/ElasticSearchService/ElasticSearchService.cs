using System.Text;
using System.Text.Json;
using Elastic.Clients.Elasticsearch;
using Elastic.Clients.Elasticsearch.Core.Search;
using Elastic.Clients.Elasticsearch.QueryDsl;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Shared.Dto.Shop;
using Shared.Dto.Shop.Product;
using Shared.Elastic.Models.ElasticShopLog;
using Shared.Services;
using Shared.Services.Partner;
using Shop_Services.Models;
using Shop_Services.Models.ElasticSearch;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;
using ApiKey = Elastic.Transport.ApiKey;
using Field = Elastic.Clients.Elasticsearch.Field;
using RabbitMQ.Client;

namespace Shop_Services.Services.ElasticSearchService;

public interface IElasticSearchService
{
    Task<ElasticSearchResponseDto> PerformElasticSearch(
        SearchOptimizeAppDto searchOptimizeAppDto);

    Task<ProductPaginationResponse> ProcessElasticSearchResponse(
        ElasticSearchResponseDto response,
        SearchOptimizeAppDto searchOptimizeAppDto,
        ProductFilterDto productFilter,
        int size,
        int page,
        string email,
        DateTime startTime);
}

public class ElasticSearchService : IElasticSearchService
{
    private readonly IConfiguration _configuration;
    private readonly IMerchantService _merchantService;
    private readonly IPartnerContext _partnerContext;
    private readonly ILogger _logger;
    private readonly IConnection _rabbitConnectionCloud;

    public ElasticSearchService(
        IConfiguration configuration,
        IMerchantService merchantService,
        IPartnerContext partnerContext,
        [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud,
        ILogger logger)
    {
        _configuration = configuration;
        _merchantService = merchantService;
        _partnerContext = partnerContext;
        _rabbitConnectionCloud = rabbitConnectionCloud;
        _logger = logger;
    }

    /// <summary>
    /// Initializes and configures the Elasticsearch client.
    /// </summary>
    /// <returns>An instance of <see cref="ElasticsearchClient"/> configured with the specified settings.</returns>
    private ElasticsearchClient InitializeElasticClient()
    {
        // TODO Remove hardcoded values and use configuration after Elastic Index Refinement
        var prodElasticApiKey = "T0l3N09ZOEI3d0NHQkIycEp1T3U6WFl5ZEhOaDhSLUtucVhZUGdpdXQ1dw==";
        var settings = new ElasticsearchClientSettings(new Uri(_configuration["ElasticHost"]))
            .Authentication(new ApiKey(prodElasticApiKey))
            .RequestTimeout(TimeSpan.FromMinutes(2));
        return new ElasticsearchClient(settings);
    }

    /// <summary>
    /// Performs an Elasticsearch query based on the provided search optimization criteria and user contact information.
    /// </summary>
    /// <param name="searchOptimizeAppDto">The search optimization DTO containing filter criteria and search parameters.</param>
    /// <returns>A <see cref="ElasticSearchResponseDto"/> containing the search results.</returns>
    public async Task<ElasticSearchResponseDto> PerformElasticSearch(
        SearchOptimizeAppDto searchOptimizeAppDto)
    {
        var elasticClient = InitializeElasticClient();
        var randomizationSeed = searchOptimizeAppDto.RandomizeSeed;
        var partnerId = _partnerContext.PartnerId;
        QueryDescriptor<ElasticSearchSearchEvent> query;

        // Build the main query
        if (!string.IsNullOrEmpty(searchOptimizeAppDto.MerchantsToFilter))
        {
            // With merchant filter
            var merchantIds = searchOptimizeAppDto.MerchantsToFilter
                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(id => FieldValue.String(id)).ToArray();

            var filters = BuildCommonFilters(searchOptimizeAppDto, partnerId);
            filters.Add(new TermsQuery()
            {
                Field = "Merchant.Id",
                Term = new TermsQueryField(merchantIds)
            });

            query = new QueryDescriptor<ElasticSearchSearchEvent>().Bool(b => b
                .Must(new Query[] { BuildProductMultiMatch(searchOptimizeAppDto, partnerId) })
                .MustNot(new Query[] { BuildNegativeTagsQuery(searchOptimizeAppDto) })
                .Filter(filters.ToArray())
            );
        }
        else
        {
            // Default (no merchant filter)
            var filters = BuildCommonFilters(searchOptimizeAppDto, partnerId);
            query = new QueryDescriptor<ElasticSearchSearchEvent>().Bool(b => b
                .Must(new Query[] { BuildProductMultiMatch(searchOptimizeAppDto, partnerId) })
                .MustNot(new Query[] { BuildNegativeTagsQuery(searchOptimizeAppDto) })
                .Filter(filters.ToArray())
            );
        }

        // Track total hits for the search
        var track = new TrackHits(true);
        SearchResponse<ElasticSearchSearchEvent> response;

        // Execute the search with or without randomization
        if (searchOptimizeAppDto.Randomize)
        {
            response = await elasticClient.SearchAsync<ElasticSearchSearchEvent>(s => s
                .Index("merchants-products-search-alias")
                .From(searchOptimizeAppDto.Page * searchOptimizeAppDto.Size)
                .Size(searchOptimizeAppDto.Size)
                .MinScore(searchOptimizeAppDto.MinimumScore)
                .TrackTotalHits(track)
                .Query(q => q.FunctionScore(fs => fs
                    .Query(query)
                    .Functions(f =>
                        f.RandomScore(r =>
                            r.Seed(randomizationSeed).Field(
                                f =>
                                    f.ProductInfo.Id)))
                ))
            );
        }
        else
        {
            response = await elasticClient.SearchAsync<ElasticSearchSearchEvent>(s => s
                .Index("merchants-products-search-alias")
                .From(searchOptimizeAppDto.Page * searchOptimizeAppDto.Size)
                .Size(searchOptimizeAppDto.Size)
                .MinScore(searchOptimizeAppDto.MinimumScore)
                .TrackTotalHits(track)
                .Query(q => q.FunctionScore(fs => fs.Query(query)))
            );
        }

        // Map the results to DTOs
        var products = response.Documents.Select(doc => new ElasticSearchResponseProductDto
        {
            Id = doc.ProductInfo.InternalProductId ?? string.Empty,
            Name = doc.ProductInfo.Name ?? string.Empty,
            MerchantName = doc.MerchantInfo.Name ?? "n/a",
            MerchantId = doc.MerchantInfo.Id,
            Description = doc.ProductInfo.Description ?? string.Empty,
            RegularPrice = doc.ProductInfo.RegularPrice,
            SalePrice = doc.ProductInfo.Price ?? 0,
            ImageUrl = doc.ProductInfo.Images?.FirstOrDefault() ?? string.Empty,
        }).ToList();

        return new ElasticSearchResponseDto
        {
            Products = products,
            Total = response.Total,
            Took = response.Took
        };
    }

    /// <summary>
    /// Builds the multi-match query for product search.
    /// </summary>
    private MultiMatchQuery BuildProductMultiMatch(SearchOptimizeAppDto dto, int partnerId)
    {
        bool useFuzziness = false;
        bool usePhrase = false;
        var useEnglishFields =  partnerId == 87317;

        var query = new MultiMatchQuery
        {
            Fields = new Field[]
            {
                new($"Product.Name{(useEnglishFields ? ".en" : "")}", dto.ProductNameBoost),
                new($"Product.Description{(useEnglishFields ? ".en" : "")}", dto.ProductDescriptionBoost),
                new($"Product.Categories{(useEnglishFields ? ".en" : "")}", dto.CategoryBoost)
            },
            MinimumShouldMatch = dto.MinimumMatchPercentage + "%",
            Query = dto.Search,
            Boost = dto.Bm25TextSearchBoost,
            Fuzziness = useFuzziness ? new Fuzziness("AUTO") : null,
            Type = usePhrase ? TextQueryType.Phrase : null
        };

        return query;
    }

    /// <summary>
    /// Builds the negative tags query for product search.
    /// </summary>
    private MultiMatchQuery BuildNegativeTagsQuery(SearchOptimizeAppDto searchOptimizeAppDto)
    {
        return new MultiMatchQuery
        {
            Type = TextQueryType.CrossFields,
            Fields = new Field[]
            {
                new("Product.Name"),
                new("Product.Description"),
                new("Product.Categories")
            },
            Query = searchOptimizeAppDto.NegativeTags
        };
    }

    /// <summary>
    /// Builds the common filters for product search.
    /// </summary>
    private List<Query> BuildCommonFilters(SearchOptimizeAppDto searchOptimizeAppDto, int partnerId)
    {
        return new List<Query>
        {
            new NumberRangeQuery("Product.Regular_price")
            {
                Gt = StaticVariables.MinimumPriceForShowingProduct
            },
            new TermQuery("Partner.Id")
            {
                Value = partnerId.ToString()
            }
        };
    }

    /// <summary>
    /// Processes the Elasticsearch response and returns a paginated product response.
    /// </summary>
    /// <param name="response">The Elasticsearch response containing the search results.</param>
    /// <param name="searchOptimizeAppDto">The search optimization DTO with filter criteria.</param>
    /// <param name="size">The number of products per page.</param>
    /// <param name="page">The page number to retrieve.</param>
    /// <param name="email">The email of the user requesting the products.</param>
    /// <param name="startTime">The start time for performance tracking.</param>
    /// <returns>A <see cref="ProductPaginationResponse"/> containing the paginated products.</returns>
    public async Task<ProductPaginationResponse> ProcessElasticSearchResponse(
        ElasticSearchResponseDto response,
        SearchOptimizeAppDto searchOptimizeAppDto,
        ProductFilterDto productFilter,
        int size,
        int page,
        string email,
        DateTime startTime)
    {
        // Initialize the response object with pagination details
        var productResponse = new ProductPaginationResponse
        {
            Data = new ProductPaginationDto
            {
                Products = [],
                LastPage = response.Total / size
            }
        };

        // Log the search event asynchronously (optional: inject logger or event publisher if needed)
        
        _ = AddLogSearchEventAsync(new ElasticShopLog
        {
            Search = new ElasticShopLogSearch
            {
                Id = productFilter.SearchGuid ?? "n/a",
                Hits = response.Products.Count,
                Page = page + 1,
                Query = searchOptimizeAppDto.Search,
                Query_negative = searchOptimizeAppDto.NegativeTags,
                Merchant_filter_ids = searchOptimizeAppDto.MerchantsToFilter.Split(',').ToList(),
                Total_hits = response.Total
            },
            Product = new ElasticShopLogProduct
            {
                Internal_product_ids = response.Products.Select(a => a.Id).ToList()
            },
            Event = new ElasticShopLogEvent
            {
                Created = startTime,
                Duration = (long) (DateTime.UtcNow - startTime).TotalMilliseconds,
                Duration_raw = response.Took
            }
        });

        // Extract product information from the Elasticsearch response
        var products = response.Products;
        var internalProductIds = products.Select(p => p.Id).ToList();
        var favoriteStatuses = await _merchantService.CheckFavoriteMultiple(internalProductIds, email);
        var favoriteSet = new HashSet<string>(favoriteStatuses);

        // Populate the response with product details
        foreach (var product in products)
        {
            productResponse.Data.Products.Add(new ProductRefDto
            {
                Id = product.Id,
                Name = product.Name,
                MerchantName = product.MerchantName,
                MerchantId = product.MerchantId,
                ImageSrc = product.ImageUrl,
                BestSeller = false,
                Favored = favoriteSet.Contains(product.Id),
                IncludesVariants = false,
                NormalPrice = product.RegularPrice,
                SalePrice = product.SalePrice ?? 0,
                OnSale = product.RegularPrice != product.SalePrice && product.SalePrice != null
            });
        }

        return productResponse;
    }
    
    private async Task AddLogSearchEventAsync(ElasticShopLog elasticShopLog)
    {
        try
        {
            using (var publishChannel = _rabbitConnectionCloud.CreateModel())
            {
                var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(elasticShopLog));
                publishChannel.BasicPublish(exchange: "customer",
                    routingKey: "customer_search",
                    basicProperties: null,
                    body: actionBody);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(
                ex,
                "{event} sending to {component} exchange '{exchange}' with routing key '{routingKey}' Discount Event object: {discountEvent}",
                "Failed", "RabbitMQ", "customer", "customer_discount_event",
                JsonSerializer.Serialize(elasticShopLog));
        }

        /*_logger.ForContext("service_name", GetType().Name).Information(
            "Successfully added a new Event: {event} from Customer Email: {Email}",
            discountEventDto.EventType, discountEventDto.CustomerEmail);*/
    }
} 