{"en": [{"Id": 1, "Name": "Electronics", "Src": "https://placehold.co/600x400?text=Electronics", "FkParentId": null, "ProductIds": ["P1001", "P1002", "P1003", "P1004", "P1005", "P1006", "P1007", "P1008", "P1009", "P1010", "P1011", "P1012", "P1013", "P1014", "P1015", "P1016", "P1017", "P1018", "P1019", "P1020"], "Subcategories": [{"Id": 101, "Name": "Headphones", "Src": "https://placehold.co/600x400?text=Headphones", "FkParentId": 1, "ProductIds": ["P1021", "P1022", "P1023", "P1024", "P1025", "P1026", "P1027", "P1028", "P1029", "P1030", "P1031", "P1032", "P1033", "P1034", "P1035", "P1036", "P1037", "P1038", "P1039", "P1040"], "Subcategories": []}, {"Id": 102, "Name": "Speakers", "Src": "https://placehold.co/600x400?text=Speakers", "FkParentId": 1, "ProductIds": ["P1041", "P1042", "P1043", "P1044", "P1045", "P1046", "P1047", "P1048", "P1049", "P1050", "P1001", "P1002", "P1003", "P1004", "P1005", "P1006", "P1007", "P1008", "P1009", "P1010"], "Subcategories": []}, {"Id": 103, "Name": "Wearables", "Src": "https://placehold.co/600x400?text=Wearables", "FkParentId": 1, "ProductIds": ["P1011", "P1012", "P1013", "P1014", "P1015", "P1016", "P1017", "P1018", "P1019", "P1020", "P1021", "P1022", "P1023", "P1024", "P1025", "P1026", "P1027", "P1028", "P1029", "P1030"], "Subcategories": []}]}, {"Id": 2, "Name": "Home & Kitchen", "Src": "https://placehold.co/600x400?text=Home+%26+Kitchen", "FkParentId": null, "ProductIds": ["P1031", "P1032", "P1033", "P1034", "P1035", "P1036", "P1037", "P1038", "P1039", "P1040", "P1041", "P1042", "P1043", "P1044", "P1045", "P1046", "P1047", "P1048", "P1049", "P1050"], "Subcategories": [{"Id": 201, "Name": "Cookware", "Src": "https://placehold.co/600x400?text=Cookware", "FkParentId": 2, "ProductIds": ["P1001", "P1002", "P1003", "P1004", "P1005", "P1006", "P1007", "P1008", "P1009", "P1010", "P1011", "P1012", "P1013", "P1014", "P1015", "P1016", "P1017", "P1018", "P1019", "P1020"], "Subcategories": []}, {"Id": 202, "Name": "Appliances", "Src": "https://placehold.co/600x400?text=Appliances", "FkParentId": 2, "ProductIds": ["P1021", "P1022", "P1023", "P1024", "P1025", "P1026", "P1027", "P1028", "P1029", "P1030", "P1031", "P1032", "P1033", "P1034", "P1035", "P1036", "P1037", "P1038", "P1039", "P1040"], "Subcategories": []}, {"Id": 203, "Name": "Decor", "Src": "https://placehold.co/600x400?text=Decor", "FkParentId": 2, "ProductIds": ["P1041", "P1042", "P1043", "P1044", "P1045", "P1046", "P1047", "P1048", "P1049", "P1050", "P1001", "P1002", "P1003", "P1004", "P1005", "P1006", "P1007", "P1008", "P1009", "P1010"], "Subcategories": []}]}, {"Id": 3, "Name": "Sports & Outdoors", "Src": "https://placehold.co/600x400?text=Sports+%26+Outdoors", "FkParentId": null, "ProductIds": ["P1001", "P1002", "P1003", "P1004", "P1005", "P1006", "P1007", "P1008", "P1009", "P1010", "P1011", "P1012", "P1013", "P1014", "P1015", "P1016", "P1017", "P1018", "P1019", "P1020"], "Subcategories": [{"Id": 301, "Name": "Cycling", "Src": "https://placehold.co/600x400?text=Cycling", "FkParentId": 3, "ProductIds": ["P1021", "P1022", "P1023", "P1024", "P1025", "P1026", "P1027", "P1028", "P1029", "P1030", "P1031", "P1032", "P1033", "P1034", "P1035", "P1036", "P1037", "P1038", "P1039", "P1040"], "Subcategories": []}, {"Id": 302, "Name": "Fitness", "Src": "https://placehold.co/600x400?text=Fitness", "FkParentId": 3, "ProductIds": ["P1041", "P1042", "P1043", "P1044", "P1045", "P1046", "P1047", "P1048", "P1049", "P1050", "P1001", "P1002", "P1003", "P1004", "P1005", "P1006", "P1007", "P1008", "P1009", "P1010"], "Subcategories": []}, {"Id": 303, "Name": "Camping", "Src": "https://placehold.co/600x400?text=Camping", "FkParentId": 3, "ProductIds": ["P1011", "P1012", "P1013", "P1014", "P1015", "P1016", "P1017", "P1018", "P1019", "P1020", "P1021", "P1022", "P1023", "P1024", "P1025", "P1026", "P1027", "P1028", "P1029", "P1030"], "Subcategories": []}]}, {"Id": 4, "Name": "Fashion", "Src": "https://placehold.co/600x400?text=Fashion", "FkParentId": null, "ProductIds": ["P1031", "P1032", "P1033", "P1034", "P1035", "P1036", "P1037", "P1038", "P1039", "P1040", "P1041", "P1042", "P1043", "P1044", "P1045", "P1046", "P1047", "P1048", "P1049", "P1050"], "Subcategories": [{"Id": 401, "Name": "Men's Clothing", "Src": "https://placehold.co/600x400?text=Men's+Clothing", "FkParentId": 4, "ProductIds": ["P1001", "P1002", "P1003", "P1004", "P1005", "P1006", "P1007", "P1008", "P1009", "P1010", "P1011", "P1012", "P1013", "P1014", "P1015", "P1016", "P1017", "P1018", "P1019", "P1020"], "Subcategories": []}, {"Id": 402, "Name": "Women's Clothing", "Src": "https://placehold.co/600x400?text=Women's+Clothing", "FkParentId": 4, "ProductIds": ["P1021", "P1022", "P1023", "P1024", "P1025", "P1026", "P1027", "P1028", "P1029", "P1030", "P1031", "P1032", "P1033", "P1034", "P1035", "P1036", "P1037", "P1038", "P1039", "P1040"], "Subcategories": []}, {"Id": 403, "Name": "Accessories", "Src": "https://placehold.co/600x400?text=Accessories", "FkParentId": 4, "ProductIds": ["P1041", "P1042", "P1043", "P1044", "P1045", "P1046", "P1047", "P1048", "P1049", "P1050", "P1001", "P1002", "P1003", "P1004", "P1005", "P1006", "P1007", "P1008", "P1009", "P1010"], "Subcategories": []}]}, {"Id": 5, "Name": "Books & Media", "Src": "https://placehold.co/600x400?text=Books+%26+Media", "FkParentId": null, "ProductIds": ["P1001", "P1002", "P1003", "P1004", "P1005", "P1006", "P1007", "P1008", "P1009", "P1010", "P1011", "P1012", "P1013", "P1014", "P1015", "P1016", "P1017", "P1018", "P1019", "P1020"], "Subcategories": [{"Id": 501, "Name": "Fiction", "Src": "https://placehold.co/600x400?text=Fiction", "FkParentId": 5, "ProductIds": ["P1021", "P1022", "P1023", "P1024", "P1025", "P1026", "P1027", "P1028", "P1029", "P1030", "P1031", "P1032", "P1033", "P1034", "P1035", "P1036", "P1037", "P1038", "P1039", "P1040"], "Subcategories": []}, {"Id": 502, "Name": "Non-Fiction", "Src": "https://placehold.co/600x400?text=Non-Fiction", "FkParentId": 5, "ProductIds": ["P1041", "P1042", "P1043", "P1044", "P1045", "P1046", "P1047", "P1048", "P1049", "P1050", "P1001", "P1002", "P1003", "P1004", "P1005", "P1006", "P1007", "P1008", "P1009", "P1010"], "Subcategories": []}, {"Id": 503, "Name": "Magazines", "Src": "https://placehold.co/600x400?text=Magazines", "FkParentId": 5, "ProductIds": ["P1011", "P1012", "P1013", "P1014", "P1015", "P1016", "P1017", "P1018", "P1019", "P1020", "P1021", "P1022", "P1023", "P1024", "P1025", "P1026", "P1027", "P1028", "P1029", "P1030"], "Subcategories": []}]}]}