using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Dto.Shop.MerchantGenders;
using Shop_Services.Services.ShopInternal;
using ILogger = Serilog.ILogger;

namespace Shop_Services.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class ShopMerchantGendersController : ControllerBase
{
    private readonly ILogger _logger;
    private readonly IShopInternalService _shopInternalService;

    public ShopMerchantGendersController(ILogger logger, IShopInternalService shopInternalService)
    {
        _logger = logger;
        _shopInternalService = shopInternalService;
        _shopInternalService = shopInternalService;
    }

    [HttpGet]
    public async Task<IActionResult> GetMerchantGendersAsync()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetCategoryMerchantRelsAsync"))
            {
                _logger.ForContext("service_name", GetType().Name).Information("GetCategoryMerchantRelsAsync");

                var merchantGendersAsync = await _shopInternalService.GetMerchantGendersAsync()
                    .ConfigureAwait(false);
                return Ok(merchantGendersAsync);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving In-App Shop Category Merchant Relations");
            return BadRequest();
        }
    }

    [HttpPut]
    public async Task<IActionResult> UpdateMerchantGendersAsync(MerchantGenderDto merchantGenderDto)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateCategoryMerchantAsync"))
            {
                _logger.ForContext("service_name", GetType().Name).Information("UpdateCategoryMerchantAsync");

                await _shopInternalService.UpdateMerchantGendersAsync(merchantGenderDto)
                    .ConfigureAwait(false);
                return Ok();
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while updating In-App Shop Category Merchant Relations");
            return BadRequest();
        }
    }
}