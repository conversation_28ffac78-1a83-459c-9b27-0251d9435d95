using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Services.Partner;
using Shop_Services.Models;
using ILogger = Serilog.ILogger;

namespace Shop_Services.Controllers.Shop;

[Authorize(Roles = "valyrion")]
[Route("shop/v1")]
[ApiController]
public class SectionController(ILogger logger, Services.Shop.IShopService shopService, IPartnerContext partnerContext) : ControllerBase
{
    [HttpGet]
    [Route("sections")]
    [ProducesResponseType(typeof(ShopPaginationDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [AllowAnonymous]
    public async Task<IActionResult> GetSectionsAsync()
    {
        try
        {
            if (partnerContext.IsFoundByDefault)
            {
                return Unauthorized();
            }

            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetSectionsAsync"))
            {
                var success = await shopService.GetSectionsAsync()
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving ViaAds - Sections");
            return BadRequest();
        }
    }
}