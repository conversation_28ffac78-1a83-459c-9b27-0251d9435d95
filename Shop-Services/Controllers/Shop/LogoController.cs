using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Services.Partner;
using Shop_Services.Models;
using ILogger = Serilog.ILogger;

namespace Shop_Services.Controllers.Shop;

[Authorize(Roles = "valyrion")]
[Route("shop/v1/logos")]
[ApiController]
public class LogoController(ILogger logger, Services.Shop.IShopService shopService, IPartnerContext partnerContext) : ControllerBase
{
    [HttpGet]
    [Route("")]
    [ProducesResponseType(typeof(ShopPaginationDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [AllowAnonymous]
    public async Task<IActionResult> GetLogosAsync()
    {
        try
        {
            if (partnerContext.IsFoundByDefault)
            {
                return Unauthorized();
            }

            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetLogosAsync"))
            {
                var success = await shopService.GetLogosAsync()
                    .ConfigureAwait(false);
                if (success.Count == 0)
                {
                    return NoContent();
                }

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving ViaAds - Logos");
            return BadRequest();
        }
    }
    
    [HttpGet]
    [Route("{languageCode}")]
    [ProducesResponseType(typeof(ShopPaginationDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [AllowAnonymous]
    public async Task<IActionResult> GetBannersByLanguageAsync(string languageCode)
    {
        try
        {
            if (partnerContext.IsFoundByDefault)
            {
                return Unauthorized();
            }

            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetProductsByEmailAndCategoryAsync"))
            {
                var success = await shopService.GetBannersAsync(languageCode)
                    .ConfigureAwait(false);
                if (success.Count == 0)
                {
                    return NoContent();
                }

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving ViaAds - Banners for language: {LanguageCode}", languageCode);
            return BadRequest();
        }
    }
}