using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Services.Partner;
using Shop_Services.Models;
using ILogger = Serilog.ILogger;

namespace Shop_Services.Controllers.Shop.Banner;

[Authorize(Roles = "valyrion")]
[Route("shop/v1/banners")]
[ApiController]
public class BannerV1Controller(ILogger logger, Services.Shop.IShopService shopService, IPartnerContext partnerContext) : ControllerBase
{
    [HttpGet]
    [ProducesResponseType(typeof(ShopPaginationDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [AllowAnonymous]
    public async Task<IActionResult> GetBannersByLanguageAsync([FromQuery] string? languageCode)
    {
        try
        {
            if (partnerContext.IsFoundByDefault)
            {
                return Unauthorized();
            }

            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetBannersByLanguageAsync"))
            {
                var success = await shopService.GetBannersAsync(languageCode)
                    .ConfigureAwait(false);
                if (success.Count == 0)
                {
                    return NoContent();
                }

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving ViaAds - Banners for language: {LanguageCode}", languageCode);
            return BadRequest();
        }
    }
}