using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shop_Services.Models;
using ILogger = Serilog.ILogger;

namespace Shop_Services.Controllers.Shop.Banner;

[Authorize(Roles = "valyrion")]
[Route("shop/v2/banners")]
[ApiController]
public class BannerV2Controller(ILogger logger, Services.Shop.IShopService shopService) : ControllerBase
{
    private readonly string _apiKey = "c9cdcc05-c54a-4172-a47d-32476aaa3cf6";
    private readonly string _apiKeyDev = "084af50a-cd6e-4a57-852c-3a1b9493ee87";

    [HttpGet]
    [ProducesResponseType(typeof(ShopPaginationDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [AllowAnonymous]
    public async Task<IActionResult> GetBannersByLanguageAsync([FromQuery] string? languageCode)
    {
        try
        {
            if (!ValidateHeader())
            {
                return Unauthorized();
            }

            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetBannersByLanguageAsync"))
            {
                var success = await shopService.GetBannersAsync(languageCode)
                    .ConfigureAwait(false);
                if (success.Count == 0)
                {
                    return NoContent();
                }

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving ViaAds - Banners for language: {LanguageCode}", languageCode);
            return BadRequest();
        }
    }

    private bool ValidateHeader()
    {
        return Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey").Value == _apiKey ||
               Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey").Value == _apiKeyDev;
    }
}