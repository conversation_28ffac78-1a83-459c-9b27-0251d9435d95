using System.Text.Json;
using Azure;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Controllers.ControllerExtensions.ActionFilters;
using Shared.Dto.Shop;
using Shared.Dto.Shop.Product;
using Shared.Services.Partner;
using Shop_Services.Models;
using ILogger = Serilog.ILogger;

namespace Shop_Services.Controllers.Shop;

[Authorize(Roles = "valyrion")]
[Route("shop/v1")]
[ApiController]
public class ProductController(ILogger logger, Services.Shop.IShopService shopService, IPartnerContext partnerContext) : ControllerBase
{
    [HttpGet]
    [AllowAnonymous]
    [Route("products/{email}/{size}/{page}")]
    [ProducesResponseType(typeof(ShopPaginationDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    public async Task<IActionResult> GetProductsByEmailAndCategoryAsync(string email, int size = 25,
        int page = 1, [FromQuery] ProductFilterDto? productFilterDto = null)
    {
        try
        {
            if (partnerContext.IsFoundByDefault)
            {
                return Unauthorized();
            }

            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetProductsByEmailAndCategoryAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information(
                    "GetProductsByEmailAndCategoryAsync customer: {CustomerEmail} category: {Filters} requested page: {Page} size: {Size}",
                    email, JsonSerializer.Serialize(productFilterDto),
                    page, size);
                var success = await shopService.GetProductsByEmailAndFiltersAsync(email, size, page, productFilterDto)
                    .ConfigureAwait(false);
                if (success.Error != null)
                {
                    return BadRequest(success.Error);
                }

                // Log how many products are returned, including the total count, the page number, size and the filters and the Last Page
                logger.ForContext("service_name", GetType().Name).Warning("Products returned: {Count} for page: {Page} and size: {Size}, filters: {Filters}, lastPage: {LastPage}, email: {Email}", success.Data.Products.Count, page, size, JsonSerializer.Serialize(productFilterDto), success.Data.LastPage, email);

                return Ok(success.Data);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving ViaAds - Products for customer: {Email} page: {Page} size: {Size} filters: {Filters}",
                email, page, size, JsonSerializer.Serialize(productFilterDto));
            return BadRequest();
        }
    }

    [HttpGet]
    [Route("products/{productId}/{email}")]
    [ProducesResponseType(typeof(ProductSingleRefResponse), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [ProducesResponseType(typeof(ShopErrorDto), 404)]
    [AllowAnonymous]
    public async Task<IActionResult> GetProductByProductIdAndEmailAsync(string productId, string email)
    {
        try
        {
            if (partnerContext.IsFoundByDefault)
            {
                return Unauthorized();
            }

            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetProductByProductIdAndEmailAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information(
                    "GetProductByProductIdAndEmailAsync productId: {productId} customer: {CustomerEmail}",
                    productId, email);
                var success = await shopService.GetProductByProductIdAndEmailAsync(productId, email)
                    .ConfigureAwait(false);
                if (success.Error is {Status: 404})
                {
                    var errorResponse = new ObjectResult(success.Error)
                    {
                        StatusCode = 404
                    };

                    return errorResponse;
                }

                return Ok(success.Data);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving ViaAds - Product with productId: {ProductId} for customer: {Email}", productId,
                email);
            return BadRequest();
        }
    }

    [HttpPut]
    [ProducesResponseType(typeof(Response), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [AllowAnonymous]
    [Route("products/toggle-favorite")]
    public async Task<IActionResult> ToggleFavoriteAsync(ShopFavoriteRequestDto favoriteRequest)
    {
        try
        {
            if (partnerContext.IsFoundByDefault)
            {
                return Unauthorized();
            }

            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ToggleFavoriteAsync"))
            {
                var response = await shopService.ToggleFavoriteAsync(favoriteRequest).ConfigureAwait(false);
                if (response.Success)
                    return Ok(response);
                return BadRequest(response);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error toggling favorite for ProductId: {ProductId}, for customer: {CustomerEmail}",
                favoriteRequest.ProductId, favoriteRequest.Email);
            return BadRequest($"Error trying to toggle favorite for product: {favoriteRequest.ProductId}");
        }
    }

    [HttpGet]
    [Route("categories/{email}/{parentId?}")]
    [ProducesResponseType(typeof(ShopPaginationDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [AllowAnonymous]
    public async Task<IActionResult> GetCategoriesAndSubCategoriesAsync(string email, int? parentId = null)
    {
        try
        {
            if (partnerContext.IsFoundByDefault)
            {
                return Unauthorized();
            }

            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetCategoriesAndSubCategoriesAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information(
                    "GetCategoriesAndSubCategoriesAsync parentId: {ParentId}", parentId);

                if (parentId == null)
                {
                    var successCategory = await shopService.GetCategoriesAsync()
                        .ConfigureAwait(false);
                    return Ok(successCategory);
                }

                var successSubCategory = await shopService.GetCategoriesSubCategoriesAsync(email, (int) parentId)
                    .ConfigureAwait(false);

                if (successSubCategory.Error is {Status: 404})
                {
                    var errorResponse = new ObjectResult(successSubCategory.Error)
                    {
                        StatusCode = 404
                    };

                    return errorResponse;
                }


                return Ok(successSubCategory.Data);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving ViaAds - Categories with parentId {ParentId}", parentId);
            return BadRequest();
        }
    }

    [HttpPost]
    [ProducesResponseType(typeof(Response), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [AllowAnonymous]
    [Route("event")]
    public async Task<IActionResult> AddShopEventsAsync(ShopEventDto shopEventDto)
    {
        try
        {
            if (partnerContext.IsFoundByDefault)
            {
                return Unauthorized();
            }

            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddShopEventsAsync"))
            {
                var response = await shopService.AddShopEventAsync(shopEventDto).ConfigureAwait(false);
                if (response.Success)
                    return Ok(response);
                return BadRequest(response);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error adding Shop Event: {ShopEvent}, for ProductId: {ProductId}, for customer: {CustomerEmail}",
                shopEventDto.EventType, shopEventDto.ProductId, shopEventDto.CustomerEmail);
            return BadRequest($"Error trying to consume event with type: {shopEventDto.EventType}");
        }
    }
}