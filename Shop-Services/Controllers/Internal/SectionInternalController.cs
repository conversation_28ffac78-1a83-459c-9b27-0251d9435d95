using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Dto.Shop.Sections.Internal;
using Shop_Services.Services.ShopInternal;
using ILogger = Serilog.ILogger;

namespace Shop_Services.Controllers.Internal;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class SectionInternalController(ILogger logger, IShopInternalService shopInternalService) : ControllerBase
{
    [HttpGet]
    public async Task<IActionResult> GetSectionsAsync()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetSectionsAsync"))
            {
                var success = await shopInternalService.GetSectionsAsync()
                    .ConfigureAwait(false);

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving In-App Shop sections");
            return BadRequest();
        }
    }

    [HttpGet]
    [Route("{sectionId:int}")]
    public async Task<IActionResult> GetSectionAsync(int sectionId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetSectionAsync"))
            {
                var success = await shopInternalService.GetSectionAsync(sectionId);
                if (success == null)
                {
                    return NotFound();
                }
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                $"Error while retrieving In-App section with Id: {sectionId}");
            return BadRequest();
        }
    }

    [HttpPut]
    public async Task<IActionResult> UpdateSectionAsync(SectionInternalDto section)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateSectionAsync"))
            {
                var success = await shopInternalService.UpdateSectionAsync(section)
                    .ConfigureAwait(false);

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                $"Error while updating In-App section with Id: {section.Id}");
            return BadRequest();
        }
    }

    [HttpPost]
    public async Task<IActionResult> CreateSectionAsync(SectionInternalDto section)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CreateSectionAsync"))
            {
                var success = await shopInternalService.CreateSectionsAsync(section)
                    .ConfigureAwait(false);

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                $"Error while creating In-App section with name: {section.Name}");
            return BadRequest();
        }
    }

    [HttpDelete]
    [Route("{sectionId:int}")]
    public async Task<IActionResult> DeleteSectionAsync(int sectionId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "DeleteSectionAsync"))
            {
                var success = await shopInternalService.DeleteSectionAsync(sectionId)
                    .ConfigureAwait(false);

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                $"Error while deleting In-App section with id: {sectionId}");
            return BadRequest();
        }
    }
}