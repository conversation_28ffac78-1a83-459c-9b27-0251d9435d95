using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shop_Services.Models.ModelsDal.Shop;
using Shop_Services.Models.ModelsDto;
using Shop_Services.Services.ShopInternal;
using ILogger = Serilog.ILogger;

namespace Shop_Services.Controllers.Internal;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class BannerInternalController(ILogger logger, IShopInternalService shopInternalService) : ControllerBase
{
    [HttpGet]
    public async Task<IActionResult> GetBannersAsync()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetBannersAsync"))
            {
                var success = await shopInternalService.GetBannersAsync()
                    .ConfigureAwait(false);

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving In-App Shop banners");
            return BadRequest();
        }
    }

    [HttpPut]
    public async Task<IActionResult> UpdateBannerAsync(BannerDto banner)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateBannerAsync"))
            {
                var success = await shopInternalService.UpdateBannerAsync(banner)
                    .ConfigureAwait(false);

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                $"Error while updating In-App banner with Id: {banner.Id}");
            return BadRequest();
        }
    }

    [HttpPost]
    public async Task<IActionResult> CreateBannerAsync(BannerDto banner)
    {
        try
        {
            
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CreateBannerAsync"))
            {
                var success = await shopInternalService.CreateBannerAsync(banner)
                    .ConfigureAwait(false);

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                $"Error while creating In-App banner with redirect: {banner.Redirect}");
            return BadRequest();
        }
    }

    [HttpDelete]
    [Route("{bannerId:int}")]
    public async Task<IActionResult> DeleteBannerAsync(int bannerId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "DeleteBannerAsync"))
            {
                var success = await shopInternalService.DeleteBannerAsync(bannerId)
                    .ConfigureAwait(false);

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                $"Error while deleting In-App banner with id: {bannerId}");
            return BadRequest();
        }
    }
}