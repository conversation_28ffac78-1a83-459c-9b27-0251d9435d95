using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Dto.Shop.Categories;
using Shop_Services.Services.ShopInternal;
using ILogger = Serilog.ILogger;

namespace Shop_Services.Controllers.Internal;

[Authorize(Roles = "valyrion")]
[Route("shopInternal/categories")]
[ApiController]
public class CategoriesInternalController(ILogger logger, IShopInternalService shopInternalService) : ControllerBase
{
    [HttpGet]
    public async Task<IActionResult> GetCategoriesAsync()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetCategoriesAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information("GetCategoriesAsync");

                var successCategory = await shopInternalService.GetCategoriesInternalAsync()
                    .ConfigureAwait(false);

                return Ok(successCategory);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving In-App Shop Categories");
            return BadRequest();
        }
    }

    [HttpPost]
    public async Task<IActionResult> CreateCategoryAsync(InternalCategoryDto category)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CreateCategoryAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information("CreateCategoryAsync");

                var successCategory = await shopInternalService.CreateCategoryAsync(category)
                    .ConfigureAwait(false);

                return Ok(successCategory);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while creating In-App Shop Category");
            return BadRequest();
        }
    }

    [HttpPut]
    public async Task<IActionResult> UpdateCategoryAsync(InternalCategoryDto category)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateCategoryAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information("UpdateCategoryAsync");

                var successCategory = await shopInternalService.UpdateCategoryAsync(category)
                    .ConfigureAwait(false);

                return Ok(successCategory);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while updating In-App Shop Category with Id: {CategoryId}", category.Id);
            return BadRequest();
        }
    }

    [HttpDelete]
    [Route("{categoryId:int}")]
    public async Task<IActionResult> DeleteCategoryAsync(int categoryId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "DeleteCategoryAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information("DeleteCategoryAsync");

                var successCategory = await shopInternalService.DeleteCategoryAsync(categoryId)
                    .ConfigureAwait(false);

                return Ok(successCategory);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while deleting In-App Shop Category with Id: {CategoryId}", categoryId);
            return BadRequest();
        }
    }


    [HttpGet]
    [Route("merchant/{merchantId:int}")]
    public async Task<IActionResult> GetCategoryMerchantRelsAsync(int merchantId)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetCategoryMerchantRelsAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information("GetCategoryMerchantRelsAsync");

                var successCategory = await shopInternalService
                    .GetCategoryMerchantRelationsByMerchantIdAsync(merchantId)
                    .ConfigureAwait(false);

                return Ok(successCategory);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving In-App Shop Category Merchant Relations");
            return BadRequest();
        }
    }

    [HttpPut]
    [Route("merchant")]
    public async Task<IActionResult> UpdateCategoryMerchantAsync(
        CategoryMerchantRelationDto categoryMerchantRelationDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateCategoryMerchantAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information("UpdateCategoryMerchantAsync");

                var successCategory = await shopInternalService
                    .UpdateCategoryMerchantRelationsAsync(categoryMerchantRelationDto)
                    .ConfigureAwait(false);

                return Ok(successCategory);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while updating In-App Shop Category Merchant Relations");
            return BadRequest();
        }
    }
}