using System.Text.Json;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Dto.Shop.Sections.Internal;
using Shop_Services.Models;
using Shop_Services.Services.ShopInternal;
using ILogger = Serilog.ILogger;

namespace Shop_Services.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class ShopInternalController(ILogger logger, IShopInternalService shopInternalService) : ControllerBase
{
    [HttpPost]
    [Route("searchProducts")]
    public async Task<IActionResult> SearchProductAsync(SearchOptimizeAppDto searchOptimizeAppDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetProductByProductIdAndEmailAsync"))
            {
                var success = await shopInternalService.SearchProductAsync(searchOptimizeAppDto)
                    .ConfigureAwait(false);
                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving ViaAds - Product with query {Query}",
                JsonSerializer.Serialize(searchOptimizeAppDto));
            return BadRequest();
        }
    }


    [HttpGet]
    [Route("sections")]
    public async Task<IActionResult> GetSectionsAsync()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetSectionsAsync"))
            {
                var successSection = await shopInternalService.GetSectionsAsync()
                    .ConfigureAwait(false);

                return Ok(successSection);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving In-App Shop Sections");
            return BadRequest();
        }
    }

    [HttpPut]
    [Route("sections")]
    public async Task<IActionResult> UpdateSegmentsAsync(SectionInternalDto section)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateSegmentsAsync"))
            {
                var successCategory = await shopInternalService.UpdateSectionAsync(section)
                    .ConfigureAwait(false);

                return Ok(successCategory);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving In-App update segments");
            return BadRequest();
        }
    }
    
    [HttpGet]
    [Route("checkImages")]
    [AllowAnonymous]
    public async Task<IActionResult> CheckImagesAsync()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CheckImagesAsync"))
            {
                await shopInternalService.CheckAllImagesExist().ConfigureAwait(false);

                return Ok();
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving In-App check images");
            return BadRequest();
        }
    }
}