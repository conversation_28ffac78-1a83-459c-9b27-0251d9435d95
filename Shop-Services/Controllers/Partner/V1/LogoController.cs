using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Controllers.ControllerExtensions.ActionFilters;
using Shop_Services.Models;
using ILogger = Serilog.ILogger;

namespace Shop_Services.Controllers.Partner.V1;

[Authorize(Roles = "valyrion")]
[Route("v1/logos")]
[ApiController]
public class LogoV1Controller(ILogger logger, Services.Shop.IShopService shopService) : ControllerBase
{
    [HttpGet]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ShopPaginationDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    public async Task<IActionResult> GetLogosAsync()
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetLogosAsync"))
            {
                var success = await shopService.GetLogosAsync()
                    .ConfigureAwait(false);
                if (success.Count == 0)
                {
                    return NoContent();
                }

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving Logos");
            return BadRequest();
        }
    }
    
    [HttpGet]
    [AllowAnonymous]
    [Route("{languageCode}")]
    [ProducesResponseType(typeof(ShopPaginationDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    public async Task<IActionResult> GetLogosByLanguageAsync(string languageCode)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetLogosByLanguageAsync"))
            {
                var success = await shopService.GetBannersAsync(languageCode)
                    .ConfigureAwait(false);
                if (success.Count == 0)
                {
                    return NoContent();
                }

                return Ok(success);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving Logos for language: {LanguageCode}", languageCode);
            return BadRequest();
        }
    }
}