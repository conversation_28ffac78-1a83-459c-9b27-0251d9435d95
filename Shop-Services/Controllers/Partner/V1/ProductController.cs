using System.Runtime.InteropServices.Marshalling;
using System.Text.Json;
using Azure;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Constants;
using Shared.Controllers.ControllerExtensions.ActionFilters;
using Shared.Dto.Shop;
using Shared.Dto.Shop.Product;
using Shared.Models;
using Shop_Services.Models;
using Shop_Services.Models.ModelsDto.Partner.Products;
using Shop_Services.Models.ModelsDto.Query_Parameters;
using Shop_Services.Services.Partner.V1;
using ILogger = Serilog.ILogger;

namespace Shop_Services.Controllers.Partner.V1;

[Authorize(Roles = "valyrion")]
[Route("v1/products")]
[ApiController]
public class ProductsV1Controller(ILogger logger, IProductService productService) : ControllerBase
{
    [HttpGet]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ProductPartnerPaginationDto), 200)]
    [ProducesResponseType(typeof(HttpResponseDto), 400)]
    public async Task<IActionResult> SearchProductsAsync([FromQuery] SearchProductParameters searchProductParameters)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetProductsByEmailAndCategoryAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information(
                    "GetProductsByEmailAndCategoryAsync customer: {CustomerEmail} category: {Filters} requested page: {Page}, size: {Size}, Search Input: {SearchInput}, Search Identifier: {SearchIdentifier}",
                    searchProductParameters.Email, JsonSerializer.Serialize(searchProductParameters),
                    searchProductParameters.Page, searchProductParameters.Size, searchProductParameters.Search, searchProductParameters.SearchIdentifier);
                var result = await productService.SearchProductsAsync(searchProductParameters)
                    .ConfigureAwait(false);

                return result.Response.StatusCode switch
                {
                    Shared.Models.StatusCode.BadRequest => BadRequest(result.Response),
                    Shared.Models.StatusCode.NotFound => NotFound(result.Response),
                    _ => Ok(result.Data)
                };
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving Products for customer: {Email} page: {Page} size: {Size}, Search Input: {SearchInput}, Search Identifier: {SearchIdentifier}",
                searchProductParameters.Email,
                searchProductParameters.Page, searchProductParameters.Size, searchProductParameters.Search, searchProductParameters.SearchIdentifier);
            return BadRequest();
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("recommendation")]
    [ProducesResponseType(typeof(ProductPartnerPaginationDto), 200)]
    [ProducesResponseType(typeof(HttpResponseDto), 400)]
    [ProducesResponseType(typeof(HttpResponseDto), 404)]
    public async Task<IActionResult> GetProductRecommendationsAsync(
        [FromQuery] string email, 
        [FromQuery] string strategy = OfferRecommendationTypes.CuratedProducts, 
        [FromQuery] int size = 10, 
        [FromQuery] int page = 1, 
        [FromQuery(Name = "use_mock")] bool useMock = false)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetProductRecommendationsAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information(
                    "GetProductRecommendationsAsync customer: {CustomerEmail} strategy: {Strategy} page: {Page} size: {Size} useMock: {UseMock}",
                    email, strategy, page, size, useMock);
                
                var result = await productService.GetProductRecommendationsAsync(email, strategy, size, page, useMock)
                    .ConfigureAwait(false);
                
                return result.Response.StatusCode switch
                {
                    Shared.Models.StatusCode.BadRequest => BadRequest(result.Response),
                    Shared.Models.StatusCode.NotFound => NotFound(result.Response),
                    Shared.Models.StatusCode.ServerError => StatusCode(500, result.Response),
                    _ => Ok(result.Data)
                };
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving Product Recommendations for customer: {Email} strategy: {Strategy} page: {Page} size: {Size}",
                email, strategy, page, size);
            return BadRequest();
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("{productId}")]
    [ProducesResponseType(typeof(ProductPartnerDto), 200)]
    [ProducesResponseType(typeof(HttpResponseDto), 400)]
    [ProducesResponseType(typeof(HttpResponseDto), 404)]
    public async Task<IActionResult> GetProductByProductIdAndEmailAsync(string productId, [FromQuery] string email, [FromQuery(Name = "use_mock")] bool useMock = false)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetProductByProductIdAndEmailAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information(
                    "GetProductByProductIdAndEmailAsync productId: {productId} customer: {CustomerEmail}",
                    productId, email);
                
                var result = await productService.GetProductByProductIdAndEmailAsync(productId, email, useMock)
                    .ConfigureAwait(false);
                
                return result.Response.StatusCode switch
                {
                    Shared.Models.StatusCode.BadRequest => BadRequest(result.Response),
                    Shared.Models.StatusCode.NotFound => NotFound(result.Response),
                    _ => Ok(result.Data)
                };
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving Product with productId: {ProductId} for customer: {Email}", productId,
                email);
            return BadRequest();
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("favorites")]
    [ProducesResponseType(typeof(ProductPartnerDto), 200)]
    [ProducesResponseType(typeof(HttpResponseDto), 400)]
    public async Task<IActionResult> GetFavoritesProductsAsync([FromQuery] string email, [FromQuery(Name = "use_mock")] bool useMock = false, [FromQuery(Name = "page")] int page = 1, [FromQuery(Name = "size")] int size = 10)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetFavoritesProductsAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information(
                    "GetFavoritesProductsAsync customer: {CustomerEmail} page: {Page} size: {Size}", email, page, size);
                var result = await productService.GetFavoritesProductsAsync(email, useMock, page, size)
                    .ConfigureAwait(false);

                return result.Response.StatusCode switch
                {
                    Shared.Models.StatusCode.BadRequest => BadRequest(result.Response),
                    Shared.Models.StatusCode.NotFound => NotFound(result.Response),
                    _ => Ok(result.Data)
                };
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving Favorites Products for customer: {Email} page: {Page} size: {Size}", email, page, size);
            return BadRequest();
        }
    }

    // Toggle favorite
    [HttpPut]
    [AllowAnonymous]
    [Route("toggle-favorite")]
    [ProducesResponseType(typeof(HttpResponseDto), 200)]
    [ProducesResponseType(typeof(HttpResponseDto), 400)]
    public async Task<IActionResult> ToggleFavoriteAsync(ShopFavoriteRequestDto favoriteRequest)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ToggleFavoriteAsync"))
            {
                var response = await productService.ToggleFavoriteAsync(favoriteRequest).ConfigureAwait(false);

                if (response.StatusCode == Shared.Models.StatusCode.Ok)
                    return Ok(response);
                return BadRequest(response);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error toggling favorite for ProductId: {ProductId}, for customer: {CustomerEmail}",
                favoriteRequest.ProductId, favoriteRequest.Email);
            return BadRequest($"Error trying to toggle favorite for product: {favoriteRequest.ProductId}");
        }
    }

    // Add Behavior Event
    [HttpPost]
    [AllowAnonymous]
    [Route("event")]
    [ProducesResponseType(typeof(HttpResponseDto), 200)]
    [ProducesResponseType(typeof(HttpResponseDto), 400)]
    public async Task<IActionResult> AddBehaviorEventAsync(ShopEventDto shopEventDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddBehaviorEventAsync"))
            {
                var response = await productService.AddBehaviorEventAsync(shopEventDto).ConfigureAwait(false);

                if (response.StatusCode == Shared.Models.StatusCode.Ok)
                    return Ok(response);
                return BadRequest(response);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error adding Shop Event: {ShopEvent}, for ProductId: {ProductId}, for customer: {CustomerEmail}",
                shopEventDto.EventType, shopEventDto.ProductId, shopEventDto.CustomerEmail);
            return BadRequest($"Error trying to consume event with type: {shopEventDto.EventType}");
        }
    }

    /*[HttpPut]
    [AllowAnonymous]
    [ProducesResponseType(typeof(Response), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [Route("products/toggle-favorite")]
    public async Task<IActionResult> ToggleFavoriteAsync(ShopFavoriteRequestDto favoriteRequest)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ToggleFavoriteAsync"))
            {
                var response = await shopService.ToggleFavoriteAsync(favoriteRequest).ConfigureAwait(false);
                if (response.Success)
                    return Ok(response);
                return BadRequest(response);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error toggling favorite for ProductId: {ProductId}, for customer: {CustomerEmail}",
                favoriteRequest.ProductId, favoriteRequest.Email);
            return BadRequest($"Error trying to toggle favorite for product: {favoriteRequest.ProductId}");
        }
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("categories/{email}/{parentId?}")]
    [ProducesResponseType(typeof(ShopPaginationDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    public async Task<IActionResult> GetCategoriesAndSubCategoriesAsync(string email, int? parentId = null)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetCategoriesAndSubCategoriesAsync"))
            {
                logger.ForContext("service_name", GetType().Name).Information(
                    "GetCategoriesAndSubCategoriesAsync parentId: {ParentId}", parentId);

                if (parentId == null)
                {
                    var successCategory = await shopService.GetCategoriesAsync()
                        .ConfigureAwait(false);
                    return Ok(successCategory);
                }

                var successSubCategory = await shopService.GetCategoriesSubCategoriesAsync(email, (int) parentId)
                    .ConfigureAwait(false);

                if (successSubCategory.Error is {Status: 404})
                {
                    var errorResponse = new ObjectResult(successSubCategory.Error)
                    {
                        StatusCode = 404
                    };

                    return errorResponse;
                }


                return Ok(successSubCategory.Data);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving ViaAds - Categories with parentId {ParentId}", parentId);
            return BadRequest();
        }
    }

    [HttpPost]
    [AllowAnonymous]
    [ProducesResponseType(typeof(Response), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [Route("event")]
    public async Task<IActionResult> AddShopEventsAsync(ShopEventDto shopEventDto)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "AddShopEventsAsync"))
            {
                var response = await shopService.AddShopEventAsync(shopEventDto).ConfigureAwait(false);
                if (response.Success)
                    return Ok(response);
                return BadRequest(response);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error adding Shop Event: {ShopEvent}, for ProductId: {ProductId}, for customer: {CustomerEmail}",
                shopEventDto.EventType, shopEventDto.ProductId, shopEventDto.CustomerEmail);
            return BadRequest($"Error trying to consume event with type: {shopEventDto.EventType}");
        }
    }*/
}