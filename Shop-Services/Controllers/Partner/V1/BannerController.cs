using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shop_Services.Models;
using ILogger = Serilog.ILogger;
using Shop_Services.Models.ModelsDal.Shop;

namespace Shop_Services.Controllers.Partner.V1;

[Authorize(Roles = "valyrion")]
[Route("v1/banners")]
[ApiController]
public class BannerV1Controller(ILogger logger, Shop_Services.Services.Partner.V1.IBannerService bannerService) : ControllerBase
{
    [HttpGet]
    [ProducesResponseType(typeof(ShopPaginationDto), 200)]
    [ProducesResponseType(typeof(string), 400)]
    [AllowAnonymous]
    public async Task<IActionResult> GetBannersByLanguageAsync([FromQuery] string? languageCode, [FromQuery(Name = "use_mock")] bool useMock = false)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetBannersByLanguageAsync"))
            {
                var banners = await bannerService.GetBannersAsync(languageCode, useMock).ConfigureAwait(false);
                if (banners.Count == 0)
                {
                    return NoContent();
                }
                return Ok(banners);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving ViaAds - Banners for language: {LanguageCode}", languageCode);
            return BadRequest();
        }
    }

    [HttpGet("{bannerId}")]
    [ProducesResponseType(typeof(Banner), 200)]
    [ProducesResponseType(typeof(string), 404)]
    [AllowAnonymous]
    public async Task<IActionResult> GetBannerByIdAsync(int bannerId, [FromQuery(Name = "use_mock")] bool useMock = false)
    {
        try
        {
            using (logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetBannerByIdAsync"))
            {
                var banner = await bannerService.GetBannerByIdAsync(bannerId, useMock).ConfigureAwait(false);
                if (banner == null)
                {
                    return NotFound($"Banner with id {bannerId} not found");
                }
                return Ok(banner);
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name).Error(ex,
                "Error while retrieving ViaAds - Banner with id: {BannerId}", bannerId);
            return BadRequest();
        }
    }
}