using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Shop_Services.Models.ModelsDto.Partner.Categories;
using Shop_Services.Models.ModelsDto.Partner.Products;
using Shop_Services.Services.Partner.V1;
using ILogger = Serilog.ILogger;

namespace Shop_Services.Controllers.Partner.V1;

[Authorize(Roles = "valyrion")]
[Route("v1/categories")]
[ApiController]
public class CategoryController(ILogger logger, ICategoryService categoryService) : ControllerBase
{
    [HttpGet]
    [AllowAnonymous]
    [ProducesResponseType(typeof(List<CategoryDto>), 200)]
    public async Task<IActionResult> GetCategoriesAsync([FromQuery(Name = "parent_id")] int? parentId, [FromQuery(Name = "lang")] string? lang, [FromQuery(Name = "use_mock")] bool useMock = false)
    {
        try
        {
            var categories = await categoryService.GetCategoriesAsync(parentId, lang, useMock);
            if(categories.Count == 0) {
                return NotFound();
            }
            return Ok(categories);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error retrieving categories");
            return BadRequest();
        }
    }

    [HttpGet("{categoryId}/items")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ProductPartnerPaginationResponse), 200)]
    public async Task<IActionResult> GetCategoryItemsAsync(int categoryId, [FromQuery] string email, [FromQuery] int size = 10, [FromQuery] int page = 1, [FromQuery(Name = "search_identifier")] string? searchIdentifier = "", [FromQuery(Name = "use_mock")] bool useMock = false)
    {
        try
        {
            var result = await categoryService.GetCategoryItemsAsync(categoryId, email, size, page, searchIdentifier, useMock);
            return Ok(result);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error retrieving category items for category {CategoryId}", categoryId);
            return BadRequest();
        }
    }
} 