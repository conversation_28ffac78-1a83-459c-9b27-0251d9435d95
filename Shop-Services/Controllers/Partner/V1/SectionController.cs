using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using SerilogTimings.Extensions;
using Shop_Services.Models;
using ILogger = Serilog.ILogger;
using Shop_Services.Models.ModelsDto.Partner.Products;
using Shop_Services.Models.ModelsDto.Partner.Sections;
using Shop_Services.Services.Partner.V1;

namespace Shop_Services.Controllers.Partner.V1;

[Authorize(Roles = "valyrion")]
[Route("v1/sections")]
[ApiController]
public class SectionController : ControllerBase
{
    private readonly ISectionService _sectionService;
    private readonly ILogger<SectionController> _logger;

    public SectionController(ISectionService sectionService, ILogger<SectionController> logger)
    {
        _sectionService = sectionService;
        _logger = logger;
    }

    [HttpGet]
    [AllowAnonymous]
    [ProducesResponseType(typeof(List<SectionDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(List<SectionWithProductsDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetSections(
        [FromQuery] int? items_pr_section,
        [FromQuery] string? lang,
        [FromQuery] bool use_mock = false)
    {
        try
        {
            if (items_pr_section.HasValue && items_pr_section > 0)
            {
                var sectionsWithProducts = await _sectionService.GetSectionsWithProductsAsync(items_pr_section.Value, lang, use_mock);
                return Ok(sectionsWithProducts);
            }
            else
            {
                var sections = await _sectionService.GetSectionsAsync(items_pr_section, lang, use_mock);
                return Ok(sections);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting sections");
            return StatusCode(StatusCodes.Status500InternalServerError);
        }
    }

    [HttpGet("{section_id}/items")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(ProductPartnerPaginationResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> GetSectionItems(
        [FromRoute] int section_id,
        [FromQuery] string email,
        [FromQuery] int size = 10,
        [FromQuery] int page = 1,
        [FromQuery] string? search_identifier = null,
        [FromQuery] bool use_mock = false)
    {
        try
        {
            var response = await _sectionService.GetSectionItemsAsync(section_id, email, size, page, search_identifier, use_mock);
            return Ok(response);
        }
        catch (KeyNotFoundException)
        {
            return NotFound();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting section items for section {SectionId}", section_id);
            return StatusCode(StatusCodes.Status500InternalServerError);
        }
    }
}