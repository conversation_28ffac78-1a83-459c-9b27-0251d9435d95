using System.Collections.Concurrent;
using Marlin_OS_Integration_API.Models.Order;
using Nest;
using Shared.Dto.Campaign;
using Shared.Elastic.ElasticCampaignsMailsClicks;
using Shared.Elastic.Models.ElasticCampaignsMailsClicks;
using Shared.Elastic.Models.ElasticCampaignsMailsOpens;
using Shared.Models.Elastic.ElasticCampaignsMailsOpens;
using Shared.Models.Merchant;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace Campaign_Services.Services.FilterElastic;

public class FilterElasticService : IFilterElasticService
{
    private readonly ILogger _logger;
    private readonly ElasticClient _elasticClient;
    private readonly IMerchantService _merchantService;

    public FilterElasticService(ILogger logger, ElasticClient elasticClient, IMerchantService merchantService)
    {
        _logger = logger;
        _elasticClient = elasticClient;
        _merchantService = merchantService;
    }

    public async Task<HashSet<string>> ClickResponses(int days, string merchantId)
    {
        var merchant = await _merchantService.GetPaymentByIdAsync(Convert.ToInt32(merchantId));

        var toOld = DateTime.UtcNow.AddDays(-45);
        var loop = true;
        var clicks = new List<ElasticCampaignsMailsClicks>();
        var opens = new List<ElasticCampaignsMailsOpens>();
        var index = "campaigns-mails-clicks";
        if (merchant.MerchantPayments.First().RedirectInvoice)
        {
            var pitResponse = await _elasticClient.OpenPointInTimeAsync(index, p => p.KeepAlive("2m"));
            IHit<ElasticCampaignsMailsClicks>? lastHit = null;
            while (loop)
            {
                var search = _elasticClient.Search<ElasticCampaignsMailsClicks>((s => s
                        .Source(sf => sf
                            .Includes(i => i
                                .Fields(
                                    f => f.Event_received,
                                    f => f.Customer.Email,
                                    f => f.Shop_event.Webshop_id
                                )
                            )
                        )
                        .Index(index)
                        .Query(q => q.Match(m => m
                                        .Field(f => f.Shop_event.Webshop_id).Query(merchantId))
                                    && q.Bool(b => b
                                        .Filter(f =>
                                            f.DateRange(dt => dt
                                                .Field(field => field.Event_received)
                                                .GreaterThanOrEquals(toOld))
                                        ))
                        )
                        .Size(10_000)
                        .PointInTime(pitResponse.Id).Sort(srt => srt.Descending(f => f.Event_received))
                        .SearchAfter(lastHit?.Sorts)
                    ));
                if (search.Hits != null && search.Hits.Count != 0)
                {
                    lastHit = search.Hits.Last();
                }
                else
                {
                    loop = false;
                }

                clicks.AddRange(search.Documents);
                if (search.Documents.Count % 10000 != 0)
                {
                    loop = false;
                }
            }
        }

        if (merchant.MerchantPayments.First().DisplayInvoice || merchant.MerchantMeta
                .SingleOrDefault(a => a.FkMerchantMetaTypeName == MerchantMetaTypeNames.AttributionType)?.Value
                .ToLowerInvariant() == "cpm")
        {
            loop = true;
            index = "campaigns-mails-opens";
            var pitResponse = await _elasticClient.OpenPointInTimeAsync(index, p => p.KeepAlive("2m"));
            IHit<ElasticCampaignsMailsOpens>? lastHit = null;
            while (loop)
            {
                var search = _elasticClient.Search<ElasticCampaignsMailsOpens>((s => s
                        .Source(sf => sf
                            .Includes(i => i
                                .Fields(
                                    f => f.timestamp,
                                    f => f.Customer.Email,
                                    f => f.Shop_event.Webshop_id
                                )
                            )
                        )
                        .Index(index)
                        .Query(q => q.Match(m => m
                                        .Field(f => f.Shop_event.Webshop_id).Query(merchantId))
                                    && q.Bool(b => b
                                        .Filter(f =>
                                            f.DateRange(dt => dt
                                                .Field(field => field.timestamp)
                                                .GreaterThanOrEquals(toOld))
                                        ))
                        )
                        .Size(10_000)
                        .PointInTime(pitResponse.Id).Sort(srt => srt.Descending(f => f.timestamp))
                        .SearchAfter(lastHit?.Sorts)
                    ));
                if (search.Hits != null && search.Hits.Count != 0)
                {
                    lastHit = search.Hits.Last();
                }
                else
                {
                    loop = false;
                }

                opens.AddRange(search.Documents);
                if (search.Documents.Count % 10000 != 0)
                {
                    loop = false;
                }
            }
        }

        //remove the events there is to old
        clicks = clicks.Where(a =>
                a.Event_received.AddDays(merchant.MerchantPayments.First().RedirectExposureDays) >=
                DateTime.UtcNow.AddDays(days))
            .ToList();
        opens = opens.Where(a =>
                a.timestamp.AddDays(merchant.MerchantPayments.First().DisplayExposureDays) >=
                DateTime.UtcNow.AddDays(days))
            .ToList();

        var emailFounds = clicks.Select(a => a.Customer.Email?.Trim().ToLowerInvariant()).ToList();
        emailFounds.AddRange(opens.Select(a => a.Customer.Email?.Trim().ToLowerInvariant()));
        emailFounds = emailFounds.Distinct().ToList();
        if (emailFounds.Count == 0)
        {
            emailFounds.Add("<EMAIL>");
        }

        return new HashSet<string>(emailFounds);
    }

    public async Task<HashSet<string>> OrderResponses(int days, List<string> merchantIds)
    {
        var orders = new List<ElasticOrderEvent>();
        var index = "customers-orders-partners";
        var searchResponse = await _elasticClient.SearchAsync<ElasticOrderEvent>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Shop_order,
                        f => f.Shop_order!.Webshop_id,
                        f => f.Customer.Email,
                        f => f.Order_date
                    )
                )
            )
            .Index(index)
            .Query(
                q => q.Terms(t =>
                         t.Field(f => f.Shop_order.Webshop_id).Terms(merchantIds))
                     && q.Bool(b => b
                         .Filter(f =>
                             f.DateRange(dt => dt
                                 .Field(field => field.Order_date)
                                 .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days)))
                         ))
            )
            .Size(10000)
            .Scroll("2m")
        );

        orders.AddRange(searchResponse.Documents);

        while (searchResponse.IsValid && searchResponse.Documents.Count != 0)
        {
            searchResponse = await _elasticClient.ScrollAsync<ElasticOrderEvent>("2m", searchResponse.ScrollId);
            orders.AddRange(searchResponse.Documents);
        }

        await _elasticClient.ClearScrollAsync(cs => cs
            .ScrollId(searchResponse.ScrollId)
        );
        var emailfound = orders.Select(a => a.Customer.Email).Distinct().ToList();

        if (emailfound.Count == 0)
        {
            emailfound.Add("<EMAIL>");
        }

        return new HashSet<string>(emailfound);
    }

    public async Task<HashSet<string>> OrderResponsesGdpr(int days, List<string> merchantIds)
    {
        var orders = new List<ElasticOrderEvent>();
        var index = "customers-orders";
        var searchResponse = await _elasticClient.SearchAsync<ElasticOrderEvent>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Shop_order,
                        f => f.Shop_order!.Webshop_id,
                        f => f.Customer.Email,
                        f => f.Order_date
                    )
                )
            )
            .Index(index)
            .Query(
                q => q.Terms(t =>
                         t.Field(f => f.Shop_order.Webshop_id).Terms(merchantIds))
                     && q.Bool(b => b
                         .Filter(f =>
                             f.DateRange(dt => dt
                                 .Field(field => field.Order_date)
                                 .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days)))
                         ))
            )
            .Size(10000)
            .Scroll("2m")
        );

        orders.AddRange(searchResponse.Documents);

        while (searchResponse.IsValid && searchResponse.Documents.Count != 0)
        {
            searchResponse = await _elasticClient.ScrollAsync<ElasticOrderEvent>("2m", searchResponse.ScrollId);
            orders.AddRange(searchResponse.Documents);
        }

        await _elasticClient.ClearScrollAsync(cs => cs
            .ScrollId(searchResponse.ScrollId)
        );
        var emailfound = orders.Select(a => a.Customer.Email).Distinct().ToList();

        index = "customers-orders-partners";
        searchResponse = await _elasticClient.SearchAsync<ElasticOrderEvent>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Shop_order,
                        f => f.Shop_order!.Webshop_id,
                        f => f.Customer.Email,
                        f => f.Order_date
                    )
                )
            )
            .Index(index)
            .Query(
                q => q.Terms(t =>
                         t.Field(f => f.Shop_order.Webshop_id).Terms(merchantIds))
                     && q.Bool(b => b
                         .Filter(f =>
                             f.DateRange(dt => dt
                                 .Field(field => field.Order_date)
                                 .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days)))
                         ))
            )
            .Size(10000)
            .Scroll("2m")
        );

        orders.AddRange(searchResponse.Documents);

        while (searchResponse.IsValid && searchResponse.Documents.Count != 0)
        {
            searchResponse = await _elasticClient.ScrollAsync<ElasticOrderEvent>("2m", searchResponse.ScrollId);
            orders.AddRange(searchResponse.Documents);
        }

        await _elasticClient.ClearScrollAsync(cs => cs
            .ScrollId(searchResponse.ScrollId)
        );
        emailfound.AddRange(orders.Select(a => a.Customer.Email).Distinct().ToList());
        emailfound = emailfound.Distinct().ToList();

        if (emailfound.Count == 0)
        {
            emailfound.Add("<EMAIL>");
        }

        return new HashSet<string>(emailfound);
    }

    public async Task<HashSet<string>> CampaignMailResponses(int days, int campaignId)
    {
        var orders = new List<ElasticCampaignsMailsOpens>();
        var index = "campaigns-mails-opens";
        var searchResponse = await _elasticClient.SearchAsync<ElasticCampaignsMailsOpens>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Customer.Campaign_Id,
                        f => f.timestamp,
                        f => f.Customer.Email
                    )
                )
            )
            .Index(index)
            .Query(
                q => q.Terms(t =>
                         t.Field(f => f.Customer.Campaign_Id).Terms(campaignId.ToString()))
                     && q.Bool(b => b
                         .Filter(f =>
                             f.DateRange(dt => dt
                                 .Field(field => field.timestamp)
                                 .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days)))
                         ))
            )
            .Size(10000)
            .Scroll("2m")
        );

        orders.AddRange(searchResponse.Documents);

        while (searchResponse.IsValid && searchResponse.Documents.Count != 0)
        {
            searchResponse =
                await _elasticClient.ScrollAsync<ElasticCampaignsMailsOpens>("2m", searchResponse.ScrollId);
            orders.AddRange(searchResponse.Documents);
        }

        await _elasticClient.ClearScrollAsync(cs => cs
            .ScrollId(searchResponse.ScrollId)
        );
        var emailfound = orders.Select(a => a.Customer.Email).Distinct().ToList();

        if (emailfound.Count == 0)
        {
            emailfound.Add("<EMAIL>");
        }

        return new HashSet<string>(emailfound);
    }

    public async Task<HashSet<string>> PersonInterestedIn(int days, string webshopId, bool gdpr)
    {
        var webshop = await _merchantService.GetPaymentByIdAsync(Convert.ToInt32(webshopId));
        if (webshop != null)
        {
            var loop = true;
            var clicks = new List<ElasticCampaignsMailsClicks>();
            var index = "campaigns-mails-clicks";
            if (webshop.MerchantPayments.First().RedirectInvoice == true)
            {
                var pitResponse = await _elasticClient.OpenPointInTimeAsync(index, p => p.KeepAlive("2m"));
                IHit<ElasticCampaignsMailsClicks>? lastHit = null;
                while (loop)
                {
                    var search = _elasticClient.Search<ElasticCampaignsMailsClicks>((s => s
                            .Source(sf => sf
                                .Includes(i => i
                                    .Fields(
                                        f => f.Event_received,
                                        f => f.Customer.Email,
                                        f => f.Shop_event.Webshop_id
                                    )
                                )
                            )
                            .Index(index)
                            .Query(q => q.Match(m => m
                                            .Field(f => f.Shop_event.Webshop_id).Query(webshopId))
                                        && q.Bool(b => b
                                            .Filter(f =>
                                                f.DateRange(dt => dt
                                                    .Field(field => field.Event_received)
                                                    .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days)))
                                            ))
                            )
                            .Size(10_000)
                            .PointInTime(pitResponse.Id).Sort(srt => srt.Descending(f => f.Event_received))
                            .SearchAfter(lastHit?.Sorts)
                        ));
                    if (search.Hits != null && search.Hits.Count != 0)
                    {
                        lastHit = search.Hits.Last();
                    }
                    else
                    {
                        loop = false;
                    }

                    clicks.AddRange(search.Documents);
                    if (search.Documents.Count % 10000 != 0)
                    {
                        loop = false;
                    }
                }
            }

            var orders = new List<ElasticOrderEvent>();
            index = "customers-orders-partners";
            var searchResponse = await _elasticClient.SearchAsync<ElasticOrderEvent>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_order,
                            f => f.Shop_order!.Webshop_id,
                            f => f.Customer.Email,
                            f => f.Order_date
                        )
                    )
                )
                .Index(index)
                .Query(
                    q => q.Terms(t =>
                             t.Field(f => f.Shop_order.Webshop_id).Terms(webshopId))
                         && q.Bool(b => b
                             .Filter(f =>
                                 f.DateRange(dt => dt
                                     .Field(field => field.Order_date)
                                     .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days)))
                             ))
                )
                .Size(10000)
                .Scroll("2m")
            );

            orders.AddRange(searchResponse.Documents);

            while (searchResponse.IsValid && searchResponse.Documents.Count != 0)
            {
                searchResponse = await _elasticClient.ScrollAsync<ElasticOrderEvent>("2m", searchResponse.ScrollId);
                orders.AddRange(searchResponse.Documents);
            }

            await _elasticClient.ClearScrollAsync(cs => cs
                .ScrollId(searchResponse.ScrollId)
            );
            var emailfound = orders.Select(a => a.Customer.Email).Distinct().ToList();

            if (gdpr)
            {
                index = "customers-orders";
                searchResponse = await _elasticClient.SearchAsync<ElasticOrderEvent>(s => s
                    .Source(sf => sf
                        .Includes(i => i
                            .Fields(
                                f => f.Shop_order,
                                f => f.Shop_order!.Webshop_id,
                                f => f.Customer.Email,
                                f => f.Order_date
                            )
                        )
                    )
                    .Index(index)
                    .Query(
                        q => q.Terms(t =>
                                 t.Field(f => f.Shop_order.Webshop_id).Terms(webshopId))
                             && q.Bool(b => b
                                 .Filter(f =>
                                     f.DateRange(dt => dt
                                         .Field(field => field.Order_date)
                                         .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days)))
                                 ))
                    )
                    .Size(10000)
                    .Scroll("2m")
                );

                orders.AddRange(searchResponse.Documents);

                while (searchResponse.IsValid && searchResponse.Documents.Count != 0)
                {
                    searchResponse = await _elasticClient.ScrollAsync<ElasticOrderEvent>("2m", searchResponse.ScrollId);
                    orders.AddRange(searchResponse.Documents);
                }

                await _elasticClient.ClearScrollAsync(cs => cs
                    .ScrollId(searchResponse.ScrollId)
                );
                emailfound.AddRange(orders.Select(a => a.Customer.Email).Distinct().ToList());
            }

            var emailFounds = clicks.Select(a => a.Customer.Email?.Trim().ToLowerInvariant()).Distinct()
                .ToList();
            emailFounds.AddRange(emailfound);
            emailFounds = emailFounds.Distinct().ToList();
            if (emailFounds.Count == 0)
            {
                emailFounds.Add("<EMAIL>");
            }

            return new HashSet<string>(emailFounds);
        }

        return new HashSet<string>(new List<string> {"<EMAIL>"});
    }

    /*public async Task<List<FilterAffinityDto>> OrderResponsesAffinityFilter(int days, string webshopId, bool gdpr)
    {
        //var webShops = await _webshopClient.Merchants();
        var webShops = await _merchantService.GetAllAsync();
        var orders = new List<ElasticOrderEvent>();
        var index = "customers-orders-partners";
        var searchResponse = await _elasticClient.SearchAsync<ElasticOrderEvent>(s => s
            .Source(sf => sf
                .Includes(i => i
                    .Fields(
                        f => f.Shop_order,
                        f => f.Shop_order!.Webshop_id,
                        f => f.Customer.Email,
                        f => f.Order_date
                    )
                )
            )
            .Index(index)
            .Query(
                q => q.Terms(t =>
                         t.Field(f => f.Shop_order.Webshop_id).Terms(webshopId))
                     && q.Bool(b => b
                         .Filter(f =>
                             f.DateRange(dt => dt
                                 .Field(field => field.Order_date)
                                 .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days)))
                         ))
            )
            .Size(10000)
            .Scroll("2m")
        );

        orders.AddRange(searchResponse.Documents);
        while (searchResponse.IsValid && searchResponse.Documents.Count != 0)
        {
            searchResponse = await _elasticClient.ScrollAsync<ElasticOrderEvent>("2m", searchResponse.ScrollId);
            orders.AddRange(searchResponse.Documents);
        }

        await _elasticClient.ClearScrollAsync(cs => cs
            .ScrollId(searchResponse.ScrollId)
        );
        var emailfounds = orders.Select(a => a.Customer.Email).Distinct().ToList();

        if (gdpr)
        {
            index = "customers-orders";
            searchResponse = await _elasticClient.SearchAsync<ElasticOrderEvent>(s => s
                .Source(sf => sf
                    .Includes(i => i
                        .Fields(
                            f => f.Shop_order,
                            f => f.Shop_order!.Webshop_id,
                            f => f.Customer.Email,
                            f => f.Order_date
                        )
                    )
                )
                .Index(index)
                .Query(
                    q => q.Terms(t =>
                             t.Field(f => f.Shop_order.Webshop_id).Terms(webshopId))
                         && q.Bool(b => b
                             .Filter(f =>
                                 f.DateRange(dt => dt
                                     .Field(field => field.Order_date)
                                     .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days)))
                             ))
                )
                .Size(10000)
                .Scroll("2m")
            );
            orders.AddRange(searchResponse.Documents);
            while (searchResponse.IsValid && searchResponse.Documents.Count != 0)
            {
                searchResponse = await _elasticClient.ScrollAsync<ElasticOrderEvent>("2m", searchResponse.ScrollId);
                orders.AddRange(searchResponse.Documents);
            }

            await _elasticClient.ClearScrollAsync(cs => cs
                .ScrollId(searchResponse.ScrollId)
            );
            emailfounds.AddRange(orders.Select(a => a.Customer.Email).Distinct().ToList());
        }

        ConcurrentBag<FilterAffinityDto> affinitys = new ConcurrentBag<FilterAffinityDto>();

        await Parallel.ForEachAsync(webShops,
            new ParallelOptions
                { MaxDegreeOfParallelism = 4 },
            async (webshop, stoppingToken) =>
            {
                var response = await _elasticClient.SearchAsync<ElasticOrderEvent>(s => s
                    .Index("customers-orders-partners")
                    .Size(0)
                    .Query(q =>
                        q.Terms(t =>
                            t.Field(f => f.Shop_order.Webshop_id).Terms(webshop.Id)) &&
                        q.Terms(t =>
                            t.Field(f => f.Customer.Email).Terms(emailfounds)) &&
                        q.Bool(b => b
                            .Filter(f =>
                                f.DateRange(dt => dt
                                    .Field(field => field.Order_date)
                                    .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days)))
                            ))
                    )
                    .Aggregations(a => a
                        .Cardinality("unique_emails", c => c
                            .Field(f => f.Customer.Email)
                        )
                    ), stoppingToken);

                var count = response.Aggregations.Cardinality("unique_emails").Value;

                if (gdpr)
                {
                    response = await _elasticClient.SearchAsync<ElasticOrderEvent>(s => s
                        .Index("customers-orders")
                        .Size(0)
                        .Query(q =>
                            q.Terms(t =>
                                t.Field(f => f.Shop_order.Webshop_id).Terms(webshop.Id)) &&
                            q.Terms(t =>
                                t.Field(f => f.Customer.Email).Terms(emailfounds)) &&
                            q.Bool(b => b
                                .Filter(f =>
                                    f.DateRange(dt => dt
                                        .Field(field => field.Order_date)
                                        .GreaterThanOrEquals(DateTime.UtcNow.AddDays(-days)))
                                ))
                        )
                        .Aggregations(a => a
                            .Cardinality("unique_emails", c => c
                                .Field(f => f.Customer.Email)
                            )
                        ), stoppingToken);

                    count += response.Aggregations.Cardinality("unique_emails").Value;
                }

                affinitys.Add(new FilterAffinityDto
                {
                    WebShopName =
                        webShops.SingleOrDefault(a => a.Id.ToString() == webshop.Id.ToString())
                            ?.Name ?? "n/a",
                    WebShopId = webshop.Id.ToString(),
                    EventsAffinity = (int)count
                });
            });


        foreach (var affinity in affinitys)
        {
            affinity.Percentage = Math.Round(affinity.EventsAffinity / emailfounds.Count * 100, 2);
        }

        var affinitysList = affinitys.ToList();
        affinitysList = affinitysList.OrderByDescending(a => a.Percentage).ToList();
        return affinitysList.Take(20).ToList();
    }*/
}