using Shared.Dto.Campaign;

namespace Campaign_Services.Services.FilterElastic
{
    public interface IFilterElasticService
    {
        Task<HashSet<string>> ClickResponses(int days, string webshopId);
        Task<HashSet<string>> OrderResponses(int days, List<string> merchantIds);
        Task<HashSet<string>> OrderResponsesGdpr(int days, List<string> merchantIds);
        Task<HashSet<string>> CampaignMailResponses(int days, int campaignId);

        Task<HashSet<string>> PersonInterestedIn(int days, string webshopId, bool gdpr);
        //Task<List<FilterAffinityDto>> OrderResponsesAffinityFilter(int days, string webshopId, bool gdpr);
    }
}