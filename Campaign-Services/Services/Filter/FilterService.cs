using Audience.Services.Audience;
using Campaign_Service.Models.ModelsDal.Campaign;
using Campaign_Services.Models.ModelsDal.Campaign;
using Campaign_Services.Services.FilterElastic;
using Customer_Services.Models.ModelsDal.Customer;
using Microsoft.EntityFrameworkCore;
using Shared.Dto.Campaign;
using Shared.Services.Partner;
using Webshop.Webshop;
using ILogger = Serilog.ILogger;

namespace Campaign_Services.Services.Filter;

public class FilterService(
    ILogger logger,
    IFilterElasticService filterElasticService,
    CampaignDbContextTracking campaignDbContext,
    ICustomerService customerService,
    IMerchantService merchantService,
    IPartnerContext partnerContext)
    : IFilterService
{
    public async Task<List<string>> GetAllFilterEmailsAsync(int filterId)
    {
        var filter = campaignDbContext.Filters
            .Include(a => a.FilterValues)
            .Single(a => a.Id == filterId);
        return await GetAllFilterEmailsAsync(filter);
    }

    public async Task<List<string>> GetAllFilterEmailsAsync(Models.ModelsDal.Campaign.Filter filter)
    {
        var contactsAll = await customerService.GetAllConsentAsync();

        //0 only engaged
        //1 only unengaged
        //2 both

        contactsAll = contactsAll.DistinctBy(a => a.Email).ToList();
        switch (filter.OpenRate ?? "")
        {
            case "veryEngaged":
                contactsAll = contactsAll.Where(a => a.OpenRate >= 50).ToList();
                break;
            case "mediumEngaged":
                contactsAll = contactsAll.Where(a => a.OpenRate is >= 33 and < 50).ToList();
                break;
            case "lightlyEngaged":
                contactsAll = contactsAll.Where(a => a.OpenRate is >= 25 and < 33).ToList();
                break;
            case "lowlyEngaged":
                contactsAll = contactsAll.Where(a => a.OpenRate is >= 10 and < 25).ToList();
                break;
            case "unengaged":
                contactsAll = contactsAll.Where(a => a.OpenRate is < 10).ToList();
                break;
        }

        /*if (filter.OnlyUnengaged == 0)
        {
            contactsAll = contactsAll.Where(a => a.MissedOpenMails is < 11).ToList();
        }
        else if (filter.OnlyUnengaged == 1)
        {
            contactsAll = contactsAll.Where(a => a.MissedOpenMails is >= 11).ToList();
        }*/

        var contactsConditionAny = new List<Customer>();
        var contactsConditionAnyEmail = new List<string>();
        var contactsConditionAll = contactsAll;
        var contactsConditionAllEmail = new List<string>();

        var hasPurchasedIn = new List<HashSet<string>>();
        var hasNotPurchasedIn = new List<HashSet<string>>();

        var hasPurchasedAt = new List<HashSet<string>>();
        var hasNotPurchasedAt = new List<HashSet<string>>();

        var hasPurchasedAtGdpr = new List<HashSet<string>>();
        var hasNotPurchasedAtGdpr = new List<HashSet<string>>();

        var hasActiveExposurePeriod = new List<HashSet<string>>();
        var hasNotActiveExposurePeriod = new List<HashSet<string>>();

        var campaignActivityExposed = new List<HashSet<string>>();
        var campaignActivityNotExposed = new List<HashSet<string>>();

        var personInterestedIn = new List<HashSet<string>>();

        var affinity = new List<HashSet<string>>();

        foreach (var filterValue in filter.FilterValues)
        {
            if (filterValue.Active == true)
            {
                switch (filterValue.FkFilterTemplateId)
                {
                    case 1:
                        //Has purchased in
                        if (filterValue.Time != null)
                        {
                            var webshops = await merchantService
                                .GetAllMerchantsByCategoryAsync(Convert.ToInt32(filterValue.Value))
                                .ConfigureAwait(false);

                            var webShopsId = webshops.Select(a => a.Id).ToList();
                            List<string> webShopsIdStr = webShopsId.ConvertAll<string>(x => x.ToString());
                            hasPurchasedIn.Add(new HashSet<string>(await filterElasticService.OrderResponses(
                                (int) filterValue.Time,
                                webShopsIdStr)));
                        }

                        break;
                    case 2:
                        //Haven't purchased in
                        if (filterValue.Time != null)
                        {
                            var webshops = await merchantService
                                .GetAllMerchantsByCategoryAsync(Convert.ToInt32(filterValue.Value))
                                .ConfigureAwait(false);
                            var webShopsId = webshops.Select(a => a.Id).ToList();
                            List<string> webShopsIdStr = webShopsId.ConvertAll<string>(x => x.ToString());
                            hasNotPurchasedIn.Add(new HashSet<string>(await filterElasticService.OrderResponses(
                                (int) filterValue.Time,
                                webShopsIdStr)));
                        }

                        break;
                    case 3:
                        //Has purchased at
                        if (filterValue.Time != null)
                        {
                            hasPurchasedAt.Add(new HashSet<string>(await filterElasticService.OrderResponses(
                                (int) filterValue.Time,
                                new List<string> {filterValue.Value})));
                        }

                        break;
                    case 4:
                        //Haven't purchased at
                        if (filterValue.Time != null)
                        {
                            hasNotPurchasedAt.Add(new HashSet<string>(await filterElasticService.OrderResponses(
                                (int) filterValue.Time,
                                new List<string> {filterValue.Value})));
                        }

                        break;
                    case 5:
                        //Credit available
                        break;
                    case 6:
                        //Country
                        break;
                    case 7:
                        //Customer type
                        if (filterValue.Value == "")
                        {
                            //Free
                            if (filter.Match == "all")
                            {
                                contactsConditionAll =
                                    contactsConditionAll.Where(a =>
                                        a.PartnerProduct == "" || a.PartnerProduct == "FREE" ||
                                        a.PartnerProduct == "FREE_TIER_DK" || a.PartnerProduct == "NONE" ||
                                        a.PartnerProduct == "STANDARD"
                                    ).ToList();
                            }
                            else
                            {
                                contactsConditionAny.AddRange(contactsAll.Where(a =>
                                    a.PartnerProduct == "" || a.PartnerProduct == "FREE" ||
                                    a.PartnerProduct == "FREE_TIER_DK" || a.PartnerProduct == "NONE" ||
                                    a.PartnerProduct == "STANDARD"));
                            }
                        }
                        else if (filterValue.Value == "paid")
                        {
                            //Paid
                            if (filter.Match == "all")
                            {
                                contactsConditionAll =
                                    contactsConditionAll.Where(a =>
                                        a.PartnerProduct == "FULLRATE" || a.PartnerProduct == "LIBERTY" ||
                                        a.PartnerProduct == "LIGHT" || a.PartnerProduct == "MINIMUM" ||
                                        a.PartnerProduct == "PLATINUM" || a.PartnerProduct == "PREMIUM" ||
                                        a.PartnerProduct == "PAID_TIER_DK_20000" ||
                                        a.PartnerProduct == "PAID_TIER_DK_15000" ||
                                        a.PartnerProduct == "PAID_TIER_DK_10000" ||
                                        a.PartnerProduct == "PAID_TIER_DK_8000" ||
                                        a.PartnerProduct == "PAID_TIER_DK_6000" ||
                                        a.PartnerProduct == "PAID_TIER_DK_4000" ||
                                        a.PartnerProduct == "PAID_TIER_DK_2000" ||
                                        a.PartnerProduct == "PAID_TIER_ES_250" ||
                                        a.PartnerProduct == "PAID_TIER_ES_500" ||
                                        a.PartnerProduct == "PAID_TIER_ES_750" ||
                                        a.PartnerProduct == "PAID_TIER_US_300" ||
                                        a.PartnerProduct == "PAID_TIER_US_600" ||
                                        a.PartnerProduct == "PAID_TIER_US_900" ||
                                        a.PartnerProduct == "PAID_TIER_US_1200" ||
                                        a.PartnerProduct == "PAID_TIER_US_1500"
                                    ).ToList();
                            }
                            else
                            {
                                contactsConditionAny.AddRange(contactsAll.Where(a =>
                                    a.PartnerProduct == "FULLRATE" || a.PartnerProduct == "LIBERTY" ||
                                    a.PartnerProduct == "LIGHT" || a.PartnerProduct == "MINIMUM" ||
                                    a.PartnerProduct == "PLATINUM" || a.PartnerProduct == "PREMIUM" ||
                                    a.PartnerProduct == "PAID_TIER_DK_20000" ||
                                    a.PartnerProduct == "PAID_TIER_DK_15000" ||
                                    a.PartnerProduct == "PAID_TIER_DK_10000" ||
                                    a.PartnerProduct == "PAID_TIER_DK_8000" ||
                                    a.PartnerProduct == "PAID_TIER_DK_6000" ||
                                    a.PartnerProduct == "PAID_TIER_DK_4000" ||
                                    a.PartnerProduct == "PAID_TIER_DK_2000" ||
                                    a.PartnerProduct == "PAID_TIER_ES_250" ||
                                    a.PartnerProduct == "PAID_TIER_ES_500" ||
                                    a.PartnerProduct == "PAID_TIER_ES_750" ||
                                    a.PartnerProduct == "PAID_TIER_US_300" ||
                                    a.PartnerProduct == "PAID_TIER_US_600" ||
                                    a.PartnerProduct == "PAID_TIER_US_900" ||
                                    a.PartnerProduct == "PAID_TIER_US_1200" ||
                                    a.PartnerProduct == "PAID_TIER_US_1500"));
                            }
                        }
                        else
                        {
                            //TBYB
                            if (filter.Match == "all")
                            {
                                contactsConditionAll =
                                    contactsConditionAll.Where(a =>
                                        a.PartnerProduct == "PUSH_PAYMENT" || a.PartnerProduct == "TBYB").ToList();
                            }
                            else
                            {
                                contactsConditionAny.AddRange(contactsAll.Where(a =>
                                    a.PartnerProduct == "PUSH_PAYMENT" || a.PartnerProduct == "TBYB"));
                            }
                        }

                        break;
                    case 8:
                        //Campaign activity
                        if (filterValue.Time != null)
                        {
                            if (int.TryParse(filterValue.Value, out var valueInt))
                            {
                                if (string.IsNullOrEmpty(filterValue.Condition))
                                {
                                    campaignActivityExposed.Add(new HashSet<string>(
                                        await filterElasticService.CampaignMailResponses(
                                            (int) filterValue.Time,
                                            valueInt)));
                                }
                                else
                                {
                                    campaignActivityNotExposed.Add(new HashSet<string>(
                                        await filterElasticService.CampaignMailResponses(
                                            (int) filterValue.Time,
                                            valueInt)));
                                }
                            }
                        }

                        break;
                    case 9:
                        //Automation activity
                        break;
                    case 10:
                        //Customer lifetime
                        break;
                    case 11:
                        //Active exposure period
                        if (filterValue.Time != null)
                        {
                            if (filterValue.Condition == "")
                            {
                                hasActiveExposurePeriod.Add(new HashSet<string>(
                                    await filterElasticService.ClickResponses(
                                        (int) filterValue.Time,
                                        filterValue.Value)));
                            }
                            else
                            {
                                hasNotActiveExposurePeriod.Add(new HashSet<string>(
                                    await filterElasticService.ClickResponses(
                                        (int) filterValue.Time,
                                        filterValue.Value)));
                            }
                        }

                        break;
                    case 12:
                        //Gender
                        if (filterValue.Value == "")
                        {
                            //Only Male
                            if (filter.Match == "all")
                            {
                                contactsConditionAll =
                                    contactsConditionAll.Where(a => a.Gender == "Male").ToList();
                            }
                            else
                            {
                                contactsConditionAny.AddRange(contactsAll.Where(a => a.Gender == "Male"));
                            }
                        }
                        else if (filterValue.Value == "yes")
                        {
                            //Only Female
                            if (filter.Match == "all")
                            {
                                contactsConditionAll =
                                    contactsConditionAll.Where(a => a.Gender == "Female").ToList();
                            }
                            else
                            {
                                contactsConditionAny.AddRange(contactsAll.Where(a => a.Gender == "Female"));
                            }
                        }
                        else
                        {
                            //Only unknown
                            if (filter.Match == "all")
                            {
                                contactsConditionAll =
                                    contactsConditionAll.Where(a => a.Gender == "Unknown").ToList();
                            }
                            else
                            {
                                contactsConditionAny.AddRange(contactsAll.Where(a => a.Gender == "Unknown"));
                            }
                        }

                        break;
                    case 13:
                        //Has purchased at gdpr
                        if (filterValue.Time != null)
                        {
                            hasPurchasedAtGdpr.Add(new HashSet<string>(
                                await filterElasticService.OrderResponsesGdpr(
                                    (int) filterValue.Time,
                                    new List<string> {filterValue.Value})));
                        }

                        break;
                    case 14:
                        //Haven't purchased at gdpr
                        if (filterValue.Time != null)
                        {
                            hasNotPurchasedAtGdpr.Add(new HashSet<string>(
                                await filterElasticService.OrderResponsesGdpr(
                                    (int) filterValue.Time,
                                    new List<string> {filterValue.Value})));
                        }

                        break;
                    case 15:
                        //Person interested in
                        if (filterValue.Time != null)
                        {
                            personInterestedIn.Add(new HashSet<string>(
                                await filterElasticService.PersonInterestedIn(
                                    (int) filterValue.Time,
                                    filterValue.Value, false)));
                        }

                        break;
                    case 16:
                        //Person interested in
                        if (filterValue.Time != null)
                        {
                            personInterestedIn.Add(new HashSet<string>(
                                await filterElasticService.PersonInterestedIn(
                                    (int) filterValue.Time,
                                    filterValue.Value, true)));
                        }

                        break;
                    case 17:
                        //Affinity
                        if (filterValue.Time != null)
                        {
                            affinity.Add(new HashSet<string>(
                                await filterElasticService.OrderResponses(
                                    (int) filterValue.Time,
                                    filterValue.Condition.Split(",").ToList())));
                        }

                        break;
                    case 18:
                        //GDPRAffinity
                        if (filterValue.Time != null)
                        {
                            affinity.Add(new HashSet<string>(
                                await filterElasticService.OrderResponsesGdpr(
                                    (int) filterValue.Time,
                                    filterValue.Condition.Split(",").ToList())));
                        }

                        break;
                    default:
                        logger.ForContext("service_name", GetType().Name).Warning(
                            "Missing logic for " + filterValue.Id);
                        break;
                }
            }
        }

        contactsConditionAllEmail = ContactsToStringList(contactsConditionAll);
        if (filter.Match == "all")
        {
            //Has purchased in
            contactsConditionAllEmail = ValidateFilter(hasPurchasedIn, contactsConditionAllEmail);
            //Haven't purchased in
            contactsConditionAllEmail = ValidateFilter(hasNotPurchasedIn, contactsConditionAllEmail, false);
            //Has purchased at
            contactsConditionAllEmail = ValidateFilter(hasPurchasedAt, contactsConditionAllEmail);
            //Haven't purchased at
            contactsConditionAllEmail = ValidateFilter(hasNotPurchasedAt, contactsConditionAllEmail, false);
            //Has purchased at GDPR
            contactsConditionAllEmail = ValidateFilter(hasPurchasedAtGdpr, contactsConditionAllEmail);
            //Haven't purchased at GDPR
            contactsConditionAllEmail = ValidateFilter(hasNotPurchasedAtGdpr, contactsConditionAllEmail, false);
            //Has active exposure period
            contactsConditionAllEmail = ValidateFilter(hasActiveExposurePeriod, contactsConditionAllEmail);
            //Has not active exposure period
            contactsConditionAllEmail =
                ValidateFilter(hasNotActiveExposurePeriod, contactsConditionAllEmail, false);
            //Campaign activity exposed
            contactsConditionAllEmail = ValidateFilter(campaignActivityExposed, contactsConditionAllEmail);
            //Campaign activity not exposed
            contactsConditionAllEmail =
                ValidateFilter(campaignActivityNotExposed, contactsConditionAllEmail, false);
            //Person interested in
            contactsConditionAllEmail = ValidateFilter(personInterestedIn, contactsConditionAllEmail);
            //Affinity
            contactsConditionAllEmail = ValidateFilter(affinity, contactsConditionAllEmail);

            return contactsConditionAllEmail.ToList();
        }

        ///////////////////////////////////// ANY /////////////////////////////////////
        ///////////////////////////////////// ANY /////////////////////////////////////
        ///////////////////////////////////// ANY /////////////////////////////////////
        var returnEmails = new List<string>();

        //Has purchased in
        returnEmails.AddRange(ValidateFilterAny(hasPurchasedIn, contactsConditionAllEmail));
        //Haven't purchased in
        returnEmails.AddRange(ValidateFilterAny(hasNotPurchasedIn, contactsConditionAllEmail, false));
        //Has purchased at
        returnEmails.AddRange(ValidateFilterAny(hasPurchasedAt, contactsConditionAllEmail));
        //Haven't purchased at
        returnEmails.AddRange(ValidateFilterAny(hasNotPurchasedAt, contactsConditionAllEmail, false));
        //Has purchased at GDPR
        returnEmails.AddRange(ValidateFilterAny(hasPurchasedAtGdpr, contactsConditionAllEmail));
        //Haven't purchased at GDPR
        returnEmails.AddRange(ValidateFilterAny(hasNotPurchasedAtGdpr, contactsConditionAllEmail, false));
        //Has active exposure period
        returnEmails.AddRange(ValidateFilterAny(hasActiveExposurePeriod, contactsConditionAllEmail));
        //Has not active exposure period
        returnEmails.AddRange(ValidateFilterAny(hasNotActiveExposurePeriod, contactsConditionAllEmail, false));
        //Campaign activity exposed
        returnEmails.AddRange(ValidateFilterAny(campaignActivityExposed, contactsConditionAllEmail));
        //Campaign activity not exposed
        returnEmails.AddRange(ValidateFilterAny(campaignActivityNotExposed, contactsConditionAllEmail, false));
        //Person interested in
        returnEmails.AddRange(ValidateFilterAny(personInterestedIn, contactsConditionAllEmail));
        //Affinity
        returnEmails.AddRange(ValidateFilterAny(affinity, contactsConditionAllEmail));

        returnEmails.AddRange(contactsConditionAny.Select(a => a.Email));
        return returnEmails.Distinct().ToList();
    }

    private List<string> ContactsToStringList(List<Customer> customers)
    {
        var emails = new List<string>();
        foreach (var customer in customers)
        {
            emails.Add(customer.Email.Trim().ToLowerInvariant());
        }

        return emails;
    }

    private List<string> ValidateFilterAny(List<HashSet<string>> list, List<string> emails, bool has = true)
    {
        var returnEmails = new HashSet<string>();
        foreach (var items in list)
        {
            foreach (var email in emails)
            {
                if (items.Count != 0)
                {
                    if (has)
                    {
                        if (items.Contains(email))
                        {
                            returnEmails.Add(email);
                        }
                    }
                    else
                    {
                        if (!items.Contains(email))
                        {
                            returnEmails.Add(email);
                        }
                    }
                }
            }
        }

        return returnEmails.ToList();
    }

    private List<string> ValidateFilter(List<HashSet<string>> list, List<string> emails, bool has = true)
    {
        var removeEmails = new HashSet<string>();
        foreach (var items in list)
        {
            foreach (var email in emails)
            {
                if (items.Count != 0)
                {
                    if (has)
                    {
                        if (!items.Contains(email))
                        {
                            removeEmails.Add(email);
                        }
                    }
                    else
                    {
                        if (items.Contains(email))
                        {
                            removeEmails.Add(email);
                        }
                    }
                }
            }
        }

        return emails.Except(removeEmails).ToList();
    }

    /*public async Task<List<FilterAffinityDto>> GetAffinityAsync(FilterValue filterValue)
    {
        if (filterValue.FkFilterTemplateId == 17)
        {
            return await _filterElasticService.OrderResponsesAffinityFilter(
                (int)filterValue.Time,
                filterValue.Value, false);
        }

        return await _filterElasticService.OrderResponsesAffinityFilter(
            (int)filterValue.Time, filterValue.Value, true);
    }*/

    public async Task<Models.ModelsDal.Campaign.Filter> CreateFilterAsync(Models.ModelsDal.Campaign.Filter filter)
    {
        foreach (var filterValue in filter.FilterValues)
        {
            filterValue.FkFilterTemplate = null;
        }

        await campaignDbContext.AddAsync(filter);
        await campaignDbContext.SaveChangesAsync();
        return filter;
    }

    public async Task<Models.ModelsDal.Campaign.Filter> UpdateFilterAsync(Models.ModelsDal.Campaign.Filter filter)
    {
        foreach (var filterValue in filter.FilterValues)
        {
            filterValue.FkFilterTemplate = null;
        }

        campaignDbContext.Update(filter);
        await campaignDbContext.SaveChangesAsync();
        return filter;
    }
}