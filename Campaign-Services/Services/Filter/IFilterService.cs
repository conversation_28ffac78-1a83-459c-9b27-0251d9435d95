using Campaign_Service.Models.ModelsDal.Campaign;
using Campaign_Services.Models.ModelsDal.Campaign;
using Shared.Dto.Campaign;

namespace Campaign_Services.Services.Filter
{
    public interface IFilterService
    {
        Task<List<string>> GetAllFilterEmailsAsync(int filterId);

        Task<List<string>> GetAllFilterEmailsAsync(Models.ModelsDal.Campaign.Filter filter);

        //Task<List<FilterAffinityDto>> GetAffinityAsync(FilterValue filterValue);
        Task<Models.ModelsDal.Campaign.Filter> CreateFilterAsync(Models.ModelsDal.Campaign.Filter filter);
        Task<Models.ModelsDal.Campaign.Filter> UpdateFilterAsync(Models.ModelsDal.Campaign.Filter filter);
    }
}