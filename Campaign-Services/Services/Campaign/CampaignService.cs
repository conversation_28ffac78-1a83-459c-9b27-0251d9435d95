using System.Text;
using Audience.Services.Audience;
using AutoMapper;
using Campaign_Services.Models.Message;
using Campaign_Services.Models.ModelsDal.Campaign;
using Campaign_Services.Services.Filter;
using Message_Services.Services.Message;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using RabbitMQ.Client;
using Renci.SshNet;
using Shared.Dto.Campaign;
using Shared.Dto.Campaign.Enums;
using Shared.Dto.Image;
using Shared.Models;
using Shared.Services.Setting;
using ILogger = Serilog.ILogger;
using JsonSerializer = System.Text.Json.JsonSerializer;
using Shared.Dto.EmailOnAcid;
using Shared.Elastic.CampaignMailClick;
using Shared.Services.Cache;
using Shared.Services.Image;
using Shared.Services.Partner;
using ClientDetails = Shared.Dto.EmailOnAcid.ClientDetails;
using IPartnerService = Partner_Services.Services.General.IPartnerService;

namespace Campaign_Services.Services.Campaign;

public class CampaignService(
    ILogger logger,
    CampaignDbContextTracking campaignDbContext,
    IConfiguration configuration,
    IFilterService filterService,
    IMessageService messageService,
    ISettingService settingService,
    IImageService imageService,
    IElasticCampaignMailClickService elasticCampaignMailClickService,
    ISegmentService segmentService,
    [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud,
    ICustomerService customerService,
    //IConnection rabbitConnection,
    
    IPartnerService partnerService,
    ICacheService cacheService,
    IPartnerContext partnerContext)
    : ICampaignService
{
    public async Task<List<CampaignDto>> GetAllAsync(int partnerId)
    {
        return await campaignDbContext.Campaigns
            .Where(x => x.Active && x.FkPartnerId == partnerId)
            .OrderByDescending(a => a.LastModifiedDate)
            .Select(campaign => new CampaignDto
            {
                Id = campaign.Id,
                //GlobalStyles = campaign.GlobalStyles,
                Name = campaign.Name,
                PreviewText = campaign.PreviewText,
                //PublishStatus = campaign.PublishStatus,
                FromName = campaign.FromName,
                //SenderEmail = campaign.SenderEmail,
                Subject = campaign.Subject,
                //Template = campaign.Template,
                //StartDate = campaign.StartDate,
                //EndDate = campaign.EndDate,
                //Preview = campaign.Preview ?? "",
                LastModifiedDate = campaign.LastModifiedDate,
                //LastPublishedDate = campaign.LastPublishedDate,
                CampaignType = Enum.Parse<CampaignType>(campaign.CampaignType),
                //LastSent = lastSent?.DateSent,
                //Schedules = schedules
                Status = campaign.Status,
            })
            .ToListAsync()
            .ConfigureAwait(false);
    }

    public async Task<List<CampaignDto>> GetAllTemplates(int partnerId)
    {
        var campaigns = new List<CampaignDto>();
        var campaignQuery = await campaignDbContext.Campaigns
            .Where(x => x.Active)
            .Include(x => x.Blocks)
            .AsSplitQuery()
            .OrderByDescending(a => a.LastModifiedDate)
            .ToListAsync()
            .ConfigureAwait(false);

        foreach (var campaign in campaignQuery)
        {
            var newCampaign = new CampaignDto
            {
                Id = campaign.Id,
                //Body = campaign.Body,
                GlobalStyles = campaign.GlobalStyles,
                Name = campaign.Name,
                //Preview = campaign.Preview,
                PreviewText = campaign.PreviewText,
                //PublishStatus = campaign.PublishStatus,
                FromName = campaign.FromName,
                //SenderEmail = campaign.SenderEmail,
                Subject = campaign.Subject,
                //Template = campaign.Template,
                //StartDate = campaign.StartDate,
                //EndDate = campaign.EndDate,
                LastModifiedDate = campaign.LastModifiedDate,
                //LastPublishedDate = campaign.LastPublishedDate,
                CampaignType = Enum.Parse<CampaignType>(campaign.CampaignType),
                Blocks = campaign.Blocks.Select(x => new BlockDto
                {
                    Id = x.Id,
                    BlockType = (BlockTypeDto) x.FkBlockTypeId,
                    SortOrder = x.SortOrder,
                    BlockGuid = x.BlockGuid ?? "",
                    Settings = x.Settings ?? ""
                }).ToList()
            };

            campaigns.Add(newCampaign);
        }

        return campaigns;
    }

    public async Task<List<Models.ModelsDal.Campaign.Campaign>> GetAllFullAsync(int partnerId)
    {
        return await campaignDbContext.Campaigns
            .Include(a => a.FkFilter)
            .ThenInclude(b => b.FilterValues.Where(c => c.Active))
            .Where(a => a.Active && a.FkPartnerId == partnerId).OrderByDescending(a => a.Id).ToListAsync();
    }


    /// <summary>
    /// Get all Tempates
    /// </summary>
    /// <returns>Response</returns>
    /*public async Task<IEnumerable<CampaignDto>> GetAllTemplatesAsync()
    {
        var templates = new List<CampaignDto>();
        var campaignQuery = await _campaignDbContext.Campaigns
            .Where(x => x.Template == true
                        && x.Active == true
                        && string.IsNullOrEmpty(x.Body))
            .AsSplitQuery()
            .OrderByDescending(a => a.Id)
            .ToListAsync()
            .ConfigureAwait(false);
        //var now = DateTime.UtcNow;
        foreach (var campaign in campaignQuery)
        {
            var newCampaign = new CampaignDto
            {
                Id = campaign.Id,
                Name = campaign.Name,
                Template = campaign.Template,
                Preview = campaign.Preview ?? "",
            };

            templates.Add(newCampaign);
        }

        return templates;
    }*/
    public async Task<List<QueuedCampaigns>> GetAllQueuedCampaignsAsync(int partnerId)
    {
        return await messageService.GetCampaignsInQueue(partnerId);
    }

    public async Task<ResponseDto> DisregardQueuedCampaign(int campaignId)
    {
        var campaign = await campaignDbContext.Campaigns
            .SingleAsync(a => a.Id == campaignId);
        campaign.Status = null;
        campaignDbContext.Update(campaign);
        await campaignDbContext.SaveChangesAsync();

        return await messageService.DisregardQueuedCampaign(campaignId);
    }

    public async Task<ResponseDto> ChangePriorityOfQueuedCampaigns(List<QueuedCampaigns> queuedCampaigns)
    {
        return await messageService.ChangePriorityOfQueuedCampaigns(queuedCampaigns);
    }

    public async Task<DateTime> QueueEmptyEstimate(int partnerId)
    {
        var totalMails = await messageService.QueueSize(partnerId);
        var mailsPerMinute = Convert.ToInt32((await settingService.GetSettingAsync(partnerId, "HourlyMailLimit")).Value) / 60;
        var allowedHours = (await settingService.GetSettingAsync(partnerId, "MailDeliveringHours")).Value.Split(',')
            .Select(str => int.Parse(str))
            .ToList();

        int totalMinutesRequired = (int) Math.Ceiling((double) totalMails / mailsPerMinute);

        DateTime currentTime = DateTime.UtcNow;

        DateTime timePointer = new DateTime(currentTime.Year, currentTime.Month, currentTime.Day, currentTime.Hour,
            currentTime.Minute, 0);

        // Move timePointer to the next available minute if not already in one
        while (!allowedHours.Contains(timePointer.Hour) || timePointer < currentTime)
        {
            timePointer = timePointer.AddMinutes(1);

            if (timePointer.Hour > allowedHours[allowedHours.Count - 1])
            {
                timePointer = new DateTime(timePointer.Year, timePointer.Month, timePointer.Day, allowedHours[0], 0, 0)
                    .AddDays(1);
            }
        }

        // Fast-forward through allowed minutes
        for (int i = 0; i < totalMinutesRequired; i++)
        {
            // If we've hit the last allowed minute for the day, move to the next day's first allowed minute
            if (timePointer.Hour == allowedHours[allowedHours.Count - 1] && timePointer.Minute == 59)
            {
                timePointer = new DateTime(timePointer.Year, timePointer.Month, timePointer.Day, allowedHours[0], 0, 0)
                    .AddDays(1);
            }
            else
            {
                timePointer = timePointer.AddMinutes(1);

                // If we've moved to a non-allowed hour, skip to the next allowed hour
                while (!allowedHours.Contains(timePointer.Hour))
                {
                    timePointer = new DateTime(timePointer.Year, timePointer.Month, timePointer.Day,
                        timePointer.Hour + 1, 0, 0);
                }
            }
        }

        return timePointer;
    }

    public async Task<CampaignPaginationDto> GetAllAsync(int partnerId, int page, int size, string filter,
        IMapper mapper,
        CampaignType campaignType = CampaignType.Customer, string sortName = "", string sortOrder = "",
        string searchString = "")
    {
        var search = searchString.Trim();
        var campaignsReturn = new List<CampaignDto>();
        var campaigns = new List<CampaignDto>();
        var campaignQuery = campaignDbContext.Campaigns
            .Where(x => x.Active == true
                        && x.FkPartnerId == partnerId
                        && x.CampaignType == campaignType.ToString()
                        && (searchString == "" || x.Id.ToString().Contains(search) || x.Name.Contains(search)))
            .OrderByDescending(a => a.LastModifiedDate);

        switch (sortName)
        {
            case "id":
                campaignQuery = sortOrder == "asc"
                    ? campaignQuery.OrderBy(a => a.Id)
                    : campaignQuery.OrderByDescending(a => a.Id);
                break;
            case "name":
                campaignQuery = sortOrder == "asc"
                    ? campaignQuery.OrderBy(a => a.Name)
                    : campaignQuery.OrderByDescending(a => a.Name);
                break;
            /*case "audience":
                campaignQuery = sortOrder == "asc"
                    ? campaignQuery.OrderBy(a => a.CampaignStats.First().Audience)
                    : campaignQuery.OrderByDescending(a => a.CampaignStats.First().Audience);
                break;
            case "delivered":
                campaignQuery = sortOrder == "asc"
                    ? campaignQuery.OrderBy(a => a.CampaignStats.First().Delivered)
                    : campaignQuery.OrderByDescending(a => a.CampaignStats.First().Delivered);
                break;
            case "openRate":
                campaignQuery = sortOrder == "asc"
                    ? campaignQuery.OrderBy(a => a.CampaignStats.First().OpenRate)
                    : campaignQuery.OrderByDescending(a => a.CampaignStats.First().OpenRate);
                break;
            case "clickRate":
                campaignQuery = sortOrder == "asc"
                    ? campaignQuery.OrderBy(a => a.CampaignStats.First().ClickRate)
                    : campaignQuery.OrderByDescending(a => a.CampaignStats.First().ClickRate);
                break;
            case "unsubscribeRate":
                campaignQuery = sortOrder == "asc"
                    ? campaignQuery.OrderBy(a => a.CampaignStats.First().UnsubscribeRate)
                    : campaignQuery.OrderByDescending(a => a.CampaignStats.First().UnsubscribeRate);
                break;*/
        }

        var start = size * page;
        var campaigns1 = await campaignQuery
            .Skip(start).Take(size)
            .ToListAsync();

        var sendStatuses = await messageService.SendStatus(campaigns1.Select(a => a.Id).ToList());

        foreach (var campaign in campaigns1)
        {
            var status = "DRAFT";
            var queueSizeReady = await messageService.QueueSizeByCampaignId(campaign.Id);
            if (queueSizeReady != 0)
            {
                status = "SENDING";
            }
            else
            {
                if (sendStatuses.Contains(campaign.Id))
                {
                    status = "SENT";
                }
            }

            var newCampaign = new CampaignDto
            {
                Id = campaign.Id,
                Name = campaign.Name,
                //PublishStatus = campaign.PublishStatus,
                //Template = campaign.Template,
                //StartDate = campaign.StartDate,
                //EndDate = campaign.EndDate,
                //Preview = campaign.Preview ?? "",
                LastModifiedDate = campaign.LastModifiedDate,
                //LastPublishedDate = campaign.LastPublishedDate,
                //LastSent = lastSent?.DateSent,
                Status = status,
                /*CampaignStats =
                    new()
                    {
                        Audience = campaign.CampaignStats.FirstOrDefault()?.Audience ?? 0,
                        Delivered = campaign.CampaignStats.FirstOrDefault()?.Delivered ?? 0,
                        Opens = campaign.CampaignStats.FirstOrDefault()?.Opens ?? 0,
                        OpenRate = campaign.CampaignStats.FirstOrDefault()?.OpenRate ?? 0,
                        Clicks = campaign.CampaignStats.FirstOrDefault()?.Clicks ?? 0,
                        ClickRate = campaign.CampaignStats.FirstOrDefault()?.ClickRate ?? 0,
                        Unsubscribes = campaign.CampaignStats.FirstOrDefault()?.Unsubscribe ?? 0,
                        UnsubscribeRate = campaign.CampaignStats.FirstOrDefault()?.UnsubscribeRate ?? 0,
                    }*/
            };

            campaigns.Add(newCampaign);
        }

        switch (filter)
        {
            case "scheduled":
                campaignsReturn = campaigns.Where(a => a.Schedules.Count > 0).ToList();
                break;
            case "automated":
                /*var automations = (await _automationClient.Automations()).Select(a => a.CampaignId);
                foreach (var campaign in campaigns)
                {
                    if (automations.Contains(campaign.Id))
                    {
                        campaignsReturn.Add(campaign);
                    }
                }*/

                break;
            default:
                campaignsReturn = campaigns;
                break;
        }

        //var totalSize = campaignQuery.Count;
        var totalSize = await campaignQuery.CountAsync();

        var campaignPagination = new CampaignPaginationDto
        {
            Campaigns = campaignsReturn,
            Size = totalSize,
            HasPreviousPage = page > 0,
            HasNextPage = (page + 1) * size < totalSize
        };
        return campaignPagination;
    }

    public async Task<CampaignDto> GetAsync(int id)
    {
        var campaign = new CampaignDto();

        var campaignQuery = await campaignDbContext.Campaigns
            .Include(x => x.Blocks)
            //.Include(a => a.CampaignInstances)
            //.Include(a => a.FkCampaignGroup)
            .Include(a => a.CampaignTests)
            .ThenInclude(a => a.CampaignTestValues)
            .AsSplitQuery()
            .Where(x => x.Id == id)
            .FirstOrDefaultAsync()
            .ConfigureAwait(false);

        if (campaignQuery != null)
        {
            campaign = new CampaignDto
            {
                Id = campaignQuery.Id,
                CampaignId = campaignQuery.Id,
                InternalCampaignId = campaign.InternalCampaignId,
                //Body = campaignQuery.Body,
                GlobalStyles = campaignQuery.GlobalStyles,
                Name = campaignQuery.Name,
                PreviewText = campaignQuery.PreviewText,
                //PublishStatus = campaignQuery.PublishStatus,
                FromName = campaignQuery.FromName,
                //SenderEmail = campaignQuery.SenderEmail,
                Subject = campaignQuery.Subject,
                //Preview = campaignQuery.Preview ?? "",
                //Template = campaignQuery.Template,
                //StartDate = campaignQuery.StartDate,
                //EndDate = campaignQuery.EndDate,
                LastModifiedDate = campaignQuery.LastModifiedDate,
                //LastPublishedDate = campaignQuery.LastPublishedDate,
                Status = campaignQuery.Status,
                //CampaignGroupId = campaignQuery.FkCampaignGroupId,
                FkFilterId = campaignQuery.FkFilterId,
                CampaignType = Enum.Parse<CampaignType>(campaignQuery.CampaignType),
                FkSegmentId = campaignQuery.FkSegmentId ?? 0,
                Blocks = campaignQuery.Blocks.Select(x => new BlockDto
                {
                    Id = x.Id,
                    BlockType = (BlockTypeDto) x.FkBlockTypeId,
                    SortOrder = x.SortOrder,
                    Settings = x.Settings ?? "",
                    BlockGuid = x.BlockGuid ?? ""
                }).ToList(),
                CampaignTest = campaignQuery.CampaignTests.Select(ct => new CampaignTestDto
                {
                    Id = ct.Id,
                    Percentage = ct.Percentage,
                    ComparisonType = ct.ComparisonType,
                    Hours = ct.Hours,
                    FkCampaignId = ct.FkCampaignId,
                    CampaignTestValues = ct.CampaignTestValues.Select(ctv => new CampaignTestValueDto
                    {
                        Id = ctv.Id,
                        FkCampaignTestId = ctv.FkCampaignTestId,
                        Type = ctv.Type,
                        Value = ctv.Value
                    }).ToList()
                }).FirstOrDefault(),
            };
        }

        return campaign;
    }

    public async Task<Dictionary<int, string>> GetNameAsync(List<int> ids)
    {
        return await campaignDbContext.Campaigns
            .Where(a => ids.Contains(a.Id))
            .ToDictionaryAsync(a => a.Id, a => a.Name);
    }

    /*public async Task<List<int>> GetCampaignIdsByGroupIdAsync(int campaignGroupId)
    {
        return await _campaignDbContext.Campaigns
            .Where(a => a.FkCampaignGroupId == campaignGroupId)
            .Select(a => a.Id)
            .ToListAsync()
            .ConfigureAwait(false);
    }*/

    /*public async Task<List<CampaignGroup>> GetCampaignGroupsAsync()
    {
        var campaignGroupQuery = await _campaignDbContext.CampaignGroups
            .AsSplitQuery()
            .ToListAsync()
            .ConfigureAwait(false);

        return campaignGroupQuery
            .Select(campaignGroup => new CampaignGroup { Id = campaignGroup.Id, Name = campaignGroup.Name }).ToList();
    }*/

    /*public async Task<ResponseDto> DeleteCampaignGroupAsync(int id)
    {
        var campaignGroup = await _campaignDbContext.CampaignGroups.FirstOrDefaultAsync(a => a.Id == id);

        if (campaignGroup != null)
        {
            var campaigns = await _campaignDbContext.Campaigns.Where(a => a.FkCampaignGroupId == id).ToListAsync();

            foreach (var campaign in campaigns)
            {
                campaign.FkCampaignGroupId = null;
            }

            _campaignDbContext.Remove(campaignGroup);

            await _campaignDbContext.SaveChangesAsync();
        }
        else
            return new ResponseDto()
            {
                Message = "Campaign Group not found",
                Success = false
            };

        return new ResponseDto()
        {
            Message = "Success",
            Success = true
        };
    }*/

    public async Task<Models.ModelsDal.Campaign.Filter> GetFilterAsync(int id)
    {
        return await campaignDbContext.Filters
            .Include(a => a.FilterValues.Where(b => b.Active == true))
            .ThenInclude(b => b.FkFilterTemplate)
            .SingleAsync(a => a.Id == id);
    }

    public async Task<List<Models.ModelsDal.Campaign.Filter>> GetFilterPresetAsync()
    {
        return await campaignDbContext.Filters.Where(a => a.PresetName != null)
            .Include(a => a.FilterValues
                .Where(b => b.Active == true))
            .ThenInclude(a => a.FkFilterTemplate)
            .ToListAsync();
    }

    private async Task<List<CampaignDto>> GetAllCampaigns(bool isTemplate)
    {
        var campaigns = new List<CampaignDto>();

        try
        {
            if (isTemplate == false)
            {
                var campaignQuery = await campaignDbContext.Campaigns
                    .Where(x => x.Active)
                    //.Include(a => a.CampaignInstances)
                    .AsSplitQuery()
                    .OrderByDescending(a => a.LastModifiedDate)
                    .ToListAsync()
                    .ConfigureAwait(false);
                var now = DateTime.UtcNow;
                foreach (var campaign in campaignQuery)
                {
                    /*var scheduled = campaign.CampaignInstances
                        .Where(a => a.ScheduledDate > now && a.EndDate == null).ToList();
                    var lastSent = campaign.CampaignInstances.Where(a => a.DateSent != null).MaxBy(a => a.DateSent);
                    var schedules = new List<DateTime>();
                    foreach (var sc in scheduled)
                    {
                        if (sc.ScheduledDate != null)
                        {
                            schedules.Add((DateTime)sc.ScheduledDate);
                        }
                    }*/

                    var newCampaign = new CampaignDto
                    {
                        Id = campaign.Id,
                        //GlobalStyles = campaign.GlobalStyles,
                        Name = campaign.Name,
                        PreviewText = campaign.PreviewText,
                        //PublishStatus = campaign.PublishStatus,
                        FromName = campaign.FromName,
                        //SenderEmail = campaign.SenderEmail,
                        Subject = campaign.Subject,
                        //Template = campaign.Template,
                        //StartDate = campaign.StartDate,
                        //EndDate = campaign.EndDate,
                        //Preview = campaign.Preview ?? "",
                        LastModifiedDate = campaign.LastModifiedDate,
                        //LastPublishedDate = campaign.LastPublishedDate,
                        CampaignType = Enum.Parse<CampaignType>(campaign.CampaignType),
                        //LastSent = lastSent?.DateSent,
                        //Schedules = schedules
                    };

                    campaigns.Add(newCampaign);
                }
            }
            else
            {
                var campaignQuery = await campaignDbContext.Campaigns
                    .Where(x => x.Active)
                    .Include(x => x.Blocks)
                    .AsSplitQuery()
                    .OrderByDescending(a => a.LastModifiedDate)
                    .ToListAsync()
                    .ConfigureAwait(false);

                foreach (var campaign in campaignQuery)
                {
                    var newCampaign = new CampaignDto
                    {
                        Id = campaign.Id,
                        //Body = campaign.Body,
                        GlobalStyles = campaign.GlobalStyles,
                        Name = campaign.Name,
                        //Preview = campaign.Preview,
                        PreviewText = campaign.PreviewText,
                        //PublishStatus = campaign.PublishStatus,
                        FromName = campaign.FromName,
                        //SenderEmail = campaign.SenderEmail,
                        Subject = campaign.Subject,
                        //Template = campaign.Template,
                        //StartDate = campaign.StartDate,
                        //EndDate = campaign.EndDate,
                        LastModifiedDate = campaign.LastModifiedDate,
                        //LastPublishedDate = campaign.LastPublishedDate,
                        CampaignType = Enum.Parse<CampaignType>(campaign.CampaignType),
                        Blocks = campaign.Blocks.Select(x => new BlockDto
                        {
                            Id = x.Id,
                            BlockType = (BlockTypeDto) x.FkBlockTypeId,
                            SortOrder = x.SortOrder,
                            BlockGuid = x.BlockGuid ?? "",
                            Settings = x.Settings ?? ""
                        }).ToList()
                    };

                    campaigns.Add(newCampaign);
                }
            }
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Get all campaigns");
            throw;
        }

        return campaigns;
    }

    public async Task<List<FilterTemplate>> GetAllFilterTemplatesAsync()
    {
        return await campaignDbContext.FilterTemplates.Where(a => a.Name != "" && a.Active == true).ToListAsync()
            .ConfigureAwait(false);
    }

    public async Task<CampaignResponse> CreateAsync(int partnerId, CampaignDto campaign)
    {
        var response = new CampaignResponse();

        var fromName = await settingService.GetSettingValueAsync(partnerId, "EmailFromName");
        if(string.IsNullOrEmpty(fromName))
        {
            fromName = await partnerService.GetPartnerName();
        }

        var newCampaign = new Models.ModelsDal.Campaign.Campaign
        {
            //Body = "",
            GlobalStyles = campaign.GlobalStyles,
            Name = campaign.Name,
            PreviewText = campaign.PreviewText,
            //PublishStatus = campaign.PublishStatus,
            FromName = fromName,
            //SenderEmail = campaign.SenderEmail,
            Subject = campaign.Subject,
            //Template = campaign.Template,
            //StartDate = campaign.StartDate,
            //EndDate = campaign.EndDate,
            FkFilterId = campaign.FkFilterId,
            LastModifiedDate = DateTime.UtcNow,
            Active = campaign.Active,
            CampaignType = campaign.CampaignType.ToString(),
            FkSegmentId = campaign.FkSegmentId,
            FkPartnerId = partnerId
        };

        campaignDbContext.Campaigns.Add(newCampaign);
        await campaignDbContext.SaveChangesAsync();

        var campaignId = newCampaign.Id;

        response.Id = campaignId;

        if (campaignId <= 0) return response;
        
        foreach (var block in campaign.Blocks)
        {
            var newBlock = new Block
            {
                FkCampaignId = campaignId,
                FkBlockTypeId = (int) block.BlockType,
                SortOrder = block.SortOrder,
                Settings = block.Settings,
                BlockGuid = !string.IsNullOrEmpty(block.BlockGuid) ? block.BlockGuid : Guid.NewGuid().ToString()
            };

            await campaignDbContext.Blocks.AddAsync(newBlock);
            await campaignDbContext.SaveChangesAsync();
        }

        await campaignDbContext.SaveChangesAsync();

        return response;
    }


    /// <summary>
    /// Create a campaign template
    /// </summary>
    /// <param name="id"></param>
    /// <returns>Response</returns> 
    public async Task<CampaignDto?> CreateTemplateAsync(int id)
    {
        var campaign = await GetAsync(id);

        var response = new CampaignResponse();

        var newCampaign = new Models.ModelsDal.Campaign.Campaign
        {
            //Body = campaign.Body,
            GlobalStyles = campaign.GlobalStyles,
            Name = campaign.Name,
            PreviewText = campaign.PreviewText,
            //Preview = campaign.Preview,
            //PublishStatus = campaign.PublishStatus,
            FromName = campaign.FromName,
            //SenderEmail = campaign.SenderEmail,
            Subject = campaign.Subject,
            //Template = true,
            //StartDate = campaign.StartDate,
            //EndDate = campaign.EndDate,
            LastModifiedDate = DateTime.UtcNow,
            CampaignType = campaign.CampaignType.ToString()
        };

        campaignDbContext.Campaigns.Add(newCampaign);
        await campaignDbContext.SaveChangesAsync();

        var campaignId = newCampaign.Id;

        response.Id = campaignId;

        if (campaignId <= 0) return null;

        var blocks = new List<Block>();
        foreach (var block in campaign.Blocks)
        {
            blocks.Add(new Block()
            {
                FkCampaignId = campaignId,
                FkBlockTypeId = (int) block.BlockType,
                SortOrder = block.SortOrder,
                Settings = block.Settings
            });
        }

        await campaignDbContext.Blocks.AddRangeAsync(blocks);
        await campaignDbContext.SaveChangesAsync();

        return await GetAsync(campaignId);
    }


    /// <summary>
    /// Override a campaign with a Template
    /// </summary>
    /// <param name="id"></param>
    /// <param name="templateId"></param>
    /// <returns>Response</returns> 
    public async Task<ResponseDto> UseTemplateAsync(int id, int templateId)
    {
        var campaign = await GetAsync(id);
        var template = await GetAsync(templateId);

        campaign.Blocks = new List<BlockDto>();
        foreach (var templateBlock in template.Blocks)
        {
            campaign.Blocks.Add(new BlockDto()
            {
                BlockType = templateBlock.BlockType,
                Settings = templateBlock.Settings,
                SortOrder = templateBlock.SortOrder
            });
        }

        campaign.GlobalStyles = template.GlobalStyles;
        //campaign.Preview = template.Preview;
        campaign.PreviewText = template.PreviewText;
        campaign.Subject = template.Subject;
        campaign.FromName = template.FromName;

        await UpdateAsync(campaign, true);

        return new ResponseDto()
        {
            Message = "Success",
            Success = true
        };
    }


    /// <summary>
    /// Duplicate a campaign
    /// </summary>
    /// <param name="id"></param>
    /// <returns>Response</returns> 
    public async Task<CampaignResponse> DuplicateAsync(int id)
    {
        var campaign = await campaignDbContext.Campaigns
            .Include(x => x.Blocks)
            .Include(a => a.CampaignTests)
            .ThenInclude(a => a.CampaignTestValues)
            .AsSplitQuery()
            .Where(x => x.Id == id)
            .FirstOrDefaultAsync()
            .ConfigureAwait(false);

        if(campaign == null)
        {
            return new CampaignResponse
            {
                Message = "Campaign not found"
            };
        }
        var response = new CampaignResponse();

        var newCampaign = new Models.ModelsDal.Campaign.Campaign
        {
            Active = true,
            GlobalStyles = campaign.GlobalStyles,
            Name = $"{campaign.Name} (Copy)",
            PreviewText = campaign.PreviewText,
            FromName = campaign.FromName,
            Subject = campaign.Subject,
            LastModifiedDate = DateTime.UtcNow,
            CampaignType = campaign.CampaignType,
            FkPartnerId = campaign.FkPartnerId,
        };

        campaignDbContext.Campaigns.Add(newCampaign);
        await campaignDbContext.SaveChangesAsync();

        var campaignId = newCampaign.Id;

        response.Id = campaignId;

        if (campaignId <= 0) return response;

        var blocks = new List<Block>();
        foreach (var block in campaign.Blocks)
        {
            blocks.Add(new Block()
            {
                FkCampaignId = campaignId,
                FkBlockTypeId = block.FkBlockTypeId,
                SortOrder = block.SortOrder,
                Settings = block.Settings
            });
        }

        await campaignDbContext.Blocks.AddRangeAsync(blocks);
        await campaignDbContext.SaveChangesAsync();

        return response;
    }

    public async Task UpdateAsync(Models.ModelsDal.Campaign.Campaign campaign)
    {
        campaignDbContext.Campaigns.Update(campaign);
        await campaignDbContext.SaveChangesAsync();
    }

    public async Task<ResponseDto> UpdateAsync(CampaignDto campaign, bool forceUpdatePreview = false)
    {
        var currentCampaign = await campaignDbContext.Campaigns.FirstOrDefaultAsync(c => c.Id == campaign.Id);
        if (currentCampaign == null)
        {
            return new ResponseDto
            {
                Message = $"No campaign with id: {campaign.Id} found",
                Success = false
            };
        }

        currentCampaign.GlobalStyles = campaign.GlobalStyles;
        currentCampaign.Name = campaign.Name;
        currentCampaign.PreviewText = campaign.PreviewText;
        currentCampaign.FromName = campaign.FromName;
        currentCampaign.Subject = campaign.Subject;
        currentCampaign.LastModifiedDate = DateTime.UtcNow;
        currentCampaign.CampaignType = campaign.CampaignType.ToString();
        
        if(campaign.CampaignType == CampaignType.Customer)
            currentCampaign.FkSegmentId = campaign.FkSegmentId;
        else
            currentCampaign.FkSegmentId = null;

        await UpdateBlocksAsync(campaign);
        await UpdateCampaignTestsAsync(campaign);

        try
        {
            await campaignDbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            return new ResponseDto {Message = ex.Message, Success = false};
        }

        return new ResponseDto {Message = "Success", Success = true};
    }

    private async Task UpdateBlocksAsync(CampaignDto campaign)
    {
        var blocks = (from block in campaignDbContext.Blocks
            where block.FkCampaignId == campaign.Id
            select block).ToList();

        // Delete all blocks in the database but not in the new submitted campaign
        campaignDbContext.RemoveRange(blocks.Where(x => !campaign.Blocks.Select(y => y.Id).Contains(x.Id)));

        // Update or insert submitted blocks
        foreach (var block in campaign.Blocks)
        {
            var existingBlock = (from bl in campaignDbContext.Blocks
                where bl.Id == block.Id
                select bl).FirstOrDefault();

            // Update if found in DB else create new
            if (existingBlock != null)
            {
                existingBlock.SortOrder = block.SortOrder;
                existingBlock.BlockGuid = !string.IsNullOrEmpty(block.BlockGuid)
                    ? block.BlockGuid
                    : Guid.NewGuid().ToString();
                existingBlock.Settings = block.Settings;
                existingBlock.LastModifiedDate = DateTime.UtcNow;
                await campaignDbContext.SaveChangesAsync();
            }
            else
            {
                var newBlock = new Block
                {
                    FkCampaignId = campaign.Id,
                    FkBlockTypeId = (int) block.BlockType,
                    SortOrder = block.SortOrder,
                    Settings = block.Settings,
                    BlockGuid = !string.IsNullOrEmpty(block.BlockGuid) ? block.BlockGuid : Guid.NewGuid().ToString()
                };
                campaignDbContext.Blocks.Add(newBlock);
                await campaignDbContext.SaveChangesAsync();
            }
        }
    }

    private async Task UpdateCampaignTestsAsync(CampaignDto campaign)
    {
        var campaignTest = await campaignDbContext.CampaignTests
            .Include(ct => ct.CampaignTestValues)
            .FirstOrDefaultAsync(ct => ct.FkCampaignId == campaign.Id);

        if (campaign.CampaignTest != null)
        {
            if (campaignTest == null)
            {
                campaignTest = new CampaignTest()
                {
                    FkCampaignId = campaign.Id
                };
                campaignDbContext.CampaignTests.Add(campaignTest);
            }

            // Update CampaignTest properties
            campaignTest.Percentage = campaign.CampaignTest.Percentage;
            campaignTest.ComparisonType = campaign.CampaignTest.ComparisonType;
            campaignTest.Hours = campaign.CampaignTest.Hours;

            // Get a list of Types from the incoming DTO for comparison
            var incomingTypes = campaign.CampaignTest.CampaignTestValues.Select(ctv => ctv.Type).ToList();

            // Remove CampaignTestValues that are no longer present
            campaignTest.CampaignTestValues = campaignTest.CampaignTestValues
                .Where(ctv => incomingTypes.Contains(ctv.Type))
                .ToList();

            // Update or insert CampaignTestValues
            for (var index = 0; index < campaign.CampaignTest.CampaignTestValues.Count; index++)
            {
                var testValueDto = campaign.CampaignTest.CampaignTestValues[index];
                var testValue = campaignTest.CampaignTestValues.ElementAtOrDefault(index);

                if (testValue != null)
                {
                    // Update existing CampaignTestValue
                    testValue.Value = testValueDto.Value;
                }
                else
                {
                    var newTestValue = new CampaignTestValue
                    {
                        Type = testValueDto.Type,
                        Value = testValueDto.Value
                    };
                    campaignTest.CampaignTestValues.Add(newTestValue);
                }
            }
        }
        else
        {
            if (campaignTest != null)
            {
                // Remove the CampaignTest and its related CampaignTestValues
                campaignDbContext.CampaignTestValues.RemoveRange(campaignTest.CampaignTestValues);
                campaignDbContext.CampaignTests.Remove(campaignTest);
            }
        }
    }

    public async Task<ResponseDto> RenameAsync(int campaignId, string name)
    {
        var currentCampaign = await campaignDbContext.Campaigns.FirstOrDefaultAsync(a => a.Id == campaignId);

        if (currentCampaign != null)
        {
            currentCampaign.Name = name;
            await campaignDbContext.SaveChangesAsync();
        }
        else
        {
            return new ResponseDto()
            {
                Message = $"No campaign with id: {campaignId} found",
                Success = false
            };
        }

        return new ResponseDto()
        {
            Message = "Success",
            Success = true
        };
    }

    private string Preview(string campaignPreview, int campaignId)
    {
        var now = DateTime.UtcNow;
        var year = now.ToString("yyyy");

        string url = $"campaigns/{year}/{campaignId}.jpg";
        string imageData = campaignPreview.Split("base64,")[1];

        var keyString = configuration["ValyrionFtpKey"].Replace(" ", "\r\n");
        keyString = keyString.Replace("-----BEGIN\r\nRSA\r\nPRIVATE\r\nKEY-----",
            "-----BEGIN RSA PRIVATE KEY-----");
        keyString = keyString.Replace("-----END\r\nRSA\r\nPRIVATE\r\nKEY-----", "-----END RSA PRIVATE KEY-----");
        MemoryStream keyStream = new MemoryStream(Encoding.UTF8.GetBytes(keyString));
        var key = new PrivateKeyFile(keyStream);
        using (var client = new SftpClient(configuration["ValyrionFtpHost"],
                   configuration["ValyrionFtpUsername"], key))
        {
            client.Connect();

            //Year folder exists
            var folderSftpFiles = client.ListDirectory("campaigns")
                .Where(a => a.IsDirectory)
                .ToList();
            var folderExists = folderSftpFiles.SingleOrDefault(a => a.Name == year);
            if (folderExists == null)
            {
                client.CreateDirectory($"campaigns/{year}");
            }

            byte[] fileBytes = Convert.FromBase64String(imageData);
            client.UploadFile(new MemoryStream(fileBytes),
                url, true);
            client.Disconnect();
        }

        return $"https://viabilldata.blob.core.windows.net/{configuration["FilesPath"]}/{url}";
    }

    /// <summary>
    /// Delete a campaign
    /// </summary>
    /// <param name="id"></param>
    /// <returns>Response</returns>
    public async Task<ResponseDto> DeleteAsync(int id)
    {
        var campaign = await campaignDbContext.Campaigns
            .SingleOrDefaultAsync(camp => camp.Id == id);

        if (campaign == null)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error($"No campaign with id: {id} found");
            return new ResponseDto
            {
                Message = "Campaign not found",
                Success = false
            };
        }

        campaign.Active = false;
        await campaignDbContext.SaveChangesAsync();

        var cacheKey = $"GeneralService_GetCampaignsAsync_PartnerId:{campaign.FkPartnerId}_{configuration["Environment"]}";
        cacheService.RemoveData(cacheKey);

        // Here we call the cache Controller to update the campaign pagination (fire and forget)
        _ = Task.Run(async () =>
        {
            try
            {
                var secret = "hP2Am%9HMEJmHmR8jm)sX(3qjhRPxb";
                var baseUrl = configuration["ValyrionServices-BaseUrl"];
                //var baseUrl = "https://localhost:7100";
                
                // Skip if no base URL is configured
                if (string.IsNullOrEmpty(baseUrl))
                {
                    logger.ForContext("service_name", GetType().Name)
                        .Debug("Skipping campaign pagination cache update - no ValyrionServices-BaseUrl configured");
                    return;
                }

                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(10); // Reduced timeout for fire-and-forget
                httpClient.DefaultRequestHeaders.Add("X-Partner-Id", campaign.FkPartnerId.ToString());
                
                var response = await httpClient.GetAsync($"{baseUrl}/cache/updateCampaignPagination/{secret}");
                
                if (response.IsSuccessStatusCode)
                {
                    logger.ForContext("service_name", GetType().Name)
                        .Debug("Successfully updated campaign pagination cache after deleting campaign {CampaignId}", id);
                }
                else
                {
                    logger.ForContext("service_name", GetType().Name)
                        .Warning("Cache update returned status {StatusCode} for campaign {CampaignId}", response.StatusCode, id);
                }
            }
            catch (HttpRequestException ex)
            {
                logger.ForContext("service_name", GetType().Name)
                    .Debug(ex, "Cache service unavailable when trying to update pagination after deleting campaign {CampaignId}", id);
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                logger.ForContext("service_name", GetType().Name)
                    .Debug("Cache update request timed out for campaign {CampaignId}", id);
            }
            catch (Exception ex)
            {
                logger.ForContext("service_name", GetType().Name)
                    .Warning(ex, "Unexpected error updating campaign pagination cache after deleting campaign {CampaignId}", id);
            }
        });
        
        return new ResponseDto
        {
            Message = "Success",
            Success = true
        };
    }

    public async Task<ResponseDto> SendCampaignAsync(SendEmailDto sendEmail, bool localhost)
    {
        try
        {
            var campaign = await campaignDbContext.Campaigns
                /*.Include(a => a.CampaignTests)
                .ThenInclude(a => a.CampaignTestValues)*/
                .SingleAsync(a => a.Id == sendEmail.CampaignId);
            campaign.Status = "locked";
            await campaignDbContext.SaveChangesAsync();
            var start = DateTime.UtcNow;

            List<string> IndividualEmails = [];
            var newMessageObject = new MessageToBeQueued
            {
                CampaignId = sendEmail.CampaignId,
                ActionDate = start,
                Origin = "campaign",
                IsOverride = sendEmail.ForceSend,
                PartnerId = campaign.FkPartnerId
            };
            
            // To be used in the A/B Testing campaign
            /*var fromName = campaign.FromName;
            var previewText = campaign.PreviewText;
            var subject = campaign.Subject;
            var externalListMailDelay =
                Convert.ToInt32((await settingService.GetSettingAsync(campaign.FkPartnerId, "MailExternalListHoursDelay")).Value);*/

            // send Override mails to our internal receiver list
            if (configuration["CampaignEmailReceivers"] != null)
            {
                IndividualEmails.AddRange(configuration["CampaignEmailReceivers"]!.Split(',')
                    .Select(x => x.Replace(" ", string.Empty))
                    .ToList());
            }

            // send Override mails to the inserted Individual Recipients
            foreach (var email in sendEmail.IndividualRecipients.Where(email =>
                         IndividualEmails.All(a => a != email)))
            {
                IndividualEmails.Add(email);
            }

            // send Override mails to our external receiver list
            if (configuration["CampaignExternalEmailReceivers"] != null)
            {
                IndividualEmails.AddRange(configuration["CampaignExternalEmailReceivers"]!.Split(',')
                    .Select(x => x.Replace(" ", string.Empty))
                    .ToList());
            }
            
            newMessageObject.IndividualEmails = IndividualEmails;

            if (campaign.FkSegmentId != null)
            {
                // Security for not adding audience from filter on localhost
                //if(true)
                if (!localhost)
                {
                    newMessageObject.SegmentId = campaign.FkSegmentId ?? 0;
                }
            }

            // TODO A/B Testing campaign - Not implemented yet
            /*var campaignTest = campaign.CampaignTests.FirstOrDefault();
            if (campaignTest != null)
            {
                //Get percentage pr option
                var percentageSplit = campaignTest.Percentage / campaignTest.CampaignTestValues.Count;
                //Get percentage count from list
                var percentageSplitCount = (int) Math.Ceiling(contactEmails.Count * ((decimal) percentageSplit / 100));

                //Loop options
                foreach (var campaignTestValue in
                         campaignTest.CampaignTestValues.Select((value, i) => new {i, value}))
                {
                    var emails = contactEmails.Skip(percentageSplitCount * campaignTestValue.i)
                        .Take(percentageSplitCount)
                        .ToList();
                    foreach (var email in emails)
                    {
                        campaign.Status = "testing";
                        if (campaignTestValue.value.Type == "FromName")
                        {
                            fromName = campaignTestValue.value.Value;
                        }

                        if (campaignTestValue.value.Type == "Subject")
                        {
                            subject = campaignTestValue.value.Value;
                        }

                        if (campaignTestValue.value.Type == "PreviewText")
                        {
                            previewText = campaignTestValue.value.Value;
                        }

                        if (campaignMessages.Any(a => a.Email == email)) continue;
                        campaignMessages.Add(new Message
                        {
                            Email = email,
                            EmailGuid = Guid.NewGuid(),
                            CampaignId = sendEmail.CampaignId,
                            ActionDate = start,
                            Origin = "campaign",
                            Priority = 1,
                            FromName = fromName,
                            PreviewText = previewText,
                            Subject = subject,
                            CampaignTestValueId = campaignTestValue.value.Id,
                            IsOverride = sendEmail.ForceSend,
                            PartnerId = campaign.FkPartnerId
                        });
                    }
                }

                contactEmails = [];
            }*/
            
            var partnerName = await partnerService.GetPartnerName();
            
            using (var publishChannel = rabbitConnectionCloud.CreateModel())
            {
                publishChannel.ConfirmSelect();

                SendMessage(publishChannel, newMessageObject, partnerName);
            }

            await campaignDbContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error sending campaign by with id: {CampaignId}", sendEmail.CampaignId);
            throw;
        }

        return new ResponseDto()
        {
            Success = true
        };
    }

    public async Task<SimpleDataResponseDto> PreviewCampaignAsync(int campaignId)
    {
        var clients =
            "applemail15,applemail15_dm,gmailw10_chr26_win,gmailw10dm_chr26_win,android10_gmailapp_pixel4_lm,android10_gmailapp_pixel4_dm,android13_gmailapp_pixel7_dm,android13_gmailapp_pixel7_lm," +
            "ipadpro_11_15,iphone12_15,iphone12_15_dm,iphone15_17,iphone15_17_dm,o365_m_lm_dt,o365_m_dm_dt,m365_mac13_lm_dt,m365_mac13_dm_dt,outlook2021_win11_lm_dt,outlook2021_win11_dm_dt," +
            "android12_outlookapp_pixel6_lm,android12_outlookapp_pixel6_dm,ybw10_chr26_win";
        var httpClientHandler = new HttpClientHandler
            {ServerCertificateCustomValidationCallback = (message, cert, chain, sslPolicyErrors) => true};
        var httpClient = new HttpClient(httpClientHandler);
        httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0");
        var baseUrl = string.Empty;
        
        var partnerId = partnerContext.PartnerId;
        if (partnerId == 87317)
        {
            baseUrl = $"{configuration["MessageWorker-Url-HappyPay"]}";
        }
        else
        {
            baseUrl = $"{configuration["MessageWorker-Url"]}";
        }

        var response =
            await httpClient.GetAsync($"{baseUrl}/previewCampaign/{campaignId}/{clients}");
        return new SimpleDataResponseDto
        {
            Data = await response.Content.ReadAsStringAsync()
        };
    }

    public async Task<List<ClientDetails>> PreviewCampaignResultAsync(string testId)
    {
        var httpClient = new HttpClient();
        var byteArray =
            Encoding.ASCII.GetBytes($"{configuration["EmailOnAcid-ApiKey"]}:{configuration["EmailOnAcid-Password"]}");
        string base64String = Convert.ToBase64String(byteArray);
        httpClient.DefaultRequestHeaders.Authorization =
            new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", base64String);
        var rawResponse = await httpClient.GetAsync($"https://api.emailonacid.com/v5/email/tests/{testId}/results");
        var body = await rawResponse.Content.ReadAsStringAsync();
        var emailOnAcidDto = JsonConvert.DeserializeObject<EmailOnAcidDto>(body);

        var clients = new List<ClientDetails>();
        if (emailOnAcidDto != null)
        {
            foreach (var client in emailOnAcidDto)
            {
                clients.Add(client.Value);
            }
        }

        return clients;
    }

    public async Task<ResponseDto> SendTestCampaignAsync(int partnerId, TestEmailDto testEmail)
    {
        try
        {
            /*List<Message> messages = [];
            foreach (var recipientEmail in testEmail.Recipients)
            {
                var messageProducts = new List<MessageProduct>();

                for (int i = 1; i <= 20; i++)
                {
                    messageProducts.Add(new MessageProduct
                    {
                        InternalProductId = "b8e7e8ea-b330-432a-a9b3-f44cf0ddcd59", Score = 100,
                        RecommendedBy = new List<string> {"n/a"}
                    });
                }

                var message = new Message
                {
                    Email = recipientEmail,
                    EmailGuid = Guid.NewGuid(),
                    CampaignId = testEmail.CampaignId,
                    Origin = "campaign",
                    Priority = 1,
                    IsTest = true,
                    ActionDate = TimeZoneInfo.ConvertTimeToUtc(DateTime.UtcNow.AddMinutes(-1)),
                    MessageProducts = messageProducts,
                    /*FromName = campaign.FromName,
                    PreviewText = campaign.PreviewText,
                    Subject = campaign.Subject#1#
                    PartnerId = partnerId
                };

                messages.Add(message);
            }*/
            
            var newMessageToQueue = new MessageToBeQueued
            {
                CampaignId = testEmail.CampaignId,
                ActionDate = DateTime.UtcNow,
                Origin = "campaign",
                IsOverride = true,
                IsTest = true,
                PartnerId = partnerId,
                IndividualEmails = testEmail.Recipients
            };

            if (partnerId == 0)
            {
                newMessageToQueue.PartnerId = partnerContext.PartnerId;
            }

            //await SendMessagesToQueue(messages);

            var partnerName = await partnerService.GetPartnerName();
            
            using (var publishChannel = rabbitConnectionCloud.CreateModel())
            {
                publishChannel.ConfirmSelect();

                SendMessage(publishChannel, newMessageToQueue, partnerName);
            }
        }
        catch (Exception e)
        {
            logger.Error(e, "Error sending test campaign by with id: {CampaignId}", testEmail.CampaignId);
            throw;
        }

        return new ResponseDto()
        {
            Success = true
        };
    }

    /// <summary>
    /// Create a campaign block
    /// </summary>
    /// <returns>Response</returns>
    public async Task<BlockDto> CreateBlockAsync(BlockDto block, int campaignId)
    {
        var blockType = await campaignDbContext.BlockTypes.SingleOrDefaultAsync(a => a.Name == block.BlockType.ToString());
        if (blockType == null)
        {
            throw new Exception($"Block type {block.BlockType} not found");
        }

        var newBlock = new Block()
        {
            FkCampaignId = campaignId,
            FkBlockTypeId = blockType.Id,
            SortOrder = block.SortOrder,
            Settings = block.Settings,
            BlockGuid = block.BlockGuid
        };

        campaignDbContext.Blocks.Add(newBlock);
        await campaignDbContext.SaveChangesAsync();
        block.Id = newBlock.Id;
        return block;
    }


    /// <summary>
    /// Get a Setting By Its Setting Name
    /// </summary>
    /// <returns>Response</returns>
    /*public async Task<string> GetSettingByName(string settingName)
    {
        return (await _campaignDbContext.Settings.SingleOrDefaultAsync(a => a.Name == settingName))?.Value ?? "";
    }*/
    public async Task<List<Image>> GetImagesAsync(int? merchantId)
    {
        if (merchantId == 0)
        {
            merchantId = null;
        }

        return await campaignDbContext.Images.Where(a =>
                a.Active == true && a.FkMerchantId == merchantId && a.FkPartnerId == partnerContext.PartnerId)
            .ToListAsync();
    }

    public async Task<Image> CreateImagesAsync(int partnerId, ImageCreateDto image)
    {
        var url = imageService.CreateImages(image.Name, image.Data, "images");

        var imageObject = new Image
        {
            Url = url,
            Active = true,
            FkMerchantId = image.MerchantId == 0 ? null : image.MerchantId,
            FkPartnerId = partnerId
        };
        await campaignDbContext.AddAsync(imageObject);
        await campaignDbContext.SaveChangesAsync().ConfigureAwait(false);
        return imageObject;
    }

    public async Task<Image> DeleteImagesAsync(long id)
    {
        var image = await campaignDbContext.Images.SingleAsync(a => a.Id == id);
        image.Active = false;
        await campaignDbContext.SaveChangesAsync();
        return image;
    }

    public async Task CampaignTesting()
    {
        var campaigns = campaignDbContext.Campaigns
            .Include(a => a.CampaignTests)
            .ThenInclude(a => a.CampaignTestValues)
            .Where(a => a.Status == "testing" && a.CampaignTests.Any()).ToList();
        foreach (var campaign in campaigns)
        {
            var messagesInQue = await messageService.MessagesWithLowerActionDate(campaign.Id, "new");
            if (messagesInQue == 0)
            {
                var messages = await messageService.MessagesSent(campaign.Id);

                var contactEmails = new List<string>();
                contactEmails.AddRange(
                    await filterService.GetAllFilterEmailsAsync(Convert.ToInt32(campaign.FkFilterId)));

                var campaignTest = campaign.CampaignTests.FirstOrDefault();
                //Get percentage pr option
                var percentageSplit = campaignTest.Percentage / campaignTest.CampaignTestValues.Count;
                //Get percentage count from list
                var percentCount = (int) Math.Floor(contactEmails.Count * ((decimal) percentageSplit / 100));
            }

            Console.WriteLine();
            //TODO
        }

        Console.WriteLine();
    }

    public async Task UpdateCampaignStats()
    {
        //TODO REMOVE
        /*var newer = DateTime.UtcNow.AddDays(-14);
        var campaigns = _campaignDbContext.Campaigns
            .Include(a => a.CampaignStats)
            .Where(a => ((a.Status == "locked" || a.Status == "testing") || (a.CampaignStats.Any() &&
            a.CampaignStats.First().LastMailSent != null  &&
            a.CampaignStats.First().LastMailSent!.Value > newer && a.Status == "done"))
                        && a.Active == true)
            .ToList();

        foreach (var campaign in campaigns)
        {
            if (campaign.CampaignStats.Count == 0)
            {
                campaign.CampaignStats = new List<CampaignStat> { new() };
            }

            var totalQueueLeft = await _messageService.QueueSize(campaign.Id);
            if (totalQueueLeft == 0 && campaign.Status != "done")
            {
                campaign.Status = "done";
                campaign.CampaignStats.First().LastMailSent = DateTime.UtcNow;
            }

            campaign.CampaignStats.First().Audience = await _messageService.Messages(campaign.Id);
            campaign.CampaignStats.First().Delivered = await _messageService.Messages(campaign.Id, "sent");
            if (campaign.CampaignStats.First().Delivered != 0)
            {
                campaign.CampaignStats.First().Unsubscribe = await _customerService.UnsubscribedCustomers(campaign.Id);
                campaign.CampaignStats.First().UnsubscribeRate = Math.Round(
                    campaign.CampaignStats.First().Unsubscribe /
                    (decimal)campaign.CampaignStats.First().Delivered * 100, 2);
                campaign.CampaignStats.First().Opens =
                    (int)await _elasticCampaignMailOpenService.UniqueOpens(null, null, null, campaign.Id);
                campaign.CampaignStats.First().OpenRate = Math.Round(
                    campaign.CampaignStats.First().Opens / (decimal)campaign.CampaignStats.First().Delivered * 100,
                    2);
                campaign.CampaignStats.First().Clicks = (int)await _elasticCampaignMailClickService.UniqueClicks(null,null,null, campaign.Id);
                campaign.CampaignStats.First().ClickRate = Math.Round(
                    campaign.CampaignStats.First().Clicks / (decimal)campaign.CampaignStats.First().Delivered *
                    100, 2);
            }
        }

        await _campaignDbContext.SaveChangesAsync().ConfigureAwait(false);*/
    }

    public async Task<List<CampaignClickInsightDto>> GetCampaignClickInsightAsync(int campaignId)
    {
        return await elasticCampaignMailClickService.ClicksCampaignInsight(campaignId);
    }
    
    public async Task<List<string>> GetEmailsBySegmentIdAsync(int segmentId)
    {
        return await segmentService.CalculateAsync(segmentId);
    }

    public async Task<List<CampaignDto>> GetByIdsAsync(List<int> campaignIds)
    {
        var partnerId = partnerContext.PartnerId;

        return await campaignDbContext.Campaigns
            .Where(x => x.Active && x.FkPartnerId == partnerId && campaignIds.Contains(x.Id))
            .OrderByDescending(a => a.LastModifiedDate)
            .Select(campaign => new CampaignDto
            {
                Id = campaign.Id,
                Name = campaign.Name,
                PreviewText = campaign.PreviewText,
                FromName = campaign.FromName,
                Subject = campaign.Subject,
                LastModifiedDate = campaign.LastModifiedDate,
                CampaignType = Enum.Parse<CampaignType>(campaign.CampaignType),
                FkSegmentId = campaign.FkSegmentId ?? 0,
                Status = campaign.Status
            })
            .ToListAsync()
            .ConfigureAwait(false);
    }

    public async Task<int> GetPartnerIdByCampaignIdAsync(int campaignId)
    {
        return await campaignDbContext.Campaigns
            .AsNoTracking()
            .Where(x => x.Id == campaignId)
            .Select(x => x.FkPartnerId)
            .FirstOrDefaultAsync();
    }

    public async Task<List<Models.ModelsDal.Campaign.Campaign>> GetAllStatsAsync(int partnerId)
    {
        return await campaignDbContext.Campaigns
            .Where(x => x.FkPartnerId == partnerId)
            .ToListAsync();
    }

    public async Task UpdateStatusAsync(int id, string status)
    {
        var campaign = await campaignDbContext.Campaigns.SingleAsync(x => x.Id == id);
        campaign.Status = status;
        await campaignDbContext.SaveChangesAsync();
    }

    public async Task<List<CampaignDto>> GetAllFromCurrentYearAsync(int partnerId)
    {
        var currentYear = DateTime.UtcNow.Year;
        var firstDayOfYear = new DateTime(currentYear, 1, 1);
        return await campaignDbContext.Campaigns
            .Where(x => x.FkPartnerId == partnerId && x.LastModifiedDate >= firstDayOfYear)
            .Select(campaign => new CampaignDto
            {
                Id = campaign.Id,
                Name = campaign.Name,
                PreviewText = campaign.PreviewText,
                FromName = campaign.FromName,
                Subject = campaign.Subject,
                LastModifiedDate = campaign.LastModifiedDate,
                CampaignType = Enum.Parse<CampaignType>(campaign.CampaignType)
            })
            .ToListAsync();
    }

    private async Task SendMessagesToQueue(List<Message> messages)
    {
        // Define the chunk size
        const int chunkSize = 20;

        // Split the messages into chunks
        var chunks = messages
            .Select((message, index) => new {message, index})
            .GroupBy(x => x.index / chunkSize)
            .Select(group => group.Select(x => x.message).ToList())
            .ToList();

        // Send each chunk
        foreach (var serializedMessages in chunks.Select(chunk =>
                     chunk.Select(message => JsonSerializer.Serialize(message)).ToList()))
        {
            //await messagingService.SendMessagesAsync(serializedMessages, "campaign_mail_new");
        }
    }
    
    private static void SendMessage(IModel publishChannel, MessageToBeQueued message, string partnerName)
    {
        partnerName = string.Concat(partnerName.Where(c => !char.IsWhiteSpace(c)));
        var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(message));
        publishChannel.BasicPublish(exchange: "campaign",
            routingKey: $"campaign_mail_new_{partnerName.ToLower()}",
            basicProperties: null,
            body: actionBody);

        publishChannel.WaitForConfirmsOrDie(new TimeSpan(0, 0, 30));
    }

    private static void SendMessages(IModel publishChannel, List<Message> messages)
    {
        var actionBody = Encoding.UTF8.GetBytes(JsonSerializer.Serialize(messages));
        publishChannel.BasicPublish(exchange: "campaign",
            routingKey: "campaign_mail_new",
            basicProperties: null,
            body: actionBody);

        publishChannel.WaitForConfirmsOrDie(new TimeSpan(0, 0, 30));
    }

}