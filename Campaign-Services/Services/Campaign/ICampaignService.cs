using AutoMapper;
using Campaign_Service.Models.ModelsDal.Campaign;
using Campaign_Services.Models.ModelsDal.Campaign;
using Shared.Dto.Campaign;
using Shared.Dto.Campaign.Enums;
using Shared.Dto.EmailOnAcid;
using Shared.Dto.Image;
using Shared.Dto.Partner;
using Shared.Models;

namespace Campaign_Services.Services.Campaign
{
    public interface ICampaignService
    {
        Task<List<CampaignDto>> GetAllAsync(int partnerId);

        Task<List<Models.ModelsDal.Campaign.Campaign>> GetAllFullAsync(int partnerId);

        //Task<IEnumerable<CampaignDto>> GetAllTemplatesAsync();
        Task<List<QueuedCampaigns>> GetAllQueuedCampaignsAsync(int partnerId);
        Task<ResponseDto> DisregardQueuedCampaign(int campaignId);
        Task<ResponseDto> ChangePriorityOfQueuedCampaigns(List<QueuedCampaigns> queuedCampaigns);
        Task<DateTime> QueueEmptyEstimate(int partnerId);

        Task<CampaignPaginationDto> GetAllAsync(int partnerId, int page, int size, string filter, IMapper mapper,
            CampaignType campaignType, string sortName, string sortOrder, string searchString);

        Task<CampaignDto> GetAsync(int id);

        Task<Dictionary<int, string>> GetNameAsync(List<int> ids);

        //Task<List<int>> GetCampaignIdsByGroupIdAsync(int campaignGroupId);
        //Task<List<Models.ModelsDal.Campaign.Campaign>> GetCampaignGroupsAsync();
        //Task<ResponseDto> DeleteCampaignGroupAsync(int id);
        Task<Models.ModelsDal.Campaign.Filter> GetFilterAsync(int id);
        Task<List<Models.ModelsDal.Campaign.Filter>> GetFilterPresetAsync();
        Task<List<FilterTemplate>> GetAllFilterTemplatesAsync();
        Task<CampaignResponse> CreateAsync(int partnerId, CampaignDto campaign);
        Task<CampaignDto> CreateTemplateAsync(int id);
        Task<ResponseDto> UseTemplateAsync(int id, int templateId);
        Task<CampaignResponse> DuplicateAsync(int id);
        Task UpdateAsync(Models.ModelsDal.Campaign.Campaign campaign);
        Task<ResponseDto> UpdateAsync(CampaignDto campaign, bool forceUpdatePreview = true);
        Task<ResponseDto> RenameAsync(int campaignId, string name);
        Task<ResponseDto> DeleteAsync(int id);
        Task<ResponseDto> SendCampaignAsync(SendEmailDto email, bool localhost);
        Task<SimpleDataResponseDto> PreviewCampaignAsync(int campaignId);
        Task<List<ClientDetails>> PreviewCampaignResultAsync(string testId);
        Task<ResponseDto> SendTestCampaignAsync(int partnerId, TestEmailDto testEmail);

        Task<BlockDto> CreateBlockAsync(BlockDto block, int CampaignId);

        //Task<string> GetSettingByName(string settingName);
        //Images
        Task<List<Image>> GetImagesAsync(int? merchantId);
        Task<Image> CreateImagesAsync(int partnerId, ImageCreateDto image);
        Task<Image> DeleteImagesAsync(long id);
        Task CampaignTesting();
        Task UpdateCampaignStats();
        Task<List<CampaignClickInsightDto>> GetCampaignClickInsightAsync(int campaignId);
        Task<List<string>> GetEmailsBySegmentIdAsync(int segmentId);
        Task<List<CampaignDto>> GetByIdsAsync(List<int> campaignIds);
        Task<int> GetPartnerIdByCampaignIdAsync(int campaignId);
        Task<List<Models.ModelsDal.Campaign.Campaign>> GetAllStatsAsync(int partnerId);
        Task UpdateStatusAsync(int id, string status);
        Task<List<CampaignDto>> GetAllFromCurrentYearAsync(int partnerId);
    }
}