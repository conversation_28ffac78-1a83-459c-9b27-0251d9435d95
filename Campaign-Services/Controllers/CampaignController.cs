using System.Net;
using AutoMapper;
using Campaign_Services.Models.ModelsDal.Campaign;
using Campaign_Services.Services.Campaign;
using Campaign_Services.Services.Filter;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SerilogTimings.Extensions;
using Shared.Dto.Campaign;
using Shared.Dto.Campaign.Enums;
using Shared.Dto.Image;
using Shared.Models;
using Shared.Services;
using ILogger = Serilog.ILogger;

namespace Campaign_Services.Controllers;

[Authorize(Roles = "valyrion")]
[Route("[controller]")]
[ApiController]
public class CampaignController : ControllerBase
{
    private readonly ILogger _logger;
    private readonly IMapper _mapper;
    private readonly ICampaignService _campaignService;
    private readonly IFilterService _filterService;

    public CampaignController(ILogger logger, ICampaignService campaignService, IFilterService filterService)
    {
        _logger = logger;
        _campaignService = campaignService;
        _filterService = filterService;
        var config = new MapperConfiguration(cfg =>
        {
            // Campaign
            //cfg.CreateMap<CampaignGroup, CampaignGroupDto>().ReverseMap();
            cfg.CreateMap<FilterValue, FilterValueDto>().ReverseMap();
            cfg.CreateMap<FilterTemplate, FilterTemplateDto>().ReverseMap();
            cfg.CreateMap<FilterDto, Filter>().ReverseMap();
            cfg.CreateMap<BlockDto, Block>().ReverseMap();
            cfg.CreateMap<CampaignStat, CampaignStatDto>().ReverseMap();
        });
        _mapper = config.CreateMapper();
    }

    [HttpGet]
    [AllowAnonymous]
    [Route("external/{id:int}")]
    public async Task<IActionResult> GetByIdAsync(int id)
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetByIdAsync"))
                {
                    var success = await _campaignService.GetAsync(id)
                        .ConfigureAwait(false);
                    return Ok(success);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while retrieving campaign by id {Id}", id);
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    /*[HttpGet]
    [AllowAnonymous]
    [Route("external/campaignGroup/{id}")]
    public async Task<IActionResult> GetCampaignGroup(int id)
    {
        try
        {
            if (Validate.ValidateInternalKey(Request.Headers.FirstOrDefault(a => a.Key.ToLower() == "apikey")
                    .Value.ToString()))
            {
                using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                           .TimeOperation("Request {0}", "GetCampaignGroup"))
                {
                    var success = await _campaignService.GetCampaignIdsByGroupIdAsync(id)
                        .ConfigureAwait(false);
                    return Ok(success);
                }
            }

            return Forbid();
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error while retrieving GetCampaignGroup by id {Id}", id);
            return StatusCode(500, new ResponseDto() { Message = ex.InnerException?.Message, Success = false });
        }
    }*/

    /*
     * External end
     */

    [HttpGet]
    [Route("{id:int}")]
    public async Task<IActionResult> GetAsync(int id)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetCampaignGroup"))
            {
                var result = await _campaignService.GetAsync(id).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error extracting campaign with id: {id}");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetAllAsync([FromHeader] int partnerId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllAsync"))
            {
                var result = await _campaignService.GetAllAsync(partnerId).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error extracting all campaigns");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }


    /// <summary>
    /// Get all campaign templates
    /// </summary>
    /// <returns>Response</returns>
    /*[HttpGet]
    [Route("templates")]
    public async Task<IActionResult> GetAllTemplatesAsync()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllTemplatesAsync"))
            {
                var result = await _campaignService.GetAllTemplatesAsync().ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error extracting all campaigns templates");
            return StatusCode(500, new ResponseDto() { Message = ex.InnerException?.Message, Success = false });
        }
    }*/
    [HttpGet]
    [Route("pagination/{page}/{size}/{filter}/{campaignType}/{sortName}/{sortOrder}")]
    public async Task<IActionResult> GetAllWithPaginationAsync([FromHeader] int partnerId, int page, int size, string filter,
        CampaignType campaignType, string sortName, string sortOrder, [FromQuery] string searchString = "")
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllWithPaginationAsync"))
            {
                var result = await _campaignService.GetAllAsync(partnerId, page, size, filter, _mapper, campaignType, sortName,
                        sortOrder, searchString)
                    .ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error extracting all campaigns");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    /// <summary>
    /// Get All Queued Campaigns
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Route("Queue")]
    public async Task<IActionResult> GetAllQueuedCampaignsAsync([FromHeader] int partnerId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllQueuedCampaignsAsync"))
            {
                var result = await _campaignService.GetAllQueuedCampaignsAsync(partnerId).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error extracting all campaigns in queue");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    /// <summary>
    /// Disregard the rest of a campaign from the queue by its Id 
    /// </summary>
    /// <returns></returns>
    [HttpDelete]
    [Route("disregardQueuedCampaign/{campaignId:int}")]
    public async Task<IActionResult> DisregardQueuedCampaign(int campaignId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "DisregardQueuedCampaign"))
            {
                var result = await _campaignService.DisregardQueuedCampaign(campaignId).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error DisregardQueuedCampaign");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }


    /// <summary>
    /// Change the priority of queued campaigns
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Route("changePriority")]
    public async Task<IActionResult> ChangeQueuePriority(List<QueuedCampaigns> queuedCampaigns)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ChangeQueuePriority"))
            {
                var result = await _campaignService.ChangePriorityOfQueuedCampaigns(queuedCampaigns);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error changing priority of campaigns");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    /// <summary>
    /// Get the estimated time when queue is empty
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Route("queueEmptyEstimate")]
    public async Task<IActionResult> QueueEmptyEstimate([FromHeader] int partnerId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "ChangeQueuePriority"))
            {
                var result = await _campaignService.QueueEmptyEstimate(partnerId);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error changing priority of campaigns");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    /// <summary>
    /// Create a campaign
    /// </summary>
    /// <param name="partnerId"></param>
    /// <param name="campaign"></param>
    /// <returns>Response</returns>
    [HttpPost]
    public async Task<IActionResult> CreateAsync([FromHeader] int partnerId, CampaignDto campaign)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllAsync"))
            {
                var result = await _campaignService.CreateAsync(partnerId, campaign).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error creating campaign with name: {campaign.Name}");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    /// <summary>
    /// Duplicate a campaign
    /// </summary>
    /// <param name="id"></param>
    /// <returns>Response</returns>
    [HttpGet]
    [Route("template/create/{id:int}")]
    public async Task<IActionResult> CreateTemplateAsync(int id)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer")
                       .TimeOperation("Request {0}", "DuplicateCampaignAsync"))
            {
                var result = await _campaignService.CreateTemplateAsync(id).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error duplicating campaign with Id: {id}");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    /// <summary>
    /// Duplicate a campaign
    /// </summary>
    /// <param name="id"></param>
    /// <param name="templateId"></param>
    /// <returns>Response</returns>
    [HttpGet]
    [Route("template/use/{id:int}/{templateId:int}")]
    public async Task<IActionResult> UseTemplateAsync(int id, int templateId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer")
                       .TimeOperation("Request {0}", "UseTemplateAsync"))
            {
                var result = await _campaignService.UseTemplateAsync(id, templateId).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error overriding campaign: {id} with template: {templateId}");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    /// <summary>
    /// Duplicate a campaign
    /// </summary>
    /// <param name="id"></param>
    /// <returns>Response</returns>
    [HttpGet]
    [Route("duplicate/{id:int}")]
    public async Task<IActionResult> DuplicateAsync(int id)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer")
                       .TimeOperation("Request {0}", "DuplicateCampaignAsync"))
            {
                var result = await _campaignService.DuplicateAsync(id).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error duplicating campaign with Id: {id}");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    [HttpPut]
    public async Task<IActionResult> UpdateAsync(CampaignDto campaignDto)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllAsync"))
            {
                var result = await _campaignService.UpdateAsync(campaignDto).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error updating campaign with Id: {campaignDto.Id}");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    /// <summary>
    /// Rename a campaign
    /// </summary>
    /// <param name="rename"></param>
    /// <returns>Response</returns>
    [HttpPut]
    [Route("Rename")]
    public async Task<IActionResult> RenameAsync(RenameCampaignDto rename)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").TimeOperation("Request {0}", "RenameCampaignAsync"))
            {
                var result = await _campaignService.RenameAsync(rename.Id, rename.Name)
                    .ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error renaming campaign with Id: {rename.Id}");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    /// <summary>
    /// Delete a campaign
    /// </summary>
    /// <param name="id"></param>
    /// <returns>Response</returns>
    [HttpDelete]
    [Route("{id:int}")]
    public async Task<IActionResult> DeleteAsync(int id)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "DeleteCampaignAsync"))
            {
                var result = await _campaignService.DeleteAsync(id).ConfigureAwait(false);

                return !result.Success ? StatusCode(500, result) : Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error deleting campaign with id: {id}");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    /// <summary>
    /// Create a Campaign Block
    /// </summary>
    /// <param name="block"></param>
    /// <param name="CampaignId"></param>
    /// <returns>Response</returns>
    [HttpPost]
    [Route("block/{CampaignId:int}")]
    public async Task<IActionResult> CreateBlockAsync(BlockDto block, int CampaignId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CreateCampaignBlockAsync"))
            {
                var result = await _campaignService.CreateBlockAsync(block, CampaignId)
                    .ConfigureAwait(false);

                if (result.Id == 0)
                    return StatusCode(500);

                //return Ok(_mapper.Map<Block, BlockDto>(result));
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, $"Error creating block with guid: {block.BlockGuid}");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    [HttpPost]
    [Route("sendEmailCampaign")]
    public async Task<IActionResult> SendEmailCampaignAsync(SendEmailDto email)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "SendEmailCampaignAsync"))
            {
                var localhost = HttpContext.Connection.RemoteIpAddress != null &&
                                IPAddress.IsLoopback(HttpContext.Connection.RemoteIpAddress);
                var result = await _campaignService.SendCampaignAsync(email, localhost).ConfigureAwait(false);

                if (!result.Success)
                    return StatusCode(202, new ErrorDto("Mails not sent to: " + result));

                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error sending campaign with id {CampaignId}", email.CampaignId);
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    [HttpGet]
    [Route("previewCampaign/{campaignId}")]
    public async Task<IActionResult> PreviewCampaignAsync(int campaignId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PreviewCampaignAsync"))
            {
                var result = await _campaignService.PreviewCampaignAsync(campaignId).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error previewing campaign with id {CampaignId}", campaignId);
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    [HttpGet]
    [Route("previewCampaignResult/{testId}")]
    public async Task<IActionResult> PreviewCampaignResultAsync(string testId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "PreviewCampaignResultAsync"))
            {
                var result = await _campaignService.PreviewCampaignResultAsync(testId).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error getting previewing test with id {TestId}", testId);
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    [HttpPost]
    [Route("sendTestEmail")]
    public async Task<IActionResult> SendTestEmailAsync([FromHeader] int partnerId, TestEmailDto testEmail)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "SendTestEmailAsync"))
            {
                var result = await _campaignService.SendTestCampaignAsync(partnerId, testEmail).ConfigureAwait(false);

                if (!result.Success)
                    return StatusCode(202, new ErrorDto("Error sending campaign with id: " + result));

                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error sending test campaign with id {CampaignId}", testEmail.CampaignId);
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    /*
     * Start Campaign Groups
     */

    /*[HttpGet]
    [Route("campaignGroups")]
    public async Task<IActionResult> GetCampaignGroupsAsync()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetCampaignGroup"))
            {
                var success = await _campaignService.GetCampaignGroupsAsync()
                    .ConfigureAwait(false);
                return Ok(_mapper.Map<List<CampaignGroup>, List<CampaignGroupDto>>(success));
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, "Error extracting All Campaign Groups");
            return StatusCode(500, new ResponseDto() { Message = ex.InnerException?.Message, Success = false });
        }
    }*/

    /*[HttpPost]
    [Route("campaignGroup")]
    public async Task<IActionResult> CreateCampaignGroupAsync(CampaignGroupDto campaignGroup)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CreateCampaignGroupAsync"))
            {
                var success = await _campaignService.GetCampaignGroupsAsync()
                    .ConfigureAwait(false);
                return Ok(_mapper.Map<List<CampaignGroup>, List<CampaignGroupDto>>(success));
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error while creating campaign group with name: {campaignGroup.Name}");
            return StatusCode(500, new ResponseDto() { Message = ex.InnerException?.Message, Success = false });
        }
    }*/

    /*[HttpPut]
    [Route("campaignGroup")]
    public async Task<IActionResult> UpdateCampaignGroupAsync(CampaignGroupDto campaignGroup)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateCampaignGroupAsync"))
            {
                var success = await _campaignService.GetCampaignGroupsAsync()
                    .ConfigureAwait(false);
                return Ok(_mapper.Map<List<CampaignGroup>, List<CampaignGroupDto>>(success));
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error while updating campaign group with name: {campaignGroup.Name}");
            return StatusCode(500, new ResponseDto() { Message = ex.InnerException?.Message, Success = false });
        }
    }*/

    /*[HttpDelete]
    [Route("campaignGroup/{id:int}")]
    public async Task<IActionResult> DeleteCampaignGroupAsync(int id)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "DeleteCampaignGroupAsync"))
            {
                var result = await _campaignService.DeleteCampaignGroupAsync(id)
                    .ConfigureAwait(false);
                return !result.Success ? StatusCode(500, result) : Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error deleting campaign group with id: {id}");
            return StatusCode(500, new ResponseDto() { Message = ex.InnerException?.Message, Success = false });
        }
    }*/

    /*
     * End Campaign Groups
     */

    /*
     * Start Filter
     */

    [HttpGet]
    [Route("filter/{id:int}")]
    public async Task<IActionResult> GetFilterAsync(int id)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetFilterAsync"))
            {
                var result = await _campaignService.GetFilterAsync(id).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error extracting filters async");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    [HttpGet]
    [Route("filterPreset")]
    public async Task<IActionResult> GetFilterPresetAsync()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetFilterPresetAsync"))
            {
                var result = await _campaignService.GetFilterPresetAsync().ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error extracting filterPreset");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    [HttpGet]
    [Route("filterTemplates")]
    public async Task<IActionResult> GetAllFilterTemplatesAsync()
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllFilterTemplatesAsync"))
            {
                var result = await _campaignService.GetAllFilterTemplatesAsync().ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error extracting all filter templates");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    /*[HttpPost]
    [Route("affinity")]
    public async Task<IActionResult> GetAffinityAsync(FilterValueDto filterValueDto)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllFilterTemplatesAsync"))
            {
                var filterValue = _mapper.Map<FilterValueDto, FilterValue>(filterValueDto);
                var result = await _filterService.GetAffinityAsync(filterValue).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error extracting all filter templates");
            return StatusCode(500, new ResponseDto() { Message = ex.InnerException?.Message, Success = false });
        }
    }*/

    [HttpPost]
    [Route("filterEmailFound")]
    public async Task<IActionResult> GetAllFilterEmailCountAsync(FilterDto filterDto)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllFilterEmailCountAsync"))
            {
                var filters = _mapper.Map<FilterDto, Filter>(filterDto);
                var result = await _filterService.GetAllFilterEmailsAsync(filters).ConfigureAwait(false);
                Console.WriteLine(result.Count);
                return Ok(result.Count);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error extracting GetAllFilterEmailCountAsync");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    [HttpGet]
    [Route("filterEmailFound/{filterId}")]
    public async Task<IActionResult> GetAllFilterEmailsAsync(int filterId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllFilterEmailsAsync"))
            {
                var result = await _filterService.GetAllFilterEmailsAsync(filterId).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error extracting GetAllFilterEmailsAsync");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    [HttpPost]
    [Route("filter")]
    public async Task<IActionResult> CreateFilterAsync([FromHeader] int partnerId, FilterDto filterDto)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetAllFilterAsync"))
            {
                var filter = _mapper.Map<FilterDto, Filter>(filterDto);
                filter.FkPartnerId = partnerId;
                var result = await _filterService.CreateFilterAsync(filter)
                    .ConfigureAwait(false);
                return Ok(_mapper.Map<Filter, FilterDto>(result));
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error Creating Audience Filter");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    [HttpPut]
    [Route("filter")]
    public async Task<IActionResult> UpdateFilterAsync([FromHeader] int partnerId, FilterDto filterDto)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "UpdateFilterAsync"))
            {
                var filter = _mapper.Map<FilterDto, Filter>(filterDto);
                filter.FkPartnerId = partnerId;
                var result = await _filterService.UpdateFilterAsync(filter).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error UpdateFilterAsync");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }

    /*
     * End Filter
     */

    //Images
    [HttpGet]
    [Route("image/{merchantId}")]
    public async Task<IActionResult> GetImagesAsync(int merchantId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetImagesAsync"))
            {
                var images = await _campaignService.GetImagesAsync(merchantId)
                    .ConfigureAwait(false);
                return Ok(images);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error GetImagesAsync");
            return Ok();
        }
    }

    [HttpPost]
    [Route("image")]
    public async Task<IActionResult> CreateImageAsync([FromHeader] int partnerId, ImageCreateDto image)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "CreateImageAsync"))
            {
                var images = await _campaignService.CreateImagesAsync(partnerId, image)
                    .ConfigureAwait(false);
                return Ok(images);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error CreateImageAsync");
            return Ok();
        }
    }

    [HttpDelete]
    [Route("image/{id}")]
    public async Task<IActionResult> DeleteImageAsync(long id)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "DeleteImageAsync"))
            {
                var images = await _campaignService.DeleteImagesAsync(id)
                    .ConfigureAwait(false);
                return Ok(images);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error DeleteImageAsync");
            return Ok();
        }
    }

    [HttpGet]
    [Route("ClickInsight/{campaignId:int}")]
    public async Task<IActionResult> GetCampaignClickInsightAsync(int campaignId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetCampaignClickInsightAsync"))
            {
                var result = await _campaignService.GetCampaignClickInsightAsync(campaignId).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error extracting campaign click insight with campaignId: {campaignId}");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }
    
    [HttpGet]
    [Route("emailsBySegment/{segmentId:int}")]
    [AllowAnonymous]
    public async Task<IActionResult> GetCampaignEmailsAsync(int segmentId)
    {
        try
        {
            using (_logger.ForContext("serilog_logtype", "timer").ForContext("service_name", GetType().Name)
                       .TimeOperation("Request {0}", "GetCampaignEmailsAsync"))
            {
                var result = await _campaignService.GetEmailsBySegmentIdAsync(segmentId).ConfigureAwait(false);
                return Ok(result);
            }
        }
        catch (Exception ex)
        {
            _logger.ForContext("service_name", GetType().Name)
                .Error(ex, $"Error extracting campaign emails with Segment Id: {segmentId}");
            return StatusCode(500, new ResponseDto() {Message = ex.InnerException?.Message, Success = false});
        }
    }
}