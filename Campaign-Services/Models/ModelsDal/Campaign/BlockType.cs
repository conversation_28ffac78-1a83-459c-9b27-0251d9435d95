using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Campaign_Services.Models.ModelsDal.Campaign;

[Table("BlockTypes", Schema = "campaign")]
public partial class BlockType
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(50)]
    public string Name { get; set; } = null!;

    [InverseProperty("FkBlockType")]
    public virtual ICollection<Block> Blocks { get; set; } = new List<Block>();
}
