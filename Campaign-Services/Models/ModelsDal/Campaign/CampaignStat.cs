using System;
using System.Collections.Generic;

namespace Campaign_Services.Models.ModelsDal.Campaign;

public partial class CampaignStat
{
    public int Id { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime LastModifiedDate { get; set; }

    public int Audience { get; set; }

    public int Delivered { get; set; }

    public int Opens { get; set; }

    public decimal? OpenRate { get; set; }

    public int Clicks { get; set; }

    public decimal? ClickRate { get; set; }

    public int Unsubscribe { get; set; }

    public decimal UnsubscribeRate { get; set; }

    public DateTime? LastMailSent { get; set; }

    public int FkCampaignId { get; set; }

    public virtual Campaign FkCampaign { get; set; } = null!;
}