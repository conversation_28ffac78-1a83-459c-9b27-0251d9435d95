using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Campaign_Services.Models.ModelsDal.Campaign;

[Table("CampaignTests", Schema = "campaign")]
public partial class CampaignTest
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public byte Percentage { get; set; }

    [StringLength(50)]
    public string ComparisonType { get; set; } = null!;

    public int Hours { get; set; }

    [Column("FK_CampaignId")]
    public int FkCampaignId { get; set; }

    [InverseProperty("FkCampaignTest")]
    public virtual ICollection<CampaignTestValue> CampaignTestValues { get; set; } = new List<CampaignTestValue>();

    [ForeignKey("FkCampaignId")]
    [InverseProperty("CampaignTests")]
    public virtual Campaign FkCampaign { get; set; } = null!;
}
