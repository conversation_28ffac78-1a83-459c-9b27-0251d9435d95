using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Campaign_Services.Models.ModelsDal.Campaign;

[Table("Images", Schema = "campaign")]
public partial class Image
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(300)]
    public string Url { get; set; } = null!;

    [Column("FK_CampaignId")]
    public int? FkCampaignId { get; set; }

    [Column("FK_MerchantId")]
    public int? FkMerchantId { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [ForeignKey("FkCampaignId")]
    [InverseProperty("Images")]
    public virtual Campaign? FkCampaign { get; set; }
}
