using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace Campaign_Services.Models.ModelsDal.Campaign;

public partial class CampaignDbContext : DbContext
{
    public CampaignDbContext(DbContextOptions<CampaignDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Block> Blocks { get; set; }

    public virtual DbSet<BlockType> BlockTypes { get; set; }

    public virtual DbSet<Campaign> Campaigns { get; set; }

    public virtual DbSet<CampaignTest> CampaignTests { get; set; }

    public virtual DbSet<CampaignTestValue> CampaignTestValues { get; set; }

    public virtual DbSet<Filter> Filters { get; set; }

    public virtual DbSet<FilterTemplate> FilterTemplates { get; set; }

    public virtual DbSet<FilterValue> FilterValues { get; set; }

    public virtual DbSet<Image> Images { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Block>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Blocks__3214EC079478A703");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.BlockGuid).HasDefaultValueSql("(lower(newid()))");
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkBlockType).WithMany(p => p.Blocks).HasConstraintName("FK__Blocks__FK_Block__5F7E2DAC");

            entity.HasOne(d => d.FkCampaign).WithMany(p => p.Blocks).HasConstraintName("FK__Blocks__FK_Campa__5E8A0973");
        });

        modelBuilder.Entity<BlockType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__BlockTyp__3214EC07C7843DC4");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<Campaign>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Campaign__3214EC07C8C06EBE");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkFilter).WithMany(p => p.Campaigns).HasConstraintName("FK__Campaigns__FK_Fi__540C7B00");
        });

        modelBuilder.Entity<CampaignTest>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Campaign__3214EC071F5EDF3A");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkCampaign).WithMany(p => p.CampaignTests)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__CampaignT__FK_Ca__719CDDE7");
        });

        modelBuilder.Entity<CampaignTestValue>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Campaign__3214EC076615DECB");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkCampaignTest).WithMany(p => p.CampaignTestValues)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__CampaignT__FK_Ca__7755B73D");
        });

        modelBuilder.Entity<Filter>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Filters__3214EC074A3EA29C");

            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<FilterTemplate>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__FilterTe__3214EC0771EF61FB");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");
        });

        modelBuilder.Entity<FilterValue>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__FilterVa__3214EC0782DDAF9F");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkFilter).WithMany(p => p.FilterValues)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FilterVal__FK_Fi__4E53A1AA");

            entity.HasOne(d => d.FkFilterTemplate).WithMany(p => p.FilterValues)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FilterVal__FK_Fi__4D5F7D71");
        });

        modelBuilder.Entity<Image>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Images__3214EC07DCF39E0A");

            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.LastModifiedDate).HasDefaultValueSql("(getdate())");

            entity.HasOne(d => d.FkCampaign).WithMany(p => p.Images).HasConstraintName("FK__Images__FK_Campa__662B2B3B");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
