using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Campaign_Services.Models.ModelsDal.Campaign;

[Table("Filters", Schema = "campaign")]
public partial class Filter
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    [StringLength(50)]
    public string Match { get; set; } = null!;

    public byte OnlyUnengaged { get; set; }

    [StringLength(200)]
    public string? PresetName { get; set; }

    [StringLength(200)]
    public string? OpenRate { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [InverseProperty("FkFilter")]
    public virtual ICollection<Campaign> Campaigns { get; set; } = new List<Campaign>();

    [InverseProperty("FkFilter")]
    public virtual ICollection<FilterValue> FilterValues { get; set; } = new List<FilterValue>();
}
