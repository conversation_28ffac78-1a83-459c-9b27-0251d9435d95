using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Campaign_Services.Models.ModelsDal.Campaign;

[Table("FilterTemplates", Schema = "campaign")]
public partial class FilterTemplate
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(200)]
    public string Name { get; set; } = null!;

    public int? SortOrder { get; set; }

    [InverseProperty("FkFilterTemplate")]
    public virtual ICollection<FilterValue> FilterValues { get; set; } = new List<FilterValue>();
}
