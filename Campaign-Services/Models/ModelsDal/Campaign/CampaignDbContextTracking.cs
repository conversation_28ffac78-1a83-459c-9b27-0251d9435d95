using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Shared;
using IConnection = RabbitMQ.Client.IConnection;

namespace Campaign_Services.Models.ModelsDal.Campaign;

public class CampaignDbContextTracking(
    DbContextOptions<CampaignDbContext> options,
    IHttpContextAccessor httpContextAccessor,
    [FromKeyedServices("cloud")] IConnection rabbitConnectionCloud)
    //IConnection rabbitConnection)
    : CampaignDbContext(options)
{
    public virtual async Task<int> SaveChangesAsync()
    {
        OnBeforeSaveChanges();
        var result = await base.SaveChangesAsync();
        return result;
    }

    public virtual int SaveChanges()
    {
        OnBeforeSaveChanges();
        var result = base.SaveChanges();
        return result;
    }

    private void OnBeforeSaveChanges()
    {
        AuditSaveDb.OnBeforeSaveChanges(ChangeTracker, httpContextAccessor, rabbitConnectionCloud);
    }
}