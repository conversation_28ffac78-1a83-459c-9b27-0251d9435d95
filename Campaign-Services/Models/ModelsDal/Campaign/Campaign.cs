using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Campaign_Services.Models.ModelsDal.Campaign;

[Table("Campaigns", Schema = "campaign")]
public partial class Campaign
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(50)]
    public string Name { get; set; } = null!;

    public string GlobalStyles { get; set; } = null!;

    [StringLength(2000)]
    public string? PreviewText { get; set; }

    [StringLength(255)]
    public string? Subject { get; set; }

    [StringLength(255)]
    public string FromName { get; set; } = null!;

    [Column("FK_FilterId")]
    public int? FkFilterId { get; set; }

    [StringLength(50)]
    public string CampaignType { get; set; } = null!;

    [StringLength(100)]
    public string? Status { get; set; }

    [Column("FK_SegmentId")]
    public int? FkSegmentId { get; set; }

    [Column("FK_PartnerId")]
    public int FkPartnerId { get; set; }

    [InverseProperty("FkCampaign")]
    public virtual ICollection<Block> Blocks { get; set; } = new List<Block>();

    [InverseProperty("FkCampaign")]
    public virtual ICollection<CampaignTest> CampaignTests { get; set; } = new List<CampaignTest>();

    [ForeignKey("FkFilterId")]
    [InverseProperty("Campaigns")]
    public virtual Filter? FkFilter { get; set; }

    [InverseProperty("FkCampaign")]
    public virtual ICollection<Image> Images { get; set; } = new List<Image>();
}
