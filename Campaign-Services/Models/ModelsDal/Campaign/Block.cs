using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Campaign_Services.Models.ModelsDal.Campaign;

[Table("Blocks", Schema = "campaign")]
public partial class Block
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [Column("FK_CampaignId")]
    public int? FkCampaignId { get; set; }

    [Column("FK_BlockTypeId")]
    public int? FkBlockTypeId { get; set; }

    public int SortOrder { get; set; }

    public string? Settings { get; set; }

    [StringLength(36)]
    public string BlockGuid { get; set; } = null!;

    [ForeignKey("FkBlockTypeId")]
    [InverseProperty("Blocks")]
    public virtual BlockType? FkBlockType { get; set; }

    [ForeignKey("FkCampaignId")]
    [InverseProperty("Blocks")]
    public virtual Campaign? FkCampaign { get; set; }
}
