using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Campaign_Services.Models.ModelsDal.Campaign;

[Table("CampaignTestValues", Schema = "campaign")]
public partial class CampaignTestValue
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    [StringLength(100)]
    public string Type { get; set; } = null!;

    [StringLength(200)]
    public string Value { get; set; } = null!;

    [Column("FK_CampaignTestId")]
    public int FkCampaignTestId { get; set; }

    [ForeignKey("FkCampaignTestId")]
    [InverseProperty("CampaignTestValues")]
    public virtual CampaignTest FkCampaignTest { get; set; } = null!;
}
