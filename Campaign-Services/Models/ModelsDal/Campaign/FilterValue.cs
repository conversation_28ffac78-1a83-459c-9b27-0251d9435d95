using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Campaign_Services.Models.ModelsDal.Campaign;

[Table("FilterValues", Schema = "campaign")]
public partial class FilterValue
{
    [Key]
    public int Id { get; set; }

    [Precision(0)]
    public DateTime CreatedDate { get; set; }

    [Precision(0)]
    public DateTime LastModifiedDate { get; set; }

    public bool Active { get; set; }

    public string Value { get; set; } = null!;

    [StringLength(50)]
    public string? Condition { get; set; }

    public int? Time { get; set; }

    [Column("FK_FilterTemplateId")]
    public int FkFilterTemplateId { get; set; }

    [Column("FK_FilterId")]
    public int FkFilterId { get; set; }

    [ForeignKey("FkFilterId")]
    [InverseProperty("FilterValues")]
    public virtual Filter FkFilter { get; set; } = null!;

    [ForeignKey("FkFilterTemplateId")]
    [InverseProperty("FilterValues")]
    public virtual FilterTemplate FkFilterTemplate { get; set; } = null!;
}
