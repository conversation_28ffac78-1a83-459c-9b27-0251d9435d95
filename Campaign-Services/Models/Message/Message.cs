namespace Campaign_Services.Models.Message;

public class Message
{
    public Message()
    {
        MessageProducts = new List<MessageProduct>();
    }

    public string Origin { get; set; }
    public string Subject { get; set; }
    public string PreviewText { get; set; }
    public string FromName { get; set; }

    public int Priority { get; set; }

    public Guid EmailGuid { get; set; }

    public int? CampaignId { get; set; }

    public int? CampaignGroupId { get; set; }

    public string Email { get; set; }

    public string? AutomationId { get; set; }

    public string? AutomationTrigger { get; set; }
    public int? CampaignTestValueId { get; set; }

    public bool IsOverride { get; set; }

    public bool IsTest { get; set; }

    public DateTime ActionDate { get; set; }

    public List<MessageProduct> MessageProducts { get; set; }
    public int PartnerId { get; set; }
}