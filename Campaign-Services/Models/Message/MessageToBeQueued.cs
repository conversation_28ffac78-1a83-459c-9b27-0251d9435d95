namespace Campaign_Services.Models.Message;

public class MessageToBeQueued
{
    public string Origin { get; set; }

    public int CampaignId { get; set; }

    public bool IsOverride { get; set; }

    public bool IsTest { get; set; }

    public DateTime ActionDate { get; set; }
    
    public int PartnerId { get; set; }

    public int? SegmentId { get; set; }

    public List<string> IndividualEmails { get; set; }
}