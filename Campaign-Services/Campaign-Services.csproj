<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <RootNamespace>Campaign_Services</RootNamespace>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Invoice-Services\Invoice-Services.csproj" />
        <ProjectReference Include="..\Customer-Services\Customer-Services.csproj" />
        <ProjectReference Include="..\Message-Services\Message-Services.csproj" />
        <ProjectReference Include="..\Partner-Services\Partner-Services.csproj" />
        <ProjectReference Include="..\Shared\Shared.csproj" />
        <ProjectReference Include="..\Merchant-Services\Merchant-Services.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="RabbitMQ.Client" Version="6.8.1" />
    </ItemGroup>

</Project>